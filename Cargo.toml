[package]
name = "solana-bug-hunter"
version = "0.1.0"
edition = "2021"

[dependencies]
# Solana core dependencies
solana-client = "1.17"
solana-sdk = "1.17"
solana-program = "1.17"
solana-account-decoder = "1.17"

# Async runtime
tokio = { version = "1.0", features = ["full"] }
futures = "0.3"

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
bincode = "1.3"
borsh = "0.10"
bytemuck = { version = "1.13", features = ["derive"] }

# Networking
reqwest = { version = "0.11", features = ["json"] }
tungstenite = "0.20"
tokio-tungstenite = "0.20"

# Cryptography
sha2 = "0.10"
ed25519-dalek = "1.0"
rand = "0.8"

# Utilities
clap = { version = "4.0", features = ["derive"] }
env_logger = "0.10"
log = "0.4"
anyhow = "1.0"
thiserror = "1.0"

# Parallel processing
rayon = "1.7"
crossbeam = "0.8"

# Data structures
dashmap = "5.4"
parking_lot = "0.12"

# Time handling
chrono = { version = "0.4", features = ["serde"] }

# Email sending
lettre = "0.10"

# Progress bars
indicatif = "0.17"

# Configuration
config = "0.13"
toml = "0.8"

# Database (for storing results)
rusqlite = { version = "0.29", features = ["bundled"] }

# UUID generation
uuid = { version = "1.0", features = ["v4"] }

# Memory analysis
memmap2 = "0.7"

# Disassembly
capstone = "0.11"

[dev-dependencies]
tempfile = "3.0"

[[bin]]
name = "bug-hunter"
path = "src/main.rs"

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
