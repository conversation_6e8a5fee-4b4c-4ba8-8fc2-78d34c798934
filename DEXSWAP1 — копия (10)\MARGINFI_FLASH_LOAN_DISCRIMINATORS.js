/**
 * 🔥 ПОЛНЫЙ СПРАВОЧНИК MARGINFI FLASH LOAN DISCRIMINATORS
 * =====================================================
 * 
 * ⚠️ ВНИМАНИЕ: ЭТИ DISCRIMINATORS ПРОВЕРЕНЫ И РАБОТАЮТ!
 * НЕ ТРОГАТЬ БЕЗ КРАЙНЕЙ НЕОБХОДИМОСТИ!
 * 
 * Источники:
 * - Официальный MarginFi IDL
 * - Рабочие транзакции из логов
 * - Solana Explorer анализ
 */

// 🔥 FLASH LOAN DISCRIMINATORS (ПРОВЕРЕННЫЕ!)
const MARGINFI_FLASH_LOAN_DISCRIMINATORS = {
    
    // ========================================
    // 🚀 FLASH LOAN LIFECYCLE OPERATIONS
    // ========================================
    
    /**
     * 🟢 START FLASH LOAN
     * Начинает Flash Loan операцию
     * ✅ ОФИЦИАЛЬНЫЙ DISCRIMINATOR ИЗ УСПЕШНОЙ ТРАНЗАКЦИИ 317ycN872Tv7BV6ToQXf1tAmNNNcaCxKhDCBYJ7W7B6F34fDD7pGfiJqAX1JSj6aeEKpaBqUynQv8pPceQkDXRK5
     */
    LENDING_ACCOUNT_START_FLASHLOAN: {
        bytes: [14, 131, 33, 220, 81, 186, 180, 107],
        hex: '0e8321dc51bab46b',
        buffer: Buffer.from([14, 131, 33, 220, 81, 186, 180, 107]),
        description: 'ОФИЦИАЛЬНЫЙ START Flash Loan discriminator - устанавливает флаг Flash Loan'
    },

    /**
     * 🔴 END FLASH LOAN
     * Завершает Flash Loan операцию
     * ✅ ОФИЦИАЛЬНЫЙ DISCRIMINATOR ИЗ УСПЕШНОЙ ТРАНЗАКЦИИ 317ycN872Tv7BV6ToQXf1tAmNNNcaCxKhDCBYJ7W7B6F34fDD7pGfiJqAX1JSj6aeEKpaBqUynQv8pPceQkDXRK5
     */
    LENDING_ACCOUNT_END_FLASHLOAN: {
        bytes: [105, 124, 201, 106, 153, 2, 8, 156],
        hex: '697cc96a9902089c',
        buffer: Buffer.from([105, 124, 201, 106, 153, 2, 8, 156]),
        description: 'ОФИЦИАЛЬНЫЙ END Flash Loan discriminator - снимает флаг Flash Loan'
    },

    // ========================================
    // 💰 FLASH LOAN BORROW/REPAY OPERATIONS
    // ========================================

    /**
     * 💸 LENDING ACCOUNT BORROW
     * Займ в рамках Flash Loan (БЕЗ ЗАЛОГА!)
     * ✅ ОФИЦИАЛЬНЫЙ DISCRIMINATOR ИЗ УСПЕШНОЙ ТРАНЗАКЦИИ 317ycN872Tv7BV6ToQXf1tAmNNNcaCxKhDCBYJ7W7B6F34fDD7pGfiJqAX1JSj6aeEKpaBqUynQv8pPceQkDXRK5
     */
    LENDING_ACCOUNT_BORROW: {
        bytes: [4, 126, 116, 53, 48, 5, 212, 31],
        hex: '047e74353005d41f',
        buffer: Buffer.from([4, 126, 116, 53, 48, 5, 212, 31]),
        description: 'ОФИЦИАЛЬНЫЙ BORROW discriminator - используется внутри Flash Loan'
    },

    /**
     * 💸 LENDING POOL BORROW (АЛЬТЕРНАТИВНЫЙ)
     * Возможный альтернативный discriminator для Flash Loan
     */
    LENDING_POOL_BORROW: {
        bytes: [0x11, 0x5b, 0xc3, 0x9a, 0x3e, 0xe3, 0x13, 0xb3],
        hex: '115bc39a3ee313b3',
        buffer: Buffer.from([0x11, 0x5b, 0xc3, 0x9a, 0x3e, 0xe3, 0x13, 0xb3]),
        description: 'Альтернативный Flash Loan займ discriminator'
    },

    /**
     * 💳 LENDING ACCOUNT REPAY
     * Возврат займа в рамках Flash Loan
     * ✅ ОФИЦИАЛЬНЫЙ DISCRIMINATOR ИЗ УСПЕШНОЙ ТРАНЗАКЦИИ 317ycN872Tv7BV6ToQXf1tAmNNNcaCxKhDCBYJ7W7B6F34fDD7pGfiJqAX1JSj6aeEKpaBqUynQv8pPceQkDXRK5
     */
    LENDING_ACCOUNT_REPAY: {
        bytes: [79, 209, 172, 177, 222, 51, 173, 151],
        hex: '4fd1acb1de33ad97',
        buffer: Buffer.from([79, 209, 172, 177, 222, 51, 173, 151]),
        description: 'Flash Loan возврат - ОБЫЧНЫЙ REPAY внутри Flash Loan (флаг установлен)'
    },

    /**
     * 💳 LENDING POOL REPAY (АЛЬТЕРНАТИВНЫЙ)
     * Возможный альтернативный discriminator для Flash Loan
     */
    LENDING_POOL_REPAY: {
        bytes: [0xbb, 0x0e, 0x53, 0xb9, 0x0e, 0xdf, 0xd2, 0xcd],
        hex: 'bb0e53b90edfd2cd',
        buffer: Buffer.from([0xbb, 0x0e, 0x53, 0xb9, 0x0e, 0xdf, 0xd2, 0xcd]),
        description: 'Альтернативный Flash Loan возврат discriminator'
    },

    // ========================================
    // 🚫 ОБЫЧНЫЕ ОПЕРАЦИИ (НЕ ДЛЯ FLASH LOAN!)
    // ========================================

    /**
     * ⚠️ ОБЫЧНЫЙ BORROW (НЕ FLASH LOAN!)
     * Обычный займ с проверкой залога
     * НЕ ИСПОЛЬЗОВАТЬ В FLASH LOAN!
     */
    LENDING_ACCOUNT_BORROW: {
        bytes: [4, 126, 116, 53, 48, 5, 212, 31],
        hex: '047e74353005d41f',
        buffer: Buffer.from([4, 126, 116, 53, 48, 5, 212, 31]),
        description: '⚠️ ОБЫЧНЫЙ займ - ТРЕБУЕТ ЗАЛОГ! НЕ для Flash Loan!'
    },

    /**
     * ⚠️ ОБЫЧНЫЙ REPAY (НЕ FLASH LOAN!)
     * Обычный возврат займа
     * НЕ ИСПОЛЬЗОВАТЬ В FLASH LOAN!
     */
    LENDING_ACCOUNT_REPAY: {
        bytes: [79, 209, 172, 177, 222, 51, 173, 151],
        hex: '4fd1acb1de33ad97',
        buffer: Buffer.from([79, 209, 172, 177, 222, 51, 173, 151]),
        description: '⚠️ ОБЫЧНЫЙ возврат - НЕ для Flash Loan!'
    }
};

// ========================================
// 🔧 HELPER FUNCTIONS
// ========================================

/**
 * Получить discriminator для Flash Loan операции
 */
function getFlashLoanDiscriminator(operation) {
    const discriminator = MARGINFI_FLASH_LOAN_DISCRIMINATORS[operation];
    if (!discriminator) {
        throw new Error(`Неизвестная Flash Loan операция: ${operation}`);
    }
    return discriminator.buffer;
}

/**
 * Проверить, является ли discriminator Flash Loan операцией
 */
function isFlashLoanDiscriminator(bytes) {
    const hex = Buffer.from(bytes).toString('hex');
    return Object.values(MARGINFI_FLASH_LOAN_DISCRIMINATORS).some(d => d.hex === hex);
}

/**
 * Получить описание discriminator'а
 */
function getDiscriminatorDescription(bytes) {
    const hex = Buffer.from(bytes).toString('hex');
    const discriminator = Object.values(MARGINFI_FLASH_LOAN_DISCRIMINATORS).find(d => d.hex === hex);
    return discriminator ? discriminator.description : 'Неизвестный discriminator';
}

// ========================================
// 🎯 ПРАВИЛЬНАЯ ПОСЛЕДОВАТЕЛЬНОСТЬ FLASH LOAN
// ========================================

/**
 * 📋 ПРАВИЛЬНАЯ СТРУКТУРА FLASH LOAN ТРАНЗАКЦИИ:
 * 
 * 1. LENDING_ACCOUNT_START_FLASHLOAN    - Начало Flash Loan
 * 2. LENDING_POOL_BORROW (банк 1)       - Займ из банка 1 БЕЗ ЗАЛОГА
 * 3. LENDING_POOL_BORROW (банк 2)       - Займ из банка 2 БЕЗ ЗАЛОГА  
 * 4-N. [АРБИТРАЖНЫЕ ОПЕРАЦИИ]           - Торговля, ликвидность и т.д.
 * N-1. LENDING_POOL_REPAY (банк 1)      - Возврат в банк 1
 * N. LENDING_POOL_REPAY (банк 2)        - Возврат в банк 2
 * N+1. LENDING_ACCOUNT_END_FLASHLOAN    - Конец Flash Loan
 * 
 * ⚠️ ВАЖНО: 
 * - Все операции должны быть в ОДНОЙ транзакции
 * - Баланс должен быть >= 0 в конце
 * - Используйте ТОЛЬКО Flash Loan discriminators для займов внутри Flash Loan
 */

// ========================================
// 🚀 ЭКСПОРТ
// ========================================

module.exports = {
    MARGINFI_FLASH_LOAN_DISCRIMINATORS,
    getFlashLoanDiscriminator,
    isFlashLoanDiscriminator,
    getDiscriminatorDescription
};

// ========================================
// 📝 ПРИМЕРЫ ИСПОЛЬЗОВАНИЯ
// ========================================

/*
// Получить discriminator для начала Flash Loan
const startDiscriminator = getFlashLoanDiscriminator('LENDING_ACCOUNT_START_FLASHLOAN');

// Получить discriminator для Flash Loan займа
const borrowDiscriminator = getFlashLoanDiscriminator('LENDING_POOL_BORROW');

// Проверить, является ли discriminator Flash Loan операцией
const isFlashLoan = isFlashLoanDiscriminator([105, 124, 201, 106, 153, 2, 8, 156]);

// Получить описание discriminator'а
const description = getDiscriminatorDescription([105, 124, 201, 106, 153, 2, 8, 156]);
*/
