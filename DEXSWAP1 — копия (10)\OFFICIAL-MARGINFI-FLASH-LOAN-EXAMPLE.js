// 🔥 ОФИЦИАЛЬНЫЙ ПРИМЕР FLASH LOAN ИЗ ДОКУМЕНТАЦИИ MARGINFI
// Источник: https://docs.marginfi.com/ts-sdk#conduct-a-flash-loan

import { Connection } from "@solana/web3.js";
import { MarginfiClient, MarginfiAccountWrapper } from '@mrgnlabs/marginfi-client-v2';
import { NodeWallet } from "@mrgnlabs/mrgn-common";

async function officialFlashLoanExample() {
  const connection = new Connection("<rpc-url>", "confirmed");
  const wallet = NodeWallet.local()
  const config = getConfig("<environment>");
  const client = await MarginfiClient.fetch(config, wallet, connection);

  const marginfiAccounts = await client.getMarginfiAccountsForAuthority();
  if (marginfiAccounts.length === 0) throw Error("No marginfi account found");

  const marginfiAccount = marginfiAccounts[0];

  const solBank = client.getBankByTokenSymbol("SOL");
  if (!solBank) throw Error("SOL bank not found");

  const amount = 10; // SOL

  // 🔥 ОФИЦИАЛЬНАЯ ПОСЛЕДОВАТЕЛЬНОСТЬ:
  const borrowIx = await marginfiAccount.makeBorrowIx(amount, solBank.address);
  const repayIx = await marginfiAccount.makeRepayIx(amount, solBank.address, true);

  const flashLoanTx = await marginfiAccount.buildFlashLoanTx({
    ixs: [...borrowIx.instructions, ...repayIx.instructions],
    signers: [],
  });

  await client.processTransaction(flashLoanTx);
}

// 🔥 КЛЮЧЕВЫЕ МОМЕНТЫ:
// 1. ✅ Flash Loan НЕ ТРЕБУЕТ ОБЕСПЕЧЕНИЯ (0% комиссия)
// 2. ✅ Используется buildFlashLoanTx() вместо старых методов
// 3. ✅ Последовательность: borrow → [ваши операции] → repay
// 4. ✅ repayAll=true для полного возврата займа

// 🔥 ОФИЦИАЛЬНЫЕ МЕТОДЫ ИЗ ДОКУМЕНТАЦИИ:

// makeBeginFlashLoanIx(endIndex)
// - Создает инструкции для начала flash loan
// - endIndex: индекс где заканчивается flash loan

// makeEndFlashLoanIx(projectedActiveBalances)  
// - Создает инструкции для завершения flash loan
// - projectedActiveBalances: массив PublicKey активных балансов

// buildFlashLoanTx(args)
// - Создает транзакцию для flash loan
// - args: { ixs: TransactionInstruction[], signers: [] }

// flashLoan(args)
// - Выполняет flash loan транзакцию
// - args: FlashLoanArgs с инструкциями и подписантами
