/**
 * 🔥 POOL A - ИНИЦИАЛИЗАЦИЯ POSITION АККАУНТА ДЛЯ ПУЛА 1
 * ОТДЕЛЬНЫЙ СКРИПТ - НЕ СВЯЗАН С ОСНОВНЫМ КОДОМ!
 * ИСПОЛЬЗУЕТ FLASH LOAN ДЛЯ МИНИМАЛЬНОЙ ПЛАТЫ
 */

const { Connection, PublicKey, TransactionInstruction, ComputeBudgetProgram, Transaction, sendAndConfirmTransaction } = require('@solana/web3.js');
const { Keypair } = require('@solana/web3.js');
const DLMM = require('@meteora-ag/dlmm').default;
const { BN } = require('@coral-xyz/anchor');
const fs = require('fs');

class PoolAInitializer {
    constructor(wallet, marginfiAccountAddress, connection) {
        this.wallet = wallet;
        this.marginfiAccountAddress = new PublicKey(marginfiAccountAddress);
        this.connection = connection;

        // 🔥 КОНСТАНТЫ ИЗ ОСНОВНОГО КОДА
        this.MARGINFI_PROGRAM = new PublicKey('MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA');
        this.METEORA_DLMM_PROGRAM = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');
        this.TOKEN_PROGRAM = new PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA');

        // 🔥 MARGINFI GROUP
        this.MARGINFI_GROUP = new PublicKey('4qp6Fx6tnZkY5Wropq9wUYgtFxXKwE6viZxFHg3rdAG8');
        
        // 🔥 БАНКИ
        this.BANKS = {
            SOL: new PublicKey('CCKtUs6Cgwo4aaQUmBPmyoApH2gUDErxNZCAntD6LYGh')
        };

        // 🔥 VAULT АККАУНТЫ
        this.VAULTS = {
            SOL: {
                liquidityVault: new PublicKey('2eicbpitfJXDwqCuFAmPgDP7t2oUotnAzbGzRKLMgSLe'),
                vaultAuthority: new PublicKey('DD3AeAssFvjqTvRTrRAtpfjkBF8FpVKnFuwnMLN9haXD'),
                userTokenAccount: new PublicKey('68rtTtSuEPR84Wo1xWGs6ytBttn7JN33Ux8WsDp38FHk')
            }
        };

        // 🔥 POOL 1 ADDRESS
        this.POOL_1_ADDRESS = '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6';

        // 🔥 DLMM INSTANCE (БУДЕТ ИНИЦИАЛИЗИРОВАН ПОЗЖЕ)
        this.dlmmPool = null;
    }

    /**
     * 🚀 СОЗДАНИЕ ТРАНЗАКЦИИ ИНИЦИАЛИЗАЦИИ POOL A
     */
    async createPoolAInitTransaction() {
        console.log('🔥 СОЗДАНИЕ ТРАНЗАКЦИИ ИНИЦИАЛИЗАЦИИ POOL A...');
        
        const instructions = [];

        // 0-1: ComputeBudget
        const computeUnitLimitIx = ComputeBudgetProgram.setComputeUnitLimit({
            units: 1400000
        });
        instructions.push(computeUnitLimitIx);
        
        const computeUnitPriceIx = ComputeBudgetProgram.setComputeUnitPrice({
            microLamports: 0
        });
        instructions.push(computeUnitPriceIx);

        // 2: START Flash Loan
        const endIndex = 6; // END Flash Loan на позиции 6 (БЕЗ WITHDRAW!)
        const startFlashLoanIx = this.createStartFlashLoanInstruction(endIndex);
        instructions.push(startFlashLoanIx);

        // 3: BORROW SOL (минимум для инициализации)
        const borrowAmount = **********; // 1 SOL
        const borrowSOLIx = this.createBorrowInstruction(borrowAmount, this.BANKS.SOL);
        instructions.push(borrowSOLIx);

        // 4: INITIALIZE POSITION + ADD LIQUIDITY
        const initAndAddLiquidityIx = await this.createInitializePositionAndAddLiquidityInstruction();
        instructions.push(initAndAddLiquidityIx);

        // 5: REMOVE LIQUIDITY + DEPOSIT обратно
        const removeLiquidityIx = this.createRemoveLiquidityInstruction();
        instructions.push(removeLiquidityIx);

        // 6: END Flash Loan (ДОЛЖЕН БЫТЬ НА ПОЗИЦИИ endIndex!)
        const endFlashLoanIx = this.createEndFlashLoanInstruction();
        instructions.push(endFlashLoanIx);

        // 7: REPAY SOL (ПОСЛЕ END FLASH LOAN!)
        const repaySOLIx = this.createRepayInstruction(this.BANKS.SOL, true);
        instructions.push(repaySOLIx);

        console.log(`✅ Создано ${instructions.length} инструкций для Pool A`);
        return instructions;
    }

    /**
     * 🔧 START FLASH LOAN (СКОПИРОВАНО ИЗ ОСНОВНОГО КОДА!)
     */
    createStartFlashLoanInstruction(endIndex) {
        console.log(`🔧 START Flash Loan с endIndex: ${endIndex}`);

        // 🔥 ПРАВИЛЬНЫЙ DISCRIMINATOR ИЗ УСПЕШНОЙ ТРАНЗАКЦИИ
        const instructionData = Buffer.alloc(16);
        const correctDiscriminator = [14, 131, 33, 220, 81, 186, 180, 107]; // 0x0e8321dc51bab46b
        Buffer.from(correctDiscriminator).copy(instructionData, 0);
        instructionData.writeBigUInt64LE(BigInt(endIndex), 8);

        // 🔥 ПРАВИЛЬНЫЕ АККАУНТЫ (КАК В РАБОЧЕЙ ТРАНЗАКЦИИ!)
        const accounts = [
            { pubkey: this.marginfiAccountAddress, isSigner: false, isWritable: true },  // MarginFi Account
            { pubkey: this.wallet.publicKey, isSigner: true, isWritable: true },         // Authority ✅ WRITABLE!
            { pubkey: new PublicKey('Sysvar1nstructions1111111111111111111111111'), isSigner: false, isWritable: false } // Instructions Sysvar
        ];

        return new TransactionInstruction({
            programId: this.MARGINFI_PROGRAM,
            keys: accounts,
            data: instructionData
        });
    }

    /**
     * 🔧 BORROW INSTRUCTION (СКОПИРОВАНО ИЗ ОСНОВНОГО КОДА!)
     */
    createBorrowInstruction(amount, bankAddress) {
        console.log(`🔧 BORROW ${amount} от банка ${bankAddress.toString().slice(0,8)}...`);

        // 🔥 ОПРЕДЕЛЯЕМ КАКОЙ БАНК (ТОЛЬКО SOL ДЛЯ PoolA)
        const vaultInfo = this.VAULTS.SOL;

        console.log(`   💰 Токен: SOL`);
        console.log(`   🏦 Vault: ${vaultInfo.liquidityVault.toString().slice(0,8)}...`);
        console.log(`   👤 User Account: ${vaultInfo.userTokenAccount.toString().slice(0,8)}...`);

        // 🔥 ПРАВИЛЬНЫЙ DISCRIMINATOR ДЛЯ BORROW
        const borrowDiscriminator = [0x04, 0x7e, 0x74, 0x35, 0x30, 0x05, 0xd4, 0x1f];

        const instructionData = Buffer.alloc(16);
        Buffer.from(borrowDiscriminator).copy(instructionData, 0);
        instructionData.writeBigUInt64LE(BigInt(amount), 8);

        // 🔥 ПРАВИЛЬНЫЕ АККАУНТЫ ДЛЯ BORROW
        const accounts = [
            { pubkey: this.MARGINFI_GROUP, isSigner: false, isWritable: false },        // 0: marginfi_group
            { pubkey: new PublicKey(this.marginfiAccountAddress), isSigner: false, isWritable: true }, // 1: marginfi_account
            { pubkey: this.wallet.publicKey, isSigner: true, isWritable: false },       // 2: authority (signer)
            { pubkey: bankAddress, isSigner: false, isWritable: true },                 // 3: bank
            { pubkey: vaultInfo.userTokenAccount, isSigner: false, isWritable: true },  // 4: destination_token_account
            { pubkey: vaultInfo.vaultAuthority, isSigner: false, isWritable: true },    // 5: bank_liquidity_vault_authority
            { pubkey: vaultInfo.liquidityVault, isSigner: false, isWritable: true },    // 6: bank_liquidity_vault
            { pubkey: this.TOKEN_PROGRAM, isSigner: false, isWritable: false }          // 7: token_program
        ];

        return new TransactionInstruction({
            programId: this.MARGINFI_PROGRAM,
            keys: accounts,
            data: instructionData
        });
    }

    /**
     * 🔧 WITHDRAW INSTRUCTION
     */
    createWithdrawInstruction(tokenType, amount) {
        const discriminator = [183, 18, 70, 156, 148, 109, 161, 34]; // lendingAccountWithdraw
        const instructionData = Buffer.concat([
            Buffer.from(discriminator),
            Buffer.from(amount.toString(16).padStart(16, '0'), 'hex').reverse(),
            Buffer.from([1]) // withdrawAll = false
        ]);

        return new TransactionInstruction({
            keys: [
                { pubkey: this.marginfiAccountAddress, isSigner: false, isWritable: true },
                { pubkey: this.BANKS.SOL, isSigner: false, isWritable: true },
                { pubkey: this.VAULTS.SOL.userTokenAccount, isSigner: false, isWritable: true },
                { pubkey: this.VAULTS.SOL.liquidityVault, isSigner: false, isWritable: true },
                { pubkey: this.TOKEN_PROGRAM, isSigner: false, isWritable: false },
                { pubkey: this.wallet.publicKey, isSigner: true, isWritable: false }
            ],
            programId: this.MARGINFI_PROGRAM,
            data: instructionData
        });
    }

    /**
     * 🔧 INITIALIZE POSITION + ADD LIQUIDITY (ИСПОЛЬЗУЕМ SDK!)
     */
    async createInitializePositionAndAddLiquidityInstruction() {
        console.log('🔥 Создание DLMM инстанса...');

        // Инициализируем DLMM если еще не создан
        if (!this.dlmmPool) {
            this.dlmmPool = await DLMM.create(this.connection, new PublicKey(this.POOL_1_ADDRESS));
        }

        // Получаем активный bin
        const activeBin = await this.dlmmPool.getActiveBin();
        console.log(`📊 Активный bin ID: ${activeBin.binId}`);

        // Настройка стратегии
        const TOTAL_RANGE_INTERVAL = 3; // Минимальный диапазон
        const minBinId = activeBin.binId - TOTAL_RANGE_INTERVAL;
        const maxBinId = activeBin.binId + TOTAL_RANGE_INTERVAL;

        // Минимальные суммы
        const totalXAmount = new BN(100000); // 0.1 токена
        const totalYAmount = new BN(100000); // 0.1 токена

        // Создаем новый position keypair
        const newPosition = new Keypair();
        console.log(`🔑 Новый position: ${newPosition.publicKey.toString()}`);

        // Создаем транзакцию через SDK
        const createPositionTx = await this.dlmmPool.initializePositionAndAddLiquidityByStrategy({
            positionPubKey: newPosition.publicKey,
            user: this.wallet.publicKey,
            totalXAmount,
            totalYAmount,
            strategy: {
                maxBinId,
                minBinId,
                strategyType: 0, // StrategyType.Spot
            },
        });

        console.log('✅ SDK транзакция создана!');

        // Возвращаем первую инструкцию из транзакции
        return createPositionTx.instructions[0];
    }

    /**
     * 🔧 REMOVE LIQUIDITY
     */
    createRemoveLiquidityInstruction() {
        // TODO: Реализовать
        const discriminator = [80, 85, 209, 72, 24, 206, 177, 108]; // removeLiquidityByRange2
        const instructionData = Buffer.from(discriminator);

        return new TransactionInstruction({
            keys: [
                { pubkey: this.wallet.publicKey, isSigner: true, isWritable: true }
            ],
            programId: this.METEORA_DLMM_PROGRAM,
            data: instructionData
        });
    }

    /**
     * 🔧 REPAY INSTRUCTION
     */
    createRepayInstruction(bank, repayAll) {
        const discriminator = [234, 103, 67, 82, 208, 234, 219, 166]; // lendingAccountRepay
        const instructionData = Buffer.concat([
            Buffer.from(discriminator),
            Buffer.from([0, 0, 0, 0, 0, 0, 0, 0]), // amount (ignored if repayAll)
            Buffer.from([repayAll ? 1 : 0]) // repayAll
        ]);

        return new TransactionInstruction({
            keys: [
                { pubkey: this.marginfiAccountAddress, isSigner: false, isWritable: true },
                { pubkey: bank, isSigner: false, isWritable: true },
                { pubkey: this.VAULTS.SOL.userTokenAccount, isSigner: false, isWritable: true },
                { pubkey: this.VAULTS.SOL.liquidityVault, isSigner: false, isWritable: true },
                { pubkey: this.TOKEN_PROGRAM, isSigner: false, isWritable: false },
                { pubkey: this.wallet.publicKey, isSigner: true, isWritable: false }
            ],
            programId: this.MARGINFI_PROGRAM,
            data: instructionData
        });
    }

    /**
     * 🔧 END FLASH LOAN (ПРАВИЛЬНЫЙ DISCRIMINATOR!)
     */
    createEndFlashLoanInstruction() {
        const discriminator = [105, 124, 201, 106, 153, 2, 8, 156]; // ПРАВИЛЬНЫЙ endFlashloan!
        const instructionData = Buffer.from(discriminator);

        return new TransactionInstruction({
            keys: [
                { pubkey: this.marginfiAccountAddress, isSigner: false, isWritable: true },
                { pubkey: this.wallet.publicKey, isSigner: true, isWritable: false }
            ],
            programId: this.MARGINFI_PROGRAM,
            data: instructionData
        });
    }

    /**
     * 🚀 ВЫПОЛНЕНИЕ ИНИЦИАЛИЗАЦИИ POOL A
     */
    async executePoolAInit() {
        try {
            console.log('\n🚀 ЗАПУСК ИНИЦИАЛИЗАЦИИ POOL A...');
            
            const instructions = await this.createPoolAInitTransaction();
            const transaction = new Transaction();
            instructions.forEach(ix => transaction.add(ix));

            console.log('📤 Отправка транзакции...');
            const signature = await sendAndConfirmTransaction(this.connection, transaction, [this.wallet]);
            
            console.log(`✅ POOL A ИНИЦИАЛИЗИРОВАН! Signature: ${signature}`);
            return signature;
        } catch (error) {
            console.error('❌ Ошибка инициализации Pool A:', error);
            throw error;
        }
    }
}

module.exports = PoolAInitializer;
