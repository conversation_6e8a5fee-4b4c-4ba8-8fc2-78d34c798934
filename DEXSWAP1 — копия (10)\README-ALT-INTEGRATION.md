# 🔥 Jupiter Meteora ALT Integration

Система для получения и использования Address Lookup Tables (ALT) от Jupiter для оптимизации Meteora DLMM транзакций.

## 📊 Результаты тестирования

✅ **Система готова к использованию в продакшене!**
- 🏆 Итоговая оценка: **80/100**
- 📊 ALT таблиц: **4 валидных**
- 📊 Всего аккаунтов: **549**
- ⚡ Загрузка адресов: **<1ms**
- 🔄 Кэш актуален: **ДА**

## 🚀 Быстрый старт

### 1. Простое использование

```javascript
const { Connection } = require('@solana/web3.js');
const MeteoraALTReady = require('./meteora-alt-ready.js');

const connection = new Connection('https://api.mainnet-beta.solana.com');
const altReady = new MeteoraALTReady(connection);

// Получение ALT для транзакции
const altAccounts = await altReady.getAddressLookupTableAccounts();

// Создание транзакции с ALT
const transaction = new VersionedTransaction(message, {
    addressLookupTableAccounts: altAccounts
});
```

### 2. Интеграция в существующий код

В `jupiter-swap-instructions.js`:

```javascript
// В конструкторе добавить:
const MeteoraALTReady = require('./meteora-alt-ready.js');
this.altReady = new MeteoraALTReady(this.connection);

// Заменить существующий метод:
async getAddressLookupTableAccounts(keys) {
    return await this.altReady.getAddressLookupTableAccounts(keys);
}
```

## 📁 Структура файлов

### Основные модули
- `meteora-alt-ready.js` - **Готовый к использованию модуль**
- `quick-alt-loader.js` - Быстрая загрузка из кэша
- `jupiter-alt-fetcher.js` - Получение ALT от Jupiter API
- `meteora-alt-integration.js` - Интеграция с Meteora

### Файлы данных
- `meteora-alt-addresses.json` - Адреса ALT таблиц (быстрая загрузка)
- `meteora-alt-detailed.json` - Детальная информация о таблицах
- `meteora-alt-cache.json` - Полный кэш с валидацией

### Тесты
- `test-final-integration.js` - Финальный тест системы
- `test-quick-alt-loader.js` - Тест быстрого загрузчика
- `test-alt-tables-save.js` - Тест сохранения таблиц

## 🔧 API Reference

### MeteoraALTReady

#### `getAddressLookupTableAccounts(altAddresses?)`
Основной метод для получения ALT аккаунтов.
```javascript
const altAccounts = await altReady.getAddressLookupTableAccounts();
```

#### `getALTAddresses()`
Быстрое получение адресов ALT из кэша.
```javascript
const addresses = altReady.getALTAddresses(); // Синхронно, <1ms
```

#### `getMeteoraALT()`
Meteora-специфичное получение ALT.
```javascript
const meteoraALT = await altReady.getMeteoraALT();
if (meteoraALT.success) {
    // Используем meteoraALT.accounts
}
```

#### `diagnose()`
Диагностика состояния системы.
```javascript
const diagnosis = await altReady.diagnose();
if (!diagnosis.ready) {
    console.log('Требуется обновление ALT кэша');
}
```

#### `getALTStatus()`
Получение статуса ALT системы.
```javascript
const status = altReady.getALTStatus();
console.log(`Доступно таблиц: ${status.tablesCount}`);
```

## 📊 Доступные ALT таблицы

1. **HGmknUTU...3ZhyL4fC** - 256 аккаунтов (Meteora Main)
2. **5FuKF7C1...4KwojoF1** - 256 аккаунтов (Meteora DLMM)
3. **FEFhAFKz...xEPZ8wNR** - 19 аккаунтов (Meteora Pools)
4. **FAeyUf4A...gqArBGXe** - 18 аккаунтов (Jupiter Main)

**Всего: 549 уникальных аккаунтов**

## 🔄 Обновление кэша

### Автоматическое обновление
Система автоматически проверяет актуальность кэша (по умолчанию 6 часов).

### Ручное обновление
```bash
# Обновить ALT таблицы
node test-alt-tables-save.js

# Протестировать систему
node test-final-integration.js
```

## ⚡ Производительность

- **Загрузка адресов**: <1ms (из кэша)
- **Загрузка аккаунтов**: ~700ms (из сети)
- **Meteora ALT**: ~700ms
- **Совместимый метод**: ~800ms

## 🛡️ Обработка ошибок

Система устойчива к ошибкам:
- При недоступности ALT продолжает работу без них
- Автоматический fallback на известные таблицы
- Валидация всех загружаемых данных

## 🔧 Настройка

### Изменение интервала кэширования
```javascript
// В quick-alt-loader.js изменить maxAgeHours
const isCacheValid = loader.isCacheValid(12); // 12 часов вместо 6
```

### Добавление новых ALT таблиц
```javascript
// В jupiter-alt-fetcher.js в методе getKnownMeteoraALTs()
return [
    'HGmknUTUmeovMc9ryERNWG6UFZDFDVr9xrum3ZhyL4fC',
    'ВАШ_НОВЫЙ_ALT_АДРЕС_ЗДЕСЬ'
];
```

## 🚨 Важные замечания

1. **Совместимость**: Полностью совместимо с существующим кодом
2. **Производительность**: Кэширование обеспечивает быструю загрузку
3. **Надежность**: Система работает даже при недоступности Jupiter API
4. **Обновления**: Рекомендуется обновлять кэш каждые 6-12 часов

## 📝 Примеры использования

### В арбитражном боте
```javascript
const altReady = new MeteoraALTReady(connection);

// Перед созданием транзакции
const altAccounts = await altReady.getAddressLookupTableAccounts();

// Создание оптимизированной транзакции
const transaction = new VersionedTransaction(message, {
    addressLookupTableAccounts: altAccounts
});
```

### Проверка перед торговлей
```javascript
const diagnosis = await altReady.diagnose();
if (!diagnosis.ready) {
    console.log('ALT система не готова, обновляем...');
    // Запустить обновление
}
```

### Мониторинг системы
```javascript
const status = altReady.getALTStatus();
console.log(`ALT статус: ${status.available ? 'OK' : 'ERROR'}`);
console.log(`Кэш: ${status.cacheValid ? 'актуален' : 'устарел'}`);
```

## 🎯 Следующие шаги

1. Интегрируйте `meteora-alt-ready.js` в ваш основной код
2. Замените существующие методы `getAddressLookupTableAccounts`
3. Настройте автоматическое обновление кэша
4. Мониторьте производительность и актуальность данных

---

**🔥 Система готова к использованию в продакшене!**
