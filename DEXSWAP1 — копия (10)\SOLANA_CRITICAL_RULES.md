# 🚨 SOLANA КРИТИЧЕСКИЕ ПРАВИЛА - БЫСТРЫЙ СПРАВОЧНИК

## ⚠️ 15 НЕРУШИМЫХ ПРАВИЛ НИЗКОУРОВНЕВОГО ПРОГРАММИРОВАНИЯ

### 🔴 УРОВЕНЬ 1: БАЗОВАЯ БЕЗОПАСНОСТЬ
1. **MEMORY SAFETY** - Всегда используй `bytemuck` для безопасной работы с памятью
2. **ZERO-COPY SERIALIZATION** - Используй `#[repr(C)]` и zero-copy для производительности  
3. **UNSAFE RUST** - Только с полным пониманием последствий
4. **BPF CONSTRAINTS** - Соблюдай ограничения sBPF виртуальной машины

### 🟠 УРОВЕНЬ 2: РЕСУРСЫ И ПРОИЗВОДИТЕЛЬНОСТЬ
5. **SYSCALL LIMITS** - Максимум 200,000 compute units на транзакцию
6. **STACK OVERFLOW** - Стек ограничен 4KB, избегай глубокой рекурсии
7. **HEAP ALLOCATION** - Минимизируй аллокации, используй статические буферы

### 🟡 УРОВЕНЬ 3: ВАЛИДАЦИЯ И ПРОВЕРКИ
8. **ACCOUNT VALIDATION** - Всегда проверяй владельца, размер и подпись аккаунтов
9. **INTEGER OVERFLOW** - Используй checked_* методы для арифметических операций
10. **PDA VERIFICATION** - Всегда верифицируй Program Derived Addresses
11. **DISCRIMINATOR CHECKS** - Всегда проверяй discriminator инструкций

### 🔵 УРОВЕНЬ 4: БЕЗОПАСНОСТЬ ВЗАИМОДЕЙСТВИЙ
12. **CPI SECURITY** - Проверяй все аккаунты при Cross-Program Invocation
13. **REENTRANCY PROTECTION** - Защищайся от повторных вызовов
14. **INSTRUCTION VALIDATION** - Валидируй все входные данные инструкций
15. **SIGNER VERIFICATION** - Проверяй подписи для критических операций

## 🛡️ БЫСТРЫЕ ПРОВЕРКИ БЕЗОПАСНОСТИ

### Account Validation Template
```rust
fn validate_account(account: &AccountInfo) -> ProgramResult {
    // 1. Владелец
    if account.owner != &crate::ID { return Err(ProgramError::IncorrectProgramId); }
    
    // 2. Размер
    if account.data_len() < EXPECTED_SIZE { return Err(ProgramError::AccountDataTooSmall); }
    
    // 3. Подпись (для writable)
    if account.is_writable && !account.is_signer { return Err(ProgramError::MissingRequiredSignature); }
    
    // 4. Инициализация
    if account.data_is_empty() { return Err(ProgramError::UninitializedAccount); }
    
    // 5. Rent exemption
    if !account.is_rent_exempt() { return Err(ProgramError::NotRentExempt); }
    
    Ok(())
}
```

### Integer Overflow Protection
```rust
// ✅ ВСЕГДА используй checked операции
let result = a.checked_add(b).ok_or(ProgramError::ArithmeticOverflow)?;
let result = a.checked_sub(b).ok_or(ProgramError::ArithmeticOverflow)?;
let result = a.checked_mul(b).ok_or(ProgramError::ArithmeticOverflow)?;
let result = a.checked_div(b).ok_or(ProgramError::ArithmeticOverflow)?;
```

### PDA Verification
```rust
fn verify_pda(address: &Pubkey, seeds: &[&[u8]], bump: u8, program_id: &Pubkey) -> ProgramResult {
    let expected = Pubkey::create_program_address(&[seeds, &[&[bump]]].concat(), program_id)
        .map_err(|_| ProgramError::InvalidSeeds)?;
    if address != &expected { return Err(ProgramError::InvalidSeeds); }
    Ok(())
}
```

### Reentrancy Protection
```rust
static mut EXECUTION_LOCK: bool = false;

fn protected_function() -> ProgramResult {
    unsafe {
        if EXECUTION_LOCK { return Err(ProgramError::Custom(1)); }
        EXECUTION_LOCK = true;
    }
    
    let result = critical_operation();
    
    unsafe { EXECUTION_LOCK = false; }
    result
}
```

## 🚀 ПРОИЗВОДИТЕЛЬНОСТЬ - КРИТИЧЕСКИЕ ЛИМИТЫ

### Compute Units
- **Максимум**: 200,000 CU на транзакцию
- **Типичное потребление**:
  - Anchor Initialize: ~5,095 CU
  - Anchor Increment: ~1,162 CU
  - Native Initialize: ~3,500 CU
  - Native Increment: ~800 CU
  - Unsafe + Syscalls: ~400 CU

### Memory Limits
- **Stack**: 4KB максимум
- **Heap**: 32KB по умолчанию (можно увеличить через Compute Budget)
- **Account Data**: До 10MB на аккаунт

### Syscall Limits
- **Максимум syscalls**: Ограничено compute units
- **Критические syscalls**: sol_log_, sol_memcpy_, sol_invoke_signed_c

## 🔍 БЫСТРАЯ ДИАГНОСТИКА ОШИБОК

### Частые ошибки и решения
- **0x1**: `InsufficientFunds` → Проверь баланс аккаунта
- **0x3**: `InvalidAccountData` → Проверь discriminator и размер
- **0x4**: `InvalidArgument` → Валидируй входные данные
- **0x5**: `InvalidInstruction` → Проверь instruction data
- **0x6**: `MissingRequiredSignature` → Добавь is_signer
- **0x7**: `AccountAlreadyInitialized` → Проверь состояние аккаунта
- **0x8**: `UninitializedAccount` → Инициализируй аккаунт сначала

### Memory Safety Ошибки
- **Segmentation Fault** → Проверь bounds в unsafe блоках
- **Stack Overflow** → Уменьши рекурсию или локальные переменные
- **Invalid Memory Access** → Используй bytemuck для cast операций

## ⚡ ЭКСТРЕННЫЙ ЧЕКЛИСТ

### Перед каждым коммитом:
- [ ] Все unsafe блоки имеют комментарии с обоснованием
- [ ] Нет прямых cast операций без bytemuck
- [ ] Все арифметические операции используют checked_*
- [ ] Account validation присутствует везде
- [ ] PDA verification корректна
- [ ] Discriminator проверяется
- [ ] Instruction data валидируется
- [ ] CPI calls безопасны
- [ ] Reentrancy protection установлена
- [ ] Compute units в пределах лимита

### Перед деплоем:
- [ ] Полное тестирование edge cases
- [ ] Аудит безопасности проведен
- [ ] Профилирование производительности
- [ ] Проверка на всех типах аккаунтов
- [ ] Тестирование с максимальными лимитами
- [ ] Проверка совместимости с другими программами

## 🎯 ОПТИМИЗАЦИЯ - ПРИОРИТЕТЫ

### 1. Критические оптимизации (обязательно):
- Zero-copy serialization для больших структур
- Inline функции для hot paths
- Предкомпилированные константы
- Минимизация syscalls

### 2. Продвинутые оптимизации (при необходимости):
- SIMD операции для массовых вычислений
- Lock-free структуры данных
- Memory prefetching
- Custom allocators

### 3. Экстремальные оптимизации (только для HFT):
- Unsafe + прямые syscalls
- Битовые операции вместо арифметики
- Статические буферы
- Предвычисленные lookup tables

## 🔗 БЫСТРЫЕ ССЫЛКИ

- **Solana Docs**: https://solana.com/docs
- **Anchor Book**: https://www.anchor-lang.com
- **rBPF Repository**: https://github.com/solana-labs/rbpf
- **Agave Validator**: https://github.com/anza-xyz/agave
- **Program Examples**: https://github.com/solana-labs/solana-program-library

---
**ПОМНИ**: Безопасность > Производительность > Удобство разработки
