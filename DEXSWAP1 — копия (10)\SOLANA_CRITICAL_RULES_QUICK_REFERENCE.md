# 🚨 SOLANA CRITICAL RULES - QUICK REFERENCE
## Критические правила низкоуровневого программирования на Solana

---

## ❌ НИКОГДА НЕ ДЕЛАТЬ (КРИТИЧЕСКИЕ ОШИБКИ)

### **1. Memory Management Violations**
```rust
// ❌ ЗАПРЕЩЕНО: std::collections в программах
use std::collections::HashMap; // НИКОГДА!

// ❌ ЗАПРЕЩЕНО: Лишние аллокации
let string = format!("Value: {}", value); // Дорого!
let vec = vec![1, 2, 3]; // Избегать в hot paths

// ❌ ЗАПРЕЩЕНО: Клонирование больших структур
let cloned_data = large_struct.clone(); // Медленно!

// ❌ ЗАПРЕЩЕНО: Рекурсивные функции
fn recursive(n: u32) -> u32 {
    if n == 0 { 1 } else { n * recursive(n - 1) } // CU взрыв!
}
```

### **2. Error Handling Violations**
```rust
// ❌ ЗАПРЕЩЕНО: unwrap() и expect()
account.serialize(&mut data).unwrap(); // Паника!
let value = result.expect("Failed"); // Паника!

// ❌ ЗАПРЕЩЕНО: Игнорирование ошибок
let _ = dangerous_operation(); // Скрытые баги!

// ❌ ЗАПРЕЩЕНО: Неспецифичные ошибки
return Err(ProgramError::Custom(0)); // Неинформативно!
```

### **3. Account Security Violations**
```rust
// ❌ ЗАПРЕЩЕНО: Пропуск проверки владельца
// if account.owner != &program_id { ... } // ОБЯЗАТЕЛЬНО!

// ❌ ЗАПРЕЩЕНО: Отсутствие проверки подписи
// if !account.is_signer { ... } // ОБЯЗАТЕЛЬНО для изменений!

// ❌ ЗАПРЕЩЕНО: Небезопасный доступ к данным
let data = unsafe { &*(account.data.as_ptr() as *const MyStruct) }; // Опасно!
```

### **4. Compute Units Violations**
```rust
// ❌ ЗАПРЕЩЕНО: Floating point операции
let result = 10.5 * 2.0; // Не поддерживается!

// ❌ ЗАПРЕЩЕНО: Сложные математические операции
let result = value.pow(large_exponent); // Дорого!

// ❌ ЗАПРЕЩЕНО: Неоптимизированные циклы
for i in 0..1000000 { // Слишком много итераций!
    expensive_operation();
}
```

---

## ✅ ОБЯЗАТЕЛЬНО ДЕЛАТЬ (КРИТИЧЕСКИЕ ТРЕБОВАНИЯ)

### **1. Account Validation**
```rust
// ✅ ОБЯЗАТЕЛЬНО: Проверка владельца
if account.owner != &program_id {
    return Err(ProgramError::IncorrectProgramId);
}

// ✅ ОБЯЗАТЕЛЬНО: Проверка подписи
if !account.is_signer {
    return Err(ProgramError::MissingRequiredSignature);
}

// ✅ ОБЯЗАТЕЛЬНО: Проверка размера данных
if account.data.len() < EXPECTED_SIZE {
    return Err(ProgramError::AccountDataTooSmall);
}

// ✅ ОБЯЗАТЕЛЬНО: Проверка возможности записи
if !account.is_writable {
    return Err(ProgramError::InvalidAccountData);
}
```

### **2. Safe Memory Operations**
```rust
// ✅ ОБЯЗАТЕЛЬНО: Zero-copy десериализация
use bytemuck::{Pod, Zeroable, try_from_bytes};

let data: &MyStruct = try_from_bytes(&account.data)
    .map_err(|_| ProgramError::InvalidAccountData)?;

// ✅ ОБЯЗАТЕЛЬНО: Безопасное приведение типов
let value = u64::from_le_bytes(
    bytes[0..8].try_into()
        .map_err(|_| ProgramError::InvalidInstructionData)?
);

// ✅ ОБЯЗАТЕЛЬНО: Проверка границ массива
if index >= array.len() {
    return Err(ProgramError::InvalidInstructionData);
}
```

### **3. Error Handling**
```rust
// ✅ ОБЯЗАТЕЛЬНО: Правильная обработка ошибок
account.serialize(&mut data)
    .map_err(|_| ProgramError::AccountDataTooSmall)?;

// ✅ ОБЯЗАТЕЛЬНО: Специфичные ошибки
if amount == 0 {
    return Err(ProgramError::InvalidInstructionData);
}

// ✅ ОБЯЗАТЕЛЬНО: Логирование ошибок
msg!("Transfer failed: insufficient funds");
```

### **4. Performance Optimization**
```rust
// ✅ ОБЯЗАТЕЛЬНО: Инлайн функции
#[inline(always)]
fn hot_path_function() {
    // Критический код
}

// ✅ ОБЯЗАТЕЛЬНО: Константы для магических чисел
const MAX_SUPPLY: u64 = 1_000_000_000;
const DECIMALS: u8 = 9;

// ✅ ОБЯЗАТЕЛЬНО: Предварительные вычисления
const PRECOMPUTED_HASH: [u8; 32] = [/* ... */];
```

---

## 🔧 ОПТИМИЗАЦИЯ PATTERNS

### **1. Memory Patterns**
```rust
// ✅ Эффективное выравнивание структур
#[repr(C, packed)]
struct OptimizedStruct {
    flag: u8,     // 1 байт
    value: u64,   // 8 байт
}

// ✅ Использование стека вместо кучи
let mut buffer = [0u8; 1024]; // Стек
// let mut buffer = vec![0u8; 1024]; // Куча - избегать!

// ✅ Переиспользование буферов
static mut REUSABLE_BUFFER: [u8; 4096] = [0; 4096];
```

### **2. Compute Units Patterns**
```rust
// ✅ Битовые операции вместо арифметических
let result = value & 0xFF; // Вместо value % 256
let doubled = value << 1;  // Вместо value * 2

// ✅ Batch операции
for item in items.chunks(128) {
    process_batch(item);
}

// ✅ Early returns
if condition {
    return Ok(()); // Экономия CU
}
expensive_operation();
```

### **3. Account Access Patterns**
```rust
// ✅ Группировка доступа к аккаунтам
{
    let mut account_data = account.data.borrow_mut();
    // Все операции с данными здесь
    account_data[0] = new_value;
    account_data[1] = another_value;
} // Автоматическое освобождение

// ✅ Минимизация заимствований
let data_slice = &account.data.borrow()[start..end];
process_data(data_slice);
```

---

## 🚨 SECURITY CHECKLIST

### **Перед каждым commit проверить:**

- [ ] Все аккаунты проверены на владельца
- [ ] Все изменяемые аккаунты требуют подписи
- [ ] Все входные данные валидированы
- [ ] Нет unwrap() или expect() в production коде
- [ ] Нет floating point операций
- [ ] Нет std::collections в программах
- [ ] Все ошибки правильно обработаны
- [ ] Нет рекурсивных функций
- [ ] Проверены границы массивов
- [ ] Используется zero-copy где возможно

---

## 📊 PERFORMANCE CHECKLIST

### **Перед оптимизацией проверить:**

- [ ] Профилирование показывает узкие места
- [ ] CU usage измерен и оптимизирован
- [ ] Memory layout оптимизирован
- [ ] Используются инлайн функции для hot paths
- [ ] Batch операции где возможно
- [ ] Предварительные вычисления для констант
- [ ] Битовые операции вместо арифметических
- [ ] Минимизированы системные вызовы

---

## 🔍 DEBUGGING PATTERNS

### **1. Logging**
```rust
// ✅ Структурированное логирование
msg!("Transfer: from={}, to={}, amount={}", from, to, amount);

// ✅ CU мониторинг
sol_log_compute_units();
expensive_operation();
sol_log_compute_units();

// ✅ Условное логирование
#[cfg(feature = "debug")]
msg!("Debug: state={:?}", state);
```

### **2. Testing**
```rust
// ✅ Comprehensive тесты
#[cfg(test)]
mod tests {
    use super::*;
    use solana_program_test::*;
    
    #[tokio::test]
    async fn test_edge_cases() {
        // Тестирование граничных случаев
    }
    
    #[tokio::test]
    async fn test_error_conditions() {
        // Тестирование ошибок
    }
}
```

---

## 🎯 QUICK REFERENCE COMMANDS

### **Проверка CU usage:**
```bash
solana logs --url devnet | grep "consumed"
```

### **Профилирование программы:**
```bash
cargo build-bpf --features profiling
```

### **Тестирование на devnet:**
```bash
solana program deploy --url devnet target/deploy/program.so
```

---

**⚡ ПОМНИТЕ**: Эти правила - основа безопасного и эффективного программирования на Solana. Нарушение любого из них может привести к критическим ошибкам, потере средств или неэффективной работе программы!
