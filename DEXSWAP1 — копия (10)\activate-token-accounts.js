#!/usr/bin/env node

/**
 * 🔧 АКТИВАЦИЯ ТОКЕН-АККАУНТОВ ДЛЯ НОВОГО КОШЕЛЬКА
 * ═══════════════════════════════════════════════════════════════════════════════
 * 🎯 ЦЕЛЬ: Создать Associated Token Accounts для USDC, USDT, WSOL
 * 💰 СТОИМОСТЬ: ~0.002 SOL за каждый аккаунт (~0.006 SOL общая стоимость)
 * 🔑 КОШЕЛЕК: bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV
 * ═══════════════════════════════════════════════════════════════════════════════
 */

const { Connection, PublicKey, Keypair, Transaction, SystemProgram, LAMPORTS_PER_SOL } = require('@solana/web3.js');
const {
  TOKEN_PROGRAM_ID,
  NATIVE_MINT,
  getAssociatedTokenAddress,
  createAssociatedTokenAccountInstruction,
  createSyncNativeInstruction
} = require('@solana/spl-token');
const bs58 = require('bs58');
require('dotenv').config({ path: '.env.solana' });

class TokenAccountActivator {
  constructor() {
    this.connection = null;
    this.wallet = null;

    // Токены для активации
    this.tokens = {
      USDC: {
        name: 'USDC',
        mint: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
        decimals: 6
      },
      USDT: {
        name: 'USDT',
        mint: 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB',
        decimals: 6
      },
      WSOL: {
        name: 'WSOL (Wrapped SOL)',
        mint: 'So11111111111111111111111111111111111111112',
        decimals: 9,
        isNative: true
      }
    };
  }

  /**
   * 🔧 ИНИЦИАЛИЗАЦИЯ
   */
  async initialize() {
    try {
      console.log('🔧 ИНИЦИАЛИЗАЦИЯ АКТИВАТОРА ТОКЕН-АККАУНТОВ');
      console.log('═══════════════════════════════════════════════════════════════');

      // Подключение к Solana
      const rpcUrl = process.env.QUICKNODE_RPC_URL || process.env.SOLANA_RPC_URL;
      this.connection = new Connection(rpcUrl, 'confirmed');
      console.log(`🌐 Подключение к RPC: ${rpcUrl.substring(0, 50)}...`);

      // Загрузка кошелька
      if (!process.env.WALLET_PRIVATE_KEY) {
        throw new Error('WALLET_PRIVATE_KEY не найден в .env.solana');
      }

      const privateKeyBytes = bs58.default ? bs58.default.decode(process.env.WALLET_PRIVATE_KEY) : bs58.decode(process.env.WALLET_PRIVATE_KEY);
      this.wallet = Keypair.fromSecretKey(privateKeyBytes);

      console.log(`🔑 Кошелек загружен: ${this.wallet.publicKey.toString()}`);
      console.log(`📍 Ожидаемый адрес: bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV`);

      // Проверяем соответствие адреса
      if (this.wallet.publicKey.toString() !== 'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV') {
        console.log('⚠️ ВНИМАНИЕ: Адрес кошелька не соответствует ожидаемому!');
        console.log(`   Загруженный: ${this.wallet.publicKey.toString()}`);
        console.log(`   Ожидаемый: bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV`);
      }

      // Проверяем баланс SOL
      const solBalance = await this.connection.getBalance(this.wallet.publicKey);
      const solBalanceUI = solBalance / LAMPORTS_PER_SOL;
      console.log(`💰 Баланс SOL: ${solBalanceUI.toFixed(6)} SOL`);

      if (solBalanceUI < 0.01) {
        throw new Error(`Недостаточно SOL для активации токен-аккаунтов. Требуется минимум 0.01 SOL, доступно: ${solBalanceUI.toFixed(6)} SOL`);
      }

      console.log('✅ Инициализация завершена успешно');
      return true;

    } catch (error) {
      console.error('❌ Ошибка инициализации:', error.message);
      return false;
    }
  }

  /**
   * 🔍 ПРОВЕРКА СУЩЕСТВУЮЩИХ ТОКЕН-АККАУНТОВ
   */
  async checkExistingAccounts() {
    try {
      console.log('\n🔍 ПРОВЕРКА СУЩЕСТВУЮЩИХ ТОКЕН-АККАУНТОВ');
      console.log('═══════════════════════════════════════════════════════════════');

      const results = {};

      for (const [symbol, token] of Object.entries(this.tokens)) {
        try {
          const mint = new PublicKey(token.mint);
          const ata = await getAssociatedTokenAddress(mint, this.wallet.publicKey);

          console.log(`\n📊 ${token.name} (${symbol}):`);
          console.log(`   Mint: ${token.mint}`);
          console.log(`   ATA: ${ata.toString()}`);

          // Проверяем существование аккаунта
          const accountInfo = await this.connection.getAccountInfo(ata);

          if (accountInfo) {
            console.log(`   ✅ Аккаунт существует`);
            console.log(`   📊 Размер: ${accountInfo.data.length} байт`);
            console.log(`   💰 Баланс: ${accountInfo.lamports} lamports`);
            results[symbol] = { exists: true, address: ata.toString() };
          } else {
            console.log(`   ❌ Аккаунт не существует - требуется создание`);
            results[symbol] = { exists: false, address: ata.toString() };
          }

        } catch (error) {
          console.log(`   ❌ Ошибка проверки ${symbol}: ${error.message}`);
          results[symbol] = { exists: false, error: error.message };
        }
      }

      return results;

    } catch (error) {
      console.error('❌ Ошибка проверки аккаунтов:', error.message);
      return {};
    }
  }

  /**
   * 🚀 СОЗДАНИЕ ТОКЕН-АККАУНТА
   */
  async createTokenAccount(symbol, token) {
    try {
      console.log(`\n🚀 СОЗДАНИЕ АККАУНТА ${token.name}`);
      console.log('─────────────────────────────────────────────────────────────');

      const mint = new PublicKey(token.mint);
      const ata = await getAssociatedTokenAddress(mint, this.wallet.publicKey);

      console.log(`📍 Создаем ATA: ${ata.toString()}`);

      const transaction = new Transaction();

      // Создаем Associated Token Account
      const createATAInstruction = createAssociatedTokenAccountInstruction(
        this.wallet.publicKey, // payer
        ata,                   // ata
        this.wallet.publicKey, // owner
        mint                   // mint
      );

      transaction.add(createATAInstruction);

      // Для WSOL добавляем немного SOL для активации
      if (token.isNative) {
        console.log('💰 Добавляем 0.001 SOL в WSOL аккаунт для активации...');

        const transferAmount = 0.001 * LAMPORTS_PER_SOL;
        const transferInstruction = SystemProgram.transfer({
          fromPubkey: this.wallet.publicKey,
          toPubkey: ata,
          lamports: transferAmount
        });

        const syncInstruction = createSyncNativeInstruction(ata);

        transaction.add(transferInstruction);
        transaction.add(syncInstruction);
      }

      // Получаем последний blockhash
      const { blockhash } = await this.connection.getLatestBlockhash();
      transaction.recentBlockhash = blockhash;
      transaction.feePayer = this.wallet.publicKey;

      // Подписываем транзакцию
      transaction.sign(this.wallet);

      console.log('📡 Отправляем транзакцию...');
      const signature = await this.connection.sendTransaction(transaction, [this.wallet], {
        skipPreflight: false,
        preflightCommitment: 'confirmed'
      });

      console.log(`✅ Транзакция отправлена: ${signature}`);

      // Ждем подтверждения
      console.log('⏳ Ждем подтверждения...');
      await this.connection.confirmTransaction(signature, 'confirmed');

      console.log(`✅ ${token.name} аккаунт создан успешно!`);

      // Проверяем результат
      const newAccountInfo = await this.connection.getAccountInfo(ata);
      if (newAccountInfo) {
        console.log(`📊 Размер аккаунта: ${newAccountInfo.data.length} байт`);
        console.log(`💰 Баланс аккаунта: ${newAccountInfo.lamports} lamports`);

        if (token.isNative) {
          const wsolBalance = newAccountInfo.lamports / LAMPORTS_PER_SOL;
          console.log(`💰 WSOL баланс: ${wsolBalance.toFixed(6)} SOL`);
        }
      }

      return { success: true, signature, address: ata.toString() };

    } catch (error) {
      console.error(`❌ Ошибка создания аккаунта ${token.name}:`, error.message);
      return { success: false, error: error.message };
    }
  }

  /**
   * 🎯 АКТИВАЦИЯ ВСЕХ ТОКЕН-АККАУНТОВ
   */
  async activateAllAccounts() {
    try {
      console.log('\n🎯 АКТИВАЦИЯ ВСЕХ ТОКЕН-АККАУНТОВ');
      console.log('═══════════════════════════════════════════════════════════════');

      // Проверяем существующие аккаунты
      const existingAccounts = await this.checkExistingAccounts();

      const results = {};
      let totalCost = 0;

      // Создаем недостающие аккаунты
      for (const [symbol, token] of Object.entries(this.tokens)) {
        if (existingAccounts[symbol] && existingAccounts[symbol].exists) {
          console.log(`\n✅ ${token.name} аккаунт уже существует - пропускаем`);
          results[symbol] = {
            success: true,
            existed: true,
            address: existingAccounts[symbol].address
          };
        } else {
          console.log(`\n🔧 Создаем аккаунт ${token.name}...`);
          const result = await this.createTokenAccount(symbol, token);
          results[symbol] = result;

          if (result.success) {
            totalCost += 0.002044; // Примерная стоимость создания аккаунта
          }

          // Пауза между созданием аккаунтов
          if (result.success) {
            console.log('⏳ Пауза 2 секунды перед следующим аккаунтом...');
            await new Promise(resolve => setTimeout(resolve, 2000));
          }
        }
      }

      // Итоговый отчет
      console.log('\n📊 ИТОГОВЫЙ ОТЧЕТ АКТИВАЦИИ');
      console.log('═══════════════════════════════════════════════════════════════');

      let successCount = 0;
      let existedCount = 0;
      let failedCount = 0;

      for (const [symbol, result] of Object.entries(results)) {
        const token = this.tokens[symbol];
        console.log(`\n${token.name} (${symbol}):`);

        if (result.success) {
          if (result.existed) {
            console.log(`   ✅ Уже существовал`);
            existedCount++;
          } else {
            console.log(`   ✅ Создан успешно`);
            console.log(`   📍 Адрес: ${result.address}`);
            if (result.signature) {
              console.log(`   🔗 Транзакция: ${result.signature}`);
            }
            successCount++;
          }
        } else {
          console.log(`   ❌ Ошибка: ${result.error}`);
          failedCount++;
        }
      }

      console.log(`\n📈 СТАТИСТИКА:`);
      console.log(`   ✅ Создано новых: ${successCount}`);
      console.log(`   📋 Уже существовало: ${existedCount}`);
      console.log(`   ❌ Ошибок: ${failedCount}`);
      console.log(`   💰 Общая стоимость: ~${totalCost.toFixed(6)} SOL`);

      const allSuccess = failedCount === 0;
      if (allSuccess) {
        console.log('\n🎉 ВСЕ ТОКЕН-АККАУНТЫ АКТИВИРОВАНЫ УСПЕШНО!');
        console.log('✅ Кошелек готов для торговли с USDC, USDT и WSOL');
      } else {
        console.log('\n⚠️ НЕКОТОРЫЕ АККАУНТЫ НЕ УДАЛОСЬ СОЗДАТЬ');
        console.log('🔧 Проверьте ошибки выше и повторите попытку');
      }

      return { success: allSuccess, results, totalCost };

    } catch (error) {
      console.error('❌ Ошибка активации аккаунтов:', error.message);
      return { success: false, error: error.message };
    }
  }

  /**
   * 🚀 ГЛАВНАЯ ФУНКЦИЯ
   */
  async run() {
    try {
      console.log('🚀 ЗАПУСК АКТИВАТОРА ТОКЕН-АККАУНТОВ');
      console.log('═══════════════════════════════════════════════════════════════');
      console.log('🎯 Цель: Активировать USDC, USDT, WSOL аккаунты');
      console.log('🔑 Кошелек: bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV');
      console.log('💰 Стоимость: ~0.006 SOL (максимум)');

      // Инициализация
      const initResult = await this.initialize();
      if (!initResult) {
        throw new Error('Не удалось инициализировать активатор');
      }

      // Активация аккаунтов
      const activationResult = await this.activateAllAccounts();

      if (activationResult.success) {
        console.log('\n🎉 АКТИВАЦИЯ ЗАВЕРШЕНА УСПЕШНО!');
        return true;
      } else {
        console.log('\n❌ АКТИВАЦИЯ ЗАВЕРШЕНА С ОШИБКАМИ');
        return false;
      }

    } catch (error) {
      console.error('❌ КРИТИЧЕСКАЯ ОШИБКА:', error.message);
      return false;
    }
  }
}

// Запуск скрипта
if (require.main === module) {
  const activator = new TokenAccountActivator();
  activator.run()
    .then(success => {
      console.log(`\n🏁 АКТИВАЦИЯ ЗАВЕРШЕНА: ${success ? 'УСПЕШНО' : 'С ОШИБКАМИ'}`);
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ КРИТИЧЕСКАЯ ОШИБКА АКТИВАЦИИ:', error.message);
      process.exit(1);
    });
}

module.exports = TokenAccountActivator;
