/**
 * 🔥 ДОБАВЛЕНИЕ 14 РЕАЛЬНЫХ АДРЕСОВ В КАСТОМНУЮ ALT ТАБЛИЦУ
 * РАБОЧИЙ СКРИПТ через JavaScript API (без CLI)
 * ЭКОНОМИЯ: 14 × 31 bytes = 434 bytes (ДОЛЖНО РЕШИТЬ ПРОБЛЕМУ РАЗМЕРА!)
 */

const {
    Connection,
    PublicKey,
    Keypair,
    TransactionMessage,
    VersionedTransaction,
    AddressLookupTableProgram
} = require('@solana/web3.js');
const fs = require('fs');
require('dotenv').config({ path: '.env.solana' });

async function add2PositionKeypairsToALT() {
    console.log('🔥 ДОБАВЛЕНИЕ 2 POSITION KEYPAIRS В КАСТОМНУЮ ALT ТАБЛИЦУ');
    console.log('🎯 ИСПОЛЬЗУЕМ РАБОЧИЙ JAVASCRIPT API (AddressLookupTableProgram)');
    console.log('💾 ЭКОНОМИЯ: 2 × 31 bytes = 62 bytes');
    console.log('=' .repeat(80));

    try {
        // 1. Подключение к RPC для проверки
        const connection = new Connection(process.env.QUICKNODE_RPC_URL);
        console.log('✅ Подключение к RPC установлено');

        // 2. Загружаем кошелек для подписи транзакций
        let wallet;
        if (fs.existsSync('wallet.json')) {
            const walletData = JSON.parse(fs.readFileSync('wallet.json', 'utf8'));
            wallet = Keypair.fromSecretKey(new Uint8Array(walletData));
            console.log(`✅ Кошелек загружен: ${wallet.publicKey.toString()}`);
        } else {
            throw new Error('Файл кошелька wallet.json не найден!');
        }

        // 3. ДЕНЬГИ ЕСТЬ - РАБОТАЕМ БЕЗ ПРОВЕРОК!
        console.log(`💰 ДЕНЬГИ ЕСТЬ - ПРОПУСКАЕМ ПРОВЕРКУ БАЛАНСА!`);

        // 4. Кастомная ALT таблица
        const customALTAddress = new PublicKey('FAeyUf4AdG7vTQ2Q89r9hWBkPdYkykJKG4CxgqArBGXe');
        console.log(`📋 Кастомная ALT таблица: ${customALTAddress.toString()}`);

        // 5. ДОБАВЛЯЕМ 14 РЕАЛЬНЫХ АДРЕСОВ ИЗ ИНСТРУКЦИЙ + 🔥 НОВЫЕ ПОЗИЦИИ!
        const positionKeypairs = [
            'CfKmtCvWTsXSEAwJaHBPyrRmoanPqZfJSNmyJFFVnpmn',  // 🔥 НОВАЯ POOL_1 Position (диапазон ±10 бинов)
            '7ydf4bfon7f8YSu8xR35QsKU5Tynb5CKJ36fr3vnycis',  // 🔥 НОВАЯ POOL_2 Position (диапазон ±10 бинов)
            'Gc5zPCzW6JzDSnkgLtm3K5LUfFNpk92PpVB3M89CDTHq',  // Token account
            'D1ZN9Wj1fRSUQfCjhvnu1hqDMT7hzjzBBpi12nVniYD6',  // Token account
            'BuMzEa4ajuG9mNFgBmf6nEQQr6LJB8xqcQWLwnV8ABpp',  // Pool related
            'BYbync1QmagyM1oL59dwYdcKdJ9yhtrMxDAEwUaXVW3T',  // Token account
            'DjyDh54Q6LqPkARYmgjgLUKmwo1BZGDcY5Z5MFwvPc4N',  // Token account
            'DwZz4S1Z1LBXomzmncQRVKCYhjCqSAMQ6RPKbUAadr7H',  // Token account
            '4N22J4vW2juHocTntJNmXywSonYjkndCwahjZ2cYLDgb',  // Token account
            'EYj9xKw6ZszwpyNibHY7JD5o3QgTVrSdcBp1fMJhrR9o',  // Token account
            'CoaxzEh8p5YyGLcj36Eo3cUThVJxeKCs7qvLAGDYwBcz',  // Token account
            'BuMzEa4aJHKjkecKqeKXGPiZZHmPSNqeJ1q6QkMrKBdv',  // Token account
            '41J5yxxAQbkoCPFoxcGA9QhsvEEEVnDihmEyQPYPwWzQ',  // Token account
            'EGf71UkFXVC726gxMzmWDjJzHzjhNW7WhoNfPUMHGBnN'   // Token account
        ];
        console.log(`🔑 ДОБАВЛЯЕМ 14 АДРЕСОВ + 🔥 НОВЫЕ ПОЗИЦИИ:`);
        const names = [
            '🔥 НОВАЯ POOL_1 Position', '🔥 НОВАЯ POOL_2 Position', 'Token account', 'Token account',
            'Pool related', 'Token account', 'Token account', 'Token account',
            'Token account', 'Token account', 'Token account', 'Token account',
            'Token account', 'Token account'
        ];
        positionKeypairs.forEach((key, i) => {
            console.log(`   ${i + 1}. ${key} (${names[i]})`);
        });

        if (false) { // Отключаем загрузку файла
            throw new Error(`Файл ${scanFile} не найден! Сначала запустите simple-keys-comparison.js`);
        }

        // ДОБАВЛЯЕМ 2 POSITION KEYPAIRS
        const addressesToAdd = positionKeypairs;

        console.log(`📊 Адресов для добавления: ${addressesToAdd.length}`);
        console.log(`🎯 Экономия: ${addressesToAdd.length} × 31 bytes = ${addressesToAdd.length * 31} bytes`);

        // 6. Проверяем текущее состояние ALT таблицы
        console.log('\n🔍 ПРОВЕРКА ТЕКУЩЕГО СОСТОЯНИЯ ALT ТАБЛИЦЫ...');
        const altAccount = await connection.getAddressLookupTable(customALTAddress);

        if (!altAccount || !altAccount.value) {
            throw new Error('Кастомная ALT таблица не найдена в блокчейне!');
        }

        const currentAddresses = altAccount.value.state.addresses.map(addr => addr.toString());
        console.log(`📊 Текущих адресов в ALT: ${currentAddresses.length}`);

        // 6. ДОБАВЛЯЕМ ВСЕ АДРЕСА БЕЗ ПРОВЕРОК И ОГРАНИЧЕНИЙ!
        const newAddresses = addressesToAdd; // БЕЗ ФИЛЬТРАЦИИ!
        console.log(`🔥 ДОБАВЛЯЕМ ВСЕ ${newAddresses.length} АДРЕСОВ БЕЗ ПРОВЕРОК!`);

        // 8. Показываем адреса которые будем добавлять
        console.log(`\n📋 АДРЕСА ДЛЯ ДОБАВЛЕНИЯ (${newAddresses.length}):`);
        newAddresses.forEach((addr, i) => {
            console.log(`   ${i + 1}. ${addr}`);
        });

        // 9. Создаем инструкцию для расширения ALT таблицы
        console.log(`\n🔧 СОЗДАНИЕ ИНСТРУКЦИИ ДЛЯ ДОБАВЛЕНИЯ ${newAddresses.length} АДРЕСОВ...`);

        // Валидируем и конвертируем строки в PublicKey
        const addressPublicKeys = [];
        for (const addr of newAddresses) {
            try {
                const pubkey = new PublicKey(addr);
                addressPublicKeys.push(pubkey);
                console.log(`✅ Валидный адрес: ${addr.slice(0,8)}...${addr.slice(-8)}`);
            } catch (error) {
                console.log(`❌ ПРОПУСКАЕМ невалидный адрес: ${addr} - ${error.message}`);
            }
        }

        console.log(`✅ Валидных адресов: ${addressPublicKeys.length} из ${newAddresses.length}`);

        // Создаем инструкцию extend
        const extendInstruction = AddressLookupTableProgram.extendLookupTable({
            payer: wallet.publicKey,
            authority: wallet.publicKey,
            lookupTable: customALTAddress,
            addresses: addressPublicKeys
        });

        console.log('✅ Инструкция extend создана');

        // 10. Создаем и отправляем транзакцию
        console.log('\n🚀 СОЗДАНИЕ И ОТПРАВКА ТРАНЗАКЦИИ...');

        // Получаем последний blockhash
        const { blockhash } = await connection.getLatestBlockhash('confirmed');
        console.log(`✅ Blockhash получен: ${blockhash.slice(0, 8)}...`);

        // Создаем транзакцию
        const message = new TransactionMessage({
            payerKey: wallet.publicKey,
            recentBlockhash: blockhash,
            instructions: [extendInstruction]
        }).compileToV0Message();

        const transaction = new VersionedTransaction(message);

        // Подписываем транзакцию
        transaction.sign([wallet]);
        console.log('✅ Транзакция подписана');

        // Отправляем транзакцию БЕЗ PREFLIGHT
        const signature = await connection.sendTransaction(transaction, {
            maxRetries: 5,
            preflightCommitment: 'confirmed',
            skipPreflight: true
        });

        console.log(`🚀 Транзакция отправлена: ${signature}`);

        // 11. Ждем подтверждения
        console.log('⏳ Ожидание подтверждения транзакции...');

        const confirmation = await connection.confirmTransaction({
            signature,
            blockhash,
            lastValidBlockHeight: (await connection.getLatestBlockhash()).lastValidBlockHeight
        }, 'confirmed');

        if (confirmation.value.err) {
            throw new Error(`Транзакция провалена: ${JSON.stringify(confirmation.value.err)}`);
        }

        console.log('✅ Транзакция подтверждена!');

        // 12. Проверяем результат
        console.log('\n🔍 ПРОВЕРКА РЕЗУЛЬТАТА...');

        // Ждем дольше для обновления состояния (блокчейн может быть медленным)
        console.log('⏳ Ожидание обновления ALT таблицы (10 секунд)...');
        await new Promise(resolve => setTimeout(resolve, 10000));

        let updatedAddresses = currentAddresses;
        let attempts = 0;
        const maxAttempts = 5;

        // Пытаемся несколько раз получить обновленное состояние
        while (attempts < maxAttempts) {
            try {
                const updatedAltAccount = await connection.getAddressLookupTable(customALTAddress);
                if (updatedAltAccount && updatedAltAccount.value) {
                    updatedAddresses = updatedAltAccount.value.state.addresses.map(addr => addr.toString());

                    if (updatedAddresses.length > currentAddresses.length) {
                        console.log(`✅ ALT таблица обновлена! Попытка ${attempts + 1}`);
                        break;
                    }
                }

                attempts++;
                if (attempts < maxAttempts) {
                    console.log(`⏳ Попытка ${attempts}: ALT еще не обновлена, ждем еще 5 секунд...`);
                    await new Promise(resolve => setTimeout(resolve, 5000));
                }
            } catch (error) {
                console.log(`⚠️ Ошибка при проверке ALT (попытка ${attempts + 1}): ${error.message}`);
                attempts++;
                await new Promise(resolve => setTimeout(resolve, 3000));
            }
        }

        console.log(`📊 Адресов в ALT до обновления: ${currentAddresses.length}`);
        console.log(`📊 Адресов в ALT после обновления: ${updatedAddresses.length}`);
        console.log(`✅ Добавлено новых адресов: ${updatedAddresses.length - currentAddresses.length}`);

        // 13. Сохраняем результат
        const result = {
            timestamp: new Date().toISOString(),
            method: 'javascript-api',
            transactionSignature: signature,
            altTableAddress: customALTAddress.toString(),
            addressesBeforeUpdate: currentAddresses.length,
            addressesAfterUpdate: updatedAddresses.length,
            addressesAdded: updatedAddresses.length - currentAddresses.length,
            newAddressesAdded: newAddresses,
            success: true
        };

        const resultFile = 'alt-update-result.json';
        fs.writeFileSync(resultFile, JSON.stringify(result, null, 2));
        console.log(`✅ Результат сохранен в: ${resultFile}`);

        console.log(`\n${'='.repeat(80)}`);
        console.log('🎉 POSITION KEYPAIRS УСПЕШНО ДОБАВЛЕНЫ В ALT ТАБЛИЦУ!');
        console.log(`📊 Метод: JavaScript API (AddressLookupTableProgram)`);
        console.log(`📊 Транзакция: ${signature}`);
        console.log(`📊 Добавлено адресов: ${updatedAddresses.length - currentAddresses.length}`);
        console.log(`📊 Всего адресов в ALT: ${updatedAddresses.length}`);
        console.log(`💾 Экономия: ${updatedAddresses.length - currentAddresses.length} × 31 bytes = ${(updatedAddresses.length - currentAddresses.length) * 31} bytes`);
        console.log(`${'='.repeat(80)}`);

    } catch (error) {
        console.error('❌ Ошибка добавления адресов:', error.message);
        console.error(error.stack);

        // Сохраняем ошибку
        const errorResult = {
            timestamp: new Date().toISOString(),
            method: 'javascript-api',
            error: error.message,
            stack: error.stack,
            success: false
        };

        fs.writeFileSync('alt-update-error.json', JSON.stringify(errorResult, null, 2));

        console.log('\n💡 ВОЗМОЖНЫЕ РЕШЕНИЯ:');
        console.log('1. Проверьте права доступа к ALT таблице (authority)');
        console.log('2. Попробуйте добавить адреса по частям (меньшими группами)');
        console.log('3. Проверьте что кошелек является authority для ALT таблицы');
    }
}

// Запуск добавления
if (require.main === module) {
    add2PositionKeypairsToALT();
}

module.exports = { add2PositionKeypairsToALT };
