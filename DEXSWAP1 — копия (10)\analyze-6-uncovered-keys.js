#!/usr/bin/env node

/**
 * 🔍 АНАЛИЗ 6 НЕПОКРЫТЫХ КЛЮЧЕЙ ALT
 * Показывает, какие именно ключи не покрыты Jupiter ALT
 */

const { Connection, PublicKey, Keypair, TransactionMessage, VersionedTransaction } = require('@solana/web3.js');
const AtomicTransactionBuilderFixed = require('./src/atomic-transaction-builder-fixed');
const fs = require('fs');

async function analyze6UncoveredKeys() {
  try {
    console.log('🔍 АНАЛИЗ 6 НЕПОКРЫТЫХ КЛЮЧЕЙ ALT');
    console.log('═══════════════════════════════════════════════════════════');

    // Загружаем wallet
    function base58Decode(str) {
      const alphabet = '**********************************************************';
      let decoded = 0n;
      let multi = 1n;
      for (let i = str.length - 1; i >= 0; i--) {
        const char = str[i];
        const index = alphabet.indexOf(char);
        if (index === -1) throw new Error(`Invalid character: ${char}`);
        decoded += BigInt(index) * multi;
        multi *= 58n;
      }
      const bytes = [];
      while (decoded > 0n) {
        bytes.unshift(Number(decoded % 256n));
        decoded = decoded / 256n;
      }
      return new Uint8Array(bytes);
    }

    const walletData = JSON.parse(fs.readFileSync('./wallet.json', 'utf8'));
    const secretKey = base58Decode(walletData.privateKey);
    const userKeypair = Keypair.fromSecretKey(secretKey);
    const userPublicKey = userKeypair.publicKey;

    console.log(`👤 Wallet: ${userPublicKey.toString().slice(0, 8)}...`);

    // Подключение к RPC
    const connection = new Connection('https://api.mainnet-beta.solana.com', 'confirmed');

    // Создаем AtomicTransactionBuilder
    const builder = new AtomicTransactionBuilderFixed(
      connection,
      userKeypair
    );

    console.log('🔧 Создаем тестовую транзакцию...');

    // Создаем простую тестовую транзакцию с правильными параметрами
    const result = await builder.buildAtomicFlashLoanTransaction({
      inputMint: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // USDC
      outputMint: 'So11111111111111111111111111111111111111112', // SOL
      amount: 100000000, // 100 USDC
      slippageBps: 50
    });
    
    if (result && result.transaction) {
      console.log('✅ Транзакция создана успешно');
      
      // Анализируем ALT покрытие
      if (result.altAnalysis) {
        console.log('\n📊 АНАЛИЗ ALT ПОКРЫТИЯ:');
        console.log('═══════════════════════════════════════════════════════════');
        
        const analysis = result.altAnalysis;
        console.log(`📊 Всего ключей в ALT: ${analysis.totalAltKeys || 'N/A'}`);
        console.log(`📊 Ключей в инструкциях: ${analysis.totalInstructionKeys || 'N/A'}`);
        console.log(`✅ Покрыто ALT: ${analysis.coveredKeys || 'N/A'}`);
        console.log(`❌ НЕ покрыто ALT: ${analysis.uncoveredKeys || 'N/A'}`);
        
        if (analysis.uncoveredKeysList && analysis.uncoveredKeysList.length > 0) {
          console.log('\n⚠️ НЕПОКРЫТЫЕ КЛЮЧИ (первые 10):');
          analysis.uncoveredKeysList.slice(0, 10).forEach((key, i) => {
            const description = builder.getKeyDescription ? builder.getKeyDescription(key) : 'Unknown';
            console.log(`     ${i + 1}. ${key.slice(0, 8)}... (${description})`);
          });
        }
      }
    } else {
      console.log('❌ Не удалось создать транзакцию');
    }

  } catch (error) {
    console.error(`❌ Ошибка анализа: ${error.message}`);
    console.error(error.stack);
  }
}

// Запускаем анализ
if (require.main === module) {
  analyze6UncoveredKeys().catch(console.error);
}

module.exports = { analyze6UncoveredKeys };
