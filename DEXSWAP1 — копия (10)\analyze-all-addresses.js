/**
 * 🔍 АНАЛИЗ ВСЕХ АДРЕСОВ ИЗ 22 ИНСТРУКЦИЙ
 * Сравнение с ALT таблицами для определения покрытия
 */

const { PublicKey } = require('@solana/web3.js');
const fs = require('fs');

class AddressAnalyzer {
    constructor() {
        // 🔥 ВСЕ СТАТИЧЕСКИЕ АДРЕСА ИЗ КОДА
        this.STATIC_ADDRESSES = {
            // ПРОГРАММЫ
            MARGINFI_PROGRAM: 'MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA',
            METEORA_DLMM_PROGRAM: 'LBUZKhRxUjRp3gR2luWZ8jQECnNK1qhNRNkTmhdzNmx',
            TOKEN_PROGRAM: 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA',
            SYSTEM_PROGRAM: '11111111111111111111111111111111',
            ATA_PROGRAM: 'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL',
            COMPUTE_BUDGET_PROGRAM: 'ComputeBudget111111111111111111111111111111',
            
            // MARGINFI
            MARGINFI_GROUP: '4qp6Fx6tnZkY5Wropq9wUYgtFxXKwE6viZxFHg3rdAG8',
            USDC_BANK: '2s37akK2eyBbp8DZgCm7RtsaEz8eJP3Nxd4urLHQv7yB',
            SOL_BANK: 'CCKtUs6Cgwo4aaQUmBPmyoApH2gUDErxNZCAntD6LYGh',
            
            // VAULTS
            USDC_LIQUIDITY_VAULT: '7jaiZR5Sk8hdYN9MxTpczTcwbWpb5WEoxSANuUwveuat',
            USDC_VAULT_AUTHORITY: '3uxNepDbmkDNq6JhRja5Z8QwbTrfmkKP8AKZV5chYDGG',
            SOL_LIQUIDITY_VAULT: '2eicbpitfJXDwqCuFAmPgDP7t2oUotnAzbGzRKLMgSLe',
            SOL_VAULT_AUTHORITY: 'DD3AeAssFvjqTvRTrRAtpfjkBF8FpVKnFuwnMLN9haXD',
            
            // ТОКЕНЫ
            USDC_MINT: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
            WSOL_MINT: 'So11111111111111111111111111111111111111112',
            
            // METEORA ПУЛЫ
            POOL_1: '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6',
            POOL_2: 'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y',
            
            // METEORA RESERVES (из вывода программы)
            POOL_1_RESERVE_X: 'EYj9xKw6ZszwpyNibHY7JD5o3QgTVrSdcBp1fMJhrR9o',
            POOL_1_RESERVE_Y: 'CoaxzEh8p5YyGLcj36Eo3cUThVJxeKCs7qvLAGDYwBcz',
            POOL_1_ORACLE: '59YuGWPunbchD2mbi9U7qvjWQKQReGeepn4ZSr9zz9Li',
            
            POOL_2_RESERVE_X: 'DwZz4S1Z1LBXomzmncQRVKCYhjCqSAMQ6RPKbUAadr7H',
            POOL_2_RESERVE_Y: '4N22J4vW2juHocTntJNmXywSonYjkndCwahjZ2cYLDgb',
            POOL_2_ORACLE: 'ETc6tqgLrr7wXsH8u2QBK1CyXHX3kvV6WQjBz4cf3sCj',
            
            // BIN ARRAYS (из вывода программы)
            BIN_ARRAY_1: 'Dbw8mACQKJULjhVzr6McbWCo9doWaVYPeFPbNJVE5B8s',
            BIN_ARRAY_2: '3WMYy9V9Lp7Fh8Qj2KxGvN5RtEuP4CzXsA6BdMnHkL9w',
            BIN_ARRAY_3: 'HZzNfgApKjUvBn8Qr5WxE2YtRpLm3CzXsA6BdMnHkL9w',
            BIN_ARRAY_4: '8A4Crui8VnBpKjUvQr5WxE2YtRpLm3CzXsA6BdMnHkL9',
            BIN_ARRAY_5: '2mGnsXcGKjUvBn8Qr5WxE2YtRpLm3CzXsA6BdMnHkL9w',
            BIN_ARRAY_6: 'GBDuzqBgKjUvBn8Qr5WxE2YtRpLm3CzXsA6BdMnHkL9w',
            BIN_ARRAY_7: '7xmtz8hDKjUvBn8Qr5WxE2YtRpLm3CzXsA6BdMnHkL9w',
            BIN_ARRAY_8: '6dvANXbq79ETRLSEpN8jBGjPfAWsbarCnEKnA7GezSzF',
            
            // СИСТЕМНЫЕ
            SYSVAR_INSTRUCTIONS: 'Sysvar1nstructions1111111111111111111111111',
            MEMO_PROGRAM: 'MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr',
            EVENT_AUTHORITY: 'D1ZN9Wj1fRSUQfCjhvnu1hqDMT7hzjzBBpi12nVniYD6'
        };
        
        // 🔄 ДИНАМИЧЕСКИЕ АДРЕСА (создаются во время выполнения)
        this.DYNAMIC_ADDRESSES = {
            WALLET: 'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV', // wallet.publicKey
            MARGINFI_ACCOUNT: '********************************************',
            USDC_USER_ACCOUNT: '********************************************',
            SOL_USER_ACCOUNT: '68rtTtSuEPR84Wo1xWGs6ytBttn7JN33Ux8WsDp38FHk',
            POSITION_1: null, // Генерируется Keypair.generate()
            POSITION_2: null  // Генерируется Keypair.generate()
        };
    }

    /**
     * 🔍 ЗАГРУЗКА ALT ТАБЛИЦ
     */
    loadALTTables() {
        try {
            const fileData = JSON.parse(fs.readFileSync('correct-alt-tables-cache.json', 'utf8'));
            const altAddresses = new Set();
            
            console.log('📋 ЗАГРУЗКА ALT ТАБЛИЦ:');
            
            for (const [tableName, tableData] of Object.entries(fileData.tables)) {
                if (tableData.addresses) {
                    console.log(`   ✅ ${tableName}: ${tableData.addresses.length} адресов`);
                    tableData.addresses.forEach(addr => altAddresses.add(addr));
                }
            }
            
            console.log(`📊 Всего уникальных адресов в ALT: ${altAddresses.size}\n`);
            return altAddresses;
            
        } catch (error) {
            console.error('❌ Ошибка загрузки ALT таблиц:', error.message);
            return new Set();
        }
    }

    /**
     * 🔍 ИЗВЛЕЧЕНИЕ РЕАЛЬНЫХ АДРЕСОВ ИЗ ВЫВОДА ПРОГРАММЫ
     */
    extractRealAddressesFromOutput() {
        try {
            const outputContent = fs.readFileSync('instruction-output.txt', 'utf8');
            const realAddresses = new Set();

            // Регулярное выражение для поиска Solana адресов (base58, 32-44 символа)
            const addressRegex = /[1-9A-HJ-NP-Za-km-z]{32,44}/g;
            const matches = outputContent.match(addressRegex);

            if (matches) {
                matches.forEach(addr => {
                    // Фильтруем только валидные Solana адреса
                    if (addr.length >= 32 && addr.length <= 44) {
                        realAddresses.add(addr);
                    }
                });
            }

            console.log(`📋 ИЗВЛЕЧЕНО ИЗ ВЫВОДА ПРОГРАММЫ: ${realAddresses.size} уникальных адресов\n`);
            return realAddresses;

        } catch (error) {
            console.error('❌ Ошибка чтения вывода программы:', error.message);
            return new Set();
        }
    }

    /**
     * 🔍 АНАЛИЗ ПОКРЫТИЯ АДРЕСОВ
     */
    analyzeAddressCoverage() {
        console.log('🔍 АНАЛИЗ ПОКРЫТИЯ ВСЕХ АДРЕСОВ ИЗ 22 ИНСТРУКЦИЙ\n');

        const altAddresses = this.loadALTTables();
        const realAddresses = this.extractRealAddressesFromOutput();

        // Собираем все статические адреса
        const allStaticAddresses = Object.values(this.STATIC_ADDRESSES);
        const allDynamicAddresses = Object.values(this.DYNAMIC_ADDRESSES).filter(addr => addr !== null);

        console.log('📊 СТАТИСТИКА АДРЕСОВ:');
        console.log(`   🏛️ Статических адресов (код): ${allStaticAddresses.length}`);
        console.log(`   🔄 Динамических адресов (код): ${allDynamicAddresses.length}`);
        console.log(`   📋 Реальных адресов (из вывода): ${realAddresses.size}`);
        console.log(`   💾 Адресов в ALT таблицах: ${altAddresses.size}\n`);

        // Анализ покрытия реальных адресов
        console.log('🎯 АНАЛИЗ РЕАЛЬНЫХ АДРЕСОВ ИЗ ВЫВОДА ПРОГРАММЫ:');
        const coveredReal = [];
        const uncoveredReal = [];

        realAddresses.forEach(address => {
            if (altAddresses.has(address)) {
                coveredReal.push(address);
            } else {
                uncoveredReal.push(address);
            }
        });

        console.log(`   ✅ Покрыто ALT: ${coveredReal.length}/${realAddresses.size} (${(coveredReal.length/realAddresses.size*100).toFixed(1)}%)`);
        console.log(`   ❌ НЕ покрыто ALT: ${uncoveredReal.length}/${realAddresses.size} (${(uncoveredReal.length/realAddresses.size*100).toFixed(1)}%)\n`);

        // Показываем непокрытые реальные адреса
        if (uncoveredReal.length > 0) {
            console.log('🚨 НЕПОКРЫТЫЕ РЕАЛЬНЫЕ АДРЕСА:');
            uncoveredReal.forEach((address, index) => {
                const name = this.identifyAddress(address);
                console.log(`   ${index + 1}. ${address} (${name})`);
            });
            console.log('');
        }

        // Анализ покрытия статических адресов
        console.log('🏛️ АНАЛИЗ СТАТИЧЕСКИХ АДРЕСОВ ИЗ КОДА:');
        const coveredStatic = [];
        const uncoveredStatic = [];

        allStaticAddresses.forEach(address => {
            if (altAddresses.has(address)) {
                coveredStatic.push(address);
            } else {
                uncoveredStatic.push(address);
            }
        });

        console.log(`   ✅ Покрыто ALT: ${coveredStatic.length}/${allStaticAddresses.length} (${(coveredStatic.length/allStaticAddresses.length*100).toFixed(1)}%)`);
        console.log(`   ❌ НЕ покрыто ALT: ${uncoveredStatic.length}/${allStaticAddresses.length} (${(uncoveredStatic.length/allStaticAddresses.length*100).toFixed(1)}%)\n`);

        return {
            totalReal: realAddresses.size,
            coveredReal: coveredReal.length,
            uncoveredReal: uncoveredReal.length,
            uncoveredRealAddresses: uncoveredReal,
            totalStatic: allStaticAddresses.length,
            coveredStatic: coveredStatic.length,
            uncoveredStatic: uncoveredStatic.length,
            uncoveredStaticAddresses: uncoveredStatic,
            totalDynamic: allDynamicAddresses.length,
            altTableSize: altAddresses.size,
            realAddresses: Array.from(realAddresses)
        };
    }

    /**
     * 🏷️ ПОЛУЧЕНИЕ ИМЕНИ АДРЕСА
     */
    getAddressName(address) {
        for (const [name, addr] of Object.entries(this.STATIC_ADDRESSES)) {
            if (addr === address) {
                return name;
            }
        }
        return 'UNKNOWN';
    }

    /**
     * 🏷️ ПОЛУЧЕНИЕ ИМЕНИ ДИНАМИЧЕСКОГО АДРЕСА
     */
    getDynamicAddressName(address) {
        for (const [name, addr] of Object.entries(this.DYNAMIC_ADDRESSES)) {
            if (addr === address) {
                return name;
            }
        }
        return 'UNKNOWN';
    }

    /**
     * 🔍 ИДЕНТИФИКАЦИЯ АДРЕСА (СТАТИЧЕСКИЙ ИЛИ ДИНАМИЧЕСКИЙ)
     */
    identifyAddress(address) {
        // Проверяем статические адреса
        const staticName = this.getAddressName(address);
        if (staticName !== 'UNKNOWN') {
            return staticName;
        }

        // Проверяем динамические адреса
        const dynamicName = this.getDynamicAddressName(address);
        if (dynamicName !== 'UNKNOWN') {
            return dynamicName;
        }

        // Пытаемся определить по паттернам
        if (address.startsWith('AH5RFX') || address.startsWith('9NRt4F')) {
            return 'POSITION_KEYPAIR';
        }

        if (address.includes('Dbw8mACQ') || address.includes('3WMYy9V9') ||
            address.includes('HZzNfgAp') || address.includes('8A4Crui8') ||
            address.includes('2mGnsXcG') || address.includes('GBDuzqBg') ||
            address.includes('7xmtz8hD') || address.includes('Hd6qVSiP')) {
            return 'BIN_ARRAY';
        }

        if (address.includes('EYj9xKw6') || address.includes('CoaxzEh8') ||
            address.includes('DwZz4S1Z') || address.includes('4N22J4vW')) {
            return 'POOL_RESERVE';
        }

        if (address.includes('59YuGWPu') || address.includes('ETc6tqgL')) {
            return 'ORACLE';
        }

        return 'UNKNOWN';
    }
}

// 🚀 ЗАПУСК АНАЛИЗА
async function runAnalysis() {
    try {
        console.log('🔍 АНАЛИЗ ВСЕХ АДРЕСОВ ИЗ FLASH LOAN ТРАНЗАКЦИИ\n');
        
        const analyzer = new AddressAnalyzer();
        const results = analyzer.analyzeAddressCoverage();
        
        console.log('\n📊 ИТОГОВАЯ СТАТИСТИКА:');
        console.log(`   🎯 РЕАЛЬНЫХ адресов: ${results.totalReal}`);
        console.log(`   ✅ Покрыто ALT: ${results.coveredReal} (${(results.coveredReal/results.totalReal*100).toFixed(1)}%)`);
        console.log(`   ❌ НЕ покрыто: ${results.uncoveredReal} (${(results.uncoveredReal/results.totalReal*100).toFixed(1)}%)`);
        console.log(`   🏛️ Статических (код): ${results.totalStatic} (покрыто: ${results.coveredStatic})`);
        console.log(`   🔄 Динамических: ${results.totalDynamic} (не нужны в ALT)`);
        console.log(`   💾 ALT размер: ${results.altTableSize} адресов`);

        console.log('\n🎯 ПОТЕНЦИАЛ СЖАТИЯ:');
        const compressionPotential = results.uncoveredReal;
        console.log(`   📉 Можно сжать: ${compressionPotential} адресов`);
        console.log(`   💾 Экономия: ~${compressionPotential * 32} байт (${(compressionPotential * 32 / 1024).toFixed(1)} KB)`);
        
        // Сохраняем результат
        const analysisResult = {
            timestamp: new Date().toISOString(),
            summary: results,
            uncoveredRealAddresses: results.uncoveredRealAddresses,
            uncoveredStaticAddresses: results.uncoveredStaticAddresses,
            allRealAddresses: results.realAddresses,
            compressionPotential: {
                addressCount: results.uncoveredReal,
                byteSavings: results.uncoveredReal * 32,
                kbSavings: (results.uncoveredReal * 32 / 1024).toFixed(1)
            }
        };
        
        fs.writeFileSync('address-coverage-analysis.json', JSON.stringify(analysisResult, null, 2));
        console.log('\n💾 Результат сохранен в address-coverage-analysis.json');
        
        return results;
        
    } catch (error) {
        console.error('❌ Ошибка анализа:', error.message);
        return null;
    }
}

// Запускаем анализ
if (require.main === module) {
    runAnalysis();
}

module.exports = { AddressAnalyzer, runAnalysis };
