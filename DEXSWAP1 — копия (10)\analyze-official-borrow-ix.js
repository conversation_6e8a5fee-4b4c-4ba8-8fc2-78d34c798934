/**
 * 🔍 АНАЛИЗ ОФИЦИАЛЬНОЙ BORROW ИНСТРУКЦИИ ИЗ MARGINFI SDK
 * 
 * Цель: Понять что именно создает makeBorrowIx() и какие аккаунты использует
 */

const { Connection, PublicKey } = require('@solana/web3.js');
const { MarginfiClient, getConfig } = require('@mrgnlabs/marginfi-client-v2');
const { NodeWallet } = require('@mrgnlabs/mrgn-common');
const { Keypair } = require('@solana/web3.js');

async function analyzeOfficialBorrowIx() {
    try {
        console.log('🔍 АНАЛИЗ ОФИЦИАЛЬНОЙ MARGINFI BORROW ИНСТРУКЦИИ...\n');
        
        // Подключение к Solana
        const connection = new Connection('https://solana-mainnet.g.alchemy.com/v2/alch-demo', 'confirmed');
        
        // Создаем временный кошелек для анализа
        const tempKeypair = Keypair.generate();
        const wallet = new NodeWallet(tempKeypair);
        
        // Инициализируем MarginFi клиент
        console.log('🚀 Инициализируем MarginFi клиент...');
        const config = getConfig("production");
        const marginfiClient = await MarginfiClient.fetch(config, wallet, connection, {
            readOnly: true
        });
        console.log('✅ MarginFi клиент инициализирован');
        
        // Получаем USDC банк
        console.log('🏦 Получаем USDC банк...');
        const usdcMint = new PublicKey('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v');
        const bank = marginfiClient.getBankByMint(usdcMint);
        console.log(`✅ USDC банк: ${bank.address.toString()}`);
        
        // Загружаем существующий MarginFi аккаунт
        console.log('📋 Загружаем MarginFi аккаунт...');
        const marginfiAccountAddress = new PublicKey('********************************************');
        const { MarginfiAccountWrapper } = require('@mrgnlabs/marginfi-client-v2');
        const marginfiAccount = await MarginfiAccountWrapper.fetch(marginfiAccountAddress, marginfiClient);
        console.log('✅ MarginFi аккаунт загружен');
        
        // Создаем borrow инструкцию через официальный SDK
        console.log('💰 Создаем borrow инструкцию через официальный SDK...');
        const amount = 1000000; // 1 USDC в микроюнитах
        const borrowIxResult = await marginfiAccount.makeBorrowIx(amount, bank.address);
        
        console.log('\n🎯 АНАЛИЗ РЕЗУЛЬТАТА makeBorrowIx():');
        console.log(`   Тип результата: ${typeof borrowIxResult}`);
        console.log(`   Ключи объекта: ${Object.keys(borrowIxResult)}`);
        
        if (borrowIxResult.instructions) {
            console.log(`   Количество инструкций: ${borrowIxResult.instructions.length}`);
            
            borrowIxResult.instructions.forEach((ix, index) => {
                console.log(`\n📋 ИНСТРУКЦИЯ ${index + 1}:`);
                console.log(`   Program ID: ${ix.programId.toString()}`);
                console.log(`   Data length: ${ix.data.length} байт`);
                console.log(`   Data (hex): ${ix.data.toString('hex')}`);
                console.log(`   Data (array): [${Array.from(ix.data).join(', ')}]`);
                console.log(`   Количество аккаунтов: ${ix.keys.length}`);
                
                ix.keys.forEach((key, keyIndex) => {
                    console.log(`   Аккаунт ${keyIndex}: ${key.pubkey.toString()}`);
                    console.log(`      Signer: ${key.isSigner}, Writable: ${key.isWritable}`);
                });
            });
        }
        
        if (borrowIxResult.signers) {
            console.log(`\n🔑 SIGNERS: ${borrowIxResult.signers.length}`);
            borrowIxResult.signers.forEach((signer, index) => {
                console.log(`   Signer ${index}: ${signer.toString()}`);
            });
        }
        
        console.log('\n🎉 АНАЛИЗ ЗАВЕРШЕН!');
        
    } catch (error) {
        console.error(`❌ Ошибка анализа: ${error.message}`);
        console.error(`   Стек: ${error.stack}`);
    }
}

// Запускаем анализ
analyzeOfficialBorrowIx();
