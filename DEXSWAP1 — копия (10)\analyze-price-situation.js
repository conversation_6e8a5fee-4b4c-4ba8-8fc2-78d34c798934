#!/usr/bin/env node

/**
 * 🔍 АНАЛИЗ КОНКРЕТНОЙ ЦЕНОВОЙ СИТУАЦИИ
 * ═══════════════════════════════════════════════════════════════
 * 🎯 ЦЕЛЬ: Анализ ситуации с покупкой по $150 и продажей по $163
 * 🚨 ПРОВЕРКА: Реальность арбитражной возможности
 */

// const colors = require('colors'); // Убираем зависимость для простоты
const PriceAnomalyDetector = require('./price-anomaly-detector.js');

/**
 * 🔍 АНАЛИЗАТОР КОНКРЕТНОЙ СИТУАЦИИ
 */
class PriceSituationAnalyzer {
    constructor() {
        this.anomalyDetector = new PriceAnomalyDetector();
        
        console.log('🔍 Анализатор ценовой ситуации создан');
    }

    /**
     * 🚨 АНАЛИЗ СИТУАЦИИ: ПОКУПКА $150, ПРОДАЖА $163
     */
    async analyzeSituation() {
        console.log('\n🚨 АНАЛИЗ ЦЕНОВОЙ СИТУАЦИИ');
        console.log('═'.repeat(60));
        console.log('📊 Данные из скриншота:');
        console.log('   💰 SOL цена (верх): $163.12244');
        console.log('   💱 Exchange rate: 1 SOL ≈ 150.739253 USDC');
        console.log('   📈 Потенциальный спред: ~8.2%');
        console.log('═'.repeat(60));

        // Подготавливаем данные для анализа
        const priceData = {
            token: 'SOL/USDC',
            buyPrice: 150.739253,    // Цена покупки через USDC
            sellPrice: 163.12244,    // Рыночная цена SOL
            spread: ((163.12244 - 150.739253) / 150.739253) * 100, // ~8.2%
            timestamp: Date.now(),
            source: 'DEX_INTERFACE',
            
            // Дополнительные данные
            exchangeRate: 150.739253,
            priceImpact: 6.94, // Из скриншота
            minReceived: 150.588513,
            
            // Контекст
            tradeAmount: 1, // 1 SOL
            tradeAmountUsd: 163.12244
        };

        console.log(`\n🧮 РАСЧЕТНЫЕ ДАННЫЕ:`);
        console.log(`   Спред: ${priceData.spread.toFixed(4)}%`);
        console.log(`   Потенциальная прибыль: $${(priceData.sellPrice - priceData.buyPrice).toFixed(2)}`);
        console.log(`   Price Impact: ${priceData.priceImpact}%`);

        // 1. 🚨 ПРОВЕРКА АНОМАЛИЙ
        console.log('\n🔍 ЗАПУСК ДЕТЕКТОРА АНОМАЛИЙ...');
        const anomalyResult = await this.anomalyDetector.detectPriceAnomaly(priceData);

        // 2. 🧠 ДЕТАЛЬНЫЙ АНАЛИЗ
        const detailedAnalysis = this.performDetailedAnalysis(priceData);

        // 3. 💡 ВОЗМОЖНЫЕ ПРИЧИНЫ
        const possibleCauses = this.identifyPossibleCauses(priceData, anomalyResult);

        // 4. ⚠️ РИСКИ И ПРЕДУПРЕЖДЕНИЯ
        const risks = this.identifyRisks(priceData);

        // 5. 🎯 РЕКОМЕНДАЦИИ
        const recommendations = this.generateRecommendations(priceData, anomalyResult, risks);

        // 📊 ИТОГОВЫЙ ОТЧЕТ
        this.generateFinalReport({
            priceData,
            anomalyResult,
            detailedAnalysis,
            possibleCauses,
            risks,
            recommendations
        });

        return {
            isRealOpportunity: recommendations.isRealOpportunity,
            shouldTrade: recommendations.shouldTrade,
            riskLevel: anomalyResult.riskLevel,
            mainConcerns: risks.mainConcerns
        };
    }

    /**
     * 🧠 ДЕТАЛЬНЫЙ АНАЛИЗ
     */
    performDetailedAnalysis(priceData) {
        console.log('\n🧠 ДЕТАЛЬНЫЙ АНАЛИЗ...');

        const analysis = {
            spreadAnalysis: this.analyzeSpread(priceData),
            priceImpactAnalysis: this.analyzePriceImpact(priceData),
            liquidityAnalysis: this.analyzeLiquidity(priceData),
            marketConditions: this.analyzeMarketConditions(priceData)
        };

        return analysis;
    }

    /**
     * 📊 АНАЛИЗ СПРЕДА
     */
    analyzeSpread(priceData) {
        const { spread } = priceData;

        console.log(`📊 Анализ спреда: ${spread.toFixed(4)}%`);

        if (spread > 5.0) {
            return {
                status: 'SUSPICIOUS',
                message: 'Спред слишком большой для нормального рынка',
                likelihood: 'LIKELY_ANOMALY'
            };
        } else if (spread > 2.0) {
            return {
                status: 'HIGH',
                message: 'Высокий спред, требует осторожности',
                likelihood: 'POSSIBLE_OPPORTUNITY'
            };
        } else {
            return {
                status: 'NORMAL',
                message: 'Спред в нормальных пределах',
                likelihood: 'REAL_OPPORTUNITY'
            };
        }
    }

    /**
     * 📈 АНАЛИЗ PRICE IMPACT
     */
    analyzePriceImpact(priceData) {
        const { priceImpact } = priceData;

        console.log(`📈 Анализ price impact: ${priceImpact}%`);

        if (priceImpact > 5.0) {
            return {
                status: 'CRITICAL',
                message: 'Очень высокий price impact - пул неликвиден',
                impact: 'MAJOR_LOSS_RISK'
            };
        } else if (priceImpact > 2.0) {
            return {
                status: 'HIGH',
                message: 'Высокий price impact - ограниченная ликвидность',
                impact: 'REDUCED_PROFIT'
            };
        } else {
            return {
                status: 'ACCEPTABLE',
                message: 'Price impact в приемлемых пределах',
                impact: 'MINIMAL'
            };
        }
    }

    /**
     * 💧 АНАЛИЗ ЛИКВИДНОСТИ
     */
    analyzeLiquidity(priceData) {
        console.log('💧 Анализ ликвидности...');

        // На основе price impact можем оценить ликвидность
        const { priceImpact } = priceData;

        if (priceImpact > 5.0) {
            return {
                status: 'LOW',
                message: 'Низкая ликвидность пула',
                recommendation: 'AVOID_LARGE_TRADES'
            };
        } else {
            return {
                status: 'ADEQUATE',
                message: 'Ликвидность достаточная для небольших сделок',
                recommendation: 'PROCEED_WITH_CAUTION'
            };
        }
    }

    /**
     * 🌍 АНАЛИЗ РЫНОЧНЫХ УСЛОВИЙ
     */
    analyzeMarketConditions(priceData) {
        console.log('🌍 Анализ рыночных условий...');

        return {
            volatility: 'HIGH', // На основе большого спреда
            trend: 'UNCERTAIN',
            recommendation: 'MONITOR_CLOSELY'
        };
    }

    /**
     * 🔍 ВОЗМОЖНЫЕ ПРИЧИНЫ
     */
    identifyPossibleCauses(priceData, anomalyResult) {
        console.log('\n🔍 ВОЗМОЖНЫЕ ПРИЧИНЫ СИТУАЦИИ:');

        const causes = [];

        // 1. Высокий спред
        if (priceData.spread > 5.0) {
            causes.push({
                cause: 'ILLIQUID_POOL',
                probability: 'HIGH',
                description: 'Пул с низкой ликвидностью',
                impact: 'Высокий price impact съест прибыль'
            });
        }

        // 2. Высокий price impact
        if (priceData.priceImpact > 5.0) {
            causes.push({
                cause: 'THIN_ORDER_BOOK',
                probability: 'HIGH',
                description: 'Тонкая книга заказов',
                impact: 'Реальная цена будет хуже ожидаемой'
            });
        }

        // 3. Устаревшие данные
        causes.push({
            cause: 'STALE_PRICE_DATA',
            probability: 'MEDIUM',
            description: 'Устаревшие данные о цене',
            impact: 'Арбитраж уже недоступен'
        });

        // 4. Проблемы с оракулом
        causes.push({
            cause: 'ORACLE_ISSUES',
            probability: 'MEDIUM',
            description: 'Проблемы с оракулом цен',
            impact: 'Неточные данные о рыночной цене'
        });

        causes.forEach((cause, i) => {
            console.log(`   ${i + 1}. [${cause.probability}] ${cause.description}`);
            console.log(`      Влияние: ${cause.impact}`);
        });

        return causes;
    }

    /**
     * ⚠️ ИДЕНТИФИКАЦИЯ РИСКОВ
     */
    identifyRisks(priceData) {
        console.log('\n⚠️ ИДЕНТИФИКАЦИЯ РИСКОВ:');

        const risks = {
            mainConcerns: [],
            financialRisks: [],
            technicalRisks: []
        };

        // Основные проблемы
        if (priceData.spread > 5.0) {
            risks.mainConcerns.push('Нереально высокий спред');
        }

        if (priceData.priceImpact > 5.0) {
            risks.mainConcerns.push('Критический price impact');
        }

        // Финансовые риски
        risks.financialRisks.push({
            risk: 'SLIPPAGE_LOSS',
            probability: 'HIGH',
            impact: `Потеря до ${priceData.priceImpact}% от суммы`
        });

        risks.financialRisks.push({
            risk: 'FAILED_ARBITRAGE',
            probability: 'MEDIUM',
            impact: 'Полная потеря комиссий транзакции'
        });

        // Технические риски
        risks.technicalRisks.push({
            risk: 'TRANSACTION_FAILURE',
            probability: 'MEDIUM',
            impact: 'Потеря gas fees'
        });

        console.log(`   Основные проблемы: ${risks.mainConcerns.join(', ')}`);
        console.log(`   Финансовых рисков: ${risks.financialRisks.length}`);
        console.log(`   Технических рисков: ${risks.technicalRisks.length}`);

        return risks;
    }

    /**
     * 💡 ГЕНЕРАЦИЯ РЕКОМЕНДАЦИЙ
     */
    generateRecommendations(priceData, anomalyResult, risks) {
        console.log('\n💡 РЕКОМЕНДАЦИИ:');

        const recommendations = {
            isRealOpportunity: false,
            shouldTrade: false,
            actions: [],
            reasoning: []
        };

        // Анализ на основе данных
        if (priceData.spread > 5.0 && priceData.priceImpact > 5.0) {
            recommendations.isRealOpportunity = false;
            recommendations.shouldTrade = false;
            recommendations.actions.push('НЕ ТОРГОВАТЬ');
            recommendations.reasoning.push('Слишком высокий price impact съест всю прибыль');
        }

        if (anomalyResult.riskLevel === 'CRITICAL' || anomalyResult.riskLevel === 'HIGH') {
            recommendations.shouldTrade = false;
            recommendations.actions.push('ПРОПУСТИТЬ СДЕЛКУ');
            recommendations.reasoning.push('Детектор аномалий выявил высокий риск');
        }

        // Конкретные действия
        recommendations.actions.push('Проверить ликвидность пула вручную');
        recommendations.actions.push('Сравнить с ценами других DEX');
        recommendations.actions.push('Проверить актуальность данных');

        if (priceData.priceImpact > 2.0) {
            recommendations.actions.push('Если торговать - уменьшить размер позиции в 5-10 раз');
        }

        console.log(`   Реальная возможность: ${recommendations.isRealOpportunity ? 'ДА' : 'НЕТ'}`);
        console.log(`   Рекомендация торговать: ${recommendations.shouldTrade ? 'ДА' : 'НЕТ'}`);

        return recommendations;
    }

    /**
     * 📋 ИТОГОВЫЙ ОТЧЕТ
     */
    generateFinalReport(data) {
        console.log('\n📋 ИТОГОВЫЙ ОТЧЕТ');
        console.log('═'.repeat(60));

        const { priceData, recommendations, risks } = data;

        console.log('🎯 ЗАКЛЮЧЕНИЕ:');
        
        if (priceData.spread > 5.0 && priceData.priceImpact > 5.0) {
            console.log('❌ Это НЕ реальная арбитражная возможность');
            console.log('📊 Причины:');
            console.log('   • Спред 8.2% слишком высок для нормального рынка');
            console.log('   • Price impact 6.94% съест большую часть прибыли');
            console.log('   • Пул имеет очень низкую ликвидность');
            console.log('   • Реальная прибыль будет близка к нулю или отрицательна');
        }

        console.log('\n🚨 ГЛАВНЫЕ РИСКИ:');
        risks.mainConcerns.forEach(concern => {
            console.log(`   • ${concern}`);
        });

        console.log('\n💡 РЕКОМЕНДАЦИИ:');
        recommendations.actions.forEach(action => {
            console.log(`   • ${action}`);
        });

        console.log('\n🎓 УРОК:');
        console.log('   Большие спреды часто указывают на проблемы с ликвидностью,');
        console.log('   а не на реальные арбитражные возможности. Всегда проверяйте');
        console.log('   price impact перед выполнением сделки!');

        console.log('═'.repeat(60));
    }
}

// 🚀 ЗАПУСК АНАЛИЗА
if (require.main === module) {
    console.log('🔍 ЗАПУСК АНАЛИЗА ЦЕНОВОЙ СИТУАЦИИ');
    
    const analyzer = new PriceSituationAnalyzer();
    
    analyzer.analyzeSituation()
        .then((result) => {
            console.log('\n🎉 АНАЛИЗ ЗАВЕРШЕН!');
            console.log(`Результат: ${result.isRealOpportunity ? 'РЕАЛЬНАЯ ВОЗМОЖНОСТЬ' : 'ЛОЖНЫЙ СИГНАЛ'}`);
        })
        .catch((error) => {
            console.error('\n💥 ОШИБКА АНАЛИЗА!');
            console.error(error);
        });
}

module.exports = PriceSituationAnalyzer;
