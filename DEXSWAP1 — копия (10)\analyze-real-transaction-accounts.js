#!/usr/bin/env node

/**
 * 🔍 АНАЛИЗ АККАУНТОВ В РЕАЛЬНОЙ MARGINFI FLASH LOAN ТРАНЗАКЦИИ
 * Извлекаем все аккаунты из реальной транзакции и создаем специализированную ALT таблицу
 */

const { Connection, PublicKey } = require('@solana/web3.js');
const fs = require('fs');

async function analyzeRealTransactionAccounts() {
    console.log('🔍 АНАЛИЗ АККАУНТОВ В РЕАЛЬНОЙ MARGINFI FLASH LOAN ТРАНЗАКЦИИ');
    console.log('=' .repeat(80));

    try {
        // 1. Подключение к Solana
        console.log('🔗 Подключение к Solana RPC...');
        const connection = new Connection('https://api.mainnet-beta.solana.com');
        
        // 2. Загружаем существующие ALT таблицы
        console.log('\n📁 Загрузка существующих ALT таблиц...');
        
        const meteoraFile = './meteora-alt-cache.json';
        const marginfiFile = './marginfi-alt-cache.json';
        
        const meteoraCache = JSON.parse(fs.readFileSync(meteoraFile, 'utf8'));
        const marginfiCache = JSON.parse(fs.readFileSync(marginfiFile, 'utf8'));
        
        // Собираем все существующие аккаунты
        const existingAccounts = new Set();
        
        // Meteora аккаунты
        meteoraCache.validationResults.forEach(validation => {
            if (validation.valid && validation.accounts) {
                validation.accounts.forEach(account => {
                    existingAccounts.add(account);
                });
            }
        });
        
        // MarginFi аккаунты
        marginfiCache.validationResults.forEach(validation => {
            if (validation.valid && validation.accounts) {
                validation.accounts.forEach(account => {
                    existingAccounts.add(account);
                });
            }
        });
        
        console.log(`✅ Существующих аккаунтов в ALT: ${existingAccounts.size}`);

        // 3. Анализируем известные аккаунты из логов
        console.log('\n🔍 Анализ аккаунтов из реальной транзакции...');
        
        // Аккаунты из логов MarginFi Flash Loan транзакции
        const realTransactionAccounts = [
            // MarginFi аккаунты
            'MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA', // MarginFi Program
            '4qp6Fx6tnZkY5Wropq9wUYgtFxXKwE6viZxFHg3rdAG8', // MarginFi Group
            '3dPECtRBPcYtcJG6wWDCr7VCyzxttDTjgGgo9gWC9vmU', // MarginFi Account
            'CCKtUs6Cgwo4aaQUmBPmyoApH2gUDErxNZCAntD6LYGh', // SOL Bank
            '2eicbpitfJXDwqCuFAmPgDP7t2oUotnAzbGzRKLMgSLe', // Bank Vault
            'DD3AeAssFvjqTvRTrRAtpfjkBF8FpVKnFuwnMLN9haXD', // Vault Authority
            
            // Token аккаунты
            'So********************************111111112', // SOL Mint
            'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // USDC Mint
            'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA', // Token Program
            '68rtTtSuEPR84Wo1xWGs6ytBttn7JN33Ux8WsDp38FHk', // User SOL Account
            '3AWxcMzxsTeBQ6YC1khJ9NkPtmLw2XbCGxvZHZpbAQFo', // User USDC Account
            
            // Meteora DLMM аккаунты
            'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo', // Meteora Program
            '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6', // Pool 1
            'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y', // Pool 2
            'HTvjzsfX3yU6BUodCjZ5vZkUrAxMDTrBs3CJaq43ashR', // Pool 3
            
            // Pool reserves
            'EYj9xKw6ZszwpyNibHY7JD5o3QgTVrSdcBp1fMJhrR9o', // Reserve X Pool 1
            'CoaxzEh8p5YyGLcj36Eo3cUThVJxeKCs7qvLAGDYwBcz', // Reserve Y Pool 1
            'DwZz4S1Z1LBXomzmncQRVKCYhjCqSAMQ6RPKbUAadr7H', // Reserve X Pool 2
            '4N22J4vW2juHocTntJNmXywSonYjkndCwahjZ2cYLDgb', // Reserve Y Pool 2
            'H7j5NPopj3tQvDg4N8CxwtYciTn3e8AEV6wSVrxpyDUc', // Reserve X Pool 3
            'HbYjRzx7teCxqW3unpXBEcNHhfVZvW2vW9MQ99TkizWt', // Reserve Y Pool 3
            
            // Oracles
            '59YuGWPunbchD2mbi9U7qvjWQKQReGeepn4ZSr9zz9Li', // Oracle Pool 1
            'ETc6tqgLrr7wXsH8u2QBK1CyXHX3kvV6WQjBz4cf3sCj', // Oracle Pool 2
            'EgEYXef2FCoEYLHJJW74dMbom1atLXo6KwPuA6mSATYA', // Oracle Pool 3
            
            // Bin Arrays
            '6dvANXbq79ETRLSEpN8jBGjPfAWsbarCnEKnA7GezSzF', // Bin Array Lower Pool 1
            '7xmtz8hDZtkUm3nphp4PMNsiZv5fJxVxh8VjN6kt7ws4', // Bin Array Upper Pool 1
            'B1ktEow6tgGowzLD514CpyhoHWEM5ijKiJycutkskuAh', // Bin Array Lower Pool 2
            'Dbw8mACQmyrvbvLZs9bzAA6Tg2GVf5cmLzVbkfRzahRS', // Bin Array Upper Pool 2
            '7qBNCuJ6TihLmnAa7Ebh7v2rndUGYxoAaqRqehpUUS3V', // Bin Array Lower Pool 3
            'Bfz347AfD3e5FTrfgZ5gpHRDtwLhQ5X77ZSBAqRdjFbv', // Bin Array Upper Pool 3
            
            // Event Authority
            'D1ZN9Wj1fRSUQfCjhvnu1hqDMT7hzjzBBpi12nVniYD6', // Event Authority
            
            // System аккаунты
            '********************************', // System Program
            'ComputeBudget111111111111111111111111111111', // Compute Budget
            
            // User wallet
            'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV' // User Wallet
        ];

        console.log(`📊 Аккаунтов из реальной транзакции: ${realTransactionAccounts.length}`);

        // 4. Проверяем покрытие
        console.log('\n🔍 Проверка покрытия аккаунтов...');
        
        const coveredAccounts = [];
        const uncoveredAccounts = [];
        
        realTransactionAccounts.forEach(account => {
            if (existingAccounts.has(account)) {
                coveredAccounts.push(account);
            } else {
                uncoveredAccounts.push(account);
            }
        });
        
        console.log(`✅ Покрытых аккаунтов: ${coveredAccounts.length}/${realTransactionAccounts.length}`);
        console.log(`❌ Непокрытых аккаунтов: ${uncoveredAccounts.length}/${realTransactionAccounts.length}`);
        
        const coveragePercent = (coveredAccounts.length / realTransactionAccounts.length * 100).toFixed(1);
        console.log(`📊 Покрытие: ${coveragePercent}%`);

        // 5. Показываем непокрытые аккаунты
        if (uncoveredAccounts.length > 0) {
            console.log('\n❌ НЕПОКРЫТЫЕ АККАУНТЫ:');
            uncoveredAccounts.forEach((account, index) => {
                console.log(`   ${index + 1}. ${account}`);
            });
        }

        // 6. Создаем специализированную ALT таблицу
        console.log('\n🔧 Создание специализированной ALT таблицы...');
        
        const specializedALT = {
            timestamp: new Date().toISOString(),
            source: 'MarginFi Flash Loan Arbitrage Specialized ALT',
            description: 'Специализированная ALT таблица для MarginFi Flash Loan арбитража',
            totalAccounts: realTransactionAccounts.length,
            coverage: {
                existing: coveredAccounts.length,
                new: uncoveredAccounts.length,
                percent: parseFloat(coveragePercent)
            },
            accounts: realTransactionAccounts,
            categorized: {
                marginfi: realTransactionAccounts.filter(acc => 
                    acc.includes('MFv2') || acc.includes('4qp6') || acc.includes('3dPE') || 
                    acc.includes('CCKt') || acc.includes('2eic') || acc.includes('DD3A')
                ),
                meteora: realTransactionAccounts.filter(acc => 
                    acc.includes('LBUZ') || acc.includes('5rCf') || acc.includes('BGm1') || 
                    acc.includes('HTvj') || acc.includes('EYj9') || acc.includes('Coax') ||
                    acc.includes('DwZz') || acc.includes('4N22') || acc.includes('H7j5') ||
                    acc.includes('HbYj') || acc.includes('6dvA') || acc.includes('7xmt') ||
                    acc.includes('B1kt') || acc.includes('Dbw8') || acc.includes('7qBN') ||
                    acc.includes('Bfz3')
                ),
                tokens: realTransactionAccounts.filter(acc => 
                    acc.includes('So11') || acc.includes('EPjF') || acc.includes('Token') ||
                    acc.includes('68rt') || acc.includes('3AWx')
                ),
                system: realTransactionAccounts.filter(acc => 
                    acc.includes('1111') || acc.includes('Comp') || acc.includes('D1ZN') ||
                    acc.includes('bbTG')
                ),
                oracles: realTransactionAccounts.filter(acc => 
                    acc.includes('59Yu') || acc.includes('ETc6') || acc.includes('EgEY')
                )
            }
        };

        // 7. Сохраняем специализированную ALT таблицу
        const specializedFile = './specialized-arbitrage-alt.json';
        fs.writeFileSync(specializedFile, JSON.stringify(specializedALT, null, 2));
        
        console.log(`✅ Специализированная ALT таблица сохранена: ${specializedFile}`);
        console.log(`📊 Всего аккаунтов: ${specializedALT.totalAccounts}`);
        console.log(`📊 MarginFi аккаунтов: ${specializedALT.categorized.marginfi.length}`);
        console.log(`📊 Meteora аккаунтов: ${specializedALT.categorized.meteora.length}`);
        console.log(`📊 Token аккаунтов: ${specializedALT.categorized.tokens.length}`);
        console.log(`📊 System аккаунтов: ${specializedALT.categorized.system.length}`);
        console.log(`📊 Oracle аккаунтов: ${specializedALT.categorized.oracles.length}`);

        // 8. Рекомендации
        console.log('\n💡 РЕКОМЕНДАЦИИ:');
        
        if (uncoveredAccounts.length > 0) {
            console.log(`   1. Добавить ${uncoveredAccounts.length} непокрытых аккаунтов в ALT таблицы`);
            console.log(`   2. Создать новую ALT таблицу с этими аккаунтами`);
            console.log(`   3. Использовать специализированную ALT таблицу для арбитража`);
        }
        
        if (coveragePercent < 90) {
            console.log(`   4. Покрытие ${coveragePercent}% недостаточно для эффективного сжатия`);
            console.log(`   5. Нужно достичь покрытия >90% для значительного сжатия`);
        }
        
        console.log(`   6. Использовать специализированную ALT таблицу вместе с существующими`);
        console.log(`   7. Тестировать сжатие с новой ALT таблицей`);

        // 9. Итоговый результат
        console.log('\n🎯 ИТОГОВЫЙ РЕЗУЛЬТАТ:');
        
        if (coveragePercent >= 90) {
            console.log('✅ ОТЛИЧНОЕ ПОКРЫТИЕ! ALT сжатие должно работать эффективно');
        } else if (coveragePercent >= 70) {
            console.log('⚠️ ХОРОШЕЕ ПОКРЫТИЕ, но нужны дополнительные аккаунты');
        } else {
            console.log('❌ ПЛОХОЕ ПОКРЫТИЕ! Нужна специализированная ALT таблица');
        }
        
        console.log(`📊 Покрытие: ${coveragePercent}%`);
        console.log(`📊 Непокрытых аккаунтов: ${uncoveredAccounts.length}`);
        console.log(`💾 Специализированная ALT: ${specializedFile}`);

    } catch (error) {
        console.error('❌ Ошибка анализа:', error.message);
        console.error(error.stack);
    }
}

// Запуск анализа
if (require.main === module) {
    analyzeRealTransactionAccounts().catch(console.error);
}

module.exports = { analyzeRealTransactionAccounts };
