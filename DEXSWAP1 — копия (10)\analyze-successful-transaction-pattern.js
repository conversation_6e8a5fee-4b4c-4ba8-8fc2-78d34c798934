/**
 * 🔥 АНАЛИЗ УСПЕШНОЙ ТРАНЗАКЦИИ
 * Детальный разбор паттерна из успешного flash loan арбитража
 */

function analyzeSuccessfulTransaction() {
  console.log('🔥 АНАЛИЗ УСПЕШНОЙ ТРАНЗАКЦИИ');
  console.log('=' .repeat(80));

  console.log('\n📋 УСПЕШНАЯ ПОСЛЕДОВАТЕЛЬНОСТЬ ИНСТРУКЦИЙ:');
  console.log('─'.repeat(60));

  const successfulPattern = [
    {
      index: 0,
      program: 'ComputeBudget',
      action: 'setComputeUnitLimit',
      description: 'Устанавливает лимит compute units'
    },
    {
      index: 1,
      program: 'ComputeBudget',
      action: 'setComputeUnitPrice',
      description: 'Устанавливает цену priority fee'
    },
    {
      index: 2,
      program: 'MarginFi',
      action: 'lending_account_start_flashloan',
      description: '💰 НАЧАЛО FLASH LOAN - занимаем 10,000 USDC'
    },
    {
      index: 3,
      program: 'Jupiter',
      action: 'route (USDC → WSOL)',
      description: '🔄 ПЕРВЫЙ SWAP: 10,000 USDC → 68.1414 WSOL',
      details: 'Получили WSOL токены в существующий аккаунт'
    },
    {
      index: 4,
      program: 'Token Program',
      action: 'closeAccount',
      description: '🔒 ЗАКРЫТИЕ WSOL АККАУНТА → 68.14 SOL возвращается в кошелек!',
      critical: true,
      note: 'ЭТО КЛЮЧЕВОЙ МОМЕНТ! SOL освобождается для следующих операций'
    },
    {
      index: 5,
      program: 'Associated Token',
      action: 'createIdempotent',
      description: '🆕 СОЗДАНИЕ НОВОГО WSOL АККАУНТА'
    },
    {
      index: 6,
      program: 'System Program',
      action: 'transfer',
      description: '💸 ПЕРЕВОД 68.1503 SOL в новый WSOL аккаунт',
      note: 'У кошелька есть SOL благодаря closeAccount!'
    },
    {
      index: 7,
      program: 'Token Program',
      action: 'syncNative',
      description: '🔄 СИНХРОНИЗАЦИЯ WSOL баланса'
    },
    {
      index: 8,
      program: 'Jupiter',
      action: 'route (WSOL → USDC)',
      description: '🔄 ВТОРОЙ SWAP: WSOL → 9,998 USDC'
    },
    {
      index: 9,
      program: 'MarginFi',
      action: 'lending_account_end_flashloan',
      description: '💰 КОНЕЦ FLASH LOAN - возвращаем займ'
    }
  ];

  successfulPattern.forEach((step, i) => {
    const prefix = step.critical ? '🔥' : '  ';
    console.log(`${prefix} ${step.index}. ${step.program}: ${step.action}`);
    console.log(`     ${step.description}`);
    if (step.note) {
      console.log(`     ⚠️  ${step.note}`);
    }
    if (step.details) {
      console.log(`     📝 ${step.details}`);
    }
    console.log('');
  });

  console.log('\n🔥 КРИТИЧЕСКИЕ ОТКРЫТИЯ:');
  console.log('─'.repeat(60));
  console.log('✅ Инструкция #4 (closeAccount) ОСВОБОЖДАЕТ SOL из WSOL аккаунта');
  console.log('✅ Это дает кошельку достаточно SOL для инструкции #6 (transfer)');
  console.log('✅ БЕЗ closeAccount у кошелька не хватило бы SOL!');
  console.log('');
  console.log('❌ В НАШЕЙ РЕАЛИЗАЦИИ:');
  console.log('❌ Мы НЕ закрываем WSOL аккаунт после первого свапа');
  console.log('❌ SOL остается "заблокированным" в WSOL аккаунте');
  console.log('❌ Jupiter требует дополнительные 329 SOL для второго свапа');
  console.log('❌ У кошелька только 45 SOL → "insufficient lamports"');

  console.log('\n🔧 РЕШЕНИЕ:');
  console.log('─'.repeat(60));
  console.log('🔥 Добавить closeAccount инструкцию между двумя Jupiter свапами!');
  console.log('🔥 Это освободит SOL для второго свапа, как в успешной транзакции!');
}

// Запускаем анализ
analyzeSuccessfulTransaction();