# 🔥 ЧЕКЛИСТ ANCHOR-ТРАНЗАКЦИЙ
## Избежание ошибок типа AccountSysvarMismatch (3015), MissingRequiredSignature, ProgramError

### 📦 ОБЩАЯ СТРУКТУРА ТРАНЗАКЦИИ
- [ ] Инструкция содержит валидный `programId`
- [ ] Все ключи в `keys[]` расположены в **точном порядке**, ожидаемом по IDL
- [ ] ALT-оптимизация применена корректно (не заменяет sysvar, rent, signers)
- [ ] Размер транзакции **< 1232 байт**
- [ ] Все `data` сериализованы в правильном формате (Borsh для Anchor)

### 🔐 SIGNERS И ПОДПИСИ
- [ ] Каждый `isSigner: true` ключ **подписан**
- [ ] Указаны **только нужные** signers, без лишних
- [ ] Подписи соответствуют ключам, особенно для flash loan borrower
- [ ] Position keypairs добавлены в signers (для Meteora DLMM)

### 🧠 SYSVAR ЧЕКЛИСТ (Anchor ≥ 0.29)

| Sysvar | Статус в Anchor ≥ 0.29 | Передавать? | Примечание |
|--------|-------------------------|-------------|------------|
| `SysvarRent111...` | ❌ **НЕ нужен** | ❌ **НЕЛЬЗЯ** | **Вызывает ошибку 3015!** |
| `SysvarClock111...` | ✅ Может быть нужен | ✅ Только если IDL требует | Для временных ограничений |
| `SysvarInstructions111...` | ✅ Иногда требуется | ✅ При CPI или программной проверке | |
| `SysvarRecentBlockhashes` | 🔒 Устарел | ❌ | Не используется |
| `SysvarEpochSchedule111` | ❗ Редко нужен | ✅ Только по IDL | |

### 🔍 METEORA DLMM СПЕЦИФИКА
```javascript
// ✅ ПРАВИЛЬНАЯ СТРУКТУРА (БЕЗ RENT!)
const keys = [
  { pubkey: user, isSigner: true, isWritable: true },           // #0
  { pubkey: position, isSigner: true, isWritable: true },       // #1
  { pubkey: lbPair, isSigner: false, isWritable: true },        // #2
  { pubkey: owner, isSigner: true, isWritable: false },         // #3
  { pubkey: systemProgram, isSigner: false, isWritable: false }, // #4
  { pubkey: eventAuthority, isSigner: false, isWritable: false }, // #5
  { pubkey: program, isSigner: false, isWritable: false }       // #6
  // 🚫 rent НЕ добавляем! (вызовет ошибку 3015)
];
```

### 🧬 SYSVAR ПЕРЕДАЧА ПРАВИЛА
- [ ] **rent отсутствует полностью** (ошибка 3015 иначе)
- [ ] **clock есть**, если инструкция зависит от временных ограничений
- [ ] **instructions sysvar** передается, если используется CPI или проверка ix внутри
- [ ] Позиция sysvar **строго соответствует IDL**

### 🔁 CPI ИНСТРУКЦИИ
- [ ] Все промежуточные программы указаны в `programs` секции
- [ ] Все accounts указаны через `remainingAccounts`, если Anchor
- [ ] В `data` — сериализация Borsh, структура **строго по IDL**
- [ ] Flash loan CPI не передает запрещенные sysvar

### 🔐 FLASH LOAN ТРАНЗАКЦИЯ
- [ ] Займ → swap → repay — всё в **одной транзакции**
- [ ] **Не передается rent**, если DLMM участвует
- [ ] Account sizes и balances корректны
- [ ] MarginFi аккаунты в правильном порядке
- [ ] Vault аккаунты соответствуют токенам

### 📦 ALT ОПТИМИЗАЦИЯ
#### ❌ НЕ ЗАМЕНЯЙ:
- [ ] **Signer аккаунты** (isSigner: true)
- [ ] **Sysvar аккаунты** (SysvarRent111..., SysvarClock111...)
- [ ] **ProgramId** аккаунты
- [ ] **Dynamic keypairs** (position keypairs)

#### ✅ МОЖНО ЗАМЕНЯТЬ:
- [ ] Статические vault аккаунты
- [ ] Token program аккаунты
- [ ] Pool аккаунты
- [ ] Oracle аккаунты

### 🚨 ОБРАБОТКА ТИПИЧНЫХ ОШИБОК

| Ошибка | Причина | Решение |
|--------|---------|---------|
| **3015 AccountSysvarMismatch** | Передан Sysvar не того типа или в неправильной позиции | **Удали rent**, проверь clock, позицию |
| **MissingRequiredSignature** | Аккаунт isSigner, но не подписан | Добавь подпись в signers |
| **InvalidAccountData** | Неверный формат аккаунта (длина, данные) | Проверь owner + layout |
| **ProgramError: 0xbbd** | DLMM не принимает параметры / pool в неправильном состоянии | Проверить токены, баланс, tick_spacing |
| **encoding overruns Uint8Array** | Транзакция слишком большая | Применить ALT, убрать лишние инструкции |

### 🎯 ФИНАЛЬНАЯ ПРОВЕРКА ПЕРЕД ОТПРАВКОЙ
- [ ] Размер транзакции **< 1232 байт**
- [ ] Все signers **валидны и не undefined**
- [ ] **Rent sysvar отсутствует** в Meteora DLMM инструкциях
- [ ] ALT таблицы **загружены и применены**
- [ ] Instruction data **соответствует ожидаемому формату**
- [ ] Flash loan **endIndex корректный**

### 💡 РЕКОМЕНДАЦИИ ДЛЯ РАЗРАБОТКИ
1. **Всегда проверяй версию Anchor** программы перед использованием sysvar
2. **Тестируй на devnet** перед mainnet
3. **Используй симуляцию** для проверки структуры
4. **Веди лог изменений** в программах, которые используешь
5. **Создавай fallback** для разных версий программ

---
**Создано:** 2025-01-21  
**Версия:** 1.0  
**Применимо к:** Anchor ≥ 0.29, Meteora DLMM, MarginFi Flash Loans
