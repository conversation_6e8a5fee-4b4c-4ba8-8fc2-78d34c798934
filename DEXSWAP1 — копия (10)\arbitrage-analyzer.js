/**
 * 📊 АНАЛИЗАТОР АРБИТРАЖА - ВЫБОР ОПТИМАЛЬНЫХ ПУЛОВ
 * 
 * Анализирует влияние размера сделки на цену в разных пулах
 * Помогает выбрать оптимальный пул для минимизации slippage
 */

class ArbitrageAnalyzer {
    constructor() {
        // 🌊 РЕАЛЬНЫЕ ПУЛЫ ДЛЯ АРБИТРАЖА (ГИГАНТЫ!)
        this.POOLS = {
            ORCA_GIANT: {
                address: 'Czfq3xZZDmsdGdUyrNLtRhGc47cXcZtLG4crryfu44zE',
                name: 'OR<PERSON> SOL-USDC ГИГАНТ',
                tvl: 44000000, // $44M!
                liquidity: { SOL: 150000, USDC: 44000000 }, // Примерная оценка
                protocol: 'ORCA',
                baseFee: 0.0025, // 0.25%
                status: 'ULTRA_MEGA_LIQUIDITY'
            },
            RAYDIUM_GIANT: {
                address: '3ucNos4NbumPLZNWztqGHNFFgkHeRMBQAVemeeomsUxv',
                name: 'RAYDIUM SOL-USDC ГИГАНТ',
                tvl: 11000000, // $11M
                liquidity: { SOL: 40000, USDC: 11000000 }, // Примерная оценка
                protocol: 'RAYDIUM',
                baseFee: 0.0025, // 0.25%
                status: 'MEGA_LIQUIDITY'
            },
            METEORA_MEGA: {
                address: '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6',
                name: 'METEORA SOL-USDC MEGA',
                tvl: 7132613, // $7.1M
                liquidity: { SOL: 24088.03, USDC: 3111532.00 },
                protocol: 'METEORA',
                binStep: 4,
                baseFee: 0.0004, // 0.04%
                status: 'MEGA_LIQUIDITY'
            },
            METEORA_PRIMARY: {
                address: 'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y',
                name: 'METEORA SOL-USDC Primary',
                tvl: 2926841,
                liquidity: { SOL: 8162.93, USDC: 1561741.00 },
                protocol: 'METEORA',
                binStep: 10,
                baseFee: 0.001, // 0.1%
                status: 'HIGH_LIQUIDITY'
            }
        };
        
        console.log('📊 ArbitrageAnalyzer инициализирован');
    }

    /**
     * 🎯 АНАЛИЗ ВЛИЯНИЯ СДЕЛКИ НА ЦЕНУ
     */
    analyzePriceImpact(tradeAmount) {
        console.log(`\n🎯 АНАЛИЗ ВЛИЯНИЯ СДЕЛКИ $${tradeAmount.toLocaleString()} НА ЦЕНУ`);
        console.log('=' .repeat(80));
        
        const results = {};
        
        Object.entries(this.POOLS).forEach(([key, pool]) => {
            const impact = this.calculatePriceImpact(tradeAmount, pool);
            results[key] = impact;
            
            console.log(`\n${this.getPoolIcon(key)} ${pool.name}:`);
            console.log(`   TVL: $${pool.tvl.toLocaleString()}`);
            console.log(`   USDC Ликвидность: $${pool.liquidity.USDC.toLocaleString()}`);
            console.log(`   Влияние сделки: ${impact.impactPercent}%`);
            console.log(`   Потери от slippage: $${impact.slippageLoss.toLocaleString()}`);
            console.log(`   Комиссия: $${impact.fee.toLocaleString()}`);
            console.log(`   Общие потери: $${impact.totalLoss.toLocaleString()}`);
            console.log(`   Рейтинг: ${impact.rating}`);
        });
        
        return results;
    }

    /**
     * 🧮 РАСЧЕТ ВЛИЯНИЯ НА ЦЕНУ
     */
    calculatePriceImpact(tradeAmount, pool) {
        // Процент от ликвидности
        const impactPercent = (tradeAmount / pool.liquidity.USDC * 100);
        
        // Приблизительный расчет slippage (квадратичная функция)
        const slippagePercent = Math.pow(impactPercent / 100, 1.5) * 100;
        const slippageLoss = tradeAmount * (slippagePercent / 100);
        
        // Комиссия
        const fee = tradeAmount * pool.baseFee;
        
        // Общие потери
        const totalLoss = slippageLoss + fee;
        
        // Рейтинг (чем меньше потери, тем лучше)
        let rating;
        if (totalLoss < tradeAmount * 0.001) rating = '🟢 ОТЛИЧНО';
        else if (totalLoss < tradeAmount * 0.005) rating = '🟡 ХОРОШО';
        else if (totalLoss < tradeAmount * 0.02) rating = '🟠 ПРИЕМЛЕМО';
        else rating = '🔴 ПЛОХО';
        
        return {
            impactPercent: impactPercent.toFixed(2),
            slippagePercent: slippagePercent.toFixed(4),
            slippageLoss: Math.round(slippageLoss),
            fee: Math.round(fee),
            totalLoss: Math.round(totalLoss),
            rating
        };
    }

    /**
     * 🏆 РЕКОМЕНДАЦИЯ ОПТИМАЛЬНОГО ПУЛА
     */
    recommendOptimalPool(tradeAmount) {
        console.log(`\n🏆 РЕКОМЕНДАЦИЯ ОПТИМАЛЬНОГО ПУЛА ДЛЯ $${tradeAmount.toLocaleString()}`);
        console.log('=' .repeat(80));
        
        const analysis = this.analyzePriceImpact(tradeAmount);
        
        // Находим пул с минимальными потерями
        let bestPool = null;
        let minLoss = Infinity;
        
        Object.entries(analysis).forEach(([key, data]) => {
            if (data.totalLoss < minLoss) {
                minLoss = data.totalLoss;
                bestPool = key;
            }
        });
        
        const recommendedPool = this.POOLS[bestPool];
        const bestAnalysis = analysis[bestPool];
        
        console.log(`\n🎯 РЕКОМЕНДУЕМЫЙ ПУЛ: ${recommendedPool.name}`);
        console.log(`   Адрес: ${recommendedPool.address}`);
        console.log(`   Причина: Минимальные потери ($${bestAnalysis.totalLoss.toLocaleString()})`);
        console.log(`   Влияние на цену: всего ${bestAnalysis.impactPercent}%`);
        console.log(`   Рейтинг: ${bestAnalysis.rating}`);
        
        // Сравнение с худшим вариантом
        const worstPool = Object.entries(analysis).reduce((worst, [key, data]) => 
            data.totalLoss > worst.loss ? { key, loss: data.totalLoss } : worst, 
            { key: null, loss: 0 }
        );
        
        const savings = worstPool.loss - bestAnalysis.totalLoss;
        console.log(`\n💰 ЭКОНОМИЯ по сравнению с худшим вариантом: $${savings.toLocaleString()}`);
        
        return {
            recommended: {
                key: bestPool,
                pool: recommendedPool,
                analysis: bestAnalysis
            },
            savings,
            allAnalysis: analysis
        };
    }

    /**
     * 📈 АНАЛИЗ РАЗНЫХ РАЗМЕРОВ СДЕЛОК
     */
    analyzeMultipleTradeSizes() {
        console.log('\n📈 АНАЛИЗ РАЗНЫХ РАЗМЕРОВ АРБИТРАЖНЫХ СДЕЛОК');
        console.log('=' .repeat(80));
        
        const tradeSizes = [50000, 100000, 250000, 350000, 500000, 1000000]; // $50K - $1M
        
        tradeSizes.forEach(size => {
            const recommendation = this.recommendOptimalPool(size);
            console.log(`\n💵 Сделка $${size.toLocaleString()}:`);
            console.log(`   Лучший пул: ${recommendation.recommended.pool.name}`);
            console.log(`   Потери: $${recommendation.recommended.analysis.totalLoss.toLocaleString()}`);
            console.log(`   Экономия: $${recommendation.savings.toLocaleString()}`);
        });
    }

    /**
     * 🎨 ИКОНКА ПУЛА
     */
    getPoolIcon(poolKey) {
        const icons = {
            MEGA: '🌊',
            PRIMARY: '🏊',
            SECONDARY: '💧'
        };
        return icons[poolKey] || '📊';
    }

    /**
     * 🚀 ДЕМОНСТРАЦИЯ ПРЕИМУЩЕСТВ MEGA ПУЛА
     */
    demonstrateMegaPoolAdvantages() {
        console.log('\n🚀 ДЕМОНСТРАЦИЯ ПРЕИМУЩЕСТВ MEGA ПУЛА');
        console.log('=' .repeat(80));

        const arbitrageAmount = 50000; // $50K реалистичная сумма арбитража
        
        console.log(`💰 Арбитражная сделка: $${arbitrageAmount.toLocaleString()}`);
        
        const megaPool = this.POOLS.METEORA_MEGA;
        const secondaryPool = this.POOLS.METEORA_PRIMARY;
        
        const megaImpact = this.calculatePriceImpact(arbitrageAmount, megaPool);
        const secondaryImpact = this.calculatePriceImpact(arbitrageAmount, secondaryPool);
        
        console.log(`\n🌊 MEGA ПУЛ ($${megaPool.tvl.toLocaleString()} TVL):`);
        console.log(`   Влияние: ${megaImpact.impactPercent}% (незаметно!)`);
        console.log(`   Потери: $${megaImpact.totalLoss.toLocaleString()}`);
        console.log(`   Рейтинг: ${megaImpact.rating}`);
        
        console.log(`\n💧 МАЛЕНЬКИЙ ПУЛ ($${secondaryPool.tvl.toLocaleString()} TVL):`);
        console.log(`   Влияние: ${secondaryImpact.impactPercent}% (огромное!)`);
        console.log(`   Потери: $${secondaryImpact.totalLoss.toLocaleString()}`);
        console.log(`   Рейтинг: ${secondaryImpact.rating}`);
        
        const savings = secondaryImpact.totalLoss - megaImpact.totalLoss;
        console.log(`\n💎 ЭКОНОМИЯ ПРИ ИСПОЛЬЗОВАНИИ MEGA ПУЛА: $${savings.toLocaleString()}`);
        console.log(`🎯 ВЫВОД: MEGA пул экономит ${(savings/arbitrageAmount*100).toFixed(2)}% от суммы сделки!`);
        
        return {
            megaPool: megaImpact,
            secondaryPool: secondaryImpact,
            savings,
            savingsPercent: (savings/arbitrageAmount*100).toFixed(2)
        };
    }

    /**
     * 💰 АНАЛИЗ РЕАЛЬНОЙ ПРИБЫЛЬНОСТИ АРБИТРАЖА
     */
    analyzeArbitrageProfitability() {
        console.log('\n💰 АНАЛИЗ РЕАЛЬНОЙ ПРИБЫЛЬНОСТИ АРБИТРАЖА');
        console.log('=' .repeat(80));

        // Типичные размеры арбитражных сделок
        const tradeSizes = [5000, 10000, 25000, 50000]; // $5K - $50K

        // Типичные разности цен в арбитраже
        const priceDifferences = [0.1, 0.25, 0.5, 1.0]; // 0.1% - 1.0%

        console.log('📊 АНАЛИЗ ПРИБЫЛЬНОСТИ ДЛЯ РАЗНЫХ СЦЕНАРИЕВ:\n');

        tradeSizes.forEach(tradeSize => {
            console.log(`💵 СДЕЛКА $${tradeSize.toLocaleString()}:`);

            const megaImpact = this.calculatePriceImpact(tradeSize, this.POOLS.METEORA_MEGA);
            const totalCosts = parseInt(megaImpact.totalLoss);

            console.log(`   Потери в MEGA пуле: $${totalCosts.toLocaleString()}`);
            console.log(`   Влияние на цену: ${megaImpact.impactPercent}%`);

            priceDifferences.forEach(priceDiff => {
                const grossProfit = tradeSize * (priceDiff / 100);
                const netProfit = grossProfit - totalCosts;
                const profitPercent = (netProfit / tradeSize * 100).toFixed(3);

                const status = netProfit > 0 ? '✅ ПРИБЫЛЬНО' : '❌ УБЫТОЧНО';
                const emoji = netProfit > 0 ? '💚' : '🔴';

                console.log(`   ${emoji} Разница ${priceDiff}%: Прибыль $${netProfit.toLocaleString()} (${profitPercent}%) ${status}`);
            });
            console.log('');
        });

        // Рекомендации
        console.log('🎯 РЕКОМЕНДАЦИИ ДЛЯ ПРИБЫЛЬНОГО АРБИТРАЖА:');
        console.log('   • Оптимальный размер сделки: $5,000 - $25,000');
        console.log('   • Минимальная разница цен: 0.25% - 0.5%');
        console.log('   • Используйте только MEGA пул для минимальных потерь');
        console.log('   • Мониторьте возможности каждые 10-30 секунд');

        return this.calculateOptimalTradeSize();
    }

    /**
     * 🎯 РАСЧЕТ ОПТИМАЛЬНОГО РАЗМЕРА СДЕЛКИ
     */
    calculateOptimalTradeSize() {
        console.log('\n🎯 ПОИСК ОПТИМАЛЬНОГО РАЗМЕРА СДЕЛКИ');
        console.log('=' .repeat(80));

        const testSizes = [1000, 2500, 5000, 7500, 10000, 15000, 20000, 25000, 30000];
        const targetPriceDiff = 0.5; // 0.5% разница цен

        let bestTrade = { size: 0, profit: -Infinity, profitPercent: 0 };

        console.log(`📈 Анализ для разности цен ${targetPriceDiff}%:\n`);

        testSizes.forEach(size => {
            const impact = this.calculatePriceImpact(size, this.POOLS.MEGA);
            const costs = parseInt(impact.totalLoss);
            const grossProfit = size * (targetPriceDiff / 100);
            const netProfit = grossProfit - costs;
            const profitPercent = (netProfit / size * 100);

            if (netProfit > bestTrade.profit) {
                bestTrade = { size, profit: netProfit, profitPercent };
            }

            const status = netProfit > 0 ? '✅' : '❌';
            console.log(`   ${status} $${size.toLocaleString()}: Прибыль $${netProfit.toLocaleString()} (${profitPercent.toFixed(3)}%)`);
        });

        console.log(`\n🏆 ОПТИМАЛЬНЫЙ РАЗМЕР СДЕЛКИ: $${bestTrade.size.toLocaleString()}`);
        console.log(`   Чистая прибыль: $${bestTrade.profit.toLocaleString()}`);
        console.log(`   Процент прибыли: ${bestTrade.profitPercent.toFixed(3)}%`);

        return bestTrade;
    }

    /**
     * 🚀 АНАЛИЗ АРБИТРАЖА МЕЖДУ ГИГАНТСКИМИ ПУЛАМИ
     */
    analyzeGiantPoolArbitrage() {
        console.log('\n🚀 АНАЛИЗ АРБИТРАЖА МЕЖДУ ГИГАНТСКИМИ ПУЛАМИ');
        console.log('=' .repeat(80));

        const giants = [
            { key: 'ORCA_GIANT', pool: this.POOLS.ORCA_GIANT },
            { key: 'RAYDIUM_GIANT', pool: this.POOLS.RAYDIUM_GIANT },
            { key: 'METEORA_MEGA', pool: this.POOLS.METEORA_MEGA }
        ];

        const tradeSizes = [10000, 25000, 50000, 100000]; // $10K - $100K

        console.log('💎 СРАВНЕНИЕ ГИГАНТСКИХ ПУЛОВ:\n');

        giants.forEach(({ key, pool }) => {
            console.log(`${this.getGiantIcon(key)} ${pool.name}:`);
            console.log(`   TVL: $${pool.tvl.toLocaleString()}`);
            console.log(`   Протокол: ${pool.protocol}`);
            console.log(`   Комиссия: ${(pool.baseFee * 100).toFixed(3)}%`);

            tradeSizes.forEach(size => {
                const impact = this.calculatePriceImpact(size, pool);
                const emoji = parseFloat(impact.impactPercent) < 1 ? '🟢' :
                             parseFloat(impact.impactPercent) < 3 ? '🟡' : '🔴';
                console.log(`   ${emoji} $${size.toLocaleString()}: ${impact.impactPercent}% влияния, $${impact.totalLoss} потерь`);
            });
            console.log('');
        });

        // Анализ арбитражных возможностей
        console.log('🎯 АРБИТРАЖНЫЕ ВОЗМОЖНОСТИ:');
        console.log('   • ORCA ↔ RAYDIUM: Разные протоколы = разные цены!');
        console.log('   • ORCA ↔ METEORA: Огромная vs средняя ликвидность');
        console.log('   • RAYDIUM ↔ METEORA: Разные механизмы ценообразования');

        // Рекомендуемые пары для арбитража
        console.log('\n🏆 ЛУЧШИЕ ПАРЫ ДЛЯ АРБИТРАЖА:');
        console.log('   1. 🐋 ORCA ($44M) ↔ 🌊 RAYDIUM ($11M)');
        console.log('      └─ Минимальный slippage в обоих пулах');
        console.log('      └─ Разные протоколы = стабильные разности цен');
        console.log('   2. 🐋 ORCA ($44M) ↔ ⚡ METEORA ($7M)');
        console.log('      └─ ORCA практически без slippage');
        console.log('      └─ METEORA с низкими комиссиями');

        return this.calculateOptimalArbitragePair();
    }

    /**
     * 🎯 РАСЧЕТ ОПТИМАЛЬНОЙ АРБИТРАЖНОЙ ПАРЫ
     */
    calculateOptimalArbitragePair() {
        console.log('\n🎯 ПОИСК ОПТИМАЛЬНОЙ АРБИТРАЖНОЙ ПАРЫ');
        console.log('=' .repeat(80));

        const pairs = [
            { buy: 'ORCA_GIANT', sell: 'RAYDIUM_GIANT', name: 'ORCA → RAYDIUM' },
            { buy: 'RAYDIUM_GIANT', sell: 'ORCA_GIANT', name: 'RAYDIUM → ORCA' },
            { buy: 'ORCA_GIANT', sell: 'METEORA_MEGA', name: 'ORCA → METEORA' },
            { buy: 'METEORA_MEGA', sell: 'ORCA_GIANT', name: 'METEORA → ORCA' },
            { buy: 'RAYDIUM_GIANT', sell: 'METEORA_MEGA', name: 'RAYDIUM → METEORA' },
            { buy: 'METEORA_MEGA', sell: 'RAYDIUM_GIANT', name: 'METEORA → RAYDIUM' }
        ];

        const tradeSize = 30000; // $30K оптимальный размер
        const priceDiff = 0.3; // 0.3% типичная разница между протоколами

        console.log(`📊 Анализ для сделки $${tradeSize.toLocaleString()} при разнице ${priceDiff}%:\n`);

        let bestPair = { profit: -Infinity, name: '', details: null };

        pairs.forEach(pair => {
            const buyPool = this.POOLS[pair.buy];
            const sellPool = this.POOLS[pair.sell];

            const buyCosts = this.calculatePriceImpact(tradeSize, buyPool);
            const sellCosts = this.calculatePriceImpact(tradeSize, sellPool);

            const totalCosts = parseInt(buyCosts.totalLoss) + parseInt(sellCosts.totalLoss);
            const grossProfit = tradeSize * (priceDiff / 100);
            const netProfit = grossProfit - totalCosts;
            const profitPercent = (netProfit / tradeSize * 100);

            if (netProfit > bestPair.profit) {
                bestPair = {
                    profit: netProfit,
                    name: pair.name,
                    details: { buyCosts, sellCosts, totalCosts, grossProfit, profitPercent }
                };
            }

            const status = netProfit > 0 ? '✅' : '❌';
            const emoji = netProfit > 0 ? '💚' : '🔴';

            console.log(`   ${status} ${pair.name}:`);
            console.log(`      ${emoji} Прибыль: $${netProfit.toLocaleString()} (${profitPercent.toFixed(3)}%)`);
            console.log(`      💸 Потери покупки: $${buyCosts.totalLoss} (${buyPool.name})`);
            console.log(`      💸 Потери продажи: $${sellCosts.totalLoss} (${sellPool.name})`);
            console.log('');
        });

        console.log(`🏆 ЛУЧШАЯ АРБИТРАЖНАЯ ПАРА: ${bestPair.name}`);
        console.log(`   💰 Чистая прибыль: $${bestPair.profit.toLocaleString()}`);
        console.log(`   📈 Процент прибыли: ${bestPair.details.profitPercent.toFixed(3)}%`);
        console.log(`   💸 Общие потери: $${bestPair.details.totalCosts.toLocaleString()}`);

        return bestPair;
    }

    /**
     * 🎨 ИКОНКИ ДЛЯ ГИГАНТСКИХ ПУЛОВ
     */
    getGiantIcon(poolKey) {
        const icons = {
            ORCA_GIANT: '🐋',
            RAYDIUM_GIANT: '🌊',
            METEORA_MEGA: '⚡',
            METEORA_PRIMARY: '💧'
        };
        return icons[poolKey] || '📊';
    }
}

/**
 * 🎯 ГЛАВНАЯ ФУНКЦИЯ АНАЛИЗА
 */
async function main() {
    console.log('📊 ЗАПУСК АНАЛИЗАТОРА АРБИТРАЖА...\n');
    
    const analyzer = new ArbitrageAnalyzer();
    
    try {
        // 1. Анализ арбитража между гигантскими пулами
        const giantAnalysis = analyzer.analyzeGiantPoolArbitrage();

        // 2. Анализ реальной прибыльности арбитража
        const profitability = analyzer.analyzeArbitrageProfitability();

        // 3. Демонстрируем преимущества MEGA пула
        const advantages = analyzer.demonstrateMegaPoolAdvantages();

        console.log('\n🎉 АНАЛИЗ ЗАВЕРШЕН!');
        console.log(`🏆 ЛУЧШАЯ АРБИТРАЖНАЯ ПАРА: ${giantAnalysis.name}`);
        console.log(`💰 ПРИБЫЛЬ С ГИГАНТСКИМИ ПУЛАМИ: $${giantAnalysis.profit.toLocaleString()}`);
        console.log(`📈 ПРОЦЕНТ ПРИБЫЛИ: ${giantAnalysis.details.profitPercent.toFixed(3)}%`);
        console.log(`🚀 ORCA + RAYDIUM = ИДЕАЛЬНАЯ КОМБИНАЦИЯ ДЛЯ АРБИТРАЖА!`);

        return {
            status: 'SUCCESS',
            bestArbitragePair: giantAnalysis,
            optimalTrade: profitability,
            advantages
        };
        
    } catch (error) {
        console.error('❌ Ошибка анализа:', error);
        return {
            status: 'ERROR',
            error: error.message
        };
    }
}

// Запуск если файл выполняется напрямую
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { ArbitrageAnalyzer };
