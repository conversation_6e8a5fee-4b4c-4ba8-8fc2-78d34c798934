/**
 * 🔥 ТЕСТОВЫЙ СКРИПТ ДЛЯ СОЗДАНИЯ ПРАВИЛЬНЫХ BIN ARRAYS
 * 
 * Цель: Создать bin arrays из наших 3 бинов так, чтобы SDK их принял
 */

const { Connection, PublicKey } = require('@solana/web3.js');
const DLMM = require('@meteora-ag/dlmm').default;
const BN = require('bn.js');

// 🔥 КОНФИГУРАЦИЯ
const RPC_URL = 'https://boldest-alien-sailboat.solana-mainnet.quiknode.pro/490b2144daf49e665cacd2cca143e15e4579caf3/';
const POOL_ADDRESS = '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6'; // Pool 1
const METEORA_PROGRAM_ID = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');

// 🔥 НАШИ 3 БИНА ИЗ КЭША (ПРИМЕР)
const OUR_THREE_BINS = [
    {
        binId: -4192,
        price: 186.9217,
        liquidityX: 0,
        liquidityY: 0,
        isActive: false,
        empty: true
    },
    {
        binId: -4191,
        price: 187.1088,
        liquidityX: 83892, // WSOL
        liquidityY: 15588832, // USDC
        isActive: true,
        empty: false
    },
    {
        binId: -4190,
        price: 187.2959,
        liquidityX: 0,
        liquidityY: 0,
        isActive: false,
        empty: true
    }
];

class BinArrayTester {
    constructor() {
        this.connection = new Connection(RPC_URL);
        this.dlmmPool = null;
    }

    async initialize() {
        console.log('🔥 ИНИЦИАЛИЗАЦИЯ ТЕСТЕРА BIN ARRAYS...');
        
        // Создаем DLMM пул
        this.dlmmPool = await DLMM.create(this.connection, new PublicKey(POOL_ADDRESS));
        console.log('✅ DLMM пул создан');
        
        // Обновляем состояние
        await this.dlmmPool.refetchStates();
        console.log('✅ Состояние пула обновлено');
        
        // Показываем активный бин
        console.log(`📊 Активный бин: ${this.dlmmPool.lbPair.activeId}`);
    }

    /**
     * 🔥 МЕТОД 1: ИСПОЛЬЗУЕМ ОФИЦИАЛЬНЫЙ getBinArrayForSwap
     */
    async testOfficialBinArrays() {
        console.log('\n🔥 ТЕСТ 1: ОФИЦИАЛЬНЫЙ getBinArrayForSwap');
        
        try {
            const binArrays = await this.dlmmPool.getBinArrayForSwap(false); // X->Y (SOL->USDC)
            console.log(`✅ Получено ${binArrays.length} bin arrays от SDK`);
            
            binArrays.forEach((binArray, index) => {
                console.log(`   ${index + 1}. ${binArray.publicKey.toString()}`);
                console.log(`      Index: ${binArray.account.index}`);
                console.log(`      Bins: ${binArray.account.bins.length}`);
            });
            
            return binArrays;
        } catch (error) {
            console.log(`❌ Ошибка официального метода: ${error.message}`);
            return null;
        }
    }

    /**
     * 🔥 МЕТОД 2: СОЗДАЕМ BIN ARRAYS ИЗ НАШИХ 3 БИНОВ
     */
    async createBinArraysFromOurBins() {
        console.log('\n🔥 ТЕСТ 2: СОЗДАНИЕ BIN ARRAYS ИЗ НАШИХ БИНОВ');
        
        try {
            // 1. Определяем какие bin array индексы нам нужны
            const binArrayIndices = this.calculateRequiredBinArrayIndices();
            console.log(`📊 Нужные bin array индексы: ${binArrayIndices.join(', ')}`);
            
            // 2. Генерируем PDA адреса для bin arrays
            const binArrayAddresses = [];
            for (const index of binArrayIndices) {
                const [binArrayPda] = await PublicKey.findProgramAddress(
                    [
                        Buffer.from('bin_array'),
                        new PublicKey(POOL_ADDRESS).toBuffer(),
                        this.indexToBytes(index)
                    ],
                    METEORA_PROGRAM_ID
                );
                binArrayAddresses.push(binArrayPda);
                console.log(`   Index ${index} -> ${binArrayPda.toString()}`);
            }
            
            // 3. Загружаем данные bin arrays с блокчейна
            const binArraysData = [];
            for (let i = 0; i < binArrayAddresses.length; i++) {
                try {
                    const accountInfo = await this.connection.getAccountInfo(binArrayAddresses[i]);
                    if (accountInfo) {
                        binArraysData.push({
                            publicKey: binArrayAddresses[i],
                            account: {
                                index: binArrayIndices[i],
                                bins: [], // Заполним позже
                                version: 1
                            }
                        });
                        console.log(`   ✅ Bin array ${binArrayIndices[i]} загружен`);
                    } else {
                        console.log(`   ⚠️ Bin array ${binArrayIndices[i]} не существует`);
                    }
                } catch (error) {
                    console.log(`   ❌ Ошибка загрузки bin array ${binArrayIndices[i]}: ${error.message}`);
                }
            }
            
            return binArraysData;
            
        } catch (error) {
            console.log(`❌ Ошибка создания bin arrays: ${error.message}`);
            return null;
        }
    }

    /**
     * 🔥 МЕТОД 3: ТЕСТИРУЕМ SWAP QUOTE С РАЗНЫМИ BIN ARRAYS
     */
    async testSwapQuote(binArrays, testName) {
        console.log(`\n🔥 ТЕСТ SWAP QUOTE: ${testName}`);
        
        if (!binArrays || binArrays.length === 0) {
            console.log('❌ Нет bin arrays для тестирования');
            return false;
        }
        
        try {
            const amountIn = new BN('1000000000000'); // 1000 WSOL
            const swapYtoX = false; // X->Y (SOL->USDC)
            const slippage = new BN(10000); // 100% slippage
            
            console.log(`📊 Параметры swap:`);
            console.log(`   Amount in: ${amountIn.toString()} lamports`);
            console.log(`   Direction: ${swapYtoX ? 'Y->X (USDC->SOL)' : 'X->Y (SOL->USDC)'}`);
            console.log(`   Bin arrays: ${binArrays.length}`);
            
            const swapQuote = await this.dlmmPool.swapQuote(
                amountIn,
                swapYtoX,
                slippage,
                binArrays
            );
            
            console.log(`✅ SWAP QUOTE УСПЕШЕН!`);
            console.log(`   Out amount: ${swapQuote.outAmount.toString()}`);
            console.log(`   Min out: ${swapQuote.minOutAmount.toString()}`);
            
            return true;
            
        } catch (error) {
            console.log(`❌ SWAP QUOTE ПРОВАЛИЛСЯ: ${error.message}`);
            return false;
        }
    }

    /**
     * 🔧 ВСПОМОГАТЕЛЬНЫЕ МЕТОДЫ
     */
    calculateRequiredBinArrayIndices() {
        const BIN_ARRAY_SIZE = 70; // Размер bin array в Meteora
        const indices = new Set();
        
        // Добавляем индексы для всех наших бинов
        OUR_THREE_BINS.forEach(bin => {
            const binArrayIndex = Math.floor(bin.binId / BIN_ARRAY_SIZE);
            indices.add(binArrayIndex);
        });
        
        return Array.from(indices).sort((a, b) => a - b);
    }

    indexToBytes(index) {
        const buffer = Buffer.alloc(8);
        buffer.writeInt32LE(index, 0);
        return buffer;
    }
}

/**
 * 🚀 ГЛАВНАЯ ФУНКЦИЯ ТЕСТИРОВАНИЯ
 */
async function main() {
    console.log('🔥🔥🔥 ТЕСТЕР BIN ARRAYS ЗАПУЩЕН! 🔥🔥🔥');
    console.log(`📊 Пул: ${POOL_ADDRESS}`);
    console.log(`📊 Наши бины: ${OUR_THREE_BINS.map(b => b.binId).join(', ')}`);
    
    const tester = new BinArrayTester();
    
    try {
        // Инициализация
        await tester.initialize();
        
        // Тест 1: Официальные bin arrays
        const officialBinArrays = await tester.testOfficialBinArrays();
        if (officialBinArrays) {
            await tester.testSwapQuote(officialBinArrays, 'ОФИЦИАЛЬНЫЕ BIN ARRAYS');
        }
        
        // Тест 2: Наши bin arrays
        const ourBinArrays = await tester.createBinArraysFromOurBins();
        if (ourBinArrays) {
            await tester.testSwapQuote(ourBinArrays, 'НАШИ BIN ARRAYS');
        }
        
        console.log('\n🎯 ТЕСТИРОВАНИЕ ЗАВЕРШЕНО!');
        
    } catch (error) {
        console.error('❌ КРИТИЧЕСКАЯ ОШИБКА:', error);
    }
}

// Запуск
if (require.main === module) {
    main().catch(console.error);
}

module.exports = BinArrayTester;
