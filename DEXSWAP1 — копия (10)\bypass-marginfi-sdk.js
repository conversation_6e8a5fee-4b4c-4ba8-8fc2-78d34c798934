#!/usr/bin/env node

/**
 * 🔥 ОБХОД MARGINFI SDK
 * Работаем напрямую с RPC без MarginFi SDK
 */

const { Connection, PublicKey, TransactionMessage, TransactionInstruction } = require('@solana/web3.js');
const CustomFlashLoanSystem = require('./custom-flash-loan-system');
const fs = require('fs');

/**
 * 🔍 ПЕРЕХВАТЧИК compileToV0Message
 */
function interceptCompileToV0Message() {
    const originalCompile = TransactionMessage.prototype.compileToV0Message;
    
    TransactionMessage.prototype.compileToV0Message = function(addressLookupTableAccounts = []) {
        console.log('\n🚨 ПЕРЕХВАЧЕН compileToV0Message!');
        console.log('=' .repeat(60));
        
        // Анализируем инструкции
        console.log(`📊 Инструкций: ${this.instructions.length}`);
        console.log(`🗜️ ALT таблиц: ${addressLookupTableAccounts.length}`);
        console.log(`💰 Payer: ${this.payerKey.toString().slice(0, 8)}...`);
        
        // Собираем все аккаунты из инструкций
        const allAccounts = new Map();
        const duplicates = new Set();
        
        this.instructions.forEach((instruction, instrIndex) => {
            console.log(`\n📋 ИНСТРУКЦИЯ ${instrIndex}: ${instruction.programId.toString().slice(0, 8)}...`);
            
            // Program ID
            const programIdStr = instruction.programId.toString();
            if (allAccounts.has(programIdStr)) {
                console.log(`🚨 ДУБЛИРОВАНИЕ PROGRAM ID: ${programIdStr.slice(0, 8)}...`);
                duplicates.add(programIdStr);
            } else {
                allAccounts.set(programIdStr, `instr${instrIndex}_programId`);
            }
            
            // Account keys
            instruction.keys.forEach((key, keyIndex) => {
                const keyStr = key.pubkey.toString();
                const role = `instr${instrIndex}_key${keyIndex}${key.isSigner ? '_signer' : ''}${key.isWritable ? '_writable' : ''}`;
                
                console.log(`   ${keyIndex}. ${keyStr.slice(0, 8)}...${keyStr.slice(-8)} (${key.isSigner ? 'signer' : ''}${key.isWritable ? ' writable' : ''})`);
                
                if (allAccounts.has(keyStr)) {
                    const existing = allAccounts.get(keyStr);
                    console.log(`🚨 ДУБЛИРОВАНИЕ ACCOUNT: ${keyStr.slice(0, 8)}...${keyStr.slice(-8)}`);
                    console.log(`   Первое: ${existing}`);
                    console.log(`   Повторное: ${role}`);
                    duplicates.add(keyStr);
                } else {
                    allAccounts.set(keyStr, role);
                }
            });
        });
        
        // Анализируем ALT таблицы
        console.log('\n🗜️ АНАЛИЗ ALT ТАБЛИЦ:');
        addressLookupTableAccounts.forEach((altTable, index) => {
            console.log(`📋 ALT ${index}: ${altTable.key.toString().slice(0, 8)}... (${altTable.state.addresses.length} аккаунтов)`);
            
            altTable.state.addresses.forEach((address, addrIndex) => {
                const addressStr = address.toString();
                
                if (allAccounts.has(addressStr)) {
                    const existing = allAccounts.get(addressStr);
                    console.log(`🚨 ДУБЛИРОВАНИЕ ALT-INSTRUCTION: ${addressStr.slice(0, 8)}...${addressStr.slice(-8)}`);
                    console.log(`   В инструкциях: ${existing}`);
                    console.log(`   В ALT ${index}[${addrIndex}]`);
                    duplicates.add(addressStr);
                }
            });
        });
        
        console.log('\n🎯 ИТОГОВЫЙ АНАЛИЗ:');
        console.log(`📊 Всего уникальных аккаунтов: ${allAccounts.size}`);
        console.log(`🚨 Дублирующихся аккаунтов: ${duplicates.size}`);
        
        if (duplicates.size > 0) {
            console.log('\n❌ ДУБЛИРУЮЩИЕСЯ АККАУНТЫ:');
            duplicates.forEach(address => {
                console.log(`   ${address.slice(0, 8)}...${address.slice(-8)}`);
            });
        }
        
        // Вызываем оригинальный метод
        try {
            const result = originalCompile.call(this, addressLookupTableAccounts);
            console.log('\n✅ compileToV0Message УСПЕШНО!');
            console.log(`📊 Static account keys: ${result.staticAccountKeys.length}`);
            console.log(`🗜️ Address table lookups: ${result.addressTableLookups.length}`);
            return result;
        } catch (error) {
            console.log('\n❌ compileToV0Message ОШИБКА!');
            console.log(`🚨 Ошибка: ${error.message}`);
            
            if (error.message.includes('AccountLoadedTwice')) {
                console.log('🎯 НАЙДЕНА ПРИЧИНА AccountLoadedTwice!');
                
                // Детальный анализ ошибки
                console.log('\n🔍 ДЕТАЛЬНЫЙ АНАЛИЗ ОШИБКИ:');
                console.log('Дублирующиеся аккаунты которые вызывают ошибку:');
                duplicates.forEach(address => {
                    console.log(`   🚨 ${address}`);
                });
            }
            
            throw error;
        }
    };
}

/**
 * 🔧 СОЗДАНИЕ ПРОСТОЙ ТРАНЗАКЦИИ БЕЗ MARGINFI SDK
 */
async function createSimpleTransactionWithoutSDK() {
    console.log('🔧 СОЗДАНИЕ ПРОСТОЙ ТРАНЗАКЦИИ БЕЗ MARGINFI SDK...');
    
    // Загружаем кошелек
    const walletData = JSON.parse(fs.readFileSync('./wallet.json', 'utf8'));
    const { Keypair } = require('@solana/web3.js');
    const wallet = Keypair.fromSecretKey(new Uint8Array(walletData));
    
    // Создаем простую инструкцию (например, transfer)
    const { SystemProgram } = require('@solana/web3.js');
    const instruction = SystemProgram.transfer({
        fromPubkey: wallet.publicKey,
        toPubkey: new PublicKey('So11111111111111111111111111111111111111112'),
        lamports: 1000
    });
    
    console.log('📋 Создана простая transfer инструкция');
    
    // Загружаем ALT таблицы
    require('dotenv').config({ path: '.env.solana' });
    const quicknodeUrl = process.env.QUICKNODE3_RPC_URL || 
                        process.env.QUICKNODE_RPC_URL || 
                        'https://sparkling-patient-market.solana-mainnet.quiknode.pro/146de9e433ca3b8f3473c0bd41742b24429dfc43/';
    
    const connection = new Connection(quicknodeUrl, {
        commitment: 'confirmed',
        confirmTransactionInitialTimeout: 60000
    });
    
    console.log('🗜️ Загружаем ALT таблицы...');
    const altTables = [];
    const altAddresses = [
        'HGmknUTUmeovMc9ryERNWG6UFZDFDVr9xrum3ZhyL4fC', // MarginFi 1
        '5FuKF7C1tJji2mXZuJ14U9oDb37is5mmvYLf4KwojoF1', // MarginFi 2
        'FEFhAFKz48P3w82Ds5VhvyEDwhRqu2FejmnuxEPZ8wNR', // MarginFi 3
        'FAeyUf4AdG7vTQ2Q89r9hWBkPdYkykJKG4CxgqArBGXe'  // Кастомная
    ];
    
    for (const address of altAddresses) {
        try {
            const altAccount = await connection.getAddressLookupTable(new PublicKey(address));
            if (altAccount && altAccount.value) {
                altTables.push(altAccount.value);
                console.log(`✅ ALT таблица загружена: ${address} (${altAccount.value.state.addresses.length} адресов)`);
            }
        } catch (e) {
            console.log(`⚠️ ALT таблица недоступна: ${address}`);
        }
    }
    
    console.log(`🗜️ Загружено ALT таблиц: ${altTables.length}`);
    
    // Создаем транзакцию
    const message = new TransactionMessage({
        payerKey: wallet.publicKey,
        recentBlockhash: 'EkSnNWid2cvwEVnVx9aBqawnmiCNiDgp3gUdkDPTKN1N', // Статический blockhash
        instructions: [instruction],
    });
    
    // Компилируем с ALT таблицами
    try {
        const compiledMessage = message.compileToV0Message(altTables);
        console.log('✅ Простая транзакция создана успешно БЕЗ MarginFi SDK');
        console.log(`📊 Static account keys: ${compiledMessage.staticAccountKeys.length}`);
        console.log(`🗜️ Address table lookups: ${compiledMessage.addressTableLookups.length}`);
    } catch (error) {
        console.log(`❌ Ошибка создания простой транзакции: ${error.message}`);
        
        if (error.message.includes('AccountLoadedTwice')) {
            console.log('🎉 ВОСПРОИЗВЕДЕНА AccountLoadedTwice БЕЗ MarginFi SDK!');
        }
    }
}

/**
 * 🚀 ОСНОВНАЯ ФУНКЦИЯ ОБХОДА
 */
async function bypassMarginFiSDK() {
    console.log('🔥 ОБХОД MARGINFI SDK');
    console.log('=' .repeat(50));

    try {
        // 1. Устанавливаем перехватчик
        console.log('🔧 Установка перехватчика compileToV0Message...');
        interceptCompileToV0Message();
        
        // 2. Создаем простую транзакцию БЕЗ MarginFi SDK
        await createSimpleTransactionWithoutSDK();

    } catch (error) {
        console.error('❌ Ошибка обхода:', error.message);
        console.error(error.stack);
    }
}

// Запуск обхода
if (require.main === module) {
    bypassMarginFiSDK().catch(console.error);
}

module.exports = { bypassMarginFiSDK, interceptCompileToV0Message };
