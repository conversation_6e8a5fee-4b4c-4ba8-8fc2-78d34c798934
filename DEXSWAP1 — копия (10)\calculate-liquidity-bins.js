#!/usr/bin/env node

/**
 * 🔍 РАСЧЕТ BIN ARRAYS ДЛЯ ДОБАВЛЕНИЯ ЛИКВИДНОСТИ $1.4M
 * 
 * Этот скрипт рассчитывает:
 * 1. Сколько bins покроет позиция $1.4M
 * 2. Какие bin arrays нужны для покрытия
 * 3. Влияние на цену (спойлер: никакого)
 */

const BN = require('bn.js');

async function calculateLiquidityBins() {
    try {
        console.log('🔍 РАСЧЕТ BIN ARRAYS ДЛЯ ДОБАВЛЕНИЯ ЛИКВИДНОСТИ $1.4M\n');
        
        // 🔧 ПАРАМЕТРЫ ИЗ ЛОГОВ
        const activeBinId = -1691;
        const TOTAL_RANGE_INTERVAL = 10; // 10 bins на каждую сторону
        const minBinId = activeBinId - TOTAL_RANGE_INTERVAL; // -1701
        const maxBinId = activeBinId + TOTAL_RANGE_INTERVAL; // -1681
        const totalBins = maxBinId - minBinId + 1; // 21 bin
        
        console.log('📊 ПАРАМЕТРЫ ПОЗИЦИИ:');
        console.log(`   Активный bin: ${activeBinId}`);
        console.log(`   Диапазон: ${minBinId} до ${maxBinId}`);
        console.log(`   Всего bins: ${totalBins}`);
        
        // 💰 РАЗМЕР ЛИКВИДНОСТИ
        const liquidityUSD = 1400000; // $1.4M
        const liquidityPerBin = liquidityUSD / totalBins;
        
        console.log(`\n💰 РАСПРЕДЕЛЕНИЕ ЛИКВИДНОСТИ:`);
        console.log(`   Общая ликвидность: $${liquidityUSD.toLocaleString()}`);
        console.log(`   На каждый bin: $${liquidityPerBin.toLocaleString()}`);
        
        // 🔍 BIN ARRAYS РАСЧЕТ
        const BIN_ARRAY_SIZE = 70; // bins в одном bin array
        
        // Находим, какие bin arrays покрывают наш диапазон
        const requiredBinArrays = [];
        
        for (let binId = minBinId; binId <= maxBinId; binId++) {
            const binArrayIndex = Math.floor(binId / BIN_ARRAY_SIZE);
            const binArrayStartId = binArrayIndex * BIN_ARRAY_SIZE;
            const binArrayEndId = binArrayStartId + BIN_ARRAY_SIZE - 1;
            
            // Проверяем, есть ли уже этот bin array в списке
            const existingBinArray = requiredBinArrays.find(ba => ba.index === binArrayIndex);
            if (!existingBinArray) {
                requiredBinArrays.push({
                    index: binArrayIndex,
                    startId: binArrayStartId,
                    endId: binArrayEndId,
                    coversBins: []
                });
            }
            
            // Добавляем bin в покрытие
            const binArray = requiredBinArrays.find(ba => ba.index === binArrayIndex);
            binArray.coversBins.push(binId);
        }
        
        console.log(`\n🔥 НЕОБХОДИМЫЕ BIN ARRAYS:`);
        requiredBinArrays.forEach((binArray, index) => {
            console.log(`   ${index + 1}. Bin Array ${binArray.index}:`);
            console.log(`      Покрывает bins: ${binArray.startId} до ${binArray.endId}`);
            console.log(`      Наши bins: ${binArray.coversBins.join(', ')}`);
            console.log(`      Количество наших bins: ${binArray.coversBins.length}`);
        });
        
        console.log(`\n📊 ИТОГО:`);
        console.log(`   Всего bin arrays: ${requiredBinArrays.length}`);
        console.log(`   Всего bins в позиции: ${totalBins}`);
        
        // 🎯 ВЛИЯНИЕ НА ЦЕНУ
        console.log(`\n🎯 ВЛИЯНИЕ НА ЦЕНУ:`);
        console.log(`   ❌ Добавление ликвидности НЕ МЕНЯЕТ цену!`);
        console.log(`   ✅ Ликвидность распределяется по диапазону`);
        console.log(`   ✅ Увеличивается глубина в каждом bin`);
        console.log(`   ✅ Цена остается на активном bin: ${activeBinId}`);
        
        // 🔍 СРАВНЕНИЕ СО SWAP
        console.log(`\n🔍 СРАВНЕНИЕ СО SWAP:`);
        console.log(`   📈 SWAP $420K: ДВИГАЕТ цену на ~10-50 bins`);
        console.log(`   📊 LIQUIDITY $1.4M: НЕ двигает цену (0 bins)`);
        console.log(`   🎯 Разные механизмы: swap = торговля, liquidity = обеспечение`);
        
        // 🚨 ВАЖНЫЕ ВЫВОДЫ
        console.log(`\n🚨 ВАЖНЫЕ ВЫВОДЫ:`);
        console.log(`   1. Для добавления ликвидности нужен только ${requiredBinArrays.length} bin array`);
        console.log(`   2. Цена НЕ МЕНЯЕТСЯ при добавлении ликвидности`);
        console.log(`   3. Все bin arrays уже должны быть в ALT таблицах`);
        console.log(`   4. Размер позиции НЕ ВЛИЯЕТ на количество bin arrays`);
        console.log(`   5. Влияет только ДИАПАЗОН позиции (${totalBins} bins)`);
        
        // 🔥 РЕКОМЕНДАЦИИ ДЛЯ SWAP
        console.log(`\n🔥 РЕКОМЕНДАЦИИ ДЛЯ SWAP:`);
        console.log(`   1. Первый swap (USDC→SOL): bins от ${activeBinId} до ${activeBinId + 50}`);
        console.log(`   2. Второй swap (SOL→USDC): bins от ${activeBinId - 50} до ${activeBinId + 100}`);
        console.log(`   3. Используйте широкий диапазон для покрытия движения цены`);
        console.log(`   4. Ограничьте до 15 bin arrays для соблюдения лимита транзакции`);
        
    } catch (error) {
        console.error(`❌ Ошибка: ${error.message}`);
        process.exit(1);
    }
}

calculateLiquidityBins();
