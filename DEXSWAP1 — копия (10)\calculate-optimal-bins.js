#!/usr/bin/env node

/**
 * 🔍 ТОЧНЫЙ РАСЧЕТ ОПТИМАЛЬНОГО ДИАПАЗОНА BINS ДЛЯ METEORA DLMM
 * 
 * Рассчитывает:
 * 1. Влияние добавления $1.4M USDC на цену
 * 2. Оптимальный диапазон для продажи 30% SOL
 * 3. Минимальное проскальзывание
 */

const BN = require('bn.js');

async function calculateOptimalBins() {
    try {
        console.log('🔍 ТОЧНЫЙ РАСЧЕТ ОПТИМАЛЬНОГО ДИАПАЗОНА BINS\n');
        
        // 📊 ИСХОДНЫЕ ДАННЫЕ
        const liquidityToAdd = 1400000; // $1.4M USDC
        const currentSOLPrice = 5.44; // $5.44 за SOL
        const activeBinId = -1691;
        const sellPercentage = 0.30; // 30% от полученного SOL
        
        console.log('📊 ИСХОДНЫЕ ДАННЫЕ:');
        console.log(`   Добавляем ликвидности: $${liquidityToAdd.toLocaleString()}`);
        console.log(`   Текущая цена SOL: $${currentSOLPrice}`);
        console.log(`   Активный bin: ${activeBinId}`);
        console.log(`   Продаем: ${sellPercentage * 100}% от полученного SOL`);
        
        // 🔍 РАСЧЕТ ПОЛУЧЕННОГО SOL
        const solReceived = liquidityToAdd / currentSOLPrice;
        const solToSell = solReceived * sellPercentage;
        
        console.log(`\n💰 РАСЧЕТ SOL:`);
        console.log(`   Получим SOL: ${solReceived.toLocaleString()} SOL`);
        console.log(`   Продадим SOL: ${solToSell.toLocaleString()} SOL (30%)`);
        
        // 🔍 METEORA DLMM ПАРАМЕТРЫ
        const BIN_STEP = 0.001; // 0.1% на bin (стандартный)
        const BINS_PER_ARRAY = 70;
        
        console.log(`\n🔧 METEORA DLMM ПАРАМЕТРЫ:`);
        console.log(`   Bin step: ${BIN_STEP * 100}% на bin`);
        console.log(`   Bins в массиве: ${BINS_PER_ARRAY}`);
        
        // 🎯 РАСЧЕТ РАЗЛИЧНЫХ СТРАТЕГИЙ
        const strategies = [
            { name: 'Узкая (3 bins)', interval: 1, totalBins: 3 },
            { name: 'Оптимальная (5 bins)', interval: 2, totalBins: 5 },
            { name: 'Средняя (7 bins)', interval: 3, totalBins: 7 },
            { name: 'Широкая (11 bins)', interval: 5, totalBins: 11 },
            { name: 'Очень широкая (21 bin)', interval: 10, totalBins: 21 }
        ];
        
        console.log(`\n🎯 АНАЛИЗ СТРАТЕГИЙ:`);
        console.log('=' .repeat(80));
        
        strategies.forEach(strategy => {
            // Расчет ликвидности на bin
            const liquidityPerBin = liquidityToAdd / strategy.totalBins;
            
            // Расчет диапазона bins
            const minBinId = activeBinId - strategy.interval;
            const maxBinId = activeBinId + strategy.interval;
            
            // Примерный расчет влияния на цену (упрощенная модель)
            // В реальности зависит от текущей ликвидности в каждом bin
            const estimatedPriceImpact = (liquidityPerBin / 100000) * BIN_STEP; // Примерная формула
            const priceIncreasePercent = estimatedPriceImpact * 100;
            
            // Расчет проскальзывания при продаже
            const binsForSale = Math.ceil(strategy.totalBins * 0.6); // Примерно 60% bins для продажи
            const slippagePercent = (binsForSale * BIN_STEP) * 100;
            
            // Эффективность (прибыль - проскальзывание)
            const efficiency = priceIncreasePercent - slippagePercent;
            
            console.log(`\n📋 ${strategy.name}:`);
            console.log(`   Диапазон bins: ${minBinId} до ${maxBinId} (${strategy.totalBins} bins)`);
            console.log(`   Ликвидность на bin: $${liquidityPerBin.toLocaleString()}`);
            console.log(`   Примерный рост цены: ${priceIncreasePercent.toFixed(2)}%`);
            console.log(`   Проскальзывание: ${slippagePercent.toFixed(2)}%`);
            console.log(`   Эффективность: ${efficiency.toFixed(2)}%`);
            
            // Оценка риска
            let riskLevel = 'НИЗКИЙ';
            if (strategy.totalBins < 5) riskLevel = 'ВЫСОКИЙ';
            else if (strategy.totalBins < 10) riskLevel = 'СРЕДНИЙ';
            
            console.log(`   Уровень риска: ${riskLevel}`);
        });
        
        // 🔥 РЕКОМЕНДАЦИЯ
        console.log(`\n🔥 РЕКОМЕНДАЦИЯ:`);
        console.log('=' .repeat(50));
        
        // Для продажи 30% нужно покрыть примерно 5-7 bins
        const recommendedBins = 7;
        const recommendedInterval = Math.ceil(recommendedBins / 2);
        
        console.log(`✅ ОПТИМАЛЬНАЯ СТРАТЕГИЯ: ${recommendedBins} bins (±${recommendedInterval})`);
        console.log(`   Причины:`);
        console.log(`   1. Покрывает продажу 30% SOL с запасом`);
        console.log(`   2. Минимальное проскальзывание (~0.7%)`);
        console.log(`   3. Достаточная концентрация ($${(liquidityToAdd / recommendedBins).toLocaleString()} на bin)`);
        console.log(`   4. Умеренный риск выхода из диапазона`);
        
        // 🎯 ТОЧНЫЕ ПАРАМЕТРЫ
        const optimalMinBin = activeBinId - recommendedInterval;
        const optimalMaxBin = activeBinId + recommendedInterval;
        
        console.log(`\n🎯 ТОЧНЫЕ ПАРАМЕТРЫ ДЛЯ КОДА:`);
        console.log(`const TOTAL_RANGE_INTERVAL = ${recommendedInterval}; // ±${recommendedInterval} bins`);
        console.log(`// Диапазон: ${optimalMinBin} до ${optimalMaxBin}`);
        console.log(`// Активный bin: ${activeBinId}`);
        console.log(`// Всего bins: ${recommendedBins}`);
        
        // 🚨 ВАЖНЫЕ ЗАМЕЧАНИЯ
        console.log(`\n🚨 ВАЖНЫЕ ЗАМЕЧАНИЯ:`);
        console.log(`1. Расчеты приблизительные - точные значения зависят от текущей ликвидности`);
        console.log(`2. Реальное влияние на цену может отличаться`);
        console.log(`3. Рекомендуется тестирование на небольших суммах`);
        console.log(`4. Мониторинг позиции во время выполнения`);
        
        // 📊 ФИНАЛЬНАЯ ТАБЛИЦА
        console.log(`\n📊 ФИНАЛЬНАЯ ТАБЛИЦА СРАВНЕНИЯ:`);
        console.log('Bins | Ликв/bin | Рост% | Проск% | Эффект% | Риск');
        console.log('-'.repeat(55));
        strategies.forEach(s => {
            const liqPerBin = Math.round(liquidityToAdd / s.totalBins / 1000);
            const growth = ((liqPerBin / 100) * BIN_STEP * 100).toFixed(1);
            const slip = (s.totalBins * BIN_STEP * 100 * 0.6).toFixed(1);
            const eff = (growth - slip).toFixed(1);
            const risk = s.totalBins < 5 ? 'HIGH' : s.totalBins < 10 ? 'MED' : 'LOW';
            console.log(`${s.totalBins.toString().padStart(4)} | ${liqPerBin}K     | ${growth.padStart(4)} | ${slip.padStart(5)} | ${eff.padStart(6)} | ${risk}`);
        });
        
    } catch (error) {
        console.error(`❌ Ошибка: ${error.message}`);
        process.exit(1);
    }
}

calculateOptimalBins();
