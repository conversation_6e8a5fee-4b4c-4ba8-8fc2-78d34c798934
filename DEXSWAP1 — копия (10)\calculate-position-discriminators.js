#!/usr/bin/env node

/**
 * 🔥 ВЫЧИСЛЕНИЕ ОФИЦИАЛЬНОГО METEORA DISCRIMINATOR ДЛЯ initializePosition
 */

const crypto = require('crypto');

console.log('🔥 ОФИЦИАЛЬНЫЙ METEORA DISCRIMINATOR ДЛЯ initializePosition');
console.log('='.repeat(60));

// Функция для вычисления Anchor discriminator
function calculateAnchorDiscriminator(methodName) {
    const input = `global:${methodName}`;
    const hash = crypto.createHash('sha256').update(input).digest();
    const discriminator = hash.slice(0, 8);
    return discriminator;
}

// 🔥 ОФИЦИАЛЬНОЕ ИМЯ ИЗ IDL
const officialMethodName = 'initializePosition';
const discriminator = calculateAnchorDiscriminator(officialMethodName);

console.log(`📋 Метод: ${officialMethodName}`);
console.log(`🔧 Input: "global:${officialMethodName}"`);
console.log(`🎯 Discriminator: [${Array.from(discriminator).join(', ')}]`);
console.log(`🔢 Hex: ${discriminator.toString('hex')}`);
console.log(`📊 Bytes: ${JSON.stringify(Array.from(discriminator))}`);

// Проверим наш старый discriminator
const oldDiscriminator = [219, 192, 234, 71, 190, 191, 102, 80];
const newDiscriminator = Array.from(discriminator);

console.log('\n🔍 СРАВНЕНИЕ:');
console.log(`   Старый: [${oldDiscriminator.join(', ')}]`);
console.log(`   Новый:  [${newDiscriminator.join(', ')}]`);
console.log(`   Совпадает: ${JSON.stringify(oldDiscriminator) === JSON.stringify(newDiscriminator) ? '✅ ДА' : '❌ НЕТ'}`);

if (JSON.stringify(oldDiscriminator) !== JSON.stringify(newDiscriminator)) {
    console.log('\n🚨 DISCRIMINATOR НЕПРАВИЛЬНЫЙ! НУЖНО ОБНОВИТЬ!');
} else {
    console.log('\n✅ DISCRIMINATOR ПРАВИЛЬНЫЙ!');
}

// Возможные имена для initializePositionAndAddLiquidityByStrategy
const positionMethods = [
    'initialize_position',
    'initialize_position_and_add_liquidity',
    'initialize_position_and_add_liquidity_by_strategy',
    'add_liquidity_by_strategy',
    'add_liquidity_by_weight',
    'add_liquidity_one_side',
    'position_initialize',
    'position_add_liquidity',
    'create_position',
    'create_position_and_add_liquidity'
];

console.log('📊 РЕЗУЛЬТАТЫ ВЫЧИСЛЕНИЙ:');
console.log('='.repeat(80));

positionMethods.forEach(method => {
    const discriminator = calculateAnchorDiscriminator(method);
    const bytes = Array.from(discriminator);
    const hex = discriminator.toString('hex');
    
    console.log(`📋 Метод: ${method}`);
    console.log(`   Input: "global:${method}"`);
    console.log(`   Discriminator: [${bytes.join(', ')}]`);
    console.log(`   Hex: ${hex}`);
    console.log('');
});

console.log('✅ POSITION DISCRIMINATORS ГОТОВЫ!');
