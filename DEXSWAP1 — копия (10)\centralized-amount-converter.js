#!/usr/bin/env node

/**
 * 🔥 ЦЕНТРАЛИЗОВАННАЯ СИСТЕМА КОНВЕРТАЦИИ AMOUNTS
 * 
 * ЕДИНСТВЕННОЕ МЕСТО ДЛЯ ВСЕХ КОНВЕРТАЦИЙ В СИСТЕМЕ!
 * ВСЕ ОСТАЛЬНЫЕ МОДУЛИ ДОЛЖНЫ ИСПОЛЬЗОВАТЬ ТОЛЬКО ЭТОТ ФАЙЛ!
 * 
 * ОФИЦИАЛЬНАЯ ДОКУМЕНТАЦИЯ:
 * - Jupiter API: amount = raw amount in lamports/atomic units
 * - MarginFi SDK: amount = UI amount (human readable)
 * - Solana: lamports = smallest unit (1 SOL = 1,000,000,000 lamports)
 * - USDC: micro-units = smallest unit (1 USDC = 1,000,000 micro-units)
 */

console.log('🔥 ЦЕНТРАЛИЗОВАННАЯ СИСТЕМА КОНВЕРТАЦИИ ЗАГРУЖЕНА');

// 🔧 ОФИЦИАЛЬНАЯ КОНФИГУРАЦИЯ ТОКЕНОВ
const OFFICIAL_TOKEN_CONFIG = {
  'USDC': {
    decimals: 6,
    symbol: 'USDC',
    mint: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
    name: 'USD Coin',
    isStablecoin: true,
    verified: true
  },
  'USDT': {
    decimals: 6,
    symbol: 'USDT',
    mint: 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB',
    name: 'Tether USD',
    isStablecoin: true,
    verified: true
  },
  'SOL': {
    decimals: 9,
    symbol: 'SOL',
    mint: 'So11111111111111111111111111111111111111112',
    name: 'Solana',
    isStablecoin: false,
    verified: true
  },
  'WSOL': {
    decimals: 9,
    symbol: 'WSOL',
    mint: 'So11111111111111111111111111111111111111112',
    name: 'Wrapped SOL',
    isStablecoin: false,
    verified: true
  }
};

class CentralizedAmountConverter {
  constructor() {
    console.log('🔧 Централизованный конвертер amounts инициализирован');
    console.log('📋 Поддерживаемые токены:', Object.keys(OFFICIAL_TOKEN_CONFIG).join(', '));
  }

  /**
   * 💰 ФОРМАТИРОВАНИЕ СУММЫ В ДОЛЛАРАХ ДЛЯ ЛОГОВ
   * Конвертирует микроюниты в доллары для наглядного отображения
   */
  formatAmountInUSD(amount, tokenSymbol, realSolPrice = null) {
    try {
      const tokenInfo = OFFICIAL_TOKEN_CONFIG[tokenSymbol];
      if (!tokenInfo) {
        return `${amount} микроюнитов (неизвестный токен ${tokenSymbol})`;
      }

      const decimals = tokenInfo.decimals;

      if (tokenInfo.isStablecoin) {
        // Для стейблкоинов: микроюниты → USD напрямую
        const usdAmount = amount / Math.pow(10, decimals);
        return `$${usdAmount.toLocaleString()} (${amount.toLocaleString()} микроюнитов)`;
      } else if (tokenSymbol === 'SOL' || tokenSymbol === 'WSOL') {
        // Для SOL: lamports → SOL → USD
        const solAmount = amount / Math.pow(10, decimals);
        if (realSolPrice) {
          const usdAmount = solAmount * realSolPrice;
          return `$${usdAmount.toLocaleString()} (${solAmount.toFixed(4)} SOL, ${amount.toLocaleString()} lamports)`;
        } else {
          return `${solAmount.toFixed(4)} SOL (${amount.toLocaleString()} lamports)`;
        }
      } else {
        // Для других токенов
        const tokenAmount = amount / Math.pow(10, decimals);
        return `${tokenAmount.toFixed(4)} ${tokenSymbol} (${amount.toLocaleString()} микроюнитов)`;
      }
    } catch (error) {
      return `${amount} микроюнитов (ошибка форматирования: ${error.message})`;
    }
  }

  /**
   * 🔥 ГЛАВНАЯ ФУНКЦИЯ: USD → NATIVE AMOUNT (для Jupiter API)
   *
   * КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: ТОЧНАЯ КОНВЕРТАЦИЯ БЕЗ ОШИБОК ОКРУГЛЕНИЯ!
   *
   * ПРИМЕРЫ:
   * - $10,000 USDC → 10,000,000,000 lamports (ТОЧНО!)
   * - $1 USDC → 1,000,000 lamports
   * - $150 SOL → 1,000,000,000 lamports (1 SOL)
   */
  async convertUsdToNativeAmount(usdAmount, tokenSymbol, realSolPrice = null) {
    console.log(`\n🔥 КОНВЕРТАЦИЯ USD → NATIVE: $${usdAmount} ${tokenSymbol}`);

    // 🚨 КРИТИЧЕСКАЯ ПРОВЕРКА: ПРЕДОТВРАЩАЕМ ОГРОМНЫЕ СУММЫ!
    // ✅ ИСПРАВЛЕНО: Лимит соответствует flash loan арбитражу!
    if (usdAmount > 10000000) { // Больше $10M - МАКСИМАЛЬНАЯ ЗАЩИТА!
      console.log(`🚨 КРИТИЧЕСКАЯ ОШИБКА: Сумма слишком большая! $${usdAmount.toLocaleString()}`);
      console.log(`🚨 Максимально допустимая сумма: $10,000,000 (МАКСИМАЛЬНАЯ ЗАЩИТА)`);
      throw new Error(`КРИТИЧЕСКАЯ ОШИБКА: Сумма $${usdAmount.toLocaleString()} превышает максимально допустимую ($10,000,000)!`);
    }

    const tokenInfo = OFFICIAL_TOKEN_CONFIG[tokenSymbol];
    if (!tokenInfo) {
      throw new Error(`❌ НЕИЗВЕСТНЫЙ ТОКЕН: ${tokenSymbol}`);
    }

    const decimals = tokenInfo.decimals;

    if (tokenInfo.isStablecoin) {
      // 🔥 ОФИЦИАЛЬНАЯ ФОРМУЛА ИЗ QUICKNODE ДОКУМЕНТАЦИИ!
      // ИСТОЧНИК: https://www.quicknode.com/guides/solana-development/spl-tokens/how-to-transfer-spl-tokens-on-solana
      // ФОРМУЛА: TRANSFER_AMOUNT * Math.pow(10, numberDecimals)

      // Для стейблкоинов: $1 USD = 1 токен ТОЧНО
      // $10,000 USDC = 10,000 USDC = 10,000 * 10^6 = 10,000,000,000 микро-единиц

      // 🔥 ИСПОЛЬЗУЕМ ТОЧНУЮ ОФИЦИАЛЬНУЮ ФОРМУЛУ БЕЗ ОКРУГЛЕНИЯ!
      const nativeAmount = usdAmount * Math.pow(10, decimals);

      console.log(`💰 ${tokenSymbol} ОФИЦИАЛЬНАЯ КОНВЕРТАЦИЯ (QuickNode):`);
      console.log(`   $${usdAmount} USD → ${usdAmount} ${tokenSymbol} → ${nativeAmount.toLocaleString()} микро-единиц`);
      console.log(`   📚 ОФИЦИАЛЬНАЯ ФОРМУЛА: ${usdAmount} * Math.pow(10, ${decimals}) = ${nativeAmount}`);
      console.log(`   🔥 ИСТОЧНИК: QuickNode Solana SPL Token Transfer Guide`);

      // СТРОГАЯ ПРОВЕРКА ТОЧНОСТИ
      const backToUsd = nativeAmount / Math.pow(10, decimals);
      console.log(`   ✅ ПРОВЕРКА: ${nativeAmount} → $${backToUsd} (должно быть $${usdAmount})`);

      // 🔥 КРИТИЧЕСКАЯ ПРОВЕРКА: Для стейблкоинов должна быть АБСОЛЮТНАЯ точность!
      if (backToUsd !== usdAmount) {
        console.log(`❌ КРИТИЧЕСКАЯ ОШИБКА КОНВЕРТАЦИИ:`);
        console.log(`   Ожидается: $${usdAmount}`);
        console.log(`   Получено: $${backToUsd}`);
        console.log(`   Разница: $${Math.abs(backToUsd - usdAmount)}`);
        throw new Error(`❌ ОШИБКА КОНВЕРТАЦИИ: ${backToUsd} ≠ ${usdAmount}`);
      }

      return nativeAmount;

    } else if (tokenSymbol === 'SOL' || tokenSymbol === 'WSOL') {
      // SOL: ЦЕНА ДОЛЖНА ПЕРЕДАВАТЬСЯ ИЗ РЕАЛЬНЫХ ПУЛОВ!
      if (!realSolPrice) {
        throw new Error(`❌ ЦЕНА SOL НЕ ПЕРЕДАНА! Используйте: convertUsdToNativeAmount(${usdAmount}, '${tokenSymbol}', РЕАЛЬНАЯ_ЦЕНА_ИЗ_ПУЛОВ)`);
      }

      const solAmount = usdAmount / realSolPrice;
      const nativeAmount = Math.floor(solAmount * Math.pow(10, decimals));

      console.log(`💰 ${tokenSymbol} КОНВЕРТАЦИЯ С РЕАЛЬНОЙ ЦЕНОЙ:`);
      console.log(`   $${usdAmount} USD → ${solAmount.toFixed(6)} SOL → ${nativeAmount.toLocaleString()} lamports`);
      console.log(`   РЕАЛЬНАЯ ЦЕНА SOL: $${realSolPrice} (ИЗ ПУЛОВ!)`);
      console.log(`   Формула: (${usdAmount} / ${realSolPrice}) * 10^${decimals} = ${nativeAmount}`);

      return nativeAmount;
    }

    throw new Error(`❌ НЕПОДДЕРЖИВАЕМЫЙ ТОКЕН: ${tokenSymbol}`);
  }

  /**
   * 🔥 ОБРАТНАЯ ФУНКЦИЯ: NATIVE AMOUNT → USD (для отчетов)
   *
   * КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: ТОЧНАЯ ОБРАТНАЯ КОНВЕРТАЦИЯ БЕЗ ОШИБОК!
   */
  convertNativeToUsdAmount(nativeAmount, tokenSymbol) {
    console.log(`\n🔄 КОНВЕРТАЦИЯ NATIVE → USD: ${nativeAmount.toLocaleString()} ${tokenSymbol}`);

    const tokenInfo = OFFICIAL_TOKEN_CONFIG[tokenSymbol];
    if (!tokenInfo) {
      throw new Error(`❌ НЕИЗВЕСТНЫЙ ТОКЕН: ${tokenSymbol}`);
    }

    const decimals = tokenInfo.decimals;

    if (tokenInfo.isStablecoin) {
      // 🔥 ОФИЦИАЛЬНАЯ ОБРАТНАЯ ФОРМУЛА ИЗ QUICKNODE ДОКУМЕНТАЦИИ!
      // ИСТОЧНИК: https://www.quicknode.com/guides/solana-development/spl-tokens/how-to-transfer-spl-tokens-on-solana
      // ОБРАТНАЯ ФОРМУЛА: nativeAmount / Math.pow(10, decimals)

      const usdAmount = nativeAmount / Math.pow(10, decimals);

      console.log(`💰 ${tokenSymbol} ОФИЦИАЛЬНАЯ ОБРАТНАЯ КОНВЕРТАЦИЯ (QuickNode):`);
      console.log(`   ${nativeAmount.toLocaleString()} микро-единиц → ${usdAmount} ${tokenSymbol} → $${usdAmount} USD`);
      console.log(`   📚 ОФИЦИАЛЬНАЯ ФОРМУЛА: ${nativeAmount} / Math.pow(10, ${decimals}) = ${usdAmount}`);
      console.log(`   🔥 ИСТОЧНИК: QuickNode Solana SPL Token Transfer Guide`);

      // 🔥 ПРОВЕРКА ТОЧНОСТИ: Обратная конвертация должна быть точной
      const backToNative = usdAmount * Math.pow(10, decimals);
      if (backToNative !== nativeAmount) {
        console.log(`⚠️ ПРЕДУПРЕЖДЕНИЕ: Потеря точности при обратной конвертации:`);
        console.log(`   Исходный native: ${nativeAmount}`);
        console.log(`   Обратно в native: ${backToNative}`);
        console.log(`   Разница: ${Math.abs(backToNative - nativeAmount)}`);
        console.log(`   🔥 Это может быть причиной ошибки в flash loan!`);
      }

      return usdAmount;

    } else if (tokenSymbol === 'SOL' || tokenSymbol === 'WSOL') {
      // SOL: ЦЕНА ДОЛЖНА ПЕРЕДАВАТЬСЯ ИЗ РЕАЛЬНЫХ ПУЛОВ!
      throw new Error(`❌ ЦЕНА SOL НЕ ПЕРЕДАНА! Используйте: convertNativeToUsdAmount(${nativeAmount}, '${tokenSymbol}', РЕАЛЬНАЯ_ЦЕНА_ИЗ_ПУЛОВ)`);
    }

    throw new Error(`❌ НЕПОДДЕРЖИВАЕМЫЙ ТОКЕН: ${tokenSymbol}`);
  }

  /**
   * 🔥 СПЕЦИАЛЬНАЯ ФУНКЦИЯ: USD → UI AMOUNT (для MarginFi SDK)
   * MarginFi SDK ожидает "человеко-читаемые" числа, не lamports!
   *
   * КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: ТОЧНАЯ UI КОНВЕРТАЦИЯ ДЛЯ MARGINFI!
   */
  convertUsdToUiAmount(usdAmount, tokenSymbol) {
    console.log(`\n🏦 КОНВЕРТАЦИЯ USD → UI (для MarginFi): $${usdAmount} ${tokenSymbol}`);

    const tokenInfo = OFFICIAL_TOKEN_CONFIG[tokenSymbol];
    if (!tokenInfo) {
      throw new Error(`❌ НЕИЗВЕСТНЫЙ ТОКЕН: ${tokenSymbol}`);
    }

    if (tokenInfo.isStablecoin) {
      // 🔥 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: ТОЧНАЯ UI КОНВЕРТАЦИЯ ДЛЯ СТЕЙБЛКОИНОВ!
      // СТЕЙБЛКОИНЫ: $1 USD = 1 UI токен (ТОЧНО!)
      // $10,000 USD = 10,000 UI USDC (без потери точности)

      const uiAmount = usdAmount; // Прямое соответствие для стейблкоинов

      console.log(`🏦 ${tokenSymbol} ТОЧНАЯ UI КОНВЕРТАЦИЯ:`);
      console.log(`   $${usdAmount} USD → ${uiAmount} UI ${tokenSymbol}`);
      console.log(`   ✅ MarginFi SDK получит: ${uiAmount}`);
      console.log(`   🔥 ИСПРАВЛЕНО: Точное соответствие для стейблкоинов!`);

      // ПРОВЕРКА ТОЧНОСТИ
      if (uiAmount !== usdAmount) {
        throw new Error(`❌ ОШИБКА UI КОНВЕРТАЦИИ: ${uiAmount} ≠ ${usdAmount}`);
      }

      return uiAmount;

    } else if (tokenSymbol === 'SOL' || tokenSymbol === 'WSOL') {
      // SOL: ЦЕНА ДОЛЖНА ПЕРЕДАВАТЬСЯ ИЗ РЕАЛЬНЫХ ПУЛОВ!
      throw new Error(`❌ ЦЕНА SOL НЕ ПЕРЕДАНА! Используйте: convertUsdToUiAmount(${usdAmount}, '${tokenSymbol}', РЕАЛЬНАЯ_ЦЕНА_ИЗ_ПУЛОВ)`);
    }

    throw new Error(`❌ НЕПОДДЕРЖИВАЕМЫЙ ТОКЕН: ${tokenSymbol}`);
  }

  /**
   * 🔥 ВСПОМОГАТЕЛЬНАЯ ФУНКЦИЯ: UI AMOUNT → NATIVE AMOUNT
   */
  convertUiToNativeAmount(uiAmount, tokenSymbol) {
    const tokenInfo = OFFICIAL_TOKEN_CONFIG[tokenSymbol];
    if (!tokenInfo) {
      throw new Error(`❌ НЕИЗВЕСТНЫЙ ТОКЕН: ${tokenSymbol}`);
    }

    const decimals = tokenInfo.decimals;
    const nativeAmount = Math.floor(uiAmount * Math.pow(10, decimals));
    
    console.log(`🔧 UI → NATIVE: ${uiAmount} ${tokenSymbol} → ${nativeAmount.toLocaleString()} atomic units`);
    
    return nativeAmount;
  }

  /**
   * 🔥 ВСПОМОГАТЕЛЬНАЯ ФУНКЦИЯ: NATIVE AMOUNT → UI AMOUNT
   */
  convertNativeToUiAmount(nativeAmount, tokenSymbol) {
    const tokenInfo = OFFICIAL_TOKEN_CONFIG[tokenSymbol];
    if (!tokenInfo) {
      throw new Error(`❌ НЕИЗВЕСТНЫЙ ТОКЕН: ${tokenSymbol}`);
    }

    const decimals = tokenInfo.decimals;
    const uiAmount = nativeAmount / Math.pow(10, decimals);
    
    console.log(`🔧 NATIVE → UI: ${nativeAmount.toLocaleString()} atomic units → ${uiAmount} ${tokenSymbol}`);
    
    return uiAmount;
  }

  /**
   * 📊 ПОЛУЧЕНИЕ ИНФОРМАЦИИ О ТОКЕНЕ
   */
  getTokenInfo(tokenSymbol) {
    return OFFICIAL_TOKEN_CONFIG[tokenSymbol] || null;
  }

  /**
   * 📋 СПИСОК ПОДДЕРЖИВАЕМЫХ ТОКЕНОВ
   */
  getSupportedTokens() {
    return Object.keys(OFFICIAL_TOKEN_CONFIG);
  }

  /**
   * 💰 ПОЛУЧЕНИЕ РЕАЛЬНОЙ ЦЕНЫ ТОКЕНА
   */
  async getTokenPrice(tokenSymbol) {
    try {
      console.log(`🔍 Получение реальной цены ${tokenSymbol}...`);

      if (tokenSymbol === 'SOL') {
        // Получаем цену SOL из Jupiter API
        const response = await fetch('https://price.jup.ag/v4/price?ids=SOL');
        const data = await response.json();

        if (data && data.data && data.data.SOL && data.data.SOL.price) {
          const solPrice = parseFloat(data.data.SOL.price);
          console.log(`✅ Получена реальная цена SOL: $${solPrice}`);
          return solPrice;
        } else {
          throw new Error('Не удалось получить цену SOL из Jupiter API');
        }
      } else if (tokenSymbol === 'USDC') {
        // USDC всегда $1
        return 1.0;
      } else {
        throw new Error(`Неподдерживаемый токен для получения цены: ${tokenSymbol}`);
      }

    } catch (error) {
      console.error(`❌ Ошибка получения цены ${tokenSymbol}:`, error.message);
      throw new Error(`Невозможно получить реальную цену ${tokenSymbol}: ${error.message}`);
    }
  }

  /**
   * 🧪 ТЕСТИРОВАНИЕ КОНВЕРТАЦИИ
   */
  testConversions() {
    console.log('\n🧪 ТЕСТИРОВАНИЕ ЦЕНТРАЛИЗОВАННОЙ КОНВЕРТАЦИИ');
    console.log('═'.repeat(60));

    const testCases = [
      { usd: 10000, token: 'USDC' },
      { usd: 1, token: 'USDC' },
      { usd: 0.08, token: 'USDC' },
      { usd: 150, token: 'SOL' },
      { usd: 1500, token: 'SOL' }
    ];

    testCases.forEach((test, index) => {
      console.log(`\n📋 ТЕСТ ${index + 1}: $${test.usd} ${test.token}`);
      try {
        const native = this.convertUsdToNativeAmount(test.usd, test.token);
        const backToUsd = this.convertNativeToUsdAmount(native, test.token);
        const uiAmount = this.convertUsdToUiAmount(test.usd, test.token);
        
        console.log(`✅ РЕЗУЛЬТАТ:`);
        console.log(`   Native: ${native.toLocaleString()}`);
        console.log(`   Back to USD: $${backToUsd}`);
        console.log(`   UI Amount: ${uiAmount}`);
        console.log(`   Точность: ${Math.abs(backToUsd - test.usd) < 0.000001 ? '✅' : '❌'}`);
        
      } catch (error) {
        console.log(`❌ ОШИБКА: ${error.message}`);
      }
    });
  }
}

// 🔥 СОЗДАЕМ ГЛОБАЛЬНЫЙ ЭКЗЕМПЛЯР
const centralizedConverter = new CentralizedAmountConverter();

// 🔥 ЭКСПОРТИРУЕМ ВСЕ ФУНКЦИИ
module.exports = {
  // Основные функции конвертации
  convertUsdToNativeAmount: (usdAmount, tokenSymbol, realSolPrice = null) => centralizedConverter.convertUsdToNativeAmount(usdAmount, tokenSymbol, realSolPrice),
  convertNativeToUsdAmount: (nativeAmount, tokenSymbol) => centralizedConverter.convertNativeToUsdAmount(nativeAmount, tokenSymbol),
  convertUsdToUiAmount: (usdAmount, tokenSymbol) => centralizedConverter.convertUsdToUiAmount(usdAmount, tokenSymbol),
  convertUiToNativeAmount: (uiAmount, tokenSymbol) => centralizedConverter.convertUiToNativeAmount(uiAmount, tokenSymbol),
  convertNativeToUiAmount: (nativeAmount, tokenSymbol) => centralizedConverter.convertNativeToUiAmount(nativeAmount, tokenSymbol),
  
  // Вспомогательные функции
  getTokenInfo: (tokenSymbol) => centralizedConverter.getTokenInfo(tokenSymbol),
  getSupportedTokens: () => centralizedConverter.getSupportedTokens(),
  getTokenPrice: (tokenSymbol) => centralizedConverter.getTokenPrice(tokenSymbol),
  testConversions: () => centralizedConverter.testConversions(),
  formatAmountInUSD: (amount, tokenSymbol, realSolPrice = null) => centralizedConverter.formatAmountInUSD(amount, tokenSymbol, realSolPrice),
  
  // Конфигурация
  OFFICIAL_TOKEN_CONFIG,
  
  // Экземпляр класса
  centralizedConverter
};

console.log('✅ ЦЕНТРАЛИЗОВАННАЯ СИСТЕМА КОНВЕРТАЦИИ ГОТОВА К ИСПОЛЬЗОВАНИЮ!');
