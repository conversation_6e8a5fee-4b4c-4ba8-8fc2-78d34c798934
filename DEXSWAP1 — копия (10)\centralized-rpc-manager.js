/**
 * 🌐 ЦЕНТРАЛИЗОВАННЫЙ RPC МЕНЕДЖЕР
 * Единая система управления RPC подключениями с fallback и retry логикой
 */

const { Connection } = require('@solana/web3.js');

class CentralizedRPCManager {
    constructor() {
        // 🔥 ПРИОРИТЕТНЫЕ RPC ENDPOINTS (QUICKNODE ПЕРВЫЙ!)
        this.rpcEndpoints = [
            // QuickNode (платный, высокая скорость)
            {
                url: process.env.QUICKNODE_RPC_URL,
                name: 'QuickNode Primary',
                priority: 1,
                maxRequestsPerSecond: 1000,
                timeout: 5000
            },
            {
                url: process.env.QUICKNODE2_RPC_URL,
                name: 'QuickNode Backup 2',
                priority: 2,
                maxRequestsPerSecond: 1000,
                timeout: 5000
            },
            {
                url: process.env.QUICKNODE3_RPC_URL,
                name: 'QuickNode Backup 3',
                priority: 3,
                maxRequestsPerSecond: 1000,
                timeout: 5000
            },
            {
                url: process.env.HELIUS_RPC_URL,
                name: '<PERSON><PERSON> RPC',
                priority: 4,
                maxRequestsPerSecond: 500,
                timeout: 8000
            },
            // Бесплатные endpoints (fallback)
            {
                url: 'https://api.mainnet-beta.solana.com',
                name: 'Solana Official',
                priority: 5,
                maxRequestsPerSecond: 100,
                timeout: 10000
            },
            {
                url: 'https://rpc.ankr.com/solana',
                name: 'Ankr RPC',
                priority: 6,
                maxRequestsPerSecond: 100,
                timeout: 10000
            },
            {
                url: 'https://solana-api.projectserum.com',
                name: 'Project Serum',
                priority: 7,
                maxRequestsPerSecond: 50,
                timeout: 15000
            },
            {
                url: 'https://solana.public-rpc.com',
                name: 'Public RPC',
                priority: 8,
                maxRequestsPerSecond: 50,
                timeout: 15000
            }
        ].filter(endpoint => endpoint.url); // Убираем undefined endpoints

        this.currentEndpointIndex = 0;
        this.connection = null;
        this.connectionStats = new Map();
        this.lastHealthCheck = 0;
        this.healthCheckInterval = 30000; // 30 секунд

        // Инициализируем статистику
        this.rpcEndpoints.forEach(endpoint => {
            this.connectionStats.set(endpoint.url, {
                successCount: 0,
                errorCount: 0,
                lastError: null,
                lastSuccess: null,
                isHealthy: true,
                averageResponseTime: 0
            });
        });

        // RPC Manager инициализирован тихо
    }

    /**
     * 🔗 ПОЛУЧЕНИЕ АКТИВНОГО ПОДКЛЮЧЕНИЯ С АВТОМАТИЧЕСКИМ FALLBACK
     */
    async getConnection() {
        // Проверяем здоровье текущего подключения
        if (this.connection && await this.isConnectionHealthy()) {
            return this.connection;
        }

        // Пытаемся подключиться к лучшему доступному endpoint
        for (let attempt = 0; attempt < this.rpcEndpoints.length; attempt++) {
            const endpoint = this.rpcEndpoints[this.currentEndpointIndex];

            try {
                const connection = new Connection(endpoint.url, {
                    commitment: 'confirmed',
                    confirmTransactionInitialTimeout: endpoint.timeout,
                    disableRetryOnRateLimit: false,
                    maxSupportedTransactionVersion: 0
                });

                // Быстрая проверка подключения
                const startTime = Date.now();
                await connection.getSlot();
                const responseTime = Date.now() - startTime;

                // Обновляем статистику
                const stats = this.connectionStats.get(endpoint.url);
                stats.successCount++;
                stats.lastSuccess = Date.now();
                stats.averageResponseTime = (stats.averageResponseTime + responseTime) / 2;
                stats.isHealthy = true;

                this.connection = connection;
                return connection;

            } catch (error) {
                // Обновляем статистику ошибок
                const stats = this.connectionStats.get(endpoint.url);
                stats.errorCount++;
                stats.lastError = error.message;
                stats.isHealthy = false;

                // Переходим к следующему endpoint
                this.currentEndpointIndex = (this.currentEndpointIndex + 1) % this.rpcEndpoints.length;
            }
        }

        throw new Error('Не удалось подключиться ни к одному RPC endpoint');
    }

    /**
     * 🏥 ПРОВЕРКА ЗДОРОВЬЯ ПОДКЛЮЧЕНИЯ
     */
    async isConnectionHealthy() {
        if (!this.connection) return false;

        try {
            const startTime = Date.now();
            await this.connection.getSlot();
            const responseTime = Date.now() - startTime;
            
            // Считаем подключение здоровым если ответ быстрый
            return responseTime < 5000;
        } catch (error) {
            console.log(`⚠️ Подключение нездорово: ${error.message}`);
            return false;
        }
    }

    /**
     * 🔄 ВЫПОЛНЕНИЕ RPC ЗАПРОСА С RETRY ЛОГИКОЙ
     */
    async executeWithRetry(operation, maxRetries = 3, delayMs = 2000) {
        let lastError;

        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                const connection = await this.getConnection();
                const result = await operation(connection);
                
                // Успешный запрос - обновляем статистику
                const currentEndpoint = this.rpcEndpoints[this.currentEndpointIndex];
                const stats = this.connectionStats.get(currentEndpoint.url);
                stats.successCount++;
                stats.lastSuccess = Date.now();
                
                return result;

            } catch (error) {
                lastError = error;
                console.log(`❌ RPC запрос провалился (попытка ${attempt}/${maxRetries}): ${error.message}`);

                // Обновляем статистику ошибок
                const currentEndpoint = this.rpcEndpoints[this.currentEndpointIndex];
                const stats = this.connectionStats.get(currentEndpoint.url);
                stats.errorCount++;
                stats.lastError = error.message;

                if (attempt < maxRetries) {
                    console.log(`🔄 Повторная попытка через ${delayMs}ms...`);
                    await new Promise(resolve => setTimeout(resolve, delayMs));
                    
                    // Переключаемся на следующий endpoint для retry
                    this.currentEndpointIndex = (this.currentEndpointIndex + 1) % this.rpcEndpoints.length;
                    this.connection = null; // Сбрасываем подключение для пересоздания
                }
            }
        }

        throw new Error(`RPC запрос провалился после ${maxRetries} попыток: ${lastError.message}`);
    }

    /**
     * 📊 ПОЛУЧЕНИЕ СТАТИСТИКИ RPC
     */
    getStats() {
        const stats = {
            currentEndpoint: this.rpcEndpoints[this.currentEndpointIndex],
            totalEndpoints: this.rpcEndpoints.length,
            healthyEndpoints: 0,
            endpoints: []
        };

        this.rpcEndpoints.forEach(endpoint => {
            const endpointStats = this.connectionStats.get(endpoint.url);
            const endpointInfo = {
                name: endpoint.name,
                url: endpoint.url.slice(0, 50) + '...',
                priority: endpoint.priority,
                isHealthy: endpointStats.isHealthy,
                successCount: endpointStats.successCount,
                errorCount: endpointStats.errorCount,
                averageResponseTime: Math.round(endpointStats.averageResponseTime),
                lastSuccess: endpointStats.lastSuccess ? new Date(endpointStats.lastSuccess).toISOString() : null,
                lastError: endpointStats.lastError
            };

            if (endpointStats.isHealthy) {
                stats.healthyEndpoints++;
            }

            stats.endpoints.push(endpointInfo);
        });

        return stats;
    }

    /**
     * 🔧 ПРИНУДИТЕЛЬНОЕ ПЕРЕКЛЮЧЕНИЕ НА СЛЕДУЮЩИЙ ENDPOINT
     */
    switchToNextEndpoint() {
        this.currentEndpointIndex = (this.currentEndpointIndex + 1) % this.rpcEndpoints.length;
        this.connection = null;
        console.log(`🔄 Переключение на: ${this.rpcEndpoints[this.currentEndpointIndex].name}`);
    }

    /**
     * 🔥 ПОЛУЧЕНИЕ LATEST BLOCKHASH ЧЕРЕЗ ЦЕНТРАЛИЗОВАННЫЙ RPC МЕНЕДЖЕР
     */
    async getLatestBlockhash(commitment = 'finalized') {
        const connection = await this.getConnection();
        if (!connection) {
            throw new Error('Нет доступного RPC подключения для получения blockhash');
        }

        try {
            const result = await connection.getLatestBlockhash(commitment);
            return result;
        } catch (error) {
            // Пробуем следующий endpoint
            this.switchToNextEndpoint();
            const nextConnection = await this.getConnection();
            if (nextConnection) {
                return await nextConnection.getLatestBlockhash(commitment);
            }

            throw new Error(`Не удалось получить blockhash ни через один RPC endpoint: ${error.message}`);
        }
    }

    /**
     * 📈 ВЫВОД СТАТИСТИКИ В КОНСОЛЬ
     */
    logStats() {
        const stats = this.getStats();
        console.log(`\n📊 RPC СТАТИСТИКА:`);
        console.log(`   🎯 Текущий: ${stats.currentEndpoint.name}`);
        console.log(`   ✅ Здоровых: ${stats.healthyEndpoints}/${stats.totalEndpoints}`);

        stats.endpoints.forEach((endpoint, index) => {
            const status = endpoint.isHealthy ? '✅' : '❌';
            const successRate = endpoint.successCount + endpoint.errorCount > 0
                ? Math.round((endpoint.successCount / (endpoint.successCount + endpoint.errorCount)) * 100)
                : 0;

            console.log(`   ${status} ${endpoint.name}: ${successRate}% успех, ${endpoint.averageResponseTime}ms`);
        });
    }
}

// Создаем глобальный экземпляр
const globalRPCManager = new CentralizedRPCManager();

module.exports = {
    CentralizedRPCManager,
    globalRPCManager
};
