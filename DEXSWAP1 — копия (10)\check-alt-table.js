/**
 * 🔍 ПРОВЕРКА КАСТОМНОЙ ALT ТАБЛИЦЫ НА ДУБЛИКАТЫ
 * Анализируем что именно содержится в таблице после добавления
 */

const { Connection, PublicKey } = require('@solana/web3.js');
require('dotenv').config({ path: '.env.solana' });

async function checkALTTable() {
    console.log('🔍 ПРОВЕРКА КАСТОМНОЙ ALT ТАБЛИЦЫ НА ДУБЛИКАТЫ');
    console.log('=' .repeat(80));

    try {
        // 1. Подключение к RPC
        const connection = new Connection(process.env.QUICKNODE_RPC_URL);
        console.log('✅ Подключение к RPC установлено');

        // 2. Кастомная ALT таблица
        const customALTAddress = new PublicKey('FAeyUf4AdG7vTQ2Q89r9hWBkPdYkykJKG4CxgqArBGXe');
        console.log(`📋 Анализ кастомной ALT: ${customALTAddress.toString()}`);

        // 3. Загружаем ALT таблицу
        const altAccount = await connection.getAddressLookupTable(customALTAddress);
        
        if (!altAccount || !altAccount.value) {
            throw new Error('Кастомная ALT таблица не найдена в блокчейне!');
        }

        const addresses = altAccount.value.state.addresses.map(addr => addr.toString());
        console.log(`📊 Всего адресов в ALT таблице: ${addresses.length}`);

        // 4. Проверяем на дубликаты
        const uniqueAddresses = new Set(addresses);
        const duplicateCount = addresses.length - uniqueAddresses.size;
        
        console.log(`\n📊 АНАЛИЗ ДУБЛИКАТОВ:`);
        console.log(`   Всего адресов: ${addresses.length}`);
        console.log(`   Уникальных адресов: ${uniqueAddresses.size}`);
        console.log(`   Дубликатов: ${duplicateCount}`);

        if (duplicateCount > 0) {
            console.log(`🚨 НАЙДЕНЫ ДУБЛИКАТЫ! ${duplicateCount} повторяющихся адресов`);
            
            // Находим дубликаты
            const addressCounts = {};
            addresses.forEach(addr => {
                addressCounts[addr] = (addressCounts[addr] || 0) + 1;
            });

            console.log(`\n📋 ДУБЛИРУЮЩИЕСЯ АДРЕСА:`);
            Object.entries(addressCounts).forEach(([addr, count]) => {
                if (count > 1) {
                    console.log(`   ${addr} - встречается ${count} раз`);
                }
            });
        } else {
            console.log(`✅ Дубликатов не найдено - все адреса уникальны`);
        }

        // 5. Показываем все адреса
        console.log(`\n📋 ВСЕ АДРЕСА В ALT ТАБЛИЦЕ (${addresses.length}):`);
        addresses.forEach((addr, i) => {
            console.log(`   ${(i + 1).toString().padStart(2)}. ${addr}`);
        });

        // 6. Анализируем типы адресов
        console.log(`\n🔍 АНАЛИЗ ТИПОВ АДРЕСОВ:`);
        
        const meteoraPools = [
            'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y', // Pool 1
            '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6', // Pool 2
            'HTvjzsfX3yU6BUodCjZ5vZkUrAxMDTrBs3CJaq43ashR'  // Pool 3
        ];

        const systemAddresses = [
            'So11111111111111111111111111111111111111112', // SOL
            'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // USDC
            'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA', // Token Program
            '11111111111111111111111111111111', // System Program
            'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo', // Meteora Program
            'MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr', // Memo Program
            'ComputeBudget111111111111111111111111111111', // Compute Budget
            'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL' // Associated Token Program
        ];

        let poolCount = 0;
        let systemCount = 0;
        let binArrayCount = 0;
        let unknownCount = 0;

        addresses.forEach(addr => {
            if (meteoraPools.includes(addr)) {
                poolCount++;
            } else if (systemAddresses.includes(addr)) {
                systemCount++;
            } else if (addr.length === 44) {
                // Предположительно bin array или reserve
                binArrayCount++;
            } else {
                unknownCount++;
            }
        });

        console.log(`   🎯 Meteora Pool адреса: ${poolCount}`);
        console.log(`   🔧 Системные адреса: ${systemCount}`);
        console.log(`   📦 Bin Arrays/Reserves: ${binArrayCount}`);
        console.log(`   ❓ Неизвестные адреса: ${unknownCount}`);

        // 7. Проверяем покрытие наших пулов
        console.log(`\n🎯 ПОКРЫТИЕ METEORA ПУЛОВ:`);
        meteoraPools.forEach((pool, i) => {
            const found = addresses.includes(pool);
            console.log(`   Pool ${i + 1} (${pool}): ${found ? '✅ ЕСТЬ' : '❌ НЕТ'}`);
        });

        console.log(`\n${'='.repeat(80)}`);
        console.log('🎯 РЕЗУЛЬТАТ ПРОВЕРКИ:');
        console.log(`📊 Всего адресов: ${addresses.length}`);
        console.log(`📊 Уникальных адресов: ${uniqueAddresses.size}`);
        console.log(`📊 Дубликатов: ${duplicateCount}`);
        console.log(`🎯 Meteora пулов: ${poolCount}/3`);
        console.log(`${'='.repeat(80)}`);

    } catch (error) {
        console.error('❌ Ошибка проверки ALT таблицы:', error.message);
        console.error(error.stack);
    }
}

// Запуск проверки
if (require.main === module) {
    checkALTTable();
}

module.exports = { checkALTTable };
