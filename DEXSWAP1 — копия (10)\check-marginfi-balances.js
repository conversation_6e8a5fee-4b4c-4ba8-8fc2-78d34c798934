/**
 * 🔍 ПРОВЕРКА РЕАЛЬНЫХ MARGINFI BALANCES
 * 
 * Проверяем какие lending balances есть в MarginFi аккаунте
 */

const { Connection, PublicKey } = require('@solana/web3.js');

class MarginFiBalanceChecker {
    constructor() {
        this.connection = new Connection('https://billowing-empty-patron.solana-mainnet.quiknode.pro/c79212a7508ec6a37353793d9be93c917898e858/', 'confirmed');
        
        // 🔥 MARGINFI КОНСТАНТЫ
        this.MARGINFI_ACCOUNT = new PublicKey('3dPECtRBPcYtcJG6wWDCr7VCyzxttDTjgGgo9gWC9vmU');
        
        // 🏦 ИЗВЕСТНЫЕ БАНКИ
        this.BANKS = {
            USDC: new PublicKey('2s37akK2eyBbp8DZgCm7RtsaEz8eJP3Nxd4urLHQv7yB'),
            SOL: new PublicKey('CCKtUs6Cgwo4aaQUmBPmyoApH2gUDErxNZCAntD6LYGh'),
            USDT: new PublicKey('BFBVrMmJJLdVKdqyFQzjQFcRXEqhTBNHXaFBtYeURAhz'),
        };
        
        // 🔧 ИЗВЕСТНЫЕ MINTS
        this.MINTS = {
            USDC: new PublicKey('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'),
            SOL: new PublicKey('So11111111111111111111111111111111111111112'),
            USDT: new PublicKey('Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB'),
        };
    }

    /**
     * 🔍 ДЕТАЛЬНЫЙ АНАЛИЗ MARGINFI АККАУНТА
     */
    async analyzeMarginFiAccount() {
        try {
            console.log('🔍 ДЕТАЛЬНЫЙ АНАЛИЗ MARGINFI АККАУНТА');
            console.log('═══════════════════════════════════════════════════════════════');
            
            // Получаем данные аккаунта
            const accountInfo = await this.connection.getAccountInfo(this.MARGINFI_ACCOUNT);
            if (!accountInfo) {
                console.log('❌ MarginFi аккаунт не найден');
                return false;
            }
            
            console.log('✅ MarginFi аккаунт найден');
            console.log(`📏 Размер данных: ${accountInfo.data.length} bytes`);
            console.log(`👤 Владелец: ${accountInfo.owner.toString()}`);
            
            // Анализируем структуру данных
            const data = accountInfo.data;
            console.log('\n📊 АНАЛИЗ СТРУКТУРЫ MARGINFI АККАУНТА:');
            
            // MarginFi аккаунт структура:
            // 8 bytes - discriminator
            // 32 bytes - group
            // 32 bytes - authority  
            // 1 byte - account_seed
            // 1 byte - bump
            // остальное - balances array
            
            const discriminator = data.slice(0, 8);
            const group = new PublicKey(data.slice(8, 40));
            const authority = new PublicKey(data.slice(40, 72));
            const accountSeed = data[72];
            const bump = data[73];
            
            console.log(`🔧 Discriminator: ${Array.from(discriminator).map(b => b.toString(16).padStart(2, '0')).join('')}`);
            console.log(`🏦 Group: ${group.toString()}`);
            console.log(`👤 Authority: ${authority.toString()}`);
            console.log(`🌱 Account Seed: ${accountSeed}`);
            console.log(`📈 Bump: ${bump}`);
            
            // Анализируем balances
            const balancesStart = 74; // После header
            const balancesData = data.slice(balancesStart);
            
            console.log(`\n📊 АНАЛИЗ BALANCES:`);
            console.log(`📏 Размер данных balances: ${balancesData.length} bytes`);
            
            // Каждый balance занимает 184 байта
            const BALANCE_SIZE = 184;
            const maxBalances = Math.floor(balancesData.length / BALANCE_SIZE);
            
            console.log(`📊 Максимум balances: ${maxBalances}`);
            
            let activeBalances = 0;
            const foundBalances = [];
            
            for (let i = 0; i < maxBalances; i++) {
                const balanceOffset = i * BALANCE_SIZE;
                if (balanceOffset + BALANCE_SIZE <= balancesData.length) {
                    
                    // Структура balance:
                    // 1 byte - active flag
                    // 32 bytes - bank pubkey
                    // остальное - amounts и другие данные
                    
                    const isActive = balancesData[balanceOffset] === 1;
                    
                    if (isActive) {
                        activeBalances++;
                        
                        const bankOffset = balanceOffset + 8; // После флагов
                        const bankBytes = balancesData.slice(bankOffset, bankOffset + 32);
                        const bankPubkey = new PublicKey(bankBytes);
                        
                        // Получаем amounts
                        const assetSharesOffset = bankOffset + 32;
                        const liabilitySharesOffset = assetSharesOffset + 16;
                        
                        const assetShares = balancesData.readBigUInt64LE(assetSharesOffset);
                        const liabilityShares = balancesData.readBigUInt64LE(liabilitySharesOffset);
                        
                        console.log(`\n   ✅ Balance ${i + 1}: АКТИВЕН`);
                        console.log(`      🏦 Банк: ${bankPubkey.toString()}`);
                        console.log(`      💰 Asset Shares: ${assetShares.toString()}`);
                        console.log(`      💸 Liability Shares: ${liabilityShares.toString()}`);
                        
                        // Определяем токен по банку
                        let tokenName = 'UNKNOWN';
                        for (const [name, bankAddr] of Object.entries(this.BANKS)) {
                            if (bankAddr.equals(bankPubkey)) {
                                tokenName = name;
                                break;
                            }
                        }
                        
                        console.log(`      🪙 Токен: ${tokenName}`);
                        
                        foundBalances.push({
                            index: i,
                            bank: bankPubkey,
                            token: tokenName,
                            assetShares: assetShares,
                            liabilityShares: liabilityShares,
                            hasAssets: assetShares > 0n,
                            hasLiabilities: liabilityShares > 0n
                        });
                        
                        if (bankPubkey.equals(this.BANKS.SOL)) {
                            console.log('      🎯 ЭТО SOL БАНК! Balance найден!');
                        }
                        if (bankPubkey.equals(this.BANKS.USDC)) {
                            console.log('      🎯 ЭТО USDC БАНК! Balance найден!');
                        }
                    }
                }
            }
            
            console.log(`\n📊 ИТОГОВАЯ СТАТИСТИКА:`);
            console.log(`✅ Всего активных balances: ${activeBalances}`);
            
            if (activeBalances === 0) {
                console.log('❌ НЕТ АКТИВНЫХ BALANCES!');
                console.log('💡 Нужно создать balance для токена перед flash loan');
                return { hasBalances: false, balances: [] };
            }
            
            console.log('\n🎯 НАЙДЕННЫЕ BALANCES:');
            foundBalances.forEach(balance => {
                console.log(`   ${balance.token}: ${balance.hasAssets ? '💰' : '⚪'} Assets, ${balance.hasLiabilities ? '💸' : '⚪'} Liabilities`);
            });
            
            // Проверяем какие токены можно использовать для flash loan
            const availableForFlashLoan = foundBalances.filter(b => 
                b.hasAssets || b.liabilityShares === 0n
            );
            
            console.log('\n🚀 ДОСТУПНЫЕ ДЛЯ FLASH LOAN:');
            if (availableForFlashLoan.length === 0) {
                console.log('❌ НЕТ ДОСТУПНЫХ ТОКЕНОВ ДЛЯ FLASH LOAN');
                console.log('💡 Все balances имеют долги без активов');
            } else {
                availableForFlashLoan.forEach(balance => {
                    console.log(`   ✅ ${balance.token}: Банк ${balance.bank.toString()}`);
                });
            }
            
            return {
                hasBalances: activeBalances > 0,
                balances: foundBalances,
                availableForFlashLoan: availableForFlashLoan
            };
            
        } catch (error) {
            console.error(`❌ Ошибка анализа: ${error.message}`);
            return { hasBalances: false, balances: [] };
        }
    }

    /**
     * 🔧 РЕКОМЕНДАЦИИ ПО ИСПРАВЛЕНИЮ
     */
    async getRecommendations(analysisResult) {
        console.log('\n🔧 РЕКОМЕНДАЦИИ ПО ИСПРАВЛЕНИЮ ОШИБКИ 3002:');
        console.log('═══════════════════════════════════════════════════════════════');
        
        if (!analysisResult.hasBalances) {
            console.log('❌ ПРОБЛЕМА: Нет активных lending balances в MarginFi аккаунте');
            console.log('\n✅ РЕШЕНИЯ:');
            console.log('   1. 🔧 Создать lending balance через deposit операцию');
            console.log('   2. 💰 Сделать минимальный депозит любого токена');
            console.log('   3. 🌐 Использовать MarginFi UI для создания balance');
            console.log('   4. 🔄 Переключиться на другой MarginFi аккаунт с balances');
            
            console.log('\n💡 БЫСТРОЕ РЕШЕНИЕ:');
            console.log('   node create-marginfi-balance.js');
            
        } else if (analysisResult.availableForFlashLoan.length === 0) {
            console.log('❌ ПРОБЛЕМА: Все balances имеют долги без активов');
            console.log('\n✅ РЕШЕНИЯ:');
            console.log('   1. 💰 Пополнить один из существующих balances');
            console.log('   2. 💸 Погасить долги в существующих balances');
            console.log('   3. 🔧 Создать новый balance для другого токена');
            
        } else {
            console.log('✅ НАЙДЕНЫ ДОСТУПНЫЕ BALANCES ДЛЯ FLASH LOAN!');
            console.log('\n🎯 РЕКОМЕНДУЕМЫЕ ТОКЕНЫ:');
            analysisResult.availableForFlashLoan.forEach(balance => {
                console.log(`   ✅ Используй ${balance.token} банк: ${balance.bank.toString()}`);
            });
            
            console.log('\n🔧 ОБНОВЛЕНИЕ КОДА:');
            console.log('   Измени DEFAULT_FLASH_LOAN_BANK на один из доступных банков');
        }
    }
}

async function main() {
    console.log('🔍 ПРОВЕРКА MARGINFI BALANCES');
    console.log('═══════════════════════════════════════════════════════════════');
    console.log('🎯 Ищем причину ошибки 3002 - LendingAccountBalanceNotFound');
    console.log('💡 Проверяем какие lending balances есть в MarginFi аккаунте');
    console.log('═══════════════════════════════════════════════════════════════');

    const checker = new MarginFiBalanceChecker();

    const analysisResult = await checker.analyzeMarginFiAccount();
    await checker.getRecommendations(analysisResult);

    console.log('\n🎯 ИТОГ:');
    if (analysisResult.hasBalances && analysisResult.availableForFlashLoan.length > 0) {
        console.log('✅ ПРОБЛЕМА РЕШАЕМА! Есть доступные balances для flash loan');
    } else {
        console.log('❌ НУЖНО СОЗДАТЬ LENDING BALANCE ПЕРЕД FLASH LOAN');
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = MarginFiBalanceChecker;
