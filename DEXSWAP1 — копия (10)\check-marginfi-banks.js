#!/usr/bin/env node

/**
 * 🏦 ПРОВЕРКА ДОСТУПНОСТИ БАНКОВ MARGINFI
 * ═══════════════════════════════════════════════════════════════
 * 🎯 ПРОВЕРЯЕМ ЛИМИТЫ ЗАЙМОВ И ДОСТУПНУЮ ЛИКВИДНОСТЬ
 */

const { Connection, Keypair } = require('@solana/web3.js');
const { MarginfiClient, getConfig } = require('@mrgnlabs/marginfi-client-v2');
const { NodeWallet } = require('@mrgnlabs/mrgn-common');
const fs = require('fs');

class MarginFiBankChecker {
  constructor() {
    this.connection = new Connection('https://billowing-empty-patron.solana-mainnet.quiknode.pro/c79212a7508ec6a37353793d9be93c917898e858/', 'confirmed');
    this.wallet = null;
    this.marginfiClient = null;
  }

  async initialize() {
    try {
      // Загружаем wallet
      const walletData = JSON.parse(fs.readFileSync('wallet.json', 'utf8'));
      const keypair = Keypair.fromSecretKey(new Uint8Array(walletData));
      this.wallet = new NodeWallet(keypair);

      // Создаем MarginFi client
      const config = getConfig('production');
      this.marginfiClient = await MarginfiClient.fetch(config, this.wallet, this.connection);

      return true;
    } catch (error) {
      console.error(`❌ Ошибка инициализации: ${error.message}`);
      return false;
    }
  }

  formatNumber(num) {
    if (num >= 1e9) return (num / 1e9).toFixed(2) + 'B';
    if (num >= 1e6) return (num / 1e6).toFixed(2) + 'M';
    if (num >= 1e3) return (num / 1e3).toFixed(2) + 'K';
    return num.toFixed(2);
  }

  async checkAllBanks() {
    console.log('🏦 ПРОВЕРКА ВСЕХ БАНКОВ MARGINFI');
    console.log('═══════════════════════════════════════════════════════════════');
    console.log(`💼 Wallet: ${this.wallet.publicKey.toString()}`);
    console.log('');

    try {
      const banks = Array.from(this.marginfiClient.banks.values());
      console.log(`📊 Найдено банков: ${banks.length}`);
      console.log('');

      const bankData = [];

      for (const bank of banks) {
        try {
          const symbol = bank.config.assetSymbol;
          const mint = bank.config.mint.toString();
          
          // Получаем данные о ликвидности
          const totalAssets = bank.getTotalAssetQuantity().toNumber();
          const totalLiabilities = bank.getTotalLiabilityQuantity().toNumber();
          const availableLiquidity = totalAssets - totalLiabilities;
          
          // Получаем лимиты
          const borrowLimit = bank.config.borrowLimit?.toNumber() || 0;
          const depositLimit = bank.config.depositLimit?.toNumber() || 0;
          
          // Конвертируем в USD (приблизительно)
          const assetPrice = bank.config.oracleSetup ? 1 : 1; // Упрощенно
          const availableLiquidityUsd = availableLiquidity * assetPrice;
          const borrowLimitUsd = borrowLimit * assetPrice;
          
          // Проверяем доступность для займа
          const canBorrow = availableLiquidity > 0 && borrowLimit > totalLiabilities;
          const utilizationRate = totalLiabilities / totalAssets * 100;
          
          bankData.push({
            symbol,
            mint: mint.slice(0, 8) + '...',
            totalAssets,
            totalLiabilities,
            availableLiquidity,
            borrowLimit,
            depositLimit,
            availableLiquidityUsd,
            borrowLimitUsd,
            canBorrow,
            utilizationRate: isNaN(utilizationRate) ? 0 : utilizationRate,
            bank
          });

        } catch (bankError) {
          console.log(`❌ Ошибка обработки банка: ${bankError.message}`);
        }
      }

      // Сортируем по доступной ликвидности
      bankData.sort((a, b) => b.availableLiquidity - a.availableLiquidity);

      console.log('📋 ДЕТАЛЬНАЯ ИНФОРМАЦИЯ О БАНКАХ:');
      console.log('═══════════════════════════════════════════════════════════════');

      for (const data of bankData) {
        console.log(`\n🏦 ${data.symbol} (${data.mint})`);
        console.log('─────────────────────────────────────────────────────────────');
        console.log(`💰 Общие активы: ${this.formatNumber(data.totalAssets)}`);
        console.log(`💸 Общие долги: ${this.formatNumber(data.totalLiabilities)}`);
        console.log(`💧 Доступная ликвидность: ${this.formatNumber(data.availableLiquidity)}`);
        console.log(`📊 Утилизация: ${data.utilizationRate.toFixed(1)}%`);
        console.log(`🎯 Лимит займов: ${this.formatNumber(data.borrowLimit)}`);
        console.log(`🏛️ Лимит депозитов: ${this.formatNumber(data.depositLimit)}`);
        
        if (data.canBorrow) {
          console.log(`✅ ДОСТУПЕН ДЛЯ ЗАЙМА`);
          
          // Рекомендуемая сумма займа (80% от доступной ликвидности)
          const recommendedAmount = data.availableLiquidity * 0.8;
          console.log(`💡 Рекомендуемая сумма: ${this.formatNumber(recommendedAmount)}`);
          
          if (data.symbol === 'USDC' || data.symbol === 'USDT') {
            const usdAmount = recommendedAmount / 1e6; // Для USDC/USDT
            console.log(`💵 В USD: $${this.formatNumber(usdAmount)}`);
          } else if (data.symbol === 'SOL') {
            const solAmount = recommendedAmount / 1e9; // Для SOL
            console.log(`🪙 В SOL: ${this.formatNumber(solAmount)} SOL`);
          }
        } else {
          console.log(`❌ НЕ ДОСТУПЕН ДЛЯ ЗАЙМА`);
          
          if (data.availableLiquidity <= 0) {
            console.log(`   Причина: Нет доступной ликвидности`);
          }
          if (data.borrowLimit <= data.totalLiabilities) {
            console.log(`   Причина: Достигнут лимит займов`);
          }
        }
      }

      console.log('\n🎯 РЕКОМЕНДАЦИИ ДЛЯ FLASH LOAN:');
      console.log('═══════════════════════════════════════════════════════════════');

      // Находим лучшие банки для flash loan
      const availableBanks = bankData.filter(b => b.canBorrow && b.availableLiquidity > 1000);
      
      if (availableBanks.length === 0) {
        console.log('❌ НЕТ ДОСТУПНЫХ БАНКОВ ДЛЯ FLASH LOAN');
        console.log('💡 Попробуйте позже или уменьшите сумму займа');
      } else {
        console.log(`✅ Доступно банков: ${availableBanks.length}`);
        console.log('');
        
        console.log('🥇 ТОП-3 БАНКА ДЛЯ FLASH LOAN:');
        
        for (let i = 0; i < Math.min(3, availableBanks.length); i++) {
          const bank = availableBanks[i];
          const recommendedAmount = bank.availableLiquidity * 0.8;
          
          console.log(`\n${i + 1}. ${bank.symbol}`);
          console.log(`   💧 Доступно: ${this.formatNumber(bank.availableLiquidity)}`);
          console.log(`   💡 Рекомендуется: ${this.formatNumber(recommendedAmount)}`);
          
          if (bank.symbol === 'USDC' || bank.symbol === 'USDT') {
            const usdAmount = recommendedAmount / 1e6;
            console.log(`   💵 В USD: $${this.formatNumber(usdAmount)}`);
          } else if (bank.symbol === 'SOL') {
            const solAmount = recommendedAmount / 1e9;
            console.log(`   🪙 В SOL: ${this.formatNumber(solAmount)} SOL`);
          }
          
          console.log(`   📊 Утилизация: ${bank.utilizationRate.toFixed(1)}%`);
        }
      }

      return availableBanks;

    } catch (error) {
      console.error(`❌ Ошибка проверки банков: ${error.message}`);
      return [];
    }
  }

  async testFlashLoanAmount(symbol, amount) {
    console.log(`\n🧪 ТЕСТ FLASH LOAN: ${amount} ${symbol}`);
    console.log('─────────────────────────────────────────────────────────────');

    try {
      // Находим банк по символу
      const banks = Array.from(this.marginfiClient.banks.values());
      const bank = banks.find(b => b.config.assetSymbol === symbol);
      
      if (!bank) {
        console.log(`❌ Банк ${symbol} не найден`);
        return false;
      }

      // Получаем MarginFi аккаунт
      const accounts = await this.marginfiClient.getMarginfiAccountsForAuthority();
      if (accounts.length === 0) {
        console.log('❌ MarginFi аккаунты не найдены');
        return false;
      }

      const marginfiAccount = accounts[0];

      // Создаем тестовую инструкцию займа
      const borrowIx = await marginfiAccount.makeBorrowIx(amount, bank.address);
      
      if (borrowIx && borrowIx.instructions && borrowIx.instructions.length > 0) {
        console.log(`✅ Займ ${amount} ${symbol} возможен`);
        console.log(`📏 Инструкций: ${borrowIx.instructions.length}`);
        return true;
      } else {
        console.log(`❌ Не удалось создать инструкцию займа`);
        return false;
      }

    } catch (error) {
      if (error.message.includes('6027') || error.message.includes('BankLiabilityCapacityExceeded')) {
        console.log(`❌ Банк ${symbol} переполнен`);
        console.log(`🔍 Ошибка: Bank borrow cap exceeded`);
      } else {
        console.log(`❌ Ошибка теста: ${error.message}`);
      }
      return false;
    }
  }
}

async function main() {
  const checker = new MarginFiBankChecker();
  
  const initialized = await checker.initialize();
  if (!initialized) {
    console.log('❌ Не удалось инициализировать проверку банков');
    return;
  }

  // Проверяем все банки
  const availableBanks = await checker.checkAllBanks();

  // Тестируем конкретные суммы для доступных банков
  if (availableBanks.length > 0) {
    console.log('\n🧪 ТЕСТИРОВАНИЕ КОНКРЕТНЫХ СУММ:');
    console.log('═══════════════════════════════════════════════════════════════');

    for (const bank of availableBanks.slice(0, 3)) { // Тестируем топ-3
      const recommendedAmount = Math.floor(bank.availableLiquidity * 0.5); // 50% для безопасности
      await checker.testFlashLoanAmount(bank.symbol, recommendedAmount);
    }
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = MarginFiBankChecker;
