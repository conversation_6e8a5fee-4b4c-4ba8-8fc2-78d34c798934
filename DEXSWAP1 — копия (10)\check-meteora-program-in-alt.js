/**
 * 🔍 ПРОВЕРКА METEORA_DLMM_PROGRAM В ALT ТАБЛИЦЕ
 */

const { Connection, PublicKey } = require('@solana/web3.js');
require('dotenv').config({ path: '.env.solana' });

async function checkMeteoraInALT() {
    console.log('🔍 ПРОВЕРКА METEORA_DLMM_PROGRAM В ALT ТАБЛИЦЕ');
    console.log('=' .repeat(60));

    try {
        // 1. Подключение к RPC
        const connection = new Connection(process.env.QUICKNODE_RPC_URL);
        console.log('✅ Подключение к RPC установлено');

        // 2. Кастомная ALT таблица
        const customALTAddress = new PublicKey('FAeyUf4AdG7vTQ2Q89r9hWBkPdYkykJKG4CxgqArBGXe');
        console.log(`📋 Кастомная ALT таблица: ${customALTAddress.toString()}`);

        // 3. METEORA_DLMM_PROGRAM адрес
        const meteoraProgram = 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo';
        console.log(`🎯 Проверяем адрес: ${meteoraProgram}`);

        // 4. Загружаем ALT таблицу
        const altAccount = await connection.getAddressLookupTable(customALTAddress);
        
        if (!altAccount || !altAccount.value) {
            throw new Error('ALT таблица не найдена!');
        }

        const addresses = altAccount.value.state.addresses.map(addr => addr.toString());
        console.log(`📊 Всего адресов в ALT: ${addresses.length}`);

        // 5. Ищем METEORA_DLMM_PROGRAM
        const index = addresses.findIndex(addr => addr === meteoraProgram);
        
        if (index !== -1) {
            console.log(`✅ METEORA_DLMM_PROGRAM НАЙДЕН В ALT!`);
            console.log(`   Индекс: ${index}`);
            console.log(`   ALT[3][${index}]: ${meteoraProgram}`);
        } else {
            console.log(`❌ METEORA_DLMM_PROGRAM НЕ НАЙДЕН В ALT!`);
            console.log(`   Нужно добавить: ${meteoraProgram}`);
        }

        // 6. Показываем первые 10 адресов для контекста
        console.log(`\n📋 ПЕРВЫЕ 10 АДРЕСОВ В ALT:`);
        addresses.slice(0, 10).forEach((addr, i) => {
            const isTarget = addr === meteoraProgram;
            console.log(`   ${i}: ${addr.slice(0,8)}...${addr.slice(-8)} ${isTarget ? '← METEORA_DLMM_PROGRAM' : ''}`);
        });

        console.log(`\n${'='.repeat(60)}`);
        console.log(index !== -1 ? '✅ METEORA_DLMM_PROGRAM УЖЕ В ALT!' : '❌ METEORA_DLMM_PROGRAM НУЖНО ДОБАВИТЬ!');
        console.log(`${'='.repeat(60)}`);

    } catch (error) {
        console.error('❌ Ошибка проверки:', error.message);
    }
}

// Запуск проверки
if (require.main === module) {
    checkMeteoraInALT();
}

module.exports = { checkMeteoraInALT };
