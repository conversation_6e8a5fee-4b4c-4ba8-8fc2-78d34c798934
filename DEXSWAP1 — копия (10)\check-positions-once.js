#!/usr/bin/env node

/**
 * 🔍 ОТДЕЛЬНАЯ ПРОВЕРКА ПОЗИЦИЙ METEORA
 * Запускается ОДИН РАЗ для проверки что позиции готовы
 * НЕ ИНТЕГРИРУЕТСЯ В ОСНОВНОЙ КОД!
 */

const { Connection, PublicKey, Keypair } = require('@solana/web3.js');
const { MeteoraPositionBalanceChecker } = require('./meteora-position-balance-checker');
require('dotenv').config();

class PositionChecker {
    constructor() {
        // 🔥 QUICKNODE RPC
        const quicknodeUrl = process.env.QUICKNODE_RPC_URL || process.env.QUICKNODE_RPC_URL_BACKUP2;
        this.connection = new Connection(quicknodeUrl, 'confirmed');
        
        // 🔑 КОШЕЛЕК
        const privateKeyArray = JSON.parse(process.env.PRIVATE_KEY);
        this.wallet = Keypair.fromSecretKey(new Uint8Array(privateKeyArray));
        
        console.log(`🚀 Position Checker инициализирован`);
        console.log(`   RPC: ${quicknodeUrl.slice(0, 50)}...`);
        console.log(`   Wallet: ${this.wallet.publicKey.toString().slice(0, 8)}...`);
    }

    async checkAllPositions() {
        console.log('\n🔍 ПРОВЕРКА ВСЕХ ПОЗИЦИЙ METEORA...\n');

        const checker = new MeteoraPositionBalanceChecker(this.connection, this.wallet);

        // 🎯 ПОЗИЦИИ ИЗ КОНФИГА
        const positions = [
            {
                name: 'Pool 1 Position',
                poolAddress: '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6',
                positionAddress: 'HCvX4un57v1SdYQ2LFywaDYyZySCWAv5cKfKgM5EKkpF'
            },
            {
                name: 'Pool 2 Position', 
                poolAddress: 'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y',
                positionAddress: '7ydf4bfon7f8YSu8xR35QsKU5Tynb5CKJ36fr3vnycis'
            }
        ];

        let allReady = true;

        for (let i = 0; i < positions.length; i++) {
            const pos = positions[i];
            console.log(`\n📍 ПРОВЕРКА ${pos.name}:`);
            console.log(`   Pool: ${pos.poolAddress.slice(0, 8)}...`);
            console.log(`   Position: ${pos.positionAddress.slice(0, 8)}...`);

            try {
                const result = await checker.ensurePositionIsEmpty(
                    new PublicKey(pos.poolAddress),
                    new PublicKey(pos.positionAddress)
                );

                if (result.success) {
                    console.log(`   ✅ ${pos.name}: ГОТОВА К ИСПОЛЬЗОВАНИЮ`);
                    if (result.cleared) {
                        console.log(`   🧹 Позиция была очищена автоматически`);
                    }
                } else {
                    console.log(`   ❌ ${pos.name}: НЕ ГОТОВА - ${result.error}`);
                    allReady = false;
                }

            } catch (error) {
                console.log(`   💥 ${pos.name}: ОШИБКА - ${error.message}`);
                allReady = false;
            }
        }

        console.log('\n' + '='.repeat(60));
        if (allReady) {
            console.log('✅ ВСЕ ПОЗИЦИИ ГОТОВЫ К FLASH LOAN ОПЕРАЦИЯМ!');
            console.log('🚀 Можно запускать основной бот без дополнительных проверок');
        } else {
            console.log('❌ НЕКОТОРЫЕ ПОЗИЦИИ НЕ ГОТОВЫ!');
            console.log('🔧 Исправьте проблемы перед запуском основного бота');
        }
        console.log('='.repeat(60));

        return allReady;
    }
}

// Запуск проверки
async function main() {
    try {
        const checker = new PositionChecker();
        const ready = await checker.checkAllPositions();
        
        process.exit(ready ? 0 : 1);
        
    } catch (error) {
        console.error('💥 КРИТИЧЕСКАЯ ОШИБКА:', error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = { PositionChecker };
