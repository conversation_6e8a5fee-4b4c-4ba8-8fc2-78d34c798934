@echo off
echo ===== ПРОВЕРКА СТАТУСА MARGINFI СИСТЕМЫ =====
echo Время: %date% %time%
echo.

echo 1. ПРОВЕРКА ПРОЦЕССОВ NODE.JS:
tasklist /FI "IMAGENAME eq node.exe" /FO TABLE
echo.

echo 2. ПРОВЕРКА .ENV ФАЙЛА:
if exist .env (
    echo ✅ .env файл найден
    findstr /C:"MARGINFI_ENABLED" .env
    findstr /C:"FLASH_LOAN_ENABLED" .env
    findstr /C:"QUICKNODE_RPC_URL" .env
) else (
    echo ❌ .env файл НЕ найден
)
echo.

echo 3. ПРОВЕРКА WALLET ФАЙЛА:
if exist wallet.json (
    echo ✅ wallet.json найден
) else (
    echo ❌ wallet.json НЕ найден
)
echo.

echo 4. ПРОВЕРКА АКТИВНОСТИ СИСТЕМЫ (ALT файлы):
if exist ALT (
    dir ALT /O-D /B | head -5
    echo Последние 5 ALT файлов (по дате)
) else (
    echo ❌ ALT папка не найдена
)
echo.

echo 5. ПРОВЕРКА ОСНОВНЫХ ФАЙЛОВ:
if exist real-solana-rpc-websocket.js (
    echo ✅ real-solana-rpc-websocket.js найден
) else (
    echo ❌ real-solana-rpc-websocket.js НЕ найден
)

if exist "src\atomic-transaction-builder-fixed.js" (
    echo ✅ atomic-transaction-builder-fixed.js найден
) else (
    echo ❌ atomic-transaction-builder-fixed.js НЕ найден
)
echo.

echo 6. ЗАПУСК ПРОВЕРКИ MARGINFI:
node check-marginfi-status.js
echo.

echo ===== ПРОВЕРКА ЗАВЕРШЕНА =====
pause
