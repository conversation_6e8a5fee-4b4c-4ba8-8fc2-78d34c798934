const { PublicKey } = require('@solana/web3.js');

console.log('🔍 ПРОВЕРКА ПРАВИЛЬНЫХ АДРЕСОВ SYSVAR...');

// Проверяем различные варианты Instructions Sysvar
const variants = [
    'Sysvar1nstructions1111111111111111111111111',  // 43 символа - НЕПРАВИЛЬНЫЙ
    'Sysvar1nstructions11111111111111111111111111', // 44 символа - попытка 1
    'Sysvar1nstructions111111111111111111111111111', // 45 символов - попытка 2
];

variants.forEach((address, i) => {
    console.log(`\n${i + 1}. Адрес: ${address}`);
    console.log(`   Длина: ${address.length} символов`);
    
    try {
        const pubkey = new PublicKey(address);
        console.log(`   ✅ Валидный PublicKey: ${pubkey.toString()}`);
        console.log(`   📊 Длина результата: ${pubkey.toString().length}`);
    } catch (error) {
        console.log(`   ❌ Ошибка: ${error.message}`);
    }
});

// Проверяем наш wallet
console.log('\n🔑 ПРОВЕРКА WALLET:');
const walletAddress = 'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV';
console.log(`Wallet: ${walletAddress}`);
console.log(`Длина: ${walletAddress.length} символов`);

try {
    const walletPubkey = new PublicKey(walletAddress);
    console.log(`✅ Валидный PublicKey: ${walletPubkey.toString()}`);
    console.log(`📊 Длина результата: ${walletPubkey.toString().length}`);
} catch (error) {
    console.log(`❌ Ошибка: ${error.message}`);
}

// Попробуем создать правильный Instructions Sysvar адрес
console.log('\n🔧 СОЗДАНИЕ ПРАВИЛЬНОГО INSTRUCTIONS SYSVAR:');

// Известные правильные Sysvar адреса для сравнения
const knownSysvars = {
    'Clock': 'SysvarC1ock11111111111111111111111111111111',
    'Rent': 'SysvarRent111111111111111111111111111111111',
    'RecentBlockhashes': 'SysvarRecentB1ockHashes11111111111111111111',
    'StakeHistory': 'SysvarStakeHistory1111111111111111111111111'
};

Object.entries(knownSysvars).forEach(([name, address]) => {
    console.log(`${name}: ${address} (${address.length} символов)`);
    try {
        const pubkey = new PublicKey(address);
        console.log(`   ✅ Валидный: ${pubkey.toString().length === 44 ? 'ДА' : 'НЕТ'}`);
    } catch (error) {
        console.log(`   ❌ Ошибка: ${error.message}`);
    }
});

// Попробуем найти правильный Instructions Sysvar
console.log('\n🎯 ПОИСК ПРАВИЛЬНОГО INSTRUCTIONS SYSVAR:');

// Базируясь на паттерне других Sysvar, попробуем разные варианты
const instructionsVariants = [
    'Sysvar1nstructions1111111111111111111111111',   // Оригинал (43)
    'Sysvar1nstructions11111111111111111111111111',  // +1 символ (44)
    'SysvarInstructions1111111111111111111111111',   // Полное слово (44)
    'SysvarInstructions11111111111111111111111111',  // Полное слово +1 (45)
];

instructionsVariants.forEach((address, i) => {
    console.log(`\nВариант ${i + 1}: ${address}`);
    console.log(`Длина: ${address.length} символов`);
    
    try {
        const pubkey = new PublicKey(address);
        console.log(`✅ Валидный PublicKey`);
        
        // Проверяем, является ли это правильным Sysvar
        if (address.startsWith('Sysvar') && address.length === 44) {
            console.log(`🎯 ПОТЕНЦИАЛЬНО ПРАВИЛЬНЫЙ АДРЕС!`);
        }
    } catch (error) {
        console.log(`❌ Невалидный: ${error.message}`);
    }
});
