const { Connection, PublicKey } = require('@solana/web3.js');

async function checkUsdcOracle() {
    const connection = new Connection('https://api.mainnet-beta.solana.com');
    
    console.log('🔍 ПРОВЕРКА USDC ORACLE ИЗ ЛОГОВ...\n');
    
    const oracleFromLogs = new PublicKey('66bbb81Xo3cwR3J8XNbumHixVHSeLy49Dcb3Pn2kb5Gd');
    const expectedPythOracle = new PublicKey('Gnt27xtC473ZT2Mw5u8wZ68Z3gULkSTb5DuxJy7eJotD');
    const pythProgram = new PublicKey('FsJ3A3u2vn5cTVofAjvy6y5kwABJAqYWpe4975bi2epH');
    
    console.log('📋 ПРОВЕРЯЕМЫЕ ORACLE:');
    console.log(`   Из логов: ${oracleFromLogs.toString()}`);
    console.log(`   Ожидаемый Pyth: ${expectedPythOracle.toString()}`);
    console.log(`   Pyth Program: ${pythProgram.toString()}\n`);
    
    // 1. Проверяем oracle из логов
    console.log('🔍 ПРОВЕРКА ORACLE ИЗ ЛОГОВ:');
    const oracleInfo = await connection.getAccountInfo(oracleFromLogs);
    
    if (!oracleInfo) {
        console.log('❌ Oracle из логов НЕ СУЩЕСТВУЕТ!');
        return;
    }
    
    console.log(`✅ Oracle из логов существует`);
    console.log(`   Owner: ${oracleInfo.owner.toString()}`);
    console.log(`   Data length: ${oracleInfo.data.length}`);
    
    if (oracleInfo.owner.toString() === pythProgram.toString()) {
        console.log(`   ✅ ПРАВИЛЬНЫЙ PYTH ORACLE!`);
    } else {
        console.log(`   ❌ НЕПРАВИЛЬНЫЙ OWNER! Не Pyth oracle.`);
        console.log(`   🔍 Фактический owner: ${oracleInfo.owner.toString()}`);
    }
    
    // 2. Проверяем ожидаемый Pyth oracle
    console.log('\n🔍 ПРОВЕРКА ОЖИДАЕМОГО PYTH ORACLE:');
    const expectedInfo = await connection.getAccountInfo(expectedPythOracle);
    
    if (!expectedInfo) {
        console.log('❌ Ожидаемый Pyth oracle НЕ СУЩЕСТВУЕТ!');
        return;
    }
    
    console.log(`✅ Ожидаемый Pyth oracle существует`);
    console.log(`   Owner: ${expectedInfo.owner.toString()}`);
    console.log(`   Data length: ${expectedInfo.data.length}`);
    
    if (expectedInfo.owner.toString() === pythProgram.toString()) {
        console.log(`   ✅ ПРАВИЛЬНЫЙ PYTH ORACLE!`);
    } else {
        console.log(`   ❌ НЕПРАВИЛЬНЫЙ OWNER!`);
    }
    
    // 3. Сравнение
    console.log('\n📊 СРАВНЕНИЕ:');
    console.log(`   Oracle из логов == Ожидаемый: ${oracleFromLogs.toString() === expectedPythOracle.toString()}`);
    
    if (oracleFromLogs.toString() !== expectedPythOracle.toString()) {
        console.log('🔧 РЕШЕНИЕ: Нужно использовать правильный Pyth USDC oracle!');
        console.log(`   Заменить: ${oracleFromLogs.toString()}`);
        console.log(`   На: ${expectedPythOracle.toString()}`);
    }
    
    console.log('\n🎉 ПРОВЕРКА ORACLE ЗАВЕРШЕНА!');
}

checkUsdcOracle().catch(console.error);
