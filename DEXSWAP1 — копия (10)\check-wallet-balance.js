#!/usr/bin/env node

/**
 * 🔍 ПРОВЕРКА БАЛАНСА КОШЕЛЬКА
 */

const { Connection, Keypair, PublicKey } = require('@solana/web3.js');
const fs = require('fs');
const path = require('path');

async function checkWalletBalance() {
  try {
    // Загружаем wallet
    function base58Decode(str) {
      const alphabet = '**********************************************************';
      let decoded = 0n;
      let multi = 1n;
      for (let i = str.length - 1; i >= 0; i--) {
        const char = str[i];
        const index = alphabet.indexOf(char);
        if (index === -1) throw new Error(`Invalid character: ${char}`);
        decoded += BigInt(index) * multi;
        multi *= 58n;
      }
      const bytes = [];
      while (decoded > 0n) {
        bytes.unshift(Number(decoded % 256n));
        decoded = decoded / 256n;
      }
      for (let i = 0; i < str.length && str[i] === '1'; i++) {
        bytes.unshift(0);
      }
      return new Uint8Array(bytes);
    }
    
    const envPath = path.join(__dirname, '.env.solana');
    const envContent = fs.readFileSync(envPath, 'utf8');
    const walletMatch = envContent.match(/WALLET_PRIVATE_KEY=([^\n\r]+)/);
    const privateKeyBase58 = walletMatch[1].trim();
    const privateKeyBytes = base58Decode(privateKeyBase58);
    const wallet = Keypair.fromSecretKey(privateKeyBytes);

    const connection = new Connection('https://sparkling-patient-market.solana-mainnet.quiknode.pro/146de9e433ca3b8f3473c0bd41742b24429dfc43/', 'confirmed');
    
    console.log('🔍 ПРОВЕРКА БАЛАНСА КОШЕЛЬКА');
    console.log('═══════════════════════════════════════════════════════════════════');
    console.log(`💰 Wallet: ${wallet.publicKey.toString()}`);

    // SOL баланс
    const solBalance = await connection.getBalance(wallet.publicKey);
    console.log(`💎 SOL: ${solBalance / 1e9} SOL`);

    // USDC баланс
    const usdcMint = new PublicKey('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v');
    const usdcTokenAccount = new PublicKey('********************************************');
    
    try {
      const usdcAccountInfo = await connection.getTokenAccountBalance(usdcTokenAccount);
      const usdcBalance = usdcAccountInfo.value.uiAmount;
      console.log(`💵 USDC: ${usdcBalance} USDC`);
      
      if (usdcBalance >= 12526) {
        console.log(`✅ ДОСТАТОЧНО USDC ДЛЯ ВОЗВРАТА ДОЛГА (нужно ~12,526 USDC)`);
      } else {
        console.log(`❌ НЕ ХВАТАЕТ USDC! Нужно: 12,526 USDC, есть: ${usdcBalance} USDC`);
        console.log(`💡 Нужно пополнить: ${12526 - usdcBalance} USDC`);
      }
    } catch (error) {
      console.log(`❌ Ошибка получения USDC баланса: ${error.message}`);
    }

    // USDT баланс
    const usdtMint = new PublicKey('Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB');
    
    try {
      // Ищем USDT token account
      const tokenAccounts = await connection.getTokenAccountsByOwner(wallet.publicKey, {
        mint: usdtMint
      });
      
      if (tokenAccounts.value.length > 0) {
        const usdtAccountInfo = await connection.getTokenAccountBalance(tokenAccounts.value[0].pubkey);
        const usdtBalance = usdtAccountInfo.value.uiAmount;
        console.log(`💴 USDT: ${usdtBalance} USDT`);
      } else {
        console.log(`💴 USDT: 0 USDT (нет token account)`);
      }
    } catch (error) {
      console.log(`❌ Ошибка получения USDT баланса: ${error.message}`);
    }

    console.log('\n🚨 КРИТИЧЕСКАЯ СИТУАЦИЯ:');
    console.log('💸 MarginFi долг: ~12,526 USDC');
    console.log('⚠️ Аккаунт в состоянии "bad health"');
    console.log('🔧 Нужно СРОЧНО вернуть долг!');
    
    console.log('\n💡 ВАРИАНТЫ РЕШЕНИЯ:');
    console.log('1. Пополнить кошелек USDC и вернуть долг');
    console.log('2. Использовать MarginFi UI: https://app.marginfi.com/');
    console.log('3. Продать другие токены за USDC');
    console.log('4. Обратиться в поддержку MarginFi');
    
  } catch (error) {
    console.error(`❌ Ошибка: ${error.message}`);
    console.error(error.stack);
  }
}

checkWalletBalance().catch(console.error);
