const { Connection, PublicKey } = require('@solana/web3.js');
const { getAssociatedTokenAddress } = require('@solana/spl-token');

/**
 * 🔍 ПРОВЕРКА БАЛАНСОВ КОШЕЛЬКА
 */
async function checkWalletBalances() {
    const connection = new Connection('https://api.mainnet-beta.solana.com');
    const walletPubkey = new PublicKey('bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV');
    
    console.log('🔍 ПРОВЕРКА БАЛАНСОВ КОШЕЛЬКА...');
    console.log(`👤 Кошелек: ${walletPubkey.toString()}`);
    console.log('═══════════════════════════════════════════════════════════');
    
    // SOL баланс
    try {
        const solBalance = await connection.getBalance(walletPubkey);
        console.log(`💰 SOL: ${(solBalance / 1e9).toFixed(6)} SOL`);
    } catch (error) {
        console.log(`❌ Ошибка получения SOL баланса: ${error.message}`);
    }
    
    // Токены для проверки
    const tokens = [
        { name: 'USDC', mint: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', decimals: 6 },
        { name: 'USDT', mint: 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB', decimals: 6 },
        { name: 'WSOL', mint: 'So11111111111111111111111111111111111111112', decimals: 9 },
    ];
    
    for (const token of tokens) {
        try {
            const tokenAccount = await getAssociatedTokenAddress(
                new PublicKey(token.mint),
                walletPubkey
            );
            
            console.log(`\n🔍 ${token.name}:`);
            console.log(`   Mint: ${token.mint}`);
            console.log(`   ATA: ${tokenAccount.toString()}`);
            
            try {
                const balance = await connection.getTokenAccountBalance(tokenAccount);
                const amount = parseFloat(balance.value.amount) / Math.pow(10, token.decimals);
                console.log(`   💰 Баланс: ${amount.toLocaleString()} ${token.name}`);
                console.log(`   📊 Raw: ${balance.value.amount} (${balance.value.decimals} decimals)`);
            } catch (balanceError) {
                if (balanceError.message.includes('could not find account')) {
                    console.log(`   ❌ Аккаунт не существует`);
                } else {
                    console.log(`   ❌ Ошибка баланса: ${balanceError.message}`);
                }
            }
        } catch (error) {
            console.log(`❌ Ошибка ${token.name}: ${error.message}`);
        }
    }
    
    // Получаем все token accounts
    console.log('\n🔍 ВСЕ TOKEN ACCOUNTS:');
    try {
        const tokenAccounts = await connection.getParsedTokenAccountsByOwner(walletPubkey, {
            programId: new PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA')
        });
        
        console.log(`📊 Найдено token accounts: ${tokenAccounts.value.length}`);
        
        tokenAccounts.value.forEach((account, index) => {
            const data = account.account.data.parsed.info;
            const mint = data.mint;
            const amount = data.tokenAmount.uiAmount;
            const decimals = data.tokenAmount.decimals;
            
            if (amount > 0) {
                console.log(`   ${index + 1}. ${mint.slice(0, 8)}...${mint.slice(-8)}: ${amount} (${decimals} decimals)`);
            }
        });
        
    } catch (error) {
        console.log(`❌ Ошибка получения token accounts: ${error.message}`);
    }
}

checkWalletBalances().catch(console.error);
