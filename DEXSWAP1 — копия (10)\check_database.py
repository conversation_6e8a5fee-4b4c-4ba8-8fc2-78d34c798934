#!/usr/bin/env python3
"""
🔍 DATABASE CHECKER
Проверка содержимого базы данных
"""

import sqlite3
import json
from datetime import datetime

def check_database():
    """Проверка базы данных"""
    print("🔍 ПРОВЕРКА БАЗЫ ДАННЫХ")
    print("=" * 60)
    
    try:
        conn = sqlite3.connect("unified_bug_hunting.db")
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # Проверка таблиц
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        print(f"📋 Таблицы: {', '.join(tables)}")
        
        # Проверка целей
        cursor.execute("SELECT COUNT(*) as count FROM test_targets")
        target_count = cursor.fetchone()['count']
        print(f"🎯 Всего целей: {target_count}")
        
        if target_count > 0:
            # Топ цели по приоритету
            cursor.execute("""
                SELECT name, target_type, priority_score, contracts, endpoints 
                FROM test_targets 
                ORDER BY priority_score DESC 
                LIMIT 10
            """)
            
            print(f"\n🏆 ТОП-10 ЦЕЛЕЙ ПО ПРИОРИТЕТУ:")
            for i, row in enumerate(cursor.fetchall(), 1):
                contracts = json.loads(row['contracts'] or '[]')
                endpoints = json.loads(row['endpoints'] or '[]')
                print(f"   {i}. {row['name']} ({row['priority_score']:.2f})")
                print(f"      Тип: {row['target_type']}")
                print(f"      Контракты: {len(contracts)}")
                print(f"      Endpoints: {len(endpoints)}")
                print()
        
        # Проверка результатов
        cursor.execute("SELECT COUNT(*) as count FROM test_results")
        result_count = cursor.fetchone()['count']
        print(f"📊 Всего результатов: {result_count}")
        
        if result_count > 0:
            # Статистика по стратегиям
            cursor.execute("""
                SELECT strategy_name, strategy_type,
                       COUNT(*) as executions,
                       SUM(CASE WHEN vulnerability_found THEN 1 ELSE 0 END) as vulnerabilities
                FROM test_results 
                GROUP BY strategy_name, strategy_type
                ORDER BY vulnerabilities DESC, executions DESC
                LIMIT 10
            """)
            
            print(f"\n⚡ ТОП СТРАТЕГИИ:")
            for row in cursor.fetchall():
                success_rate = row['vulnerabilities'] / row['executions'] * 100 if row['executions'] > 0 else 0
                print(f"   {row['strategy_name']} ({row['strategy_type']})")
                print(f"      Выполнений: {row['executions']}, Уязвимостей: {row['vulnerabilities']}")
                print(f"      Успешность: {success_rate:.1f}%")
                print()
        
        # Проверка стратегий
        cursor.execute("SELECT COUNT(*) as count FROM strategies")
        strategy_count = cursor.fetchone()['count']
        print(f"🧠 Всего стратегий: {strategy_count}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Ошибка проверки базы данных: {e}")

def fix_database_targets():
    """Исправление целей в базе данных"""
    print("\n🔧 ИСПРАВЛЕНИЕ ЦЕЛЕЙ В БАЗЕ ДАННЫХ")
    print("-" * 40)
    
    try:
        conn = sqlite3.connect("unified_bug_hunting.db")
        cursor = conn.cursor()
        
        # Удаляем неправильные цели
        cursor.execute("DELETE FROM test_targets WHERE name IN ('Close menu', 'I''m feeling lucky', 'General', 'Join the newsletter', 'EXPLORE')")
        deleted = cursor.rowcount
        print(f"🗑️ Удалено неправильных целей: {deleted}")
        
        # Проверяем, есть ли правильные цели
        cursor.execute("SELECT COUNT(*) as count FROM test_targets WHERE target_type = 'immunefi_program'")
        immunefi_count = cursor.fetchone()[0]
        
        if immunefi_count == 0:
            print("📥 Добавляем правильные цели...")
            
            # Добавляем правильные цели
            correct_targets = [
                {
                    'target_id': 'uniswap_v3',
                    'target_type': 'immunefi_program',
                    'name': 'Uniswap V3',
                    'url': 'https://immunefi.com/bounty/uniswap/',
                    'contracts': ['******************************************'],
                    'endpoints': ['https://api.uniswap.org/v1/'],
                    'metadata': {'max_bounty': '$2,250,000', 'ecosystem': 'Ethereum'},
                    'priority_score': 0.95
                },
                {
                    'target_id': 'solana_foundation',
                    'target_type': 'immunefi_program',
                    'name': 'Solana Foundation',
                    'url': 'https://immunefi.com/bounty/solana/',
                    'contracts': ['********************************'],
                    'endpoints': ['https://api.mainnet-beta.solana.com'],
                    'metadata': {'max_bounty': '$2,000,000', 'ecosystem': 'Solana'},
                    'priority_score': 0.92
                },
                {
                    'target_id': 'compound_finance',
                    'target_type': 'immunefi_program',
                    'name': 'Compound Finance',
                    'url': 'https://immunefi.com/bounty/compound/',
                    'contracts': ['******************************************'],
                    'endpoints': ['https://api.compound.finance/api/v2/'],
                    'metadata': {'max_bounty': '$1,000,000', 'ecosystem': 'Ethereum'},
                    'priority_score': 0.88
                }
            ]
            
            for target in correct_targets:
                cursor.execute("""
                    INSERT OR REPLACE INTO test_targets 
                    (target_id, target_type, name, url, contracts, endpoints, metadata, priority_score, test_count, vulnerability_count, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, 0, 0, ?, ?)
                """, (
                    target['target_id'],
                    target['target_type'],
                    target['name'],
                    target['url'],
                    json.dumps(target['contracts']),
                    json.dumps(target['endpoints']),
                    json.dumps(target['metadata']),
                    target['priority_score'],
                    datetime.now().isoformat(),
                    datetime.now().isoformat()
                ))
            
            conn.commit()
            print(f"✅ Добавлено {len(correct_targets)} правильных целей")
        else:
            print(f"✅ Уже есть {immunefi_count} правильных целей")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Ошибка исправления базы данных: {e}")

if __name__ == "__main__":
    check_database()
    fix_database_targets()
    print("\n" + "="*60)
    check_database()
