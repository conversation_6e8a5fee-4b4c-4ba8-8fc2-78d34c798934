#!/usr/bin/env python3
"""
🧹 CLEAN RESTART
Очистка базы данных и перезапуск с правильными данными
"""

import os
import asyncio
from master_bug_hunting_coordinator import MasterBugHuntingCoordinator

async def clean_restart():
    """Очистка и перезапуск системы"""
    print("🧹 CLEAN RESTART SYSTEM")
    print("=" * 60)
    
    # Удаляем старую базу данных
    if os.path.exists("unified_bug_hunting.db"):
        os.remove("unified_bug_hunting.db")
        print("🗑️ Старая база данных удалена")
    
    # Запускаем систему заново
    print("🚀 Запуск системы с чистой базой данных...")
    
    async with MasterBugHuntingCoordinator() as coordinator:
        # Получение статуса
        status = await coordinator.get_real_time_status()
        print(f"📊 Статус: {status['loaded_strategies']} стратегий загружено")
        
        # Запуск полного цикла с ограниченными параметрами
        report = await coordinator.run_full_hunting_cycle(
            max_targets=3  # Только 3 цели для быстрого теста
        )
        
        print(f"\n🎉 ЦИКЛ ЗАВЕРШЕН!")
        print(f"   Целей протестировано: {report['session_info']['targets_processed']}")
        print(f"   Стратегий выполнено: {report['session_info']['strategies_executed']}")
        print(f"   Уязвимостей найдено: {report['session_info']['vulnerabilities_found']}")
        print(f"   Время выполнения: {report['cycle_duration_minutes']:.1f} минут")
        
        return report

if __name__ == "__main__":
    asyncio.run(clean_restart())
