const { Connection, Keypair, PublicKey, sendAndConfirmTransaction } = require('@solana/web3.js');
const DLMM = require('@meteora-ag/dlmm').default;
require('dotenv').config();

class PositionCloser {
    constructor() {
        // 🔥 QUICKNODE RPC ДЛЯ БЫСТРОГО ПОДКЛЮЧЕНИЯ!
        const quicknodeUrl = process.env.QUICKNODE_RPC_URL || process.env.QUICKNODE_RPC_URL_BACKUP2;
        if (!quicknodeUrl) {
            throw new Error('❌ QUICKNODE_RPC_URL не найден в .env файле!');
        }

        this.connection = new Connection(quicknodeUrl, 'confirmed');
        console.log(`🌐 RPC: ${quicknodeUrl.substring(0, 50)}...`);

        // 🔥 ЗАГРУЗКА ПРИВАТНОГО КЛЮЧА (BASE58 ФОРМАТ!)
        const privateKeyString = process.env.PRIVATE_KEY || process.env.WALLET_PRIVATE_KEY;
        if (!privateKeyString) {
            throw new Error('❌ PRIVATE_KEY или WALLET_PRIVATE_KEY не найден в .env файле!');
        }

        try {
            // 🔥 ПРИВАТНЫЙ КЛЮЧ В BASE58 ФОРМАТЕ - ПРАВИЛЬНЫЙ ИМПОРТ
            const bs58 = require('bs58').default;
            this.wallet = Keypair.fromSecretKey(bs58.decode(privateKeyString));
            console.log(`✅ Wallet загружен: ${this.wallet.publicKey.toString()}`);
        } catch (error) {
            // 🔥 FALLBACK: ПОПРОБУЕМ КАК JSON МАССИВ
            try {
                this.wallet = Keypair.fromSecretKey(new Uint8Array(JSON.parse(privateKeyString)));
                console.log(`✅ Wallet загружен (JSON): ${this.wallet.publicKey.toString()}`);
            } catch (jsonError) {
                throw new Error(`❌ Ошибка загрузки приватного ключа: ${error.message} | JSON: ${jsonError.message}`);
            }
        }

        // 🎯 ПОЗИЦИИ ИЗ ЦЕНТРАЛИЗОВАННОГО КОНФИГА (ЕДИНСТВЕННЫЙ ИСТОЧНИК ИСТИНЫ!)
        const { getMeteoraPositions } = require('./trading-config');
        const centralizedPositions = getMeteoraPositions();

        this.positions = [
            {
                address: centralizedPositions.POOL_1, // ✅ ИЗ TRADING-CONFIG
                pool: '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6',
                name: 'POOL_1'
            },
            {
                address: centralizedPositions.POOL_2, // ✅ ИЗ TRADING-CONFIG
                pool: 'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y',
                name: 'POOL_2'
            }
        ];

        // 🔥 ПРАВИЛЬНЫЙ RENT ДЕПОЗИТ ПО ПОЗИЦИИ
        this.RENT_PER_POSITION = 0.057; // SOL (ОФИЦИАЛЬНАЯ СУММА!)
    }

    // 🔥 СОЗДАНИЕ DLMM POOL ИНСТАНСА (ОФИЦИАЛЬНЫЙ SDK)
    async createDlmmPool(poolAddress) {
        console.log(`   🔧 Создание DLMM pool инстанса для ${poolAddress}`);

        try {
            const dlmmPool = await DLMM.create(this.connection, new PublicKey(poolAddress));
            console.log(`   ✅ DLMM pool создан успешно`);
            return dlmmPool;
        } catch (error) {
            console.log(`   ❌ Ошибка создания DLMM pool: ${error.message}`);
            throw error;
        }
    }

    // 🔥 ОСНОВНАЯ ФУНКЦИЯ ЗАКРЫТИЯ ПОЗИЦИЙ (ОФИЦИАЛЬНЫЙ SDK)
    async closeAllPositions() {
        console.log('🔥 ЗАКРЫТИЕ ВСЕХ ПОЗИЦИЙ - ВОЗВРАТ RENT ДЕПОЗИТА (0.057 SOL)');
        console.log(`💰 Wallet: ${this.wallet.publicKey.toString()}`);
        console.log(`📊 Позиций для закрытия: ${this.positions.length}`);

        try {
            // 🔥 ЗАКРЫВАЕМ КАЖДУЮ ПОЗИЦИЮ ЧЕРЕЗ ОФИЦИАЛЬНЫЙ SDK
            for (const position of this.positions) {
                console.log(`\n📊 ${position.name}: ${position.address}`);
                console.log(`   Pool: ${position.pool}`);

                // 🔥 СОЗДАЕМ DLMM POOL ИНСТАНС
                const dlmmPool = await this.createDlmmPool(position.pool);

                // 🔥 ПРОВЕРЯЕМ ПОЗИЦИЮ ПЕРЕД ЗАКРЫТИЕМ
                console.log(`   🔍 Проверка позиции перед закрытием...`);
                const { userPositions } = await dlmmPool.getPositionsByUserAndLbPair(this.wallet.publicKey);
                const targetPosition = userPositions.find(pos => pos.publicKey.toString() === position.address);

                if (!targetPosition) {
                    console.log(`   ⚠️ Позиция ${position.address} не найдена или уже закрыта`);
                    continue;
                }

                console.log(`   ✅ Позиция найдена, ликвидность: ${targetPosition.positionData.totalXAmount.toString()}, ${targetPosition.positionData.totalYAmount.toString()}`);
                console.log(`   🔍 Структура позиции:`, Object.keys(targetPosition));
                console.log(`   🔍 PublicKey позиции:`, targetPosition.publicKey.toString());

                // 🔥 СОЗДАЕМ CLOSE POSITION ТРАНЗАКЦИЮ (РАЗНЫЕ ВАРИАНТЫ ПАРАМЕТРОВ)
                console.log(`   🔧 Создание closePosition транзакции...`);

                // Попробуем разные варианты параметров
                let closePositionTx;
                try {
                    // Вариант 1: Только position
                    closePositionTx = await dlmmPool.closePosition({
                        position: targetPosition.publicKey
                    });
                } catch (error1) {
                    console.log(`   ⚠️ Вариант 1 не сработал: ${error1.message}`);
                    try {
                        // Вариант 2: owner + position
                        closePositionTx = await dlmmPool.closePosition({
                            owner: this.wallet.publicKey,
                            position: targetPosition.publicKey
                        });
                    } catch (error2) {
                        console.log(`   ⚠️ Вариант 2 не сработал: ${error2.message}`);
                        // Вариант 3: Передаем весь объект позиции
                        closePositionTx = await dlmmPool.closePosition({
                            owner: this.wallet.publicKey,
                            position: targetPosition
                        });
                    }
                }

                console.log(`   📊 Транзакция создана: ${closePositionTx.instructions.length} инструкций`);

                // 🔥 ОТПРАВЛЯЕМ ТРАНЗАКЦИЮ (МИНИМАЛЬНАЯ КОМИССИЯ)
                console.log(`   ⚡ Отправка транзакции...`);
                const signature = await sendAndConfirmTransaction(
                    this.connection,
                    closePositionTx,
                    [this.wallet],
                    {
                        skipPreflight: true,  // 🔥 ЭКОНОМИЯ ВРЕМЕНИ
                        preflightCommitment: "singleGossip"
                    }
                );

                console.log(`   ✅ ${position.name} ЗАКРЫТА!`);
                console.log(`   Signature: ${signature}`);
                console.log(`   💰 Rent возвращен: ${this.RENT_PER_POSITION} SOL`);
            }

            console.log(`\n🎉 ВСЕ ПОЗИЦИИ УСПЕШНО ЗАКРЫТЫ!`);

            // 🔥 ПОКАЗЫВАЕМ ПРАВИЛЬНУЮ ЭКОНОМИЮ (0.057 SOL * 2)
            const totalRentReturned = this.RENT_PER_POSITION * this.positions.length;
            console.log(`   💵 Общий возврат rent: ${totalRentReturned} SOL`);
            console.log(`   💸 Сетевая комиссия: ~0.00001 SOL (минимальная)`);
            console.log(`   💰 Чистая прибыль: ~${(totalRentReturned - 0.00001).toFixed(5)} SOL`);

            return true;

        } catch (error) {
            console.log(`❌ ОШИБКА ЗАКРЫТИЯ ПОЗИЦИЙ:`);
            console.log(`   💥 Сообщение: ${error.message}`);
            console.log(`   📋 Тип ошибки: ${error.constructor.name}`);
            if (error.logs) {
                console.log(`   📋 Логи: ${JSON.stringify(error.logs, null, 2)}`);
            }
            return false;
        }
    }
}

// 🔥 ЗАПУСК СКРИПТА
async function main() {
    console.log('🔥 CLOSE POSITIONS - ЗАПУСК\n');
    
    const closer = new PositionCloser();
    const success = await closer.closeAllPositions();
    
    if (success) {
        console.log('\n✅ СКРИПТ ЗАВЕРШЕН УСПЕШНО');
        process.exit(0);
    } else {
        console.log('\n❌ СКРИПТ ЗАВЕРШЕН С ОШИБКОЙ');
        process.exit(1);
    }
}

// Запуск
main().catch(console.error);
