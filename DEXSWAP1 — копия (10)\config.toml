rpc_url = "https://api.devnet.solana.com"
websocket_url = "wss://api.devnet.solana.com"
max_threads = 8
scan_interval_seconds = 60
database_path = "bug_hunter.db"
output_directory = "output"

[[bug_bounty_programs]]
name = "Solana Foundation"
email = "<EMAIL>"
max_reward = 1000000
submission_format = "email"

[[bug_bounty_programs]]
name = "Immunefi"
api_endpoint = "https://immunefi.com/api/submit"
api_key = "YOUR_IMMUNEFI_API_KEY"
max_reward = 1000000
submission_format = "api"

[[bug_bounty_programs]]
name = "Jupiter"
email = "<EMAIL>"
max_reward = 500000
submission_format = "email"

[[bug_bounty_programs]]
name = "Marinade Finance"
email = "<EMAIL>"
max_reward = 1000000
submission_format = "email"

[[bug_bounty_programs]]
name = "Raydium"
email = "<EMAIL>"
max_reward = 250000
submission_format = "email"

[[bug_bounty_programs]]
name = "Orca"
email = "<EMAIL>"
max_reward = 200000
submission_format = "email"

[[bug_bounty_programs]]
name = "Mango Markets"
email = "<EMAIL>"
max_reward = 500000
submission_format = "email"

[email_config]
smtp_server = "smtp.gmail.com"
smtp_port = 587
username = "<EMAIL>"
password = "your-app-password"
from_address = "<EMAIL>"
