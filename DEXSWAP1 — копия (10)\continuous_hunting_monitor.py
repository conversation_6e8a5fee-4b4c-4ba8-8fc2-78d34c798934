#!/usr/bin/env python3
"""
🔄 CONTINUOUS HUNTING MONITOR
Непрерывный мониторинг и автоматическое тестирование
"""

import asyncio
import json
import time
from datetime import datetime, timedelta
import logging
import signal
import sys

from master_bug_hunting_coordinator import MasterBugHuntingCoordinator

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('continuous_hunting.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ContinuousHuntingMonitor:
    """Непрерывный мониторинг системы поиска уязвимостей"""
    
    def __init__(self):
        self.running = True
        self.cycle_count = 0
        self.total_vulnerabilities = 0
        self.total_targets_tested = 0
        self.start_time = datetime.now()
        
        # Конфигурация
        self.config = {
            'cycle_interval_minutes': 30,      # Интервал между циклами
            'max_targets_per_cycle': 10,       # Максимум целей за цикл
            'min_cycle_gap_minutes': 5,        # Минимальный перерыв между циклами
            'max_continuous_hours': 24,        # Максимум непрерывной работы
            'vulnerability_threshold': 50,     # Порог для остановки
            'error_retry_minutes': 10,         # Пауза при ошибках
        }
        
        # Статистика
        self.stats = {
            'cycles_completed': 0,
            'total_vulnerabilities': 0,
            'total_targets_tested': 0,
            'total_strategies_executed': 0,
            'avg_vulnerabilities_per_cycle': 0,
            'avg_cycle_duration': 0,
            'last_cycle_time': None,
            'errors_count': 0,
            'uptime_hours': 0
        }
        
        # Обработчик сигналов для graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Обработчик сигналов для корректного завершения"""
        logger.info(f"🛑 Получен сигнал {signum}, завершение работы...")
        self.running = False
    
    async def run_continuous_monitoring(self):
        """Запуск непрерывного мониторинга"""
        logger.info("🚀 ЗАПУСК НЕПРЕРЫВНОГО МОНИТОРИНГА")
        logger.info("=" * 60)
        logger.info(f"⚙️ Конфигурация:")
        logger.info(f"   Интервал циклов: {self.config['cycle_interval_minutes']} минут")
        logger.info(f"   Целей за цикл: {self.config['max_targets_per_cycle']}")
        logger.info(f"   Максимум работы: {self.config['max_continuous_hours']} часов")
        logger.info(f"   Порог уязвимостей: {self.config['vulnerability_threshold']}")
        
        try:
            while self.running:
                cycle_start = time.time()
                
                # Проверка времени работы
                uptime_hours = (datetime.now() - self.start_time).total_seconds() / 3600
                if uptime_hours >= self.config['max_continuous_hours']:
                    logger.info(f"⏰ Достигнут лимит времени работы ({self.config['max_continuous_hours']}ч)")
                    break
                
                # Проверка порога уязвимостей
                if self.total_vulnerabilities >= self.config['vulnerability_threshold']:
                    logger.info(f"🎯 Достигнут порог уязвимостей ({self.config['vulnerability_threshold']})")
                    break
                
                try:
                    # Выполнение цикла поиска
                    cycle_result = await self._execute_hunting_cycle()
                    
                    if cycle_result:
                        # Обновление статистики
                        self._update_statistics(cycle_result, time.time() - cycle_start)
                        
                        # Вывод статистики цикла
                        self._print_cycle_summary(cycle_result)
                        
                        # Проверка на критические находки
                        if cycle_result.get('session_info', {}).get('vulnerabilities_found', 0) > 5:
                            logger.warning(f"🚨 КРИТИЧЕСКИЕ НАХОДКИ: {cycle_result['session_info']['vulnerabilities_found']} уязвимостей!")
                    
                    # Пауза между циклами
                    await self._wait_for_next_cycle()
                    
                except Exception as e:
                    logger.error(f"❌ Ошибка в цикле {self.cycle_count + 1}: {e}")
                    self.stats['errors_count'] += 1
                    
                    # Пауза при ошибке
                    error_wait = self.config['error_retry_minutes'] * 60
                    logger.info(f"⏸️ Пауза {self.config['error_retry_minutes']} минут после ошибки...")
                    await asyncio.sleep(error_wait)
                    continue
        
        except KeyboardInterrupt:
            logger.info("🛑 Получен сигнал прерывания")
        except Exception as e:
            logger.error(f"❌ Критическая ошибка мониторинга: {e}")
        finally:
            await self._generate_final_report()
    
    async def _execute_hunting_cycle(self):
        """Выполнение одного цикла поиска"""
        self.cycle_count += 1
        logger.info(f"🔄 ЦИКЛ {self.cycle_count}")
        logger.info("-" * 40)
        
        try:
            async with MasterBugHuntingCoordinator() as coordinator:
                # Получение статуса перед циклом
                status = await coordinator.get_real_time_status()
                logger.info(f"📊 Статус: {status['loaded_strategies']} стратегий, очередь: {status['retest_queue_size']}")
                
                # Выполнение полного цикла
                result = await coordinator.run_full_hunting_cycle(
                    max_targets=self.config['max_targets_per_cycle']
                )
                
                return result
                
        except Exception as e:
            logger.error(f"Ошибка выполнения цикла: {e}")
            raise
    
    def _update_statistics(self, cycle_result, cycle_duration):
        """Обновление статистики"""
        session_info = cycle_result.get('session_info', {})
        
        self.stats['cycles_completed'] += 1
        self.stats['total_vulnerabilities'] += session_info.get('vulnerabilities_found', 0)
        self.stats['total_targets_tested'] += session_info.get('targets_processed', 0)
        self.stats['total_strategies_executed'] += session_info.get('strategies_executed', 0)
        self.stats['last_cycle_time'] = datetime.now()
        
        # Средние значения
        if self.stats['cycles_completed'] > 0:
            self.stats['avg_vulnerabilities_per_cycle'] = self.stats['total_vulnerabilities'] / self.stats['cycles_completed']
            self.stats['avg_cycle_duration'] = (self.stats.get('total_cycle_time', 0) + cycle_duration) / self.stats['cycles_completed']
        
        self.stats['total_cycle_time'] = self.stats.get('total_cycle_time', 0) + cycle_duration
        self.stats['uptime_hours'] = (datetime.now() - self.start_time).total_seconds() / 3600
        
        # Обновление глобальных счетчиков
        self.total_vulnerabilities = self.stats['total_vulnerabilities']
        self.total_targets_tested = self.stats['total_targets_tested']
    
    def _print_cycle_summary(self, cycle_result):
        """Вывод сводки по циклу"""
        session_info = cycle_result.get('session_info', {})
        efficiency = cycle_result.get('efficiency_metrics', {})
        
        logger.info(f"✅ ЦИКЛ {self.cycle_count} ЗАВЕРШЕН:")
        logger.info(f"   🎯 Целей: {session_info.get('targets_processed', 0)}")
        logger.info(f"   ⚡ Стратегий: {session_info.get('strategies_executed', 0)}")
        logger.info(f"   🐛 Уязвимостей: {session_info.get('vulnerabilities_found', 0)}")
        logger.info(f"   ⏱️ Время: {session_info.get('cycle_duration', 0):.1f}с")
        logger.info(f"   📊 Эффективность: {efficiency.get('vulnerability_discovery_rate', 0):.1%}")
        
        # Общая статистика
        logger.info(f"📈 ОБЩАЯ СТАТИСТИКА:")
        logger.info(f"   🔄 Циклов: {self.stats['cycles_completed']}")
        logger.info(f"   🎯 Всего целей: {self.stats['total_targets_tested']}")
        logger.info(f"   🐛 Всего уязвимостей: {self.stats['total_vulnerabilities']}")
        logger.info(f"   ⚡ Всего стратегий: {self.stats['total_strategies_executed']}")
        logger.info(f"   📊 Среднее за цикл: {self.stats['avg_vulnerabilities_per_cycle']:.1f} уязвимостей")
        logger.info(f"   ⏰ Время работы: {self.stats['uptime_hours']:.1f} часов")
    
    async def _wait_for_next_cycle(self):
        """Ожидание следующего цикла"""
        wait_minutes = max(
            self.config['cycle_interval_minutes'],
            self.config['min_cycle_gap_minutes']
        )
        
        logger.info(f"⏸️ Пауза {wait_minutes} минут до следующего цикла...")
        
        # Ожидание с возможностью прерывания
        for i in range(wait_minutes * 60):
            if not self.running:
                break
            await asyncio.sleep(1)
    
    async def _generate_final_report(self):
        """Генерация финального отчета"""
        logger.info("📄 ГЕНЕРАЦИЯ ФИНАЛЬНОГО ОТЧЕТА")
        logger.info("=" * 60)
        
        end_time = datetime.now()
        total_duration = (end_time - self.start_time).total_seconds()
        
        final_report = {
            'monitoring_session': {
                'start_time': self.start_time.isoformat(),
                'end_time': end_time.isoformat(),
                'total_duration_hours': total_duration / 3600,
                'reason_for_stop': self._get_stop_reason()
            },
            'statistics': self.stats,
            'performance_metrics': {
                'vulnerabilities_per_hour': self.stats['total_vulnerabilities'] / (total_duration / 3600) if total_duration > 0 else 0,
                'targets_per_hour': self.stats['total_targets_tested'] / (total_duration / 3600) if total_duration > 0 else 0,
                'strategies_per_hour': self.stats['total_strategies_executed'] / (total_duration / 3600) if total_duration > 0 else 0,
                'success_rate': (self.stats['cycles_completed'] - self.stats['errors_count']) / self.stats['cycles_completed'] if self.stats['cycles_completed'] > 0 else 0
            },
            'recommendations': self._generate_recommendations()
        }
        
        # Сохранение отчета
        report_filename = f"continuous_hunting_final_report_{int(time.time())}.json"
        with open(report_filename, 'w', encoding='utf-8') as f:
            json.dump(final_report, f, indent=2, ensure_ascii=False, default=str)
        
        # Вывод итоговой статистики
        logger.info(f"⏱️ ОБЩЕЕ ВРЕМЯ РАБОТЫ: {total_duration / 3600:.1f} часов")
        logger.info(f"🔄 ЦИКЛОВ ВЫПОЛНЕНО: {self.stats['cycles_completed']}")
        logger.info(f"🎯 ЦЕЛЕЙ ПРОТЕСТИРОВАНО: {self.stats['total_targets_tested']}")
        logger.info(f"🐛 УЯЗВИМОСТЕЙ НАЙДЕНО: {self.stats['total_vulnerabilities']}")
        logger.info(f"⚡ СТРАТЕГИЙ ВЫПОЛНЕНО: {self.stats['total_strategies_executed']}")
        logger.info(f"📊 ПРОИЗВОДИТЕЛЬНОСТЬ:")
        logger.info(f"   Уязвимостей/час: {final_report['performance_metrics']['vulnerabilities_per_hour']:.1f}")
        logger.info(f"   Целей/час: {final_report['performance_metrics']['targets_per_hour']:.1f}")
        logger.info(f"   Успешность: {final_report['performance_metrics']['success_rate']:.1%}")
        logger.info(f"💾 Отчет сохранен: {report_filename}")
        
        # Рекомендации
        logger.info(f"💡 РЕКОМЕНДАЦИИ:")
        for rec in final_report['recommendations']:
            logger.info(f"   {rec}")
    
    def _get_stop_reason(self):
        """Определение причины остановки"""
        if not self.running:
            return "manual_stop"
        elif self.stats['uptime_hours'] >= self.config['max_continuous_hours']:
            return "time_limit_reached"
        elif self.stats['total_vulnerabilities'] >= self.config['vulnerability_threshold']:
            return "vulnerability_threshold_reached"
        else:
            return "unknown"
    
    def _generate_recommendations(self):
        """Генерация рекомендаций"""
        recommendations = []
        
        if self.stats['total_vulnerabilities'] > 20:
            recommendations.append("🎯 Найдено много уязвимостей - начните подготовку отчетов для bug bounty")
        
        if self.stats['avg_vulnerabilities_per_cycle'] > 5:
            recommendations.append("📈 Высокая эффективность - рассмотрите увеличение частоты циклов")
        
        if self.stats['errors_count'] > self.stats['cycles_completed'] * 0.1:
            recommendations.append("⚠️ Много ошибок - проверьте стабильность системы")
        
        if self.stats['avg_cycle_duration'] > 300:  # 5 минут
            recommendations.append("⚡ Долгие циклы - рассмотрите оптимизацию стратегий")
        
        if not recommendations:
            recommendations.append("✅ Система работает стабильно - продолжайте мониторинг")
        
        return recommendations

async def main():
    """Главная функция"""
    print("🔄 CONTINUOUS HUNTING MONITOR")
    print("Нажмите Ctrl+C для остановки")
    print("=" * 60)
    
    monitor = ContinuousHuntingMonitor()
    await monitor.run_continuous_monitoring()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Мониторинг остановлен пользователем")
    except Exception as e:
        print(f"\n❌ Критическая ошибка: {e}")
        sys.exit(1)
