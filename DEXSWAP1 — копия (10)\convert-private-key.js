/**
 * 🔐 КОНВЕРТАЦИЯ ПРИВАТНОГО КЛЮЧА ИЗ BASE58 В WALLET.JSON
 */

const { Keypair } = require('@solana/web3.js');
const fs = require('fs');

// Простая функция для декодирования base58 без внешних зависимостей
function base58Decode(str) {
  const alphabet = '**********************************************************';
  let decoded = 0n;
  let multi = 1n;

  for (let i = str.length - 1; i >= 0; i--) {
    const char = str[i];
    const index = alphabet.indexOf(char);
    if (index === -1) throw new Error(`Invalid character: ${char}`);
    decoded += BigInt(index) * multi;
    multi *= 58n;
  }

  // Конвертируем в байты
  const bytes = [];
  while (decoded > 0n) {
    bytes.unshift(Number(decoded % 256n));
    decoded = decoded / 256n;
  }

  // Добавляем ведущие нули
  for (let i = 0; i < str.length && str[i] === '1'; i++) {
    bytes.unshift(0);
  }

  return new Uint8Array(bytes);
}

// Приватный ключ из .env.solana
const privateKeyBase58 = '2LsQ2H5p5Las865haJACjSnFC8FoFGa8rmq5KRKHVwmh16WPqgCqYbdR6Gy57LrJGYyw4RbvryLAgCEufdtkbZXu';

console.log('🔐 КОНВЕРТАЦИЯ ПРИВАТНОГО КЛЮЧА');
console.log('═'.repeat(50));

try {
  // Декодируем base58 в байты
  const privateKeyBytes = base58Decode(privateKeyBase58);
  console.log(`✅ Приватный ключ декодирован: ${privateKeyBytes.length} байт`);

  if (privateKeyBytes.length !== 64) {
    throw new Error(`Неправильная длина приватного ключа: ${privateKeyBytes.length} байт (ожидается 64)`);
  }

  // Создаем Keypair для проверки
  const keypair = Keypair.fromSecretKey(privateKeyBytes);
  const address = keypair.publicKey.toString();

  console.log(`📍 Адрес из приватного ключа: ${address}`);
  console.log(`📍 Ожидаемый адрес:           bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV`);

  const addressMatch = address === 'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV';
  console.log(`🔍 Адреса совпадают: ${addressMatch ? '✅ ДА' : '❌ НЕТ'}`);

  if (addressMatch) {
    // Конвертируем в формат wallet.json (массив чисел)
    const walletJsonArray = Array.from(privateKeyBytes);

    console.log('\n🔄 ОБНОВЛЯЕМ WALLET.JSON...');

    // Создаем резервную копию
    if (fs.existsSync('wallet.json')) {
      fs.copyFileSync('wallet.json', 'wallet.json.backup');
      console.log('✅ Создана резервная копия: wallet.json.backup');
    }

    // Записываем новый wallet.json
    fs.writeFileSync('wallet.json', JSON.stringify(walletJsonArray, null, 2));
    console.log('✅ Wallet.json обновлен с правильным приватным ключом!');

    console.log('\n🎯 РЕЗУЛЬТАТ:');
    console.log(`📍 Адрес кошелька: ${address}`);
    console.log(`💰 Теперь система будет читать баланс с правильного адреса!`);

  } else {
    console.log('\n❌ ОШИБКА: Приватный ключ не соответствует ожидаемому адресу!');
    console.log('Проверьте правильность приватного ключа в .env.solana');
  }

} catch (error) {
  console.error(`❌ Ошибка конвертации: ${error.message}`);
}
