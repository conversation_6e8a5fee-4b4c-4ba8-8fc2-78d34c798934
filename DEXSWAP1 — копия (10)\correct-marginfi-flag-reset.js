#!/usr/bin/env node

/**
 * 🔧 ПРАВИЛЬНЫЙ СБРОС ФЛАГА MARGINFI FLASH LOAN
 * ═══════════════════════════════════════════════════════════════
 * 🎯 ОСНОВАНО НА АНАЛИЗЕ КОДА И ОФИЦИАЛЬНОЙ ДОКУМЕНТАЦИИ
 * 💡 ИСПОЛЬЗУЕТ ПРАВИЛЬНЫЕ projected balances ИЗ ЗАВИСШЕГО АККАУНТА
 */

const { Connection, PublicKey, Keypair, Transaction } = require('@solana/web3.js');
const { MarginfiClient, getConfig } = require('@mrgnlabs/marginfi-client-v2');
const { NodeWallet } = require('@mrgnlabs/mrgn-common');
const fs = require('fs');

class CorrectMarginFiFlagReset {
  constructor() {
    this.connection = new Connection('https://billowing-empty-patron.solana-mainnet.quiknode.pro/c79212a7508ec6a37353793d9be93c917898e858/', 'confirmed');
    this.wallet = null;
    this.marginfiClient = null;
    this.marginfiAccount = null;
  }

  async initialize() {
    try {
      console.log('🔧 ПРАВИЛЬНЫЙ СБРОС MARGINFI FLASH LOAN ФЛАГА');
      console.log('═══════════════════════════════════════════════════════════════');
      console.log('📚 Основано на анализе monitor-flashloan-state.js');
      console.log('🎯 Использует правильные projected balances');

      // Загружаем wallet
      const walletData = JSON.parse(fs.readFileSync('wallet.json', 'utf8'));
      const keypair = Keypair.fromSecretKey(new Uint8Array(walletData));
      this.wallet = new NodeWallet(keypair);
      console.log(`💼 Wallet: ${this.wallet.publicKey.toString()}`);

      // Создаем MarginFi client
      const config = getConfig('production');
      this.marginfiClient = await MarginfiClient.fetch(config, this.wallet, this.connection);
      console.log('✅ MarginFi client создан');

      // Получаем MarginFi аккаунт
      const accounts = await this.marginfiClient.getMarginfiAccountsForAuthority();
      if (accounts.length > 0) {
        this.marginfiAccount = accounts[0];
        console.log(`✅ MarginFi аккаунт: ${this.marginfiAccount.address.toString()}`);
      } else {
        throw new Error('MarginFi аккаунты не найдены');
      }

      return true;
    } catch (error) {
      console.error(`❌ Ошибка инициализации: ${error.message}`);
      return false;
    }
  }

  // МЕТОД 1: ПРОВЕРКА ФЛАГА ЧЕРЕЗ ПОПЫТКУ СОЗДАНИЯ FLASH LOAN (САМЫЙ НАДЕЖНЫЙ!)
  async checkFlashLoanFlag() {
    console.log('\n🧪 ПРОВЕРКА ФЛАГА ЧЕРЕЗ ТЕСТ FLASH LOAN');
    console.log('─────────────────────────────────────────────────────────────');

    try {
      // Пытаемся создать пустой flash loan для теста
      const testFlashLoanTx = await this.marginfiAccount.buildFlashLoanTx({
        ixs: [], // Пустые инструкции для теста
        signers: []
      });

      console.log('✅ ФЛАГ СБРОШЕН! Flash loan можно создать');
      console.log(`📏 Размер тестовой транзакции: ${testFlashLoanTx.serialize().length} байт`);
      return { flagSet: false, canUseFlashLoan: true };

    } catch (error) {
      if (error.message.includes('6037') || error.message.includes('AccountInFlashloan')) {
        console.log('❌ ФЛАГ УСТАНОВЛЕН! Аккаунт заблокирован в flash loan');
        console.log(`🔍 Ошибка: ${error.message}`);
        return { flagSet: true, canUseFlashLoan: false, error: error.message };
      } else {
        console.log('❓ Другая ошибка (не связана с флагом)');
        console.log(`🔍 Ошибка: ${error.message}`);
        return { flagSet: false, canUseFlashLoan: false, error: error.message };
      }
    }
  }

  // МЕТОД 2: ПОЛУЧЕНИЕ ПРАВИЛЬНЫХ PROJECTED BALANCES ИЗ ЗАВИСШЕГО АККАУНТА
  async getCurrentProjectedBalances() {
    console.log('\n📊 ПОЛУЧЕНИЕ ТЕКУЩИХ PROJECTED BALANCES');
    console.log('─────────────────────────────────────────────────────────────');

    try {
      // Принудительно обновляем данные аккаунта
      await this.marginfiAccount.reload();

      const activeBalances = this.marginfiAccount.activeBalances;
      console.log(`💰 Активных балансов: ${activeBalances.length}`);

      const projectedBalances = [];

      for (const balance of activeBalances) {
        try {
          const bankPk = balance.bankPk;
          const bank = this.marginfiClient.getBankByPk(bankPk);
          
          if (bank) {
            const symbol = bank.config.assetSymbol;
            const assetShares = balance.assetShares.toNumber();
            const liabilityShares = balance.liabilityShares.toNumber();
            
            console.log(`\n🏦 ${symbol} (${bankPk.toString().slice(0, 8)}...):`);
            console.log(`   💰 Asset Shares: ${assetShares}`);
            console.log(`   💸 Liability Shares: ${liabilityShares}`);

            // Добавляем в projected balances для end flash loan
            if (assetShares > 0 || liabilityShares > 0) {
              projectedBalances.push({
                bankPk: bankPk,
                assetShares: balance.assetShares,
                liabilityShares: balance.liabilityShares
              });
              console.log(`   ✅ Добавлено в projected balances`);
            }
          }
        } catch (balanceError) {
          console.log(`   ❌ Ошибка обработки баланса: ${balanceError.message}`);
        }
      }

      console.log(`\n📋 ИТОГО projected balances: ${projectedBalances.length}`);
      return projectedBalances;

    } catch (error) {
      console.error(`❌ Ошибка получения balances: ${error.message}`);
      return [];
    }
  }

  // МЕТОД 3: ПРАВИЛЬНЫЙ СБРОС ФЛАГА С PROJECTED BALANCES
  async resetFlashLoanFlag() {
    console.log('\n🔧 ПРАВИЛЬНЫЙ СБРОС ФЛАГА FLASH LOAN');
    console.log('─────────────────────────────────────────────────────────────');

    try {
      // 1. Получаем правильные projected balances
      const projectedBalances = await this.getCurrentProjectedBalances();

      if (projectedBalances.length === 0) {
        console.log('⚠️ Нет активных балансов - используем пустой массив');
      }

      // 2. Создаем правильную end flash loan инструкцию
      console.log('🔧 Создаем end flash loan инструкцию с правильными balances...');
      
      const endFlashLoanIx = await this.marginfiAccount.makeEndFlashLoanIx(projectedBalances);

      if (!endFlashLoanIx || !endFlashLoanIx.instructions || endFlashLoanIx.instructions.length === 0) {
        throw new Error('Не удалось создать end flash loan инструкцию');
      }

      console.log(`✅ End flash loan инструкция создана: ${endFlashLoanIx.instructions.length} инструкций`);

      // 3. Создаем и отправляем транзакцию
      const transaction = new Transaction();
      endFlashLoanIx.instructions.forEach(ix => transaction.add(ix));

      console.log('📤 Отправляем транзакцию сброса флага...');

      const signature = await this.connection.sendTransaction(transaction, [this.wallet.payer], {
        skipPreflight: false,
        preflightCommitment: 'confirmed'
      });

      console.log(`📝 Транзакция отправлена: ${signature}`);

      // 4. Ждем подтверждения
      console.log('⏳ Ждем подтверждения...');
      const confirmation = await this.connection.confirmTransaction(signature, 'confirmed');

      if (confirmation.value.err) {
        throw new Error(`Транзакция провалена: ${JSON.stringify(confirmation.value.err)}`);
      }

      console.log('✅ Транзакция подтверждена!');
      return true;

    } catch (error) {
      console.error(`❌ Ошибка сброса флага: ${error.message}`);
      return false;
    }
  }

  // КОМПЛЕКСНАЯ ПРОЦЕДУРА СБРОСА
  async performCompleteReset() {
    console.log('\n🚀 КОМПЛЕКСНАЯ ПРОЦЕДУРА СБРОСА ФЛАГА');
    console.log('═══════════════════════════════════════════════════════════════');

    // 1. Проверяем текущее состояние флага
    const flagCheck = await this.checkFlashLoanFlag();

    if (flagCheck.canUseFlashLoan && !flagCheck.flagSet) {
      console.log('✅ Флаг уже сброшен! Flash loan работает!');
      return true;
    }

    if (!flagCheck.flagSet) {
      console.log('❓ Проблема не связана с флагом flash loan');
      console.log(`🔍 Ошибка: ${flagCheck.error}`);
      return false;
    }

    console.log('🔧 Флаг установлен - начинаем процедуру сброса...');

    // 2. Выполняем сброс флага
    const resetSuccess = await this.resetFlashLoanFlag();

    if (!resetSuccess) {
      console.log('❌ Не удалось сбросить флаг');
      return false;
    }

    // 3. Проверяем результат
    console.log('\n🔍 ПРОВЕРКА РЕЗУЛЬТАТА СБРОСА');
    console.log('─────────────────────────────────────────────────────────────');
    
    await new Promise(resolve => setTimeout(resolve, 3000)); // Ждем 3 секунды

    const finalCheck = await this.checkFlashLoanFlag();

    if (finalCheck.canUseFlashLoan && !finalCheck.flagSet) {
      console.log('\n🎉 УСПЕХ! ФЛАГ СБРОШЕН!');
      console.log('✅ Flash loan теперь работает!');
      console.log('💰 328.591 SOL flash loan доступен для арбитража!');
      return true;
    } else {
      console.log('\n❌ ФЛАГ НЕ СБРОШЕН');
      console.log('💡 Возможно, требуется повторная попытка или другой метод');
      return false;
    }
  }
}

async function main() {
  console.log('🚀 ЗАПУСК ПРАВИЛЬНОГО СБРОСА MARGINFI FLASH LOAN ФЛАГА');
  console.log('═══════════════════════════════════════════════════════════════');
  console.log('📚 Основано на анализе monitor-flashloan-state.js');
  console.log('🎯 Использует правильные projected balances из зависшего аккаунта');
  console.log('🛠️ Создает корректную lending_account_end_flashloan инструкцию');
  console.log('═══════════════════════════════════════════════════════════════');

  const reset = new CorrectMarginFiFlagReset();

  const initialized = await reset.initialize();
  if (!initialized) {
    console.log('❌ Не удалось инициализировать сброс');
    return;
  }

  const success = await reset.performCompleteReset();

  if (success) {
    console.log('\n🎉 FLASH LOAN ФЛАГ УСПЕШНО СБРОШЕН!');
    console.log('🚀 Теперь можно выполнять арбитраж!');
  } else {
    console.log('\n❌ НЕ УДАЛОСЬ СБРОСИТЬ ФЛАГ');
    console.log('💡 Попробуйте повторить или обратитесь к документации MarginFi');
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = CorrectMarginFiFlagReset;
