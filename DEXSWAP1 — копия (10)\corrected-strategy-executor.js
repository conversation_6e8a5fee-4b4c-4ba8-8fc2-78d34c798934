#!/usr/bin/env node

/**
 * 🔧 ИСПРАВЛЕННЫЙ ИСПОЛНИТЕЛЬ СТРАТЕГИИ
 * 
 * ПРАВИЛЬНЫЕ ПАРАМЕТРЫ:
 * - Flash Loan: $1,820,000 USDC
 * - Ликвидность: $1,400,000 USDC
 * - Торговля: $420,000 USDC (30% от ликвидности)
 * - Реальный ROI: 1.22%
 */

const Correct30PercentCalculator = require('./correct-30-percent-calculator.js');

class CorrectedStrategyExecutor {
    constructor() {
        // 🧮 ПРАВИЛЬНЫЙ КАЛЬКУЛЯТОР
        this.calculator = new Correct30PercentCalculator();
        
        // 🎯 ИСПРАВЛЕННЫЕ СТРАТЕГИЧЕСКИЕ ПАРАМЕТРЫ
        this.STRATEGY_PARAMS = {
            flash_loan_amount: 1820000,    // $1.82M USDC flash loan (ИСПРАВЛЕНО)
            liquidity_amount: 1400000,     // $1.4M USDC для ликвидности
            trading_amount: 420000,        // $420K USDC для торговли (30% от ликвидности)
            target_roi: 1.22               // 1.22% реальный ROI (ИСПРАВЛЕНО)
        };
        
        // 🔒 ИСПРАВЛЕННЫЕ ПРОВЕРКИ БЕЗОПАСНОСТИ
        this.safetyChecks = {
            minROI: 1.0,                  // Минимум 1% ROI (реалистично)
            maxTradingPercent: 30.0,      // Максимум 30% от ликвидности
            minLiquidityCoverage: 100.0,  // Минимум 100% покрытия
            requiredProfitability: 'PROFITABLE',
            maxSlippage: 1.0,             // Максимум 1% slippage
            minArbitrageSpread: 1.0,      // Минимум 1% арбитражный спред
            validateRule30Percent: true,   // Проверка правила 30%
            requireBalanceCheck: true      // Проверка баланса
        };

        // 📊 СТАТИСТИКА
        this.stats = {
            totalExecutions: 0,
            successfulExecutions: 0,
            totalProfit: 0,
            averageROI: 0,
            lastExecution: null
        };
        
        console.log('🔧 ИСПРАВЛЕННЫЙ STRATEGY EXECUTOR ИНИЦИАЛИЗИРОВАН');
        console.log('✅ Правило 30% включено');
        console.log('✅ Реалистичные параметры установлены');
    }

    /**
     * 🔍 ПРОВЕРКА СИСТЕМЫ
     */
    async checkSystem() {
        console.log('\n🔍 ПРОВЕРКА ИСПРАВЛЕННОЙ СИСТЕМЫ...');
        
        try {
            // Проверка правила 30%
            const tradingPercent = (this.STRATEGY_PARAMS.trading_amount / this.STRATEGY_PARAMS.liquidity_amount) * 100;
            console.log(`📊 Проверка правила 30%:`);
            console.log(`   Торговля: $${this.STRATEGY_PARAMS.trading_amount.toLocaleString()}`);
            console.log(`   Ликвидность: $${this.STRATEGY_PARAMS.liquidity_amount.toLocaleString()}`);
            console.log(`   Процент: ${tradingPercent.toFixed(1)}%`);
            console.log(`   ${tradingPercent <= 30 ? '✅ ПРАВИЛО 30% СОБЛЮДЕНО' : '❌ НАРУШЕНИЕ ПРАВИЛА 30%'}`);
            
            // Проверка Flash Loan
            console.log(`💰 Проверка Flash Loan:`);
            console.log(`   Сумма: $${this.STRATEGY_PARAMS.flash_loan_amount.toLocaleString()}`);
            console.log(`   ${this.STRATEGY_PARAMS.flash_loan_amount === 1820000 ? '✅ ПРАВИЛЬНАЯ СУММА' : '❌ НЕПРАВИЛЬНАЯ СУММА'}`);
            
            // Проверка целевого ROI
            console.log(`📈 Проверка целевого ROI:`);
            console.log(`   Цель: ${this.STRATEGY_PARAMS.target_roi}%`);
            console.log(`   ${this.STRATEGY_PARAMS.target_roi <= 2.0 ? '✅ РЕАЛИСТИЧНЫЙ ROI' : '❌ ЗАВЫШЕННЫЙ ROI'}`);
            
            console.log('\n✅ СИСТЕМА ПРОВЕРЕНА И ИСПРАВЛЕНА!');
            return { success: true };
            
        } catch (error) {
            console.error('❌ ОШИБКА ПРОВЕРКИ:', error.message);
            return { success: false, error: error.message };
        }
    }

    /**
     * 🧪 СИМУЛЯЦИЯ ИСПРАВЛЕННОЙ СТРАТЕГИИ
     */
    async simulateStrategy() {
        console.log('\n🧪 СИМУЛЯЦИЯ ИСПРАВЛЕННОЙ СТРАТЕГИИ...');
        
        try {
            // Запуск правильного калькулятора
            const result = await this.calculator.calculateWith30PercentRule();
            
            if (!result.success) {
                throw new Error(result.error);
            }
            
            const { profitability } = result;
            
            // Проверки безопасности
            console.log('\n🔒 ПРОВЕРКИ БЕЗОПАСНОСТИ:');
            
            // Проверка ROI
            const roiCheck = profitability.roi >= this.safetyChecks.minROI;
            console.log(`   ROI ${profitability.roi.toFixed(2)}% >= ${this.safetyChecks.minROI}%: ${roiCheck ? '✅' : '❌'}`);
            
            // Проверка правила 30%
            const tradingPercent = profitability.details.tradingPercent;
            const rule30Check = tradingPercent <= this.safetyChecks.maxTradingPercent;
            console.log(`   Правило 30% (${tradingPercent.toFixed(1)}%): ${rule30Check ? '✅' : '❌'}`);
            
            // Проверка прибыльности (ROI > 1%)
            const profitabilityCheck = profitability.roi >= 1.0;
            console.log(`   Прибыльность: ${profitabilityCheck ? '✅' : '❌'}`);

            const allChecksPass = roiCheck && rule30Check && profitabilityCheck;
            
            console.log(`\n🎯 РЕЗУЛЬТАТ СИМУЛЯЦИИ:`);
            console.log(`   💰 Чистая прибыль: $${profitability.netProfit.toFixed(0)}`);
            console.log(`   📈 ROI: ${profitability.roi.toFixed(2)}%`);
            console.log(`   ✅ Правило 30%: ${tradingPercent.toFixed(1)}%`);
            console.log(`   🔒 Все проверки: ${allChecksPass ? '✅ ПРОЙДЕНЫ' : '❌ ПРОВАЛЕНЫ'}`);
            
            // Обновляем статистику
            this.updateStats(profitability);
            
            return {
                success: allChecksPass,
                profitability: profitability,
                checks: {
                    roi: roiCheck,
                    rule30: rule30Check,
                    profitable: profitabilityCheck
                }
            };
            
        } catch (error) {
            console.error('❌ ОШИБКА СИМУЛЯЦИИ:', error.message);
            return { success: false, error: error.message };
        }
    }

    /**
     * 📊 ПОКАЗАТЬ СТАТИСТИКУ
     */
    showStats() {
        console.log('\n📊 СТАТИСТИКА ИСПРАВЛЕННОЙ СТРАТЕГИИ:');
        console.log('=' .repeat(60));
        
        console.log(`🎯 Параметры стратегии:`);
        console.log(`   Flash Loan: $${this.STRATEGY_PARAMS.flash_loan_amount.toLocaleString()}`);
        console.log(`   Ликвидность: $${this.STRATEGY_PARAMS.liquidity_amount.toLocaleString()}`);
        console.log(`   Торговля: $${this.STRATEGY_PARAMS.trading_amount.toLocaleString()}`);
        console.log(`   Процент торговли: ${((this.STRATEGY_PARAMS.trading_amount / this.STRATEGY_PARAMS.liquidity_amount) * 100).toFixed(1)}%`);
        console.log(`   Целевой ROI: ${this.STRATEGY_PARAMS.target_roi}%`);
        
        console.log(`\n📈 Статистика выполнений:`);
        console.log(`   Всего выполнений: ${this.stats.totalExecutions}`);
        console.log(`   Успешных: ${this.stats.successfulExecutions}`);
        console.log(`   Общая прибыль: $${this.stats.totalProfit.toFixed(0)}`);
        console.log(`   Средний ROI: ${this.stats.averageROI.toFixed(2)}%`);
        console.log(`   Последнее выполнение: ${this.stats.lastExecution || 'Нет'}`);
        
        console.log(`\n🔒 Проверки безопасности:`);
        console.log(`   Минимальный ROI: ${this.safetyChecks.minROI}%`);
        console.log(`   Максимум торговли: ${this.safetyChecks.maxTradingPercent}% от ликвидности`);
        console.log(`   Правило 30%: ${this.safetyChecks.validateRule30Percent ? 'Включено' : 'Отключено'}`);
        console.log(`   Проверка баланса: ${this.safetyChecks.requireBalanceCheck ? 'Включена' : 'Отключена'}`);
    }

    /**
     * 📊 ОБНОВЛЕНИЕ СТАТИСТИКИ
     */
    updateStats(profitability) {
        this.stats.totalExecutions++;
        if (profitability.isProfitable) {
            this.stats.successfulExecutions++;
            this.stats.totalProfit += profitability.netProfit;
        }
        this.stats.averageROI = this.stats.totalProfit / (this.stats.successfulExecutions || 1) / this.STRATEGY_PARAMS.flash_loan_amount * 100;
        this.stats.lastExecution = new Date().toISOString();
    }

    /**
     * 🎯 ПОЛНАЯ ПРОВЕРКА ИСПРАВЛЕНИЙ
     */
    async fullCheck() {
        console.log('🎯 ПОЛНАЯ ПРОВЕРКА ИСПРАВЛЕНИЙ');
        console.log('=' .repeat(80));
        
        // 1. Проверка системы
        const systemCheck = await this.checkSystem();
        
        // 2. Симуляция стратегии
        const simulationResult = await this.simulateStrategy();
        
        // 3. Показать статистику
        this.showStats();
        
        console.log('\n🎉 ПОЛНАЯ ПРОВЕРКА ЗАВЕРШЕНА!');
        console.log(`✅ Система: ${systemCheck.success ? 'ИСПРАВЛЕНА' : 'ТРЕБУЕТ ДОРАБОТКИ'}`);
        console.log(`✅ Симуляция: ${simulationResult.success ? 'УСПЕШНА' : 'ПРОВАЛЕНА'}`);
        
        return {
            systemCheck: systemCheck.success,
            simulation: simulationResult.success,
            overall: systemCheck.success && simulationResult.success
        };
    }
}

// 🧪 ОБРАБОТКА КОМАНД
async function handleCommand(command) {
    const executor = new CorrectedStrategyExecutor();
    
    switch (command) {
        case 'check':
            return await executor.checkSystem();
            
        case 'simulate':
            return await executor.simulateStrategy();
            
        case 'stats':
            executor.showStats();
            return { success: true };
            
        case 'full':
            return await executor.fullCheck();
            
        default:
            console.log('🔧 ИСПРАВЛЕННЫЙ STRATEGY EXECUTOR');
            console.log('Доступные команды:');
            console.log('  check    - Проверка исправленной системы');
            console.log('  simulate - Симуляция с правилом 30%');
            console.log('  stats    - Показать статистику');
            console.log('  full     - Полная проверка исправлений');
            return { success: true };
    }
}

// 🚀 ЗАПУСК
if (require.main === module) {
    const command = process.argv[2] || 'help';
    handleCommand(command).catch(console.error);
}

module.exports = CorrectedStrategyExecutor;
