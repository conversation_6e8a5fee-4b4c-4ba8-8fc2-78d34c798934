/**
 * 🔧 СОЗДАНИЕ LENDING BALANCE В MARGINFI АККАУНТЕ
 * 
 * Создает lending balance для SOL в MarginFi аккаунте
 * Решает ошибку 3002 - LendingAccountBalanceNotFound
 */

const { Connection, PublicKey, Keypair, TransactionInstruction, Transaction, SystemProgram } = require('@solana/web3.js');
const fs = require('fs');

class MarginFiBalanceCreator {
    constructor() {
        this.connection = new Connection('https://billowing-empty-patron.solana-mainnet.quiknode.pro/c79212a7508ec6a37353793d9be93c917898e858/', 'confirmed');
        
        // 🔥 MARGINFI КОНСТАНТЫ
        this.MARGINFI_PROGRAM_ID = new PublicKey('MFv2hWf************************************');
        this.MARGINFI_GROUP = new PublicKey('4qp6Fx6tnZkY5Wropq9wUYgtFxXKwE6viZxFHg3rdAG8');
        this.MARGINFI_ACCOUNT = new PublicKey('********************************************');
        
        // 🏦 SOL БАНК (будем создавать balance для SOL)
        this.SOL_BANK = new PublicKey('CCKtUs6Cgwo4aaQUmBPmyoApH2gUDErxNZCAntD6LYGh');
        this.SOL_MINT = new PublicKey('So********************************111111112');
        
        this.wallet = null;
    }

    async initialize() {
        try {
            console.log('🔧 ИНИЦИАЛИЗАЦИЯ СОЗДАНИЯ LENDING BALANCE');
            console.log('═══════════════════════════════════════════════════════════════');
            
            // Загружаем wallet
            const walletData = JSON.parse(fs.readFileSync('wallet.json', 'utf8'));
            const keypair = Keypair.fromSecretKey(new Uint8Array(walletData));
            this.wallet = { payer: keypair, publicKey: keypair.publicKey };
            
            console.log(`💼 Wallet: ${this.wallet.publicKey.toString()}`);
            console.log(`🏦 MarginFi аккаунт: ${this.MARGINFI_ACCOUNT.toString()}`);
            console.log(`💰 SOL банк: ${this.SOL_BANK.toString()}`);
            
            // Проверяем баланс SOL
            const balance = await this.connection.getBalance(this.wallet.publicKey);
            console.log(`💰 Баланс SOL: ${balance / 1e9} SOL`);
            
            if (balance < ********) { // 0.01 SOL
                console.log('⚠️ Низкий баланс SOL! Нужно минимум 0.01 SOL для создания balance');
            }
            
            return true;
        } catch (error) {
            console.error(`❌ Ошибка инициализации: ${error.message}`);
            return false;
        }
    }

    /**
     * 🔧 ПОЛУЧЕНИЕ ДАННЫХ SOL БАНКА
     */
    async getBankData() {
        try {
            console.log('\n🔧 ПОЛУЧЕНИЕ ДАННЫХ SOL БАНКА');
            console.log('═══════════════════════════════════════════════════════════════');
            
            const bankInfo = await this.connection.getAccountInfo(this.SOL_BANK);
            if (!bankInfo) {
                throw new Error('SOL банк не найден');
            }
            
            console.log('✅ SOL банк найден');
            console.log(`📏 Размер данных: ${bankInfo.data.length} bytes`);
            
            // 🔥 ПРАВИЛЬНАЯ СТРУКТУРА SOL БАНКА ИЗ АНАЛИЗА:
            // Offset 8: SOL mint
            // Offset 112: Vault (liquidity vault) - ПРАВИЛЬНЫЙ!
            // Offset 146: Другой token account

            const data = bankInfo.data;
            const mint = new PublicKey(data.slice(8, 40));
            const vault = new PublicKey(data.slice(112, 144)); // ИСПРАВЛЕНО: offset 112!
            const vaultAuthority = new PublicKey(data.slice(72, 104)); // Пока оставляем старый
            
            console.log(`🪙 Mint: ${mint.toString()}`);
            console.log(`🏦 Vault: ${vault.toString()}`);
            console.log(`🔑 Vault Authority: ${vaultAuthority.toString()}`);
            
            // Проверяем что это действительно SOL банк
            if (!mint.equals(this.SOL_MINT)) {
                throw new Error(`Неправильный mint! Ожидался SOL, получен ${mint.toString()}`);
            }
            
            return {
                mint,
                vault,
                vaultAuthority
            };
            
        } catch (error) {
            console.error(`❌ Ошибка получения данных банка: ${error.message}`);
            throw error;
        }
    }

    /**
     * 🔧 ПОЛУЧЕНИЕ ИЛИ СОЗДАНИЕ WRAPPED SOL ACCOUNT
     */
    async getOrCreateWrappedSOLAccount() {
        try {
            // Для SOL депозитов в MarginFi нужен wrapped SOL account
            // Используем стандартный associated token account для WSOL
            const { getAssociatedTokenAddress } = require('@solana/spl-token');

            const wsolMint = new PublicKey('So********************************111111112');
            const wsolAccount = await getAssociatedTokenAddress(
                wsolMint,
                this.wallet.publicKey
            );

            console.log(`🔧 Wrapped SOL account: ${wsolAccount.toString()}`);
            return wsolAccount;

        } catch (error) {
            console.error(`❌ Ошибка получения wrapped SOL account: ${error.message}`);
            // Fallback: используем сам кошелек
            return this.wallet.publicKey;
        }
    }

    /**
     * 🔧 СОЗДАНИЕ DEPOSIT ИНСТРУКЦИИ
     */
    async createDepositInstruction(bankData, amount) {
        try {
            console.log('\n🔧 СОЗДАНИЕ DEPOSIT ИНСТРУКЦИИ');
            console.log('═══════════════════════════════════════════════════════════════');
            
            console.log(`💰 Сумма депозита: ${amount / 1e9} SOL`);
            console.log('🎯 Цель: Создать lending balance для SOL в MarginFi аккаунте');
            
            // 🔥 ИСПРАВЛЕННЫЕ АККАУНТЫ ДЛЯ lending_account_deposit
            // ПРОБЛЕМА: InvalidAccountData означает неправильную структуру аккаунтов
            const accounts = [
                // 0: marginfi_group
                { pubkey: this.MARGINFI_GROUP, isSigner: false, isWritable: false },
                // 1: marginfi_account
                { pubkey: this.MARGINFI_ACCOUNT, isSigner: false, isWritable: true },
                // 2: authority (signer)
                { pubkey: this.wallet.publicKey, isSigner: true, isWritable: false },
                // 3: bank
                { pubkey: this.SOL_BANK, isSigner: false, isWritable: true },
                // 4: signer_token_account (для SOL это wrapped SOL account)
                { pubkey: await this.getOrCreateWrappedSOLAccount(), isSigner: false, isWritable: true },
                // 5: bank_liquidity_vault
                { pubkey: bankData.vault, isSigner: false, isWritable: true },
                // 6: token_program
                { pubkey: new PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA'), isSigner: false, isWritable: false },
                // 7: system_program (нужен для SOL депозитов)
                { pubkey: new PublicKey('********************************'), isSigner: false, isWritable: false }
            ];
            
            console.log('📋 Аккаунты для deposit инструкции:');
            accounts.forEach((acc, i) => {
                console.log(`   ${i}: ${acc.pubkey.toString()} ${acc.isSigner ? '(signer)' : ''} ${acc.isWritable ? '(writable)' : ''}`);
            });
            
            // 🔥 ПРАВИЛЬНЫЙ DISCRIMINATOR ИЗ IDL
            // lending_account_deposit: [171, 94, 235, 103, 82, 64, 212, 140]
            const discriminator = Buffer.from([171, 94, 235, 103, 82, 64, 212, 140]);
            
            // Amount (8 bytes, little endian)
            const amountBuffer = Buffer.alloc(8);
            amountBuffer.writeBigUInt64LE(BigInt(amount), 0);

            // deposit_up_to_limit (Option<bool>) - используем None (0)
            const depositUpToLimitBuffer = Buffer.from([0]); // None

            const instructionData = Buffer.concat([discriminator, amountBuffer, depositUpToLimitBuffer]);
            
            console.log(`📋 Instruction data: ${instructionData.toString('hex')}`);
            console.log(`📏 Размер: ${instructionData.length} bytes`);
            
            const instruction = new TransactionInstruction({
                keys: accounts,
                programId: this.MARGINFI_PROGRAM_ID,
                data: instructionData
            });
            
            console.log('✅ Deposit инструкция создана');
            
            return instruction;
            
        } catch (error) {
            console.error(`❌ Ошибка создания deposit инструкции: ${error.message}`);
            throw error;
        }
    }

    /**
     * 🚀 ВЫПОЛНЕНИЕ DEPOSIT ОПЕРАЦИИ
     */
    async executeDeposit(depositInstruction) {
        try {
            console.log('\n🚀 ВЫПОЛНЕНИЕ DEPOSIT ОПЕРАЦИИ');
            console.log('═══════════════════════════════════════════════════════════════');
            
            // Создаем транзакцию
            const transaction = new Transaction();
            transaction.add(depositInstruction);
            
            // Получаем recent blockhash
            const { blockhash } = await this.connection.getLatestBlockhash();
            transaction.recentBlockhash = blockhash;
            transaction.feePayer = this.wallet.publicKey;
            
            console.log('📋 Транзакция создана');
            console.log(`📏 Инструкций: ${transaction.instructions.length}`);
            
            // Подписываем транзакцию
            transaction.sign(this.wallet.payer);
            
            console.log('✅ Транзакция подписана');
            console.log('📤 Отправка транзакции...');
            
            // Отправляем транзакцию
            const signature = await this.connection.sendRawTransaction(transaction.serialize(), {
                skipPreflight: false,
                preflightCommitment: 'confirmed'
            });
            
            console.log(`📤 Транзакция отправлена: ${signature}`);
            console.log('⏳ Ожидание подтверждения...');
            
            // Ждем подтверждения
            const confirmation = await this.connection.confirmTransaction(signature, 'confirmed');
            
            if (confirmation.value.err) {
                throw new Error(`Транзакция провалена: ${JSON.stringify(confirmation.value.err)}`);
            }
            
            console.log('✅ Транзакция подтверждена!');
            console.log(`🔗 Signature: ${signature}`);
            
            return signature;
            
        } catch (error) {
            console.error(`❌ Ошибка выполнения deposit: ${error.message}`);
            throw error;
        }
    }

    /**
     * 🔍 ПРОВЕРКА СОЗДАННОГО BALANCE
     */
    async verifyBalance() {
        try {
            console.log('\n🔍 ПРОВЕРКА СОЗДАННОГО BALANCE');
            console.log('═══════════════════════════════════════════════════════════════');
            
            // Ждем немного для обновления данных
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            const accountInfo = await this.connection.getAccountInfo(this.MARGINFI_ACCOUNT);
            if (!accountInfo) {
                throw new Error('MarginFi аккаунт не найден');
            }
            
            const data = accountInfo.data;
            const balancesStart = 74;
            const balancesData = data.slice(balancesStart);
            const BALANCE_SIZE = 184;
            const maxBalances = Math.floor(balancesData.length / BALANCE_SIZE);
            
            let foundSOLBalance = false;
            
            for (let i = 0; i < maxBalances; i++) {
                const balanceOffset = i * BALANCE_SIZE;
                const isActive = balancesData[balanceOffset] === 1;
                
                if (isActive) {
                    const bankOffset = balanceOffset + 8;
                    const bankBytes = balancesData.slice(bankOffset, bankOffset + 32);
                    const bankPubkey = new PublicKey(bankBytes);
                    
                    if (bankPubkey.equals(this.SOL_BANK)) {
                        foundSOLBalance = true;
                        
                        const assetSharesOffset = bankOffset + 32;
                        const assetShares = balancesData.readBigUInt64LE(assetSharesOffset);
                        
                        console.log('✅ SOL LENDING BALANCE СОЗДАН!');
                        console.log(`🏦 Банк: ${bankPubkey.toString()}`);
                        console.log(`💰 Asset Shares: ${assetShares.toString()}`);
                        console.log('🎯 Теперь flash loan будет работать!');
                        break;
                    }
                }
            }
            
            if (!foundSOLBalance) {
                console.log('❌ SOL balance не найден');
                console.log('💡 Возможно нужно подождать или повторить операцию');
                return false;
            }
            
            return true;
            
        } catch (error) {
            console.error(`❌ Ошибка проверки balance: ${error.message}`);
            return false;
        }
    }

    /**
     * 🔥 ПОЛНОЕ СОЗДАНИЕ LENDING BALANCE
     */
    async createLendingBalance() {
        try {
            console.log('🔥 СОЗДАНИЕ LENDING BALANCE ДЛЯ SOL');
            console.log('═══════════════════════════════════════════════════════════════');
            console.log('🎯 Цель: Решить ошибку 3002 - LendingAccountBalanceNotFound');
            console.log('💰 Метод: Минимальный депозит SOL в MarginFi аккаунт');
            console.log('═══════════════════════════════════════════════════════════════');
            
            // 1. Получаем данные банка
            const bankData = await this.getBankData();
            
            // 2. Создаем deposit инструкцию (0.001 SOL = 1,000,000 lamports)
            const depositAmount = 1000000; // 0.001 SOL
            const depositInstruction = await this.createDepositInstruction(bankData, depositAmount);
            
            // 3. Выполняем deposit
            const signature = await this.executeDeposit(depositInstruction);
            
            // 4. Проверяем результат
            const verified = await this.verifyBalance();
            
            if (verified) {
                console.log('\n🎉 УСПЕХ! LENDING BALANCE СОЗДАН!');
                console.log('═══════════════════════════════════════════════════════════════');
                console.log('✅ SOL lending balance создан в MarginFi аккаунте');
                console.log('✅ Ошибка 3002 должна исчезнуть');
                console.log('✅ Flash loan теперь будет работать');
                console.log(`🔗 Transaction: ${signature}`);
                return true;
            } else {
                console.log('\n❌ НЕ УДАЛОСЬ ПОДТВЕРДИТЬ СОЗДАНИЕ BALANCE');
                console.log('💡 Проверьте транзакцию вручную');
                return false;
            }
            
        } catch (error) {
            console.error(`❌ Ошибка создания lending balance: ${error.message}`);
            return false;
        }
    }
}

async function main() {
    console.log('🔧 СОЗДАНИЕ LENDING BALANCE В MARGINFI АККАУНТЕ');
    console.log('═══════════════════════════════════════════════════════════════');
    console.log('🎯 Решаем ошибку 3002 - LendingAccountBalanceNotFound');
    console.log('💡 Создаем SOL lending balance через минимальный депозит');
    console.log('═══════════════════════════════════════════════════════════════');

    const creator = new MarginFiBalanceCreator();

    const initialized = await creator.initialize();
    if (!initialized) {
        console.log('❌ Не удалось инициализировать создание balance');
        return;
    }

    const success = await creator.createLendingBalance();

    if (success) {
        console.log('\n✅ ЗАДАЧА ВЫПОЛНЕНА!');
        console.log('🚀 Теперь можно запускать flash loan без ошибки 3002');
    } else {
        console.log('\n❌ НЕ УДАЛОСЬ СОЗДАТЬ LENDING BALANCE');
        console.log('💡 Проверьте баланс SOL и повторите попытку');
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = MarginFiBalanceCreator;
