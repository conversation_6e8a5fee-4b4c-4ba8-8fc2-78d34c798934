/**
 * 🔥 СОЗДАНИЕ POSITION АККАУНТОВ БЕЗ FLASH LOAN
 * ИСПОЛЬЗУЕМ ТОЛЬКО ОФИЦИАЛЬНЫЙ SDK
 * ПРОСТАЯ ОПЕРАЦИЯ - СОЗДАТЬ POSITION И СРАЗУ ЗАКРЫТЬ
 */

const { Connection, Keypair, PublicKey, sendAndConfirmTransaction } = require('@solana/web3.js');
const DLMM = require('@meteora-ag/dlmm').default;
const { BN } = require('@coral-xyz/anchor');
const fs = require('fs');

async function createPositionsOnly() {
    try {
        console.log('🚀 СОЗДАНИЕ POSITION АККАУНТОВ БЕЗ FLASH LOAN...\n');

        // 🔧 НАСТРОЙКА ПОДКЛЮЧЕНИЯ
        const connection = new Connection('https://api.mainnet-beta.solana.com', 'confirmed');
        
        // 🔧 ЗАГРУЗКА КОШЕЛЬКА
        const secretKey = JSON.parse(fs.readFileSync('wallet.json', 'utf8'));
        const wallet = Keypair.fromSecretKey(new Uint8Array(secretKey));
        console.log(`💰 Кошелек: ${wallet.publicKey.toString()}`);

        // 🔧 POOL ADDRESSES
        const poolAddresses = [
            '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6', // Pool 1
            'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y',  // Pool 2
            'HTvjzsfX3yU6BUodCjZ5vZkUrAxMDTrBs3CJaq43ashR'   // Pool 3
        ];

        console.log('🔧 Создание DLMM инстансов...');
        const dlmmPools = await DLMM.createMultiple(connection, poolAddresses.map(addr => new PublicKey(addr)));
        console.log(`✅ Создано ${dlmmPools.length} DLMM инстансов\n`);

        // 🔧 СОЗДАЕМ POSITION ДЛЯ КАЖДОГО ПУЛА
        for (let i = 0; i < dlmmPools.length; i++) {
            const dlmmPool = dlmmPools[i];
            const poolAddress = poolAddresses[i];
            
            console.log(`${'='.repeat(50)}`);
            console.log(`🔥 СОЗДАНИЕ POSITION ДЛЯ ПУЛА ${i + 1}`);
            console.log(`📍 Адрес: ${poolAddress}`);
            console.log(`${'='.repeat(50)}`);

            try {
                // Получаем активный bin
                const activeBin = await dlmmPool.getActiveBin();
                console.log(`📊 Активный bin ID: ${activeBin.binId}`);
                console.log(`💰 Цена: ${activeBin.price}`);

                // Минимальная стратегия
                const RANGE = 1; // Только 1 bin
                const minBinId = activeBin.binId;
                const maxBinId = activeBin.binId + RANGE;

                // МИНИМАЛЬНЫЕ суммы (почти ноль)
                const totalXAmount = new BN(1000); // 0.001 токена
                const totalYAmount = new BN(1000); // 0.001 токена

                console.log(`📈 Диапазон: ${minBinId} - ${maxBinId}`);
                console.log(`💵 X: ${totalXAmount.toString()}, Y: ${totalYAmount.toString()}`);

                // Создаем position keypair
                const newPosition = new Keypair();
                console.log(`🔑 Position: ${newPosition.publicKey.toString()}`);

                // Создаем транзакцию
                console.log('🔧 Создание транзакции...');
                const createPositionTx = await dlmmPool.initializePositionAndAddLiquidityByStrategy({
                    positionPubKey: newPosition.publicKey,
                    user: wallet.publicKey,
                    totalXAmount,
                    totalYAmount,
                    strategy: {
                        maxBinId,
                        minBinId,
                        strategyType: 0, // Spot
                    },
                });

                console.log('📤 Отправка транзакции...');
                const signature = await sendAndConfirmTransaction(
                    connection,
                    createPositionTx,
                    [wallet, newPosition],
                    { skipPreflight: false }
                );

                console.log(`✅ POSITION ${i + 1} СОЗДАН!`);
                console.log(`📝 Signature: ${signature}`);
                console.log(`🔑 Position Address: ${newPosition.publicKey.toString()}\n`);

                // Сохраняем адрес position в файл
                const positionData = {
                    poolIndex: i + 1,
                    poolAddress: poolAddress,
                    positionAddress: newPosition.publicKey.toString(),
                    signature: signature,
                    timestamp: new Date().toISOString()
                };

                const fileName = `position-${i + 1}.json`;
                fs.writeFileSync(fileName, JSON.stringify(positionData, null, 2));
                console.log(`💾 Данные сохранены в ${fileName}`);

            } catch (error) {
                console.error(`❌ ОШИБКА СОЗДАНИЯ POSITION ${i + 1}:`, error.message);
                
                if (error.message.includes('insufficient lamports')) {
                    console.log('💡 НУЖНО БОЛЬШЕ SOL НА КОШЕЛЬКЕ!');
                }
                
                continue; // Продолжаем со следующим пулом
            }
        }

        console.log('\n🎉 СОЗДАНИЕ POSITION АККАУНТОВ ЗАВЕРШЕНО!');
        console.log('💡 Теперь можно использовать созданные position в основном коде');

    } catch (error) {
        console.error('❌ КРИТИЧЕСКАЯ ОШИБКА:', error);
        
        if (error.message) {
            console.error('📝 Сообщение:', error.message);
        }
    }
}

// Запуск только если файл вызван напрямую
if (require.main === module) {
    createPositionsOnly().catch(console.error);
}

module.exports = { createPositionsOnly };
