/**
 * 🔥 СОЗДАНИЕ POSITION АККАУНТОВ - ПО ОДНОМУ ПУЛУ ЗА ТРАНЗАКЦИЮ
 * ОТДЕЛЬНЫЕ FLASH LOAN ТРАНЗАКЦИИ ДЛЯ КАЖДОГО ПУЛА
 */

const { Connection, Keypair } = require('@solana/web3.js');
const fs = require('fs');
const SimpleFlashLoan = require('./simple-flash-loan');

class SinglePoolFlashLoan extends SimpleFlashLoan {
    constructor(wallet, marginfiAccountAddress, connection, poolIndex) {
        super(wallet, marginfiAccountAddress, connection);
        
        // Работаем только с одним пулом
        this.targetPoolIndex = poolIndex;
        this.POOL_ADDRESSES = [this.POOL_ADDRESSES[poolIndex]]; // Только один пул
        
        console.log(`🎯 Настроен для Pool ${poolIndex + 1}: ${this.POOL_ADDRESSES[0]}`);
    }

    /**
     * 🚀 СОЗДАНИЕ FLASH LOAN ДЛЯ ОДНОГО ПУЛА
     */
    async createSinglePoolFlashLoan() {
        console.log(`🔥 СОЗДАНИЕ FLASH LOAN ДЛЯ POOL ${this.targetPoolIndex + 1}...`);
        
        const instructions = [];

        // 0-1: ComputeBudget
        const { ComputeBudgetProgram } = require('@solana/web3.js');
        const computeUnitLimitIx = ComputeBudgetProgram.setComputeUnitLimit({
            units: 1400000
        });
        instructions.push(computeUnitLimitIx);
        
        const computeUnitPriceIx = ComputeBudgetProgram.setComputeUnitPrice({
            microLamports: 0
        });
        instructions.push(computeUnitPriceIx);

        // 2: START Flash Loan
        const endIndex = 8; // Позиция END Flash Loan
        const startFlashLoanIx = this.createStartFlashLoanInstruction(endIndex);
        instructions.push(startFlashLoanIx);

        // 3: BORROW SOL
        const borrowAmount = 10 * 1e9; // 10 SOL для одного position
        const borrowSOLIx = this.createBorrowInstruction(borrowAmount, this.BANKS.SOL);
        instructions.push(borrowSOLIx);

        // 4-6: СОЗДАНИЕ POSITION ЧЕРЕЗ SDK
        await this.initializeDLMMPools();
        
        const positionInstructions = await this.createPositionInstruction(0); // Индекс 0 (единственный пул)
        
        // Добавляем инструкции из SDK
        if (Array.isArray(positionInstructions)) {
            console.log(`📊 SDK создал ${positionInstructions.length} инструкций`);
            instructions.push(...positionInstructions);
        } else {
            instructions.push(positionInstructions);
        }

        // 7: REPAY SOL
        const repaySOLIx = this.createRepayInstruction(this.BANKS.SOL, true);
        instructions.push(repaySOLIx);

        // 8: END Flash Loan
        const endFlashLoanIx = this.createEndFlashLoanInstruction();
        instructions.push(endFlashLoanIx);

        console.log(`✅ Создано ${instructions.length} инструкций для Pool ${this.targetPoolIndex + 1}`);
        
        // Проверяем позицию END Flash Loan
        const actualEndIndex = instructions.length - 1;
        console.log(`🎯 END Flash Loan на позиции: ${actualEndIndex} (ожидалось: ${endIndex})`);
        
        return instructions;
    }

    /**
     * 🚀 ВЫПОЛНЕНИЕ FLASH LOAN ДЛЯ ОДНОГО ПУЛА
     */
    async executeSinglePoolFlashLoan() {
        try {
            console.log(`\n🚀 ЗАПУСК FLASH LOAN ДЛЯ POOL ${this.targetPoolIndex + 1}...`);
            
            // Загружаем ALT таблицы
            this.altTables = this.loadALTTablesDirectly();
            
            const instructions = await this.createSinglePoolFlashLoan();
            
            // Создаем versioned транзакцию с ALT сжатием
            const { TransactionMessage, VersionedTransaction } = require('@solana/web3.js');
            
            const { blockhash } = await this.connection.getLatestBlockhash();
            
            const messageV0 = new TransactionMessage({
                payerKey: this.wallet.publicKey,
                recentBlockhash: blockhash,
                instructions: instructions
            }).compileToV0Message(this.altTables);
            
            const versionedTx = new VersionedTransaction(messageV0);
            
            // Собираем signers
            const signers = [this.wallet, ...this.allSigners];
            console.log(`🔑 Signers: ${signers.length}`);
            
            versionedTx.sign(signers);

            console.log('📤 Отправка транзакции...');
            const signature = await this.connection.sendTransaction(versionedTx);
            
            console.log(`📝 Signature: ${signature}`);
            console.log('⏳ Ожидание подтверждения...');
            
            await this.connection.confirmTransaction(signature, 'confirmed');
            
            console.log(`✅ POOL ${this.targetPoolIndex + 1} СОЗДАН! Signature: ${signature}`);
            return signature;
            
        } catch (error) {
            console.error(`❌ Ошибка Flash Loan для Pool ${this.targetPoolIndex + 1}:`, error.message);
            throw error;
        }
    }
}

async function createPositionsSeparately() {
    try {
        console.log('🚀 СОЗДАНИЕ POSITION АККАУНТОВ - ПО ОДНОМУ ПУЛУ ЗА ТРАНЗАКЦИЮ...\n');

        // Настройка
        const connection = new Connection('https://api.mainnet-beta.solana.com', 'confirmed');
        const secretKey = JSON.parse(fs.readFileSync('wallet.json', 'utf8'));
        const wallet = Keypair.fromSecretKey(new Uint8Array(secretKey));
        const marginfiAccountAddress = '********************************************';

        console.log(`💰 Кошелек: ${wallet.publicKey.toString()}`);
        console.log(`🏦 MarginFi: ${marginfiAccountAddress}\n`);

        const poolNames = ['Pool 1', 'Pool 2', 'Pool 3'];
        const results = [];

        // Создаем position для каждого пула отдельно
        for (let i = 0; i < 3; i++) {
            console.log(`${'='.repeat(50)}`);
            console.log(`🔥 СОЗДАНИЕ ${poolNames[i]}`);
            console.log(`${'='.repeat(50)}`);

            try {
                const singlePoolFlashLoan = new SinglePoolFlashLoan(wallet, marginfiAccountAddress, connection, i);
                const signature = await singlePoolFlashLoan.executeSinglePoolFlashLoan();
                
                const positionData = singlePoolFlashLoan.getCreatedPositionAddresses()[0];
                
                results.push({
                    poolIndex: i + 1,
                    poolName: poolNames[i],
                    poolAddress: positionData.poolAddress,
                    positionAddress: positionData.positionAddress,
                    signature: signature,
                    timestamp: new Date().toISOString(),
                    status: 'success'
                });

                console.log(`✅ ${poolNames[i]} УСПЕШНО СОЗДАН!`);
                console.log(`🔑 Position: ${positionData.positionAddress}\n`);

            } catch (error) {
                console.error(`❌ ОШИБКА СОЗДАНИЯ ${poolNames[i]}:`, error.message);
                
                results.push({
                    poolIndex: i + 1,
                    poolName: poolNames[i],
                    error: error.message,
                    timestamp: new Date().toISOString(),
                    status: 'failed'
                });

                // Продолжаем со следующим пулом
                continue;
            }
        }

        // Сохраняем результаты
        const finalResults = {
            created: new Date().toISOString(),
            wallet: wallet.publicKey.toString(),
            marginfiAccount: marginfiAccountAddress,
            results: results
        };

        fs.writeFileSync('position-creation-results.json', JSON.stringify(finalResults, null, 2));

        console.log('\n🎉 СОЗДАНИЕ POSITION АККАУНТОВ ЗАВЕРШЕНО!');
        console.log('💾 Результаты сохранены в position-creation-results.json');
        
        const successCount = results.filter(r => r.status === 'success').length;
        console.log(`📊 Успешно создано: ${successCount}/3 position аккаунтов`);

        if (successCount > 0) {
            console.log('\n📋 Созданные position аккаунты:');
            results.filter(r => r.status === 'success').forEach(r => {
                console.log(`   ${r.poolName}: ${r.positionAddress}`);
            });
        }

    } catch (error) {
        console.error('❌ КРИТИЧЕСКАЯ ОШИБКА:', error);
        process.exit(1);
    }
}

// Запуск
if (require.main === module) {
    createPositionsSeparately().catch(console.error);
}

module.exports = { createPositionsSeparately, SinglePoolFlashLoan };
