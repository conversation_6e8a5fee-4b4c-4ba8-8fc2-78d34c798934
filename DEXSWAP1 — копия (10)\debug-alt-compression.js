#!/usr/bin/env node

/**
 * 🔥 ОТЛАДКА ALT СЖАТИЯ ТРАНЗАКЦИЙ
 * Проверяем почему compileToV0Message не сжимает аккаунты
 */

const { Connection, PublicKey, Keypair, TransactionMessage, TransactionInstruction } = require('@solana/web3.js');
const fs = require('fs');

async function debugALTCompression() {
    console.log('🔥 ОТЛАДКА ALT СЖАТИЯ ТРАНЗАКЦИЙ');
    console.log('=' .repeat(60));

    try {
        // 1. Подключение
        const connection = new Connection('https://api.mainnet-beta.solana.com');
        const walletData = JSON.parse(fs.readFileSync('./wallet.json', 'utf8'));
        const wallet = Keypair.fromSecretKey(new Uint8Array(walletData));
        
        console.log(`✅ Кошелек: ${wallet.publicKey.toString()}`);
        
        // 2. Загружаем одну ALT таблицу для теста
        console.log('\n📁 Загрузка одной ALT таблицы для теста...');
        
        let altTable = null;
        
        if (fs.existsSync('./meteora-alt-cache.json')) {
            const meteora = JSON.parse(fs.readFileSync('./meteora-alt-cache.json', 'utf8'));
            const firstALT = meteora.validationResults?.[0];
            
            if (firstALT && firstALT.valid && firstALT.address) {
                const altAccount = await connection.getAddressLookupTable(new PublicKey(firstALT.address));
                if (altAccount?.value) {
                    altTable = altAccount.value;
                    console.log(`✅ ALT загружена: ${firstALT.address}`);
                    console.log(`📊 Аккаунтов в ALT: ${altTable.state.addresses.length}`);
                }
            }
        }
        
        if (!altTable) {
            throw new Error('Не удалось загрузить ALT таблицу');
        }
        
        // 3. Проверяем какие системные аккаунты есть в ALT
        console.log('\n🔍 ПРОВЕРКА СИСТЕМНЫХ АККАУНТОВ В ALT...');
        
        const systemAccounts = [
            'So********************************111111112',   // SOL
            'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',   // USDC
            'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA',   // Token Program
            '********************************'                // System Program
        ];
        
        const altAccountsSet = new Set(altTable.state.addresses.map(addr => addr.toString()));
        
        systemAccounts.forEach((account, index) => {
            const inALT = altAccountsSet.has(account);
            const shortAccount = `${account.slice(0, 8)}...${account.slice(-8)}`;
            
            if (inALT) {
                const altIndex = altTable.state.addresses.findIndex(addr => addr.toString() === account);
                console.log(`✅ ${index + 1}. ${shortAccount} - В ALT (индекс ${altIndex})`);
            } else {
                console.log(`❌ ${index + 1}. ${shortAccount} - НЕ в ALT`);
            }
        });
        
        // 4. Создаем простую инструкцию с системными аккаунтами
        console.log('\n🔧 СОЗДАНИЕ ИНСТРУКЦИИ С СИСТЕМНЫМИ АККАУНТАМИ...');
        
        const testInstruction = new TransactionInstruction({
            programId: new PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA'),
            keys: [
                { pubkey: wallet.publicKey, isSigner: true, isWritable: true },
                { pubkey: new PublicKey('So********************************111111112'), isSigner: false, isWritable: false },
                { pubkey: new PublicKey('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'), isSigner: false, isWritable: false },
                { pubkey: new PublicKey('********************************'), isSigner: false, isWritable: false }
            ],
            data: Buffer.from([1, 2, 3, 4]) // Тестовые данные
        });
        
        console.log('✅ Тестовая инструкция создана');
        console.log(`📊 Аккаунтов в инструкции: ${testInstruction.keys.length}`);
        
        // 5. Создаем транзакцию БЕЗ ALT
        console.log('\n🔧 СОЗДАНИЕ ТРАНЗАКЦИИ БЕЗ ALT...');
        
        const { blockhash } = await connection.getLatestBlockhash();
        
        const messageWithoutALT = new TransactionMessage({
            payerKey: wallet.publicKey,
            recentBlockhash: blockhash,
            instructions: [testInstruction],
        }).compileToV0Message([]);
        
        console.log('✅ Транзакция БЕЗ ALT создана');
        console.log(`📊 Аккаунтов в транзакции БЕЗ ALT: ${messageWithoutALT.staticAccountKeys.length}`);
        
        // 6. Создаем транзакцию С ALT
        console.log('\n🔧 СОЗДАНИЕ ТРАНЗАКЦИИ С ALT...');
        
        const messageWithALT = new TransactionMessage({
            payerKey: wallet.publicKey,
            recentBlockhash: blockhash,
            instructions: [testInstruction],
        }).compileToV0Message([altTable]);
        
        console.log('✅ Транзакция С ALT создана');
        console.log(`📊 Аккаунтов в транзакции С ALT: ${messageWithALT.staticAccountKeys.length}`);
        console.log(`📊 ALT таблиц: ${messageWithALT.addressTableLookups.length}`);
        
        if (messageWithALT.addressTableLookups.length > 0) {
            messageWithALT.addressTableLookups.forEach((lookup, index) => {
                console.log(`   ALT ${index + 1}: ${lookup.accountKey.toString()}`);
                console.log(`   Readonly индексы: ${lookup.readonlyIndexes.length}`);
                console.log(`   Writable индексы: ${lookup.writableIndexes.length}`);
            });
        }
        
        // 7. Сравниваем размеры
        console.log('\n📊 СРАВНЕНИЕ РАЗМЕРОВ:');
        
        const sizeWithoutALT = messageWithoutALT.staticAccountKeys.length;
        const sizeWithALT = messageWithALT.staticAccountKeys.length;
        const compression = sizeWithoutALT - sizeWithALT;
        
        console.log(`   БЕЗ ALT: ${sizeWithoutALT} аккаунтов`);
        console.log(`   С ALT: ${sizeWithALT} аккаунтов`);
        console.log(`   Сжатие: ${compression} аккаунтов`);
        
        if (compression > 0) {
            console.log(`✅ ALT СЖАТИЕ РАБОТАЕТ! Сэкономлено ${compression} аккаунтов`);
        } else if (compression === 0) {
            console.log(`⚠️ ALT НЕ СЖИМАЕТ - аккаунты не найдены в ALT таблице`);
        } else {
            console.log(`❌ ALT УВЕЛИЧИВАЕТ размер - что-то не так`);
        }
        
        // 8. Детальный анализ
        console.log('\n🔍 ДЕТАЛЬНЫЙ АНАЛИЗ АККАУНТОВ:');
        
        console.log('\n📋 АККАУНТЫ БЕЗ ALT:');
        messageWithoutALT.staticAccountKeys.forEach((account, index) => {
            console.log(`   ${index}. ${account.toString().slice(0, 8)}...${account.toString().slice(-8)}`);
        });
        
        console.log('\n📋 АККАУНТЫ С ALT:');
        messageWithALT.staticAccountKeys.forEach((account, index) => {
            console.log(`   ${index}. ${account.toString().slice(0, 8)}...${account.toString().slice(-8)}`);
        });
        
        // 9. Проверяем симуляцию
        console.log('\n🧪 ТЕСТИРОВАНИЕ СИМУЛЯЦИИ...');
        
        try {
            const { VersionedTransaction } = require('@solana/web3.js');
            const transaction = new VersionedTransaction(messageWithALT);
            transaction.sign([wallet]);
            
            const simulation = await connection.simulateTransaction(transaction);
            
            if (simulation.value.err) {
                const errorStr = JSON.stringify(simulation.value.err);
                console.log(`❌ ОШИБКА СИМУЛЯЦИИ: ${errorStr}`);
                
                if (errorStr.includes('AccountLoadedTwice')) {
                    console.log('🚨 ОШИБКА "AccountLoadedTwice" ПОДТВЕРЖДЕНА!');
                    console.log('💡 Проблема: аккаунты все еще дублируются несмотря на ALT');
                }
            } else {
                console.log('✅ Симуляция прошла успешно');
            }
        } catch (error) {
            console.log(`❌ Ошибка симуляции: ${error.message}`);
        }
        
        // 10. Итоговый результат
        console.log('\n🎯 ИТОГОВЫЙ РЕЗУЛЬТАТ:');
        console.log('=' .repeat(40));
        
        if (compression > 0) {
            console.log(`✅ ALT СЖАТИЕ РАБОТАЕТ ПРАВИЛЬНО`);
            console.log(`📊 Сэкономлено: ${compression} аккаунтов`);
            console.log(`💡 Проблема "AccountLoadedTwice" НЕ в ALT сжатии`);
        } else {
            console.log(`❌ ALT СЖАТИЕ НЕ РАБОТАЕТ`);
            console.log(`💡 Аккаунты из инструкций НЕ найдены в ALT таблице`);
            console.log(`🔧 Нужно добавить системные аккаунты в ALT таблицы`);
        }

    } catch (error) {
        console.error('❌ Ошибка отладки:', error.message);
        console.error(error.stack);
    }
}

// Запуск отладки
if (require.main === module) {
    debugALTCompression().catch(console.error);
}

module.exports = { debugALTCompression };
