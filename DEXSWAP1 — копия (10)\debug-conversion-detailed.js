#!/usr/bin/env node

/**
 * 🔍 ДЕТАЛЬНАЯ ПРОВЕРКА КОНВЕРТАЦИИ И АРБИТРАЖНОЙ ЛОГИКИ
 */

console.log('🔍 ДЕТАЛЬНАЯ ПРОВЕРКА КОНВЕРТАЦИИ И АРБИТРАЖНОЙ ЛОГИКИ');
console.log('=' .repeat(70));

async function debugConversion() {
    try {
        // 1. ПРОВЕРКА КОНВЕРТАЦИИ USD → LAMPORTS
        console.log('\n📊 1. ПРОВЕРКА КОНВЕРТАЦИИ USD → LAMPORTS:');
        
        const { convertUsdToNativeAmount } = require('./centralized-amount-converter.js');
        
        // Тестируем с разными ценами SOL
        const testPrices = [166.87, 166.94, 167.02]; // Цены из разных пулов
        const usdAmount = 1000;
        
        for (const price of testPrices) {
            const lamports = await convertUsdToNativeAmount(usdAmount, 'SOL', price);
            const solAmount = lamports / 1e9;
            
            console.log(`\n💰 Цена SOL: $${price}`);
            console.log(`   $${usdAmount} → ${lamports} lamports → ${solAmount.toFixed(9)} SOL`);
            console.log(`   Проверка: ${solAmount.toFixed(9)} * $${price} = $${(solAmount * price).toFixed(2)}`);
        }
        
        // 2. ПРОВЕРКА SWAP СУММ
        console.log('\n📊 2. ПРОВЕРКА SWAP СУММ:');
        
        const borrowAmount = 5992510718; // из транзакции
        const borrowSol = borrowAmount / 1e9;
        console.log(`\n💰 Займ: ${borrowAmount} lamports = ${borrowSol.toFixed(9)} SOL`);
        
        // Первый swap: должен быть весь займ
        const swap1Amount = 1000000; // из транзакции
        const swap1Sol = swap1Amount / 1e9;
        console.log(`\n🔄 Swap 1 (из транзакции): ${swap1Amount} lamports = ${swap1Sol.toFixed(9)} SOL`);
        console.log(`   ❌ ПРОБЛЕМА: Swap только ${swap1Sol} SOL вместо ${borrowSol} SOL!`);
        
        // 3. ПРОВЕРКА АРБИТРАЖНОЙ ЛОГИКИ
        console.log('\n📊 3. ПРОВЕРКА АРБИТРАЖНОЙ ЛОГИКИ:');
        
        console.log('\n🔄 ИЗ ТРАНЗАКЦИИ:');
        console.log('   Swap 1: WSOL → USDC (0.001 WSOL → 0.166818 USDC)');
        console.log('   Swap 2: USDC → WSOL (1 USDC → ??? WSOL) - НЕ ВЫПОЛНИЛСЯ');
        
        console.log('\n✅ ДОЛЖНО БЫТЬ:');
        console.log('   Swap 1: WSOL → USDC (5.99 WSOL → ~1000 USDC) - продать дорого');
        console.log('   Swap 2: USDC → WSOL (~1000 USDC → >5.99 WSOL) - купить дешево');
        
        // 4. ПРОВЕРКА ЦЕНЫ В SWAP
        console.log('\n📊 4. ПРОВЕРКА ЦЕНЫ В SWAP:');
        
        const swap1WsolAmount = 0.001;
        const swap1UsdcAmount = 0.166818;
        const swap1Price = swap1UsdcAmount / swap1WsolAmount;
        
        console.log(`\n💰 Цена в Swap 1:`);
        console.log(`   ${swap1UsdcAmount} USDC / ${swap1WsolAmount} WSOL = $${swap1Price.toFixed(2)} за SOL`);
        console.log(`   ❌ ПРОБЛЕМА: Цена $${swap1Price.toFixed(2)} вместо ~$167!`);
        
        // 5. ПРОВЕРКА ПРАВИЛЬНОЙ ЛОГИКИ
        console.log('\n📊 5. ПРАВИЛЬНАЯ АРБИТРАЖНАЯ ЛОГИКА:');
        
        const poolPrices = {
            cheap: 166.87,  // дешевый пул
            expensive: 166.94  // дорогой пул
        };
        
        const loanSol = 5.99;
        
        console.log(`\n✅ ПРАВИЛЬНАЯ ПОСЛЕДОВАТЕЛЬНОСТЬ:`);
        console.log(`   1. Займ: ${loanSol} SOL`);
        console.log(`   2. Продать на дорогом пуле: ${loanSol} SOL → ${(loanSol * poolPrices.expensive).toFixed(2)} USDC`);
        console.log(`   3. Купить на дешевом пуле: ${(loanSol * poolPrices.expensive).toFixed(2)} USDC → ${((loanSol * poolPrices.expensive) / poolPrices.cheap).toFixed(6)} SOL`);
        console.log(`   4. Прибыль: ${((loanSol * poolPrices.expensive) / poolPrices.cheap - loanSol).toFixed(6)} SOL`);
        
        console.log('\n🚨 ПРОБЛЕМЫ В КОДЕ:');
        console.log('   ❌ Неправильные суммы swap (0.001 вместо 5.99)');
        console.log('   ❌ Неправильная цена в конвертации');
        console.log('   ❌ Оба swap делают WSOL→USDC вместо WSOL→USDC и USDC→WSOL');
        console.log('   ❌ Не используется цена конкретного пула для каждого swap');
        
    } catch (error) {
        console.error('❌ Ошибка проверки:', error.message);
    }
}

// Запускаем проверку
debugConversion().catch(console.error);
