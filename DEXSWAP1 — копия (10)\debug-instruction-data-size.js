/**
 * 🔍 ОТЛАДКА РАЗМЕРА ДАННЫХ В ИНСТРУКЦИЯХ 7 И 9
 */

// Данные из строки 950
const data1 = '03dd95da6f8d76d50000000000000000e803000000000000f1eeffff00000000f0eeffff00000000060000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000100';

// Данные из строки 1469  
const data2 = '03dd95da6f8d76d50000000000000000286411420f000000f7eeffff03000000f6eefffff6eeffff06000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000200000000000100';

console.log('🔍 АНАЛИЗ РАЗМЕРА ДАННЫХ В add_liquidity_by_strategy ИНСТРУКЦИЯХ:');
console.log('=' .repeat(80));

console.log('📊 ДАННЫЕ 1 (строка 950):');
console.log(`   Hex длина: ${data1.length} символов`);
console.log(`   Bytes: ${data1.length / 2} bytes`);
console.log(`   Buffer: ${Buffer.from(data1, 'hex').length} bytes`);

console.log('\n📊 ДАННЫЕ 2 (строка 1469):');
console.log(`   Hex длина: ${data2.length} символов`);
console.log(`   Bytes: ${data2.length / 2} bytes`);
console.log(`   Buffer: ${Buffer.from(data2, 'hex').length} bytes`);

console.log('\n🔍 ДЕКОДИРОВАНИЕ СТРУКТУРЫ:');

function analyzeInstructionData(hexData, name) {
    const buffer = Buffer.from(hexData, 'hex');
    console.log(`\n📋 ${name}:`);
    console.log(`   Общий размер: ${buffer.length} bytes`);
    
    // Discriminator (8 bytes)
    const discriminator = buffer.slice(0, 8);
    console.log(`   Discriminator: ${discriminator.toString('hex')} (8 bytes)`);
    
    // Amount X (8 bytes)
    const amountX = buffer.readBigUInt64LE(8);
    console.log(`   Amount X: ${amountX} (8 bytes)`);
    
    // Amount Y (8 bytes) 
    const amountY = buffer.readBigUInt64LE(16);
    console.log(`   Amount Y: ${amountY} (8 bytes)`);
    
    // Active ID (4 bytes)
    const activeId = buffer.readInt32LE(24);
    console.log(`   Active ID: ${activeId} (4 bytes)`);
    
    // Max slippage (4 bytes)
    const maxSlippage = buffer.readUInt32LE(28);
    console.log(`   Max Slippage: ${maxSlippage} (4 bytes)`);
    
    // Остальные данные
    const remaining = buffer.slice(32);
    console.log(`   Остальные данные: ${remaining.length} bytes`);
    console.log(`   Остальные hex: ${remaining.toString('hex')}`);
    
    // Анализируем остальные данные
    if (remaining.length > 0) {
        console.log(`   🔍 АНАЛИЗ ОСТАЛЬНЫХ ${remaining.length} BYTES:`);
        
        // Возможно это bin arrays или другие структуры
        let offset = 0;
        while (offset < remaining.length) {
            if (offset + 4 <= remaining.length) {
                const value = remaining.readUInt32LE(offset);
                console.log(`     Offset ${offset}: ${value} (0x${value.toString(16)})`);
                offset += 4;
            } else {
                const lastBytes = remaining.slice(offset);
                console.log(`     Offset ${offset}: ${lastBytes.toString('hex')} (${lastBytes.length} bytes)`);
                break;
            }
        }
    }
}

analyzeInstructionData(data1, 'ИНСТРУКЦИЯ 1 (Pool 1)');
analyzeInstructionData(data2, 'ИНСТРУКЦИЯ 2 (Pool 2)');

console.log('\n💡 ВЫВОДЫ:');
console.log('=' .repeat(80));

const size1 = Buffer.from(data1, 'hex').length;
const size2 = Buffer.from(data2, 'hex').length;

console.log(`📊 Размер инструкции 1: ${size1} bytes`);
console.log(`📊 Размер инструкции 2: ${size2} bytes`);
console.log(`📊 Общий размер данных: ${size1 + size2} bytes`);

if (size1 > 50 || size2 > 50) {
    console.log('\n🔥 ПРОБЛЕМА: ДАННЫЕ СЛИШКОМ БОЛЬШИЕ!');
    console.log('💡 ВОЗМОЖНЫЕ ПРИЧИНЫ:');
    console.log('   1. Дублирование данных');
    console.log('   2. Лишние поля');
    console.log('   3. Неоптимальная структура');
    console.log('   4. Padding или alignment');
}

console.log('\n🎯 РЕКОМЕНДАЦИИ ПО ОПТИМИЗАЦИИ:');
console.log('   1. Убрать лишние нули в конце');
console.log('   2. Минимизировать количество bin arrays');
console.log('   3. Использовать более компактную структуру');
console.log('   4. Объединить похожие операции');
