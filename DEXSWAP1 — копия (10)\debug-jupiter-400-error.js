/**
 * 🔧 ОТЛАДКА JUPITER API 400 ОШИБКИ
 * Тестируем точные запросы, которые делает система
 */

const fetch = require('node-fetch');

async function debugJupiterError() {
  console.log('🔧 ОТЛАДКА JUPITER API 400 ОШИБКИ');
  
  // Точные параметры из системы
  const inputMint = 'So11111111111111111111111111111111111111112'; // SOL
  const outputMint = 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'; // USDC
  const amount = '814873420'; // Реальная сумма из системы
  const slippageBps = '50';
  
  console.log('\n🔧 ПАРАМЕТРЫ ЗАПРОСА:');
  console.log(`   inputMint: ${inputMint}`);
  console.log(`   outputMint: ${outputMint}`);
  console.log(`   amount: ${amount}`);
  console.log(`   slippageBps: ${slippageBps}`);
  
  // Тест 1: Минимальный запрос
  console.log('\n1️⃣ ТЕСТ: Минимальный запрос');
  const minimalUrl = `https://lite-api.jup.ag/swap/v1/quote?inputMint=${inputMint}&outputMint=${outputMint}&amount=${amount}&slippageBps=${slippageBps}`;
  console.log(`URL: ${minimalUrl}`);
  
  try {
    const response1 = await fetch(minimalUrl);
    console.log(`Статус: ${response1.status}`);
    
    if (response1.ok) {
      const data1 = await response1.json();
      console.log('✅ УСПЕХ! Ответ:', {
        inAmount: data1.inAmount,
        outAmount: data1.outAmount
      });
    } else {
      const errorText1 = await response1.text();
      console.log(`❌ ОШИБКА ${response1.status}: ${errorText1}`);
    }
  } catch (error) {
    console.log(`❌ ИСКЛЮЧЕНИЕ: ${error.message}`);
  }
  
  // Тест 2: С restrictIntermediateTokens
  console.log('\n2️⃣ ТЕСТ: С restrictIntermediateTokens');
  const restrictedUrl = `https://lite-api.jup.ag/swap/v1/quote?inputMint=${inputMint}&outputMint=${outputMint}&amount=${amount}&slippageBps=${slippageBps}&restrictIntermediateTokens=true`;
  console.log(`URL: ${restrictedUrl}`);
  
  try {
    const response2 = await fetch(restrictedUrl);
    console.log(`Статус: ${response2.status}`);
    
    if (response2.ok) {
      const data2 = await response2.json();
      console.log('✅ УСПЕХ! Ответ:', {
        inAmount: data2.inAmount,
        outAmount: data2.outAmount
      });
    } else {
      const errorText2 = await response2.text();
      console.log(`❌ ОШИБКА ${response2.status}: ${errorText2}`);
    }
  } catch (error) {
    console.log(`❌ ИСКЛЮЧЕНИЕ: ${error.message}`);
  }
  
  // Тест 3: Обратное направление (USDC → SOL)
  console.log('\n3️⃣ ТЕСТ: Обратное направление (USDC → SOL)');
  const reverseInputMint = 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'; // USDC
  const reverseOutputMint = 'So11111111111111111111111111111111111111112'; // SOL
  const reverseAmount = '120000000000'; // 120,000 USDC в микротокенах
  
  const reverseUrl = `https://lite-api.jup.ag/swap/v1/quote?inputMint=${reverseInputMint}&outputMint=${reverseOutputMint}&amount=${reverseAmount}&slippageBps=${slippageBps}`;
  console.log(`URL: ${reverseUrl}`);
  
  try {
    const response3 = await fetch(reverseUrl);
    console.log(`Статус: ${response3.status}`);
    
    if (response3.ok) {
      const data3 = await response3.json();
      console.log('✅ УСПЕХ! Ответ:', {
        inAmount: data3.inAmount,
        outAmount: data3.outAmount
      });
    } else {
      const errorText3 = await response3.text();
      console.log(`❌ ОШИБКА ${response3.status}: ${errorText3}`);
    }
  } catch (error) {
    console.log(`❌ ИСКЛЮЧЕНИЕ: ${error.message}`);
  }
  
  // Тест 4: Проверяем точный URL из системы
  console.log('\n4️⃣ ТЕСТ: Точный URL из системы с excludeDexes');
  const systemUrl = `https://lite-api.jup.ag/swap/v1/quote?inputMint=${reverseInputMint}&outputMint=${reverseOutputMint}&amount=${reverseAmount}&slippageBps=${slippageBps}&restrictIntermediateTokens=true&maxAccounts=50&excludeDexes=Lifinity%20V1%2CLifinity%20V2`;
  console.log(`URL: ${systemUrl}`);
  
  try {
    const response4 = await fetch(systemUrl);
    console.log(`Статус: ${response4.status}`);
    
    if (response4.ok) {
      const data4 = await response4.json();
      console.log('✅ УСПЕХ! Ответ:', {
        inAmount: data4.inAmount,
        outAmount: data4.outAmount
      });
    } else {
      const errorText4 = await response4.text();
      console.log(`❌ ОШИБКА ${response4.status}: ${errorText4}`);
    }
  } catch (error) {
    console.log(`❌ ИСКЛЮЧЕНИЕ: ${error.message}`);
  }
  
  // Тест 5: Проверяем с нулевой суммой (как в системе)
  console.log('\n5️⃣ ТЕСТ: С нулевой суммой (как в системе)');
  const zeroUrl = `https://lite-api.jup.ag/swap/v1/quote?inputMint=${reverseInputMint}&outputMint=${reverseOutputMint}&amount=0&slippageBps=${slippageBps}`;
  console.log(`URL: ${zeroUrl}`);
  
  try {
    const response5 = await fetch(zeroUrl);
    console.log(`Статус: ${response5.status}`);
    
    if (response5.ok) {
      const data5 = await response5.json();
      console.log('✅ УСПЕХ! Ответ:', {
        inAmount: data5.inAmount,
        outAmount: data5.outAmount
      });
    } else {
      const errorText5 = await response5.text();
      console.log(`❌ ОШИБКА ${response5.status}: ${errorText5}`);
      console.log('🔧 ЭТО ПРИЧИНА 400 ОШИБКИ!');
    }
  } catch (error) {
    console.log(`❌ ИСКЛЮЧЕНИЕ: ${error.message}`);
  }
  
  console.log('\n🏁 ОТЛАДКА ЗАВЕРШЕНА');
}

// Запускаем отладку
debugJupiterError().catch(console.error);
