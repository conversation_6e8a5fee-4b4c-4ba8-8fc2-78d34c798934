#!/usr/bin/env node

/**
 * 🔍 ДЕТЕКТИВНОЕ РАССЛЕДОВАНИЕ MARGINFI АККАУНТА
 * ═══════════════════════════════════════════════════════════════════════════════
 * 🎯 ЦЕЛЬ: Найти СКРЫТЫЕ ДОЛГИ в MarginFi аккаунте ********************************************
 * 🔍 ПРОБЛЕМА: activeBalances.length === 0, но долг 37580.68 существует!
 * ═══════════════════════════════════════════════════════════════════════════════
 */

const { Connection, PublicKey, Keypair } = require('@solana/web3.js');
const { MarginfiClient, getConfig, MarginfiAccountWrapper } = require('@mrgnlabs/marginfi-client-v2');
const { NodeWallet } = require('@mrgnlabs/mrgn-common');
const bs58 = require('bs58');
require('dotenv').config({ path: '.env.solana' });

class MarginFiAccountDetective {
  constructor() {
    this.connection = null;
    this.wallet = null;
    this.marginfiClient = null;
    this.suspiciousAccount = '********************************************';
  }

  async initialize() {
    console.log('🔍 ДЕТЕКТИВНОЕ РАССЛЕДОВАНИЕ MARGINFI АККАУНТА');
    console.log('═══════════════════════════════════════════════════════════════');
    console.log(`🎯 Подозрительный аккаунт: ${this.suspiciousAccount}`);
    console.log('🔍 Проблема: 0 активных позиций, но долг 37580.68 SOL');
    console.log('═══════════════════════════════════════════════════════════════');

    // Подключение к RPC
    this.connection = new Connection(
      'https://sparkling-patient-market.solana-mainnet.quiknode.pro/146de9e433ca3b8f3473c0bd41742b24429dfc43/',
      'confirmed'
    );

    // Загрузка кошелька
    const privateKeyBase58 = process.env.WALLET_PRIVATE_KEY;
    if (!privateKeyBase58) {
      throw new Error('WALLET_PRIVATE_KEY не найден в .env.solana');
    }

    const privateKeyBytes = bs58.default ? bs58.default.decode(privateKeyBase58) : bs58.decode(privateKeyBase58);
    this.wallet = Keypair.fromSecretKey(privateKeyBytes);
    console.log(`👛 Кошелек: ${this.wallet.publicKey.toString()}`);

    // Инициализация MarginFi
    const nodeWallet = new NodeWallet(this.wallet);
    const config = getConfig('production');
    this.marginfiClient = await MarginfiClient.fetch(config, nodeWallet, this.connection);
    console.log('✅ MarginFi клиент инициализирован');
  }

  async investigateAccount() {
    console.log('\n🔍 ГЛУБОКОЕ РАССЛЕДОВАНИЕ АККАУНТА');
    console.log('═══════════════════════════════════════════════════════════════');

    const accountAddress = new PublicKey(this.suspiciousAccount);
    
    try {
      // Метод 1: Загрузка через MarginfiAccountWrapper
      console.log('📊 МЕТОД 1: MarginfiAccountWrapper.fetch()');
      const account = await MarginfiAccountWrapper.fetch(accountAddress, this.marginfiClient);
      
      if (account) {
        console.log(`✅ Аккаунт загружен: ${account.address.toString()}`);
        
        // Проверяем activeBalances
        console.log('\n💰 АКТИВНЫЕ БАЛАНСЫ:');
        const activeBalances = account.activeBalances || [];
        console.log(`📊 Количество активных позиций: ${activeBalances.length}`);
        
        if (activeBalances.length > 0) {
          activeBalances.forEach((balance, index) => {
            console.log(`   ${index + 1}. ${JSON.stringify(balance, null, 2)}`);
          });
        } else {
          console.log('   ❌ НЕТ АКТИВНЫХ ПОЗИЦИЙ (ЭТО СТРАННО!)');
        }

        // Проверяем все балансы (включая скрытые)
        console.log('\n🔍 ВСЕ БАЛАНСЫ (включая скрытые):');
        if (account.balances) {
          console.log(`📊 Общее количество балансов: ${account.balances.length}`);
          account.balances.forEach((balance, index) => {
            console.log(`   ${index + 1}. Bank: ${balance.bankPk.toString()}`);
            console.log(`      Assets: ${balance.assetShares.toString()}`);
            console.log(`      Liabilities: ${balance.liabilityShares.toString()}`);
            console.log(`      Active: ${balance.active}`);
          });
        }

        // Проверяем health factor
        console.log('\n🏥 HEALTH FACTOR:');
        try {
          const healthFactor = account.getHealthFactor();
          console.log(`💊 Health Factor: ${healthFactor}`);
        } catch (error) {
          console.log(`❌ Ошибка получения Health Factor: ${error.message}`);
        }

        // Проверяем equity
        console.log('\n💰 EQUITY (СОБСТВЕННЫЙ КАПИТАЛ):');
        try {
          const equity = account.computeNetValue();
          console.log(`💰 Net Value: ${equity}`);
        } catch (error) {
          console.log(`❌ Ошибка получения Net Value: ${error.message}`);
        }

      } else {
        console.log('❌ Аккаунт не найден через MarginfiAccountWrapper');
      }

    } catch (error) {
      console.log(`❌ Ошибка загрузки аккаунта: ${error.message}`);
      console.log(`🔍 Stack trace: ${error.stack}`);
    }
  }

  async investigateRawAccountData() {
    console.log('\n🔍 ИССЛЕДОВАНИЕ RAW ДАННЫХ АККАУНТА');
    console.log('═══════════════════════════════════════════════════════════════');

    const accountAddress = new PublicKey(this.suspiciousAccount);
    
    try {
      // Получаем raw данные аккаунта
      const accountInfo = await this.connection.getAccountInfo(accountAddress);
      
      if (accountInfo) {
        console.log(`✅ Raw аккаунт найден`);
        console.log(`📊 Owner: ${accountInfo.owner.toString()}`);
        console.log(`📊 Data length: ${accountInfo.data.length} bytes`);
        console.log(`📊 Lamports: ${accountInfo.lamports}`);
        console.log(`📊 Executable: ${accountInfo.executable}`);
        
        // Показываем первые 100 байт данных
        console.log('\n🔍 ПЕРВЫЕ 100 БАЙТ ДАННЫХ:');
        const dataHex = accountInfo.data.slice(0, 100).toString('hex');
        console.log(`📊 Hex: ${dataHex}`);
        
      } else {
        console.log('❌ Raw аккаунт не найден');
      }

    } catch (error) {
      console.log(`❌ Ошибка получения raw данных: ${error.message}`);
    }
  }

  async investigateAllAccounts() {
    console.log('\n🔍 ИССЛЕДОВАНИЕ ВСЕХ MARGINFI АККАУНТОВ');
    console.log('═══════════════════════════════════════════════════════════════');

    try {
      const accounts = await this.marginfiClient.getMarginfiAccountsForAuthority();
      console.log(`📊 Найдено аккаунтов: ${accounts.length}`);

      for (let i = 0; i < accounts.length; i++) {
        const account = accounts[i];
        console.log(`\n📊 АККАУНТ ${i + 1}: ${account.address.toString()}`);
        
        // Активные балансы
        const activeBalances = account.activeBalances || [];
        console.log(`   💰 Активных позиций: ${activeBalances.length}`);
        
        // Все балансы
        if (account.balances) {
          console.log(`   🔍 Всего балансов: ${account.balances.length}`);
          
          let hasLiabilities = false;
          account.balances.forEach((balance, index) => {
            const liabilities = parseFloat(balance.liabilityShares.toString());
            if (liabilities > 0) {
              hasLiabilities = true;
              console.log(`   🚨 ДОЛГ ${index + 1}: ${liabilities} (Bank: ${balance.bankPk.toString().slice(0, 8)}...)`);
            }
          });
          
          if (!hasLiabilities) {
            console.log(`   ✅ Долгов не найдено`);
          }
        }

        // Health factor
        try {
          const healthFactor = account.getHealthFactor();
          console.log(`   💊 Health Factor: ${healthFactor}`);
        } catch (error) {
          console.log(`   ❌ Health Factor error: ${error.message}`);
        }
      }

    } catch (error) {
      console.log(`❌ Ошибка получения всех аккаунтов: ${error.message}`);
    }
  }

  async run() {
    try {
      await this.initialize();
      await this.investigateAccount();
      await this.investigateRawAccountData();
      await this.investigateAllAccounts();
      
      console.log('\n🏁 РАССЛЕДОВАНИЕ ЗАВЕРШЕНО');
      console.log('═══════════════════════════════════════════════════════════════');
      
    } catch (error) {
      console.error(`❌ Критическая ошибка: ${error.message}`);
      console.log(`🔍 Stack trace: ${error.stack}`);
    }
  }
}

// Запуск расследования
const detective = new MarginFiAccountDetective();
detective.run();
