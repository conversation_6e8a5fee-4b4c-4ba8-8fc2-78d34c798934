#!/usr/bin/env node

/**
 * 🔍 ДИАГНОСТИКА MARGINFI buildFlashLoanTx С ALT ТАБЛИЦАМИ
 * Проверяем что именно происходит с ALT таблицами в buildFlashLoanTx
 */

const { Connection, PublicKey, SystemProgram } = require('@solana/web3.js');
const fs = require('fs');

async function debugMarginFiBuildFlashLoanALT() {
    console.log('🔍 ДИАГНОСТИКА MARGINFI buildFlashLoanTx С ALT ТАБЛИЦАМИ');
    console.log('=' .repeat(80));

    try {
        // 1. Подключение к Solana
        console.log('🔗 Подключение к Solana RPC...');
        const connection = new Connection('https://api.mainnet-beta.solana.com');
        
        // 2. Загружаем ALT таблицы из локальных файлов
        console.log('\n📁 Загрузка ALT таблиц из локальных файлов...');
        
        const altTables = [];
        
        // Meteora ALT
        const meteoraFile = './meteora-alt-cache.json';
        if (fs.existsSync(meteoraFile)) {
            const meteoraCache = JSON.parse(fs.readFileSync(meteoraFile, 'utf8'));
            console.log(`✅ Meteora кэш загружен: ${meteoraCache.totalTables} таблиц`);
            
            for (const validation of meteoraCache.validationResults) {
                if (validation.valid && validation.accounts) {
                    const altAccount = {
                        key: new PublicKey(validation.address),
                        state: {
                            addresses: validation.accounts.map(addr => new PublicKey(addr))
                        }
                    };
                    
                    altTables.push(altAccount);
                    console.log(`   ALT: ${validation.address.slice(0, 8)}... (${validation.accounts.length} аккаунтов)`);
                }
            }
        }
        
        // MarginFi ALT
        const marginfiFile = './marginfi-alt-cache.json';
        if (fs.existsSync(marginfiFile)) {
            const marginfiCache = JSON.parse(fs.readFileSync(marginfiFile, 'utf8'));
            console.log(`✅ MarginFi кэш загружен: ${marginfiCache.totalTables} таблиц`);
            
            for (const validation of marginfiCache.validationResults) {
                if (validation.valid && validation.accounts) {
                    const altAccount = {
                        key: new PublicKey(validation.address),
                        state: {
                            addresses: validation.accounts.map(addr => new PublicKey(addr))
                        }
                    };
                    
                    altTables.push(altAccount);
                    console.log(`   ALT: ${validation.address.slice(0, 8)}... (${validation.accounts.length} аккаунтов)`);
                }
            }
        }
        
        console.log(`\n📊 Всего ALT таблиц загружено: ${altTables.length}`);
        const totalAccounts = altTables.reduce((sum, alt) => sum + alt.state.addresses.length, 0);
        console.log(`📊 Всего аккаунтов: ${totalAccounts}`);

        // 3. Проверяем структуру ALT таблиц
        console.log('\n🔍 Проверка структуры ALT таблиц...');
        
        altTables.forEach((alt, index) => {
            console.log(`\n📊 ALT ${index + 1}:`);
            console.log(`   Адрес: ${alt.key.toString()}`);
            console.log(`   Тип key: ${typeof alt.key} (${alt.key.constructor.name})`);
            console.log(`   Аккаунтов: ${alt.state.addresses.length}`);
            console.log(`   Тип addresses[0]: ${typeof alt.state.addresses[0]} (${alt.state.addresses[0].constructor.name})`);
            console.log(`   Первый аккаунт: ${alt.state.addresses[0].toString()}`);
            
            // Проверяем что это правильный AddressLookupTableAccount
            const hasKey = alt.hasOwnProperty('key');
            const hasState = alt.hasOwnProperty('state');
            const hasAddresses = alt.state && alt.state.hasOwnProperty('addresses');
            const keyIsPublicKey = alt.key instanceof PublicKey;
            const addressesArePublicKeys = alt.state.addresses.length > 0 && alt.state.addresses[0] instanceof PublicKey;
            
            console.log(`   ✅ Структура валидна:`);
            console.log(`      hasKey: ${hasKey}`);
            console.log(`      hasState: ${hasState}`);
            console.log(`      hasAddresses: ${hasAddresses}`);
            console.log(`      keyIsPublicKey: ${keyIsPublicKey}`);
            console.log(`      addressesArePublicKeys: ${addressesArePublicKeys}`);
            
            const isValid = hasKey && hasState && hasAddresses && keyIsPublicKey && addressesArePublicKeys;
            console.log(`   🎯 ALT валидна: ${isValid ? '✅' : '❌'}`);
        });

        // 4. Создаем тестовые инструкции с аккаунтами из ALT
        console.log('\n🧪 Создание тестовых инструкций с аккаунтами из ALT...');
        
        // Берем несколько аккаунтов из ALT таблиц
        const testAccounts = [];
        altTables.forEach(alt => {
            // Берем первые 3 аккаунта из каждой ALT таблицы
            testAccounts.push(...alt.state.addresses.slice(0, 3));
        });
        
        console.log(`📊 Тестовых аккаунтов из ALT: ${testAccounts.length}`);
        
        // Создаем инструкции с этими аккаунтами
        const testInstructions = [];
        
        // Простая инструкция с аккаунтами из ALT
        if (testAccounts.length >= 2) {
            const instruction = {
                programId: testAccounts[0], // Используем аккаунт из ALT как programId
                keys: [
                    { pubkey: testAccounts[1], isSigner: false, isWritable: false },
                    { pubkey: testAccounts[2] || testAccounts[1], isSigner: false, isWritable: false }
                ],
                data: Buffer.from([1, 2, 3]) // Простые данные
            };
            
            testInstructions.push(instruction);
            console.log(`✅ Создана тестовая инструкция с аккаунтами из ALT`);
            console.log(`   ProgramId: ${instruction.programId.toString().slice(0, 8)}...`);
            console.log(`   Keys: ${instruction.keys.length}`);
        }

        // 5. Тестируем compileToV0Message с ALT таблицами
        console.log('\n🔍 Тестирование compileToV0Message с ALT таблицами...');
        
        const { TransactionMessage, VersionedTransaction, Keypair } = require('@solana/web3.js');
        const testWallet = Keypair.generate();
        const { blockhash } = await connection.getLatestBlockhash();
        
        // Создаем простую инструкцию для теста
        const simpleInstruction = SystemProgram.transfer({
            fromPubkey: testWallet.publicKey,
            toPubkey: testWallet.publicKey,
            lamports: 1000
        });
        
        // БЕЗ ALT
        const messageWithoutALT = new TransactionMessage({
            payerKey: testWallet.publicKey,
            recentBlockhash: blockhash,
            instructions: [simpleInstruction]
        }).compileToLegacyMessage();
        
        const txWithoutALT = new VersionedTransaction(messageWithoutALT);
        const sizeWithoutALT = txWithoutALT.serialize().length;
        
        // С ALT
        const messageWithALT = new TransactionMessage({
            payerKey: testWallet.publicKey,
            recentBlockhash: blockhash,
            instructions: [simpleInstruction]
        }).compileToV0Message(altTables);
        
        const txWithALT = new VersionedTransaction(messageWithALT);
        const sizeWithALT = txWithALT.serialize().length;
        
        console.log(`📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ:`);
        console.log(`   Размер БЕЗ ALT: ${sizeWithoutALT} байт`);
        console.log(`   Размер С ALT: ${sizeWithALT} байт`);
        console.log(`   ALT lookups: ${messageWithALT.addressTableLookups.length}`);
        console.log(`   Сжатие: ${((sizeWithoutALT - sizeWithALT) / sizeWithoutALT * 100).toFixed(2)}%`);

        // 6. Тестируем с инструкциями содержащими аккаунты из ALT
        if (testInstructions.length > 0) {
            console.log('\n🔍 Тестирование с инструкциями из ALT аккаунтов...');
            
            try {
                const messageWithALTAccounts = new TransactionMessage({
                    payerKey: testWallet.publicKey,
                    recentBlockhash: blockhash,
                    instructions: testInstructions
                }).compileToV0Message(altTables);
                
                const txWithALTAccounts = new VersionedTransaction(messageWithALTAccounts);
                const sizeWithALTAccounts = txWithALTAccounts.serialize().length;
                
                console.log(`📊 РЕЗУЛЬТАТЫ С ALT АККАУНТАМИ:`);
                console.log(`   Размер: ${sizeWithALTAccounts} байт`);
                console.log(`   ALT lookups: ${messageWithALTAccounts.addressTableLookups.length}`);
                
                // Анализируем lookups
                messageWithALTAccounts.addressTableLookups.forEach((lookup, index) => {
                    console.log(`   Lookup ${index + 1}:`);
                    console.log(`      ALT адрес: ${lookup.accountKey.toString().slice(0, 8)}...`);
                    console.log(`      Writable indexes: ${lookup.writableIndexes.length}`);
                    console.log(`      Readonly indexes: ${lookup.readonlyIndexes.length}`);
                });
                
            } catch (error) {
                console.log(`❌ Ошибка создания транзакции с ALT аккаунтами: ${error.message}`);
            }
        }

        // 7. Диагноз проблемы
        console.log('\n🎯 ДИАГНОЗ ПРОБЛЕМЫ:');
        console.log('-' .repeat(50));
        
        if (messageWithALT.addressTableLookups.length === 0) {
            console.log(`❌ КРИТИЧЕСКАЯ ПРОБЛЕМА: ALT таблицы НЕ ИСПОЛЬЗУЮТСЯ!`);
            console.log(`💡 ПРИЧИНЫ:`);
            console.log(`   1. Аккаунты в инструкциях НЕ СОВПАДАЮТ с ALT таблицами`);
            console.log(`   2. ALT таблицы переданы неправильно`);
            console.log(`   3. compileToV0Message не находит совпадений`);
        } else {
            console.log(`✅ ALT таблицы используются: ${messageWithALT.addressTableLookups.length} lookups`);
        }
        
        console.log(`\n💡 РЕКОМЕНДАЦИИ:`);
        console.log(`   1. Проверить что аккаунты в реальных инструкциях есть в ALT таблицах`);
        console.log(`   2. Убедиться что ALT таблицы правильно передаются в buildFlashLoanTx`);
        console.log(`   3. Проверить что MarginFi buildFlashLoanTx использует переданные ALT таблицы`);
        console.log(`   4. Возможно нужно создать специализированную ALT таблицу`);

        // 8. Проверяем специализированную ALT таблицу
        const specializedFile = './specialized-arbitrage-alt.json';
        if (fs.existsSync(specializedFile)) {
            console.log('\n🎯 Проверка специализированной ALT таблицы...');
            
            const specializedCache = JSON.parse(fs.readFileSync(specializedFile, 'utf8'));
            console.log(`📊 Специализированных аккаунтов: ${specializedCache.totalAccounts}`);
            
            // Проверяем покрытие специализированных аккаунтов
            const specializedSet = new Set(specializedCache.accounts);
            const altAccountsSet = new Set();
            
            altTables.forEach(alt => {
                alt.state.addresses.forEach(addr => {
                    altAccountsSet.add(addr.toString());
                });
            });
            
            const coveredSpecialized = [];
            const uncoveredSpecialized = [];
            
            specializedCache.accounts.forEach(account => {
                if (altAccountsSet.has(account)) {
                    coveredSpecialized.push(account);
                } else {
                    uncoveredSpecialized.push(account);
                }
            });
            
            console.log(`✅ Покрытых специализированных: ${coveredSpecialized.length}/${specializedCache.totalAccounts}`);
            console.log(`❌ Непокрытых специализированных: ${uncoveredSpecialized.length}/${specializedCache.totalAccounts}`);
            
            const coverage = (coveredSpecialized.length / specializedCache.totalAccounts * 100).toFixed(1);
            console.log(`📊 Покрытие специализированных: ${coverage}%`);
            
            if (uncoveredSpecialized.length > 0) {
                console.log(`\n❌ НЕПОКРЫТЫЕ СПЕЦИАЛИЗИРОВАННЫЕ АККАУНТЫ:`);
                uncoveredSpecialized.slice(0, 5).forEach((account, index) => {
                    console.log(`   ${index + 1}. ${account.slice(0, 8)}...${account.slice(-8)}`);
                });
                if (uncoveredSpecialized.length > 5) {
                    console.log(`   ... и еще ${uncoveredSpecialized.length - 5}`);
                }
            }
        }

    } catch (error) {
        console.error('❌ Ошибка диагностики:', error.message);
        console.error(error.stack);
    }
}

// Запуск диагностики
if (require.main === module) {
    debugMarginFiBuildFlashLoanALT().catch(console.error);
}

module.exports = { debugMarginFiBuildFlashLoanALT };
