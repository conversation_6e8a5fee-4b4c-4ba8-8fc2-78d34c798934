const { Connection, PublicKey, TransactionInstruction, Transaction, Keypair } = require('@solana/web3.js');

/**
 * 🔧 ОТЛАДКА ОШИБКИ METEORA 0x1779
 * Цель: получить детальные логи ошибки Meteora DLMM
 */

async function debugMeteoraError() {
    try {
        console.log('🔧 ОТЛАДКА ОШИБКИ METEORA 0x1779...');
        
        // Подключение к Solana (публичный RPC для отладки)
        const connection = new Connection('https://solana-mainnet.g.alchemy.com/v2/alch-demo', 'confirmed');
        
        // Используем ваш публичный ключ для тестирования
        const wallet = {
            publicKey: new PublicKey('bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV'),
            secretKey: null // Для симуляции не нужен приватный ключ
        };
        
        console.log(`✅ Кошелек: ${wallet.publicKey.toString()}`);
        
        // 🎯 СОЗДАЕМ МИНИМАЛЬНУЮ METEORA DLMM SWAP ИНСТРУКЦИЮ
        const meteoraProgram = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');
        const poolAddress = new PublicKey('5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6');
        
        // Discriminator из системы
        const swapDiscriminator = Buffer.from([0xf8, 0xc6, 0x9e, 0x91, 0xe1, 0x75, 0x87, 0xc8]);
        
        // Минимальные данные инструкции
        const amount = ***********; // 15 SOL
        const instructionData = Buffer.alloc(16);
        swapDiscriminator.copy(instructionData, 0);
        instructionData.writeBigUInt64LE(BigInt(amount), 8);
        
        console.log(`✅ Instruction Data: ${instructionData.toString('hex')}`);
        
        // Минимальная структура аккаунтов (из логов системы)
        const accounts = [
            // Pool
            { pubkey: poolAddress, isSigner: false, isWritable: true },
            // User (Signer)
            { pubkey: wallet.publicKey, isSigner: true, isWritable: false },
            // Reserve X
            { pubkey: new PublicKey('EYj9xKw6ZszwpyNibHY7JD5o3QgTVrSdcBp1fMJhrR9o'), isSigner: false, isWritable: true },
            // Reserve Y
            { pubkey: new PublicKey('CoaxzEh8p5YyGLcj36Eo3cUThVJxeKCs7qvLAGDYwBcz'), isSigner: false, isWritable: true },
            // SOL Account
            { pubkey: wallet.publicKey, isSigner: false, isWritable: true },
            // USDC Account
            { pubkey: new PublicKey('********************************************'), isSigner: false, isWritable: true }
        ];
        
        console.log(`✅ Аккаунтов: ${accounts.length}`);
        
        const swapInstruction = new TransactionInstruction({
            keys: accounts,
            programId: meteoraProgram,
            data: instructionData
        });
        
        console.log('\n🔧 ТЕСТ 1: ТОЛЬКО METEORA SWAP ИНСТРУКЦИЯ');
        
        // Создаем минимальную транзакцию ТОЛЬКО с Meteora swap
        const minimalTransaction = new Transaction();
        minimalTransaction.add(swapInstruction);
        minimalTransaction.feePayer = wallet.publicKey;
        minimalTransaction.recentBlockhash = (await connection.getLatestBlockhash()).blockhash;
        
        console.log(`✅ Транзакция создана: ${minimalTransaction.instructions.length} инструкций`);
        
        // Симулируем минимальную транзакцию (без подписи для симуляции)
        const minimalSimulation = await connection.simulateTransaction(minimalTransaction);
        
        console.log('\n📋 РЕЗУЛЬТАТ МИНИМАЛЬНОЙ ТРАНЗАКЦИИ:');
        if (minimalSimulation.value.err) {
            const error = minimalSimulation.value.err;
            console.log(`❌ Ошибка: ${JSON.stringify(error)}`);
            
            if (minimalSimulation.value.logs) {
                console.log('📋 ДЕТАЛЬНЫЕ ЛОГИ:');
                minimalSimulation.value.logs.forEach((log, i) => {
                    console.log(`   ${i}: ${log}`);
                });
                
                // Анализируем ошибку 0x1779
                const errorLog = minimalSimulation.value.logs.find(log => log.includes('0x1779') || log.includes('6009'));
                if (errorLog) {
                    console.log(`\n🔍 ОШИБКА 0x1779 НАЙДЕНА: ${errorLog}`);
                }
                
                // Ищем программные логи Meteora
                const meteoraLogs = minimalSimulation.value.logs.filter(log => 
                    log.includes('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo') ||
                    log.includes('Program log:') ||
                    log.includes('failed')
                );
                
                if (meteoraLogs.length > 0) {
                    console.log('\n🔍 METEORA ПРОГРАММНЫЕ ЛОГИ:');
                    meteoraLogs.forEach((log, i) => {
                        console.log(`   ${i}: ${log}`);
                    });
                }
            }
        } else {
            console.log(`✅ УСПЕХ! Compute Units: ${minimalSimulation.value.unitsConsumed}`);
        }
        
        console.log('\n🔧 ТЕСТ 2: С COMPUTE BUDGET ИНСТРУКЦИЯМИ');
        
        // Создаем транзакцию с Compute Budget (как в реальной системе)
        const { ComputeBudgetProgram } = require('@solana/web3.js');
        
        const fullTransaction = new Transaction();
        
        // Добавляем Compute Budget инструкции (как в реальной системе)
        fullTransaction.add(
            ComputeBudgetProgram.setComputeUnitLimit({ units: 1400000 }),
            ComputeBudgetProgram.setComputeUnitPrice({ microLamports: 1000 })
        );
        
        // Добавляем Meteora swap инструкцию
        fullTransaction.add(swapInstruction);
        
        fullTransaction.feePayer = wallet.publicKey;
        fullTransaction.recentBlockhash = (await connection.getLatestBlockhash()).blockhash;
        
        console.log(`✅ Полная транзакция создана: ${fullTransaction.instructions.length} инструкций`);
        
        // Симулируем полную транзакцию (без подписи для симуляции)
        const fullSimulation = await connection.simulateTransaction(fullTransaction);
        
        console.log('\n📋 РЕЗУЛЬТАТ ПОЛНОЙ ТРАНЗАКЦИИ:');
        if (fullSimulation.value.err) {
            const error = fullSimulation.value.err;
            console.log(`❌ Ошибка: ${JSON.stringify(error)}`);
            
            if (fullSimulation.value.logs) {
                console.log('📋 ДЕТАЛЬНЫЕ ЛОГИ:');
                fullSimulation.value.logs.forEach((log, i) => {
                    console.log(`   ${i}: ${log}`);
                });
                
                // Анализируем, в какой инструкции происходит ошибка
                if (error.InstructionError) {
                    const [errorIndex, errorDetails] = error.InstructionError;
                    console.log(`\n🔍 ОШИБКА В ИНСТРУКЦИИ ${errorIndex}:`);
                    if (errorIndex === 0) console.log('   Compute Budget Limit');
                    if (errorIndex === 1) console.log('   Compute Budget Price');
                    if (errorIndex === 2) console.log('   Meteora DLMM Swap');
                    
                    console.log(`🔍 Детали ошибки: ${JSON.stringify(errorDetails)}`);
                    
                    if (errorDetails.Custom === 6009) {
                        console.log('🔍 ОШИБКА 0x1779 (6009) - METEORA DLMM ОШИБКА');
                        console.log('   Возможные причины:');
                        console.log('   1. Неправильная структура аккаунтов');
                        console.log('   2. Недостаточно аккаунтов для swap');
                        console.log('   3. Неправильный формат instruction data');
                        console.log('   4. Bin Array аккаунты отсутствуют');
                        console.log('   5. Oracle аккаунты отсутствуют');
                    }
                }
            }
        } else {
            console.log(`✅ УСПЕХ! Compute Units: ${fullSimulation.value.unitsConsumed}`);
        }
        
        console.log('\n🔧 АНАЛИЗ ОШИБКИ 0x1779:');
        console.log('Ошибка 0x1779 (6009) в Meteora DLMM обычно означает:');
        console.log('1. 🚨 Отсутствуют Bin Array аккаунты');
        console.log('2. 🚨 Неправильная структура аккаунтов');
        console.log('3. 🚨 Недостаточно данных в instruction data');
        console.log('4. 🚨 Неправильный порядок аккаунтов');
        console.log('5. 🚨 Отсутствуют Oracle аккаунты');
        
        console.log('\n✅ ОТЛАДКА ЗАВЕРШЕНА!');
        
    } catch (error) {
        console.error('❌ Ошибка отладки:', error.message);
        console.error('Stack:', error.stack);
    }
}

// Запускаем отладку
debugMeteoraError();
