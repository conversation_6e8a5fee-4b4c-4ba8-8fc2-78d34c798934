const { Connection, Keypair } = require('@solana/web3.js');
const { MarginfiClient, getConfig } = require('@mrgnlabs/marginfi-client-v2');
const { NodeWallet } = require('@mrgnlabs/mrgn-common');
const fs = require('fs');

async function demonstrateError6037() {
  console.log('🧪 ДЕМОНСТРАЦИЯ ОШИБКИ 6037 (AccountInFlashloan)');
  console.log('═══════════════════════════════════════════════════════════════');
  console.log('📝 Показываем как выглядит ошибка когда флаг flash loan установлен');

  try {
    // Инициализация
    const connection = new Connection('https://billowing-empty-patron.solana-mainnet.quiknode.pro/c79212a7508ec6a37353793d9be93c917898e858/', 'confirmed');
    
    const walletKeypair = Keypair.fromSecretKey(
      new Uint8Array(JSON.parse(fs.readFileSync('wallet.json', 'utf8')))
    );
    const wallet = new NodeWallet(walletKeypair);
    
    const config = getConfig('production');
    const marginfiClient = await MarginfiClient.fetch(config, wallet, connection);
    
    const marginfiAccounts = await marginfiClient.getMarginfiAccountsForAuthority();
    const marginfiAccount = marginfiAccounts[0];
    
    console.log(`🔑 Wallet: ${wallet.publicKey.toString()}`);
    console.log(`💼 MarginFi аккаунт: ${marginfiAccount.address.toString()}`);

    // Проверяем текущее состояние
    const accountInfo = await connection.getAccountInfo(marginfiAccount.address);
    const flags = accountInfo.data.readUInt8(8);
    const inFlashLoan = (flags & 64) !== 0;
    
    console.log(`\n📊 Текущие флаги: ${flags} (0x${flags.toString(16)})`);
    console.log(`🔍 Flash loan флаг установлен: ${inFlashLoan ? '❌ ДА' : '✅ НЕТ'}`);

    if (!inFlashLoan) {
      console.log('\n✅ Флаг НЕ установлен - ошибка 6037 НЕ появится');
      console.log('💡 Для демонстрации нужен аккаунт с установленным флагом');
      
      // Покажем как выглядит успешное создание
      console.log('\n🧪 Тестируем создание flash loan (должно работать)...');
      const flashLoanTx = await marginfiAccount.buildFlashLoanTx({
        ixs: [],
        signers: []
      });
      
      console.log('✅ Flash loan создан успешно!');
      console.log(`📏 Размер: ${flashLoanTx.serialize().length} байт`);
      console.log('\n📝 Если бы флаг был установлен, вы бы увидели:');
      console.log('   ❌ Error processing Instruction X: Custom program error: 0x1795');
      console.log('   ❌ AnchorError: AccountInFlashloan (6037)');
      console.log('   ❌ Account in flashloan. Wait or validate state.');
      
      return;
    }

    // Если флаг установлен, покажем ошибку
    console.log('\n🚨 Флаг установлен! Попытка создать flash loan покажет ошибку 6037...');
    
    try {
      const flashLoanTx = await marginfiAccount.buildFlashLoanTx({
        ixs: [],
        signers: []
      });
      
      console.log('❓ Неожиданно: flash loan создался без ошибки');
      
    } catch (error) {
      console.log('\n🎯 ВОТ ТАК ВЫГЛЯДИТ ОШИБКА 6037:');
      console.log('═══════════════════════════════════════════════════════════════');
      
      // Анализируем ошибку
      const errorMessage = error.message;
      
      if (errorMessage.includes('6037') || errorMessage.includes('0x1795')) {
        console.log('✅ ОБНАРУЖЕНА ОШИБКА 6037 (AccountInFlashloan)!');
        console.log('─────────────────────────────────────────────────────────────');
        console.log(`❌ Полное сообщение: ${errorMessage}`);
        
        // Проверяем различные форматы ошибки
        if (errorMessage.includes('0x1795')) {
          console.log('🔍 Формат: Custom program error: 0x1795 (шестнадцатеричный)');
          console.log('📊 0x1795 = 6037 в десятичной системе');
        }
        
        if (errorMessage.includes('6037')) {
          console.log('🔍 Формат: Error Number: 6037 (десятичный)');
        }
        
        if (errorMessage.includes('AccountInFlashloan')) {
          console.log('🔍 Формат: Error Code: AccountInFlashloan (текстовый)');
        }
        
        console.log('\n💡 ЭТО ОЗНАЧАЕТ:');
        console.log('   🚨 Аккаунт застрял в состоянии flash loan');
        console.log('   🛠️ Нужно выполнить: node reset-marginfi-flashloan-state.js');
        console.log('   ⏰ Или подождать автоматического сброса');
        
      } else {
        console.log('❓ Другая ошибка (не 6037):');
        console.log(`   ${errorMessage}`);
      }
    }

  } catch (error) {
    console.error(`❌ Ошибка демонстрации: ${error.message}`);
  }
}

// ФУНКЦИЯ ДЛЯ МОНИТОРИНГА ОШИБОК В РЕАЛЬНОМ ВРЕМЕНИ
async function monitorForError6037() {
  console.log('\n🔄 МОНИТОРИНГ ОШИБОК 6037 В РЕАЛЬНОМ ВРЕМЕНИ');
  console.log('═══════════════════════════════════════════════════════════════');
  console.log('🎯 Будем пытаться создавать flash loan каждые 10 секунд');
  console.log('🚨 При появлении ошибки 6037 - покажем детали');
  console.log('Нажмите Ctrl+C для остановки');

  const connection = new Connection('https://billowing-empty-patron.solana-mainnet.quiknode.pro/c79212a7508ec6a37353793d9be93c917898e858/', 'confirmed');
  
  const walletKeypair = Keypair.fromSecretKey(
    new Uint8Array(JSON.parse(fs.readFileSync('wallet.json', 'utf8')))
  );
  const wallet = new NodeWallet(walletKeypair);
  
  const config = getConfig('production');
  const marginfiClient = await MarginfiClient.fetch(config, wallet, connection);
  
  const marginfiAccounts = await marginfiClient.getMarginfiAccountsForAuthority();
  const marginfiAccount = marginfiAccounts[0];

  let attemptCount = 0;

  const monitor = async () => {
    attemptCount++;
    console.log(`\n⏰ ${new Date().toLocaleTimeString()} - Попытка #${attemptCount}`);
    
    try {
      const flashLoanTx = await marginfiAccount.buildFlashLoanTx({
        ixs: [],
        signers: []
      });
      
      console.log('✅ Flash loan работает нормально');
      
    } catch (error) {
      if (error.message.includes('6037') || error.message.includes('0x1795') || error.message.includes('AccountInFlashloan')) {
        console.log('🚨🚨🚨 ОБНАРУЖЕНА ОШИБКА 6037! 🚨🚨🚨');
        console.log('═══════════════════════════════════════════════════════════════');
        console.log(`❌ Время: ${new Date().toLocaleString()}`);
        console.log(`❌ Попытка: #${attemptCount}`);
        console.log(`❌ Ошибка: ${error.message}`);
        console.log('🛠️ ТРЕБУЕТСЯ СБРОС ФЛАГА!');
        console.log('🔧 Выполните: node reset-marginfi-flashloan-state.js');
        console.log('═══════════════════════════════════════════════════════════════');
        
        return true; // Останавливаем мониторинг
      } else {
        console.log(`❓ Другая ошибка: ${error.message}`);
      }
    }
    
    return false; // Продолжаем мониторинг
  };

  // Первая проверка
  const shouldStop = await monitor();
  if (shouldStop) return;

  // Периодические проверки
  const interval = setInterval(async () => {
    const shouldStop = await monitor();
    if (shouldStop) {
      clearInterval(interval);
    }
  }, 10000); // Каждые 10 секунд
}

async function main() {
  // Демонстрация
  await demonstrateError6037();
  
  // Раскомментируйте для мониторинга:
  // await monitorForError6037();
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { demonstrateError6037, monitorForError6037 };
