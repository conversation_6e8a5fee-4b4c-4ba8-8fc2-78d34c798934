const FlashLoanStateMonitor = require('./monitor-flashloan-state.js');

class StuckFlashLoanDetector {
  constructor() {
    this.monitor = new FlashLoanStateMonitor();
    this.lastSuccessfulFlashLoan = null;
    this.consecutiveFailures = 0;
    this.maxConsecutiveFailures = 3;
  }

  async initialize() {
    return await this.monitor.initialize();
  }

  // ДЕТЕКТОР ЗАСТРЯВШЕГО ФЛАГА
  async detectStuckState() {
    console.log('\n🕵️ ДЕТЕКТОР ЗАСТРЯВШЕГО FLASH LOAN ФЛАГА');
    console.log('═══════════════════════════════════════════════════════════════');

    const result = await this.monitor.checkState();
    
    if (!result.canUseFlashLoan && result.flashLoanCheck.inFlashLoan) {
      this.consecutiveFailures++;
      
      console.log('\n🚨 ОБНАРУЖЕН ЗАСТРЯВШИЙ ФЛАГ!');
      console.log('─────────────────────────────────────────────────────────────');
      console.log(`❌ Попыток подряд: ${this.consecutiveFailures}`);
      console.log(`🔍 Ошибка: ${result.flashLoanCheck.errorType}`);
      console.log(`📝 Детали: ${result.flashLoanCheck.error}`);
      
      if (this.consecutiveFailures >= this.maxConsecutiveFailures) {
        console.log('\n🚨 КРИТИЧЕСКОЕ СОСТОЯНИЕ!');
        console.log(`❌ ${this.consecutiveFailures} неудачных попыток подряд`);
        console.log('🛠️ ТРЕБУЕТСЯ СБРОС ФЛАГА!');
        
        return {
          isStuck: true,
          consecutiveFailures: this.consecutiveFailures,
          needsReset: true,
          recommendation: 'Запустите reset-marginfi-flashloan-state.js'
        };
      } else {
        console.log('\n⏳ Возможно временная проблема...');
        console.log(`🔄 Попробуем еще ${this.maxConsecutiveFailures - this.consecutiveFailures} раз`);
        
        return {
          isStuck: false,
          consecutiveFailures: this.consecutiveFailures,
          needsReset: false,
          recommendation: 'Подождите и повторите проверку'
        };
      }
    } else if (result.canUseFlashLoan) {
      this.consecutiveFailures = 0;
      this.lastSuccessfulFlashLoan = new Date();
      
      console.log('\n✅ FLASH LOAN РАБОТАЕТ!');
      console.log('─────────────────────────────────────────────────────────────');
      console.log(`🎉 Флаг сброшен успешно!`);
      console.log(`⏰ Время проверки: ${this.lastSuccessfulFlashLoan.toLocaleTimeString()}`);
      
      return {
        isStuck: false,
        consecutiveFailures: 0,
        needsReset: false,
        recommendation: 'Можно выполнять flash loan арбитраж!'
      };
    } else {
      console.log('\n❓ НЕИЗВЕСТНАЯ ПРОБЛЕМА');
      console.log('─────────────────────────────────────────────────────────────');
      console.log(`🔍 Не flash loan ошибка: ${result.flashLoanCheck.error}`);
      
      return {
        isStuck: false,
        consecutiveFailures: this.consecutiveFailures,
        needsReset: false,
        recommendation: 'Проверьте другие возможные проблемы'
      };
    }
  }

  // АВТОМАТИЧЕСКИЙ МОНИТОРИНГ С ДЕТЕКЦИЕЙ
  async startAutoDetection(checkIntervalSeconds = 60) {
    console.log('\n🤖 АВТОМАТИЧЕСКИЙ ДЕТЕКТОР ЗАСТРЯВШИХ ФЛАГОВ');
    console.log('═══════════════════════════════════════════════════════════════');
    console.log(`🔄 Проверка каждые ${checkIntervalSeconds} секунд`);
    console.log('🚨 Автоматическое обнаружение застрявших состояний');
    console.log('Нажмите Ctrl+C для остановки');

    const checkAndDetect = async () => {
      console.log(`\n⏰ ${new Date().toLocaleTimeString()} - Автоматическая проверка...`);
      
      const detection = await this.detectStuckState();
      
      if (detection.needsReset) {
        console.log('\n🚨🚨🚨 ТРЕБУЕТСЯ НЕМЕДЛЕННОЕ ВМЕШАТЕЛЬСТВО! 🚨🚨🚨');
        console.log('═══════════════════════════════════════════════════════════════');
        console.log('❌ Flash loan флаг застрял!');
        console.log('🛠️ Выполните команду: node reset-marginfi-flashloan-state.js');
        console.log('⏰ Или создайте новый MarginFi аккаунт');
        console.log('═══════════════════════════════════════════════════════════════');
        
        // Можно добавить автоматический сброс:
        // await this.autoReset();
        
        return true; // Останавливаем мониторинг для ручного вмешательства
      }
      
      return false; // Продолжаем мониторинг
    };

    // Первая проверка
    const shouldStop = await checkAndDetect();
    if (shouldStop) return;

    // Периодические проверки
    const interval = setInterval(async () => {
      const shouldStop = await checkAndDetect();
      if (shouldStop) {
        clearInterval(interval);
      }
    }, checkIntervalSeconds * 1000);
  }

  // БЫСТРАЯ ПРОВЕРКА ПЕРЕД ВАЖНЫМИ ОПЕРАЦИЯМИ
  async quickCheck() {
    console.log('⚡ БЫСТРАЯ ПРОВЕРКА ПЕРЕД FLASH LOAN');
    
    const result = await this.monitor.checkState();
    
    if (result.canUseFlashLoan) {
      console.log('✅ Готов к flash loan!');
      return true;
    } else if (result.flashLoanCheck.inFlashLoan) {
      console.log('❌ СТОП! Флаг flash loan установлен!');
      console.log('🛠️ Сначала выполните: node reset-marginfi-flashloan-state.js');
      return false;
    } else {
      console.log('❓ Другая проблема, проверьте детали');
      return false;
    }
  }
}

async function main() {
  const detector = new StuckFlashLoanDetector();
  
  const initialized = await detector.initialize();
  if (!initialized) {
    console.log('❌ Не удалось инициализировать детектор');
    return;
  }

  // Выберите режим работы:
  
  // 1. Одноразовая детекция
  await detector.detectStuckState();
  
  // 2. Быстрая проверка (раскомментируйте):
  // const canProceed = await detector.quickCheck();
  // console.log(`Можно продолжать: ${canProceed}`);
  
  // 3. Автоматический мониторинг (раскомментируйте):
  // await detector.startAutoDetection(60); // Проверка каждую минуту
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = StuckFlashLoanDetector;
