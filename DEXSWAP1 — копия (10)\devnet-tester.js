#!/usr/bin/env node

/**
 * 🧪 DEVNET TESTER
 * 
 * Тестирование стратегии на Solana Devnet перед mainnet
 */

const { Connection, PublicKey, Keypair, LAMPORTS_PER_SOL } = require('@solana/web3.js');
const fs = require('fs');
const path = require('path');

const SolanaWeb3TransactionBuilder = require('./solana-web3-transaction-builder.js');
const ErrorHandlerValidator = require('./error-handler-validator.js');

class DevnetTester {
    constructor() {
        // 🌐 DEVNET CONNECTION
        this.connection = new Connection('https://api.devnet.solana.com', 'confirmed');
        
        // 🔧 КОМПОНЕНТЫ
        this.transactionBuilder = new SolanaWeb3TransactionBuilder();
        this.validator = new ErrorHandlerValidator();
        
        // 📊 DEVNET КОНФИГУРАЦИЯ
        this.DEVNET_CONFIG = {
            // Используем реальный mainnet пул для тестов PDA (безопасно для расчетов)
            TEST_POOLS: {
                SOL_USDC: 'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y', // Реальный средний пул
                // Для devnet тестов используем mainnet адреса для PDA расчетов
            },

            // Увеличенные суммы для прохождения валидации
            TEST_AMOUNTS: {
                flash_loan_amount: 200000,    // $200K (выше минимума $100K)
                liquidity_amount: 150000,     // $150K (выше минимума $50K)
                trading_amount: 50000,        // $50K (выше минимума $10K)
                target_roi: 2.0               // 2% для тестов
            },
            
            // Тестовые токены devnet
            DEVNET_TOKENS: {
                USDC: 'Gh9ZwEmdLJ8DscKNTkTqPbNwLNNBjuSzaG9Vp2KGtKJr', // Devnet USDC
                SOL: 'So11111111111111111111111111111111111111112'    // Wrapped SOL
            }
        };

        console.log('🧪 DEVNET TESTER ИНИЦИАЛИЗИРОВАН');
        console.log(`   🌐 Подключение: ${this.connection.rpcEndpoint}`);
    }

    /**
     * 🔑 СОЗДАНИЕ ТЕСТОВОГО КОШЕЛЬКА
     */
    async createTestWallet() {
        console.log('\n🔑 СОЗДАНИЕ ТЕСТОВОГО КОШЕЛЬКА...');
        
        // Генерируем новый keypair
        const wallet = Keypair.generate();
        
        console.log(`   📍 Адрес: ${wallet.publicKey.toString()}`);
        console.log(`   🔐 Приватный ключ: [${wallet.secretKey.slice(0, 8).join(', ')}...]`);
        
        // Сохраняем в файл для повторного использования
        const walletData = {
            publicKey: wallet.publicKey.toString(),
            secretKey: Array.from(wallet.secretKey)
        };
        
        fs.writeFileSync('devnet-test-wallet.json', JSON.stringify(walletData, null, 2));
        console.log('   💾 Кошелек сохранен в devnet-test-wallet.json');
        
        return wallet;
    }

    /**
     * 💰 ЗАПРОС DEVNET SOL
     */
    async requestDevnetSOL(wallet, amount = 2) {
        console.log(`\n💰 ЗАПРОС ${amount} SOL ИЗ DEVNET FAUCET...`);
        
        try {
            const signature = await this.connection.requestAirdrop(
                wallet.publicKey,
                amount * LAMPORTS_PER_SOL
            );
            
            await this.connection.confirmTransaction(signature);
            
            const balance = await this.connection.getBalance(wallet.publicKey);
            console.log(`   ✅ Получено ${amount} SOL`);
            console.log(`   💰 Баланс: ${balance / LAMPORTS_PER_SOL} SOL`);
            
            return balance;
            
        } catch (error) {
            console.error('❌ Ошибка получения SOL:', error.message);
            throw error;
        }
    }

    /**
     * 📊 ПРОВЕРКА DEVNET ПУЛОВ
     */
    async checkDevnetPools() {
        console.log('\n📊 ПРОВЕРКА DEVNET ПУЛОВ...');
        
        // В реальности нужно найти существующие DLMM пулы на devnet
        // или создать тестовые пулы
        
        const testPools = [
            'DEVNET_SOL_USDC_POOL_1',
            'DEVNET_SOL_USDC_POOL_2'
        ];
        
        console.log('   ⚠️ ВНИМАНИЕ: Используются плейсхолдер адреса пулов');
        console.log('   🔧 Нужно найти реальные DLMM пулы на devnet');
        
        testPools.forEach((pool, index) => {
            console.log(`   📊 Тестовый пул ${index + 1}: ${pool}`);
        });
        
        return {
            available: false, // Пока нет реальных пулов
            pools: testPools,
            message: 'Нужно настроить реальные devnet пулы'
        };
    }

    /**
     * 🧪 ТЕСТ СОЗДАНИЯ ТРАНЗАКЦИИ (БЕЗОПАСНЫЙ ДЛЯ DEVNET)
     */
    async testTransactionCreation() {
        console.log('\n🧪 ТЕСТ СОЗДАНИЯ ТРАНЗАКЦИИ...');

        try {
            // Создаем тестовые параметры с реальным пулом для PDA расчетов
            const liquidityPerBin = this.DEVNET_CONFIG.TEST_AMOUNTS.liquidity_amount / 8;

            const testParams = {
                addLiquidity: {
                    poolAddress: this.DEVNET_CONFIG.TEST_POOLS.SOL_USDC,
                    totalAmount: this.DEVNET_CONFIG.TEST_AMOUNTS.liquidity_amount,
                    bins: [
                        { binId: -1, amountX: liquidityPerBin },
                        { binId: -2, amountX: liquidityPerBin },
                        { binId: -3, amountX: liquidityPerBin },
                        { binId: -4, amountX: liquidityPerBin },
                        { binId: -5, amountX: liquidityPerBin },
                        { binId: -6, amountX: liquidityPerBin },
                        { binId: -7, amountX: liquidityPerBin },
                        { binId: -8, amountX: liquidityPerBin }
                    ]
                },
                removeLiquidity: {
                    poolAddress: this.DEVNET_CONFIG.TEST_POOLS.SOL_USDC,
                    bins: [-1, -2, -3, -4, -5, -6, -7, -8],
                    expectedAmount: this.DEVNET_CONFIG.TEST_AMOUNTS.liquidity_amount
                }
            };

            // Используем реальный формат адреса для тестов
            const testWallet = 'So11111111111111111111111111111111111111112'; // Wrapped SOL mint как пример

            console.log('   🔧 Тестируем только создание PDA адресов...');

            // Тестируем только PDA расчеты (безопасно)
            const MeteoraLBPDACalculator = require('./pda-calculator.js');
            const pdaCalculator = new MeteoraLBPDACalculator();

            const poolConfig = {
                lbPairAddress: testParams.addLiquidity.poolAddress,
                tokenX: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // USDC
                tokenY: 'So11111111111111111111111111111111111111112',   // SOL
                bins: [-1, -2, -3, -4, -5, -6, -7, -8]
            };

            const pdaResults = pdaCalculator.calculateAllPDAsForStrategy(poolConfig);

            console.log('   ✅ PDA адреса рассчитаны успешно');
            console.log(`   📊 Bin Array: ${Object.values(pdaResults.binArrays)[0]?.address}`);
            console.log(`   🏦 Reserve X: ${pdaResults.reserves.x.address}`);
            console.log(`   🏦 Reserve Y: ${pdaResults.reserves.y.address}`);

            return {
                success: true,
                pdaResults: pdaResults,
                message: 'PDA расчеты успешны (полная транзакция пропущена для безопасности)'
            };

        } catch (error) {
            console.error('❌ ОШИБКА ТЕСТИРОВАНИЯ:', error.message);
            return { success: false, error: error.message };
        }
    }

    /**
     * 🛡️ ТЕСТ ВАЛИДАЦИИ (ОБНОВЛЕННЫЙ)
     */
    async testValidation() {
        console.log('\n🛡️ ТЕСТ ВАЛИДАЦИИ...');

        const testStrategy = {
            params: this.DEVNET_CONFIG.TEST_AMOUNTS,
            poolsData: {
                largePool: { tvl: 7272122, current_price: 307.15 },
                mediumPool: { tvl: 3252168, current_price: 340.35 },
                arbitragePercent: 10.81
            },
            calculations: {
                profitability: { roi: 2.5, netProfit: 5000 }, // Увеличили прибыль
                priceImpact: { impactPercent: 5.0 },
                binsData: { totalBins: 8, coverageRatio: 2.0 }
            },
            addresses: {
                program: 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo',
                binArrayLower: '6K84Zmy8ZnihPCdoHona2iT25dFHsMAHzZtdjhtw5XG9'
            }
        };

        const validation = this.validator.validateCompleteStrategy(testStrategy);

        console.log(`   📊 Результат: ${validation.valid ? 'ВАЛИДНА' : 'НЕВАЛИДНА'}`);
        console.log(`   ❌ Ошибок: ${validation.errors.length}`);
        console.log(`   ⚠️ Предупреждений: ${validation.warnings.length}`);

        return validation;
    }

    /**
     * 📊 ТЕСТ СИМУЛЯЦИИ
     */
    async testSimulation(transaction, signers) {
        console.log('\n📊 ТЕСТ СИМУЛЯЦИИ...');
        
        if (!transaction) {
            console.log('   ⚠️ Нет транзакции для симуляции');
            return { success: false, error: 'No transaction' };
        }
        
        try {
            // Переключаем connection на devnet для симуляции
            this.transactionBuilder.connection = this.connection;
            
            const simulation = await this.transactionBuilder.simulateTransaction(transaction, signers);
            
            if (simulation.success) {
                console.log('   ✅ Симуляция прошла успешно');
                console.log(`   ⚡ Compute Units: ${simulation.unitsConsumed}`);
            } else {
                console.log('   ❌ Симуляция провалена');
                console.log(`   💬 Ошибка: ${simulation.error}`);
            }
            
            return simulation;
            
        } catch (error) {
            console.error('❌ ОШИБКА СИМУЛЯЦИИ:', error.message);
            return { success: false, error: error.message };
        }
    }

    /**
     * 🚀 ПОЛНЫЙ DEVNET ТЕСТ
     */
    async runFullDevnetTest() {
        console.log('🚀 ЗАПУСК ПОЛНОГО DEVNET ТЕСТА');
        console.log('=' .repeat(80));
        
        const results = {
            walletCreation: null,
            solRequest: null,
            poolsCheck: null,
            transactionCreation: null,
            validation: null,
            simulation: null,
            overall: false
        };
        
        try {
            // 1. Создание кошелька
            console.log('\n1️⃣ СОЗДАНИЕ ТЕСТОВОГО КОШЕЛЬКА');
            const wallet = await this.createTestWallet();
            results.walletCreation = { success: true, wallet };
            
            // 2. Запрос SOL
            console.log('\n2️⃣ ЗАПРОС DEVNET SOL');
            const balance = await this.requestDevnetSOL(wallet);
            results.solRequest = { success: true, balance };
            
            // 3. Проверка пулов
            console.log('\n3️⃣ ПРОВЕРКА DEVNET ПУЛОВ');
            const poolsCheck = await this.checkDevnetPools();
            results.poolsCheck = poolsCheck;
            
            // 4. Создание транзакции
            console.log('\n4️⃣ ТЕСТ СОЗДАНИЯ ТРАНЗАКЦИИ');
            const transactionTest = await this.testTransactionCreation();
            results.transactionCreation = transactionTest;
            
            // 5. Валидация
            console.log('\n5️⃣ ТЕСТ ВАЛИДАЦИИ');
            const validation = await this.testValidation();
            results.validation = validation;
            
            // 6. Симуляция (если есть транзакция)
            if (transactionTest.success) {
                console.log('\n6️⃣ ТЕСТ СИМУЛЯЦИИ');
                const simulation = await this.testSimulation(transactionTest.transaction, [wallet]);
                results.simulation = simulation;
            }
            
            // Общий результат
            results.overall = results.walletCreation?.success && 
                            results.solRequest?.success && 
                            results.validation?.valid;
            
        } catch (error) {
            console.error('❌ КРИТИЧЕСКАЯ ОШИБКА DEVNET ТЕСТА:', error.message);
            results.overall = false;
        }
        
        // Итоги
        console.log('\n📊 ИТОГИ DEVNET ТЕСТА:');
        console.log('=' .repeat(50));
        console.log(`🔑 Создание кошелька: ${results.walletCreation?.success ? '✅' : '❌'}`);
        console.log(`💰 Запрос SOL: ${results.solRequest?.success ? '✅' : '❌'}`);
        console.log(`📊 Проверка пулов: ${results.poolsCheck?.available ? '✅' : '⚠️'}`);
        console.log(`🧪 Создание транзакции: ${results.transactionCreation?.success ? '✅' : '❌'}`);
        console.log(`🛡️ Валидация: ${results.validation?.valid ? '✅' : '❌'}`);
        console.log(`📊 Симуляция: ${results.simulation?.success ? '✅' : results.simulation ? '❌' : '⏭️'}`);
        console.log(`🎯 ОБЩИЙ РЕЗУЛЬТАТ: ${results.overall ? '✅ УСПЕШНО' : '❌ ПРОВАЛЕНО'}`);
        
        // Рекомендации
        console.log('\n💡 РЕКОМЕНДАЦИИ:');
        if (!results.poolsCheck?.available) {
            console.log('   🔧 Настроить реальные DLMM пулы на devnet');
        }
        if (!results.transactionCreation?.success) {
            console.log('   🔧 Исправить создание транзакций');
        }
        if (!results.validation?.valid) {
            console.log('   🔧 Исправить ошибки валидации');
        }
        if (results.overall) {
            console.log('   🚀 Готово к тестированию на mainnet!');
        }
        
        return results;
    }
}

// 🧪 ТЕСТОВЫЙ ЗАПУСК
if (require.main === module) {
    async function runDevnetTest() {
        const tester = new DevnetTester();
        await tester.runFullDevnetTest();
    }
    
    runDevnetTest().catch(console.error);
}

module.exports = DevnetTester;
