/**
 * 🔍 ДИАГНОСТИКА ПОРЯДКА ИНСТРУКЦИЙ В FLASH LOAN
 * ═══════════════════════════════════════════════════════════════
 * 
 * ПРОВЕРЯЕМ:
 * ✅ Правильный порядок: start_flashloan → arbitrage → end_flashloan
 * ❌ Где именно Jupiter требует SOL до получения займа
 * 🔧 Какие инструкции вызывают "insufficient lamports"
 */

const { Connection, Keypair, PublicKey } = require('@solana/web3.js');
const fs = require('fs');

async function diagnoseFlashLoanInstructionOrder() {
  console.log('🔍 ДИАГНОСТИКА ПОРЯДКА ИНСТРУКЦИЙ В FLASH LOAN');
  console.log('═'.repeat(60));

  try {
    // 🔧 Настройка тестового окружения
    const connection = new Connection('https://billowing-empty-patron.solana-mainnet.quiknode.pro/c79212a7508ec6a37353793d9be93c917898e858/', 'confirmed');
    
    const walletKeypair = Keypair.fromSecretKey(
      new Uint8Array(JSON.parse(fs.readFileSync('wallet.json', 'utf8')))
    );

    console.log(`🔑 Кошелек: ${walletKeypair.publicKey.toString()}`);

    // 🎯 ТЕСТ 1: Анализ Jupiter инструкций
    console.log('\n🎯 ТЕСТ 1: Анализ Jupiter инструкций');
    console.log('─'.repeat(50));
    
    const inputMint = 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'; // USDC
    const outputMint = 'So********************************111111112'; // SOL
    const amount = ***********; // 50,000 USDC (как в flash loan)
    
    console.log(`🔄 Анализируем swap: USDC → SOL`);
    console.log(`   Amount: ${amount} micro-USDC (50,000 USDC)`);
    
    // Получаем котировку
    const quoteParams = new URLSearchParams({
      inputMint,
      outputMint,
      amount: amount.toString(),
      slippageBps: '150',
      maxAccounts: '64',
      onlyDirectRoutes: 'false',
      restrictIntermediateTokens: 'false'
    });
    
    const quoteUrl = `https://quote-api.jup.ag/v6/quote?${quoteParams.toString()}`;
    const quoteResponse = await fetch(quoteUrl);
    const quoteData = await quoteResponse.json();
    
    console.log(`✅ Котировка получена: ${quoteData.outAmount} lamports SOL`);
    
    // Создаем swap инструкции
    const swapBody = {
      quoteResponse: quoteData,
      userPublicKey: walletKeypair.publicKey.toString(),
      wrapAndUnwrapSol: true,
      maxAccounts: 64,
      skipUserAccountsRpcCalls: true,
      dynamicComputeUnitLimit: true,
      asLegacyTransaction: false,
      prioritizationFeeLamports: {
        priorityLevelWithMaxLamports: {
          maxLamports: 1000000,
          priorityLevel: "medium"
        }
      }
    };
    
    const swapResponse = await fetch('https://quote-api.jup.ag/v6/swap-instructions', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(swapBody)
    });
    
    const swapData = await swapResponse.json();
    
    console.log('✅ Jupiter инструкции созданы');

    // 🎯 ТЕСТ 2: Анализ каждой инструкции
    console.log('\n🎯 ТЕСТ 2: Анализ каждой инструкции');
    console.log('─'.repeat(50));
    
    const allJupiterInstructions = [];
    
    // Setup инструкции
    if (swapData.setupInstructions && swapData.setupInstructions.length > 0) {
      console.log(`📋 Setup инструкции: ${swapData.setupInstructions.length}`);
      swapData.setupInstructions.forEach((ix, index) => {
        console.log(`   Setup ${index + 1}: ${ix.programId}`);
        
        // Проверяем на System Program transfers
        if (ix.programId === '********************************') {
          console.log(`     🚨 SYSTEM PROGRAM TRANSFER НАЙДЕН В SETUP!`);
          
          // Декодируем данные инструкции
          const data = Buffer.from(ix.data, 'base64');
          if (data.length >= 12) {
            const instructionType = data.readUInt32LE(0);
            if (instructionType === 2) { // Transfer instruction
              const lamports = data.readBigUInt64LE(4);
              console.log(`     💰 Transfer amount: ${lamports} lamports`);
              
              if (lamports > 1000000000n) { // Больше 1 SOL
                console.log(`     🚨 КРИТИЧЕСКАЯ ПРОБЛЕМА: Setup требует ${lamports} lamports!`);
                console.log(`     🚨 ЭТО ПРОИСХОДИТ ДО FLASH LOAN!`);
              }
            }
          }
        }
        
        allJupiterInstructions.push(ix);
      });
    }
    
    // Swap инструкция
    if (swapData.swapInstruction) {
      console.log(`📋 Swap инструкция: ${swapData.swapInstruction.programId}`);
      console.log(`   Аккаунты: ${swapData.swapInstruction.accounts.length}`);
      allJupiterInstructions.push(swapData.swapInstruction);
    }
    
    // Cleanup инструкция
    if (swapData.cleanupInstruction) {
      console.log(`📋 Cleanup инструкция: ${swapData.cleanupInstruction.programId}`);
      
      // Проверяем cleanup на System Program transfers
      if (swapData.cleanupInstruction.programId === '********************************') {
        console.log(`     🚨 SYSTEM PROGRAM TRANSFER НАЙДЕН В CLEANUP!`);
        
        const data = Buffer.from(swapData.cleanupInstruction.data, 'base64');
        if (data.length >= 12) {
          const instructionType = data.readUInt32LE(0);
          if (instructionType === 2) {
            const lamports = data.readBigUInt64LE(4);
            console.log(`     💰 Transfer amount: ${lamports} lamports`);
            
            if (lamports > 1000000000n) {
              console.log(`     🚨 КРИТИЧЕСКАЯ ПРОБЛЕМА: Cleanup требует ${lamports} lamports!`);
              console.log(`     🚨 ЭТО ПРОИСХОДИТ ПОСЛЕ FLASH LOAN!`);
            }
          }
        }
      }
      
      allJupiterInstructions.push(swapData.cleanupInstruction);
    }

    // 🎯 ТЕСТ 3: Проверка WSOL операций
    console.log('\n🎯 ТЕСТ 3: Проверка WSOL операций');
    console.log('─'.repeat(50));
    
    const wsolMint = 'So********************************111111112';
    let wsolOperationsFound = 0;
    
    allJupiterInstructions.forEach((ix, index) => {
      // Проверяем аккаунты на WSOL
      if (ix.accounts) {
        const hasWSOL = ix.accounts.some(account => account === wsolMint);
        if (hasWSOL) {
          wsolOperationsFound++;
          console.log(`   Инструкция ${index + 1}: содержит WSOL операции`);
          console.log(`     Program: ${ix.programId}`);
        }
      }
    });
    
    console.log(`📊 Найдено WSOL операций: ${wsolOperationsFound}`);
    
    if (wsolOperationsFound > 0) {
      console.log(`✅ Jupiter использует WSOL операции (wrapAndUnwrapSol: true работает)`);
    } else {
      console.log(`⚠️ WSOL операции не найдены - возможно проблема в настройках`);
    }

    // 🎯 ТЕСТ 4: Симуляция правильного порядка
    console.log('\n🎯 ТЕСТ 4: Симуляция правильного порядка Flash Loan');
    console.log('─'.repeat(50));
    
    console.log('📋 ПРАВИЛЬНЫЙ ПОРЯДОК FLASH LOAN:');
    console.log('   1. lending_account_start_flashloan ← Предоставляет ликвидность');
    console.log('   2. Jupiter Setup инструкции');
    console.log('   3. Jupiter Swap инструкция');
    console.log('   4. Jupiter Cleanup инструкция');
    console.log('   5. lending_account_end_flashloan ← Проверяет health');
    
    // Проверяем баланс кошелька
    const balance = await connection.getBalance(walletKeypair.publicKey);
    console.log(`\n💰 Текущий баланс кошелька: ${balance} lamports (${(balance / 1e9).toFixed(4)} SOL)`);
    
    // Анализируем требования
    let maxRequiredLamports = 0n;
    allJupiterInstructions.forEach((ix, index) => {
      if (ix.programId === '********************************') {
        const data = Buffer.from(ix.data, 'base64');
        if (data.length >= 12) {
          const instructionType = data.readUInt32LE(0);
          if (instructionType === 2) {
            const lamports = data.readBigUInt64LE(4);
            if (lamports > maxRequiredLamports) {
              maxRequiredLamports = lamports;
            }
          }
        }
      }
    });
    
    if (maxRequiredLamports > 0n) {
      console.log(`🚨 МАКСИМАЛЬНОЕ ТРЕБОВАНИЕ SOL: ${maxRequiredLamports} lamports`);
      console.log(`🚨 ЭТО ${(Number(maxRequiredLamports) / 1e9).toFixed(4)} SOL`);
      
      if (BigInt(balance) < maxRequiredLamports) {
        console.log(`❌ НЕДОСТАТОЧНО SOL НА КОШЕЛЬКЕ!`);
        console.log(`   Нужно: ${(Number(maxRequiredLamports) / 1e9).toFixed(4)} SOL`);
        console.log(`   Есть: ${(balance / 1e9).toFixed(4)} SOL`);
        console.log(`   Не хватает: ${(Number(maxRequiredLamports - BigInt(balance)) / 1e9).toFixed(4)} SOL`);
      } else {
        console.log(`✅ Достаточно SOL на кошельке`);
      }
    }

    // 🎯 РЕЗУЛЬТАТ
    console.log('\n🎉 РЕЗУЛЬТАТ ДИАГНОСТИКИ');
    console.log('═'.repeat(60));
    
    if (maxRequiredLamports > 0n && BigInt(balance) < maxRequiredLamports) {
      console.log('❌ ПРОБЛЕМА НАЙДЕНА: Jupiter требует SOL до Flash Loan!');
      console.log('🔧 РЕШЕНИЯ:');
      console.log('   1. Пополнить кошелек на недостающую сумму SOL');
      console.log('   2. Изменить настройки Jupiter для минимизации SOL требований');
      console.log('   3. Использовать другой маршрут арбитража');
      
      console.log(`\n💰 ТРЕБУЕТСЯ ПОПОЛНЕНИЕ:`);
      console.log(`   Сумма: ${(Number(maxRequiredLamports - BigInt(balance)) / 1e9).toFixed(4)} SOL`);
      console.log(`   Адрес: ${walletKeypair.publicKey.toString()}`);
      
    } else {
      console.log('✅ Проблем с порядком инструкций не найдено');
      console.log('✅ Flash Loan должен работать корректно');
    }

  } catch (error) {
    console.error('❌ ОШИБКА В ДИАГНОСТИКЕ:');
    console.error(`   Сообщение: ${error.message}`);
    
    if (error.message.includes('429')) {
      console.log('\n⚠️ RPC лимит исчерпан - используйте другой endpoint');
    }
  }
}

if (require.main === module) {
  diagnoseFlashLoanInstructionOrder().catch(console.error);
}

module.exports = diagnoseFlashLoanInstructionOrder;
