/**
 * 🔍 ДИАГНОСТИКА ПРОБЛЕМ ОСНОВНОГО БОТА
 */

const { Connection, PublicKey } = require('@solana/web3.js');
const fs = require('fs');

async function diagnoseMainBotIssues() {
  console.log('🔍 ДИАГНОСТИКА ПРОБЛЕМ ОСНОВНОГО БОТА');
  console.log('═'.repeat(80));

  try {
    const connection = new Connection('https://api.mainnet-beta.solana.com', 'confirmed');

    // 1. ПРОВЕРЯЕМ КОНФИГУРАЦИЮ ОСНОВНОГО БОТА
    console.log('\n📋 ШАГ 1: АНАЛИЗ КОНФИГУРАЦИИ ОСНОВНОГО БОТА');
    
    let tradingConfig;
    try {
      tradingConfig = require('./trading-config.js');
      console.log('✅ trading-config.js загружен');
      
      // Анализируем конфигурацию
      console.log('🔍 АНАЛИЗ КОНФИГУРАЦИИ:');
      if (tradingConfig.onlyDirectRoutes !== undefined) {
        console.log(`   🎯 onlyDirectRoutes: ${tradingConfig.onlyDirectRoutes}`);
      }
      if (tradingConfig.maxAccounts !== undefined) {
        console.log(`   📊 maxAccounts: ${tradingConfig.maxAccounts}`);
      }
      if (tradingConfig.slippageBps !== undefined) {
        console.log(`   📈 slippageBps: ${tradingConfig.slippageBps}`);
      }
      
    } catch (e) {
      console.log(`❌ Ошибка загрузки trading-config.js: ${e.message}`);
    }

    // 2. ПРОВЕРЯЕМ ALT ТАБЛИЦЫ В ОСНОВНОМ КОДЕ
    console.log('\n🗜️ ШАГ 2: ПРОВЕРКА ALT ТАБЛИЦ В ОСНОВНОМ КОДЕ');
    
    const mainBotALTs = [
      'HGmknUTUmeovMc9ryERNWG6UFZDFDVr9xrum3ZhyL4fC', // MarginFi ALT 1 (256 ключей)
      '5FuKF7C1tJji2mXZuJ14U9oDb37is5mmvYLf4KwojoF1', // MarginFi ALT 2 (256 ключей)
      'FEFhAFKz48P3w82Ds5VhvyEDwhRqu2FejmnuxEPZ8wNR', // MarginFi ALT 3 (19 ключей)
      'FAeyUf4AdG7vTQ2Q89r9hWBkPdYkykJKG4CxgqArBGXe'  // Custom ALT (18 ключей)
    ];

    console.log(`📊 Основной бот использует ${mainBotALTs.length} ALT таблиц:`);
    
    let totalKeys = 0;
    for (let i = 0; i < mainBotALTs.length; i++) {
      const address = mainBotALTs[i];
      try {
        const altAccount = await connection.getAddressLookupTable(new PublicKey(address));
        if (altAccount.value) {
          const keyCount = altAccount.value.addresses.length;
          totalKeys += keyCount;
          console.log(`   ${i + 1}. ${address.slice(0, 8)}... (${keyCount} ключей)`);
        } else {
          console.log(`   ${i + 1}. ${address.slice(0, 8)}... ❌ НЕ ЗАГРУЖЕНА`);
        }
      } catch (e) {
        console.log(`   ${i + 1}. ${address.slice(0, 8)}... ❌ ОШИБКА: ${e.message}`);
      }
    }
    
    console.log(`📊 ВСЕГО ключей в ALT основного бота: ${totalKeys}`);

    // 3. ПРОВЕРЯЕМ JUPITER ALT КОТОРЫХ НЕТ В ОСНОВНОМ КОДЕ
    console.log('\n🪐 ШАГ 3: ПРОВЕРКА ОТСУТСТВУЮЩИХ JUPITER ALT');
    
    const missingJupiterALTs = [
      '2immgwYNHBbyVQKVGCEkgWpi53bLwWNRMB5G2nbgYV17' // Jupiter ALT (24 ключа)
    ];

    console.log(`❌ ОТСУТСТВУЮЩИЕ Jupiter ALT в основном коде:`);
    
    for (let i = 0; i < missingJupiterALTs.length; i++) {
      const address = missingJupiterALTs[i];
      try {
        const altAccount = await connection.getAddressLookupTable(new PublicKey(address));
        if (altAccount.value) {
          const keyCount = altAccount.value.addresses.length;
          console.log(`   ${i + 1}. ${address.slice(0, 8)}... (${keyCount} ключей) ✅ ДОСТУПНА`);
          
          // Показываем первые несколько ключей
          const firstKeys = altAccount.value.addresses.slice(0, 5);
          firstKeys.forEach((key, idx) => {
            console.log(`      ${idx + 1}. ${key.toString()}`);
          });
          if (altAccount.value.addresses.length > 5) {
            console.log(`      ... и еще ${altAccount.value.addresses.length - 5} ключей`);
          }
        } else {
          console.log(`   ${i + 1}. ${address.slice(0, 8)}... ❌ НЕ ДОСТУПНА`);
        }
      } catch (e) {
        console.log(`   ${i + 1}. ${address.slice(0, 8)}... ❌ ОШИБКА: ${e.message}`);
      }
    }

    // 4. АНАЛИЗИРУЕМ РАЗМЕРЫ ТРАНЗАКЦИЙ
    console.log('\n📏 ШАГ 4: АНАЛИЗ РАЗМЕРОВ ТРАНЗАКЦИЙ');
    
    console.log('🔍 СРАВНЕНИЕ РАЗМЕРОВ:');
    console.log(`   🧪 Наш тест: 1170 байт (19 инструкций, 5 ALT)`);
    console.log(`   🤖 Основной бот: 1120 байт (12 инструкций, 4 ALT)`);
    console.log(`   📊 Разница: ${1170 - 1120} байт`);
    console.log(`   🔒 Лимит Solana: 1232 байт`);
    
    console.log('\n🔍 АНАЛИЗ ПРИЧИН РАЗНИЦЫ:');
    console.log(`   📋 Инструкций: ${19 - 12} = 7 дополнительных в тесте`);
    console.log(`   🗜️ ALT таблиц: ${5 - 4} = 1 дополнительная в тесте`);
    console.log(`   💡 ПРИЧИНА: Тест использует больше Jupiter инструкций`);

    // 5. ПРОВЕРЯЕМ ПРОБЛЕМУ "encoding overruns"
    console.log('\n🚨 ШАГ 5: АНАЛИЗ ОШИБКИ "encoding overruns Uint8Array"');
    
    console.log('🔍 ПРИЧИНЫ ОШИБКИ:');
    console.log(`   1. ❌ 21 ключ НЕ покрыт ALT таблицами`);
    console.log(`   2. ❌ Jupiter ALT отсутствуют в основном коде`);
    console.log(`   3. ❌ Транзакция превышает лимит при сериализации`);
    console.log(`   4. ❌ Неправильная структура инструкций`);

    // 6. ПРОВЕРЯЕМ onlyDirectRoutes
    console.log('\n🎯 ШАГ 6: АНАЛИЗ onlyDirectRoutes');
    
    console.log('🔍 ПРОБЛЕМА С ПРЯМЫМИ ПУТЯМИ:');
    console.log(`   ✅ onlyDirectRoutes: true (включены прямые пути)`);
    console.log(`   ❌ НО: Jupiter все равно создает сложные маршруты`);
    console.log(`   💡 ПРИЧИНА: Jupiter API игнорирует onlyDirectRoutes для некоторых пар`);
    console.log(`   🔧 РЕШЕНИЕ: Принудительная фильтрация маршрутов`);

    // 7. РЕКОМЕНДАЦИИ ПО ИСПРАВЛЕНИЮ
    console.log('\n🔧 ШАГ 7: РЕКОМЕНДАЦИИ ПО ИСПРАВЛЕНИЮ');
    
    console.log('🎯 КРИТИЧЕСКИЕ ИСПРАВЛЕНИЯ:');
    console.log(`   1. 🗜️ ДОБАВИТЬ Jupiter ALT в основной код:`);
    console.log(`      - 2immgwYNHBbyVQKVGCEkgWpi53bLwWNRMB5G2nbgYV17`);
    console.log(`   2. 📊 ОГРАНИЧИТЬ количество инструкций до 12-15`);
    console.log(`   3. 🎯 ПРИНУДИТЕЛЬНО фильтровать сложные маршруты`);
    console.log(`   4. 🔒 ЗАФИКСИРОВАТЬ размер транзакции на 1120-1170 байт`);
    console.log(`   5. ⚡ ОТКЛЮЧИТЬ обфускацию при превышении лимита`);

    console.log('\n💡 БЫСТРЫЕ ИСПРАВЛЕНИЯ:');
    console.log(`   1. В complete-alt-manager.js добавить Jupiter ALT`);
    console.log(`   2. В trading-config.js установить maxAccounts: 10`);
    console.log(`   3. В master-transaction-controller.js увеличить лимит ALT`);
    console.log(`   4. Использовать зафиксированную конфигурацию из FIXED-TRANSACTION-CONFIG.js`);

    return {
      mainBotALTs: mainBotALTs.length,
      missingJupiterALTs: missingJupiterALTs.length,
      sizeDifference: 1170 - 1120,
      instructionDifference: 19 - 12,
      totalKeysInMainBot: totalKeys,
      needsJupiterALT: true,
      needsInstructionLimit: true,
      needsRouteFiltering: true
    };

  } catch (error) {
    console.error(`❌ Ошибка диагностики: ${error.message}`);
    return false;
  }
}

if (require.main === module) {
  diagnoseMainBotIssues()
    .then(result => {
      if (result) {
        console.log(`\n🎯 ДИАГНОСТИКА ЗАВЕРШЕНА:`);
        console.log(`   🗜️ ALT в основном боте: ${result.mainBotALTs}`);
        console.log(`   ❌ Отсутствует Jupiter ALT: ${result.missingJupiterALTs}`);
        console.log(`   📊 Разница в размере: ${result.sizeDifference} байт`);
        console.log(`   📋 Разница в инструкциях: ${result.instructionDifference}`);
        console.log(`   🔧 Нужны исправления: ДА`);
      }
    });
}

module.exports = { diagnoseMainBotIssues };
