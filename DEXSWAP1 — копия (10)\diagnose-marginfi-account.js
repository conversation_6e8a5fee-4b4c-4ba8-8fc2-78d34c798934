const { Connection, PublicKey } = require('@solana/web3.js');

async function diagnoseMarginFiAccount() {
    const connection = new Connection('https://api.mainnet-beta.solana.com');
    
    console.log('🔍 ДИАГНОСТИКА MARGINFI АККАУНТА...\n');
    
    const marginfiAccount = new PublicKey('3dPECtRBPcYtcJG6wWDCr7VCyzxttDTjgGgo9gWC9vmU');
    const usdcBank = new PublicKey('2s37akK2eyBbp8DZgCm7RtsaEz8eJP3Nxd4urLHQv7yB');
    const marginfiProgram = new PublicKey('MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA');
    
    console.log('📋 ПРОВЕРЯЕМЫЕ АДРЕСА:');
    console.log(`   MarginFi Account: ${marginfiAccount.toString()}`);
    console.log(`   USDC Bank: ${usdcBank.toString()}`);
    console.log(`   MarginFi Program: ${marginfiProgram.toString()}\n`);
    
    // 1. Проверяем MarginFi аккаунт
    console.log('🔍 ПРОВЕРКА MARGINFI АККАУНТА:');
    const accountInfo = await connection.getAccountInfo(marginfiAccount);
    
    if (!accountInfo) {
        console.log('❌ MarginFi аккаунт НЕ СУЩЕСТВУЕТ!');
        return;
    }
    
    console.log(`✅ MarginFi аккаунт существует`);
    console.log(`   Owner: ${accountInfo.owner.toString()}`);
    console.log(`   Data length: ${accountInfo.data.length}`);
    console.log(`   Lamports: ${accountInfo.lamports}`);
    
    // Проверяем owner
    if (accountInfo.owner.toString() !== marginfiProgram.toString()) {
        console.log(`❌ НЕПРАВИЛЬНЫЙ OWNER! Ожидается: ${marginfiProgram.toString()}`);
        return;
    }
    
    // 2. Анализируем структуру MarginFi аккаунта
    console.log('\n🔍 АНАЛИЗ СТРУКТУРЫ MARGINFI АККАУНТА:');
    
    if (accountInfo.data.length < 8) {
        console.log('❌ Данные слишком короткие для MarginFi аккаунта');
        return;
    }
    
    // Первые 8 байт - discriminator
    const discriminator = accountInfo.data.slice(0, 8);
    console.log(`   Discriminator: ${Buffer.from(discriminator).toString('hex')}`);
    
    // Следующие 32 байта - group
    if (accountInfo.data.length >= 40) {
        const group = new PublicKey(accountInfo.data.slice(8, 40));
        console.log(`   Group: ${group.toString()}`);
        
        // Проверяем что group правильный
        const expectedGroup = new PublicKey('4qp6Fx6tnZkY5Wropq9wUYgtFxXKwE6viZxFHg3rdAG8');
        console.log(`   Expected Group: ${expectedGroup.toString()}`);
        console.log(`   Group matches: ${group.toString() === expectedGroup.toString()}`);
    }
    
    // Следующие 32 байта - authority
    if (accountInfo.data.length >= 72) {
        const authority = new PublicKey(accountInfo.data.slice(40, 72));
        console.log(`   Authority: ${authority.toString()}`);
    }
    
    // 3. Ищем банки в аккаунте
    console.log('\n🔍 ПОИСК БАНКОВ В MARGINFI АККАУНТЕ:');
    
    // MarginFi аккаунт содержит массив lending accounts
    // Каждый lending account ~184 байта
    const LENDING_ACCOUNT_SIZE = 184;
    const HEADER_SIZE = 72; // discriminator + group + authority + padding
    
    if (accountInfo.data.length <= HEADER_SIZE) {
        console.log('❌ Аккаунт слишком мал для содержания lending accounts');
        return;
    }
    
    const dataAfterHeader = accountInfo.data.slice(HEADER_SIZE);
    console.log(`   Данных после заголовка: ${dataAfterHeader.length} байт`);
    
    // Первые 4 байта - количество lending accounts
    if (dataAfterHeader.length >= 4) {
        const accountCount = dataAfterHeader.readUInt32LE(0);
        console.log(`   Количество lending accounts: ${accountCount}`);
        
        if (accountCount === 0) {
            console.log('❌ НЕТ LENDING ACCOUNTS! Аккаунт пустой.');
            console.log('💡 РЕШЕНИЕ: Нужно добавить USDC банк в MarginFi аккаунт');
            return;
        }
        
        // Анализируем каждый lending account
        let offset = 4; // После счетчика
        for (let i = 0; i < Math.min(accountCount, 10); i++) {
            if (offset + 32 > dataAfterHeader.length) break;
            
            console.log(`\n   📋 LENDING ACCOUNT ${i}:`);
            
            // Первые 32 байта - bank address
            const bankAddress = new PublicKey(dataAfterHeader.slice(offset, offset + 32));
            console.log(`      Bank: ${bankAddress.toString()}`);
            console.log(`      Is USDC Bank: ${bankAddress.toString() === usdcBank.toString()}`);
            
            // Следующие данные - balances и другая информация
            if (offset + 64 <= dataAfterHeader.length) {
                // Asset shares (8 bytes)
                const assetShares = dataAfterHeader.readBigUInt64LE(offset + 32);
                console.log(`      Asset Shares: ${assetShares.toString()}`);
                
                // Liability shares (8 bytes)  
                const liabilityShares = dataAfterHeader.readBigUInt64LE(offset + 40);
                console.log(`      Liability Shares: ${liabilityShares.toString()}`);
            }
            
            offset += LENDING_ACCOUNT_SIZE;
        }
    }
    
    console.log('\n🎉 ДИАГНОСТИКА MARGINFI АККАУНТА ЗАВЕРШЕНА!');
}

diagnoseMarginFiAccount().catch(console.error);
