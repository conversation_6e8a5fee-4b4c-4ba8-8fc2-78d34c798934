/**
 * 🔍 ДИАГНОСТИКА СТРУКТУРЫ WALLET И MARGINFI ИНИЦИАЛИЗАЦИИ
 * ═══════════════════════════════════════════════════════════════
 * 
 * ЦЕЛЬ: Найти корень проблемы с MarginFi инициализацией
 * 
 * ПРОБЛЕМЫ:
 * 1. ❌ КРИТИЧЕСКАЯ ОШИБКА: MarginFi НЕ НАЙДЕН НИ В ОДНОМ ИСТОЧНИКЕ!
 * 2. ❌ Ошибка отправки Jupiter транзакции: Expected the value to satisfy a union of type | type
 * 3. ❌ AtomicTransactionBuilder.initializeMarginFi() не может найти wallet.payer
 * 
 * ПЛАН ДИАГНОСТИКИ:
 * 1. Проверить структуру wallet после loadWalletForTrading
 * 2. Проверить как wallet передается в AtomicTransactionBuilder
 * 3. Проверить инициализацию MarginFi в разных компонентах
 * 4. Проверить тип данных serializedTransaction
 */

const { Connection, PublicKey, Keypair, VersionedTransaction } = require('@solana/web3.js');
const { MarginfiClient, getConfig } = require('@mrgnlabs/marginfi-client-v2');
const { NodeWallet } = require('@mrgnlabs/mrgn-common');
const bs58 = require('bs58');
require('dotenv').config({ path: '.env.solana' });

class WalletMarginFiDiagnostic {
  constructor() {
    this.connection = null;
    this.wallet = null;
    this.marginfiClient = null;
    this.marginfiAccount = null;
  }

  async runFullDiagnostic() {
    console.log('🔍 ПОЛНАЯ ДИАГНОСТИКА WALLET И MARGINFI СТРУКТУРЫ');
    console.log('═'.repeat(80));

    try {
      // 1. Диагностика подключения
      await this.diagnoseConnection();
      
      // 2. Диагностика wallet структуры
      await this.diagnoseWalletStructure();
      
      // 3. Диагностика MarginFi инициализации
      await this.diagnoseMarginFiInitialization();
      
      // 4. Диагностика типов данных транзакций
      await this.diagnoseTransactionTypes();
      
      // 5. Диагностика RPC совместимости
      await this.diagnoseRPCCompatibility();

      console.log('\n✅ ДИАГНОСТИКА ЗАВЕРШЕНА УСПЕШНО');
      
    } catch (error) {
      console.error(`❌ ОШИБКА ДИАГНОСТИКИ: ${error.message}`);
      console.error(`Stack: ${error.stack}`);
    }
  }

  async diagnoseConnection() {
    console.log('\n1️⃣ ДИАГНОСТИКА ПОДКЛЮЧЕНИЯ');
    console.log('─'.repeat(50));

    // Проверяем RPC подключения
    const rpcUrl = process.env.QUICKNODE_RPC_URL || process.env.SOLANA_RPC_URL;
    console.log(`🔗 RPC URL: ${rpcUrl ? rpcUrl.slice(0, 50) + '...' : 'НЕ НАЙДЕН'}`);

    if (!rpcUrl) {
      throw new Error('RPC URL не найден в переменных окружения');
    }

    this.connection = new Connection(rpcUrl, 'confirmed');
    console.log(`✅ Connection создан: ${!!this.connection}`);

    // Тестируем подключение
    try {
      const version = await this.connection.getVersion();
      console.log(`✅ RPC работает, версия: ${version['solana-core']}`);
    } catch (rpcError) {
      console.error(`❌ RPC не отвечает: ${rpcError.message}`);
      throw rpcError;
    }
  }

  async diagnoseWalletStructure() {
    console.log('\n2️⃣ ДИАГНОСТИКА WALLET СТРУКТУРЫ');
    console.log('─'.repeat(50));

    // Загружаем приватный ключ
    const privateKey = process.env.WALLET_PRIVATE_KEY;
    if (!privateKey) {
      throw new Error('WALLET_PRIVATE_KEY не найден в .env.solana');
    }

    console.log(`🔑 Приватный ключ найден, длина: ${privateKey.length}`);

    // Декодируем Base58
    const privateKeyBytes = bs58.decode(privateKey);
    console.log(`🔑 Декодированный ключ, длина: ${privateKeyBytes.length} байт`);

    if (privateKeyBytes.length !== 64) {
      throw new Error(`Неправильная длина ключа: ${privateKeyBytes.length}, ожидается 64`);
    }

    // Создаем Keypair
    const keypair = Keypair.fromSecretKey(privateKeyBytes);
    console.log(`✅ Keypair создан: ${keypair.publicKey.toString()}`);

    // Создаем wallet структуру КАК В РЕАЛЬНОМ КОДЕ
    this.wallet = {
      publicKey: keypair.publicKey,
      secretKey: keypair.secretKey,
      payer: keypair, // ЭТО КЛЮЧЕВОЕ ПОЛЕ!
      signTransaction: async (tx) => {
        tx.sign(keypair);
        return tx;
      },
      signAllTransactions: async (txs) => {
        return txs.map(tx => {
          tx.sign(keypair);
          return tx;
        });
      }
    };

    console.log('\n📊 АНАЛИЗ WALLET СТРУКТУРЫ:');
    console.log(`   ✅ wallet.publicKey: ${!!this.wallet.publicKey} (${this.wallet.publicKey?.constructor.name})`);
    console.log(`   ✅ wallet.secretKey: ${!!this.wallet.secretKey} (длина: ${this.wallet.secretKey?.length})`);
    console.log(`   ✅ wallet.payer: ${!!this.wallet.payer} (${this.wallet.payer?.constructor.name})`);
    console.log(`   ✅ wallet.signTransaction: ${typeof this.wallet.signTransaction}`);
    console.log(`   ✅ wallet.signAllTransactions: ${typeof this.wallet.signAllTransactions}`);

    if (this.wallet.payer) {
      console.log(`   ✅ wallet.payer.publicKey: ${this.wallet.payer.publicKey.toString()}`);
      console.log(`   ✅ wallet.payer.secretKey: ${!!this.wallet.payer.secretKey} (длина: ${this.wallet.payer.secretKey?.length})`);
    }

    // Проверяем баланс
    const balance = await this.connection.getBalance(this.wallet.publicKey);
    console.log(`💰 Баланс кошелька: ${balance / 1e9} SOL`);

    if (balance < 0.01 * 1e9) {
      console.log(`⚠️ ВНИМАНИЕ: Низкий баланс SOL (${balance / 1e9}) для создания MarginFi аккаунта!`);
      console.log(`💡 Рекомендуется минимум 0.05 SOL для создания аккаунта`);
    }
  }

  async diagnoseMarginFiInitialization() {
    console.log('\n3️⃣ ДИАГНОСТИКА MARGINFI ИНИЦИАЛИЗАЦИИ');
    console.log('─'.repeat(50));

    if (!this.wallet || !this.wallet.payer) {
      throw new Error('Wallet не инициализирован или payer отсутствует');
    }

    try {
      // Создаем NodeWallet КАК В AtomicTransactionBuilder
      console.log('🔧 Создаем NodeWallet...');
      const nodeWallet = new NodeWallet(this.wallet.payer);
      console.log(`✅ NodeWallet создан: ${nodeWallet.publicKey.toString()}`);

      // Получаем конфигурацию
      console.log('🔧 Получаем MarginFi конфигурацию...');
      const config = getConfig('production');
      console.log(`✅ Config получен: ${config.groupPk.toString()}`);

      // Создаем MarginFi клиент
      console.log('🔧 Создаем MarginFi клиент...');
      this.marginfiClient = await MarginfiClient.fetch(config, nodeWallet, this.connection);
      console.log(`✅ MarginFi клиент создан!`);

      // Проверяем существующие аккаунты
      console.log('🔍 Ищем существующие MarginFi аккаунты...');
      const accounts = await this.marginfiClient.getMarginfiAccountsForAuthority();
      console.log(`📊 Найдено аккаунтов: ${accounts.length}`);

      if (accounts.length > 0) {
        for (let i = 0; i < accounts.length; i++) {
          const account = accounts[i];
          console.log(`   #${i + 1}: ${account.address.toString()}`);
          
          // Проверяем состояние аккаунта
          try {
            const accountData = account.computeHealthComponents();
            console.log(`      Здоровье: ${JSON.stringify(accountData)}`);
          } catch (healthError) {
            console.log(`      Ошибка проверки здоровья: ${healthError.message}`);
          }
        }
      }

      // Тестируем создание нового аккаунта
      console.log('\n🔧 ТЕСТИРУЕМ СОЗДАНИЕ НОВОГО АККАУНТА...');
      const flashLoanKeypair = Keypair.generate();
      console.log(`🔑 Сгенерирован keypair: ${flashLoanKeypair.publicKey.toString()}`);

      // НЕ СОЗДАЕМ РЕАЛЬНО - ТОЛЬКО ТЕСТИРУЕМ ПАРАМЕТРЫ
      console.log('💡 Параметры для создания аккаунта:');
      console.log(`   newAccountKey: ${flashLoanKeypair.publicKey.toString()}`);
      console.log(`   authority: ${nodeWallet.publicKey.toString()}`);
      console.log(`   group: ${config.groupPk.toString()}`);

    } catch (marginfiError) {
      console.error(`❌ ОШИБКА MARGINFI ИНИЦИАЛИЗАЦИИ: ${marginfiError.message}`);
      console.error(`Stack: ${marginfiError.stack}`);
      throw marginfiError;
    }
  }

  async diagnoseTransactionTypes() {
    console.log('\n4️⃣ ДИАГНОСТИКА ТИПОВ ТРАНЗАКЦИЙ');
    console.log('─'.repeat(50));

    // Создаем тестовую VersionedTransaction
    try {
      const { TransactionMessage, ComputeBudgetProgram } = require('@solana/web3.js');
      
      // Создаем простую инструкцию
      const instruction = ComputeBudgetProgram.setComputeUnitLimit({ units: 200000 });
      
      // Создаем TransactionMessage
      const message = new TransactionMessage({
        payerKey: this.wallet.publicKey,
        recentBlockhash: (await this.connection.getLatestBlockhash()).blockhash,
        instructions: [instruction]
      }).compileToV0Message();

      // Создаем VersionedTransaction
      const transaction = new VersionedTransaction(message);
      console.log(`✅ VersionedTransaction создана`);
      console.log(`   Тип: ${transaction.constructor.name}`);
      console.log(`   instanceof VersionedTransaction: ${transaction instanceof VersionedTransaction}`);

      // Подписываем
      transaction.sign([this.wallet.payer]);
      console.log(`✅ Транзакция подписана`);

      // Сериализуем
      const serialized = transaction.serialize();
      console.log(`✅ Транзакция сериализована`);
      console.log(`   Тип serialized: ${serialized.constructor.name}`);
      console.log(`   instanceof Uint8Array: ${serialized instanceof Uint8Array}`);
      console.log(`   instanceof Buffer: ${serialized instanceof Buffer}`);
      console.log(`   Размер: ${serialized.length} байт`);

      // Проверяем что можно конвертировать в Buffer
      const asBuffer = Buffer.from(serialized);
      console.log(`✅ Конвертация в Buffer: ${asBuffer.length} байт`);

    } catch (txError) {
      console.error(`❌ ОШИБКА СОЗДАНИЯ ТРАНЗАКЦИИ: ${txError.message}`);
      throw txError;
    }
  }

  async diagnoseRPCCompatibility() {
    console.log('\n5️⃣ ДИАГНОСТИКА RPC СОВМЕСТИМОСТИ');
    console.log('─'.repeat(50));

    // Проверяем методы RPC
    console.log('🔍 Проверяем доступные методы RPC...');
    console.log(`   sendTransaction: ${typeof this.connection.sendTransaction}`);
    console.log(`   sendRawTransaction: ${typeof this.connection.sendRawTransaction}`);

    // Проверяем что RPC принимает
    console.log('\n🔍 Тестируем типы данных для sendRawTransaction...');
    
    try {
      // Создаем минимальную транзакцию для теста
      const { Transaction, SystemProgram } = require('@solana/web3.js');
      
      const testTx = new Transaction().add(
        SystemProgram.transfer({
          fromPubkey: this.wallet.publicKey,
          toPubkey: this.wallet.publicKey,
          lamports: 1
        })
      );
      
      testTx.recentBlockhash = (await this.connection.getLatestBlockhash()).blockhash;
      testTx.feePayer = this.wallet.publicKey;
      testTx.sign(this.wallet.payer);
      
      const serializedTest = testTx.serialize();
      console.log(`📊 Тестовая транзакция:`);
      console.log(`   Тип: ${serializedTest.constructor.name}`);
      console.log(`   Размер: ${serializedTest.length} байт`);
      
      // НЕ ОТПРАВЛЯЕМ РЕАЛЬНО - ТОЛЬКО ПРОВЕРЯЕМ ТИПЫ
      console.log('💡 Готово для отправки через sendRawTransaction');
      
    } catch (testError) {
      console.error(`❌ ОШИБКА СОЗДАНИЯ ТЕСТОВОЙ ТРАНЗАКЦИИ: ${testError.message}`);
    }
  }
}

// Запуск диагностики
async function main() {
  const diagnostic = new WalletMarginFiDiagnostic();
  await diagnostic.runFullDiagnostic();
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { WalletMarginFiDiagnostic };
