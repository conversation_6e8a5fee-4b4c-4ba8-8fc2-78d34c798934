#!/usr/bin/env node

/**
 * 🎯 DLMM BINS CALCULATOR ДЛЯ ТВОЕЙ СТРАТЕГИИ
 * 
 * 🔥 ЦЕЛЬ: Рассчитать точные bins для односторонней USDC ликвидности
 * ✅ Покрыть торговлю $400K SOL
 * ✅ Максимизировать прибыль от Dynamic Fee
 * ✅ Учесть рост цены на 16%
 */

class DLMMBinsCalculator {
    constructor() {
        // 📊 ИСХОДНЫЕ ДАННЫЕ
        this.POOL_DATA = {
            current_price: 171.97,      // Текущая цена SOL
            bin_step: 4,                // 0.4% между bins (40 basis points)
            usdc_amount: 3396139,       // USDC в пуле
            sol_amount: 21920.46        // SOL в пуле
        };

        // 💰 СТРАТЕГИЯ
        this.STRATEGY = {
            liquidity_usdc: 1400000,    // $1.4M USDC ликвидность
            trade_usdc: 400000,         // $400K торговля
            target_price_increase: 16   // 16% рост цены
        };

        // 🧮 РАСЧЕТНЫЕ ДАННЫЕ
        this.target_price = this.POOL_DATA.current_price * (1 + this.STRATEGY.target_price_increase / 100);
        this.sol_to_buy = this.STRATEGY.trade_usdc / this.POOL_DATA.current_price;

        console.log('🎯 DLMM BINS CALCULATOR ИНИЦИАЛИЗИРОВАН');
        console.log(`📊 Текущая цена SOL: $${this.POOL_DATA.current_price}`);
        console.log(`🎯 Целевая цена SOL: $${this.target_price.toFixed(2)}`);
        console.log(`🪙 SOL для покупки: ${this.sol_to_buy.toFixed(2)}`);
        console.log(`💰 USDC ликвидность: $${this.STRATEGY.liquidity_usdc.toLocaleString()}`);
    }

    /**
     * 🧮 РАСЧЕТ ЦЕНЫ BIN ПО ИНДЕКСУ
     */
    calculateBinPrice(binIndex) {
        // price = (1 + bin_step/10000)^bin_index
        const binStepDecimal = this.POOL_DATA.bin_step / 10000;
        return this.POOL_DATA.current_price * Math.pow(1 + binStepDecimal, binIndex);
    }

    /**
     * 🎯 ПОИСК BIN ИНДЕКСА ПО ЦЕНЕ
     */
    findBinIndex(targetPrice) {
        // bin_index = log(target_price / current_price) / log(1 + bin_step/10000)
        const binStepDecimal = this.POOL_DATA.bin_step / 10000;
        const priceRatio = targetPrice / this.POOL_DATA.current_price;
        return Math.round(Math.log(priceRatio) / Math.log(1 + binStepDecimal));
    }

    /**
     * 📊 СОЗДАНИЕ СТРУКТУРЫ BINS
     */
    createBinStructure() {
        const bins = [];
        const currentBinIndex = 0; // Текущий активный bin
        
        // Создаем bins от -50 до +50
        for (let i = -50; i <= 50; i++) {
            const price = this.calculateBinPrice(i);
            bins.push({
                index: i,
                price: price,
                isActive: i === currentBinIndex,
                isBelowCurrent: i < currentBinIndex,
                isAboveCurrent: i > currentBinIndex
            });
        }
        
        return bins;
    }

    /**
     * 🎯 ВЫБОР BINS ДЛЯ ЛИКВИДНОСТИ
     */
    selectLiquidityBins() {
        const bins = this.createBinStructure();
        
        // Нам нужны bins НИЖЕ текущей цены
        // Когда цена растет, эти bins становятся активными для торговли
        
        // Целевой диапазон: от текущей цены до цены -10%
        const lowerBound = this.POOL_DATA.current_price * 0.90; // -10%
        const upperBound = this.POOL_DATA.current_price * 0.99; // -1%
        
        const selectedBins = bins.filter(bin => 
            bin.price >= lowerBound && 
            bin.price <= upperBound &&
            bin.isBelowCurrent
        );
        
        return selectedBins;
    }

    /**
     * 💰 РАСПРЕДЕЛЕНИЕ ЛИКВИДНОСТИ ПО BINS
     */
    distributeLiquidity(selectedBins) {
        const totalBins = selectedBins.length;
        const usdcPerBin = this.STRATEGY.liquidity_usdc / totalBins;
        
        const distribution = selectedBins.map(bin => ({
            binIndex: bin.index,
            binPrice: bin.price,
            usdcAmount: usdcPerBin,
            solAmount: 0, // Односторонняя ликвидность - только USDC
            liquidityValue: usdcPerBin
        }));
        
        return {
            totalBins,
            usdcPerBin,
            distribution,
            priceRange: {
                min: Math.min(...selectedBins.map(b => b.price)),
                max: Math.max(...selectedBins.map(b => b.price))
            }
        };
    }

    /**
     * ⚡ СИМУЛЯЦИЯ ТОРГОВЛИ
     */
    simulateTrading(liquidityDistribution) {
        // Когда цена растет до target_price, наши bins становятся активными
        
        // Рассчитываем, сколько SOL можно купить из нашей ликвидности
        let totalSolFromLiquidity = 0;
        let totalUsdcUsed = 0;
        
        for (const bin of liquidityDistribution.distribution) {
            // Если цена выросла выше этого bin, он становится активным
            if (this.target_price > bin.binPrice) {
                const solFromThisBin = bin.usdcAmount / bin.binPrice;
                totalSolFromLiquidity += solFromThisBin;
                totalUsdcUsed += bin.usdcAmount;
            }
        }
        
        // Проверяем, покрывает ли наша ликвидность нужную торговлю
        const coverageRatio = totalSolFromLiquidity / this.sol_to_buy;
        const isSufficient = coverageRatio >= 1.0;
        
        return {
            solFromLiquidity: totalSolFromLiquidity,
            usdcUsedFromLiquidity: totalUsdcUsed,
            solNeeded: this.sol_to_buy,
            coverageRatio,
            isSufficient,
            excessSol: totalSolFromLiquidity - this.sol_to_buy
        };
    }

    /**
     * 💸 РАСЧЕТ DYNAMIC FEE ДОХОДОВ
     */
    calculateDynamicFeeIncome(tradingSimulation) {
        // Торговля $400K с Dynamic Fee 10%
        const dynamicFee = 0.10; // 10%
        const totalFeeGenerated = this.STRATEGY.trade_usdc * dynamicFee;
        
        // Наша доля в пуле после добавления ликвидности
        const newPoolTvl = (this.POOL_DATA.usdc_amount + this.STRATEGY.liquidity_usdc);
        const ourShare = this.STRATEGY.liquidity_usdc / newPoolTvl;
        
        // Наш доход от Dynamic Fee
        const ourFeeIncome = totalFeeGenerated * ourShare;
        
        return {
            totalFeeGenerated,
            ourShare: ourShare * 100,
            ourFeeIncome,
            feeRate: dynamicFee * 100
        };
    }

    /**
     * 🚀 ПОЛНЫЙ АНАЛИЗ
     */
    runFullAnalysis() {
        console.log('\n🚀 ПОЛНЫЙ АНАЛИЗ BINS ДЛЯ ТВОЕЙ СТРАТЕГИИ');
        console.log('=' .repeat(80));

        // 1. Выбираем bins
        const selectedBins = this.selectLiquidityBins();
        console.log(`\n🎯 ВЫБРАННЫЕ BINS:`);
        console.log(`   Количество bins: ${selectedBins.length}`);
        console.log(`   Диапазон цен: $${selectedBins[0].price.toFixed(2)} - $${selectedBins[selectedBins.length-1].price.toFixed(2)}`);
        console.log(`   Индексы bins: ${selectedBins[0].index} до ${selectedBins[selectedBins.length-1].index}`);

        // 2. Распределяем ликвидность
        const liquidityDist = this.distributeLiquidity(selectedBins);
        console.log(`\n💰 РАСПРЕДЕЛЕНИЕ ЛИКВИДНОСТИ:`);
        console.log(`   USDC на bin: $${liquidityDist.usdcPerBin.toLocaleString()}`);
        console.log(`   Общий диапазон: $${liquidityDist.priceRange.min.toFixed(2)} - $${liquidityDist.priceRange.max.toFixed(2)}`);

        // 3. Симулируем торговлю
        const tradingSim = this.simulateTrading(liquidityDist);
        console.log(`\n⚡ СИМУЛЯЦИЯ ТОРГОВЛИ:`);
        console.log(`   SOL нужно купить: ${tradingSim.solNeeded.toFixed(2)}`);
        console.log(`   SOL доступно из ликвидности: ${tradingSim.solFromLiquidity.toFixed(2)}`);
        console.log(`   Покрытие: ${(tradingSim.coverageRatio * 100).toFixed(1)}%`);
        console.log(`   ${tradingSim.isSufficient ? '✅ ДОСТАТОЧНО' : '❌ НЕДОСТАТОЧНО'}`);
        
        if (tradingSim.excessSol > 0) {
            console.log(`   Избыток SOL: ${tradingSim.excessSol.toFixed(2)} ($${(tradingSim.excessSol * this.target_price).toFixed(0)})`);
        }

        // 4. Рассчитываем доходы от Dynamic Fee
        const feeIncome = this.calculateDynamicFeeIncome(tradingSim);
        console.log(`\n💸 ДОХОДЫ ОТ DYNAMIC FEE:`);
        console.log(`   Общая комиссия: $${feeIncome.totalFeeGenerated.toLocaleString()}`);
        console.log(`   Наша доля в пуле: ${feeIncome.ourShare.toFixed(2)}%`);
        console.log(`   Наш доход: $${feeIncome.ourFeeIncome.toLocaleString()}`);

        // 5. Детальная таблица bins
        console.log(`\n📊 ДЕТАЛЬНАЯ ТАБЛИЦА BINS:`);
        console.log('Индекс | Цена     | USDC     | Активен при росте');
        console.log('-'.repeat(50));
        
        liquidityDist.distribution.slice(0, 10).forEach(bin => {
            const isActive = this.target_price > bin.binPrice ? '✅' : '❌';
            console.log(`${bin.binIndex.toString().padStart(6)} | $${bin.binPrice.toFixed(2).padStart(7)} | $${bin.usdcAmount.toLocaleString().padStart(7)} | ${isActive}`);
        });
        
        if (liquidityDist.distribution.length > 10) {
            console.log(`... и еще ${liquidityDist.distribution.length - 10} bins`);
        }

        // 6. Итоговые рекомендации
        console.log(`\n🎯 РЕКОМЕНДАЦИИ:`);
        
        if (tradingSim.isSufficient) {
            console.log(`   ✅ Выбранные bins ПОКРЫВАЮТ торговлю`);
            console.log(`   💰 Ожидаемый доход от комиссий: $${feeIncome.ourFeeIncome.toLocaleString()}`);
            console.log(`   🎯 Стратегия ГОТОВА к реализации`);
        } else {
            console.log(`   ❌ Нужно БОЛЬШЕ bins или БОЛЬШЕ ликвидности`);
            console.log(`   🔧 Увеличить диапазон bins или сумму ликвидности`);
        }

        return {
            selectedBins,
            liquidityDistribution: liquidityDist,
            tradingSimulation: tradingSim,
            feeIncome,
            viable: tradingSim.isSufficient
        };
    }
}

// 🚀 ЗАПУСК РАСЧЕТА
if (require.main === module) {
    const calculator = new DLMMBinsCalculator();
    const results = calculator.runFullAnalysis();
    
    console.log('\n🎉 РАСЧЕТ ЗАВЕРШЕН!');
    console.log(`🎯 Стратегия ${results.viable ? 'ГОТОВА' : 'ТРЕБУЕТ ДОРАБОТКИ'}`);
    
    if (results.viable) {
        console.log(`💰 Ожидаемый доход: $${results.feeIncome.ourFeeIncome.toLocaleString()}`);
        console.log(`📊 Bins для использования: ${results.selectedBins[0].index} до ${results.selectedBins[results.selectedBins.length-1].index}`);
    }
}

module.exports = DLMMBinsCalculator;
