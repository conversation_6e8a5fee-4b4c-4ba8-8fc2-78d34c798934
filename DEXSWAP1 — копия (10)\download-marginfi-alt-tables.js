#!/usr/bin/env node

/**
 * 🔥 СКАЧИВАНИЕ ОФИЦИАЛЬНЫХ MARGINFI ALT ТАБЛИЦ
 * Загружает 3 официальные MarginFi ALT таблицы из сети и сохраняет в файл
 */

const { Connection, PublicKey } = require('@solana/web3.js');
const fs = require('fs');

async function downloadMarginFiALTTables() {
    console.log('🔥 СКАЧИВАНИЕ ОФИЦИАЛЬНЫХ MARGINFI ALT ТАБЛИЦ');
    console.log('=' .repeat(60));

    try {
        // 1. Подключение к Solana
        console.log('🔗 Подключение к Solana RPC...');
        const connection = new Connection('https://api.mainnet-beta.solana.com');
        
        const { blockhash } = await connection.getLatestBlockhash();
        console.log(`✅ Подключение успешно! Блок: ${blockhash.slice(0, 8)}...`);

        // 2. Официальные MarginFi ALT адреса (исправленные)
        console.log('\n🏦 ОФИЦИАЛЬНЫЕ MARGINFI ALT АДРЕСА:');
        const marginfiALTAddresses = [
            'BEF6ZPBwNmZzqB2pnjhBxVzoF4YLnLHL3Lc87sNJekxo', // Jupiter ALT #1 для MarginFi (246 аккаунтов)
            'F224j8tdxFWSXh39nYpi9TH3RJUrAbTWNDvpqJ62kJqB', // Jupiter ALT #2 для MarginFi (157 аккаунтов)
            'HGmknUTUmeovMc9ryERNWG6UFZDFDVr9xrum3ZhyL4fC'  // Meteora Main ALT (256 аккаунтов) - используем как 3-ю
        ];

        marginfiALTAddresses.forEach((address, index) => {
            console.log(`   ${index + 1}. ${address}`);
        });

        // 3. Загружаем каждую ALT таблицу
        console.log('\n🔄 ЗАГРУЗКА ALT ТАБЛИЦ ИЗ СЕТИ...');
        const validationResults = [];
        let totalAccounts = 0;

        for (let i = 0; i < marginfiALTAddresses.length; i++) {
            const address = marginfiALTAddresses[i];
            console.log(`\n🔄 Загружаем ALT ${i + 1}/${marginfiALTAddresses.length}: ${address}`);
            
            try {
                // Создаем PublicKey
                const altPublicKey = new PublicKey(address);
                console.log(`   🔑 PublicKey создан: ${altPublicKey.toString()}`);
                
                // Загружаем ALT таблицу
                console.log(`   📡 Вызов connection.getAddressLookupTable()...`);
                const lookupTableResponse = await connection.getAddressLookupTable(altPublicKey);
                
                if (!lookupTableResponse.value) {
                    console.log(`   ❌ ALT НЕ НАЙДЕНА: ${address}`);
                    validationResults.push({
                        address: address,
                        accountCount: 0,
                        valid: false,
                        error: 'ALT не найдена в сети',
                        description: `Jupiter ALT #${i + 1} для MarginFi Flash Loan`,
                        accounts: []
                    });
                    continue;
                }

                const lookupTableAccount = lookupTableResponse.value;
                const addressCount = lookupTableAccount.state.addresses.length;
                
                console.log(`   ✅ ALT ЗАГРУЖЕНА: ${address}`);
                console.log(`   📊 Аккаунтов в таблице: ${addressCount}`);
                
                // Конвертируем PublicKey в строки
                const accountStrings = lookupTableAccount.state.addresses.map(pubkey => pubkey.toString());
                
                // Показываем первые 5 аккаунтов для проверки
                console.log(`   📋 Первые 5 аккаунтов:`);
                accountStrings.slice(0, 5).forEach((account, idx) => {
                    console.log(`      ${idx + 1}. ${account}`);
                });
                if (addressCount > 5) {
                    console.log(`      ... и еще ${addressCount - 5} аккаунтов`);
                }

                // Сохраняем результат
                validationResults.push({
                    address: address,
                    accountCount: addressCount,
                    valid: true,
                    description: `Jupiter ALT #${i + 1} для MarginFi Flash Loan`,
                    accounts: accountStrings
                });

                totalAccounts += addressCount;
                
                // Небольшая пауза между запросами
                await new Promise(resolve => setTimeout(resolve, 100));
                
            } catch (error) {
                console.log(`   ❌ ОШИБКА ЗАГРУЗКИ: ${error.message}`);
                validationResults.push({
                    address: address,
                    accountCount: 0,
                    valid: false,
                    error: error.message,
                    description: `Jupiter ALT #${i + 1} для MarginFi Flash Loan`,
                    accounts: []
                });
            }
        }

        // 4. Создаем финальный кэш файл
        console.log('\n💾 СОЗДАНИЕ MARGINFI КЭША...');
        const marginfiCache = {
            timestamp: new Date().toISOString(),
            source: 'MarginFi Flash Loan ALT Tables (Downloaded from Network)',
            totalTables: validationResults.filter(r => r.valid).length,
            totalAccounts: totalAccounts,
            validationResults: validationResults
        };

        // 5. Сохраняем в файл
        const cacheFile = './marginfi-alt-cache.json';
        fs.writeFileSync(cacheFile, JSON.stringify(marginfiCache, null, 2));
        
        console.log(`✅ MarginFi кэш сохранен: ${cacheFile}`);
        console.log(`📊 Валидных таблиц: ${marginfiCache.totalTables}/${marginfiALTAddresses.length}`);
        console.log(`📊 Всего аккаунтов: ${marginfiCache.totalAccounts}`);

        // 6. Статистика по таблицам
        console.log('\n📊 ДЕТАЛЬНАЯ СТАТИСТИКА:');
        validationResults.forEach((result, index) => {
            const status = result.valid ? '✅' : '❌';
            console.log(`   ${status} ALT ${index + 1}: ${result.address}`);
            console.log(`      Аккаунтов: ${result.accountCount}`);
            console.log(`      Статус: ${result.valid ? 'ВАЛИДНАЯ' : 'ОШИБКА'}`);
            if (!result.valid && result.error) {
                console.log(`      Ошибка: ${result.error}`);
            }
        });

        // 7. Проверка качества данных
        console.log('\n🔍 ПРОВЕРКА КАЧЕСТВА ДАННЫХ:');
        const validTables = validationResults.filter(r => r.valid).length;
        const validAccounts = validationResults.reduce((sum, r) => sum + (r.valid ? r.accountCount : 0), 0);
        
        console.log(`   Валидных таблиц: ${validTables}/${marginfiALTAddresses.length}`);
        console.log(`   Валидных аккаунтов: ${validAccounts}`);
        
        if (validTables === marginfiALTAddresses.length) {
            console.log(`   ✅ ВСЕ ТАБЛИЦЫ ЗАГРУЖЕНЫ УСПЕШНО!`);
        } else {
            console.log(`   ⚠️ Некоторые таблицы не загружены`);
        }

        if (validAccounts > 500) {
            console.log(`   ✅ ДОСТАТОЧНО АККАУНТОВ ДЛЯ ALT СЖАТИЯ!`);
        } else {
            console.log(`   ⚠️ Мало аккаунтов для эффективного сжатия`);
        }

        // 8. Тест загрузки из файла
        console.log('\n🧪 ТЕСТ ЗАГРУЗКИ ИЗ ФАЙЛА...');
        try {
            const testLoad = JSON.parse(fs.readFileSync(cacheFile, 'utf8'));
            console.log(`   ✅ Файл читается корректно`);
            console.log(`   📊 Таблиц в файле: ${testLoad.totalTables}`);
            console.log(`   📊 Аккаунтов в файле: ${testLoad.totalAccounts}`);
            
            // Проверяем структуру
            if (testLoad.validationResults && Array.isArray(testLoad.validationResults)) {
                console.log(`   ✅ Структура validationResults корректна`);
                
                let testAccounts = 0;
                for (const result of testLoad.validationResults) {
                    if (result.valid && result.accounts && Array.isArray(result.accounts)) {
                        testAccounts += result.accounts.length;
                    }
                }
                console.log(`   ✅ Все аккаунты доступны: ${testAccounts}`);
            } else {
                console.log(`   ❌ Неправильная структура файла`);
            }
            
        } catch (error) {
            console.log(`   ❌ ОШИБКА ЧТЕНИЯ ФАЙЛА: ${error.message}`);
        }

        // 9. Итоговый результат
        console.log('\n🎯 ИТОГОВЫЙ РЕЗУЛЬТАТ:');
        
        if (validTables === marginfiALTAddresses.length && validAccounts > 500) {
            console.log('🔥 УСПЕХ! ВСЕ MARGINFI ALT ТАБЛИЦЫ СКАЧАНЫ!');
            console.log(`✅ ${validTables} таблиц загружены`);
            console.log(`✅ ${validAccounts} аккаунтов доступны`);
            console.log(`✅ Файл сохранен: ${cacheFile}`);
            console.log('🚀 ГОТОВО К ИСПОЛЬЗОВАНИЮ В ОБЪЕДИНЕННОЙ СИСТЕМЕ!');
        } else {
            console.log('⚠️ ЧАСТИЧНЫЙ УСПЕХ - некоторые таблицы не загружены');
            console.log(`📊 Загружено: ${validTables}/${marginfiALTAddresses.length} таблиц`);
            console.log(`📊 Аккаунтов: ${validAccounts}`);
        }

    } catch (error) {
        console.error('❌ КРИТИЧЕСКАЯ ОШИБКА:', error.message);
        console.error(error.stack);
    }
}

// Запуск скачивания
if (require.main === module) {
    downloadMarginFiALTTables().catch(console.error);
}

module.exports = { downloadMarginFiALTTables };
