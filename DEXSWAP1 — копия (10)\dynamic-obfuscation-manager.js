/**
 * 🎭 ДИНАМИЧЕСКИЙ МЕНЕДЖЕР ОБФУСКАЦИИ
 * ═══════════════════════════════════════════════════════════════
 * 🎯 ЦЕЛЬ: Адаптивная обфускация с контролем веса транзакции
 * 📊 ФУНКЦИИ: Подстройка под лимит 1232 байт с запасом 20 байт
 * 🛡️ БЕЗОПАСНОСТЬ: Официальные программы Solana для обфускации
 */

const { 
  SystemProgram, 
  ComputeBudgetProgram, 
  PublicKey, 
  TransactionInstruction 
} = require('@solana/web3.js');

class DynamicObfuscationManager {
  constructor(connection, wallet, obfuscationConfig = null) {
    this.connection = connection;
    this.wallet = wallet;

    // 🎭 НАСТРОЙКИ ОБФУСКАЦИИ ИЗ TRADING CONFIG
    this.config = obfuscationConfig || {
      TARGET_SIZE_BYTES: 1182,
      SIZE_LIMIT_BYTES: 1232,
      SIZE_RESERVE_BYTES: 50
    };

    // 📊 КОНСТАНТЫ РАЗМЕРОВ (ИЗ TRADING CONFIG)
    this.SOLANA_TX_LIMIT = this.config.SIZE_LIMIT_BYTES;      // 1232 байт
    this.SIZE_LIMIT = this.config.SIZE_LIMIT_BYTES;           // 1232 байт (алиас для совместимости)
    this.SAFETY_MARGIN = this.config.SIZE_RESERVE_BYTES;      // 50 байт
    this.TARGET_SIZE = this.config.TARGET_SIZE_BYTES;         // 1182 байт
    
    // 🎭 УРОВНИ ОБФУСКАЦИИ
    this.obfuscationLevels = {
      MINIMAL: 1,    // Минимальная обфускация
      LIGHT: 2,      // Легкая обфускация  
      MEDIUM: 3,     // Средняя обфускация
      HEAVY: 4,      // Тяжелая обфускация
      MAXIMUM: 5     // Максимальная обфускация
    };
    
    console.log('🎭 Динамический менеджер обфускации инициализирован');
    console.log(`📊 Целевой размер: ${this.TARGET_SIZE} байт (лимит ${this.SOLANA_TX_LIMIT} - запас ${this.SAFETY_MARGIN})`);
    console.log('🎯 НАСТРОЙКИ ИЗ TRADING CONFIG ПРИМЕНЕНЫ');
  }

  /**
   * 📊 ОФИЦИАЛЬНАЯ ОЦЕНКА РАЗМЕРА ТРАНЗАКЦИИ ПО ДОКУМЕНТАЦИИ SOLANA
   */
  estimateRealTransactionSize(instructions, addressLookupTables = []) {
    try {
      const { VersionedTransaction, TransactionMessage, PublicKey } = require('@solana/web3.js');

      // Создаем правильный recentBlockhash в формате base58 (ОФИЦИАЛЬНЫЙ ФОРМАТ)
      // По документации: recentBlockhash должен быть 32-байтным хешем в base58
      const fakeBlockhashBytes = Buffer.alloc(32, 1); // 32 байта единиц

      // Используем встроенный метод из @solana/web3.js для кодирования base58
      let fakeBlockhash;
      try {
        // Пробуем использовать bs58 из @solana/web3.js
        const { PublicKey } = require('@solana/web3.js');
        fakeBlockhash = new PublicKey(fakeBlockhashBytes).toString();
      } catch (error) {
        // Fallback: создаем валидный base58 хеш вручную
        fakeBlockhash = '********************************111111111111'; // 44 символа base58
      }

      console.log(`🔧 Создан тестовый blockhash: ${fakeBlockhash.slice(0, 8)}... (${fakeBlockhash.length} символов)`);

      // 🔧 БЕЗОПАСНАЯ ПРОВЕРКА WALLET
      if (!this.wallet || !this.wallet.publicKey) {
        throw new Error('Wallet или wallet.publicKey не установлен');
      }

      // 🔧 ПРОВЕРКА ЧТО publicKey ЯВЛЯЕТСЯ ПРАВИЛЬНЫМ ОБЪЕКТОМ
      let payerKey;
      if (this.wallet.publicKey instanceof PublicKey) {
        payerKey = this.wallet.publicKey;
      } else if (typeof this.wallet.publicKey === 'string') {
        payerKey = new PublicKey(this.wallet.publicKey);
      } else {
        throw new Error(`Неправильный тип wallet.publicKey: ${typeof this.wallet.publicKey}`);
      }

      // 🔥 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: ПРОВЕРЯЕМ И ИСПРАВЛЯЕМ ВСЕ КЛЮЧИ В ИНСТРУКЦИЯХ
      console.log(`🔍 Проверяем ${instructions.length} инструкций на корректность ключей...`);
      const fixedInstructions = instructions.map((instruction, index) => {
        try {
          // Проверяем programId
          let fixedProgramId;
          if (instruction.programId instanceof PublicKey) {
            fixedProgramId = instruction.programId;
          } else if (typeof instruction.programId === 'string') {
            fixedProgramId = new PublicKey(instruction.programId);
          } else {
            throw new Error(`Инструкция ${index}: programId не является PublicKey или строкой`);
          }

          // 🔧 БЕЗОПАСНАЯ ПРОВЕРКА И ИСПРАВЛЕНИЕ ВСЕХ КЛЮЧЕЙ
          const fixedKeys = (instruction.keys && Array.isArray(instruction.keys))
            ? instruction.keys.map((key, keyIndex) => {
            let fixedPubkey;
            if (key.pubkey instanceof PublicKey) {
              fixedPubkey = key.pubkey;
            } else if (typeof key.pubkey === 'string') {
              fixedPubkey = new PublicKey(key.pubkey);
            } else {
              throw new Error(`Инструкция ${index}, ключ ${keyIndex}: pubkey не является PublicKey или строкой`);
            }

            return {
              pubkey: fixedPubkey,
              isSigner: Boolean(key.isSigner),
              isWritable: Boolean(key.isWritable)
            };
          })
            : []; // Fallback к пустому массиву если keys отсутствует

          return {
            programId: fixedProgramId,
            keys: fixedKeys,
            data: instruction.data || Buffer.alloc(0)
          };

        } catch (error) {
          console.error(`❌ Ошибка в инструкции ${index}: ${error.message}`);
          throw error;
        }
      });

      console.log(`✅ Все ${fixedInstructions.length} инструкций проверены и исправлены`);

      // Создаем тестовую транзакцию (ОФИЦИАЛЬНЫЙ МЕТОД)
      const testMessage = new TransactionMessage({
        payerKey: payerKey,
        recentBlockhash: fakeBlockhash,
        instructions: fixedInstructions
      });

      // Компилируем в V0 сообщение с ALT (ОФИЦИАЛЬНАЯ КОМПИЛЯЦИЯ)
      const compiledMessage = testMessage.compileToV0Message(addressLookupTables);

      // Создаем VersionedTransaction (ОФИЦИАЛЬНЫЙ КОНСТРУКТОР)
      const testTransaction = new VersionedTransaction(compiledMessage);

      // Измеряем реальный размер через сериализацию (ТОЧНОЕ ИЗМЕРЕНИЕ)
      const serialized = testTransaction.serialize();
      const realSize = serialized.length;

      console.log(`📊 ОФИЦИАЛЬНАЯ ОЦЕНКА РАЗМЕРА:`);
      console.log(`   📋 Инструкций: ${instructions.length}`);
      console.log(`   🔑 ALT таблиц: ${addressLookupTables.length}`);
      console.log(`   📊 Реальный размер: ${realSize} байт`);
      console.log(`   🎯 Лимит Solana: 1232 байт`);
      console.log(`   📈 Запас: ${1232 - realSize} байт`);

      return realSize;

    } catch (error) {
      console.error(`❌ Ошибка официальной оценки размера: ${error.message}`);

      // ТОЧНАЯ FALLBACK ОЦЕНКА
      console.log(`🔄 Переходим к точной оценке размера...`);

      const baseSize = 64; // Точный размер заголовка транзакции
      let instructionSize = 0;

      // 🎯 ТОЧНЫЙ РАСЧЕТ РАЗМЕРА КАЖДОЙ ИНСТРУКЦИИ
      instructions.forEach((instruction, index) => {
        const size = this.calculatePreciseInstructionSize(instruction);
        instructionSize += size;
      });

      const altSize = addressLookupTables.length * 40; // 40 байт на ALT таблицу

      const preciseSize = baseSize + instructionSize + altSize;

      console.log(`📊 ТОЧНАЯ ОЦЕНКА:`);
      console.log(`   🔧 Базовый размер: ${baseSize} байт`);
      console.log(`   📋 Инструкции: ${instructionSize} байт (точный расчет)`);
      console.log(`   🔑 ALT таблицы: ${addressLookupTables.length} × 40 = ${altSize} байт`);
      console.log(`   📊 Итого: ${preciseSize} байт`);

      return preciseSize;
    }
  }

  /**
   * 📏 ТОЧНЫЙ РАСЧЕТ РАЗМЕРА ИНСТРУКЦИИ
   */
  calculatePreciseInstructionSize(instruction) {
    try {
      let size = 0;

      // 1 байт - индекс программы
      size += 1;

      // 1 байт - количество аккаунтов
      size += 1;

      // По 1 байту на каждый аккаунт (индекс в таблице аккаунтов)
      size += (instruction.keys?.length || 0);

      // 4 байта - длина данных
      size += 4;

      // Данные инструкции
      size += (instruction.data?.length || 0);

      return size;
    } catch (error) {
      console.error(`❌ Ошибка точного расчета размера инструкции: ${error.message}`);
      return 50; // Разумная fallback оценка
    }
  }

  /**
   * 📊 ОЦЕНКА РАЗМЕРА ИНСТРУКЦИИ (КОНСЕРВАТИВНАЯ)
   */
  estimateInstructionSize(instruction) {
    try {
      const INSTRUCTION_BASE_SIZE = 80; // Увеличенный базовый размер для безопасности
      const dataSize = instruction.data ? instruction.data.length : 0;
      const keysSize = instruction.keys ? instruction.keys.length * 2 : 0; // Консервативная оценка ключей

      return INSTRUCTION_BASE_SIZE + dataSize + keysSize;
    } catch (error) {
      console.error(`❌ Ошибка оценки размера инструкции: ${error.message}`);
      return 150; // Более консервативная оценка
    }
  }

  /**
   * 🔍 СОЗДАНИЕ ХЕША ИНСТРУКЦИИ ДЛЯ ПРЕДОТВРАЩЕНИЯ ДУБЛИКАТОВ (SOLANA-СОВМЕСТИМЫЙ)
   */
  getInstructionHash(instruction) {
    try {
      // 🚨 SOLANA СПЕЦИФИКА: Некоторые программы НЕ ДОПУСКАЮТ дубликаты даже с разными данными!
      const programId = instruction.programId ? instruction.programId.toString() : 'unknown';
      const data = instruction.data ? instruction.data.toString('hex') : '';
      const keys = instruction.keys ? instruction.keys.map(k => k.pubkey.toString()).join(',') : '';

      // 🔧 СПЕЦИАЛЬНАЯ ОБРАБОТКА ДЛЯ КРИТИЧЕСКИХ ПРОГРАММ
      const criticalPrograms = [
        'MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA', // MarginFi
        'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo', // Meteora DLMM
        'ComputeBudget111111111111111111111111111111',   // Compute Budget
        'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA'    // Token Program
      ];

      // Для критических программ - учитываем ТОЛЬКО programId (НЕ ДУБЛИРУЕМ ВООБЩЕ!)
      if (criticalPrograms.includes(programId)) {
        return `CRITICAL:${programId}`;
      }

      // Для остальных программ - полный хеш
      return `${programId}:${data}:${keys}`;
    } catch (error) {
      console.error(`❌ Ошибка создания хеша инструкции: ${error.message}`);
      return Math.random().toString(36); // Fallback: случайный хеш
    }
  }

  /**
   * 🎯 ОПРЕДЕЛЕНИЕ ОПТИМАЛЬНОГО УРОВНЯ ОБФУСКАЦИИ
   */
  calculateOptimalObfuscationLevel(currentSize, addressLookupTables = []) {
    try {
      console.log(`\n🎯 РАСЧЕТ ОПТИМАЛЬНОГО УРОВНЯ ОБФУСКАЦИИ:`);
      console.log(`   📊 Текущий размер: ${currentSize} байт`);
      console.log(`   🎯 Лимит Solana: ${this.SIZE_LIMIT} байт`);
      console.log(`   📈 РЕАЛЬНЫЙ запас: ${this.SIZE_LIMIT - currentSize} байт`);

      const availableSpace = this.SIZE_LIMIT - currentSize;
      
      if (availableSpace < 0) {
        console.log(`🚨 КРИТИЧЕСКАЯ ОШИБКА: Транзакция уже превышает лимит!`);
        return { level: 0, instructions: [], reason: 'OVERSIZED' };
      }
      
      // 📊 РАЗМЕРЫ РАЗНЫХ УРОВНЕЙ ОБФУСКАЦИИ (ОПТИМИЗИРОВАНЫ ДЛЯ 148 БАЙТ ЗАПАСА)
      const levelSizes = {
        [this.obfuscationLevels.MINIMAL]: 80,    // 1-2 инструкции (было 150)
        [this.obfuscationLevels.LIGHT]: 140,     // 3-4 инструкции (было 300)
        [this.obfuscationLevels.MEDIUM]: 200,    // 5-6 инструкций (было 450)
        [this.obfuscationLevels.HEAVY]: 300,     // 7-8 инструкций (было 600)
        [this.obfuscationLevels.MAXIMUM]: 400    // 9-10 инструкций (было 750)
      };
      
      // Находим максимальный уровень который помещается
      let selectedLevel = 0;
      let selectedSize = 0;
      
      for (const [level, size] of Object.entries(levelSizes)) {
        if (size <= availableSpace) {
          selectedLevel = parseInt(level);
          selectedSize = size;
        }
      }
      
      const levelNames = {
        0: 'НЕТ',
        1: 'МИНИМАЛЬНАЯ',
        2: 'ЛЕГКАЯ', 
        3: 'СРЕДНЯЯ',
        4: 'ТЯЖЕЛАЯ',
        5: 'МАКСИМАЛЬНАЯ'
      };
      
      console.log(`✅ Выбран уровень: ${selectedLevel} (${levelNames[selectedLevel]})`);
      console.log(`📊 Размер обфускации: ${selectedSize} байт`);
      console.log(`📈 Итоговый размер: ${currentSize + selectedSize} байт`);
      console.log(`🛡️ РЕАЛЬНЫЙ запас: ${this.SIZE_LIMIT - (currentSize + selectedSize)} байт`);

      return {
        level: selectedLevel,
        estimatedSize: selectedSize,
        finalSize: currentSize + selectedSize,
        margin: this.SIZE_LIMIT - (currentSize + selectedSize)
      };
      
    } catch (error) {
      console.error(`❌ Ошибка расчета уровня обфускации: ${error.message}`);
      return { level: 0, instructions: [], reason: 'ERROR' };
    }
  }

  /**
   * 🎭 СОЗДАНИЕ ОБФУСКАЦИОННЫХ ИНСТРУКЦИЙ ПО УРОВНЮ (С ПРОВЕРКОЙ ДУБЛИКАТОВ)
   */
  async createObfuscationInstructions(level, availableSpace, existingInstructions = []) {
    try {
      console.log(`\n🎭 СОЗДАНИЕ ОБФУСКАЦИИ УРОВНЯ ${level}:`);
      
      const instructions = [];
      
      switch (level) {
        case this.obfuscationLevels.MINIMAL:
          instructions.push(...await this.createMinimalObfuscation());
          break;
          
        case this.obfuscationLevels.LIGHT:
          instructions.push(...await this.createLightObfuscation());
          break;
          
        case this.obfuscationLevels.MEDIUM:
          instructions.push(...await this.createMediumObfuscation());
          break;
          
        case this.obfuscationLevels.HEAVY:
          instructions.push(...await this.createHeavyObfuscation());
          break;
          
        case this.obfuscationLevels.MAXIMUM:
          instructions.push(...await this.createMaximumObfuscation());
          break;
          
        default:
          console.log(`⚠️ Обфускация не применяется (уровень ${level})`);
          return [];
      }
      
      // 🔍 СОЗДАЕМ SET СУЩЕСТВУЮЩИХ ХЕШЕЙ ИЗ БАЗОВЫХ ИНСТРУКЦИЙ
      const existingHashes = new Set();
      for (const existingIx of existingInstructions) {
        const hash = this.getInstructionHash(existingIx);
        existingHashes.add(hash);
      }
      console.log(`🔍 Найдено ${existingHashes.size} существующих хешей в базовых инструкциях`);

      // 🔍 УДАЛЯЕМ ДУБЛИРОВАННЫЕ ИНСТРУКЦИИ (ВКЛЮЧАЯ КОНФЛИКТЫ С БАЗОВЫМИ)
      const uniqueInstructions = [];
      const usedHashes = new Set([...existingHashes]); // Начинаем с существующих хешей

      for (const instruction of instructions) {
        const hash = this.getInstructionHash(instruction);
        if (!usedHashes.has(hash)) {
          usedHashes.add(hash);
          uniqueInstructions.push(instruction);
        } else {
          console.log(`⚠️ Удален дубликат/конфликт: ${instruction.programId?.toString().slice(0, 8)}...`);
        }
      }

      console.log(`🔍 Проверка дубликатов: ${instructions.length} → ${uniqueInstructions.length} инструкций`);

      // 📊 ПРОВЕРЯЕМ ЧТО НЕ ПРЕВЫШАЕМ ДОСТУПНОЕ МЕСТО
      const totalSize = uniqueInstructions.reduce((sum, ix) => sum + this.estimateInstructionSize(ix), 0);

      if (totalSize > availableSpace) {
        console.log(`⚠️ Обфускация превышает доступное место, урезаем...`);
        return this.trimObfuscationToFit(uniqueInstructions, availableSpace);
      }

      console.log(`✅ Создано ${uniqueInstructions.length} уникальных обфускационных инструкций`);
      console.log(`📊 Общий размер: ${totalSize} байт`);

      return uniqueInstructions;
      
    } catch (error) {
      console.error(`❌ Ошибка создания обфускации: ${error.message}`);
      return [];
    }
  }

  /**
   * 🎭 МИНИМАЛЬНАЯ ОБФУСКАЦИЯ (1-2 инструкции)
   */
  async createMinimalObfuscation() {
    const instructions = [];

    // 🚨 НЕ ДОБАВЛЯЕМ COMPUTE BUDGET - ОНИ УЖЕ ЕСТЬ В БАЗОВЫХ ИНСТРУКЦИЯХ!
    // Вместо этого добавляем безопасные обфускационные инструкции

    // 1. Memo инструкция с уникальными данными
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000000);
    const memoData = Buffer.from(`obf_minimal_${timestamp}_${random.toString(36)}`);
    instructions.push(new TransactionInstruction({
      keys: [],
      programId: new PublicKey('MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr'),
      data: memoData
    }));

    console.log(`   ✅ Минимальная обфускация: ${instructions.length} инструкций (БЕЗ COMPUTE BUDGET)`);
    return instructions;
  }

  /**
   * 🎭 ЛЕГКАЯ ОБФУСКАЦИЯ (3-4 уникальные инструкции)
   */
  async createLightObfuscation() {
    const instructions = [];
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000000);

    // Базовые инструкции
    instructions.push(...await this.createMinimalObfuscation());

    // 3. Memo инструкция с уникальными данными
    const memoData = Buffer.from(`obf_light_${timestamp}_${random.toString(36)}`);
    instructions.push(new TransactionInstruction({
      keys: [],
      programId: new PublicKey('MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr'),
      data: memoData
    }));

    // 4. Дополнительная memo инструкция (вместо дублирования compute budget)
    const memoData2 = Buffer.from(`light_${random}_${timestamp.toString(36)}`);
    instructions.push(new TransactionInstruction({
      keys: [],
      programId: new PublicKey('MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr'),
      data: memoData2
    }));

    console.log(`   ✅ Легкая обфускация: ${instructions.length} инструкций`);
    return instructions;
  }

  /**
   * 🎭 СРЕДНЯЯ ОБФУСКАЦИЯ (5-6 инструкций)
   */
  async createMediumObfuscation() {
    const instructions = [];
    
    // Базовые инструкции
    instructions.push(...await this.createLightObfuscation());
    
    // 5. Создание временного аккаунта (будет закрыт в той же транзакции)
    const tempAccount = new PublicKey('********************************');
    instructions.push(
      SystemProgram.createAccount({
        fromPubkey: this.wallet.publicKey,
        newAccountPubkey: tempAccount,
        lamports: 0,
        space: 0,
        programId: SystemProgram.programId
      })
    );
    
    // 6. Дополнительная memo инструкция
    const memoData2 = Buffer.from(`obf_${Math.random().toString(36).slice(2)}`);
    instructions.push(new TransactionInstruction({
      keys: [],
      programId: new PublicKey('MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr'),
      data: memoData2
    }));
    
    console.log(`   ✅ Средняя обфускация: ${instructions.length} инструкций`);
    return instructions;
  }

  /**
   * 🎭 ТЯЖЕЛАЯ ОБФУСКАЦИЯ (7-8 инструкций)
   */
  async createHeavyObfuscation() {
    const instructions = [];
    
    // Базовые инструкции
    instructions.push(...await this.createMediumObfuscation());
    
    // 7. Множественные compute budget инструкции
    for (let i = 0; i < 2; i++) {
      instructions.push(
        ComputeBudgetProgram.setComputeUnitLimit({
          units: 150000 + Math.floor(Math.random() * 50000)
        })
      );
    }
    
    console.log(`   ✅ Тяжелая обфускация: ${instructions.length} инструкций`);
    return instructions;
  }

  /**
   * 🎭 МАКСИМАЛЬНАЯ ОБФУСКАЦИЯ (9-10 инструкций)
   */
  async createMaximumObfuscation() {
    const instructions = [];
    
    // Базовые инструкции
    instructions.push(...await this.createHeavyObfuscation());
    
    // 9-10. Дополнительные memo инструкции с большими данными
    for (let i = 0; i < 2; i++) {
      const largeMemoData = Buffer.from(`max_obf_${Date.now()}_${i}_${Math.random().toString(36).slice(2)}_padding_data_for_size`);
      instructions.push(new TransactionInstruction({
        keys: [],
        programId: new PublicKey('MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr'),
        data: largeMemoData
      }));
    }
    
    console.log(`   ✅ Максимальная обфускация: ${instructions.length} инструкций`);
    return instructions;
  }

  /**
   * ✂️ УРЕЗАНИЕ ОБФУСКАЦИИ ДО НУЖНОГО РАЗМЕРА
   */
  trimObfuscationToFit(instructions, maxSize) {
    try {
      console.log(`✂️ Урезаем обфускацию до ${maxSize} байт...`);
      
      const trimmedInstructions = [];
      let currentSize = 0;
      
      for (const instruction of instructions) {
        const instructionSize = this.estimateInstructionSize(instruction);
        
        if (currentSize + instructionSize <= maxSize) {
          trimmedInstructions.push(instruction);
          currentSize += instructionSize;
        } else {
          console.log(`⚠️ Пропускаем инструкцию (не помещается): ${instructionSize} байт`);
        }
      }
      
      console.log(`✅ Урезано до ${trimmedInstructions.length} инструкций (${currentSize} байт)`);
      return trimmedInstructions;
      
    } catch (error) {
      console.error(`❌ Ошибка урезания обфускации: ${error.message}`);
      return [];
    }
  }

  /**
   * 🎯 ГЛАВНЫЙ МЕТОД: ПРИМЕНЕНИЕ ДИНАМИЧЕСКОЙ ОБФУСКАЦИИ
   */
  async applyDynamicObfuscation(baseInstructions, addressLookupTables = []) {
    try {
      console.log(`\n🎭 ===== ПРИМЕНЕНИЕ ДИНАМИЧЕСКОЙ ОБФУСКАЦИИ =====`);

      // 1. Оцениваем РЕАЛЬНЫЙ размер базовых инструкций через сериализацию
      const baseSize = this.estimateRealTransactionSize(baseInstructions, addressLookupTables);
      console.log(`📊 РЕАЛЬНЫЙ размер базовых инструкций: ${baseSize} байт`);
      
      // 2. Рассчитываем оптимальный уровень обфускации
      const obfuscationPlan = this.calculateOptimalObfuscationLevel(baseSize, addressLookupTables);
      
      if (obfuscationPlan.level === 0) {
        console.log(`⚠️ Обфускация не применяется: ${obfuscationPlan.reason}`);
        return {
          instructions: baseInstructions,
          obfuscationLevel: 0,
          finalSize: baseSize,
          margin: this.SIZE_LIMIT - baseSize
        };
      }
      
      // 3. Создаем обфускационные инструкции (С ПРОВЕРКОЙ НА БАЗОВЫЕ!)
      const availableSpace = this.SIZE_LIMIT - baseSize;
      const obfuscationInstructions = await this.createObfuscationInstructions(
        obfuscationPlan.level,
        availableSpace,
        baseInstructions // ПЕРЕДАЕМ БАЗОВЫЕ ИНСТРУКЦИИ ДЛЯ ПРОВЕРКИ ДУБЛИКАТОВ!
      );
      
      // 4. Объединяем инструкции и проверяем на дубликаты
      const combinedInstructions = [...baseInstructions, ...obfuscationInstructions];

      // 🔍 ФИНАЛЬНАЯ ПРОВЕРКА НА ДУБЛИКАТЫ В ТРАНЗАКЦИИ
      const finalInstructions = [];
      const finalUsedHashes = new Set();

      for (const instruction of combinedInstructions) {
        const hash = this.getInstructionHash(instruction);
        if (!finalUsedHashes.has(hash)) {
          finalUsedHashes.add(hash);
          finalInstructions.push(instruction);
        } else {
          console.log(`🚨 КРИТИЧЕСКИЙ ДУБЛИКАТ УДАЛЕН: ${instruction.programId?.toString().slice(0, 8)}...`);
        }
      }

      console.log(`🔍 Финальная проверка дубликатов: ${combinedInstructions.length} → ${finalInstructions.length} инструкций`);

      // 5. КРИТИЧЕСКАЯ ПРОВЕРКА: Измеряем РЕАЛЬНЫЙ размер финальной транзакции
      let finalSize = this.estimateRealTransactionSize(finalInstructions, addressLookupTables);
      let actualInstructions = finalInstructions;

      // 6. ЭКСТРЕННОЕ УРЕЗАНИЕ если реальный размер превышает лимит
      if (finalSize > this.SOLANA_TX_LIMIT) {
        console.log(`🚨 ЭКСТРЕННОЕ УРЕЗАНИЕ: Реальный размер ${finalSize} > ${this.SOLANA_TX_LIMIT}`);
        console.log(`   📊 Превышение: ${finalSize - this.SOLANA_TX_LIMIT} байт`);

        // Убираем обфускацию полностью
        actualInstructions = baseInstructions;
        finalSize = this.estimateRealTransactionSize(actualInstructions, addressLookupTables);

        console.log(`✂️ Обфускация удалена, новый размер: ${finalSize} байт`);

        if (finalSize > this.SOLANA_TX_LIMIT) {
          console.log(`🚨 КРИТИЧЕСКАЯ ОШИБКА: Даже базовая транзакция превышает лимит!`);
          console.log(`   📊 Размер базовой транзакции: ${finalSize} байт`);
          console.log(`   🎯 Лимит Solana: ${this.SOLANA_TX_LIMIT} байт`);
          console.log(`   📈 Превышение: ${finalSize - this.SOLANA_TX_LIMIT} байт`);

          return {
            instructions: baseInstructions,
            obfuscationLevel: 0,
            finalSize: finalSize,
            margin: this.SOLANA_TX_LIMIT - finalSize,
            error: 'BASE_TRANSACTION_TOO_LARGE'
          };
        }
      }

      // 7. ДОПОЛНИТЕЛЬНАЯ ПРОВЕРКА С ЗАПАСОМ БЕЗОПАСНОСТИ
      const safetyMargin = 10; // Увеличиваем запас до 10 байт
      if (finalSize > (this.SOLANA_TX_LIMIT - safetyMargin)) {
        console.log(`⚠️ ПРЕДУПРЕЖДЕНИЕ: Транзакция близка к лимиту!`);
        console.log(`   📊 Размер: ${finalSize} байт`);
        console.log(`   🎯 Безопасный лимит: ${this.SOLANA_TX_LIMIT - safetyMargin} байт`);
        console.log(`   🛡️ Запас: ${(this.SOLANA_TX_LIMIT - safetyMargin) - finalSize} байт`);

        // Если слишком близко к лимиту, убираем последние обфускационные инструкции
        while (finalSize > (this.SOLANA_TX_LIMIT - safetyMargin) && actualInstructions.length > baseInstructions.length) {
          actualInstructions.pop(); // Убираем последнюю обфускационную инструкцию
          finalSize = this.estimateRealTransactionSize(actualInstructions, addressLookupTables);
          console.log(`   ✂️ Убрана 1 обфускационная инструкция, новый размер: ${finalSize} байт`);
        }
      }

      // 8. ФИНАЛЬНАЯ ДЕДУПЛИКАЦИЯ ПОСЛЕ ОБФУСКАЦИИ (ВКЛЮЧАЯ БАЗОВЫЕ ИНСТРУКЦИИ)
      console.log(`🔍 Финальная дедупликация: ${actualInstructions.length} инструкций`);
      console.log(`   📊 Базовых: ${baseInstructions.length}, обфускационных: ${actualInstructions.length - baseInstructions.length}`);

      const uniqueFinalInstructions = this.removeDuplicateInstructions(actualInstructions);
      const duplicatesRemoved = actualInstructions.length - uniqueFinalInstructions.length;

      if (duplicatesRemoved > 0) {
        console.log(`   🗑️ Удалено дубликатов после обфускации: ${duplicatesRemoved}`);
        console.log(`   ✅ Финальных инструкций: ${uniqueFinalInstructions.length}`);
        console.log(`   💡 Обфускационная инструкция дублировала базовую!`);
        // Пересчитываем размер после удаления дубликатов
        finalSize = this.estimateRealTransactionSize(uniqueFinalInstructions, addressLookupTables);
      } else {
        console.log(`   ✅ Дубликатов не найдено`);
      }

      console.log(`\n✅ ДИНАМИЧЕСКАЯ ОБФУСКАЦИЯ ПРИМЕНЕНА:`);
      console.log(`   📊 Базовых инструкций: ${baseInstructions.length}`);
      console.log(`   🎭 Обфускационных инструкций: ${uniqueFinalInstructions.length - baseInstructions.length}`);
      console.log(`   📋 Итого инструкций: ${uniqueFinalInstructions.length}`);
      console.log(`   📊 РЕАЛЬНЫЙ размер: ${finalSize} байт`);
      console.log(`   🎯 Лимит: ${this.SOLANA_TX_LIMIT} байт`);
      console.log(`   🛡️ Запас: ${this.SOLANA_TX_LIMIT - finalSize} байт`);
      console.log(`   🎭 Уровень обфускации: ${actualInstructions === finalInstructions ? obfuscationPlan.level : 0}/5`);

      return {
        instructions: uniqueFinalInstructions,
        obfuscationLevel: actualInstructions === finalInstructions ? obfuscationPlan.level : 0,
        finalSize: finalSize,
        margin: this.SOLANA_TX_LIMIT - finalSize,
        baseInstructions: baseInstructions.length,
        obfuscationInstructions: uniqueFinalInstructions.length - baseInstructions.length
      };
      
    } catch (error) {
      console.error(`❌ Ошибка применения динамической обфускации: ${error.message}`);
      return {
        instructions: baseInstructions,
        obfuscationLevel: 0,
        finalSize: baseSize,
        margin: this.SIZE_LIMIT - baseSize,
        error: error.message
      };
    }
  }

  /**
   * 🗑️ УДАЛЕНИЕ ДУБЛИРУЮЩИХСЯ ИНСТРУКЦИЙ (УЛУЧШЕННЫЙ АЛГОРИТМ)
   */
  removeDuplicateInstructions(instructions) {
    const seen = new Set();
    const unique = [];

    for (let i = 0; i < instructions.length; i++) {
      const instruction = instructions[i];

      // 🔧 БЕЗОПАСНОЕ СОЗДАНИЕ УНИКАЛЬНОГО КЛЮЧА ДЛЯ ИНСТРУКЦИИ С СОРТИРОВКОЙ КЛЮЧЕЙ
      const sortedKeys = (instruction.keys && Array.isArray(instruction.keys))
        ? instruction.keys
            .map(k => ({
              pubkey: k.pubkey.toString(),
              isSigner: k.isSigner,
              isWritable: k.isWritable
            }))
            .sort((a, b) => a.pubkey.localeCompare(b.pubkey))
        : []; // Fallback к пустому массиву если keys отсутствует

      const key = JSON.stringify({
        programId: instruction.programId.toString(),
        data: Array.from(instruction.data),
        keys: sortedKeys
      });

      if (!seen.has(key)) {
        seen.add(key);
        unique.push(instruction);
      } else {
        console.log(`   🗑️ Найден дубликат на позиции ${i}: ${instruction.programId.toString().slice(0, 8)}...`);
      }
    }

    return unique;
  }
}

module.exports = DynamicObfuscationManager;
