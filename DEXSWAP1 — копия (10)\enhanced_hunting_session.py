#!/usr/bin/env python3
"""
🚀 ENHANCED HUNTING SESSION
Улучшенная сессия поиска уязвимостей с реальными данными
"""

import asyncio
import json
import time
from datetime import datetime
import logging

from unified_data_management_system import UnifiedDataManager, TestTarget
from strategy_integration_engine import StrategyIntegrationEngine
from intelligent_retest_system import IntelligentRetestSystem

# Настройка логирования
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def create_realistic_test_targets():
    """Создание реалистичных тестовых целей"""
    targets = [
        {
            'name': 'Uniswap V3',
            'url': 'https://immunefi.com/bounty/uniswap/',
            'contracts': [
                '0x1F98431c8aD98523631AE4a59f267346ea31F984',  # Factory
                '******************************************',  # Router
                '******************************************'   # Position Manager
            ],
            'endpoints': [
                'https://api.uniswap.org/v1/',
                'https://interface.gateway.uniswap.org/v1/'
            ],
            'metadata': {
                'max_bounty': '$2,250,000',
                'total_paid': '$1,500,000',
                'ecosystem': 'Ethereum',
                'type': 'DEX',
                'kyc_required': True,
                'poc_required': True,
                'vulnerability_types': ['Smart Contract', 'Web/App']
            },
            'priority_score': 0.95
        },
        {
            'name': 'Compound Finance',
            'url': 'https://immunefi.com/bounty/compound/',
            'contracts': [
                '******************************************',  # Comptroller
                '******************************************',  # cDAI
                '******************************************'   # cETH
            ],
            'endpoints': [
                'https://api.compound.finance/api/v2/',
                'https://compound.finance/api/'
            ],
            'metadata': {
                'max_bounty': '$1,000,000',
                'total_paid': '$800,000',
                'ecosystem': 'Ethereum',
                'type': 'Lending',
                'kyc_required': False,
                'poc_required': True,
                'vulnerability_types': ['Smart Contract']
            },
            'priority_score': 0.88
        },
        {
            'name': 'Aave Protocol',
            'url': 'https://immunefi.com/bounty/aave/',
            'contracts': [
                '******************************************',  # Lending Pool
                '******************************************',  # Address Provider
                '******************************************'   # Price Oracle
            ],
            'endpoints': [
                'https://aave-api-v2.aave.com/',
                'https://protocol-api.aave.com/'
            ],
            'metadata': {
                'max_bounty': '$1,000,000',
                'total_paid': '$600,000',
                'ecosystem': 'Ethereum',
                'type': 'Lending',
                'kyc_required': True,
                'poc_required': True,
                'vulnerability_types': ['Smart Contract', 'Web/App']
            },
            'priority_score': 0.85
        },
        {
            'name': 'Curve Finance',
            'url': 'https://immunefi.com/bounty/curve/',
            'contracts': [
                '******************************************',  # Registry
                '******************************************',  # 3Pool
                '******************************************'   # Curve Token
            ],
            'endpoints': [
                'https://api.curve.fi/',
                'https://curve.fi/api/'
            ],
            'metadata': {
                'max_bounty': '$1,000,000',
                'total_paid': '$400,000',
                'ecosystem': 'Ethereum',
                'type': 'DEX',
                'kyc_required': False,
                'poc_required': True,
                'vulnerability_types': ['Smart Contract']
            },
            'priority_score': 0.82
        },
        {
            'name': 'Solana Foundation',
            'url': 'https://immunefi.com/bounty/solana/',
            'contracts': [
                '********************************',  # System Program
                'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA',  # Token Program
                'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL'   # Associated Token
            ],
            'endpoints': [
                'https://api.mainnet-beta.solana.com',
                'https://solana-api.projectserum.com'
            ],
            'metadata': {
                'max_bounty': '$2,000,000',
                'total_paid': '$1,200,000',
                'ecosystem': 'Solana',
                'type': 'Blockchain',
                'kyc_required': True,
                'poc_required': True,
                'vulnerability_types': ['Blockchain/DLT', 'Smart Contract']
            },
            'priority_score': 0.92
        }
    ]
    
    return targets

async def run_enhanced_hunting_session():
    """Запуск улучшенной сессии поиска уязвимостей"""
    print("🚀 ENHANCED BUG HUNTING SESSION")
    print("=" * 60)
    
    session_start = time.time()
    session_stats = {
        'session_id': f"enhanced_session_{int(session_start)}",
        'start_time': datetime.now(),
        'targets_tested': 0,
        'strategies_executed': 0,
        'vulnerabilities_found': 0,
        'high_confidence_vulnerabilities': 0,
        'execution_times': []
    }
    
    try:
        # Инициализация системы
        print("🔧 Инициализация компонентов...")
        async with UnifiedDataManager("enhanced_hunting.db") as dm:
            strategy_engine = StrategyIntegrationEngine(dm)
            await strategy_engine.initialize_strategies()
            
            retest_system = IntelligentRetestSystem(dm, strategy_engine)
            
            print(f"✅ Загружено {len(strategy_engine.loaded_strategies)} стратегий")
            
            # Создание реалистичных целей
            print("🎯 Создание реалистичных целей...")
            realistic_targets_data = await create_realistic_test_targets()
            
            # Импорт целей в базу данных
            imported_count = await dm.import_immunefi_data(realistic_targets_data)
            print(f"📊 Импортировано {imported_count} реалистичных целей")
            
            # Получение целей для тестирования
            targets = await dm.get_targets_for_testing(
                min_priority=0.8,  # Только высокоприоритетные
                limit=5
            )
            
            print(f"🎯 Выбрано {len(targets)} высокоприоритетных целей")
            
            # Тестирование каждой цели
            for i, target in enumerate(targets, 1):
                print(f"\n🔍 [{i}/{len(targets)}] Тестирование: {target.name}")
                print(f"   💰 Награда: {target.metadata.get('max_bounty', 'Unknown')}")
                print(f"   🏗️ Контрактов: {len(target.contracts)}")
                print(f"   🌐 Endpoints: {len(target.endpoints)}")
                print(f"   ⭐ Приоритет: {target.priority_score:.2f}")
                
                target_start = time.time()
                
                try:
                    # Выполнение всех доступных стратегий
                    results = await strategy_engine.execute_all_strategies(target)
                    
                    target_time = time.time() - target_start
                    session_stats['execution_times'].append(target_time)
                    
                    # Анализ результатов
                    successful_strategies = [r for r in results['results'] if r.success]
                    vulnerabilities_found = [r for r in results['results'] if r.vulnerability_found]
                    high_confidence_vulns = [r for r in vulnerabilities_found if r.confidence >= 0.7]
                    
                    session_stats['targets_tested'] += 1
                    session_stats['strategies_executed'] += len(results['results'])
                    session_stats['vulnerabilities_found'] += len(vulnerabilities_found)
                    session_stats['high_confidence_vulnerabilities'] += len(high_confidence_vulns)
                    
                    print(f"   ✅ Стратегий выполнено: {len(successful_strategies)}/{len(results['results'])}")
                    print(f"   🐛 Уязвимостей найдено: {len(vulnerabilities_found)}")
                    print(f"   🎯 Высокая уверенность: {len(high_confidence_vulns)}")
                    print(f"   ⏱️ Время выполнения: {target_time:.2f}с")
                    
                    # Детальный анализ найденных уязвимостей
                    if high_confidence_vulns:
                        print(f"   🚨 ВЫСОКОУВЕРЕННЫЕ УЯЗВИМОСТИ:")
                        for vuln in high_confidence_vulns:
                            print(f"      - {vuln.strategy_name}: {vuln.vulnerability_data.get('analysis_type', 'Unknown')} (уверенность: {vuln.confidence:.1%})")
                    
                    # Небольшая пауза между целями
                    await asyncio.sleep(1)
                    
                except Exception as e:
                    print(f"   ❌ Ошибка тестирования {target.name}: {e}")
                    continue
            
            # Анализ кандидатов для ретестирования
            print(f"\n🔄 Анализ кандидатов для ретестирования...")
            retest_candidates = await retest_system.analyze_retest_candidates()
            
            # Планирование ретестов для высокоценных целей
            high_value_retests = 0
            for candidate in retest_candidates[:10]:  # Топ-10
                await retest_system.schedule_retest(candidate)
                high_value_retests += 1
            
            print(f"📅 Запланировано {high_value_retests} ретестов")
            
            # Выполнение части ретестов
            if high_value_retests > 0:
                print(f"🚀 Выполнение ретестов...")
                await retest_system.execute_retest_queue(max_concurrent=2)
            
            # Генерация финального отчета
            session_duration = time.time() - session_start
            session_stats['session_duration'] = session_duration
            session_stats['end_time'] = datetime.now()
            
            # Статистика производительности
            avg_time_per_target = sum(session_stats['execution_times']) / len(session_stats['execution_times']) if session_stats['execution_times'] else 0
            vulnerability_rate = session_stats['vulnerabilities_found'] / session_stats['strategies_executed'] if session_stats['strategies_executed'] > 0 else 0
            
            # Генерация отчета из базы данных
            db_report = await dm.generate_testing_report(session_id=session_stats['session_id'])
            
            # Финальный отчет
            final_report = {
                'session_stats': session_stats,
                'performance_metrics': {
                    'avg_time_per_target': avg_time_per_target,
                    'vulnerability_discovery_rate': vulnerability_rate,
                    'strategies_per_target': session_stats['strategies_executed'] / session_stats['targets_tested'] if session_stats['targets_tested'] > 0 else 0,
                    'high_confidence_rate': session_stats['high_confidence_vulnerabilities'] / session_stats['vulnerabilities_found'] if session_stats['vulnerabilities_found'] > 0 else 0
                },
                'database_report': db_report,
                'strategy_performance': await dm.get_strategy_performance()
            }
            
            # Сохранение отчета
            report_filename = f"enhanced_hunting_report_{session_stats['session_id']}.json"
            with open(report_filename, 'w', encoding='utf-8') as f:
                json.dump(final_report, f, indent=2, ensure_ascii=False, default=str)
            
            # Вывод итоговой статистики
            print(f"\n🎉 СЕССИЯ ЗАВЕРШЕНА!")
            print(f"=" * 60)
            print(f"⏱️  Общее время: {session_duration:.1f} секунд")
            print(f"🎯 Целей протестировано: {session_stats['targets_tested']}")
            print(f"⚡ Стратегий выполнено: {session_stats['strategies_executed']}")
            print(f"🐛 Уязвимостей найдено: {session_stats['vulnerabilities_found']}")
            print(f"🎯 Высокая уверенность: {session_stats['high_confidence_vulnerabilities']}")
            print(f"📊 Скорость обнаружения: {vulnerability_rate:.1%}")
            print(f"⚡ Среднее время на цель: {avg_time_per_target:.1f}с")
            print(f"💾 Отчет сохранен: {report_filename}")
            
            # Рекомендации
            print(f"\n💡 РЕКОМЕНДАЦИИ:")
            if session_stats['high_confidence_vulnerabilities'] > 0:
                print(f"   🎯 Найдены {session_stats['high_confidence_vulnerabilities']} высокоуверенных уязвимостей - готовы для отправки!")
            
            if vulnerability_rate > 0.1:
                print(f"   📈 Высокая эффективность ({vulnerability_rate:.1%}) - продолжайте тестирование!")
            
            if avg_time_per_target > 60:
                print(f"   ⚡ Рассмотрите оптимизацию стратегий для ускорения")
            
            print(f"\n🚀 Система готова к полномасштабному тестированию!")
            
            return final_report
            
    except Exception as e:
        logger.error(f"❌ Критическая ошибка сессии: {e}")
        raise

async def main():
    """Главная функция"""
    await run_enhanced_hunting_session()

if __name__ == "__main__":
    asyncio.run(main())
