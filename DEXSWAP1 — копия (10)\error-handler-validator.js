#!/usr/bin/env node

/**
 * 🛡️ ERROR HANDLER & VALIDATOR
 * 
 * Обработка ошибок и валидация для DLMM арбитражной стратегии
 */

const { PublicKey } = require('@solana/web3.js');

class ErrorHandlerValidator {
    constructor() {
        // 📊 ЛИМИТЫ И КОНСТАНТЫ (АДАПТИРОВАНЫ ДЛЯ ТЕСТОВ)
        this.LIMITS = {
            MIN_FLASH_LOAN: 1000,        // $1K минимум (для тестов)
            MAX_FLASH_LOAN: 10000000,    // $10M максимум
            MIN_LIQUIDITY: 500,          // $500 минимум (для тестов)
            MAX_LIQUIDITY: 5000000,      // $5M максимум
            MIN_TRADING: 100,            // $100 минимум (для тестов)
            MAX_TRADING: 2000000,        // $2M максимум
            MIN_ROI: 0.5,                // 0.5% минимум (для тестов)
            MAX_PRICE_IMPACT: 25.0,      // 25% максимум
            MIN_COVERAGE: 100.0,         // 100% минимум покрытия (для тестов)
            MAX_BINS: 50,                // 50 bins максимум
            MIN_BINS: 3,                 // 3 bins минимум
            MIN_PROFIT: 10               // $10 минимум прибыли (для тестов)
        };

        // 🔑 ИЗВЕСТНЫЕ PROGRAM IDS
        this.KNOWN_PROGRAMS = {
            'MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA': 'MarginFi',
            'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo': 'Meteora DLMM',
            'JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4': 'Jupiter V6'
        };

        console.log('🛡️ ERROR HANDLER & VALIDATOR ИНИЦИАЛИЗИРОВАН');
    }

    /**
     * ✅ ВАЛИДАЦИЯ СТРАТЕГИЧЕСКИХ ПАРАМЕТРОВ
     */
    validateStrategyParams(params) {
        console.log('\n✅ ВАЛИДАЦИЯ СТРАТЕГИЧЕСКИХ ПАРАМЕТРОВ...');
        
        const errors = [];
        const warnings = [];

        // Flash Loan валидация
        if (params.flash_loan_amount < this.LIMITS.MIN_FLASH_LOAN) {
            errors.push(`Flash Loan слишком мал: $${params.flash_loan_amount} < $${this.LIMITS.MIN_FLASH_LOAN}`);
        }
        if (params.flash_loan_amount > this.LIMITS.MAX_FLASH_LOAN) {
            errors.push(`Flash Loan слишком велик: $${params.flash_loan_amount} > $${this.LIMITS.MAX_FLASH_LOAN}`);
        }

        // Ликвидность валидация
        if (params.liquidity_amount < this.LIMITS.MIN_LIQUIDITY) {
            errors.push(`Ликвидность слишком мала: $${params.liquidity_amount} < $${this.LIMITS.MIN_LIQUIDITY}`);
        }
        if (params.liquidity_amount > this.LIMITS.MAX_LIQUIDITY) {
            errors.push(`Ликвидность слишком велика: $${params.liquidity_amount} > $${this.LIMITS.MAX_LIQUIDITY}`);
        }

        // Торговля валидация
        if (params.trading_amount < this.LIMITS.MIN_TRADING) {
            errors.push(`Торговая сумма слишком мала: $${params.trading_amount} < $${this.LIMITS.MIN_TRADING}`);
        }
        if (params.trading_amount > this.LIMITS.MAX_TRADING) {
            errors.push(`Торговая сумма слишком велика: $${params.trading_amount} > $${this.LIMITS.MAX_TRADING}`);
        }

        // ROI валидация
        if (params.target_roi < this.LIMITS.MIN_ROI) {
            warnings.push(`Целевой ROI низкий: ${params.target_roi}% < ${this.LIMITS.MIN_ROI}%`);
        }

        // Соотношения валидация
        const totalNeeded = params.liquidity_amount + params.trading_amount;
        if (totalNeeded > params.flash_loan_amount) {
            errors.push(`Flash Loan недостаточен: нужно $${totalNeeded}, есть $${params.flash_loan_amount}`);
        }

        const liquidityRatio = params.liquidity_amount / params.trading_amount;
        if (liquidityRatio < 2.0) {
            warnings.push(`Низкое соотношение ликвидность/торговля: ${liquidityRatio.toFixed(2)} < 2.0`);
        }

        this.logValidationResults('Стратегические параметры', errors, warnings);
        return { valid: errors.length === 0, errors, warnings };
    }

    /**
     * 🏊 ВАЛИДАЦИЯ ДАННЫХ ПУЛОВ
     */
    validatePoolData(poolsData) {
        console.log('\n🏊 ВАЛИДАЦИЯ ДАННЫХ ПУЛОВ...');
        
        const errors = [];
        const warnings = [];

        // Проверка структуры данных
        if (!poolsData.largePool || !poolsData.mediumPool) {
            errors.push('Отсутствуют данные пулов (largePool или mediumPool)');
            return { valid: false, errors, warnings };
        }

        // Проверка арбитражного спреда
        if (poolsData.arbitragePercent < 1.0) {
            warnings.push(`Низкий арбитражный спред: ${poolsData.arbitragePercent.toFixed(2)}% < 1.0%`);
        }
        if (poolsData.arbitragePercent > 50.0) {
            warnings.push(`Подозрительно высокий спред: ${poolsData.arbitragePercent.toFixed(2)}% > 50.0%`);
        }

        // Проверка TVL пулов
        if (poolsData.largePool.tvl < 1000000) {
            warnings.push(`Низкий TVL большого пула: $${poolsData.largePool.tvl.toLocaleString()}`);
        }
        if (poolsData.mediumPool.tvl < 500000) {
            warnings.push(`Низкий TVL среднего пула: $${poolsData.mediumPool.tvl.toLocaleString()}`);
        }

        // Проверка цен
        const priceDiff = Math.abs(poolsData.largePool.current_price - poolsData.mediumPool.current_price);
        const avgPrice = (poolsData.largePool.current_price + poolsData.mediumPool.current_price) / 2;
        const priceDeviation = (priceDiff / avgPrice) * 100;

        if (priceDeviation > 20.0) {
            warnings.push(`Большое отклонение цен: ${priceDeviation.toFixed(2)}% > 20.0%`);
        }

        this.logValidationResults('Данные пулов', errors, warnings);
        return { valid: errors.length === 0, errors, warnings };
    }

    /**
     * 📊 ВАЛИДАЦИЯ РЕЗУЛЬТАТОВ РАСЧЕТОВ
     */
    validateCalculationResults(results) {
        console.log('\n📊 ВАЛИДАЦИЯ РЕЗУЛЬТАТОВ РАСЧЕТОВ...');
        
        const errors = [];
        const warnings = [];

        // Проверка прибыльности
        if (results.profitability.roi < this.LIMITS.MIN_ROI) {
            errors.push(`ROI ниже минимума: ${results.profitability.roi.toFixed(2)}% < ${this.LIMITS.MIN_ROI}%`);
        }

        // Проверка влияния на цену
        if (results.priceImpact.impactPercent > this.LIMITS.MAX_PRICE_IMPACT) {
            warnings.push(`Высокое влияние на цену: ${results.priceImpact.impactPercent.toFixed(2)}% > ${this.LIMITS.MAX_PRICE_IMPACT}%`);
        }

        // Проверка покрытия ликвидности
        if (results.binsData.coverageRatio * 100 < this.LIMITS.MIN_COVERAGE) {
            errors.push(`Недостаточное покрытие: ${(results.binsData.coverageRatio * 100).toFixed(1)}% < ${this.LIMITS.MIN_COVERAGE}%`);
        }

        // Проверка количества bins
        if (results.binsData.totalBins < this.LIMITS.MIN_BINS) {
            errors.push(`Слишком мало bins: ${results.binsData.totalBins} < ${this.LIMITS.MIN_BINS}`);
        }
        if (results.binsData.totalBins > this.LIMITS.MAX_BINS) {
            warnings.push(`Много bins: ${results.binsData.totalBins} > ${this.LIMITS.MAX_BINS}`);
        }

        // Проверка чистой прибыли
        if (results.profitability.netProfit < this.LIMITS.MIN_PROFIT) {
            warnings.push(`Низкая прибыль: $${results.profitability.netProfit.toFixed(0)} < $${this.LIMITS.MIN_PROFIT}`);
        }

        this.logValidationResults('Результаты расчетов', errors, warnings);
        return { valid: errors.length === 0, errors, warnings };
    }

    /**
     * 🔑 ВАЛИДАЦИЯ АДРЕСОВ
     */
    validateAddresses(addresses) {
        console.log('\n🔑 ВАЛИДАЦИЯ АДРЕСОВ...');
        
        const errors = [];
        const warnings = [];

        // Проверка формата адресов
        Object.entries(addresses).forEach(([key, address]) => {
            if (typeof address === 'string' && address !== 'USER_WALLET' && address !== 'NEW_POSITION_KEYPAIR') {
                try {
                    new PublicKey(address);
                } catch (error) {
                    errors.push(`Неверный формат адреса ${key}: ${address}`);
                }
            }
        });

        // Проверка известных программ
        if (addresses.program && this.KNOWN_PROGRAMS[addresses.program]) {
            console.log(`   ✅ Программа: ${this.KNOWN_PROGRAMS[addresses.program]}`);
        }

        this.logValidationResults('Адреса', errors, warnings);
        return { valid: errors.length === 0, errors, warnings };
    }

    /**
     * 🚨 ОБРАБОТКА ОШИБОК ТРАНЗАКЦИИ
     */
    handleTransactionError(error, context = '') {
        console.log(`\n🚨 ОБРАБОТКА ОШИБКИ ТРАНЗАКЦИИ ${context}...`);
        
        const errorInfo = {
            type: 'UNKNOWN',
            message: error.message || error.toString(),
            recoverable: false,
            suggestions: []
        };

        // Анализ типов ошибок
        if (error.message.includes('insufficient funds')) {
            errorInfo.type = 'INSUFFICIENT_FUNDS';
            errorInfo.recoverable = true;
            errorInfo.suggestions.push('Проверьте баланс кошелька');
            errorInfo.suggestions.push('Уменьшите размер транзакции');
        }
        
        if (error.message.includes('slippage')) {
            errorInfo.type = 'SLIPPAGE_EXCEEDED';
            errorInfo.recoverable = true;
            errorInfo.suggestions.push('Увеличьте допустимый slippage');
            errorInfo.suggestions.push('Попробуйте позже');
        }

        if (error.message.includes('compute budget exceeded')) {
            errorInfo.type = 'COMPUTE_BUDGET_EXCEEDED';
            errorInfo.recoverable = true;
            errorInfo.suggestions.push('Увеличьте compute units');
            errorInfo.suggestions.push('Разделите транзакцию на части');
        }

        if (error.message.includes('custom program error')) {
            errorInfo.type = 'PROGRAM_ERROR';
            errorInfo.recoverable = false;
            errorInfo.suggestions.push('Проверьте параметры инструкций');
            errorInfo.suggestions.push('Обновите SDK');
        }

        console.log(`   🔍 Тип ошибки: ${errorInfo.type}`);
        console.log(`   💬 Сообщение: ${errorInfo.message}`);
        console.log(`   🔄 Восстановимо: ${errorInfo.recoverable ? 'ДА' : 'НЕТ'}`);
        
        if (errorInfo.suggestions.length > 0) {
            console.log('   💡 Предложения:');
            errorInfo.suggestions.forEach(suggestion => {
                console.log(`      - ${suggestion}`);
            });
        }

        return errorInfo;
    }

    /**
     * 📋 ЛОГИРОВАНИЕ РЕЗУЛЬТАТОВ ВАЛИДАЦИИ
     */
    logValidationResults(category, errors, warnings) {
        if (errors.length === 0 && warnings.length === 0) {
            console.log(`   ✅ ${category}: ВСЕ ПРОВЕРКИ ПРОЙДЕНЫ`);
            return;
        }

        if (errors.length > 0) {
            console.log(`   ❌ ${category}: ОШИБКИ (${errors.length}):`);
            errors.forEach(error => console.log(`      - ${error}`));
        }

        if (warnings.length > 0) {
            console.log(`   ⚠️ ${category}: ПРЕДУПРЕЖДЕНИЯ (${warnings.length}):`);
            warnings.forEach(warning => console.log(`      - ${warning}`));
        }
    }

    /**
     * 🎯 ПОЛНАЯ ВАЛИДАЦИЯ СТРАТЕГИИ
     */
    validateCompleteStrategy(strategyData) {
        console.log('\n🎯 ПОЛНАЯ ВАЛИДАЦИЯ СТРАТЕГИИ...');
        
        const results = {
            valid: true,
            errors: [],
            warnings: [],
            checks: {}
        };

        // 1. Валидация параметров
        const paramsCheck = this.validateStrategyParams(strategyData.params);
        results.checks.params = paramsCheck;
        results.errors.push(...paramsCheck.errors);
        results.warnings.push(...paramsCheck.warnings);

        // 2. Валидация пулов
        if (strategyData.poolsData) {
            const poolsCheck = this.validatePoolData(strategyData.poolsData);
            results.checks.pools = poolsCheck;
            results.errors.push(...poolsCheck.errors);
            results.warnings.push(...poolsCheck.warnings);
        }

        // 3. Валидация расчетов
        if (strategyData.calculations) {
            const calcCheck = this.validateCalculationResults(strategyData.calculations);
            results.checks.calculations = calcCheck;
            results.errors.push(...calcCheck.errors);
            results.warnings.push(...calcCheck.warnings);
        }

        // 4. Валидация адресов
        if (strategyData.addresses) {
            const addrCheck = this.validateAddresses(strategyData.addresses);
            results.checks.addresses = addrCheck;
            results.errors.push(...addrCheck.errors);
            results.warnings.push(...addrCheck.warnings);
        }

        results.valid = results.errors.length === 0;

        console.log('\n📊 ИТОГИ ВАЛИДАЦИИ:');
        console.log(`   ✅ Статус: ${results.valid ? 'ВАЛИДНА' : 'НЕВАЛИДНА'}`);
        console.log(`   ❌ Ошибок: ${results.errors.length}`);
        console.log(`   ⚠️ Предупреждений: ${results.warnings.length}`);

        return results;
    }
}

// 🧪 ТЕСТОВЫЙ ЗАПУСК
if (require.main === module) {
    const validator = new ErrorHandlerValidator();
    
    // Тестовые данные
    const testStrategy = {
        params: {
            flash_loan_amount: 1800000,
            liquidity_amount: 1400000,
            trading_amount: 400000,
            target_roi: 3.0
        },
        poolsData: {
            largePool: { tvl: 7272122, current_price: 307.15 },
            mediumPool: { tvl: 3252168, current_price: 340.35 },
            arbitragePercent: 10.81
        },
        calculations: {
            profitability: { roi: 3.24, netProfit: 58334 },
            priceImpact: { impactPercent: 6.76 },
            binsData: { totalBins: 8, coverageRatio: 3.516 }
        },
        addresses: {
            program: 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo',
            binArrayLower: '6K84Zmy8ZnihPCdoHona2iT25dFHsMAHzZtdjhtw5XG9'
        }
    };
    
    const validation = validator.validateCompleteStrategy(testStrategy);
    
    console.log('\n🎉 ВАЛИДАЦИЯ ЗАВЕРШЕНА!');
    console.log(`Результат: ${validation.valid ? 'УСПЕШНО' : 'ПРОВАЛЕНО'}`);
}

module.exports = ErrorHandlerValidator;
