/**
 * 🔥 ТОЧНОЕ СРАВНЕНИЕ ВСЕХ КЛЮЧЕЙ ИЗ ALT И ИНСТРУКЦИЙ
 * 
 * Делаем 100% точное сравнение что покрыто и что НЕ покрыто
 */

const { Connection, PublicKey } = require('@solana/web3.js');
const fs = require('fs');
require('dotenv').config({ path: '.env.solana' });

console.log('🔥 ТОЧНОЕ СРАВНЕНИЕ ВСЕХ КЛЮЧЕЙ ИЗ ALT И ИНСТРУКЦИЙ');
console.log('=' .repeat(80));

async function exactKeysComparison() {
    try {
        // 1. Подключение к RPC
        const connection = new Connection(process.env.QUICKNODE_RPC_URL);
        console.log('✅ Подключение к QUICKNODE RPC установлено');

        // 2. Загружаем ALT таблицу
        const customALTAddress = new PublicKey('FAeyUf4AdG7vTQ2Q89r9hWBkPdYkykJKG4CxgqArBGXe');
        console.log(`📋 Кастомная ALT таблица: ${customALTAddress.toString()}`);

        const altAccount = await connection.getAddressLookupTable(customALTAddress);
        if (!altAccount || !altAccount.value) {
            throw new Error('ALT таблица не найдена!');
        }

        const altAddresses = altAccount.value.state.addresses.map(addr => addr.toString());
        console.log(`📊 Адресов в ALT: ${altAddresses.length}`);

        // 3. Загружаем 24 инструкции
        console.log('\n📋 ЗАГРУЗКА 24 ИНСТРУКЦИЙ...');
        const analysis = require('./full-24-instructions-analysis.js');
        
        if (!analysis.instructions || analysis.instructions.length === 0) {
            throw new Error('Инструкции не найдены!');
        }

        console.log(`✅ Загружено инструкций: ${analysis.instructions.length}`);

        // 4. ИЗВЛЕКАЕМ ВСЕ УНИКАЛЬНЫЕ КЛЮЧИ ИЗ ИНСТРУКЦИЙ
        console.log('\n🔍 ИЗВЛЕЧЕНИЕ ВСЕХ КЛЮЧЕЙ ИЗ ИНСТРУКЦИЙ...');
        
        const allKeysFromInstructions = new Set();
        const keyDetails = new Map(); // Для хранения деталей о ключах
        
        analysis.instructions.forEach((ix, ixIndex) => {
            console.log(`📊 Инструкция ${ixIndex}: ${ix.keys.length} ключей`);
            
            // Добавляем programId
            const programId = ix.programId.toString();
            allKeysFromInstructions.add(programId);
            if (!keyDetails.has(programId)) {
                keyDetails.set(programId, {
                    address: programId,
                    type: 'PROGRAM_ID',
                    instructions: [ixIndex],
                    isWritable: false,
                    isSigner: false
                });
            } else {
                keyDetails.get(programId).instructions.push(ixIndex);
            }
            
            // Добавляем все ключи
            ix.keys.forEach((key, keyIndex) => {
                const keyStr = key.pubkey.toString();
                allKeysFromInstructions.add(keyStr);
                
                if (!keyDetails.has(keyStr)) {
                    keyDetails.set(keyStr, {
                        address: keyStr,
                        type: 'ACCOUNT_KEY',
                        instructions: [ixIndex],
                        isWritable: key.isWritable,
                        isSigner: key.isSigner,
                        positions: [`${ixIndex}:${keyIndex}`]
                    });
                } else {
                    keyDetails.get(keyStr).instructions.push(ixIndex);
                    keyDetails.get(keyStr).positions.push(`${ixIndex}:${keyIndex}`);
                }
            });
        });

        const totalUniqueKeys = allKeysFromInstructions.size;
        console.log(`🔑 Всего уникальных ключей в инструкциях: ${totalUniqueKeys}`);

        // 5. ТОЧНОЕ СРАВНЕНИЕ
        console.log('\n🔍 ТОЧНОЕ СРАВНЕНИЕ КЛЮЧЕЙ:');
        console.log('=' .repeat(80));

        const keysInALT = [];
        const keysNotInALT = [];
        
        for (const key of allKeysFromInstructions) {
            const details = keyDetails.get(key);
            
            if (altAddresses.includes(key)) {
                keysInALT.push({
                    address: key,
                    short: `${key.slice(0,8)}...${key.slice(-8)}`,
                    ...details
                });
                console.log(`✅ В ALT: ${key.slice(0,8)}...${key.slice(-8)} (${details.instructions.length} инструкций)`);
            } else {
                keysNotInALT.push({
                    address: key,
                    short: `${key.slice(0,8)}...${key.slice(-8)}`,
                    ...details
                });
                console.log(`❌ НЕ в ALT: ${key.slice(0,8)}...${key.slice(-8)} (${details.instructions.length} инструкций)`);
            }
        }

        // 6. ДЕТАЛЬНАЯ СТАТИСТИКА
        console.log('\n📊 ДЕТАЛЬНАЯ СТАТИСТИКА:');
        console.log('=' .repeat(80));
        console.log(`🔑 Всего уникальных ключей: ${totalUniqueKeys}`);
        console.log(`✅ Покрыто ALT: ${keysInALT.length}`);
        console.log(`❌ НЕ покрыто ALT: ${keysNotInALT.length}`);
        console.log(`📊 Покрытие: ${((keysInALT.length / totalUniqueKeys) * 100).toFixed(1)}%`);

        // 7. АНАЛИЗ НЕПОКРЫТЫХ КЛЮЧЕЙ
        console.log('\n❌ ДЕТАЛЬНЫЙ АНАЛИЗ НЕПОКРЫТЫХ КЛЮЧЕЙ:');
        console.log('=' .repeat(80));
        
        keysNotInALT.forEach((key, index) => {
            console.log(`${index + 1}. ${key.short}`);
            console.log(`   Полный адрес: ${key.address}`);
            console.log(`   Тип: ${key.type}`);
            console.log(`   Инструкции: [${key.instructions.join(', ')}]`);
            console.log(`   Позиции: [${key.positions ? key.positions.join(', ') : 'N/A'}]`);
            console.log(`   Writable: ${key.isWritable}, Signer: ${key.isSigner}`);
            console.log('');
        });

        // 8. АНАЛИЗ ПОКРЫТЫХ КЛЮЧЕЙ
        console.log('\n✅ ДЕТАЛЬНЫЙ АНАЛИЗ ПОКРЫТЫХ КЛЮЧЕЙ:');
        console.log('=' .repeat(80));
        
        keysInALT.forEach((key, index) => {
            console.log(`${index + 1}. ${key.short}`);
            console.log(`   Полный адрес: ${key.address}`);
            console.log(`   Тип: ${key.type}`);
            console.log(`   Инструкции: [${key.instructions.join(', ')}]`);
            console.log(`   Позиции: [${key.positions ? key.positions.join(', ') : 'N/A'}]`);
            console.log(`   Writable: ${key.isWritable}, Signer: ${key.isSigner}`);
            console.log('');
        });

        // 9. ПРОВЕРКА АДРЕСОВ В ALT КОТОРЫХ НЕТ В ИНСТРУКЦИЯХ
        console.log('\n🔍 АДРЕСА В ALT КОТОРЫХ НЕТ В ИНСТРУКЦИЯХ:');
        console.log('=' .repeat(80));
        
        const unusedALTAddresses = altAddresses.filter(addr => !allKeysFromInstructions.has(addr));
        
        if (unusedALTAddresses.length > 0) {
            console.log(`⚠️ Найдено ${unusedALTAddresses.length} неиспользуемых адресов в ALT:`);
            unusedALTAddresses.forEach((addr, index) => {
                console.log(`   ${index + 1}. ${addr.slice(0,8)}...${addr.slice(-8)} - ${addr}`);
            });
        } else {
            console.log('✅ Все адреса в ALT используются в инструкциях');
        }

        // 10. ЭКОНОМИЯ БАЙТ
        console.log('\n💰 АНАЛИЗ ЭКОНОМИИ БАЙТ:');
        console.log('=' .repeat(80));
        
        const byteSavingsPerKey = 31; // 32 байта адрес - 1 байт индекс
        const currentSavings = keysInALT.length * byteSavingsPerKey;
        const potentialSavings = keysNotInALT.length * byteSavingsPerKey;
        const totalPotentialSavings = totalUniqueKeys * byteSavingsPerKey;
        
        console.log(`💰 Текущая экономия: ${currentSavings} байт (${keysInALT.length} ключей)`);
        console.log(`🎯 Потенциальная экономия: ${potentialSavings} байт (${keysNotInALT.length} ключей)`);
        console.log(`🚀 Максимальная экономия: ${totalPotentialSavings} байт (${totalUniqueKeys} ключей)`);

        // 11. СОХРАНЯЕМ РЕЗУЛЬТАТ
        const result = {
            timestamp: new Date().toISOString(),
            altAddress: customALTAddress.toString(),
            altSize: altAddresses.length,
            analysis: {
                totalUniqueKeys: totalUniqueKeys,
                keysInALT: keysInALT.length,
                keysNotInALT: keysNotInALT.length,
                coveragePercent: parseFloat(((keysInALT.length / totalUniqueKeys) * 100).toFixed(1)),
                unusedALTAddresses: unusedALTAddresses.length
            },
            keysInALT: keysInALT,
            keysNotInALT: keysNotInALT,
            unusedALTAddresses: unusedALTAddresses,
            byteSavings: {
                current: currentSavings,
                potential: potentialSavings,
                maximum: totalPotentialSavings
            }
        };

        const resultFile = 'exact-keys-comparison-result.json';
        fs.writeFileSync(resultFile, JSON.stringify(result, null, 2));
        console.log(`\n✅ Результат сохранен в: ${resultFile}`);

        console.log(`\n${'='.repeat(80)}`);
        console.log('🎯 ТОЧНОЕ СРАВНЕНИЕ ЗАВЕРШЕНО!');
        console.log(`🔑 Всего ключей: ${totalUniqueKeys}`);
        console.log(`✅ В ALT: ${keysInALT.length} (${((keysInALT.length / totalUniqueKeys) * 100).toFixed(1)}%)`);
        console.log(`❌ НЕ в ALT: ${keysNotInALT.length} (${((keysNotInALT.length / totalUniqueKeys) * 100).toFixed(1)}%)`);
        console.log(`💰 Потенциальная экономия: ${potentialSavings} байт`);
        console.log(`${'='.repeat(80)}`);

    } catch (error) {
        console.error('❌ Ошибка сравнения:', error.message);
        console.error(error.stack);
    }
}

// Запуск сравнения
if (require.main === module) {
    exactKeysComparison();
}

module.exports = { exactKeysComparison };
