/**
 * 🏦 ПРИМЕР КОНТРОЛЯ DEX В JUPITER
 * ═══════════════════════════════════════════════════════════════
 * 🎯 ДЕМОНСТРАЦИЯ: Как настроить и проверить DEX ограничения
 */

const { JupiterSwapInstructions } = require('./jupiter-swap-instructions');

// Константы токенов
const TOKENS = {
  SOL: 'So11111111111111111111111111111111111111112',
  USDC: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
  USDT: 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB'
};

async function demonstrateDEXControl() {
  console.log(`🏦 ===== ДЕМОНСТРАЦИЯ КОНТРОЛЯ DEX В JUPITER =====`);
  
  // Создаем экземпляр Jupiter (без подключения для демо)
  const jupiter = new JupiterSwapInstructions(null, null);
  
  try {
    // 1️⃣ ПРОВЕРКА ТЕКУЩИХ НАСТРОЕК
    console.log(`\n1️⃣ ТЕКУЩИЕ НАСТРОЙКИ DEX:`);
    const currentSettings = jupiter.getDEXSettings();
    console.log(`   Разрешенные DEX: ${currentSettings.includeDexes.join(', ')}`);
    console.log(`   Исключенные DEX: ${currentSettings.excludeDexes.join(', ')}`);
    console.log(`   Режим: ${currentSettings.mode}`);
    
    // 2️⃣ ПРОВЕРКА МАРШРУТА SOL → USDC
    console.log(`\n2️⃣ ПРОВЕРКА МАРШРУТА SOL → USDC:`);
    const solUsdcCheck = await jupiter.verifyJupiterUsesOnlySpecifiedDEX(
      TOKENS.SOL,
      TOKENS.USDC,
      1000000000, // 1 SOL
      50 // 0.5% slippage
    );
    
    if (solUsdcCheck.success) {
      console.log(`   ✅ Все DEX разрешены: ${solUsdcCheck.usedDEXes.join(', ')}`);
      console.log(`   📊 Шагов в маршруте: ${solUsdcCheck.routeSteps}`);
      console.log(`   💰 Цена: ${solUsdcCheck.quote.inAmount} → ${solUsdcCheck.quote.outAmount}`);
    } else {
      console.log(`   ❌ Проблема: ${solUsdcCheck.reason || solUsdcCheck.error}`);
      if (solUsdcCheck.unauthorizedDEXes) {
        console.log(`   🚨 Неразрешенные DEX: ${solUsdcCheck.unauthorizedDEXes.join(', ')}`);
      }
    }
    
    // 3️⃣ НАСТРОЙКА БОЛЕЕ СТРОГИХ ОГРАНИЧЕНИЙ
    console.log(`\n3️⃣ НАСТРОЙКА БОЛЕЕ СТРОГИХ ОГРАНИЧЕНИЙ:`);
    jupiter.setDEXSettings(
      ['Raydium', 'Orca'], // Только Raydium и Orca
      null,
      'include'
    );
    
    // 4️⃣ ПОВТОРНАЯ ПРОВЕРКА С НОВЫМИ НАСТРОЙКАМИ
    console.log(`\n4️⃣ ПРОВЕРКА С НОВЫМИ НАСТРОЙКАМИ:`);
    const strictCheck = await jupiter.verifyJupiterUsesOnlySpecifiedDEX(
      TOKENS.SOL,
      TOKENS.USDC,
      1000000000,
      50
    );
    
    if (strictCheck.success) {
      console.log(`   ✅ Строгие ограничения соблюдены: ${strictCheck.usedDEXes.join(', ')}`);
    } else {
      console.log(`   ❌ Строгие ограничения нарушены: ${strictCheck.reason || strictCheck.error}`);
    }
    
    // 5️⃣ ДЕМОНСТРАЦИЯ ИСКЛЮЧЕНИЯ DEX
    console.log(`\n5️⃣ ДЕМОНСТРАЦИЯ ИСКЛЮЧЕНИЯ КОНКРЕТНЫХ DEX:`);
    jupiter.setDEXSettings(
      null, // Все DEX разрешены
      ['Meteora', 'Serum'], // Исключаем Meteora и Serum
      'exclude'
    );
    
    const excludeCheck = await jupiter.verifyJupiterUsesOnlySpecifiedDEX(
      TOKENS.USDC,
      TOKENS.USDT,
      100000000, // 100 USDC
      50
    );
    
    if (excludeCheck.success) {
      console.log(`   ✅ Исключения работают: ${excludeCheck.usedDEXes.join(', ')}`);
      console.log(`   📋 Meteora и Serum не используются`);
    } else {
      console.log(`   ❌ Исключения не работают: ${excludeCheck.reason || excludeCheck.error}`);
    }
    
    // 6️⃣ СРАВНЕНИЕ РАЗНЫХ НАСТРОЕК
    console.log(`\n6️⃣ СРАВНЕНИЕ РАЗНЫХ НАСТРОЕК DEX:`);
    
    // Настройка 1: Только Raydium
    jupiter.setDEXSettings(['Raydium'], null, 'include');
    const raydiumOnly = await jupiter.getJupiterQuoteWithSpecificDEX(
      TOKENS.SOL, TOKENS.USDC, 1000000000, 50, ['Raydium']
    );
    
    // Настройка 2: Только Orca
    jupiter.setDEXSettings(['Orca'], null, 'include');
    const orcaOnly = await jupiter.getJupiterQuoteWithSpecificDEX(
      TOKENS.SOL, TOKENS.USDC, 1000000000, 50, ['Orca']
    );
    
    console.log(`   🏦 Raydium: ${raydiumOnly.inAmount} → ${raydiumOnly.outAmount}`);
    console.log(`   🏦 Orca: ${orcaOnly.inAmount} → ${orcaOnly.outAmount}`);
    
    // Сравниваем цены
    const raydiumPrice = parseFloat(raydiumOnly.outAmount) / parseFloat(raydiumOnly.inAmount);
    const orcaPrice = parseFloat(orcaOnly.outAmount) / parseFloat(orcaOnly.inAmount);
    
    if (raydiumPrice > orcaPrice) {
      console.log(`   🏆 Raydium дает лучшую цену: ${((raydiumPrice - orcaPrice) / orcaPrice * 100).toFixed(4)}% лучше`);
    } else {
      console.log(`   🏆 Orca дает лучшую цену: ${((orcaPrice - raydiumPrice) / raydiumPrice * 100).toFixed(4)}% лучше`);
    }
    
    // 7️⃣ ИТОГОВЫЕ РЕКОМЕНДАЦИИ
    console.log(`\n7️⃣ ИТОГОВЫЕ РЕКОМЕНДАЦИИ:`);
    console.log(`   ✅ Для Flash Loan арбитража: используйте только Raydium + Orca`);
    console.log(`   ✅ Для обычной торговли: разрешите все ликвидные DEX`);
    console.log(`   ✅ Всегда проверяйте routePlan в ответе Jupiter`);
    console.log(`   ✅ Мониторьте новые DEX в экосистеме Solana`);
    
    console.log(`\n🎉 ДЕМОНСТРАЦИЯ ЗАВЕРШЕНА УСПЕШНО!`);
    
  } catch (error) {
    console.log(`❌ Ошибка демонстрации: ${error.message}`);
    console.log(`📋 Stack: ${error.stack}`);
  }
}

// Дополнительные утилиты для анализа DEX
class DEXAnalyzer {
  static async compareDEXPrices(inputMint, outputMint, amount, dexList) {
    console.log(`\n📊 СРАВНЕНИЕ ЦЕН РАЗНЫХ DEX:`);
    
    const jupiter = new JupiterSwapInstructions(null, null);
    const results = [];
    
    for (const dex of dexList) {
      try {
        const quote = await jupiter.getJupiterQuoteWithSpecificDEX(
          inputMint, outputMint, amount, 50, [dex]
        );
        
        const price = parseFloat(quote.outAmount) / parseFloat(quote.inAmount);
        results.push({ dex, price, quote });
        
        console.log(`   🏦 ${dex}: ${quote.outAmount} (цена: ${price.toFixed(8)})`);
        
      } catch (error) {
        console.log(`   ❌ ${dex}: Ошибка - ${error.message}`);
      }
      
      // Пауза между запросами
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    // Находим лучшую цену
    if (results.length > 0) {
      const bestResult = results.reduce((best, current) => 
        current.price > best.price ? current : best
      );
      
      console.log(`\n🏆 ЛУЧШАЯ ЦЕНА: ${bestResult.dex} (${bestResult.price.toFixed(8)})`);
    }
    
    return results;
  }
  
  static async analyzeRouteComplexity(inputMint, outputMint, amount) {
    console.log(`\n🔍 АНАЛИЗ СЛОЖНОСТИ МАРШРУТА:`);
    
    const jupiter = new JupiterSwapInstructions(null, null);
    
    // Прямой маршрут
    jupiter.setDEXSettings(['Raydium'], null, 'include');
    const directRoute = await jupiter.verifyJupiterUsesOnlySpecifiedDEX(
      inputMint, outputMint, amount, 50
    );
    
    // Сложный маршрут (если доступен)
    jupiter.setDEXSettings(null, null, 'include'); // Все DEX
    const complexRoute = await jupiter.verifyJupiterUsesOnlySpecifiedDEX(
      inputMint, outputMint, amount, 50
    );
    
    console.log(`   📊 Прямой маршрут: ${directRoute.routeSteps} шагов`);
    console.log(`   📊 Сложный маршрут: ${complexRoute.routeSteps} шагов`);
    
    if (directRoute.success && complexRoute.success) {
      const directPrice = parseFloat(directRoute.quote.outAmount);
      const complexPrice = parseFloat(complexRoute.quote.outAmount);
      
      if (complexPrice > directPrice) {
        const improvement = ((complexPrice - directPrice) / directPrice * 100).toFixed(4);
        console.log(`   🎯 Сложный маршрут лучше на ${improvement}%`);
      } else {
        console.log(`   🎯 Прямой маршрут оптимален`);
      }
    }
  }
}

// Запуск демонстрации
if (require.main === module) {
  demonstrateDEXControl().then(() => {
    console.log(`\n✅ Демонстрация завершена!`);
    
    // Дополнительный анализ (раскомментируйте если нужно)
    /*
    return DEXAnalyzer.compareDEXPrices(
      TOKENS.SOL, 
      TOKENS.USDC, 
      1000000000, 
      ['Raydium', 'Orca', 'Meteora']
    );
    */
  }).catch(error => {
    console.log(`💥 Ошибка: ${error.message}`);
  });
}

module.exports = { demonstrateDEXControl, DEXAnalyzer };
