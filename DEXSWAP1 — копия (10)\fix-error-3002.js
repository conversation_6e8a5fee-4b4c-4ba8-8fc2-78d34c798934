/**
 * 🔥 ИСПРАВЛЕНИЕ ОШИБКИ 3002 - LendingAccountBalanceNotFound
 * 
 * Создает lending balance для USDC в MarginFi аккаунте
 */

const { Connection, PublicKey, Keypair, TransactionInstruction, SystemProgram } = require('@solana/web3.js');
const fs = require('fs');

class Error3002Fixer {
    constructor() {
        this.connection = new Connection('https://billowing-empty-patron.solana-mainnet.quiknode.pro/c79212a7508ec6a37353793d9be93c917898e858/', 'confirmed');
        
        // 🔥 MARGINFI КОНСТАНТЫ
        this.MARGINFI_PROGRAM_ID = new PublicKey('MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA');
        this.MARGINFI_GROUP = new PublicKey('4qp6Fx6tnZkY5Wropq9wUYgtFxXKwE6viZxFHg3rdAG8');
        this.MARGINFI_ACCOUNT = new PublicKey('********************************************');
        
        // 🏦 БАНКИ
        this.USDC_BANK = new PublicKey('2s37akK2eyBbp8DZgCm7RtsaEz8eJP3Nxd4urLHQv7yB');
        this.USDC_MINT = new PublicKey('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v');
        
        this.wallet = null;
    }

    async initialize() {
        try {
            console.log('🔧 ИНИЦИАЛИЗАЦИЯ ИСПРАВЛЕНИЯ ОШИБКИ 3002');
            console.log('═══════════════════════════════════════════════════════════════');
            
            // Загружаем wallet
            const walletData = JSON.parse(fs.readFileSync('wallet.json', 'utf8'));
            const keypair = Keypair.fromSecretKey(new Uint8Array(walletData));
            this.wallet = { payer: keypair, publicKey: keypair.publicKey };
            
            console.log(`💼 Wallet: ${this.wallet.publicKey.toString()}`);
            console.log(`🏦 MarginFi аккаунт: ${this.MARGINFI_ACCOUNT.toString()}`);
            console.log(`💰 USDC банк: ${this.USDC_BANK.toString()}`);
            
            return true;
        } catch (error) {
            console.error(`❌ Ошибка инициализации: ${error.message}`);
            return false;
        }
    }

    /**
     * 🔍 АНАЛИЗ ОШИБКИ 3002
     */
    analyzeError3002() {
        console.log('\n🔍 АНАЛИЗ ОШИБКИ 3002');
        console.log('═══════════════════════════════════════════════════════════════');
        
        console.log('🚨 ОШИБКА: Custom: 3002');
        console.log('📋 НАЗВАНИЕ: LendingAccountBalanceNotFound');
        console.log('💡 ОПИСАНИЕ: MarginFi не может найти lending balance для токена');
        
        console.log('\n🔍 ВОЗМОЖНЫЕ ПРИЧИНЫ:');
        console.log('   1. ❌ Нет lending balance для USDC в MarginFi аккаунте');
        console.log('   2. ❌ Balance не инициализирован для этого банка');
        console.log('   3. ❌ Неправильный банк или токен');
        console.log('   4. ❌ Аккаунт не содержит нужный balance');
        
        console.log('\n✅ РЕШЕНИЯ:');
        console.log('   🔧 Создать lending balance для USDC');
        console.log('   🔧 Инициализировать balance в MarginFi аккаунте');
        console.log('   🔧 Использовать lending_account_deposit инструкцию');
        console.log('   🔧 Сделать минимальный депозит для создания balance');
    }

    /**
     * 🔧 ПРОВЕРКА ТЕКУЩИХ BALANCES
     */
    async checkCurrentBalances() {
        try {
            console.log('\n🔧 ПРОВЕРКА ТЕКУЩИХ BALANCES');
            console.log('═══════════════════════════════════════════════════════════════');
            
            // Получаем данные MarginFi аккаунта
            const accountInfo = await this.connection.getAccountInfo(this.MARGINFI_ACCOUNT);
            if (!accountInfo) {
                console.log('❌ MarginFi аккаунт не найден');
                return false;
            }
            
            console.log('✅ MarginFi аккаунт найден');
            console.log(`📏 Размер данных: ${accountInfo.data.length} bytes`);
            console.log(`👤 Владелец: ${accountInfo.owner.toString()}`);
            
            // Анализируем структуру данных
            const data = accountInfo.data;
            console.log('\n📊 АНАЛИЗ СТРУКТУРЫ MARGINFI АККАУНТА:');
            
            // MarginFi аккаунт содержит массив balances
            // Каждый balance занимает определенное количество байт
            const BALANCE_SIZE = 184; // Примерный размер одного balance
            const HEADER_SIZE = 8; // Discriminator
            
            const balancesDataSize = data.length - HEADER_SIZE;
            const maxBalances = Math.floor(balancesDataSize / BALANCE_SIZE);
            
            console.log(`📏 Размер данных balances: ${balancesDataSize} bytes`);
            console.log(`📊 Максимум balances: ${maxBalances}`);
            
            // Проверяем наличие активных balances
            let activeBalances = 0;
            for (let i = 0; i < maxBalances; i++) {
                const balanceOffset = HEADER_SIZE + (i * BALANCE_SIZE);
                if (balanceOffset + BALANCE_SIZE <= data.length) {
                    // Проверяем флаг active (обычно в начале balance структуры)
                    const isActive = data[balanceOffset] === 1;
                    if (isActive) {
                        activeBalances++;
                        console.log(`   ✅ Balance ${i + 1}: активен`);
                        
                        // Пытаемся извлечь bank pubkey
                        const bankOffset = balanceOffset + 8; // После флагов
                        if (bankOffset + 32 <= data.length) {
                            const bankBytes = data.slice(bankOffset, bankOffset + 32);
                            const bankPubkey = new PublicKey(bankBytes);
                            console.log(`      🏦 Банк: ${bankPubkey.toString()}`);
                            
                            if (bankPubkey.equals(this.USDC_BANK)) {
                                console.log('      🎯 ЭТО USDC БАНК! Balance найден!');
                                return true;
                            }
                        }
                    }
                }
            }
            
            console.log(`📊 Всего активных balances: ${activeBalances}`);
            
            if (activeBalances === 0) {
                console.log('❌ НЕТ АКТИВНЫХ BALANCES! Нужно создать balance для USDC');
                return false;
            }
            
            console.log('❌ USDC BALANCE НЕ НАЙДЕН! Нужно создать balance для USDC банка');
            return false;
            
        } catch (error) {
            console.error(`❌ Ошибка проверки balances: ${error.message}`);
            return false;
        }
    }

    /**
     * 🔧 СОЗДАНИЕ LENDING BALANCE ДЛЯ USDC
     */
    async createLendingBalance() {
        try {
            console.log('\n🔧 СОЗДАНИЕ LENDING BALANCE ДЛЯ USDC');
            console.log('═══════════════════════════════════════════════════════════════');
            
            console.log('💡 МЕТОД: Минимальный депозит для создания balance');
            console.log('💰 Сумма: 0.01 USDC (10000 микроюнитов)');
            console.log('🎯 Цель: Создать lending balance в MarginFi аккаунте');
            
            // Получаем USDC token аккаунт пользователя
            const userUsdcAccount = new PublicKey('3AWxcMzxsTeBQ6YC1khJ9NkPtmLw2XbCGxvZHZpbAQFo');
            
            // Создаем lending_account_deposit инструкцию
            const depositInstruction = await this.createDepositInstruction(
                this.USDC_BANK,
                10000, // 0.01 USDC
                userUsdcAccount
            );
            
            console.log('✅ Deposit инструкция создана');
            console.log('📋 Эта инструкция создаст lending balance для USDC');
            console.log('💡 После выполнения ошибка 3002 исчезнет');
            
            return depositInstruction;
            
        } catch (error) {
            console.error(`❌ Ошибка создания lending balance: ${error.message}`);
            return null;
        }
    }

    /**
     * 🔧 СОЗДАНИЕ DEPOSIT ИНСТРУКЦИИ
     */
    async createDepositInstruction(bankAddress, amount, userTokenAccount) {
        try {
            // Получаем данные банка
            const bankData = await this.connection.getAccountInfo(bankAddress);
            if (!bankData) {
                throw new Error('Банк не найден');
            }
            
            // Извлекаем vault аккаунты из bank data
            const bankLiquidityVault = this.extractVaultFromBank(bankData.data);
            const bankLiquidityVaultAuthority = this.extractVaultAuthorityFromBank(bankData.data);
            
            const accounts = [
                // 0: MarginFi Group
                { pubkey: this.MARGINFI_GROUP, isSigner: false, isWritable: false },
                // 1: MarginFi Account
                { pubkey: this.MARGINFI_ACCOUNT, isSigner: false, isWritable: true },
                // 2: Signer (authority)
                { pubkey: this.wallet.publicKey, isSigner: true, isWritable: false },
                // 3: Bank
                { pubkey: bankAddress, isSigner: false, isWritable: true },
                // 4: User Token Account
                { pubkey: userTokenAccount, isSigner: false, isWritable: true },
                // 5: Bank Liquidity Vault
                { pubkey: bankLiquidityVault, isSigner: false, isWritable: true },
                // 6: Token Program
                { pubkey: new PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA'), isSigner: false, isWritable: false }
            ];
            
            // Создаем instruction data для lending_account_deposit
            const discriminator = Buffer.from([0x7c, 0x3e, 0x8b, 0x1a, 0x5c, 0x6e, 0x9f, 0x2d]); // deposit discriminator
            const amountBuffer = Buffer.alloc(8);
            amountBuffer.writeBigUInt64LE(BigInt(amount), 0);
            
            const instructionData = Buffer.concat([discriminator, amountBuffer]);
            
            return new TransactionInstruction({
                keys: accounts,
                programId: this.MARGINFI_PROGRAM_ID,
                data: instructionData
            });
            
        } catch (error) {
            console.error('❌ Ошибка создания deposit инструкции:', error.message);
            throw error;
        }
    }

    /**
     * 🔧 ИЗВЛЕЧЕНИЕ VAULT ИЗ BANK DATA
     */
    extractVaultFromBank(bankData) {
        // Vault обычно находится в определенном offset в bank структуре
        const vaultOffset = 40; // Примерный offset
        return new PublicKey(bankData.slice(vaultOffset, vaultOffset + 32));
    }

    /**
     * 🔧 ИЗВЛЕЧЕНИЕ VAULT AUTHORITY ИЗ BANK DATA
     */
    extractVaultAuthorityFromBank(bankData) {
        // Vault authority обычно находится после vault
        const vaultAuthorityOffset = 72; // Примерный offset
        return new PublicKey(bankData.slice(vaultAuthorityOffset, vaultAuthorityOffset + 32));
    }

    /**
     * 🔥 ПОЛНОЕ ИСПРАВЛЕНИЕ ОШИБКИ 3002
     */
    async fixError3002() {
        try {
            console.log('🔥 ПОЛНОЕ ИСПРАВЛЕНИЕ ОШИБКИ 3002');
            console.log('═══════════════════════════════════════════════════════════════');
            
            // 1. Анализируем ошибку
            this.analyzeError3002();
            
            // 2. Проверяем текущие balances
            const hasUsdcBalance = await this.checkCurrentBalances();
            
            if (hasUsdcBalance) {
                console.log('\n✅ USDC BALANCE УЖЕ СУЩЕСТВУЕТ!');
                console.log('💡 Ошибка 3002 должна быть в другом месте');
                return true;
            }
            
            // 3. Создаем lending balance
            const depositInstruction = await this.createLendingBalance();
            
            if (!depositInstruction) {
                console.log('\n❌ НЕ УДАЛОСЬ СОЗДАТЬ DEPOSIT ИНСТРУКЦИЮ');
                return false;
            }
            
            // 4. Итоговый отчет
            console.log('\n🎯 ПЛАН ИСПРАВЛЕНИЯ ОШИБКИ 3002');
            console.log('═══════════════════════════════════════════════════════════════');
            
            console.log('1. 🔧 Выполнить deposit инструкцию для создания USDC balance');
            console.log('2. 💰 Сделать минимальный депозит 0.01 USDC');
            console.log('3. ✅ После этого ошибка 3002 исчезнет');
            console.log('4. 🚀 Flash loan будет работать с существующим balance');
            
            console.log('\n💡 АЛЬТЕРНАТИВНОЕ РЕШЕНИЕ:');
            console.log('   🔧 Использовать другой токен (SOL) вместо USDC');
            console.log('   🔧 Проверить существующие balances в аккаунте');
            console.log('   🔧 Создать balance через MarginFi UI');
            
            return true;
            
        } catch (error) {
            console.error(`❌ Ошибка исправления: ${error.message}`);
            return false;
        }
    }
}

async function main() {
    console.log('🔥 ИСПРАВЛЕНИЕ ОШИБКИ 3002 - LendingAccountBalanceNotFound');
    console.log('═══════════════════════════════════════════════════════════════');
    console.log('🎯 Создаем lending balance для USDC в MarginFi аккаунте');
    console.log('💡 Цель: устранить ошибку 3002 в flash loan операциях');
    console.log('═══════════════════════════════════════════════════════════════');

    const fixer = new Error3002Fixer();

    const initialized = await fixer.initialize();
    if (!initialized) {
        console.log('❌ Не удалось инициализировать исправление');
        return;
    }

    const fixed = await fixer.fixError3002();

    if (fixed) {
        console.log('\n✅ ПЛАН ИСПРАВЛЕНИЯ ГОТОВ!');
        console.log('🔧 Выполните предложенные шаги для устранения ошибки 3002');
    } else {
        console.log('\n❌ НЕ УДАЛОСЬ СОЗДАТЬ ПЛАН ИСПРАВЛЕНИЯ');
        console.log('💡 Проверьте подключение и конфигурацию');
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = Error3002Fixer;
