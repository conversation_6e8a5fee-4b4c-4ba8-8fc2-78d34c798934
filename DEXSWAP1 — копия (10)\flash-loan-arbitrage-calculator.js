/**
 * 🚀 КАЛЬКУЛЯТОР FLASH LOAN АРБИТРАЖА
 * 
 * Рассчитывает оптимальную стратегию:
 * 1. Покупка на RAYDIUM (поднимаем цену)
 * 2. Продажа на ORCA (без влияния на цену)
 * 3. Максимизация прибыли с Flash Loan
 */

class FlashLoanArbitrageCalculator {
    constructor() {
        // 🌊 РЕАЛЬНЫЕ ПУЛЫ ДЛЯ АРБИТРАЖА
        this.POOLS = {
            ORCA_GIANT: {
                name: 'OR<PERSON> SOL-USDC ГИГАНТ',
                tvl: 44000000, // $44M
                liquidity: 44000000,
                baseFee: 0.0025, // 0.25%
                role: 'ПРОДАЖА (без влияния на цену)'
            },
            RAYDIUM_GIANT: {
                name: 'RAYDIUM SOL-USDC ГИГАНТ', 
                tvl: 11000000, // $11M
                liquidity: 11000000,
                baseFee: 0.0025, // 0.25%
                role: 'ПОКУПКА (поднимаем цену)'
            }
        };
        
        // Flash Loan параметры
        this.FLASH_LOAN_FEE = 0.0009; // 0.09% MarginFi
        
        console.log('🚀 FlashLoanArbitrageCalculator инициализирован');
        console.log('💡 Стратегия: ПОКУПКА на RAYDIUM → ПРОДАЖА на ORCA');
    }

    /**
     * 🧮 РАСЧЕТ ВЛИЯНИЯ ПОКУПКИ НА ЦЕНУ (RAYDIUM)
     */
    calculatePriceImpactBuy(buyAmount, pool) {
        // Процент от ликвидности
        const impactPercent = (buyAmount / pool.liquidity * 100);
        
        // Квадратичная функция для slippage при покупке
        const slippagePercent = Math.pow(impactPercent / 100, 1.2) * 100;
        
        // Повышение цены = наша выгода!
        const priceIncrease = slippagePercent;
        
        // Комиссия
        const fee = buyAmount * pool.baseFee;
        
        // Общие затраты на покупку
        const totalCost = buyAmount + (buyAmount * slippagePercent / 100) + fee;
        
        return {
            buyAmount,
            impactPercent: impactPercent.toFixed(2),
            priceIncrease: priceIncrease.toFixed(4),
            slippageCost: Math.round(buyAmount * slippagePercent / 100),
            fee: Math.round(fee),
            totalCost: Math.round(totalCost)
        };
    }

    /**
     * 💰 РАСЧЕТ ПРОДАЖИ НА ORCA (БЕЗ ВЛИЯНИЯ НА ЦЕНУ)
     */
    calculateSaleOnOrca(sellAmount, priceIncrease) {
        const orcaPool = this.POOLS.ORCA_GIANT;
        
        // Влияние на ORCA (минимальное)
        const impactPercent = (sellAmount / orcaPool.liquidity * 100);
        const slippagePercent = Math.pow(impactPercent / 100, 1.5) * 100;
        
        // Продаем по повышенной цене!
        const priceBonus = sellAmount * (priceIncrease / 100);
        
        // Потери от slippage на ORCA
        const slippageLoss = sellAmount * (slippagePercent / 100);
        
        // Комиссия ORCA
        const fee = sellAmount * orcaPool.baseFee;
        
        // Чистая выручка
        const grossRevenue = sellAmount + priceBonus;
        const netRevenue = grossRevenue - slippageLoss - fee;
        
        return {
            sellAmount,
            priceBonus: Math.round(priceBonus),
            slippageLoss: Math.round(slippageLoss),
            fee: Math.round(fee),
            grossRevenue: Math.round(grossRevenue),
            netRevenue: Math.round(netRevenue),
            orcaImpact: impactPercent.toFixed(4)
        };
    }

    /**
     * 🎯 РАСЧЕТ ПОЛНОГО АРБИТРАЖА
     */
    calculateFullArbitrage(buyAmount, sellAmount) {
        // 1. Покупка на RAYDIUM (поднимаем цену)
        const buyResult = this.calculatePriceImpactBuy(buyAmount, this.POOLS.RAYDIUM_GIANT);
        
        // 2. Продажа на ORCA (используем повышенную цену)
        const sellResult = this.calculateSaleOnOrca(sellAmount, parseFloat(buyResult.priceIncrease));
        
        // 3. Flash Loan расчеты
        const flashLoanAmount = buyAmount;
        const flashLoanFee = flashLoanAmount * this.FLASH_LOAN_FEE;
        
        // 4. Итоговая прибыль
        const totalCosts = buyResult.totalCost + flashLoanFee;
        const totalRevenue = sellResult.netRevenue;
        const netProfit = totalRevenue - totalCosts;
        const profitPercent = (netProfit / flashLoanAmount * 100);
        
        return {
            strategy: {
                buyAmount,
                sellAmount,
                flashLoanAmount,
                priceIncrease: buyResult.priceIncrease
            },
            costs: {
                raydiumCost: buyResult.totalCost,
                flashLoanFee: Math.round(flashLoanFee),
                totalCosts: Math.round(totalCosts)
            },
            revenue: {
                orcaRevenue: sellResult.netRevenue,
                priceBonus: sellResult.priceBonus,
                totalRevenue
            },
            profit: {
                netProfit: Math.round(netProfit),
                profitPercent: profitPercent.toFixed(4),
                profitable: netProfit > 0
            },
            details: {
                raydiumImpact: buyResult.impactPercent,
                orcaImpact: sellResult.orcaImpact,
                buyResult,
                sellResult
            }
        };
    }

    /**
     * 🏆 ПОИСК ОПТИМАЛЬНОЙ СТРАТЕГИИ
     */
    findOptimalStrategy() {
        console.log('\n🏆 ПОИСК ОПТИМАЛЬНОЙ FLASH LOAN АРБИТРАЖНОЙ СТРАТЕГИИ');
        console.log('=' .repeat(80));
        
        const buyAmounts = [100000, 200000, 300000, 400000, 500000, 600000, 700000, 800000, 1000000];
        const sellRatios = [0.6, 0.7, 0.8, 0.9]; // Продаем 60-90% от купленного
        
        let bestStrategy = { profit: { netProfit: -Infinity } };
        
        console.log('📊 АНАЛИЗ РАЗЛИЧНЫХ СТРАТЕГИЙ:\n');
        
        buyAmounts.forEach(buyAmount => {
            console.log(`💰 ПОКУПКА НА RAYDIUM: $${buyAmount.toLocaleString()}`);
            
            sellRatios.forEach(ratio => {
                const sellAmount = Math.round(buyAmount * ratio);
                const result = this.calculateFullArbitrage(buyAmount, sellAmount);
                
                if (result.profit.netProfit > bestStrategy.profit.netProfit) {
                    bestStrategy = result;
                }
                
                const status = result.profit.profitable ? '✅' : '❌';
                const emoji = result.profit.profitable ? '💚' : '🔴';
                
                console.log(`   ${status} Продажа ${(ratio*100)}% ($${sellAmount.toLocaleString()}):`);
                console.log(`      ${emoji} Прибыль: $${result.profit.netProfit.toLocaleString()} (${result.profit.profitPercent}%)`);
                console.log(`      📈 Повышение цены: ${result.strategy.priceIncrease}%`);
                console.log(`      🎯 Влияние RAYDIUM: ${result.details.raydiumImpact}%`);
                console.log(`      🌊 Влияние ORCA: ${result.details.orcaImpact}%`);
            });
            console.log('');
        });
        
        return bestStrategy;
    }

    /**
     * 📊 ДЕТАЛЬНЫЙ АНАЛИЗ ЛУЧШЕЙ СТРАТЕГИИ
     */
    analyzeOptimalStrategy(strategy) {
        console.log('\n📊 ДЕТАЛЬНЫЙ АНАЛИЗ ОПТИМАЛЬНОЙ СТРАТЕГИИ');
        console.log('=' .repeat(80));
        
        console.log(`🚀 ОПТИМАЛЬНАЯ СТРАТЕГИЯ:`);
        console.log(`   💰 Flash Loan: $${strategy.strategy.flashLoanAmount.toLocaleString()}`);
        console.log(`   🛒 Покупка RAYDIUM: $${strategy.strategy.buyAmount.toLocaleString()}`);
        console.log(`   💸 Продажа ORCA: $${strategy.strategy.sellAmount.toLocaleString()}`);
        console.log(`   📈 Повышение цены: ${strategy.strategy.priceIncrease}%`);
        
        console.log(`\n💸 ЗАТРАТЫ:`);
        console.log(`   🌊 RAYDIUM покупка: $${strategy.costs.raydiumCost.toLocaleString()}`);
        console.log(`   🏦 Flash Loan комиссия: $${strategy.costs.flashLoanFee.toLocaleString()}`);
        console.log(`   💰 Общие затраты: $${strategy.costs.totalCosts.toLocaleString()}`);
        
        console.log(`\n💰 ДОХОДЫ:`);
        console.log(`   🐋 ORCA продажа: $${strategy.revenue.orcaRevenue.toLocaleString()}`);
        console.log(`   📈 Бонус от роста цены: $${strategy.revenue.priceBonus.toLocaleString()}`);
        console.log(`   💎 Общий доход: $${strategy.revenue.totalRevenue.toLocaleString()}`);
        
        console.log(`\n🏆 ИТОГОВАЯ ПРИБЫЛЬ:`);
        console.log(`   💚 Чистая прибыль: $${strategy.profit.netProfit.toLocaleString()}`);
        console.log(`   📊 Процент прибыли: ${strategy.profit.profitPercent}%`);
        console.log(`   ⚡ ROI: ${(strategy.profit.netProfit / strategy.strategy.flashLoanAmount * 100).toFixed(2)}%`);
        
        console.log(`\n🎯 ВЛИЯНИЕ НА РЫНОК:`);
        console.log(`   🌊 RAYDIUM: ${strategy.details.raydiumImpact}% (поднимаем цену)`);
        console.log(`   🐋 ORCA: ${strategy.details.orcaImpact}% (практически незаметно)`);
        
        // Экономическая эффективность
        const dailyPotential = strategy.profit.netProfit * 10; // 10 сделок в день
        const monthlyPotential = dailyPotential * 30;
        
        console.log(`\n💎 ПОТЕНЦИАЛ МАСШТАБИРОВАНИЯ:`);
        console.log(`   📅 Дневной потенциал (10 сделок): $${dailyPotential.toLocaleString()}`);
        console.log(`   📆 Месячный потенциал: $${monthlyPotential.toLocaleString()}`);
        console.log(`   🚀 Годовой потенциал: $${(monthlyPotential * 12).toLocaleString()}`);
        
        return strategy;
    }
}

/**
 * 🎯 ГЛАВНАЯ ФУНКЦИЯ
 */
async function main() {
    console.log('🚀 ЗАПУСК КАЛЬКУЛЯТОРА FLASH LOAN АРБИТРАЖА...\n');
    
    const calculator = new FlashLoanArbitrageCalculator();
    
    try {
        // Поиск оптимальной стратегии
        const optimalStrategy = calculator.findOptimalStrategy();
        
        // Детальный анализ
        const analysis = calculator.analyzeOptimalStrategy(optimalStrategy);
        
        console.log('\n🎉 АНАЛИЗ ЗАВЕРШЕН!');
        console.log(`🏆 МАКСИМАЛЬНАЯ ПРИБЫЛЬ: $${analysis.profit.netProfit.toLocaleString()}`);
        console.log(`💰 ОПТИМАЛЬНАЯ ПОКУПКА RAYDIUM: $${analysis.strategy.buyAmount.toLocaleString()}`);
        console.log(`🚀 СТРАТЕГИЯ ГОТОВА К РЕАЛИЗАЦИИ!`);
        
        return analysis;
        
    } catch (error) {
        console.error('❌ Ошибка расчета:', error);
        return null;
    }
}

// Запуск если файл выполняется напрямую
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { FlashLoanArbitrageCalculator };
