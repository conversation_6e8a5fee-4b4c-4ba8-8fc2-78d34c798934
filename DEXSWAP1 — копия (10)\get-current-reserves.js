/**
 * 🔍 ПОЛУЧЕНИЕ АКТУАЛЬНЫХ АДРЕСОВ РЕЗЕРВОВ ЧЕРЕЗ METEORA SDK
 */

const { Connection, PublicKey } = require('@solana/web3.js');
const DLMM = require('@meteora-ag/dlmm').default;

async function getCurrentReserves() {
    console.log('🔍 ПОЛУЧЕНИЕ АКТУАЛЬНЫХ АДРЕСОВ РЕЗЕРВОВ ЧЕРЕЗ METEORA SDK...');
    
    const connection = new Connection('https://api.mainnet-beta.solana.com', 'processed');
    
    const pools = [
        {
            name: 'POOL_1',
            address: '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6'
        },
        {
            name: 'POOL_2', 
            address: 'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y'
        }
    ];
    
    const results = {};
    
    for (const pool of pools) {
        try {
            console.log(`\n📊 ${pool.name}: ${pool.address}`);
            
            // Создаем DLMM instance
            const dlmmPool = await DLMM.create(connection, new PublicKey(pool.address));
            
            // Получаем состояние пула
            const poolState = dlmmPool.lbPair;
            
            console.log(`   🔍 Pool State:`);
            console.log(`   Reserve X: ${poolState.reserveX.toString()}`);
            console.log(`   Reserve Y: ${poolState.reserveY.toString()}`);
            console.log(`   Token X Mint: ${poolState.tokenXMint.toString()}`);
            console.log(`   Token Y Mint: ${poolState.tokenYMint.toString()}`);
            console.log(`   Active Bin ID: ${poolState.activeId}`);
            console.log(`   Bin Step: ${poolState.binStep}`);
            
            results[pool.address] = {
                name: pool.name,
                reserveX: poolState.reserveX.toString(),
                reserveY: poolState.reserveY.toString(),
                tokenXMint: poolState.tokenXMint.toString(),
                tokenYMint: poolState.tokenYMint.toString(),
                activeId: poolState.activeId,
                binStep: poolState.binStep
            };
            
        } catch (error) {
            console.log(`   ❌ ОШИБКА для ${pool.name}: ${error.message}`);
        }
    }
    
    // Сохраняем результаты
    const fs = require('fs');
    fs.writeFileSync('current-reserves.json', JSON.stringify({
        timestamp: new Date().toISOString(),
        pools: results
    }, null, 2));
    
    console.log('\n💾 Результаты сохранены в current-reserves.json');
    
    // Сравниваем с нашими текущими адресами
    console.log('\n🔍 СРАВНЕНИЕ С НАШИМИ ТЕКУЩИМИ АДРЕСАМИ:');
    
    const ourReserves = {
        '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6': {
            reserveX: 'EYj9xKw6ZszwpyNibHY7JD5o3QgTVrSdcBp1fMJhrR9o',
            reserveY: 'CoaxzEh8p5YyGLcj36Eo3cUThVJxeKCs7qvLAGDYwBcz'
        },
        'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y': {
            reserveX: 'DwZz4S1Z1LBXomzmncQRVKCYhjCqSAMQ6RPKbUAadr7H',
            reserveY: '4N22J4vW2juHocTntJNmXywSonYjkndCwahjZ2cYLDgb'
        }
    };
    
    for (const [poolAddress, poolData] of Object.entries(results)) {
        const ourData = ourReserves[poolAddress];
        if (ourData) {
            console.log(`\n📊 ${poolData.name}:`);
            console.log(`   SDK Reserve X: ${poolData.reserveX}`);
            console.log(`   НАШ Reserve X: ${ourData.reserveX}`);
            console.log(`   ✅ Reserve X: ${poolData.reserveX === ourData.reserveX ? 'СОВПАДАЕТ' : '❌ НЕ СОВПАДАЕТ'}`);
            
            console.log(`   SDK Reserve Y: ${poolData.reserveY}`);
            console.log(`   НАШ Reserve Y: ${ourData.reserveY}`);
            console.log(`   ✅ Reserve Y: ${poolData.reserveY === ourData.reserveY ? 'СОВПАДАЕТ' : '❌ НЕ СОВПАДАЕТ'}`);
        }
    }
}

getCurrentReserves().catch(console.error);
