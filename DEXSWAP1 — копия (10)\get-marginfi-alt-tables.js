#!/usr/bin/env node

/**
 * 🔍 ПОЛУЧЕНИЕ ОФИЦИАЛЬНЫХ MARGINFI ALT ТАБЛИЦ
 * 
 * 🎯 ЦЕЛЬ: Найти официальные MarginFi Address Lookup Tables
 */

async function getMarginFiALTTables() {
    try {
        console.log('🔍 ПОИСК ОФИЦИАЛЬНЫХ MARGINFI ALT ТАБЛИЦ...\n');
        
        // Пробуем импортировать константы из MarginFi SDK
        try {
            const marginfiClient = require('@mrgnlabs/marginfi-client-v2');
            console.log('✅ MarginFi SDK импортирован');
            console.log('📋 Доступные экспорты:', Object.keys(marginfiClient));
            
            // Ищем ADDRESS_LOOKUP_TABLE_FOR_GROUP
            if (marginfiClient.ADDRESS_LOOKUP_TABLE_FOR_GROUP) {
                console.log('\n🎯 НАЙДЕНА ADDRESS_LOOKUP_TABLE_FOR_GROUP:');
                console.log(marginfiClient.ADDRESS_LOOKUP_TABLE_FOR_GROUP);
            } else {
                console.log('❌ ADDRESS_LOOKUP_TABLE_FOR_GROUP не найдена в основном экспорте');
            }
            
            // Пробуем найти в других местах
            if (marginfiClient.constants) {
                console.log('\n📋 Константы:', marginfiClient.constants);
            }
            
            if (marginfiClient.config) {
                console.log('\n📋 Конфиг:', marginfiClient.config);
            }
            
        } catch (error) {
            console.log('❌ Ошибка импорта MarginFi SDK:', error.message);
        }
        
        // Пробуем импортировать из mrgn-common
        try {
            const mrgnCommon = require('@mrgnlabs/mrgn-common');
            console.log('\n✅ mrgn-common импортирован');
            console.log('📋 Доступные экспорты:', Object.keys(mrgnCommon));
            
            if (mrgnCommon.ADDRESS_LOOKUP_TABLE_FOR_GROUP) {
                console.log('\n🎯 НАЙДЕНА ADDRESS_LOOKUP_TABLE_FOR_GROUP в mrgn-common:');
                console.log(mrgnCommon.ADDRESS_LOOKUP_TABLE_FOR_GROUP);
            }
            
        } catch (error) {
            console.log('❌ Ошибка импорта mrgn-common:', error.message);
        }
        
        // Пробуем найти через getConfig
        try {
            const { getConfig } = require('@mrgnlabs/marginfi-client-v2');
            const config = getConfig('production');
            console.log('\n📋 Production конфиг:', config);
            
            if (config.addressLookupTables) {
                console.log('\n🎯 НАЙДЕНЫ ALT ТАБЛИЦЫ В КОНФИГЕ:');
                console.log(config.addressLookupTables);
            }
            
        } catch (error) {
            console.log('❌ Ошибка получения конфига:', error.message);
        }
        
        // Известные MarginFi Group IDs для поиска ALT
        const knownGroups = [
            '4qp6Fx6tnZkY5Wropq9wUYgtFxXKwE6viZxFHg3rdAG8', // Основная группа
            'J9VZnaMGTELGCPsqMxk8aoyEGYcVzhorj48HvtDdEtc8'  // Альтернативная группа
        ];
        
        console.log('\n📋 ИЗВЕСТНЫЕ MARGINFI GROUPS:');
        knownGroups.forEach((group, index) => {
            console.log(`   ${index + 1}: ${group}`);
        });
        
        console.log('\n💡 РЕКОМЕНДАЦИИ:');
        console.log('1. Используйте официальный MarginFi SDK для получения ALT таблиц');
        console.log('2. Проверьте документацию на docs.marginfi.com');
        console.log('3. Используйте getConfig() для получения правильных ALT адресов');
        
    } catch (error) {
        console.error('❌ Ошибка:', error.message);
    }
}

getMarginFiALTTables();
