#!/usr/bin/env node

/**
 * 🔍 ПОЛУЧЕНИЕ РЕАЛЬНЫХ АДРЕСОВ РЕЗЕРВОВ ИЗ METEORA SDK
 */

const { Connection, PublicKey } = require('@solana/web3.js');
const DLMM = require('@meteora-ag/dlmm').default;

async function getRealReserves() {
    try {
        console.log('🔍 Получение реальных адресов резервов из Meteora SDK...');
        
        // Подключение к RPC
        const connection = new Connection('https://api.mainnet-beta.solana.com');
        
        // Наши пулы
        const pools = [
            '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6', // Pool 1
            'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y'  // Pool 2
        ];
        
        const realReserves = {};
        
        for (const poolAddress of pools) {
            console.log(`\n🔍 Анализируем пул: ${poolAddress}`);
            
            try {
                // Получаем данные пула через Meteora SDK
                const dlmmPool = await DLMM.create(connection, new PublicKey(poolAddress));
                
                console.log(`✅ DLMM Pool создан успешно`);
                console.log(`   lbPair: ${dlmmPool.pubkey.toString()}`);
                console.log(`   tokenX: ${dlmmPool.tokenX.publicKey.toString()}`);
                console.log(`   tokenY: ${dlmmPool.tokenY.publicKey.toString()}`);
                
                // Получаем данные lbPair
                if (dlmmPool.lbPair) {
                    console.log(`✅ lbPair данные найдены:`);
                    console.log(`   reserveX: ${dlmmPool.lbPair.reserveX ? dlmmPool.lbPair.reserveX.toString() : 'undefined'}`);
                    console.log(`   reserveY: ${dlmmPool.lbPair.reserveY ? dlmmPool.lbPair.reserveY.toString() : 'undefined'}`);
                    console.log(`   oracle: ${dlmmPool.lbPair.oracle ? dlmmPool.lbPair.oracle.toString() : 'undefined'}`);
                    
                    realReserves[poolAddress] = {
                        reserveX: dlmmPool.lbPair.reserveX ? dlmmPool.lbPair.reserveX.toString() : null,
                        reserveY: dlmmPool.lbPair.reserveY ? dlmmPool.lbPair.reserveY.toString() : null,
                        oracle: dlmmPool.lbPair.oracle ? dlmmPool.lbPair.oracle.toString() : null,
                        tokenX: dlmmPool.tokenX.publicKey.toString(),
                        tokenY: dlmmPool.tokenY.publicKey.toString()
                    };
                } else {
                    console.log(`❌ lbPair данные не найдены`);
                }
                
            } catch (error) {
                console.error(`❌ Ошибка для пула ${poolAddress}:`, error.message);
            }
        }
        
        console.log('\n🎯 РЕЗУЛЬТАТ - РЕАЛЬНЫЕ АДРЕСА РЕЗЕРВОВ:');
        console.log(JSON.stringify(realReserves, null, 2));
        
        // Сохраняем в файл
        const fs = require('fs');
        fs.writeFileSync('real-reserves.json', JSON.stringify(realReserves, null, 2));
        console.log('\n💾 Результат сохранен в real-reserves.json');
        
    } catch (error) {
        console.error('❌ Общая ошибка:', error.message);
    }
}

// Запускаем
getRealReserves();
