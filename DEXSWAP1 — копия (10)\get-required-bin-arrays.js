#!/usr/bin/env node

/**
 * 🔍 СКРИПТ ДЛЯ ПОЛУЧЕНИЯ ТОЧНЫХ BIN ARRAYS ДЛЯ ALT ТАБЛИЦ
 * 
 * Этот скрипт определяет, какие именно bin arrays нужны для:
 * 1. Добавления ликвидности (диапазон позиции)
 * 2. Swap операций
 * 
 * И выводит их адреса для добавления в ALT таблицы
 */

const { Connection, PublicKey } = require('@solana/web3.js');
const DLMM = require('@meteora-ag/dlmm').default;

// 🔧 КОНФИГУРАЦИЯ
const RPC_URL = 'https://api.mainnet-beta.solana.com';
const POOL_ADDRESS = 'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y'; // SOL/USDC пул

async function main() {
    try {
        console.log('🔍 ПОЛУЧЕНИЕ ТОЧНЫХ BIN ARRAYS ДЛЯ ALT ТАБЛИЦ...\n');
        
        // 1. Подключение к RPC
        const connection = new Connection(RPC_URL, 'confirmed');
        console.log('✅ Подключение к RPC установлено');
        
        // 2. Создание DLMM инстанса
        const dlmmPool = await DLMM.create(connection, new PublicKey(POOL_ADDRESS));
        console.log(`✅ DLMM пул загружен: ${POOL_ADDRESS}`);
        
        // 3. Получение активного bin
        const activeBin = await dlmmPool.getActiveBin();
        console.log(`✅ Активный bin ID: ${activeBin.binId}`);
        console.log(`✅ Активная цена: $${activeBin.price}`);
        
        // 4. Расчет диапазона позиции (как в коде)
        const TOTAL_RANGE_INTERVAL = 10; // 10 bins на каждую сторону
        const minBinId = activeBin.binId - TOTAL_RANGE_INTERVAL;
        const maxBinId = activeBin.binId + TOTAL_RANGE_INTERVAL;
        
        console.log(`\n🔧 ДИАПАЗОН ПОЗИЦИИ:`);
        console.log(`   minBinId: ${minBinId}`);
        console.log(`   maxBinId: ${maxBinId}`);
        console.log(`   activeBinId: ${activeBin.binId}`);
        
        // 5. Получение bin arrays для позиции
        console.log(`\n🔥 ПОЛУЧЕНИЕ BIN ARRAYS ДЛЯ ПОЗИЦИИ...`);
        const allBinArrays = await dlmmPool.getBinArrays();
        console.log(`📋 Всего bin arrays в пуле: ${allBinArrays.length}`);
        
        const positionBinArrays = [];
        for (const binArray of allBinArrays) {
            const binArrayIndex = binArray.account.index;
            const binArrayStartId = binArrayIndex * 70;
            const binArrayEndId = binArrayStartId + 69;
            
            if (binArrayEndId >= minBinId && binArrayStartId <= maxBinId) {
                positionBinArrays.push(binArray);
                console.log(`✅ Bin array ${binArrayIndex}: покрывает bins ${binArrayStartId}-${binArrayEndId}`);
                console.log(`   Адрес: ${binArray.publicKey.toString()}`);
            }
        }
        
        // 6. Получение bin arrays для swap
        console.log(`\n🔥 ПОЛУЧЕНИЕ BIN ARRAYS ДЛЯ SWAP...`);
        const swapBinArrays = await dlmmPool.getBinArrayForSwap(true); // USDC → SOL
        console.log(`✅ Получено ${swapBinArrays.length} bin arrays для swap`);
        
        swapBinArrays.forEach((binArray, index) => {
            console.log(`   ${index + 1}. ${binArray.publicKey.toString()}`);
        });
        
        // 7. Объединение и дедупликация
        console.log(`\n🔥 ОБЪЕДИНЕНИЕ И ДЕДУПЛИКАЦИЯ...`);
        const allRequiredBinArrays = [...positionBinArrays, ...swapBinArrays];
        const uniqueBinArrays = Array.from(
            new Map(allRequiredBinArrays.map(ba => [ba.publicKey.toString(), ba])).values()
        );
        
        console.log(`✅ Всего уникальных bin arrays: ${uniqueBinArrays.length}`);
        
        // 8. Вывод финального списка
        console.log(`\n🎯 ФИНАЛЬНЫЙ СПИСОК BIN ARRAYS ДЛЯ ALT ТАБЛИЦ:`);
        console.log(`================================================`);
        
        uniqueBinArrays.forEach((binArray, index) => {
            console.log(`${index + 1}. ${binArray.publicKey.toString()}`);
        });
        
        console.log(`\n💡 КОМАНДА ДЛЯ ДОБАВЛЕНИЯ В ALT:`);
        console.log(`node add-meteora-to-alt.js`);
        console.log(`\n✅ Эти ${uniqueBinArrays.length} адресов нужно добавить в ALT таблицы`);
        
        // 9. Проверка текущего состояния ALT
        console.log(`\n🔍 ПРОВЕРКА ТЕКУЩЕГО СОСТОЯНИЯ ALT...`);
        try {
            const fs = require('fs');
            const altCache = JSON.parse(fs.readFileSync('correct-alt-tables-cache.json', 'utf8'));
            
            let foundInALT = 0;
            let notFoundInALT = [];
            
            uniqueBinArrays.forEach(binArray => {
                const address = binArray.publicKey.toString();
                let found = false;
                
                altCache.altTables.forEach(alt => {
                    if (alt.addresses.includes(address)) {
                        found = true;
                    }
                });
                
                if (found) {
                    foundInALT++;
                } else {
                    notFoundInALT.push(address);
                }
            });
            
            console.log(`✅ Найдено в ALT: ${foundInALT}/${uniqueBinArrays.length}`);
            console.log(`❌ НЕ найдено в ALT: ${notFoundInALT.length}`);
            
            if (notFoundInALT.length > 0) {
                console.log(`\n🚨 НУЖНО ДОБАВИТЬ В ALT:`);
                notFoundInALT.forEach((address, index) => {
                    console.log(`   ${index + 1}. ${address}`);
                });
            } else {
                console.log(`\n🎉 ВСЕ BIN ARRAYS УЖЕ ЕСТЬ В ALT ТАБЛИЦАХ!`);
                console.log(`✅ Можно запускать транзакцию`);
            }
            
        } catch (error) {
            console.log(`⚠️ Не удалось проверить ALT кэш: ${error.message}`);
        }
        
    } catch (error) {
        console.error(`❌ Ошибка: ${error.message}`);
        process.exit(1);
    }
}

main();
