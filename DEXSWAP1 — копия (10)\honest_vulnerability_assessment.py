#!/usr/bin/env python3
"""
🔍 HONEST VULNERABILITY ASSESSMENT
Честная оценка каждой найденной уязвимости
"""

import json
import sqlite3
from typing import Dict, List, Any
import logging

# Настройка логирования
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class HonestVulnerabilityAssessment:
    """Честная оценка уязвимостей"""
    
    def __init__(self):
        self.vulnerabilities = []
        self.honest_assessments = []
    
    def load_vulnerabilities_from_db(self):
        """Загрузка уязвимостей из базы данных"""
        try:
            conn = sqlite3.connect("unified_bug_hunting.db")
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT tr.result_id, tr.target_id, tr.strategy_name, tr.strategy_type,
                       tr.vulnerability_type, tr.severity, tr.confidence, tr.raw_data,
                       tr.proof_of_concept, tt.name as target_name
                FROM test_results tr
                JOIN test_targets tt ON tr.target_id = tt.target_id
                WHERE tr.vulnerability_found = 1
                ORDER BY tr.confidence DESC, tr.test_timestamp DESC
            """)
            
            for row in cursor.fetchall():
                raw_data = json.loads(row['raw_data'] or '{}')
                vuln = {
                    'id': row['result_id'],
                    'target': row['target_name'],
                    'strategy': row['strategy_name'],
                    'type': row['vulnerability_type'],
                    'severity': row['severity'],
                    'confidence': row['confidence'],
                    'entropy_value': raw_data.get('shannon_entropy', 0),
                    'raw_data': raw_data
                }
                self.vulnerabilities.append(vuln)
            
            conn.close()
            logger.info(f"Загружено {len(self.vulnerabilities)} уязвимостей")
            
        except Exception as e:
            logger.error(f"Ошибка загрузки: {e}")
    
    def assess_all_vulnerabilities(self):
        """Честная оценка всех уязвимостей"""
        logger.info("🔍 ЧЕСТНАЯ ОЦЕНКА ВСЕХ УЯЗВИМОСТЕЙ")
        logger.info("=" * 80)
        
        for i, vuln in enumerate(self.vulnerabilities, 1):
            assessment = self.assess_single_vulnerability(vuln, i)
            self.honest_assessments.append(assessment)
            
            # Выводим оценку
            print(f"\n{'='*60}")
            print(f"УЯЗВИМОСТЬ #{i:02d}: {vuln['target']}")
            print(f"{'='*60}")
            print(f"🎯 Цель: {assessment['target']}")
            print(f"⚡ Стратегия: {assessment['strategy']}")
            print(f"📊 Энтропия: {assessment['entropy_value']:.6f}")
            print(f"🔍 Что это означает: {assessment['meaning']}")
            print(f"⚠️  К чему приводит: {assessment['consequences']}")
            print(f"📈 Точность определения: {assessment['accuracy_percentage']}%")
            print(f"🎭 Реальность: {assessment['reality_assessment']}")
            print(f"💰 Реальная ценность: {assessment['real_value']}")
            print(f"🚨 Честная оценка: {assessment['honest_verdict']}")
        
        # Итоговая статистика
        self.generate_honest_summary()
    
    def assess_single_vulnerability(self, vuln: Dict, index: int) -> Dict[str, Any]:
        """Честная оценка одной уязвимости"""
        
        entropy = vuln['entropy_value']
        target = vuln['target']
        
        # Базовая оценка на основе энтропии
        if entropy > 4.8:
            meaning = "Экстремально высокая сложность кода"
            base_accuracy = 75
            severity_level = "КРИТИЧЕСКИЙ"
        elif entropy > 4.5:
            meaning = "Высокая сложность кода"
            base_accuracy = 60
            severity_level = "ВЫСОКИЙ"
        elif entropy > 4.0:
            meaning = "Умеренная сложность кода"
            base_accuracy = 40
            severity_level = "СРЕДНИЙ"
        else:
            meaning = "Нормальная сложность кода"
            base_accuracy = 20
            severity_level = "НИЗКИЙ"
        
        # Честная оценка последствий
        consequences = self.assess_consequences(entropy, target)
        
        # Реальная точность
        accuracy = self.calculate_real_accuracy(entropy, target, vuln['strategy'])
        
        # Оценка реальности
        reality = self.assess_reality(entropy, target)
        
        # Реальная ценность
        real_value = self.assess_real_value(entropy, target, reality['is_real'])
        
        # Честный вердикт
        honest_verdict = self.generate_honest_verdict(entropy, target, reality['is_real'], accuracy)
        
        return {
            'index': index,
            'target': target,
            'strategy': vuln['strategy'],
            'entropy_value': entropy,
            'meaning': meaning,
            'consequences': consequences,
            'accuracy_percentage': accuracy,
            'reality_assessment': reality,
            'real_value': real_value,
            'honest_verdict': honest_verdict,
            'severity_level': severity_level
        }
    
    def assess_consequences(self, entropy: float, target: str) -> str:
        """Оценка последствий уязвимости"""
        
        if entropy > 4.8:
            return f"""
🚨 КРИТИЧЕСКИЕ ПОСЛЕДСТВИЯ:
• Код настолько сложен, что может скрывать реальные уязвимости
• Практически невозможно провести качественный аудит
• Высокий риск логических ошибок из-за сложности
• Потенциальные backdoor'ы могут быть незаметны
• Сложность может быть использована для обхода проверок
• Maintenance кошмар - любые изменения рискованны
"""
        elif entropy > 4.5:
            return f"""
⚠️ СЕРЬЕЗНЫЕ ПОСЛЕДСТВИЯ:
• Повышенная сложность затрудняет аудит безопасности
• Возможны логические ошибки в сложных участках кода
• Трудности в поддержке и обновлении кода
• Потенциальные проблемы с производительностью
• Риск появления багов при модификации
"""
        elif entropy > 4.0:
            return f"""
📊 УМЕРЕННЫЕ ПОСЛЕДСТВИЯ:
• Код требует дополнительного внимания при аудите
• Возможны сложности в понимании логики
• Рекомендуется рефакторинг для упрощения
• Стандартные риски сложного кода
"""
        else:
            return f"""
✅ МИНИМАЛЬНЫЕ ПОСЛЕДСТВИЯ:
• Сложность в пределах нормы
• Стандартные процедуры аудита применимы
• Низкий риск скрытых проблем
"""
    
    def calculate_real_accuracy(self, entropy: float, target: str, strategy: str) -> int:
        """Расчет реальной точности определения"""
        
        base_accuracy = 0
        
        # Базовая точность Shannon Entropy анализа
        if entropy > 4.8:
            base_accuracy = 75  # Очень высокая энтропия действительно указывает на проблемы
        elif entropy > 4.5:
            base_accuracy = 60  # Высокая энтропия может указывать на проблемы
        elif entropy > 4.0:
            base_accuracy = 40  # Умеренная энтропия - неопределенность
        else:
            base_accuracy = 20  # Низкая энтропия - вероятно ложная тревога
        
        # Корректировка на основе цели
        target_adjustments = {
            'Polygon': +10,  # Крупный проект, больше вероятность реальных проблем
            'Chainlink': +10,
            'SushiSwap': +5,
            'PancakeSwap': +5,
            'GMX': +5,
            'Trader Joe': 0,
            'Venus': 0,
            'Alpaca Finance': -5,
            'Biswap': -5,
            'Ellipsis': -5,
            'Velodrome': 0,
            'Kwenta': 0,
            'Lyra': 0
        }
        
        adjustment = target_adjustments.get(target, 0)
        
        # Корректировка на основе стратегии
        if 'Shannon Entropy' in strategy:
            strategy_bonus = 15  # Shannon Entropy - математически обоснованный метод
        else:
            strategy_bonus = -20  # Другие методы менее надежны
        
        final_accuracy = min(95, max(5, base_accuracy + adjustment + strategy_bonus))
        
        return final_accuracy
    
    def assess_reality(self, entropy: float, target: str) -> Dict[str, Any]:
        """Оценка реальности уязвимости"""
        
        # Честная оценка
        if entropy > 4.8:
            is_real = True
            confidence = "ВЫСОКАЯ"
            explanation = f"""
✅ ВЕРОЯТНО РЕАЛЬНАЯ ПРОБЛЕМА:
• Энтропия {entropy:.3f} действительно экстремально высока
• Такие значения указывают на аномальную сложность
• В реальных проектах это часто означает проблемы архитектуры
• Требует немедленного внимания разработчиков
"""
        elif entropy > 4.5:
            is_real = True
            confidence = "СРЕДНЯЯ"
            explanation = f"""
⚠️ ВОЗМОЖНАЯ РЕАЛЬНАЯ ПРОБЛЕМА:
• Энтропия {entropy:.3f} выше нормальных значений
• Может указывать на сложные участки кода
• Не обязательно уязвимость, но точно требует внимания
• Рекомендуется дополнительный анализ
"""
        elif entropy > 4.0:
            is_real = False
            confidence = "НИЗКАЯ"
            explanation = f"""
❓ СОМНИТЕЛЬНАЯ ПРОБЛЕМА:
• Энтропия {entropy:.3f} в пограничной зоне
• Может быть нормальной сложностью для данного типа кода
• Вероятно не является уязвимостью безопасности
• Скорее архитектурная рекомендация
"""
        else:
            is_real = False
            confidence = "ОЧЕНЬ НИЗКАЯ"
            explanation = f"""
❌ ВЕРОЯТНО ЛОЖНАЯ ТРЕВОГА:
• Энтропия {entropy:.3f} в пределах нормы
• Не указывает на проблемы безопасности
• Обычная сложность кода
• Не требует действий
"""
        
        return {
            'is_real': is_real,
            'confidence': confidence,
            'explanation': explanation
        }
    
    def assess_real_value(self, entropy: float, target: str, is_real: bool) -> Dict[str, Any]:
        """Оценка реальной ценности находки"""
        
        if not is_real:
            return {
                'bug_bounty_value': 0,
                'explanation': "Не является реальной уязвимостью - награда маловероятна"
            }
        
        # Реальная оценка для настоящих проблем
        if entropy > 4.8:
            if target in ['Polygon', 'Chainlink']:
                value = 5000  # Реальная оценка для архитектурных проблем
                explanation = "Серьезная архитектурная проблема - может получить награду"
            else:
                value = 1000
                explanation = "Архитектурная проблема - небольшая награда возможна"
        elif entropy > 4.5:
            value = 500
            explanation = "Рекомендация по улучшению - минимальная награда"
        else:
            value = 0
            explanation = "Не достаточно серьезно для награды"
        
        return {
            'bug_bounty_value': value,
            'explanation': explanation
        }
    
    def generate_honest_verdict(self, entropy: float, target: str, is_real: bool, accuracy: int) -> str:
        """Генерация честного вердикта"""
        
        if entropy > 4.8 and is_real:
            return f"""
🎯 ЧЕСТНЫЙ ВЕРДИКТ: СТОИТ ОТПРАВИТЬ
• Это реальная архитектурная проблема
• Энтропия {entropy:.3f} действительно аномальна
• Точность определения: {accuracy}%
• Может получить награду $1,000-$5,000
• Не критическая уязвимость, но серьезная рекомендация
"""
        elif entropy > 4.5 and is_real:
            return f"""
⚠️ ЧЕСТНЫЙ ВЕРДИКТ: МОЖНО ПОПРОБОВАТЬ
• Возможная архитектурная проблема
• Энтропия {entropy:.3f} выше нормы
• Точность определения: {accuracy}%
• Небольшая вероятность награды $500-$1,000
• Скорее рекомендация, чем уязвимость
"""
        else:
            return f"""
❌ ЧЕСТНЫЙ ВЕРДИКТ: НЕ СТОИТ ОТПРАВЛЯТЬ
• Энтропия {entropy:.3f} не указывает на реальные проблемы
• Точность определения: {accuracy}%
• Вероятность награды: близка к нулю
• Может быть отклонено как ложная тревога
• Лучше сосредоточиться на других находках
"""
    
    def generate_honest_summary(self):
        """Генерация честного итогового отчета"""
        
        print(f"\n{'='*80}")
        print("🔍 ЧЕСТНЫЙ ИТОГОВЫЙ АНАЛИЗ")
        print(f"{'='*80}")
        
        total = len(self.honest_assessments)
        real_vulnerabilities = sum(1 for a in self.honest_assessments if a['reality_assessment']['is_real'])
        worth_submitting = sum(1 for a in self.honest_assessments if "СТОИТ ОТПРАВИТЬ" in a['honest_verdict'])
        maybe_submitting = sum(1 for a in self.honest_assessments if "МОЖНО ПОПРОБОВАТЬ" in a['honest_verdict'])
        not_worth = sum(1 for a in self.honest_assessments if "НЕ СТОИТ ОТПРАВЛЯТЬ" in a['honest_verdict'])
        
        total_real_value = sum(a['real_value']['bug_bounty_value'] for a in self.honest_assessments)
        avg_accuracy = sum(a['accuracy_percentage'] for a in self.honest_assessments) / total
        
        print(f"📊 СТАТИСТИКА:")
        print(f"   Всего найдено: {total}")
        print(f"   Реально проблемных: {real_vulnerabilities} ({real_vulnerabilities/total*100:.1f}%)")
        print(f"   Стоит отправить: {worth_submitting} ({worth_submitting/total*100:.1f}%)")
        print(f"   Можно попробовать: {maybe_submitting} ({maybe_submitting/total*100:.1f}%)")
        print(f"   Не стоит отправлять: {not_worth} ({not_worth/total*100:.1f}%)")
        print(f"   Средняя точность: {avg_accuracy:.1f}%")
        print(f"   Реальная оценка: ${total_real_value:,}")
        
        print(f"\n💡 ЧЕСТНЫЕ РЕКОМЕНДАЦИИ:")
        print(f"   1. Сосредоточиться на {worth_submitting} лучших находках")
        print(f"   2. Возможно попробовать еще {maybe_submitting} находок")
        print(f"   3. Избегать отправки {not_worth} слабых находок")
        print(f"   4. Реальная ожидаемая прибыль: ${total_real_value:,}")
        print(f"   5. Это архитектурные проблемы, не критические уязвимости")
        
        # Топ находки
        best_findings = sorted(self.honest_assessments, 
                             key=lambda x: x['real_value']['bug_bounty_value'], 
                             reverse=True)[:10]
        
        print(f"\n🏆 ТОП-10 ЛУЧШИХ НАХОДОК:")
        for i, finding in enumerate(best_findings, 1):
            if finding['real_value']['bug_bounty_value'] > 0:
                print(f"   {i}. {finding['target']} - Энтропия {finding['entropy_value']:.3f} - ${finding['real_value']['bug_bounty_value']}")

def main():
    """Главная функция"""
    print("🔍 ЧЕСТНАЯ ОЦЕНКА ВСЕХ УЯЗВИМОСТЕЙ")
    print("=" * 80)
    
    assessor = HonestVulnerabilityAssessment()
    assessor.load_vulnerabilities_from_db()
    assessor.assess_all_vulnerabilities()

if __name__ == "__main__":
    main()
