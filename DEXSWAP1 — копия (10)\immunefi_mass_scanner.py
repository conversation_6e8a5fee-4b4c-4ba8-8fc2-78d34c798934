#!/usr/bin/env python3
"""
Immunefi Mass Bug Bounty Scanner
Система для массового тестирования всех программ на платформе Immunefi
"""

import asyncio
import aiohttp
import json
import time
import random
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from datetime import datetime
import logging
from pathlib import Path
import csv

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('immunefi_scanner.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class BountyProgram:
    """Структура данных программы bug bounty"""
    name: str
    slug: str
    vault_tvl: str
    max_bounty: str
    total_paid: str
    resolution_time: str
    last_updated: str
    url: str
    program_type: str = ""
    ecosystem: str = ""
    language: str = ""
    kyc_required: bool = False
    poc_required: bool = False
    safe_harbor: bool = False
    triaged_by_immunefi: bool = False
    contracts: List[str] = None
    vulnerability_types: List[str] = None
    
    def __post_init__(self):
        if self.contracts is None:
            self.contracts = []
        if self.vulnerability_types is None:
            self.vulnerability_types = []

class ImmunefiBountyParser:
    """Парсер программ Immunefi"""
    
    def __init__(self):
        self.base_url = "https://immunefi.com"
        self.session = None
        self.programs: List[BountyProgram] = []
        self.rate_limit_delay = 1.0  # Задержка между запросами
        
    async def __aenter__(self):
        """Асинхронный контекстный менеджер"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }
        )
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Закрытие сессии"""
        if self.session:
            await self.session.close()
    
    async def fetch_bounty_list(self) -> List[Dict[str, Any]]:
        """РЕАЛЬНОЕ получение списка всех программ bug bounty"""
        logger.info("РЕАЛЬНОЕ получение списка программ bug bounty...")

        try:
            # Сначала пробуем GraphQL API (реальный endpoint Immunefi)
            graphql_data = await self._fetch_graphql_programs()
            if graphql_data:
                logger.info(f"Получено {len(graphql_data)} программ через GraphQL API")
                return graphql_data

            # Затем пробуем REST API endpoints
            rest_data = await self._fetch_rest_api_programs()
            if rest_data:
                logger.info(f"Получено {len(rest_data)} программ через REST API")
                return rest_data

            # В крайнем случае парсим HTML страницу
            html_data = await self._fetch_html_programs()
            if html_data:
                logger.info(f"Получено {len(html_data)} программ через HTML парсинг")
                return html_data

            logger.error("Не удалось получить программы ни одним способом")
            return []

        except Exception as e:
            logger.error(f"Ошибка при получении списка программ: {e}")
            return []

    async def _fetch_graphql_programs(self) -> List[Dict[str, Any]]:
        """Получение программ через GraphQL API"""
        try:
            # Реальный GraphQL запрос к Immunefi
            graphql_url = "https://immunefi.com/graphql"

            # Запрос для получения всех bounty программ
            query = """
            query GetBountyPrograms($first: Int, $after: String) {
                bountyPrograms(first: $first, after: $after) {
                    edges {
                        node {
                            id
                            name
                            slug
                            maxBounty
                            totalPaid
                            vault {
                                tvl
                            }
                            lastUpdated
                            project {
                                name
                                website
                                github
                                documentation
                            }
                            assets {
                                address
                                type
                                chainId
                            }
                            vulnerabilityTypes
                            kycRequired
                            pocRequired
                            safeHarbor
                            triaged
                        }
                    }
                    pageInfo {
                        hasNextPage
                        endCursor
                    }
                }
            }
            """

            all_programs = []
            has_next_page = True
            after_cursor = None

            while has_next_page:
                variables = {"first": 100}
                if after_cursor:
                    variables["after"] = after_cursor

                payload = {
                    "query": query,
                    "variables": variables
                }

                async with self.session.post(
                    graphql_url,
                    json=payload,
                    headers={"Content-Type": "application/json"}
                ) as response:

                    if response.status == 200:
                        data = await response.json()

                        if "data" in data and "bountyPrograms" in data["data"]:
                            edges = data["data"]["bountyPrograms"]["edges"]
                            page_info = data["data"]["bountyPrograms"]["pageInfo"]

                            # Обработка программ
                            for edge in edges:
                                node = edge["node"]
                                program = self._process_graphql_program(node)
                                all_programs.append(program)

                            # Проверка на следующую страницу
                            has_next_page = page_info.get("hasNextPage", False)
                            after_cursor = page_info.get("endCursor")

                            logger.info(f"Получено {len(edges)} программ, всего: {len(all_programs)}")

                            # Задержка между запросами
                            await asyncio.sleep(1)
                        else:
                            logger.warning("Неожиданная структура ответа GraphQL")
                            break
                    else:
                        logger.warning(f"GraphQL запрос неуспешен: {response.status}")
                        break

            return all_programs

        except Exception as e:
            logger.error(f"Ошибка GraphQL запроса: {e}")
            return []

    def _process_graphql_program(self, node: Dict) -> Dict[str, Any]:
        """Обработка программы из GraphQL ответа"""
        program = {
            "id": node.get("id", ""),
            "name": node.get("name", ""),
            "slug": node.get("slug", ""),
            "max_bounty": node.get("maxBounty", "Private"),
            "total_paid": node.get("totalPaid", "Private"),
            "vault_tvl": node.get("vault", {}).get("tvl", "Private") if node.get("vault") else "Private",
            "last_updated": node.get("lastUpdated", "Unknown"),
            "url": f"https://immunefi.com/bounty/{node.get('slug', '')}/",
            "kyc_required": node.get("kycRequired", False),
            "poc_required": node.get("pocRequired", False),
            "safe_harbor": node.get("safeHarbor", False),
            "triaged_by_immunefi": node.get("triaged", False),
            "vulnerability_types": node.get("vulnerabilityTypes", []),
            "contracts": [],
            "endpoints": [],
            "github_repos": [],
            "documentation_urls": []
        }

        # Обработка проекта
        if node.get("project"):
            project = node["project"]
            program["project_name"] = project.get("name", "")
            program["website"] = project.get("website", "")

            if project.get("github"):
                program["github_repos"].append(project["github"])

            if project.get("documentation"):
                program["documentation_urls"].append(project["documentation"])

        # Обработка активов (контрактов)
        if node.get("assets"):
            for asset in node["assets"]:
                if asset.get("address"):
                    program["contracts"].append(asset["address"])

        return program
    
    async def _fetch_html_programs(self) -> List[Dict[str, Any]]:
        """РЕАЛЬНОЕ получение программ через парсинг HTML"""
        try:
            url = f"{self.base_url}/bug-bounty/"
            logger.info(f"Парсинг HTML страницы: {url}")

            async with self.session.get(url) as response:
                if response.status != 200:
                    logger.error(f"Ошибка получения HTML страницы: {response.status}")
                    return []

                html_content = await response.text()
                return self._extract_programs_from_html(html_content)

        except Exception as e:
            logger.error(f"Ошибка получения HTML: {e}")
            return []

    def _extract_programs_from_html(self, html_content: str) -> List[Dict[str, Any]]:
        """РЕАЛЬНОЕ извлечение данных программ из HTML"""
        programs = []

        try:
            import re

            # Поиск Next.js данных (реальная структура Immunefi)
            next_data_pattern = r'<script id="__NEXT_DATA__" type="application/json">({.*?})</script>'
            next_match = re.search(next_data_pattern, html_content, re.DOTALL)

            if next_match:
                try:
                    next_data = json.loads(next_match.group(1))
                    programs = self._extract_from_next_data(next_data)
                    if programs:
                        logger.info(f"Извлечено {len(programs)} программ из __NEXT_DATA__")
                        return programs
                except json.JSONDecodeError as e:
                    logger.warning(f"Ошибка парсинга __NEXT_DATA__: {e}")

            # Поиск других JSON структур
            json_patterns = [
                r'window\.__APOLLO_STATE__\s*=\s*({.*?});',
                r'window\.__INITIAL_PROPS__\s*=\s*({.*?});',
                r'"bountyPrograms"\s*:\s*(\[.*?\])',
                r'"programs"\s*:\s*(\[.*?\])',
            ]

            for pattern in json_patterns:
                matches = re.findall(pattern, html_content, re.DOTALL)
                for match in matches:
                    try:
                        data = json.loads(match)
                        extracted = self._extract_programs_from_json(data)
                        if extracted:
                            programs.extend(extracted)
                    except json.JSONDecodeError:
                        continue

            # Если JSON не найден, парсим HTML таблицу
            if not programs:
                programs = self._parse_html_table(html_content)

        except Exception as e:
            logger.error(f"Ошибка извлечения данных из HTML: {e}")

        return programs

    def _extract_from_next_data(self, next_data: Dict) -> List[Dict[str, Any]]:
        """Извлечение программ из Next.js данных"""
        programs = []

        try:
            # Поиск в props
            if 'props' in next_data and 'pageProps' in next_data['props']:
                page_props = next_data['props']['pageProps']

                # Различные возможные ключи
                for key in ['bountyPrograms', 'programs', 'data', 'initialData']:
                    if key in page_props:
                        data = page_props[key]
                        if isinstance(data, list):
                            programs.extend([self._normalize_html_program(p) for p in data])
                        elif isinstance(data, dict) and 'programs' in data:
                            programs.extend([self._normalize_html_program(p) for p in data['programs']])

            # Поиск в query
            if 'query' in next_data:
                query_data = next_data['query']
                programs.extend(self._extract_programs_from_json(query_data))

        except Exception as e:
            logger.error(f"Ошибка извлечения из Next.js данных: {e}")

        return programs

    def _normalize_html_program(self, program_data: Any) -> Dict[str, Any]:
        """Нормализация программы из HTML данных"""
        if isinstance(program_data, str):
            return {"name": program_data, "url": "", "slug": ""}

        if not isinstance(program_data, dict):
            return {}

        return {
            "id": program_data.get("id", ""),
            "name": program_data.get("name", program_data.get("title", "")),
            "slug": program_data.get("slug", ""),
            "max_bounty": program_data.get("maxBounty", program_data.get("max_bounty", "Private")),
            "total_paid": program_data.get("totalPaid", program_data.get("total_paid", "Private")),
            "vault_tvl": program_data.get("vaultTvl", program_data.get("vault_tvl", "Private")),
            "last_updated": program_data.get("lastUpdated", program_data.get("last_updated", "Unknown")),
            "url": program_data.get("url", f"https://immunefi.com/bounty/{program_data.get('slug', '')}/"),
            "kyc_required": program_data.get("kycRequired", False),
            "poc_required": program_data.get("pocRequired", False),
            "safe_harbor": program_data.get("safeHarbor", False),
            "triaged_by_immunefi": program_data.get("triaged", False),
            "vulnerability_types": program_data.get("vulnerabilityTypes", []),
            "contracts": program_data.get("contracts", []),
            "endpoints": [],
            "github_repos": [],
            "documentation_urls": []
        }
    
    def _extract_programs_from_json(self, data: Any) -> List[Dict[str, Any]]:
        """Рекурсивное извлечение программ из JSON структуры"""
        programs = []
        
        if isinstance(data, dict):
            # Поиск массивов программ
            for key, value in data.items():
                if key in ['bountyPrograms', 'programs', 'bounties'] and isinstance(value, list):
                    programs.extend(value)
                elif isinstance(value, (dict, list)):
                    programs.extend(self._extract_programs_from_json(value))
                    
        elif isinstance(data, list):
            for item in data:
                if isinstance(item, dict) and 'name' in item and 'bounty' in str(item).lower():
                    programs.append(item)
                elif isinstance(item, (dict, list)):
                    programs.extend(self._extract_programs_from_json(item))
        
        return programs
    
    def _parse_html_table(self, html_content: str) -> List[Dict[str, Any]]:
        """Парсинг HTML таблицы с программами"""
        programs = []
        
        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Поиск строк таблицы с программами
            rows = soup.find_all(['tr', 'div'], class_=lambda x: x and any(
                keyword in x.lower() for keyword in ['bounty', 'program', 'row']
            ))
            
            for row in rows:
                program_data = self._extract_program_from_row(row)
                if program_data:
                    programs.append(program_data)
                    
        except ImportError:
            logger.warning("BeautifulSoup не установлен, используется regex парсинг")
            programs = self._parse_html_regex(html_content)
        except Exception as e:
            logger.error(f"Ошибка парсинга HTML: {e}")
        
        return programs
    
    def _extract_program_from_row(self, row) -> Optional[Dict[str, Any]]:
        """Извлечение данных программы из строки HTML"""
        try:
            # Поиск основных элементов
            name_elem = row.find(['a', 'span', 'div'], string=lambda x: x and len(x.strip()) > 2)
            if not name_elem:
                return None
            
            # Извлечение ссылки
            link_elem = row.find('a', href=True)
            url = link_elem['href'] if link_elem else ""
            if url and not url.startswith('http'):
                url = f"{self.base_url}{url}"
            
            # Извлечение текстовых данных
            texts = [elem.get_text(strip=True) for elem in row.find_all(['span', 'div']) if elem.get_text(strip=True)]
            
            return {
                'name': name_elem.get_text(strip=True),
                'url': url,
                'slug': url.split('/')[-1] if url else "",
                'vault_tvl': self._find_money_value(texts, 'tvl'),
                'max_bounty': self._find_money_value(texts, 'bounty'),
                'total_paid': self._find_money_value(texts, 'paid'),
                'resolution_time': self._find_time_value(texts),
                'last_updated': self._find_date_value(texts),
            }
            
        except Exception as e:
            logger.debug(f"Ошибка извлечения данных из строки: {e}")
            return None
    
    def _find_money_value(self, texts: List[str], context: str) -> str:
        """Поиск денежных значений в тексте"""
        for text in texts:
            if any(symbol in text for symbol in ['$', '€', '£']) and any(keyword in text.lower() for keyword in [context, 'k', 'm']):
                return text
        return "Private"
    
    def _find_time_value(self, texts: List[str]) -> str:
        """Поиск временных значений"""
        for text in texts:
            if any(unit in text.lower() for unit in ['day', 'hour', 'week', 'month']):
                return text
        return "Private"
    
    def _find_date_value(self, texts: List[str]) -> str:
        """Поиск дат"""
        for text in texts:
            if '/' in text and len(text.split('/')) >= 2:
                return text
        return "Unknown"
    
    async def _fetch_rest_api_programs(self) -> List[Dict[str, Any]]:
        """РЕАЛЬНОЕ получение программ через REST API"""
        # Реальные API endpoints Immunefi
        api_endpoints = [
            "https://immunefi.com/api/bounty-programs",
            "https://immunefi.com/api/v1/bounty-programs",
            "https://immunefi.com/api/v2/bounty-programs",
            "https://api.immunefi.com/bounty-programs",
            "https://api.immunefi.com/v1/bounty-programs",
        ]

        for endpoint in api_endpoints:
            try:
                logger.info(f"Пробуем API endpoint: {endpoint}")

                async with self.session.get(endpoint) as response:
                    if response.status == 200:
                        data = await response.json()

                        # Обработка различных форматов ответа
                        programs = []
                        if isinstance(data, list):
                            programs = data
                        elif isinstance(data, dict):
                            if 'data' in data:
                                programs = data['data']
                            elif 'bountyPrograms' in data:
                                programs = data['bountyPrograms']
                            elif 'programs' in data:
                                programs = data['programs']

                        if programs:
                            logger.info(f"Получено {len(programs)} программ из {endpoint}")
                            return [self._normalize_api_program(p) for p in programs]

                    elif response.status == 404:
                        logger.debug(f"Endpoint {endpoint} не найден")
                    else:
                        logger.warning(f"API endpoint {endpoint} вернул статус {response.status}")

            except Exception as e:
                logger.debug(f"Ошибка API endpoint {endpoint}: {e}")
                continue

        return []

    def _normalize_api_program(self, program_data: Dict) -> Dict[str, Any]:
        """Нормализация данных программы из API"""
        return {
            "id": program_data.get("id", ""),
            "name": program_data.get("name", program_data.get("title", "")),
            "slug": program_data.get("slug", ""),
            "max_bounty": program_data.get("maxBounty", program_data.get("max_bounty", "Private")),
            "total_paid": program_data.get("totalPaid", program_data.get("total_paid", "Private")),
            "vault_tvl": program_data.get("vaultTvl", program_data.get("vault_tvl", "Private")),
            "last_updated": program_data.get("lastUpdated", program_data.get("last_updated", "Unknown")),
            "url": program_data.get("url", f"https://immunefi.com/bounty/{program_data.get('slug', '')}/"),
            "kyc_required": program_data.get("kycRequired", program_data.get("kyc_required", False)),
            "poc_required": program_data.get("pocRequired", program_data.get("poc_required", False)),
            "safe_harbor": program_data.get("safeHarbor", program_data.get("safe_harbor", False)),
            "triaged_by_immunefi": program_data.get("triaged", program_data.get("triaged_by_immunefi", False)),
            "vulnerability_types": program_data.get("vulnerabilityTypes", program_data.get("vulnerability_types", [])),
            "contracts": program_data.get("contracts", program_data.get("assets", [])),
            "endpoints": [],
            "github_repos": [],
            "documentation_urls": []
        }
    
    def _extract_api_urls_from_js(self, js_content: str) -> List[str]:
        """Извлечение API URL из JavaScript"""
        import re
        
        patterns = [
            r'["\']([^"\']*api[^"\']*bounty[^"\']*)["\']',
            r'["\']([^"\']*bounty[^"\']*api[^"\']*)["\']',
            r'fetch\(["\']([^"\']+)["\']',
            r'axios\.get\(["\']([^"\']+)["\']',
        ]
        
        urls = []
        for pattern in patterns:
            matches = re.findall(pattern, js_content, re.IGNORECASE)
            urls.extend(matches)
        
        return list(set(urls))
    
    async def _fetch_api_data(self, url: str) -> Optional[List[Dict[str, Any]]]:
        """Получение данных из API"""
        try:
            if not url.startswith('http'):
                url = f"{self.base_url}{url}"
                
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    return data if isinstance(data, list) else data.get('data', [])
        except Exception as e:
            logger.debug(f"Ошибка получения данных из {url}: {e}")
        
        return None
    
    async def fetch_program_details(self, program_slug: str) -> Dict[str, Any]:
        """Получение детальной информации о программе"""
        await asyncio.sleep(self.rate_limit_delay)  # Rate limiting
        
        try:
            url = f"{self.base_url}/bounty/{program_slug}/"
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    html_content = await response.text()
                    return self._parse_program_details(html_content)
                else:
                    logger.warning(f"Не удалось получить детали для {program_slug}: {response.status}")
                    return {}
                    
        except Exception as e:
            logger.error(f"Ошибка получения деталей программы {program_slug}: {e}")
            return {}
    
    def _parse_program_details(self, html_content: str) -> Dict[str, Any]:
        """Парсинг детальной информации программы"""
        details = {
            'contracts': [],
            'vulnerability_types': [],
            'requirements': [],
            'scope': [],
            'out_of_scope': [],
        }
        
        try:
            # Поиск контрактов
            import re
            
            # Паттерны для поиска адресов контрактов
            contract_patterns = [
                r'0x[a-fA-F0-9]{40}',  # Ethereum адреса
                r'[A-Za-z0-9]{32,44}',  # Solana адреса
            ]
            
            for pattern in contract_patterns:
                matches = re.findall(pattern, html_content)
                details['contracts'].extend(matches)
            
            # Поиск типов уязвимостей
            vuln_keywords = [
                'smart contract', 'blockchain', 'web application', 'mobile',
                'reentrancy', 'overflow', 'underflow', 'access control',
                'oracle manipulation', 'flash loan', 'governance',
            ]
            
            for keyword in vuln_keywords:
                if keyword.lower() in html_content.lower():
                    details['vulnerability_types'].append(keyword)
            
            # Удаление дубликатов
            details['contracts'] = list(set(details['contracts']))
            details['vulnerability_types'] = list(set(details['vulnerability_types']))
            
        except Exception as e:
            logger.error(f"Ошибка парсинга деталей: {e}")
        
        return details
    
    def save_programs_to_csv(self, filename: str = "immunefi_programs.csv"):
        """Сохранение программ в CSV файл"""
        if not self.programs:
            logger.warning("Нет программ для сохранения")
            return
        
        try:
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = [
                    'name', 'slug', 'vault_tvl', 'max_bounty', 'total_paid',
                    'resolution_time', 'last_updated', 'url', 'program_type',
                    'ecosystem', 'language', 'kyc_required', 'poc_required',
                    'safe_harbor', 'triaged_by_immunefi', 'contracts',
                    'vulnerability_types'
                ]
                
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                
                for program in self.programs:
                    row = asdict(program)
                    # Преобразование списков в строки
                    row['contracts'] = '; '.join(row['contracts'])
                    row['vulnerability_types'] = '; '.join(row['vulnerability_types'])
                    writer.writerow(row)
            
            logger.info(f"Программы сохранены в {filename}")
            
        except Exception as e:
            logger.error(f"Ошибка сохранения в CSV: {e}")
    
    def save_programs_to_json(self, filename: str = "immunefi_programs.json"):
        """Сохранение программ в JSON файл"""
        if not self.programs:
            logger.warning("Нет программ для сохранения")
            return
        
        try:
            data = [asdict(program) for program in self.programs]
            
            with open(filename, 'w', encoding='utf-8') as jsonfile:
                json.dump(data, jsonfile, indent=2, ensure_ascii=False)
            
            logger.info(f"Программы сохранены в {filename}")
            
        except Exception as e:
            logger.error(f"Ошибка сохранения в JSON: {e}")

async def main():
    """Основная функция"""
    logger.info("Запуск Immunefi Mass Scanner...")
    
    async with ImmunefiBountyParser() as parser:
        # Получение списка программ
        programs_data = await parser.fetch_bounty_list()
        
        if not programs_data:
            logger.error("Не удалось получить список программ")
            return
        
        logger.info(f"Найдено {len(programs_data)} программ")
        
        # Обработка каждой программы
        for i, program_data in enumerate(programs_data[:10]):  # Ограничиваем для тестирования
            try:
                # Создание объекта программы
                program = BountyProgram(
                    name=program_data.get('name', ''),
                    slug=program_data.get('slug', ''),
                    vault_tvl=program_data.get('vault_tvl', 'Private'),
                    max_bounty=program_data.get('max_bounty', 'Private'),
                    total_paid=program_data.get('total_paid', 'Private'),
                    resolution_time=program_data.get('resolution_time', 'Private'),
                    last_updated=program_data.get('last_updated', 'Unknown'),
                    url=program_data.get('url', ''),
                )
                
                # Получение детальной информации
                if program.slug:
                    details = await parser.fetch_program_details(program.slug)
                    program.contracts = details.get('contracts', [])
                    program.vulnerability_types = details.get('vulnerability_types', [])
                
                parser.programs.append(program)
                logger.info(f"Обработана программа {i+1}/{len(programs_data)}: {program.name}")
                
            except Exception as e:
                logger.error(f"Ошибка обработки программы {i+1}: {e}")
                continue
        
        # Сохранение результатов
        parser.save_programs_to_csv()
        parser.save_programs_to_json()
        
        logger.info(f"Сканирование завершено. Обработано {len(parser.programs)} программ")

if __name__ == "__main__":
    asyncio.run(main())
