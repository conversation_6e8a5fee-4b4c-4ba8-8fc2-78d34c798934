#!/usr/bin/env python3
"""
Immunefi Program Prioritizer
Система приоритизации программ bug bounty для максимизации эффективности
"""

import json
import re
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import logging
import math

# Настройка логирования
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ProgramScore:
    """Оценка программы bug bounty"""
    program_name: str
    total_score: float
    reward_score: float
    complexity_score: float
    freshness_score: float
    competition_score: float
    success_probability: float
    estimated_time_hours: float
    priority_rank: int = 0
    
    def to_dict(self) -> Dict:
        return asdict(self)

class ImmunefiBountyPrioritizer:
    """Система приоритизации программ Immunefi"""
    
    def __init__(self):
        self.programs: List[Dict] = []
        self.scored_programs: List[ProgramScore] = []
        
        # Веса для различных факторов
        self.weights = {
            'reward': 0.35,      # Размер награды
            'complexity': 0.25,   # Сложность программы
            'freshness': 0.20,    # Свежесть программы
            'competition': 0.20,  # Уровень конкуренции
        }
        
        # Параметры для расчета сложности
        self.complexity_factors = {
            'smart_contract': 1.0,
            'blockchain': 1.2,
            'defi': 1.5,
            'bridge': 1.8,
            'oracle': 1.6,
            'governance': 1.3,
            'staking': 1.1,
            'lending': 1.4,
            'dex': 1.3,
            'nft': 0.8,
            'wallet': 0.9,
            'web_app': 0.7,
            'mobile': 0.6,
        }
    
    def load_programs(self, programs_data: List[Dict]) -> None:
        """Загрузка данных программ"""
        self.programs = programs_data
        logger.info(f"Загружено {len(programs_data)} программ для анализа")
    
    def load_programs_from_file(self, filename: str) -> None:
        """Загрузка программ из JSON файла"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                self.programs = json.load(f)
            logger.info(f"Загружено {len(self.programs)} программ из {filename}")
        except Exception as e:
            logger.error(f"Ошибка загрузки файла {filename}: {e}")
    
    def calculate_priority_scores(self) -> List[ProgramScore]:
        """Расчет приоритетных оценок для всех программ"""
        logger.info("Начало расчета приоритетных оценок...")
        
        self.scored_programs = []
        
        for program in self.programs:
            try:
                score = self._calculate_program_score(program)
                self.scored_programs.append(score)
            except Exception as e:
                logger.error(f"Ошибка расчета оценки для {program.get('name', 'Unknown')}: {e}")
                continue
        
        # Сортировка по общей оценке
        self.scored_programs.sort(key=lambda x: x.total_score, reverse=True)
        
        # Присвоение рангов
        for i, program in enumerate(self.scored_programs):
            program.priority_rank = i + 1
        
        logger.info(f"Расчет завершен для {len(self.scored_programs)} программ")
        return self.scored_programs
    
    def _calculate_program_score(self, program: Dict) -> ProgramScore:
        """Расчет оценки для одной программы"""
        program_name = program.get('name', 'Unknown')
        
        # Расчет компонентов оценки
        reward_score = self._calculate_reward_score(program)
        complexity_score = self._calculate_complexity_score(program)
        freshness_score = self._calculate_freshness_score(program)
        competition_score = self._calculate_competition_score(program)
        
        # Общая оценка
        total_score = (
            reward_score * self.weights['reward'] +
            complexity_score * self.weights['complexity'] +
            freshness_score * self.weights['freshness'] +
            competition_score * self.weights['competition']
        )
        
        # Вероятность успеха
        success_probability = self._calculate_success_probability(
            reward_score, complexity_score, freshness_score, competition_score
        )
        
        # Оценка времени
        estimated_time = self._estimate_time_required(program, complexity_score)
        
        return ProgramScore(
            program_name=program_name,
            total_score=total_score,
            reward_score=reward_score,
            complexity_score=complexity_score,
            freshness_score=freshness_score,
            competition_score=competition_score,
            success_probability=success_probability,
            estimated_time_hours=estimated_time
        )
    
    def _calculate_reward_score(self, program: Dict) -> float:
        """Расчет оценки награды"""
        max_bounty = program.get('max_bounty', 'Private')
        
        if max_bounty == 'Private' or not max_bounty:
            return 0.5  # Средняя оценка для неизвестных наград
        
        try:
            # Извлечение числового значения
            bounty_value = self._parse_money_value(max_bounty)
            
            if bounty_value == 0:
                return 0.1
            
            # Логарифмическая шкала для наград
            # $1K = 0.1, $10K = 0.3, $100K = 0.5, $1M = 0.7, $10M = 0.9, $100M+ = 1.0
            if bounty_value >= 100_000_000:  # $100M+
                return 1.0
            elif bounty_value >= 10_000_000:  # $10M+
                return 0.9
            elif bounty_value >= 1_000_000:   # $1M+
                return 0.7
            elif bounty_value >= 100_000:     # $100K+
                return 0.5
            elif bounty_value >= 10_000:      # $10K+
                return 0.3
            elif bounty_value >= 1_000:       # $1K+
                return 0.1
            else:
                return 0.05
                
        except Exception as e:
            logger.debug(f"Ошибка парсинга награды '{max_bounty}': {e}")
            return 0.3  # Значение по умолчанию
    
    def _parse_money_value(self, money_str: str) -> float:
        """Парсинг денежного значения из строки"""
        if not money_str or money_str.lower() == 'private':
            return 0
        
        # Удаление символов валюты и пробелов
        clean_str = re.sub(r'[^\d.,kmKM]', '', money_str.upper())
        
        # Поиск числового значения
        match = re.search(r'([\d.,]+)([KM]?)', clean_str)
        if not match:
            return 0
        
        value_str, multiplier = match.groups()
        
        try:
            # Обработка десятичных разделителей
            value_str = value_str.replace(',', '.')
            value = float(value_str)
            
            # Применение множителей
            if multiplier == 'K':
                value *= 1_000
            elif multiplier == 'M':
                value *= 1_000_000
            
            return value
            
        except ValueError:
            return 0
    
    def _calculate_complexity_score(self, program: Dict) -> float:
        """Расчет оценки сложности (меньше = лучше)"""
        complexity_factors = []
        
        # Анализ названия и описания программы
        program_text = f"{program.get('name', '')} {program.get('description', '')}".lower()
        
        # Поиск ключевых слов, указывающих на сложность
        for keyword, factor in self.complexity_factors.items():
            if keyword.replace('_', ' ') in program_text or keyword in program_text:
                complexity_factors.append(factor)
        
        # Анализ количества контрактов
        contracts = program.get('contracts', [])
        if isinstance(contracts, list):
            contract_complexity = min(len(contracts) * 0.1, 0.5)  # Максимум 0.5
        else:
            contract_complexity = 0.2  # Значение по умолчанию
        
        # Анализ типов уязвимостей
        vuln_types = program.get('vulnerability_types', [])
        if isinstance(vuln_types, list):
            vuln_complexity = min(len(vuln_types) * 0.05, 0.3)  # Максимум 0.3
        else:
            vuln_complexity = 0.1
        
        # Средняя сложность
        if complexity_factors:
            avg_complexity = sum(complexity_factors) / len(complexity_factors)
        else:
            avg_complexity = 1.0  # Средняя сложность по умолчанию
        
        total_complexity = avg_complexity + contract_complexity + vuln_complexity
        
        # Инвертируем оценку (меньше сложность = выше оценка)
        # Нормализуем в диапазон 0-1
        normalized_complexity = max(0, min(1, (3.0 - total_complexity) / 3.0))
        
        return normalized_complexity
    
    def _calculate_freshness_score(self, program: Dict) -> float:
        """Расчет оценки свежести программы"""
        last_updated = program.get('last_updated', '')
        
        if not last_updated or last_updated.lower() == 'unknown':
            return 0.3  # Средняя оценка для неизвестных дат
        
        try:
            # Парсинг даты
            update_date = self._parse_date(last_updated)
            if not update_date:
                return 0.3
            
            # Расчет дней с последнего обновления
            days_since_update = (datetime.now() - update_date).days
            
            # Оценка свежести
            if days_since_update <= 7:        # Неделя
                return 1.0
            elif days_since_update <= 30:     # Месяц
                return 0.8
            elif days_since_update <= 90:     # 3 месяца
                return 0.6
            elif days_since_update <= 180:    # 6 месяцев
                return 0.4
            elif days_since_update <= 365:    # Год
                return 0.2
            else:                              # Больше года
                return 0.1
                
        except Exception as e:
            logger.debug(f"Ошибка парсинга даты '{last_updated}': {e}")
            return 0.3
    
    def _parse_date(self, date_str: str) -> Optional[datetime]:
        """Парсинг даты из различных форматов"""
        date_formats = [
            '%d/%m/%Y',
            '%m/%d/%Y', 
            '%Y-%m-%d',
            '%d.%m.%Y',
            '%Y/%m/%d',
        ]
        
        for fmt in date_formats:
            try:
                return datetime.strptime(date_str, fmt)
            except ValueError:
                continue
        
        return None
    
    def _calculate_competition_score(self, program: Dict) -> float:
        """Расчет оценки конкуренции (меньше конкуренция = выше оценка)"""
        # Факторы, влияющие на конкуренцию
        competition_factors = []
        
        # Размер награды (больше награда = больше конкуренция)
        max_bounty = program.get('max_bounty', 'Private')
        bounty_value = self._parse_money_value(max_bounty)
        
        if bounty_value >= 1_000_000:      # $1M+ - высокая конкуренция
            competition_factors.append(0.2)
        elif bounty_value >= 100_000:      # $100K+ - средняя конкуренция
            competition_factors.append(0.5)
        elif bounty_value >= 10_000:       # $10K+ - низкая конкуренция
            competition_factors.append(0.7)
        else:                              # <$10K - очень низкая конкуренция
            competition_factors.append(0.9)
        
        # Популярность экосистемы
        program_text = f"{program.get('name', '')} {program.get('ecosystem', '')}".lower()
        
        popular_ecosystems = {
            'ethereum': 0.2,
            'bitcoin': 0.3,
            'binance': 0.3,
            'polygon': 0.4,
            'solana': 0.4,
            'avalanche': 0.5,
            'fantom': 0.6,
            'arbitrum': 0.5,
            'optimism': 0.5,
        }
        
        ecosystem_competition = 0.7  # Значение по умолчанию
        for ecosystem, competition in popular_ecosystems.items():
            if ecosystem in program_text:
                ecosystem_competition = competition
                break
        
        competition_factors.append(ecosystem_competition)
        
        # Требования KYC и PoC (снижают конкуренцию)
        if program.get('kyc_required', False):
            competition_factors.append(0.8)  # KYC отпугивает некоторых исследователей
        
        if program.get('poc_required', False):
            competition_factors.append(0.7)  # PoC требует больше усилий
        
        # Средняя оценка конкуренции
        avg_competition = sum(competition_factors) / len(competition_factors)
        
        return avg_competition
    
    def _calculate_success_probability(self, reward: float, complexity: float, 
                                     freshness: float, competition: float) -> float:
        """Расчет вероятности успеха"""
        # Формула учитывает все факторы
        # Высокая награда, низкая сложность, свежая программа, низкая конкуренция = высокая вероятность
        
        base_probability = (reward + complexity + freshness + competition) / 4
        
        # Бонусы и штрафы
        if reward > 0.7 and complexity > 0.6:  # Высокая награда + низкая сложность
            base_probability *= 1.2
        
        if freshness > 0.8:  # Очень свежая программа
            base_probability *= 1.1
        
        if competition > 0.8:  # Низкая конкуренция
            base_probability *= 1.15
        
        # Нормализация в диапазон 0-1
        return min(1.0, base_probability)
    
    def _estimate_time_required(self, program: Dict, complexity_score: float) -> float:
        """Оценка времени, необходимого для тестирования (в часах)"""
        # Базовое время зависит от сложности
        base_time = 8.0  # 8 часов базовое время
        
        # Корректировка по сложности (инвертируем, так как complexity_score уже инвертирован)
        complexity_multiplier = 2.0 - complexity_score  # 1.0 - 2.0
        
        # Корректировка по количеству контрактов
        contracts = program.get('contracts', [])
        if isinstance(contracts, list) and contracts:
            contract_multiplier = 1 + (len(contracts) - 1) * 0.3  # +30% за каждый дополнительный контракт
        else:
            contract_multiplier = 1.0
        
        # Корректировка по типам уязвимостей
        vuln_types = program.get('vulnerability_types', [])
        if isinstance(vuln_types, list) and vuln_types:
            vuln_multiplier = 1 + len(vuln_types) * 0.1  # +10% за каждый тип уязвимости
        else:
            vuln_multiplier = 1.0
        
        total_time = base_time * complexity_multiplier * contract_multiplier * vuln_multiplier
        
        # Ограничиваем максимальное время
        return min(total_time, 80.0)  # Максимум 80 часов
    
    def get_top_programs(self, limit: int = 20) -> List[ProgramScore]:
        """Получение топ программ по приоритету"""
        if not self.scored_programs:
            self.calculate_priority_scores()
        
        return self.scored_programs[:limit]
    
    def get_programs_by_criteria(self, min_reward: float = 0, max_time: float = 40, 
                                min_success_prob: float = 0.3) -> List[ProgramScore]:
        """Получение программ по критериям"""
        if not self.scored_programs:
            self.calculate_priority_scores()
        
        filtered_programs = []
        for program in self.scored_programs:
            if (program.reward_score >= min_reward and 
                program.estimated_time_hours <= max_time and
                program.success_probability >= min_success_prob):
                filtered_programs.append(program)
        
        return filtered_programs
    
    def save_prioritized_programs(self, filename: str = "prioritized_programs.json"):
        """Сохранение приоритизированных программ"""
        if not self.scored_programs:
            self.calculate_priority_scores()
        
        try:
            data = [program.to_dict() for program in self.scored_programs]
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Приоритизированные программы сохранены в {filename}")
            
        except Exception as e:
            logger.error(f"Ошибка сохранения: {e}")
    
    def generate_priority_report(self) -> str:
        """Генерация отчета о приоритизации"""
        if not self.scored_programs:
            self.calculate_priority_scores()
        
        report = f"# Отчет о приоритизации программ Immunefi\n\n"
        report += f"Дата: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        report += f"Всего проанализировано программ: {len(self.scored_programs)}\n\n"
        
        # Топ-10 программ
        top_programs = self.get_top_programs(10)
        report += "## Топ-10 приоритетных программ:\n\n"
        
        for i, program in enumerate(top_programs, 1):
            report += f"### {i}. {program.program_name}\n"
            report += f"- **Общая оценка:** {program.total_score:.3f}\n"
            report += f"- **Оценка награды:** {program.reward_score:.3f}\n"
            report += f"- **Оценка сложности:** {program.complexity_score:.3f}\n"
            report += f"- **Оценка свежести:** {program.freshness_score:.3f}\n"
            report += f"- **Оценка конкуренции:** {program.competition_score:.3f}\n"
            report += f"- **Вероятность успеха:** {program.success_probability:.1%}\n"
            report += f"- **Оценочное время:** {program.estimated_time_hours:.1f} часов\n\n"
        
        # Статистика
        avg_score = sum(p.total_score for p in self.scored_programs) / len(self.scored_programs)
        avg_success = sum(p.success_probability for p in self.scored_programs) / len(self.scored_programs)
        avg_time = sum(p.estimated_time_hours for p in self.scored_programs) / len(self.scored_programs)
        
        report += "## Общая статистика:\n"
        report += f"- Средняя оценка: {avg_score:.3f}\n"
        report += f"- Средняя вероятность успеха: {avg_success:.1%}\n"
        report += f"- Среднее время тестирования: {avg_time:.1f} часов\n\n"
        
        # Рекомендации
        high_priority = len([p for p in self.scored_programs if p.total_score > 0.7])
        medium_priority = len([p for p in self.scored_programs if 0.4 <= p.total_score <= 0.7])
        low_priority = len([p for p in self.scored_programs if p.total_score < 0.4])
        
        report += "## Распределение по приоритету:\n"
        report += f"- Высокий приоритет (>0.7): {high_priority} программ\n"
        report += f"- Средний приоритет (0.4-0.7): {medium_priority} программ\n"
        report += f"- Низкий приоритет (<0.4): {low_priority} программ\n\n"
        
        report += "## Рекомендации:\n"
        report += "1. Начните с программ высокого приоритета\n"
        report += "2. Сосредоточьтесь на программах с высокой вероятностью успеха\n"
        report += "3. Учитывайте доступное время для тестирования\n"
        report += "4. Регулярно обновляйте приоритизацию\n"
        
        return report

def main():
    """Основная функция для демонстрации"""
    # Пример данных программ
    sample_programs = [
        {
            'name': 'High Reward DeFi Protocol',
            'max_bounty': '$1M',
            'last_updated': '15/1/2025',
            'contracts': ['0x123...', '0x456...'],
            'vulnerability_types': ['smart_contract', 'defi'],
            'kyc_required': False,
            'poc_required': True,
        },
        {
            'name': 'Simple NFT Marketplace',
            'max_bounty': '$50K',
            'last_updated': '10/1/2025',
            'contracts': ['0x789...'],
            'vulnerability_types': ['web_app', 'nft'],
            'kyc_required': True,
            'poc_required': False,
        }
    ]
    
    prioritizer = ImmunefiBountyPrioritizer()
    prioritizer.load_programs(sample_programs)
    
    # Расчет приоритетов
    scored_programs = prioritizer.calculate_priority_scores()
    
    # Генерация отчета
    report = prioritizer.generate_priority_report()
    print(report)
    
    # Сохранение результатов
    prioritizer.save_prioritized_programs()

if __name__ == "__main__":
    main()
