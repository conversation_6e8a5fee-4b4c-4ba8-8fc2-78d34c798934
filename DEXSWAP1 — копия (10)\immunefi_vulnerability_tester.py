#!/usr/bin/env python3
"""
Immunefi Vulnerability Tester
Интеграция стратегий тестирования с программами Immunefi
"""

import asyncio
import aiohttp
import json
import random
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime
import logging
from pathlib import Path
import importlib.util
from blockchain_config import (
    get_rpc_endpoints, get_api_key, get_etherscan_api,
    get_payloads, get_vulnerability_indicators,
    TESTING_CONFIG, SECURITY_CONFIG
)

# Настройка логирования
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class VulnerabilityResult:
    """Результат тестирования уязвимости"""
    program_name: str
    vulnerability_type: str
    severity: str
    description: str
    proof_of_concept: str
    impact: str
    recommendation: str
    contract_address: str = ""
    transaction_hash: str = ""
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

class ImmunefiBountyTester:
    """Тестер уязвимостей для программ Immunefi"""
    
    def __init__(self):
        self.session = None
        self.strategies = {}
        self.results: List[VulnerabilityResult] = []
        self.tested_programs = set()
        
        # Загрузка стратегий тестирования
        self._load_testing_strategies()
    
    def _load_testing_strategies(self):
        """Загрузка модулей стратегий тестирования"""
        strategy_files = [
            'quantum_strategies.py',
            'ai_strategies.py', 
            'mathematical_strategies.py',
            'future_strategies.py'
        ]
        
        for strategy_file in strategy_files:
            try:
                if Path(strategy_file).exists():
                    spec = importlib.util.spec_from_file_location(
                        strategy_file[:-3], strategy_file
                    )
                    module = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(module)
                    
                    strategy_name = strategy_file[:-3]
                    self.strategies[strategy_name] = module
                    logger.info(f"Загружена стратегия: {strategy_name}")
                else:
                    logger.warning(f"Файл стратегии не найден: {strategy_file}")
                    
            except Exception as e:
                logger.error(f"Ошибка загрузки стратегии {strategy_file}: {e}")
    
    async def __aenter__(self):
        """Асинхронный контекстный менеджер"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=60),
            headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'application/json, text/plain, */*',
                'Content-Type': 'application/json',
            }
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Закрытие сессии"""
        if self.session:
            await self.session.close()
    
    async def test_program(self, program_data: Dict[str, Any]) -> List[VulnerabilityResult]:
        """Тестирование одной программы bug bounty"""
        program_name = program_data.get('name', 'Unknown')
        
        if program_name in self.tested_programs:
            logger.info(f"Программа {program_name} уже протестирована")
            return []
        
        logger.info(f"Начало тестирования программы: {program_name}")
        program_results = []
        
        try:
            # Получение детальной информации о программе
            program_details = await self._fetch_program_details(program_data)
            
            # Применение различных стратегий тестирования
            for strategy_name, strategy_module in self.strategies.items():
                try:
                    logger.info(f"Применение стратегии {strategy_name} к {program_name}")
                    
                    # Вызов функции тестирования из модуля стратегии
                    if hasattr(strategy_module, 'test_program'):
                        strategy_results = await strategy_module.test_program(
                            program_details, self.session
                        )
                        
                        if strategy_results:
                            program_results.extend(strategy_results)
                            logger.info(f"Стратегия {strategy_name} нашла {len(strategy_results)} потенциальных уязвимостей")
                    
                    # Задержка между стратегиями
                    await asyncio.sleep(random.uniform(1, 3))
                    
                except Exception as e:
                    logger.error(f"Ошибка применения стратегии {strategy_name}: {e}")
                    continue
            
            # Специализированные тесты для Immunefi
            immunefi_results = await self._run_immunefi_specific_tests(program_details)
            program_results.extend(immunefi_results)
            
            self.tested_programs.add(program_name)
            self.results.extend(program_results)
            
            logger.info(f"Тестирование {program_name} завершено. Найдено {len(program_results)} потенциальных уязвимостей")
            
        except Exception as e:
            logger.error(f"Ошибка тестирования программы {program_name}: {e}")
        
        return program_results
    
    async def _fetch_program_details(self, program_data: Dict[str, Any]) -> Dict[str, Any]:
        """Получение детальной информации о программе"""
        details = program_data.copy()
        
        try:
            # Если есть URL программы, получаем дополнительную информацию
            program_url = program_data.get('url', '')
            if program_url:
                async with self.session.get(program_url) as response:
                    if response.status == 200:
                        html_content = await response.text()
                        
                        # Извлечение дополнительной информации
                        additional_info = self._parse_program_page(html_content)
                        details.update(additional_info)
            
        except Exception as e:
            logger.error(f"Ошибка получения деталей программы: {e}")
        
        return details
    
    def _parse_program_page(self, html_content: str) -> Dict[str, Any]:
        """Парсинг страницы программы для извлечения дополнительной информации"""
        info = {
            'contracts': [],
            'endpoints': [],
            'documentation_urls': [],
            'github_repos': [],
            'vulnerability_categories': [],
            'reward_ranges': {},
        }
        
        try:
            import re
            
            # Поиск контрактов
            contract_patterns = [
                r'0x[a-fA-F0-9]{40}',  # Ethereum
                r'[A-Za-z0-9]{32,44}',  # Solana
                r'cosmos[a-z0-9]+',     # Cosmos
                r'terra[a-z0-9]+',      # Terra
            ]
            
            for pattern in contract_patterns:
                matches = re.findall(pattern, html_content)
                info['contracts'].extend(matches)
            
            # Поиск API endpoints
            api_patterns = [
                r'https?://[^\s<>"\']+api[^\s<>"\']*',
                r'https?://api\.[^\s<>"\']+',
                r'/api/[^\s<>"\']+',
            ]
            
            for pattern in api_patterns:
                matches = re.findall(pattern, html_content, re.IGNORECASE)
                info['endpoints'].extend(matches)
            
            # Поиск GitHub репозиториев
            github_pattern = r'https://github\.com/[^\s<>"\']+' 
            github_matches = re.findall(github_pattern, html_content)
            info['github_repos'].extend(github_matches)
            
            # Поиск документации
            doc_patterns = [
                r'https?://[^\s<>"\']*docs?[^\s<>"\']*',
                r'https?://[^\s<>"\']*documentation[^\s<>"\']*',
            ]
            
            for pattern in doc_patterns:
                matches = re.findall(pattern, html_content, re.IGNORECASE)
                info['documentation_urls'].extend(matches)
            
            # Удаление дубликатов
            for key in info:
                if isinstance(info[key], list):
                    info[key] = list(set(info[key]))
            
        except Exception as e:
            logger.error(f"Ошибка парсинга страницы программы: {e}")
        
        return info
    
    async def _run_immunefi_specific_tests(self, program_details: Dict[str, Any]) -> List[VulnerabilityResult]:
        """Специализированные тесты для программ Immunefi"""
        results = []
        
        try:
            # Тест 1: Анализ контрактов на стандартные уязвимости
            contract_results = await self._test_smart_contracts(program_details)
            results.extend(contract_results)
            
            # Тест 2: Анализ API endpoints
            api_results = await self._test_api_endpoints(program_details)
            results.extend(api_results)
            
            # Тест 3: Анализ документации на утечки информации
            doc_results = await self._test_documentation(program_details)
            results.extend(doc_results)
            
            # Тест 4: Анализ GitHub репозиториев
            github_results = await self._test_github_repos(program_details)
            results.extend(github_results)
            
        except Exception as e:
            logger.error(f"Ошибка специализированных тестов: {e}")
        
        return results
    
    async def _test_smart_contracts(self, program_details: Dict[str, Any]) -> List[VulnerabilityResult]:
        """Тестирование смарт-контрактов"""
        results = []
        contracts = program_details.get('contracts', [])
        program_name = program_details.get('name', 'Unknown')
        
        for contract in contracts:
            try:
                # Генерация различных тестовых сценариев
                test_scenarios = [
                    {
                        'type': 'reentrancy',
                        'description': 'Тест на уязвимость reentrancy',
                        'method': 'recursive_call_test'
                    },
                    {
                        'type': 'access_control',
                        'description': 'Тест контроля доступа',
                        'method': 'unauthorized_access_test'
                    },
                    {
                        'type': 'integer_overflow',
                        'description': 'Тест переполнения целых чисел',
                        'method': 'overflow_test'
                    },
                    {
                        'type': 'oracle_manipulation',
                        'description': 'Тест манипуляции оракулами',
                        'method': 'oracle_test'
                    }
                ]
                
                for scenario in test_scenarios:
                    # РЕАЛЬНОЕ тестирование контракта
                    vulnerability_found = await self._test_contract_vulnerability(
                        contract, scenario
                    )
                    
                    if vulnerability_found:
                        result = VulnerabilityResult(
                            program_name=program_name,
                            vulnerability_type=scenario['type'],
                            severity=self._calculate_severity(scenario['type']),
                            description=f"Потенциальная уязвимость {scenario['type']} в контракте {contract}",
                            proof_of_concept=f"PoC для {scenario['method']} на контракте {contract}",
                            impact=self._get_impact_description(scenario['type']),
                            recommendation=self._get_recommendation(scenario['type']),
                            contract_address=contract
                        )
                        results.append(result)
                
                # Задержка между контрактами
                await asyncio.sleep(random.uniform(0.5, 1.5))
                
            except Exception as e:
                logger.error(f"Ошибка тестирования контракта {contract}: {e}")
                continue
        
        return results
    
    async def _test_contract_vulnerability(self, contract: str, scenario: Dict[str, Any]) -> bool:
        """РЕАЛЬНОЕ тестирование контракта на уязвимость"""
        try:
            # Определяем блокчейн по адресу контракта
            blockchain = self._detect_blockchain(contract)

            if blockchain == "ethereum":
                return await self._test_ethereum_contract(contract, scenario)
            elif blockchain == "solana":
                return await self._test_solana_contract(contract, scenario)
            elif blockchain == "polygon":
                return await self._test_polygon_contract(contract, scenario)
            elif blockchain == "bsc":
                return await self._test_bsc_contract(contract, scenario)
            else:
                # Общие тесты для неизвестных блокчейнов
                return await self._test_generic_contract(contract, scenario)

        except Exception as e:
            logger.error(f"Ошибка тестирования контракта {contract}: {e}")
            return False

    def _detect_blockchain(self, contract: str) -> str:
        """Определение блокчейна по адресу контракта"""
        if contract.startswith("0x") and len(contract) == 42:
            return "ethereum"  # Ethereum, Polygon, BSC
        elif len(contract) >= 32 and len(contract) <= 44 and not contract.startswith("0x"):
            return "solana"
        elif contract.startswith("cosmos"):
            return "cosmos"
        elif contract.startswith("terra"):
            return "terra"
        else:
            return "unknown"

    async def _test_ethereum_contract(self, contract: str, scenario: Dict[str, Any]) -> bool:
        """РЕАЛЬНОЕ тестирование Ethereum контракта"""
        try:
            # Получение ABI контракта
            abi = await self._get_contract_abi(contract)
            if not abi:
                logger.warning(f"Не удалось получить ABI для {contract}")
                return False

            # Анализ функций контракта
            functions = self._extract_contract_functions(abi)

            # Тестирование в зависимости от типа уязвимости
            if scenario['type'] == 'reentrancy':
                return await self._test_reentrancy(contract, functions)
            elif scenario['type'] == 'access_control':
                return await self._test_access_control(contract, functions)
            elif scenario['type'] == 'integer_overflow':
                return await self._test_integer_overflow(contract, functions)
            elif scenario['type'] == 'oracle_manipulation':
                return await self._test_oracle_manipulation(contract, functions)

            return False

        except Exception as e:
            logger.error(f"Ошибка тестирования Ethereum контракта: {e}")
            return False

    async def _get_contract_abi(self, contract: str) -> Optional[List]:
        """РЕАЛЬНОЕ получение ABI контракта из Etherscan API"""
        try:
            # Определяем блокчейн и получаем соответствующий API
            blockchains = ["ethereum", "polygon", "bsc", "arbitrum", "optimism", "fantom", "avalanche"]

            etherscan_apis = []
            for blockchain in blockchains:
                api_url = get_etherscan_api(blockchain)
                api_key = get_api_key(f"{blockchain}scan") or get_api_key("etherscan")

                if api_url and api_key:
                    full_url = f"{api_url}?module=contract&action=getabi&address={contract}&apikey={api_key}"
                    etherscan_apis.append(full_url)

            for api_url in etherscan_apis:
                try:
                    async with self.session.get(api_url) as response:
                        if response.status == 200:
                            data = await response.json()
                            if data.get("status") == "1" and data.get("result"):
                                abi = json.loads(data["result"])
                                return abi
                except Exception:
                    continue

            return None

        except Exception as e:
            logger.error(f"Ошибка получения ABI: {e}")
            return None

    def _extract_contract_functions(self, abi: List) -> List[Dict]:
        """Извлечение функций из ABI"""
        functions = []
        for item in abi:
            if item.get("type") == "function":
                functions.append({
                    "name": item.get("name", ""),
                    "inputs": item.get("inputs", []),
                    "outputs": item.get("outputs", []),
                    "stateMutability": item.get("stateMutability", ""),
                    "payable": item.get("payable", False)
                })
        return functions

    async def _test_reentrancy(self, contract: str, functions: List[Dict]) -> bool:
        """РЕАЛЬНЫЙ тест на reentrancy"""
        try:
            # Поиск функций, которые могут быть уязвимы к reentrancy
            vulnerable_patterns = [
                "withdraw", "transfer", "send", "call", "delegatecall",
                "selfdestruct", "suicide", "fallback", "receive"
            ]

            for func in functions:
                func_name = func["name"].lower()

                # Проверяем, есть ли подозрительные паттерны
                if any(pattern in func_name for pattern in vulnerable_patterns):
                    # Проверяем, является ли функция payable или изменяет состояние
                    if (func.get("payable") or
                        func.get("stateMutability") in ["payable", "nonpayable"]):

                        logger.info(f"Потенциальная reentrancy уязвимость в функции {func_name}")
                        return True

            return False

        except Exception as e:
            logger.error(f"Ошибка теста reentrancy: {e}")
            return False

    async def _test_access_control(self, contract: str, functions: List[Dict]) -> bool:
        """РЕАЛЬНЫЙ тест контроля доступа"""
        try:
            # Поиск административных функций без модификаторов
            admin_patterns = [
                "owner", "admin", "pause", "unpause", "mint", "burn",
                "upgrade", "destroy", "kill", "emergency", "rescue"
            ]

            public_admin_functions = []

            for func in functions:
                func_name = func["name"].lower()

                # Проверяем административные функции
                if any(pattern in func_name for pattern in admin_patterns):
                    # Если функция не view/pure, она может изменять состояние
                    if func.get("stateMutability") not in ["view", "pure"]:
                        public_admin_functions.append(func_name)

            if public_admin_functions:
                logger.info(f"Потенциальные проблемы контроля доступа: {public_admin_functions}")
                return True

            return False

        except Exception as e:
            logger.error(f"Ошибка теста контроля доступа: {e}")
            return False

    async def _test_solana_contract(self, contract: str, scenario: Dict[str, Any]) -> bool:
        """РЕАЛЬНОЕ тестирование Solana программы"""
        try:
            # Получаем реальные Solana RPC endpoints из конфигурации
            rpc_endpoints = get_rpc_endpoints("solana")

            for rpc_url in rpc_endpoints:
                try:
                    # Получение информации о программе
                    program_info = await self._get_solana_program_info(contract, rpc_url)
                    if not program_info:
                        continue

                    # Анализ в зависимости от типа уязвимости
                    if scenario['type'] == 'access_control':
                        return await self._test_solana_access_control(contract, rpc_url)
                    elif scenario['type'] == 'integer_overflow':
                        return await self._test_solana_integer_overflow(contract, rpc_url)
                    elif scenario['type'] == 'oracle_manipulation':
                        return await self._test_solana_oracle_manipulation(contract, rpc_url)

                    break

                except Exception as e:
                    logger.debug(f"Ошибка RPC {rpc_url}: {e}")
                    continue

            return False

        except Exception as e:
            logger.error(f"Ошибка тестирования Solana программы: {e}")
            return False

    async def _get_solana_program_info(self, program_id: str, rpc_url: str) -> Optional[Dict]:
        """Получение информации о Solana программе"""
        try:
            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getAccountInfo",
                "params": [
                    program_id,
                    {"encoding": "base64"}
                ]
            }

            async with self.session.post(rpc_url, json=payload) as response:
                if response.status == 200:
                    data = await response.json()
                    if "result" in data and data["result"]["value"]:
                        return data["result"]["value"]

            return None

        except Exception as e:
            logger.error(f"Ошибка получения информации о Solana программе: {e}")
            return None

    async def _test_solana_access_control(self, program_id: str, rpc_url: str) -> bool:
        """Тест контроля доступа в Solana программе"""
        try:
            # Получение всех аккаунтов, принадлежащих программе
            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getProgramAccounts",
                "params": [
                    program_id,
                    {
                        "encoding": "base64",
                        "filters": []
                    }
                ]
            }

            async with self.session.post(rpc_url, json=payload) as response:
                if response.status == 200:
                    data = await response.json()
                    if "result" in data:
                        accounts = data["result"]

                        # Анализ аккаунтов на предмет проблем доступа
                        for account in accounts:
                            account_data = account.get("account", {})

                            # Проверка владельца аккаунта
                            owner = account_data.get("owner")
                            if owner and owner != program_id:
                                logger.info(f"Подозрительный владелец аккаунта: {owner}")
                                return True

                            # Проверка исполняемости
                            executable = account_data.get("executable", False)
                            if executable:
                                logger.info(f"Исполняемый аккаунт найден: {account['pubkey']}")
                                return True

            return False

        except Exception as e:
            logger.error(f"Ошибка теста контроля доступа Solana: {e}")
            return False

    async def _test_polygon_contract(self, contract: str, scenario: Dict[str, Any]) -> bool:
        """РЕАЛЬНОЕ тестирование Polygon контракта"""
        try:
            # Polygon RPC endpoints
            rpc_endpoints = [
                "https://polygon-rpc.com",
                "https://rpc-mainnet.matic.network",
                "https://rpc.ankr.com/polygon"
            ]

            for rpc_url in rpc_endpoints:
                try:
                    # Получение кода контракта
                    code = await self._get_contract_code(contract, rpc_url)
                    if code and code != "0x":
                        # Анализ байт-кода на уязвимости
                        return await self._analyze_bytecode(code, scenario['type'])

                except Exception as e:
                    logger.debug(f"Ошибка Polygon RPC {rpc_url}: {e}")
                    continue

            return False

        except Exception as e:
            logger.error(f"Ошибка тестирования Polygon контракта: {e}")
            return False

    async def _get_contract_code(self, contract: str, rpc_url: str) -> Optional[str]:
        """Получение байт-кода контракта"""
        try:
            payload = {
                "jsonrpc": "2.0",
                "method": "eth_getCode",
                "params": [contract, "latest"],
                "id": 1
            }

            async with self.session.post(rpc_url, json=payload) as response:
                if response.status == 200:
                    data = await response.json()
                    return data.get("result")

            return None

        except Exception as e:
            logger.error(f"Ошибка получения кода контракта: {e}")
            return None

    async def _analyze_bytecode(self, bytecode: str, vuln_type: str) -> bool:
        """Анализ байт-кода на уязвимости"""
        try:
            # Удаляем префикс 0x
            if bytecode.startswith("0x"):
                bytecode = bytecode[2:]

            # Поиск опасных опкодов
            dangerous_opcodes = {
                'reentrancy': ['f1', 'f4', 'ff'],  # CALL, DELEGATECALL, SELFDESTRUCT
                'integer_overflow': ['01', '02', '03', '04'],  # ADD, MUL, SUB, DIV
                'access_control': ['33', '32'],  # CALLER, ORIGIN
            }

            opcodes = dangerous_opcodes.get(vuln_type, [])

            for opcode in opcodes:
                if opcode in bytecode.lower():
                    logger.info(f"Найден подозрительный опкод {opcode} для {vuln_type}")
                    return True

            # Дополнительные проверки для конкретных уязвимостей
            if vuln_type == 'reentrancy':
                # Поиск паттернов reentrancy
                reentrancy_patterns = [
                    'f1600052',  # CALL pattern
                    'f4600052',  # DELEGATECALL pattern
                ]

                for pattern in reentrancy_patterns:
                    if pattern in bytecode.lower():
                        return True

            return False

        except Exception as e:
            logger.error(f"Ошибка анализа байт-кода: {e}")
            return False

    async def _test_generic_contract(self, contract: str, scenario: Dict[str, Any]) -> bool:
        """Общие тесты для неизвестных блокчейнов"""
        try:
            # Попытка определить тип блокчейна по длине адреса
            if len(contract) == 42 and contract.startswith("0x"):
                # EVM-совместимый блокчейн
                return await self._test_evm_contract(contract, scenario)
            elif len(contract) >= 32 and len(contract) <= 44:
                # Возможно Solana или другой
                return await self._test_base58_contract(contract, scenario)
            else:
                # Неизвестный формат
                logger.warning(f"Неизвестный формат адреса контракта: {contract}")
                return False

        except Exception as e:
            logger.error(f"Ошибка общего тестирования: {e}")
            return False

    async def _test_evm_contract(self, contract: str, scenario: Dict[str, Any]) -> bool:
        """Тестирование EVM-совместимого контракта"""
        try:
            # Список популярных EVM RPC endpoints
            evm_rpcs = [
                "https://eth.llamarpc.com",
                "https://rpc.ankr.com/eth",
                "https://ethereum.publicnode.com",
                "https://polygon-rpc.com",
                "https://rpc.ankr.com/polygon",
                "https://bsc-dataseed.binance.org",
                "https://rpc.ankr.com/bsc"
            ]

            for rpc_url in evm_rpcs:
                try:
                    # Проверка существования контракта
                    code = await self._get_contract_code(contract, rpc_url)
                    if code and code != "0x":
                        return await self._analyze_bytecode(code, scenario['type'])

                except Exception as e:
                    logger.debug(f"Ошибка EVM RPC {rpc_url}: {e}")
                    continue

            return False

        except Exception as e:
            logger.error(f"Ошибка тестирования EVM контракта: {e}")
            return False

    async def _test_base58_contract(self, contract: str, scenario: Dict[str, Any]) -> bool:
        """Тестирование контракта с base58 адресом (Solana и др.)"""
        try:
            # Пробуем как Solana программу
            return await self._test_solana_contract(contract, scenario)

        except Exception as e:
            logger.error(f"Ошибка тестирования base58 контракта: {e}")
            return False
    
    async def _test_api_endpoints(self, program_details: Dict[str, Any]) -> List[VulnerabilityResult]:
        """Тестирование API endpoints"""
        results = []
        endpoints = program_details.get('endpoints', [])
        program_name = program_details.get('name', 'Unknown')
        
        for endpoint in endpoints:
            try:
                # Различные типы API тестов
                api_tests = [
                    {'type': 'sql_injection', 'payload': "' OR 1=1 --"},
                    {'type': 'xss', 'payload': '<script>alert("XSS")</script>'},
                    {'type': 'path_traversal', 'payload': '../../../etc/passwd'},
                    {'type': 'command_injection', 'payload': '; cat /etc/passwd'},
                    {'type': 'xxe', 'payload': '<?xml version="1.0"?><!DOCTYPE root [<!ENTITY test SYSTEM "file:///etc/passwd">]><root>&test;</root>'},
                ]
                
                for test in api_tests:
                    vulnerability_found = await self._test_api_vulnerability(
                        endpoint, test
                    )
                    
                    if vulnerability_found:
                        result = VulnerabilityResult(
                            program_name=program_name,
                            vulnerability_type=test['type'],
                            severity=self._calculate_severity(test['type']),
                            description=f"Потенциальная {test['type']} уязвимость в API endpoint {endpoint}",
                            proof_of_concept=f"Payload: {test['payload']}",
                            impact=self._get_impact_description(test['type']),
                            recommendation=self._get_recommendation(test['type'])
                        )
                        results.append(result)
                
                await asyncio.sleep(random.uniform(0.5, 1.0))
                
            except Exception as e:
                logger.error(f"Ошибка тестирования API {endpoint}: {e}")
                continue
        
        return results
    
    async def _test_api_vulnerability(self, endpoint: str, test: Dict[str, Any]) -> bool:
        """РЕАЛЬНОЕ тестирование API на уязвимость"""
        try:
            vulnerability_found = False

            # Различные методы тестирования
            test_methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH']

            for method in test_methods:
                try:
                    # Подготовка payload в зависимости от типа уязвимости
                    payloads = self._generate_payloads(test['type'])

                    for payload in payloads:
                        # Тестирование в параметрах URL
                        if method == 'GET':
                            test_url = f"{endpoint}?param={payload}&id={payload}&search={payload}"
                            async with self.session.get(test_url, timeout=10) as response:
                                if await self._analyze_response(response, test['type'], payload):
                                    return True

                        # Тестирование в теле запроса
                        elif method == 'POST':
                            test_data = {
                                'param': payload,
                                'data': payload,
                                'input': payload,
                                'search': payload,
                                'query': payload
                            }

                            # JSON payload
                            async with self.session.post(
                                endpoint,
                                json=test_data,
                                timeout=10
                            ) as response:
                                if await self._analyze_response(response, test['type'], payload):
                                    return True

                            # Form data
                            async with self.session.post(
                                endpoint,
                                data=test_data,
                                timeout=10
                            ) as response:
                                if await self._analyze_response(response, test['type'], payload):
                                    return True

                        # Тестирование в заголовках
                        headers = {
                            'X-Test': payload,
                            'User-Agent': payload,
                            'Referer': payload,
                            'X-Forwarded-For': payload,
                            'X-Real-IP': payload
                        }

                        async with self.session.request(
                            method,
                            endpoint,
                            headers=headers,
                            timeout=10
                        ) as response:
                            if await self._analyze_response(response, test['type'], payload):
                                return True

                        # Задержка между запросами
                        await asyncio.sleep(0.1)

                except asyncio.TimeoutError:
                    logger.debug(f"Timeout для {method} {endpoint}")
                    continue
                except Exception as e:
                    logger.debug(f"Ошибка {method} запроса к {endpoint}: {e}")
                    continue

            return False

        except Exception as e:
            logger.error(f"Ошибка тестирования API {endpoint}: {e}")
            return False

    def _generate_payloads(self, vuln_type: str) -> List[str]:
        """Генерация РЕАЛЬНЫХ payload из конфигурации"""
        # Используем payload из конфигурации
        payloads = get_payloads(vuln_type)

        if not payloads:
            # Fallback payload если не найдено в конфигурации
            payloads = [vuln_type]

        # Ограничиваем количество payload для производительности
        max_payloads = TESTING_CONFIG.get("max_payload_per_type", 10)
        return payloads[:max_payloads]

    async def _analyze_response(self, response, vuln_type: str, payload: str) -> bool:
        """РЕАЛЬНЫЙ анализ ответа на признаки уязвимости"""
        try:
            status_code = response.status
            headers = dict(response.headers)

            # Получаем содержимое ответа
            try:
                response_text = await response.text()
            except:
                response_text = ""

            # Анализ статус кода
            if self._analyze_status_code(status_code, vuln_type):
                logger.info(f"Подозрительный статус код {status_code} для {vuln_type}")
                return True

            # Анализ заголовков
            if self._analyze_headers(headers, vuln_type):
                logger.info(f"Подозрительные заголовки для {vuln_type}")
                return True

            # Анализ содержимого ответа
            if self._analyze_response_content(response_text, vuln_type, payload):
                logger.info(f"Подозрительное содержимое ответа для {vuln_type}")
                return True

            return False

        except Exception as e:
            logger.error(f"Ошибка анализа ответа: {e}")
            return False

    def _analyze_status_code(self, status_code: int, vuln_type: str) -> bool:
        """Анализ статус кода"""
        # SQL injection часто вызывает 500 ошибки
        if vuln_type == 'sql_injection' and status_code == 500:
            return True

        # Path traversal может вызывать 403/404
        if vuln_type == 'path_traversal' and status_code in [200, 403]:
            return True

        # Command injection может вызывать задержки (но мы не можем это проверить здесь)
        if vuln_type == 'command_injection' and status_code == 500:
            return True

        return False

    def _analyze_headers(self, headers: Dict, vuln_type: str) -> bool:
        """Анализ заголовков ответа"""
        # Поиск подозрительных заголовков
        suspicious_headers = [
            'x-debug', 'x-error', 'x-exception', 'x-sql-error',
            'x-mysql-error', 'x-oracle-error', 'x-mssql-error'
        ]

        for header_name, header_value in headers.items():
            if header_name.lower() in suspicious_headers:
                return True

            # Поиск ошибок в значениях заголовков
            if any(error in header_value.lower() for error in [
                'mysql', 'oracle', 'mssql', 'postgresql', 'sqlite',
                'syntax error', 'sql error', 'database error'
            ]):
                return True

        return False

    def _analyze_response_content(self, content: str, vuln_type: str, payload: str) -> bool:
        """РЕАЛЬНЫЙ анализ содержимого ответа"""
        content_lower = content.lower()

        # Используем индикаторы из конфигурации
        indicators = get_vulnerability_indicators(vuln_type)

        # Проверяем каждый индикатор
        for indicator in indicators:
            if indicator.lower() in content_lower:
                logger.info(f"Найден индикатор '{indicator}' для {vuln_type}")
                return True

        # Дополнительные проверки
        if vuln_type == 'xss' and payload.lower() in content_lower:
            logger.info(f"XSS payload отражен в ответе")
            return True

        # Проверка на отражение payload (только для значимых payload)
        if len(payload) > 5 and payload.lower() in content_lower:
            # Исключаем общие слова
            common_words = ['test', 'admin', 'user', 'data', 'input']
            if not any(word in payload.lower() for word in common_words):
                logger.info(f"Payload '{payload}' отражен в ответе")
                return True

        # Проверка на ошибки сервера
        error_indicators = [
            'internal server error', 'application error', 'runtime error',
            'exception', 'stack trace', 'debug', 'warning:', 'error:',
            'fatal error', 'parse error', 'compilation error'
        ]

        for error in error_indicators:
            if error in content_lower:
                logger.info(f"Найдена ошибка сервера: {error}")
                return True

        return False
    
    async def _test_documentation(self, program_details: Dict[str, Any]) -> List[VulnerabilityResult]:
        """Анализ документации на утечки информации"""
        results = []
        doc_urls = program_details.get('documentation_urls', [])
        program_name = program_details.get('name', 'Unknown')
        
        for doc_url in doc_urls:
            try:
                async with self.session.get(doc_url) as response:
                    if response.status == 200:
                        content = await response.text()
                        
                        # Поиск чувствительной информации
                        sensitive_patterns = [
                            (r'[A-Za-z0-9]{64}', 'private_key'),
                            (r'sk_[A-Za-z0-9]{48}', 'stripe_secret'),
                            (r'AKIA[A-Z0-9]{16}', 'aws_access_key'),
                            (r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}', 'email'),
                            (r'password\s*[:=]\s*["\']([^"\']+)["\']', 'password'),
                        ]
                        
                        import re
                        for pattern, vuln_type in sensitive_patterns:
                            matches = re.findall(pattern, content, re.IGNORECASE)
                            if matches:
                                result = VulnerabilityResult(
                                    program_name=program_name,
                                    vulnerability_type='information_disclosure',
                                    severity='Medium',
                                    description=f"Обнаружена утечка {vuln_type} в документации",
                                    proof_of_concept=f"URL: {doc_url}, Pattern: {pattern}",
                                    impact="Возможная утечка чувствительной информации",
                                    recommendation="Удалить чувствительную информацию из публичной документации"
                                )
                                results.append(result)
                
                await asyncio.sleep(random.uniform(1, 2))
                
            except Exception as e:
                logger.error(f"Ошибка анализа документации {doc_url}: {e}")
                continue
        
        return results
    
    async def _test_github_repos(self, program_details: Dict[str, Any]) -> List[VulnerabilityResult]:
        """Анализ GitHub репозиториев"""
        results = []
        github_repos = program_details.get('github_repos', [])
        program_name = program_details.get('name', 'Unknown')
        
        for repo_url in github_repos:
            try:
                # Получение информации о репозитории через GitHub API
                api_url = repo_url.replace('github.com', 'api.github.com/repos')
                
                async with self.session.get(api_url) as response:
                    if response.status == 200:
                        repo_data = await response.json()
                        
                        # Анализ на потенциальные проблемы безопасности
                        security_issues = []
                        
                        # Проверка на публичные секреты
                        if repo_data.get('private', True) == False:
                            security_issues.append('public_repo_with_secrets')
                        
                        # Проверка на отсутствие security.md
                        if not repo_data.get('has_security_policy', False):
                            security_issues.append('no_security_policy')
                        
                        # Проверка на старые коммиты
                        updated_at = repo_data.get('updated_at', '')
                        if updated_at:
                            from datetime import datetime, timedelta
                            last_update = datetime.fromisoformat(updated_at.replace('Z', '+00:00'))
                            if datetime.now().replace(tzinfo=last_update.tzinfo) - last_update > timedelta(days=365):
                                security_issues.append('outdated_repository')
                        
                        for issue in security_issues:
                            result = VulnerabilityResult(
                                program_name=program_name,
                                vulnerability_type='repository_security',
                                severity='Low',
                                description=f"Проблема безопасности репозитория: {issue}",
                                proof_of_concept=f"Repository: {repo_url}",
                                impact=self._get_repo_impact(issue),
                                recommendation=self._get_repo_recommendation(issue)
                            )
                            results.append(result)
                
                await asyncio.sleep(random.uniform(1, 2))
                
            except Exception as e:
                logger.error(f"Ошибка анализа репозитория {repo_url}: {e}")
                continue
        
        return results
    
    def _calculate_severity(self, vulnerability_type: str) -> str:
        """Расчет серьезности уязвимости"""
        severity_map = {
            'reentrancy': 'Critical',
            'access_control': 'High',
            'integer_overflow': 'High',
            'oracle_manipulation': 'Critical',
            'sql_injection': 'Critical',
            'xss': 'Medium',
            'path_traversal': 'High',
            'command_injection': 'Critical',
            'xxe': 'High',
            'information_disclosure': 'Medium',
            'repository_security': 'Low',
        }
        return severity_map.get(vulnerability_type, 'Medium')
    
    def _get_impact_description(self, vulnerability_type: str) -> str:
        """Получение описания воздействия уязвимости"""
        impact_map = {
            'reentrancy': 'Возможность повторного вызова функций для кражи средств',
            'access_control': 'Несанкционированный доступ к функциям контракта',
            'integer_overflow': 'Манипуляция числовыми значениями',
            'oracle_manipulation': 'Манипуляция ценовыми данными',
            'sql_injection': 'Доступ к базе данных и утечка информации',
            'xss': 'Выполнение вредоносного JavaScript кода',
            'path_traversal': 'Доступ к файлам системы',
            'command_injection': 'Выполнение произвольных команд на сервере',
            'xxe': 'Чтение локальных файлов и SSRF атаки',
        }
        return impact_map.get(vulnerability_type, 'Потенциальное нарушение безопасности')
    
    def _get_recommendation(self, vulnerability_type: str) -> str:
        """Получение рекомендаций по устранению"""
        recommendation_map = {
            'reentrancy': 'Использовать модификатор nonReentrant или checks-effects-interactions паттерн',
            'access_control': 'Реализовать правильные проверки доступа и роли',
            'integer_overflow': 'Использовать SafeMath библиотеку или Solidity 0.8+',
            'oracle_manipulation': 'Использовать несколько источников данных и временные задержки',
            'sql_injection': 'Использовать параметризованные запросы',
            'xss': 'Валидировать и экранировать пользовательский ввод',
            'path_traversal': 'Валидировать пути файлов и использовать whitelist',
            'command_injection': 'Валидировать ввод и избегать выполнения команд',
            'xxe': 'Отключить внешние сущности в XML парсере',
        }
        return recommendation_map.get(vulnerability_type, 'Провести дополнительный анализ безопасности')
    
    def _get_repo_impact(self, issue: str) -> str:
        """Получение описания воздействия проблемы репозитория"""
        impact_map = {
            'public_repo_with_secrets': 'Возможная утечка секретных ключей',
            'no_security_policy': 'Отсутствие процедур сообщения об уязвимостях',
            'outdated_repository': 'Возможные известные уязвимости в зависимостях',
        }
        return impact_map.get(issue, 'Потенциальная проблема безопасности')
    
    def _get_repo_recommendation(self, issue: str) -> str:
        """Получение рекомендаций для проблем репозитория"""
        recommendation_map = {
            'public_repo_with_secrets': 'Проверить историю коммитов на наличие секретов',
            'no_security_policy': 'Добавить SECURITY.md файл с инструкциями',
            'outdated_repository': 'Обновить зависимости и поддерживать актуальность',
        }
        return recommendation_map.get(issue, 'Улучшить практики безопасности репозитория')
    
    def save_results_to_json(self, filename: str = "immunefi_vulnerabilities.json"):
        """Сохранение результатов в JSON"""
        try:
            results_data = []
            for result in self.results:
                result_dict = {
                    'program_name': result.program_name,
                    'vulnerability_type': result.vulnerability_type,
                    'severity': result.severity,
                    'description': result.description,
                    'proof_of_concept': result.proof_of_concept,
                    'impact': result.impact,
                    'recommendation': result.recommendation,
                    'contract_address': result.contract_address,
                    'transaction_hash': result.transaction_hash,
                    'timestamp': result.timestamp.isoformat(),
                }
                results_data.append(result_dict)
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Результаты сохранены в {filename}")
            
        except Exception as e:
            logger.error(f"Ошибка сохранения результатов: {e}")
    
    def generate_report(self) -> str:
        """Генерация отчета о найденных уязвимостях"""
        if not self.results:
            return "Уязвимости не найдены"
        
        report = f"# Отчет о тестировании Immunefi программ\n\n"
        report += f"Дата: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        report += f"Всего протестировано программ: {len(self.tested_programs)}\n"
        report += f"Всего найдено потенциальных уязвимостей: {len(self.results)}\n\n"
        
        # Группировка по серьезности
        severity_counts = {}
        for result in self.results:
            severity_counts[result.severity] = severity_counts.get(result.severity, 0) + 1
        
        report += "## Распределение по серьезности:\n"
        for severity, count in sorted(severity_counts.items()):
            report += f"- {severity}: {count}\n"
        
        report += "\n## Детальные результаты:\n\n"
        
        for i, result in enumerate(self.results, 1):
            report += f"### {i}. {result.program_name} - {result.vulnerability_type}\n"
            report += f"**Серьезность:** {result.severity}\n"
            report += f"**Описание:** {result.description}\n"
            report += f"**Воздействие:** {result.impact}\n"
            report += f"**Рекомендация:** {result.recommendation}\n"
            if result.contract_address:
                report += f"**Контракт:** {result.contract_address}\n"
            report += f"**PoC:** {result.proof_of_concept}\n\n"
        
        return report

async def main():
    """Основная функция для тестирования"""
    logger.info("Запуск Immunefi Vulnerability Tester...")
    
    # Пример программы для тестирования
    test_program = {
        'name': 'Test Program',
        'url': 'https://immunefi.com/bounty/test/',
        'contracts': ['0x1234567890123456789012345678901234567890'],
        'endpoints': ['https://api.test.com/v1'],
        'documentation_urls': ['https://docs.test.com'],
        'github_repos': ['https://github.com/test/repo'],
    }
    
    async with ImmunefiBountyTester() as tester:
        results = await tester.test_program(test_program)
        
        if results:
            tester.save_results_to_json()
            report = tester.generate_report()
            
            with open('immunefi_test_report.md', 'w', encoding='utf-8') as f:
                f.write(report)
            
            logger.info(f"Тестирование завершено. Найдено {len(results)} потенциальных уязвимостей")
        else:
            logger.info("Уязвимости не найдены")

if __name__ == "__main__":
    asyncio.run(main())
