/**
 * 🧪 ТЕСТИРОВАНИЕ ИНСТРУКЦИЙ ДЛЯ FLASH LOAN АРБИТРАЖА
 * 
 * Этот файл создает и тестирует каждую инструкцию отдельно
 * перед объединением их в единую транзакцию
 */

const { Connection, PublicKey, Transaction, SystemProgram } = require('@solana/web3.js');
const { BN } = require('@coral-xyz/anchor');
const { MeteoraInstructionTester } = require('./meteora-instruction-tester');

class InstructionTester {
    constructor() {
        this.connection = new Connection('https://billowing-empty-patron.solana-mainnet.quiknode.pro/c79212a7508ec6a37353793d9be93c917898e858/', 'confirmed');

        // 🔑 ОСНОВНЫЕ АДРЕСА
        this.MARGINFI_PROGRAM_ID = new PublicKey('MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA');
        this.METEORA_PROGRAM_ID = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');
        this.USDC_MINT = new PublicKey('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v');
        this.SOL_MINT = new PublicKey('So********************************111111112');

        // 🏦 РЕАЛЬНЫЕ MARGINFI АККАУНТЫ
        this.marginfiAccount = new PublicKey('********************************************');
        this.walletPublicKey = new PublicKey('bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV'); // Наш кошелек

        // 🏦 РЕАЛЬНЫЕ БАНКИ MARGINFI
        this.BANKS = {
            USDC: new PublicKey('2s37akK2eyBbp8DZgCm7RtsaEz8eJP3Nxd4urLHQv7yB'),
            SOL: new PublicKey('CCKtUs6Cgwo4aaQUmBPmyoApH2gUDErxNZCAntD6LYGh'),
            USDT: new PublicKey('HmpMfL8942u22htC4EMiWgLX931g3sacXFR6KjuLgKLV')
        };

        // 🔧 СИСТЕМНЫЕ АККАУНТЫ
        this.INSTRUCTIONS_SYSVAR = new PublicKey('Sysvar1nstructions1111111111111111111111111');
        this.TOKEN_PROGRAM = new PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA');
        this.SYSTEM_PROGRAM = new PublicKey('********************************');

        // 🌊 METEORA ТЕСТЕР
        this.meteoraTester = new MeteoraInstructionTester();

        console.log('🧪 InstructionTester инициализирован с РЕАЛЬНЫМИ адресами и Meteora SDK');
        console.log('🚀 Продвинутые методы без симуляции активированы!');
    }

    /**
     * 🔥 1. СОЗДАНИЕ FLASH LOAN START ИНСТРУКЦИИ
     */
    createFlashLoanStartInstruction(endIndex = 6) {
        console.log(`\n🔥 СОЗДАНИЕ FLASH LOAN START ИНСТРУКЦИИ (endIndex: ${endIndex})`);
        
        try {
            // 🔥 ПРАВИЛЬНЫЙ DISCRIMINATOR ИЗ ANCHOR IDL
            const discriminator = Buffer.from([0x0e, 0x83, 0x21, 0xdc, 0x51, 0xba, 0xb4, 0x6b]);
            
            // 🔥 END INDEX (8 байт, little-endian)
            const endIndexBuffer = Buffer.alloc(8);
            endIndexBuffer.writeBigUInt64LE(BigInt(endIndex), 0);
            
            // 🔥 ОБЪЕДИНЯЕМ DATA
            const data = Buffer.concat([discriminator, endIndexBuffer]);
            
            // 🔥 АККАУНТЫ ДЛЯ START FLASH LOAN
            const accounts = [
                // 0: marginfi_account (writable)
                { pubkey: this.marginfiAccount, isSigner: false, isWritable: true },
                // 1: authority (signer)
                { pubkey: this.walletPublicKey, isSigner: true, isWritable: false },
                // 2: instructions_sysvar
                { pubkey: new PublicKey('Sysvar1nstructions1111111111111111111111111'), isSigner: false, isWritable: false }
            ];
            
            const instruction = {
                programId: this.MARGINFI_PROGRAM_ID,
                keys: accounts,
                data: data
            };
            
            console.log(`✅ Flash Loan Start инструкция создана:`);
            console.log(`   - Discriminator: ${discriminator.toString('hex')}`);
            console.log(`   - End Index: ${endIndex}`);
            console.log(`   - Аккаунтов: ${accounts.length}`);
            console.log(`   - Размер data: ${data.length} байт`);
            
            return {
                success: true,
                instruction,
                details: {
                    discriminator: discriminator.toString('hex'),
                    endIndex,
                    accountsCount: accounts.length,
                    dataSize: data.length
                }
            };
            
        } catch (error) {
            console.error(`❌ Ошибка создания Flash Loan Start инструкции:`, error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 🏦 2. СОЗДАНИЕ BORROW ИНСТРУКЦИИ
     */
    createBorrowInstruction(amount, tokenSymbol = 'USDC') {
        console.log(`\n🏦 СОЗДАНИЕ BORROW ИНСТРУКЦИИ (${amount} микроюнитов ${tokenSymbol})`);

        try {
            // 🔥 ПРАВИЛЬНЫЙ DISCRIMINATOR ДЛЯ lending_account_borrow (из официального IDL)
            const discriminator = Buffer.from([0x04, 0x7e, 0x74, 0x35, 0x30, 0x05, 0xd4, 0x1f]);

            // 🔥 AMOUNT (8 байт, little-endian)
            const amountBuffer = Buffer.alloc(8);
            amountBuffer.writeBigUInt64LE(BigInt(amount), 0);

            // 🔥 ОБЪЕДИНЯЕМ DATA
            const data = Buffer.concat([discriminator, amountBuffer]);

            // 🏦 ПОЛУЧАЕМ БАНК ПО СИМВОЛУ
            const bankAddress = this.BANKS[tokenSymbol];
            if (!bankAddress) {
                throw new Error(`Банк для ${tokenSymbol} не найден`);
            }

            // 🔥 ПОЛНЫЕ АККАУНТЫ ДЛЯ BORROW (по образцу MarginFi SDK)
            const accounts = [
                // 0: marginfi_account (writable)
                { pubkey: this.marginfiAccount, isSigner: false, isWritable: true },
                // 1: authority (signer)
                { pubkey: this.walletPublicKey, isSigner: true, isWritable: false },
                // 2: bank (writable)
                { pubkey: bankAddress, isSigner: false, isWritable: true },
                // 3: destination_token_account (writable) - куда поступят токены
                { pubkey: this.walletPublicKey, isSigner: false, isWritable: true }, // Упрощение
                // 4: bank_liquidity_vault_authority
                { pubkey: this.SYSTEM_PROGRAM, isSigner: false, isWritable: false }, // Упрощение
                // 5: bank_liquidity_vault (writable)
                { pubkey: this.SYSTEM_PROGRAM, isSigner: false, isWritable: true }, // Упрощение
                // 6: token_program
                { pubkey: this.TOKEN_PROGRAM, isSigner: false, isWritable: false },
                // Remaining accounts для health check будут добавлены позже
            ];

            const instruction = {
                programId: this.MARGINFI_PROGRAM_ID,
                keys: accounts,
                data: data
            };

            console.log(`✅ Borrow инструкция создана:`);
            console.log(`   - Токен: ${tokenSymbol}`);
            console.log(`   - Сумма: ${amount} микроюнитов`);
            console.log(`   - Банк: ${bankAddress.toString()}`);
            console.log(`   - Аккаунтов: ${accounts.length}`);
            console.log(`   - Discriminator: ${discriminator.toString('hex')}`);

            return {
                success: true,
                instruction,
                details: {
                    tokenSymbol,
                    amount,
                    bankAddress: bankAddress.toString(),
                    accountsCount: accounts.length,
                    discriminator: discriminator.toString('hex')
                }
            };

        } catch (error) {
            console.error(`❌ Ошибка создания Borrow инструкции:`, error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 💧 3. СОЗДАНИЕ ADD LIQUIDITY ИНСТРУКЦИИ (РЕАЛЬНАЯ METEORA SDK)
     */
    async createAddLiquidityInstruction(poolAddress, amountX, amountY) {
        console.log(`\n💧 СОЗДАНИЕ ADD LIQUIDITY ИНСТРУКЦИИ`);
        console.log(`   - Пул: ${poolAddress}`);
        console.log(`   - Amount X: ${amountX}`);
        console.log(`   - Amount Y: ${amountY}`);

        try {
            // 🌊 ИСПОЛЬЗУЕМ РЕАЛЬНЫЙ METEORA SDK
            const result = await this.meteoraTester.createRealAddLiquidityInstruction(
                poolAddress,
                amountX,
                amountY
            );

            if (result.success) {
                console.log(`✅ Add Liquidity инструкция создана через ${result.details.sdk}`);
                console.log(`   - Инструкций: ${result.details.instructionsCount}`);

                return {
                    success: true,
                    instructions: result.instructions,
                    details: {
                        poolAddress,
                        amountX,
                        amountY,
                        instructionsCount: result.details.instructionsCount,
                        sdk: result.details.sdk,
                        userTokenX: result.details.userTokenX,
                        userTokenY: result.details.userTokenY
                    }
                };
            } else {
                throw new Error('Meteora SDK failed');
            }

        } catch (error) {
            console.error(`❌ Ошибка создания Add Liquidity инструкции:`, error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 💰 4. СОЗДАНИЕ REPAY ИНСТРУКЦИИ
     */
    createRepayInstruction(amount, tokenSymbol = 'USDC', repayAll = true) {
        console.log(`\n💰 СОЗДАНИЕ REPAY ИНСТРУКЦИИ (${amount} микроюнитов ${tokenSymbol})`);

        try {
            // 🔥 ПРАВИЛЬНЫЙ DISCRIMINATOR ДЛЯ lending_account_repay (из официального IDL)
            const discriminator = Buffer.from([0x4f, 0xd1, 0xac, 0xb1, 0xde, 0x33, 0xad, 0x97]);

            // 🔥 AMOUNT (8 байт, little-endian)
            const amountBuffer = Buffer.alloc(8);
            amountBuffer.writeBigUInt64LE(BigInt(amount), 0);

            // 🔥 REPAY_ALL (1 байт boolean)
            const repayAllBuffer = Buffer.from([repayAll ? 1 : 0]);

            // 🔥 ОБЪЕДИНЯЕМ DATA
            const data = Buffer.concat([discriminator, amountBuffer, repayAllBuffer]);

            // 🏦 ПОЛУЧАЕМ БАНК ПО СИМВОЛУ
            const bankAddress = this.BANKS[tokenSymbol];
            if (!bankAddress) {
                throw new Error(`Банк для ${tokenSymbol} не найден`);
            }

            // 🔥 ПОЛНЫЕ АККАУНТЫ ДЛЯ REPAY (по образцу MarginFi SDK)
            const accounts = [
                // 0: marginfi_account (writable)
                { pubkey: this.marginfiAccount, isSigner: false, isWritable: true },
                // 1: authority (signer)
                { pubkey: this.walletPublicKey, isSigner: true, isWritable: false },
                // 2: bank (writable)
                { pubkey: bankAddress, isSigner: false, isWritable: true },
                // 3: signer_token_account (writable) - откуда берем токены для возврата
                { pubkey: this.walletPublicKey, isSigner: false, isWritable: true }, // Упрощение
                // 4: bank_liquidity_vault (writable)
                { pubkey: this.SYSTEM_PROGRAM, isSigner: false, isWritable: true }, // Упрощение
                // 5: token_program
                { pubkey: this.TOKEN_PROGRAM, isSigner: false, isWritable: false },
                // Remaining accounts для health check будут добавлены позже
            ];

            const instruction = {
                programId: this.MARGINFI_PROGRAM_ID,
                keys: accounts,
                data: data
            };

            console.log(`✅ Repay инструкция создана:`);
            console.log(`   - Токен: ${tokenSymbol}`);
            console.log(`   - Сумма: ${amount} микроюнитов`);
            console.log(`   - Repay All: ${repayAll}`);
            console.log(`   - Банк: ${bankAddress.toString()}`);
            console.log(`   - Аккаунтов: ${accounts.length}`);
            console.log(`   - Discriminator: ${discriminator.toString('hex')}`);

            return {
                success: true,
                instruction,
                details: {
                    tokenSymbol,
                    amount,
                    repayAll,
                    bankAddress: bankAddress.toString(),
                    accountsCount: accounts.length,
                    discriminator: discriminator.toString('hex')
                }
            };

        } catch (error) {
            console.error(`❌ Ошибка создания Repay инструкции:`, error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 🔄 5. СОЗДАНИЕ SWAP ИНСТРУКЦИИ (РЕАЛЬНАЯ METEORA SDK)
     */
    async createSwapInstruction(poolAddress, amountIn, tokenIn, tokenOut) {
        console.log(`\n🔄 СОЗДАНИЕ SWAP ИНСТРУКЦИИ`);
        console.log(`   - Пул: ${poolAddress}`);
        console.log(`   - Сумма: ${amountIn}`);
        console.log(`   - Направление: ${tokenIn} -> ${tokenOut}`);

        try {
            // 🔍 ОПРЕДЕЛЯЕМ НАПРАВЛЕНИЕ SWAP (Y->X или X->Y)
            const swapYtoX = (tokenIn === 'USDC' && tokenOut === 'SOL'); // USDC обычно tokenY, SOL tokenX

            // 🌊 ИСПОЛЬЗУЕМ РЕАЛЬНЫЙ METEORA SDK
            const result = await this.meteoraTester.createRealSwapInstruction(
                poolAddress,
                amountIn,
                swapYtoX
            );

            if (result.success) {
                console.log(`✅ Swap инструкция создана через ${result.details.sdk}`);
                console.log(`   - Аккаунтов: ${result.details.accountsCount || 'N/A'}`);
                console.log(`   - Amount Out: ${result.details.amountOut || 'N/A'}`);

                return {
                    success: true,
                    instruction: result.instruction,
                    details: {
                        poolAddress,
                        amountIn,
                        amountOut: result.details.amountOut,
                        tokenIn,
                        tokenOut,
                        swapYtoX,
                        accountsCount: result.details.accountsCount,
                        sdk: result.details.sdk,
                        binArraysCount: result.details.binArraysCount
                    }
                };
            } else {
                throw new Error('Meteora SDK failed');
            }

        } catch (error) {
            console.error(`❌ Ошибка создания Swap инструкции:`, error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 💸 6. СОЗДАНИЕ REMOVE LIQUIDITY ИНСТРУКЦИИ (РЕАЛЬНАЯ METEORA SDK)
     */
    async createRemoveLiquidityInstruction(poolAddress, lpTokenAmount) {
        console.log(`\n💸 СОЗДАНИЕ REMOVE LIQUIDITY ИНСТРУКЦИИ`);
        console.log(`   - Пул: ${poolAddress}`);
        console.log(`   - LP Token Amount: ${lpTokenAmount}`);

        try {
            // 🌊 ИСПОЛЬЗУЕМ РЕАЛЬНЫЙ METEORA SDK
            const result = await this.meteoraTester.createRealRemoveLiquidityInstruction(
                poolAddress,
                lpTokenAmount
            );

            if (result.success) {
                console.log(`✅ Remove Liquidity инструкция создана через ${result.details.sdk}`);
                console.log(`   - Инструкций: ${result.details.instructionsCount}`);

                return {
                    success: true,
                    instructions: result.instructions,
                    details: {
                        poolAddress,
                        lpTokenAmount,
                        instructionsCount: result.details.instructionsCount,
                        sdk: result.details.sdk,
                        userTokenX: result.details.userTokenX,
                        userTokenY: result.details.userTokenY
                    }
                };
            } else {
                throw new Error('Meteora SDK failed');
            }

        } catch (error) {
            console.error(`❌ Ошибка создания Remove Liquidity инструкции:`, error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 🔚 7. СОЗДАНИЕ FLASH LOAN END ИНСТРУКЦИИ
     */
    createFlashLoanEndInstruction(projectedActiveBalances = []) {
        console.log(`\n🔚 СОЗДАНИЕ FLASH LOAN END ИНСТРУКЦИИ`);

        try {
            // 🔥 ПРАВИЛЬНЫЙ DISCRIMINATOR ДЛЯ lending_account_end_flashloan (из официального IDL)
            const discriminator = Buffer.from([0x69, 0x7c, 0xc9, 0x6a, 0x99, 0x02, 0x08, 0x9c]);

            // 🔥 ДЛЯ ТЕСТИРОВАНИЯ ИСПОЛЬЗУЕМ ПУСТЫЕ PROJECTED BALANCES
            const data = Buffer.from(discriminator);

            // 🔥 БАЗОВЫЕ АККАУНТЫ ДЛЯ END FLASH LOAN
            const accounts = [
                // 0: marginfi_account (writable)
                { pubkey: this.marginfiAccount, isSigner: false, isWritable: true },
                // 1: authority (signer)
                { pubkey: this.walletPublicKey, isSigner: true, isWritable: false },
                // 2: instructions_sysvar
                { pubkey: this.INSTRUCTIONS_SYSVAR, isSigner: false, isWritable: false }
            ];

            // 🔥 ДОБАВЛЯЕМ PROJECTED ACTIVE BALANCES КАК REMAINING ACCOUNTS
            if (projectedActiveBalances.length > 0) {
                projectedActiveBalances.forEach(balance => {
                    accounts.push({
                        pubkey: balance,
                        isSigner: false,
                        isWritable: true
                    });
                });
            } else {
                // 🔥 ДЛЯ ТЕСТИРОВАНИЯ ДОБАВЛЯЕМ ОСНОВНЫЕ БАНКИ
                Object.values(this.BANKS).forEach(bankAddress => {
                    accounts.push({
                        pubkey: bankAddress,
                        isSigner: false,
                        isWritable: true
                    });
                });
            }

            const instruction = {
                programId: this.MARGINFI_PROGRAM_ID,
                keys: accounts,
                data: data
            };

            console.log(`✅ Flash Loan End инструкция создана:`);
            console.log(`   - Projected Balances: ${projectedActiveBalances.length}`);
            console.log(`   - Аккаунтов: ${accounts.length}`);
            console.log(`   - Discriminator: ${discriminator.toString('hex')}`);

            return {
                success: true,
                instruction,
                details: {
                    projectedBalancesCount: projectedActiveBalances.length,
                    accountsCount: accounts.length,
                    discriminator: discriminator.toString('hex')
                }
            };

        } catch (error) {
            console.error(`❌ Ошибка создания Flash Loan End инструкции:`, error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 🔗 8. СОЗДАНИЕ ПОЛНОЙ FLASH LOAN ТРАНЗАКЦИИ
     */
    async createFullFlashLoanTransaction(arbitrageAmount = 1000000) {
        console.log(`\n🔗 СОЗДАНИЕ ПОЛНОЙ FLASH LOAN ТРАНЗАКЦИИ (${arbitrageAmount} микроюнитов)`);

        try {
            const instructions = [];
            const details = [];

            // 1. Flash Loan Start (endIndex = количество инструкций - 1)
            const totalInstructions = 7; // start + borrow + add + swap + remove + repay + end
            const flashLoanStart = this.createFlashLoanStartInstruction(totalInstructions - 1);
            if (!flashLoanStart.success) throw new Error('Flash Loan Start failed');
            instructions.push(flashLoanStart.instruction);
            details.push({ step: 1, name: 'Flash Loan Start', ...flashLoanStart.details });

            // 2. Borrow USDC
            const borrow = this.createBorrowInstruction(arbitrageAmount, 'USDC');
            if (!borrow.success) throw new Error('Borrow failed');
            instructions.push(borrow.instruction);
            details.push({ step: 2, name: 'Borrow USDC', ...borrow.details });

            // 3. Add Liquidity (MEGA Meteora DLMM пул $7.1M TVL - ИДЕАЛЬНЫЙ ДЛЯ АРБИТРАЖА!)
            const addLiquidity = await this.createAddLiquidityInstruction('5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6', arbitrageAmount/2, 3000);
            if (!addLiquidity.success) throw new Error('Add Liquidity failed');
            addLiquidity.instructions.forEach(ix => instructions.push(ix));
            details.push({ step: 3, name: 'Add Liquidity MEGA Pool', ...addLiquidity.details });

            // 4. Swap USDC->SOL (MEGA Meteora DLMM пул $7.1M TVL - МИНИМАЛЬНЫЙ SLIPPAGE!)
            const swap = await this.createSwapInstruction('5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6', arbitrageAmount/10, 'USDC', 'SOL');
            if (!swap.success) throw new Error('Swap failed');
            instructions.push(swap.instruction);
            details.push({ step: 4, name: 'Swap USDC->SOL MEGA Pool', ...swap.details });

            // 5. Remove Liquidity (MEGA Meteora DLMM пул $7.1M TVL - ОПТИМАЛЬНАЯ ЛИКВИДНОСТЬ!)
            const removeLiquidity = await this.createRemoveLiquidityInstruction('5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6', arbitrageAmount/2);
            if (!removeLiquidity.success) throw new Error('Remove Liquidity failed');
            removeLiquidity.instructions.forEach(ix => instructions.push(ix));
            details.push({ step: 5, name: 'Remove Liquidity MEGA Pool', ...removeLiquidity.details });

            // 6. Repay USDC (с комиссией 0.09%)
            const repayAmount = Math.floor(arbitrageAmount * 1.0009); // +0.09% комиссия
            const repay = this.createRepayInstruction(repayAmount, 'USDC', true);
            if (!repay.success) throw new Error('Repay failed');
            instructions.push(repay.instruction);
            details.push({ step: 6, name: 'Repay USDC', ...repay.details });

            // 7. Flash Loan End
            const flashLoanEnd = this.createFlashLoanEndInstruction();
            if (!flashLoanEnd.success) throw new Error('Flash Loan End failed');
            instructions.push(flashLoanEnd.instruction);
            details.push({ step: 7, name: 'Flash Loan End', ...flashLoanEnd.details });

            // 🔗 СОЗДАЕМ ТРАНЗАКЦИЮ
            const transaction = new Transaction();

            // Добавляем Compute Budget для тяжелых операций
            const { ComputeBudgetProgram } = require('@solana/web3.js');
            const computeBudgetIx = ComputeBudgetProgram.setComputeUnitLimit({
                units: 1400000 // 1.4M compute units
            });
            transaction.add(computeBudgetIx);

            // Добавляем все инструкции
            instructions.forEach(ix => transaction.add(ix));

            console.log(`✅ Полная Flash Loan транзакция создана:`);
            console.log(`   - Всего инструкций: ${instructions.length + 1} (включая compute budget)`);
            console.log(`   - Arbitrage Amount: ${arbitrageAmount} микроюнитов`);
            console.log(`   - Repay Amount: ${repayAmount} микроюнитов (+0.09%)`);

            return {
                success: true,
                transaction,
                instructions,
                details,
                summary: {
                    totalInstructions: instructions.length + 1,
                    arbitrageAmount,
                    repayAmount,
                    flashLoanFee: repayAmount - arbitrageAmount,
                    feePercentage: 0.09
                }
            };

        } catch (error) {
            console.error(`❌ Ошибка создания полной транзакции:`, error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 🧪 ТЕСТИРОВАНИЕ СОЗДАНИЯ ВСЕХ ИНСТРУКЦИЙ
     */
    async testAllInstructions() {
        console.log('\n🧪 НАЧИНАЕМ ТЕСТИРОВАНИЕ ВСЕХ ИНСТРУКЦИЙ ПО ПЛАНУ АРБИТРАЖА...\n');

        const results = {};

        // 1. Тестируем Flash Loan Start (endIndex = 6 для 7 инструкций: 0-6)
        console.log('=' .repeat(60));
        results.flashLoanStart = this.createFlashLoanStartInstruction(6);

        // 2. Тестируем Borrow (получаем USDC из MarginFi)
        console.log('=' .repeat(60));
        results.borrow = this.createBorrowInstruction(1000000, 'USDC'); // 1 USDC

        // 3. Тестируем Add Liquidity (реальный Meteora DLMM пул $2.9M TVL)
        console.log('=' .repeat(60));
        results.addLiquidity = await this.createAddLiquidityInstruction(
            'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y', // Основной пул SOL-USDC $2.9M TVL
            500000, // 0.5 USDC
            3000    // 3 SOL (в lamports)
        );

        // 4. Тестируем Swap (USDC -> SOL по повышенной цене)
        console.log('=' .repeat(60));
        results.swap = await this.createSwapInstruction(
            'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y', // Основной пул SOL-USDC $2.9M TVL
            100000, // 0.1 USDC
            'USDC',
            'SOL'
        );

        // 5. Тестируем Remove Liquidity (убираем ликвидность)
        console.log('=' .repeat(60));
        results.removeLiquidity = await this.createRemoveLiquidityInstruction(
            'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y', // Основной пул SOL-USDC $2.9M TVL
            500000 // LP токены для удаления
        );

        // 6. Тестируем Repay (возвращаем займ в MarginFi)
        console.log('=' .repeat(60));
        results.repay = this.createRepayInstruction(1000900, 'USDC', true); // 1 USDC + 0.09% комиссия

        // 7. Тестируем Flash Loan End
        console.log('=' .repeat(60));
        results.flashLoanEnd = this.createFlashLoanEndInstruction();

        // 📊 ИТОГОВЫЙ ОТЧЕТ
        console.log('\n📊 ИТОГОВЫЙ ОТЧЕТ ТЕСТИРОВАНИЯ FLASH LOAN АРБИТРАЖА:');
        console.log('=' .repeat(80));

        Object.entries(results).forEach(([name, result]) => {
            const status = result.success ? '✅' : '❌';
            console.log(`${status} ${name.padEnd(20)}: ${result.success ? 'SUCCESS' : result.error}`);

            if (result.success && result.details) {
                console.log(`   └─ Аккаунтов: ${result.details.accountsCount || 'N/A'}`);
                if (result.details.discriminator) {
                    console.log(`   └─ Discriminator: ${result.details.discriminator}`);
                }
            }
        });

        // 📈 СТАТИСТИКА
        const successCount = Object.values(results).filter(r => r.success).length;
        const totalCount = Object.keys(results).length;

        console.log('\n📈 СТАТИСТИКА:');
        console.log(`   Успешно: ${successCount}/${totalCount}`);
        console.log(`   Процент успеха: ${((successCount/totalCount)*100).toFixed(1)}%`);

        return results;
    }
}

// 🚀 ЗАПУСК ТЕСТИРОВАНИЯ
async function main() {
    console.log('🚀 ЗАПУСК ТЕСТИРОВАНИЯ ИНСТРУКЦИЙ...\n');

    const tester = new InstructionTester();

    // 1. Тестируем отдельные инструкции
    const results = await tester.testAllInstructions();

    // Проверяем успешность отдельных инструкций
    const successCount = Object.values(results).filter(r => r.success).length;
    const totalCount = Object.keys(results).length;

    console.log(`📈 Отдельные инструкции: ${successCount}/${totalCount}`);

    if (successCount === totalCount) {
        console.log('🎉 ВСЕ ОТДЕЛЬНЫЕ ИНСТРУКЦИИ СОЗДАНЫ УСПЕШНО!');

        // 2. Тестируем полную транзакцию
        console.log('\n' + '='.repeat(80));
        console.log('🔗 ТЕСТИРОВАНИЕ ПОЛНОЙ FLASH LOAN ТРАНЗАКЦИИ');
        console.log('='.repeat(80));

        const fullTransaction = tester.createFullFlashLoanTransaction(1000000); // 1 USDC

        if (fullTransaction.success) {
            console.log('\n✅ ПОЛНАЯ ТРАНЗАКЦИЯ СОЗДАНА УСПЕШНО!');
            console.log('\n📊 ДЕТАЛИ ТРАНЗАКЦИИ:');
            console.log(`   💰 Arbitrage Amount: ${fullTransaction.summary.arbitrageAmount} микроюнитов`);
            console.log(`   💸 Repay Amount: ${fullTransaction.summary.repayAmount} микроюнитов`);
            console.log(`   🏦 Flash Loan Fee: ${fullTransaction.summary.flashLoanFee} микроюнитов`);
            console.log(`   📊 Fee Percentage: ${fullTransaction.summary.feePercentage}%`);
            console.log(`   🔧 Total Instructions: ${fullTransaction.summary.totalInstructions}`);

            console.log('\n📋 ПОСЛЕДОВАТЕЛЬНОСТЬ ИНСТРУКЦИЙ:');
            fullTransaction.details.forEach(detail => {
                console.log(`   ${detail.step}. ${detail.name}`);
                if (detail.discriminator) {
                    console.log(`      └─ Discriminator: ${detail.discriminator}`);
                }
                if (detail.accountsCount) {
                    console.log(`      └─ Аккаунтов: ${detail.accountsCount}`);
                }
            });

            console.log('\n🎯 ГОТОВО К ИНТЕГРАЦИИ С METEORA SDK!');

        } else {
            console.log(`❌ ОШИБКА СОЗДАНИЯ ПОЛНОЙ ТРАНЗАКЦИИ: ${fullTransaction.error}`);
        }

    } else {
        console.log('⚠️  НЕКОТОРЫЕ ИНСТРУКЦИИ ТРЕБУЮТ ДОРАБОТКИ');
    }

    console.log('\n🎯 ТЕСТИРОВАНИЕ ЗАВЕРШЕНО!');
}

// Запускаем только если файл вызван напрямую
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { InstructionTester };
