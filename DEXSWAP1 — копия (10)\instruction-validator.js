/**
 * 🔍 INSTRUCTION VALIDATOR
 * ═══════════════════════════════════════════════════════════════════════════
 * 🎯 ЦЕЛЬ: Валидация инструкций перед передачей в MarginFi SDK
 * 🔧 ФУНКЦИЯ: Проверка корректности TransactionInstruction объектов
 * 📋 ПРИМЕНЕНИЕ: Предотвращение ошибок "ix.programId.equals is not a function"
 */

const { PublicKey, TransactionInstruction } = require('@solana/web3.js');

/**
 * 🔍 INSTRUCTION VALIDATOR CLASS
 * Комплексная валидация инструкций для MarginFi SDK
 */
class InstructionValidator {

  /**
   * 🔍 ВАЛИДАЦИЯ ОДНОЙ ИНСТРУКЦИИ
   * @param {TransactionInstruction} instruction - Инструкция для валидации
   * @returns {Object} - Результат валидации {isValid: boolean, errors: Array}
   */
  static validateInstruction(instruction) {
    const errors = [];

    try {
      // 1. Проверка типа
      if (!(instruction instanceof TransactionInstruction)) {
        errors.push('Instruction is not a TransactionInstruction instance');
        return { isValid: false, errors };
      }

      // 2. Валидация programId
      const programIdValidation = this.validateProgramId(instruction.programId);
      if (!programIdValidation.isValid) {
        errors.push(...programIdValidation.errors);
      }

      // 3. Валидация keys
      const keysValidation = this.validateKeys(instruction.keys);
      if (!keysValidation.isValid) {
        errors.push(...keysValidation.errors);
      }

      // 4. Валидация data
      const dataValidation = this.validateData(instruction.data);
      if (!dataValidation.isValid) {
        errors.push(...dataValidation.errors);
      }

      // 5. ИСПРАВЛЕНИЕ: ComputeBudget инструкции ДОЛЖНЫ иметь 0 keys - это нормально!
      if (instruction.keys.length === 0) {
        // Проверяем, это ComputeBudget инструкция?
        const programIdStr = instruction.programId.toString();
        if (programIdStr === 'ComputeBudget111111111111111111111111111111') {
          // ComputeBudget инструкции ВСЕГДА имеют 0 keys - это правильно!
          console.log(`✅ ComputeBudget инструкция с 0 keys - это нормально!`);
        } else {
          errors.push('Instruction has no keys (empty keys array)');
        }
      }

      const isValid = errors.length === 0;
      
      if (isValid) {
        console.log(`✅ Инструкция валидна: ${instruction.programId.toBase58()}`);
      } else {
        console.warn(`⚠️ Инструкция невалидна: ${errors.join(', ')}`);
      }

      return { isValid, errors };

    } catch (error) {
      errors.push(`Validation error: ${error.message}`);
      return { isValid: false, errors };
    }
  }

  /**
   * 🔍 ВАЛИДАЦИЯ МАССИВА ИНСТРУКЦИЙ
   * @param {Array<TransactionInstruction>} instructions - Массив инструкций
   * @returns {Object} - Результат валидации {isValid: boolean, validInstructions: Array, errors: Array}
   */
  static validateInstructions(instructions) {
    if (!Array.isArray(instructions)) {
      return {
        isValid: false,
        validInstructions: [],
        errors: ['Instructions must be an array']
      };
    }

    console.log(`🔍 Валидируем ${instructions.length} инструкций...`);

    const validInstructions = [];
    const allErrors = [];

    for (let i = 0; i < instructions.length; i++) {
      const validation = this.validateInstruction(instructions[i]);
      
      if (validation.isValid) {
        validInstructions.push(instructions[i]);
      } else {
        allErrors.push(`Instruction ${i}: ${validation.errors.join(', ')}`);
      }
    }

    const isValid = validInstructions.length > 0;

    console.log(`✅ Валидация завершена: ${validInstructions.length} валидных из ${instructions.length}`);
    
    if (allErrors.length > 0) {
      console.warn(`⚠️ Найдены ошибки валидации: ${allErrors.length}`);
    }

    return {
      isValid,
      validInstructions,
      errors: allErrors
    };
  }

  /**
   * 🔍 ВАЛИДАЦИЯ PROGRAM ID
   * @param {PublicKey} programId - Program ID для валидации
   * @returns {Object} - Результат валидации
   */
  static validateProgramId(programId) {
    const errors = [];

    try {
      // Проверка что это PublicKey
      if (!(programId instanceof PublicKey)) {
        errors.push('programId is not a PublicKey instance');
        return { isValid: false, errors };
      }

      // Проверка что у PublicKey есть equals метод
      if (typeof programId.equals !== 'function') {
        errors.push('programId.equals is not a function');
      }

      // Проверка что у PublicKey есть toBase58 метод
      if (typeof programId.toBase58 !== 'function') {
        errors.push('programId.toBase58 is not a function');
      }

      // Проверка что programId не null/undefined
      if (!programId) {
        errors.push('programId is null or undefined');
      }

      // Попытка вызвать toBase58 для проверки
      try {
        const base58 = programId.toBase58();
        if (!base58 || typeof base58 !== 'string') {
          errors.push('programId.toBase58() returned invalid value');
        }
      } catch (error) {
        errors.push(`programId.toBase58() failed: ${error.message}`);
      }

      return { isValid: errors.length === 0, errors };

    } catch (error) {
      errors.push(`programId validation error: ${error.message}`);
      return { isValid: false, errors };
    }
  }

  /**
   * 🔍 ВАЛИДАЦИЯ KEYS МАССИВА
   * @param {Array} keys - Массив keys для валидации
   * @returns {Object} - Результат валидации
   */
  static validateKeys(keys) {
    const errors = [];

    try {
      // Проверка что keys это массив
      if (!Array.isArray(keys)) {
        errors.push('keys is not an array');
        return { isValid: false, errors };
      }

      // Валидация каждого key
      for (let i = 0; i < keys.length; i++) {
        const key = keys[i];
        
        if (!key) {
          errors.push(`Key ${i} is null or undefined`);
          continue;
        }

        // Валидация pubkey
        if (!key.pubkey) {
          errors.push(`Key ${i} missing pubkey`);
          continue;
        }

        if (!(key.pubkey instanceof PublicKey)) {
          errors.push(`Key ${i} pubkey is not a PublicKey instance`);
          continue;
        }

        if (typeof key.pubkey.equals !== 'function') {
          errors.push(`Key ${i} pubkey.equals is not a function`);
        }

        // Валидация флагов
        if (typeof key.isSigner !== 'boolean') {
          errors.push(`Key ${i} isSigner is not a boolean`);
        }

        if (typeof key.isWritable !== 'boolean') {
          errors.push(`Key ${i} isWritable is not a boolean`);
        }
      }

      return { isValid: errors.length === 0, errors };

    } catch (error) {
      errors.push(`keys validation error: ${error.message}`);
      return { isValid: false, errors };
    }
  }

  /**
   * 🔍 ВАЛИДАЦИЯ INSTRUCTION DATA
   * @param {Buffer} data - Data для валидации
   * @returns {Object} - Результат валидации
   */
  static validateData(data) {
    const errors = [];

    try {
      // Проверка что data это Buffer
      if (!Buffer.isBuffer(data)) {
        errors.push('data is not a Buffer');
        return { isValid: false, errors };
      }

      // data может быть пустым Buffer - это нормально
      // Просто проверяем что это валидный Buffer

      return { isValid: true, errors: [] };

    } catch (error) {
      errors.push(`data validation error: ${error.message}`);
      return { isValid: false, errors };
    }
  }

  /**
   * 🔧 ФИЛЬТРАЦИЯ ВАЛИДНЫХ ИНСТРУКЦИЙ
   * @param {Array<TransactionInstruction>} instructions - Массив инструкций
   * @returns {Array<TransactionInstruction>} - Только валидные инструкции
   */
  static filterValidInstructions(instructions) {
    const validation = this.validateInstructions(instructions);
    return validation.validInstructions;
  }

  /**
   * 🔍 ПРОВЕРКА СОВМЕСТИМОСТИ С MARGINFI
   * @param {Array<TransactionInstruction>} instructions - Инструкции для MarginFi
   * @returns {Object} - Результат проверки совместимости
   */
  static validateMarginFiCompatibility(instructions) {
    const errors = [];

    try {
      // Базовая валидация
      const basicValidation = this.validateInstructions(instructions);
      if (!basicValidation.isValid) {
        errors.push(...basicValidation.errors);
      }

      // Проверка что есть хотя бы одна инструкция
      if (instructions.length === 0) {
        errors.push('No instructions provided for MarginFi');
      }

      // Проверка что все инструкции имеют непустые keys (кроме ComputeBudget)
      for (let i = 0; i < instructions.length; i++) {
        if (!instructions[i].keys || instructions[i].keys.length === 0) {
          // 🔥 ИСПРАВЛЕНО: ComputeBudget инструкции ДОЛЖНЫ иметь пустые keys!
          const programIdStr = instructions[i].programId.toString();
          if (programIdStr !== 'ComputeBudget111111111111111111111111111111') {
            errors.push(`Instruction ${i} has empty keys array - will cause MarginFi errors`);
          }
        }
      }

      const isValid = errors.length === 0;

      if (isValid) {
        console.log(`✅ Инструкции совместимы с MarginFi: ${instructions.length} инструкций`);
      } else {
        console.warn(`⚠️ Проблемы совместимости с MarginFi: ${errors.length} ошибок`);
      }

      return {
        isValid,
        compatibleInstructions: isValid ? instructions : basicValidation.validInstructions,
        errors
      };

    } catch (error) {
      errors.push(`MarginFi compatibility check error: ${error.message}`);
      return {
        isValid: false,
        compatibleInstructions: [],
        errors
      };
    }
  }

  /**
   * 📊 СТАТИСТИКА ВАЛИДАЦИИ
   * @param {Array<TransactionInstruction>} instructions - Инструкции для анализа
   * @returns {Object} - Статистика валидации
   */
  static getValidationStats(instructions) {
    const validation = this.validateInstructions(instructions);
    
    return {
      total: instructions.length,
      valid: validation.validInstructions.length,
      invalid: instructions.length - validation.validInstructions.length,
      validPercentage: instructions.length > 0 ? 
        Math.round((validation.validInstructions.length / instructions.length) * 100) : 0,
      errors: validation.errors
    };
  }
}

module.exports = {
  InstructionValidator
};
