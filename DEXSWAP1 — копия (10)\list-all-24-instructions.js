/**
 * 🔥 СПИСОК ВСЕХ 24 ИНСТРУКЦИЙ ПО ПОРЯДКУ
 */

const analysis = require('./full-24-instructions-analysis.js');

console.log('🔥 СПИСОК ВСЕХ 24 ИНСТРУКЦИЙ ПО ПОРЯДКУ');
console.log('=' .repeat(80));

if (!analysis.instructions || analysis.instructions.length === 0) {
    console.log('❌ Инструкции не найдены!');
    process.exit(1);
}

console.log(`📊 Всего инструкций: ${analysis.instructions.length}`);
console.log('');

analysis.instructions.forEach((ix, index) => {
    console.log(`📋 ИНСТРУКЦИЯ ${index}:`);
    console.log(`   Program ID: ${ix.programId.toString()}`);
    console.log(`   Ключей: ${ix.keys.length}`);
    
    // Определяем тип инструкции по programId
    const programId = ix.programId.toString();
    let instructionType = 'UNKNOWN';
    
    if (programId === 'MFv2hWf31CqbX2vTKRtLuGnvGFgkJd4KVvwGVgr5EQxf') {
        instructionType = 'MARGINFI';
    } else if (programId === 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo') {
        instructionType = 'METEORA';
    } else if (programId === 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA') {
        instructionType = 'TOKEN_PROGRAM';
    } else if (programId === '11111111111111111111111111111111') {
        instructionType = 'SYSTEM_PROGRAM';
    } else if (programId === 'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL') {
        instructionType = 'ASSOCIATED_TOKEN_PROGRAM';
    } else if (programId === 'MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr') {
        instructionType = 'MEMO_PROGRAM';
    } else if (programId === 'SysvarRent111111111111111111111111111111111') {
        instructionType = 'SYSVAR_RENT';
    }
    
    console.log(`   Тип: ${instructionType}`);
    
    // Показываем первые несколько ключей
    if (ix.keys.length > 0) {
        console.log(`   Ключи:`);
        ix.keys.slice(0, 3).forEach((key, keyIndex) => {
            const keyStr = key.pubkey.toString();
            console.log(`     ${keyIndex}: ${keyStr.slice(0,8)}...${keyStr.slice(-8)} (W:${key.isWritable}, S:${key.isSigner})`);
        });
        if (ix.keys.length > 3) {
            console.log(`     ... и еще ${ix.keys.length - 3} ключей`);
        }
    }
    
    console.log('');
});

// Группировка по типам
console.log('\n📊 ГРУППИРОВКА ПО ТИПАМ:');
console.log('=' .repeat(80));

const groupedInstructions = {};
analysis.instructions.forEach((ix, index) => {
    const programId = ix.programId.toString();
    let instructionType = 'UNKNOWN';
    
    if (programId === 'MFv2hWf31CqbX2vTKRtLuGnvGFgkJd4KVvwGVgr5EQxf') {
        instructionType = 'MARGINFI';
    } else if (programId === 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo') {
        instructionType = 'METEORA';
    } else if (programId === 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA') {
        instructionType = 'TOKEN_PROGRAM';
    } else if (programId === '11111111111111111111111111111111') {
        instructionType = 'SYSTEM_PROGRAM';
    } else if (programId === 'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL') {
        instructionType = 'ASSOCIATED_TOKEN_PROGRAM';
    } else if (programId === 'MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr') {
        instructionType = 'MEMO_PROGRAM';
    }
    
    if (!groupedInstructions[instructionType]) {
        groupedInstructions[instructionType] = [];
    }
    groupedInstructions[instructionType].push(index);
});

Object.keys(groupedInstructions).forEach(type => {
    const indices = groupedInstructions[type];
    console.log(`🔥 ${type}: ${indices.length} инструкций`);
    console.log(`   Индексы: [${indices.join(', ')}]`);
});

console.log('\n🎯 ВОЗМОЖНЫЕ ОБЪЕДИНЕНИЯ:');
console.log('=' .repeat(80));

// Ищем последовательные инструкции одного типа
const meteora = groupedInstructions['METEORA'] || [];
const marginfi = groupedInstructions['MARGINFI'] || [];

console.log('🔥 METEORA инструкции для объединения:');
if (meteora.length > 1) {
    // Ищем последовательные группы
    let currentGroup = [meteora[0]];
    for (let i = 1; i < meteora.length; i++) {
        if (meteora[i] === meteora[i-1] + 1) {
            currentGroup.push(meteora[i]);
        } else {
            if (currentGroup.length > 1) {
                console.log(`   Группа ${currentGroup[0]}-${currentGroup[currentGroup.length-1]}: [${currentGroup.join(', ')}]`);
            }
            currentGroup = [meteora[i]];
        }
    }
    if (currentGroup.length > 1) {
        console.log(`   Группа ${currentGroup[0]}-${currentGroup[currentGroup.length-1]}: [${currentGroup.join(', ')}]`);
    }
}

console.log('🔥 MARGINFI инструкции для объединения:');
if (marginfi.length > 1) {
    // Ищем последовательные группы
    let currentGroup = [marginfi[0]];
    for (let i = 1; i < marginfi.length; i++) {
        if (marginfi[i] === marginfi[i-1] + 1) {
            currentGroup.push(marginfi[i]);
        } else {
            if (currentGroup.length > 1) {
                console.log(`   Группа ${currentGroup[0]}-${currentGroup[currentGroup.length-1]}: [${currentGroup.join(', ')}]`);
            }
            currentGroup = [marginfi[i]];
        }
    }
    if (currentGroup.length > 1) {
        console.log(`   Группа ${currentGroup[0]}-${currentGroup[currentGroup.length-1]}: [${currentGroup.join(', ')}]`);
    }
}

console.log(`\n${'='.repeat(80)}`);
console.log('🎯 СПИСОК ВСЕХ 24 ИНСТРУКЦИЙ ЗАВЕРШЁН!');
console.log(`📊 Всего инструкций: ${analysis.instructions.length}`);
console.log(`🔥 MARGINFI: ${(groupedInstructions['MARGINFI'] || []).length}`);
console.log(`🔥 METEORA: ${(groupedInstructions['METEORA'] || []).length}`);
console.log(`🔥 TOKEN_PROGRAM: ${(groupedInstructions['TOKEN_PROGRAM'] || []).length}`);
console.log(`🔥 SYSTEM_PROGRAM: ${(groupedInstructions['SYSTEM_PROGRAM'] || []).length}`);
console.log(`🔥 ASSOCIATED_TOKEN_PROGRAM: ${(groupedInstructions['ASSOCIATED_TOKEN_PROGRAM'] || []).length}`);
console.log(`🔥 MEMO_PROGRAM: ${(groupedInstructions['MEMO_PROGRAM'] || []).length}`);
console.log(`${'='.repeat(80)}`);
