/**
 * 🔍 ЗАГРУЗКА ALT ТАБЛИЦ: 3 MARGINFI + 1 КАСТОМНАЯ
 * Проверяем и загружаем все необходимые ALT таблицы
 */

// 🔧 Загружаем переменные окружения
require('dotenv').config();

const { Connection, PublicKey } = require('@solana/web3.js');
const fs = require('fs');

async function loadALTTables() {
    try {
        console.log('🔥 Загружаем ALT таблицы из локального кэша...');

        // 🔧 СНАЧАЛА ПРОБУЕМ ЗАГРУЗИТЬ ИЗ КЭША
        const cacheFile = 'correct-alt-tables-cache.json';
        if (fs.existsSync(cacheFile)) {
            console.log('📁 Найден кэш файл, загружаем из кэша...');

            const cacheData = JSON.parse(fs.readFileSync(cacheFile, 'utf8'));
            console.log(`📅 Дата кэша: ${cacheData.timestamp}`);
            console.log(`📊 Всего таблиц: ${cacheData.totalTables}`);
            console.log(`📊 Всего аккаунтов: ${cacheData.totalAccounts}`);

            // Проверяем валидность всех таблиц
            const validTables = Object.entries(cacheData.tables).filter(([name, data]) => data.valid);

            if (validTables.length === 4) {
                console.log('✅ Все 4 таблицы валидны, используем кэш');

                validTables.forEach(([name, data]) => {
                    console.log(`✅ ${name}: ${data.accountCount} адресов`);
                });

                if (cacheData.tables.custom && cacheData.tables.custom.valid) {
                    // Проверяем наличие пулов Meteора в кастомной ALT
                    const meteoraPools = [
                        '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6',
                        'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y',
                        'HTvjzsfX3yU6BUodCjZ5vZkUrAxMDTrBs3CJaq43ashR'
                    ];

                    const foundPools = meteoraPools.filter(pool =>
                        cacheData.tables.custom.addresses.includes(pool)
                    );

                    console.log(`🎯 ИТОГО ЗАГРУЖЕНО: ${validTables.length} ALT таблиц из кэша`);
                    console.log(`✅ Используем статические ALT таблицы: ${validTables.length}`);
                    console.log(`🌪️ Пулы Meteора в Custom ALT: ${foundPools.length}/3`);

                    return {
                        success: true,
                        source: 'cache',
                        tables: cacheData.tables,
                        totalTables: cacheData.totalTables,
                        totalAccounts: cacheData.totalAccounts
                    };
                }
            }
        }

        console.log('⚠️ Кэш недоступен или неполный, загружаем live данные...');

        // 🔗 Подключение к RPC для live загрузки
        const connection = new Connection(process.env.QUICKNODE_RPC_URL);
        console.log('✅ Подключение к RPC установлено');

        // 📋 ТОЛЬКО 4 ПРАВИЛЬНЫЕ ALT ТАБЛИЦЫ
        const altTables = {
            // 🏦 MARGINFI ОФИЦИАЛЬНЫЕ ALT ТАБЛИЦЫ
            marginfi1: 'HGmknUTUmeovMc9ryERNWG6UFZDFDVr9xrum3ZhyL4fC',
            marginfi2: '5FuKF7C1tJji2mXZuJ14U9oDb37is5mmvYLf4KwojoF1',
            marginfi3: 'FEFhAFKz48P3w82Ds5VhvyEDwhRqu2FejmnuxEPZ8wNR',

            // 🎯 КАСТОМНАЯ ALT ТАБЛИЦА (ОБНОВЛЕННАЯ С ПУЛАМИ METEORA)
            custom: 'FAeyUf4AdG7vTQ2Q89r9hWBkPdYkykJKG4CxgqArBGXe'
        };

        const loadedTables = {};
        let totalAccounts = 0;

        // 🔍 ЗАГРУЖАЕМ КАЖДУЮ ALT ТАБЛИЦУ
        for (const [name, address] of Object.entries(altTables)) {
            console.log(`\n🔍 ЗАГРУЗКА ALT ТАБЛИЦЫ: ${name}`);
            console.log(`📍 Адрес: ${address}`);
            
            try {
                const altAddress = new PublicKey(address);
                
                // 🔧 Загружаем ALT таблицу
                const altAccount = await connection.getAddressLookupTable(altAddress);
                
                if (altAccount && altAccount.value) {
                    const addresses = altAccount.value.state.addresses;
                    console.log(`✅ ALT таблица загружена: ${addresses.length} адресов`);
                    
                    loadedTables[name] = {
                        address: address,
                        accountCount: addresses.length,
                        addresses: addresses.map(addr => addr.toString()),
                        valid: true
                    };
                    
                    totalAccounts += addresses.length;
                    
                    // 🔍 ПОКАЗЫВАЕМ ПЕРВЫЕ 5 АДРЕСОВ
                    console.log(`   Первые 5 адресов:`);
                    for (let i = 0; i < Math.min(5, addresses.length); i++) {
                        console.log(`      ${i + 1}: ${addresses[i].toString()}`);
                    }
                    
                } else {
                    console.log(`❌ ALT таблица не найдена или пустая`);
                    loadedTables[name] = {
                        address: address,
                        accountCount: 0,
                        addresses: [],
                        valid: false
                    };
                }
                
            } catch (error) {
                console.log(`❌ Ошибка загрузки ALT таблицы ${name}: ${error.message}`);
                loadedTables[name] = {
                    address: address,
                    accountCount: 0,
                    addresses: [],
                    valid: false,
                    error: error.message
                };
            }
        }

        // 📊 ИТОГОВАЯ СТАТИСТИКА
        console.log(`\n📊 ИТОГОВАЯ СТАТИСТИКА ALT ТАБЛИЦ:`);
        console.log(`   Всего таблиц: ${Object.keys(altTables).length}/4`);
        console.log(`   Успешно загружено: ${Object.values(loadedTables).filter(t => t.valid).length}/4`);
        console.log(`   Всего адресов: ${totalAccounts}`);
        console.log(`🏦 MarginFi ALT таблицы: 3`);
        console.log(`🎯 Кастомная ALT таблица: 1 (с пулами Meteора)`);

        // 📋 ДЕТАЛЬНАЯ СТАТИСТИКА
        console.log(`\n📋 ДЕТАЛЬНАЯ СТАТИСТИКА:`);
        for (const [name, table] of Object.entries(loadedTables)) {
            const status = table.valid ? '✅' : '❌';
            console.log(`   ${status} ${name}: ${table.accountCount} адресов`);
            if (table.error) {
                console.log(`      Ошибка: ${table.error}`);
            }
        }

        // Проверяем пулы Meteора в кастомной ALT
        if (loadedTables.custom && loadedTables.custom.valid) {
            const meteoraPools = [
                '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6',
                'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y',
                'HTvjzsfX3yU6BUodCjZ5vZkUrAxMDTrBs3CJaq43ashR'
            ];

            const foundPools = meteoraPools.filter(pool =>
                loadedTables.custom.addresses.includes(pool)
            );

            console.log(`🌪️ Пулы Meteора в Custom ALT: ${foundPools.length}/3`);
            foundPools.forEach(pool => {
                console.log(`   ✅ ${pool}`);
            });
        }

        // 💾 СОХРАНЯЕМ КЭШИРОВАННЫЕ ДАННЫЕ
        const cacheData = {
            timestamp: new Date().toISOString(),
            source: "Cleaned ALT Tables - Only 4 Correct Tables",
            totalTables: Object.keys(altTables).length,
            totalAccounts: totalAccounts,
            tables: loadedTables
        };

        // Сохраняем в правильный файл кэша
        fs.writeFileSync('correct-alt-tables-cache.json', JSON.stringify(cacheData, null, 2));
        console.log(`\n💾 Кэш сохранен: correct-alt-tables-cache.json`);

        // 🔧 СОХРАНЯЕМ MARGINFI КЭШИРОВАННЫЕ ДАННЫЕ
        const marginfiTables = {};
        const marginfiAddresses = [];
        
        for (const [name, table] of Object.entries(loadedTables)) {
            if (name.startsWith('marginfi') && table.valid) {
                marginfiTables[name] = table;
                marginfiAddresses.push(...table.addresses);
            }
        }

        const marginfiCache = {
            timestamp: new Date().toISOString(),
            source: "MarginFi ALT Tables",
            totalTables: Object.keys(marginfiTables).length,
            totalAccounts: marginfiAddresses.length,
            validationResults: Object.values(marginfiTables).map(table => ({
                address: table.address,
                accountCount: table.accountCount,
                valid: table.valid,
                addresses: table.addresses
            }))
        };

        fs.writeFileSync('marginfi-alt-cache.json', JSON.stringify(marginfiCache, null, 2));
        console.log(`\n💾 MarginFi ALT кэшированные данные сохранены: marginfi-alt-cache.json`);

        console.log(`\n🎯 ЗАГРУЗКА ALT ТАБЛИЦ ЗАВЕРШЕНА!`);
        console.log(`✅ Готово к использованию в системе сжатия транзакций`);

        return {
            success: true,
            source: 'live',
            tables: loadedTables,
            totalTables: Object.keys(altTables).length,
            totalAccounts: totalAccounts
        };
        
    } catch (error) {
        console.error('❌ Критическая ошибка загрузки ALT таблиц:', error.message);
        console.error('Stack:', error.stack);
    }
}

// 🚀 ЗАПУСК ЗАГРУЗКИ
if (require.main === module) {
    console.log('🔍 ЗАПУСК ЗАГРУЗКИ ALT ТАБЛИЦ...');
    loadALTTables().then(() => {
        console.log('✅ Загрузка завершена');
        process.exit(0);
    }).catch(error => {
        console.error('❌ Ошибка:', error);
        process.exit(1);
    });
}

module.exports = { loadALTTables };
