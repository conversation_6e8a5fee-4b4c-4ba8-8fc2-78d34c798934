// 🏦 РЕАЛЬНЫЕ АДРЕСА БАНКОВ MARGINFI ДЛЯ ОСНОВНОГО КОДА
// ✅ ПРОВЕРЕННЫЕ АДРЕСА ИЗ ОФИЦИАЛЬНОГО MARGINFI SDK
// Дата: 2025-01-27

/**
 * 🎯 ОСНОВНЫЕ БАНКИ ДЛЯ АРБИТРАЖА - РЕАЛЬНЫЕ АДРЕСА
 */
const MARGINFI_BANKS = {
  // 🪙 Основные токены с реальными адресами
  USDC: {
    address: '2s37akK2eyBbp8DZgCm7RtsaEz8eJP3Nxd4urLHQv7yB',
    mint: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
    borrowLimit: '***************', // 200 триллионов
    borrowCapacity: '***************',
    symbol: 'USDC',
    decimals: 6
  },

  SOL: {
    address: 'CCKtUs6Cgwo4aaQUmBPmyoApH2gUDErxNZCAntD6LYGh',
    mint: 'So11111111111111111111111111111111111111112',
    borrowLimit: '***************', // 500 триллионов
    borrowCapacity: '***************',
    symbol: 'SOL',
    decimals: 9
  },

  USDT: {
    address: 'HmpMfL8942u22htC4EMiWgLX931g3sacXFR6KjuLgKLV',
    mint: 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB',
    borrowLimit: '**************', // 50 триллионов
    borrowCapacity: '**************',
    symbol: 'USDT',
    decimals: 6
  },

  WBTC: {
    address: 'BKsfDJCMbYep6gr9pq8PsmJbb5XGLHbAJzUV8vmorz7a',
    mint: '********************************************',
    borrowLimit: '1000000000', // 1 миллиард
    borrowCapacity: '640032666',
    symbol: 'WBTC',
    decimals: 8
  },

  ETH: {
    address: 'BkUyfXjbBBALcfZvw76WAFRvYQ21xxMWWeoPtJrUqG3z',
    mint: '7vfCXTUXx5WJV5JADk17DUJ4ksgau7utNKj4b963voxs',
    borrowLimit: '20000000000', // 20 миллиардов
    borrowCapacity: '13569926549',
    symbol: 'ETH',
    decimals: 8
  },

  // 🏦 Liquid Staking токены
  mSOL: {
    address: '22DcjMZrMwC5Bpa5AGBsmjc5V9VuQrXG6N9ZtdUNyYGE',
    mint: 'mSoLzYCxHdYgdzU16g5QSh3i5K3z3KZK7ytfqcJm7So',
    borrowLimit: '***************',
    borrowCapacity: '493977802862044',
    symbol: 'mSOL',
    decimals: 9
  },

  stSOL: {
    address: '7TZABdVVzqtGwgtqHM6VS8E34LtFq4dogNvTWEH9QaaM',
    mint: '7dHbWXmci3dT8UFYWYZweBLXgycu7Y3iL6trKn1Y7ARj',
    borrowLimit: '30000000000000',
    borrowCapacity: '29979521678029',
    symbol: 'stSOL',
    decimals: 9
  },

  // 🎯 Популярные токены
  JUP: {
    address: 'Guu5uBc8k1WK1U2ihGosNaCy57LSgCkpWAabtzQqrQf8',
    mint: 'JUPyiwrYJFskUPiHa7hkeR8VUtAeFoSYbKedZNsDvCN',
    borrowLimit: '***********',
    borrowCapacity: '-300571', // Отрицательная capacity
    symbol: 'JUP',
    decimals: 6
  },

  BONK: {
    address: 'BZAm4qGscR8gg5bmWrEq6BTofgaZPbg7Fwfa7rFghEXL',
    mint: '6B8hZSupE5mcACmjzozP6C1DR2uaCCtmrGqcYWC6SBCc',
    borrowLimit: '0', // НЕТ займов
    borrowCapacity: '0',
    symbol: 'BONK',
    decimals: 5
  }
};

/**
 * 🎯 ФУНКЦИИ ДЛЯ РАБОТЫ С БАНКАМИ
 */

// Получить банк по символу
function getBankBySymbol(symbol) {
  return MARGINFI_BANKS[symbol] || null;
}

// Получить адрес банка
function getBankAddress(symbol) {
  const bank = getBankBySymbol(symbol);
  return bank ? bank.address : null;
}

// Получить mint адрес токена
function getMintAddress(symbol) {
  const bank = getBankBySymbol(symbol);
  return bank ? bank.mint : null;
}

// Проверить, можно ли брать займы
function canBorrow(symbol) {
  const bank = getBankBySymbol(symbol);
  return bank && parseInt(bank.borrowLimit) > 0;
}

// Получить лимит займов
function getBorrowLimit(symbol) {
  const bank = getBankBySymbol(symbol);
  return bank ? bank.borrowLimit : '0';
}

// Получить все основные банки
function getAllBanks() {
  return MARGINFI_BANKS;
}

// Получить только банки с возможностью займов
function getBorrowableBanks() {
  const borrowable = {};
  for (const [symbol, bank] of Object.entries(MARGINFI_BANKS)) {
    if (parseInt(bank.borrowLimit) > 0) {
      borrowable[symbol] = bank;
    }
  }
  return borrowable;
}

// Получить топ банки по лимиту займов
function getTopBanksByBorrowLimit(limit = 5) {
  return Object.entries(MARGINFI_BANKS)
    .filter(([symbol, bank]) => parseInt(bank.borrowLimit) > 0)
    .sort(([, a], [, b]) => parseInt(b.borrowLimit) - parseInt(a.borrowLimit))
    .slice(0, limit)
    .reduce((acc, [symbol, bank]) => {
      acc[symbol] = bank;
      return acc;
    }, {});
}

/**
 * 🚀 ЭКСПОРТ
 */
module.exports = {
  MARGINFI_BANKS,
  
  // Функции
  getBankBySymbol,
  getBankAddress,
  getMintAddress,
  canBorrow,
  getBorrowLimit,
  getAllBanks,
  getBorrowableBanks,
  getTopBanksByBorrowLimit
};

/**
 * 📝 ПРИМЕРЫ ИСПОЛЬЗОВАНИЯ:
 * 
 * const { getBankAddress, canBorrow, getTopBanksByBorrowLimit } = require('./marginfi-banks-config');
 * 
 * // Получить адрес USDC банка
 * console.log('USDC банк:', getBankAddress('USDC'));
 * // Результат: 2s37akK2eyBbp8DZgCm7RtsaEz8eJP3Nxd4urLHQv7yB
 * 
 * // Проверить возможность займа
 * console.log('Можно брать SOL:', canBorrow('SOL'));
 * // Результат: true
 * 
 * // Получить топ-3 банка
 * console.log('Топ банки:', getTopBanksByBorrowLimit(3));
 * // Результат: { SOL: {...}, mSOL: {...}, USDC: {...} }
 */
