/**
 * 🏦 MARGINFI INTEGRATION
 * ═══════════════════════════════════════════════════════════════════════════
 * 🎯 ЦЕЛЬ: Правильная интеграция с MarginFi SDK БЕЗ ПАТЧЕЙ
 * 🔧 ФУНКЦИЯ: Валидация и обработка инструкций перед передачей в MarginFi
 * 📋 ПРИМЕНЕНИЕ: Использование только официальных методов MarginFi SDK
 */

const { PublicKey, TransactionInstruction } = require('@solana/web3.js');
const { InstructionValidator } = require('./instruction-validator.js');
const { MarginFiBuildFlashLoanTxAdapter } = require('./marginfi-buildFlashLoanTx-adapter.js');

/**
 * 🏦 MARGINFI INTEGRATION CLASS
 * Правильная работа с MarginFi SDK согласно официальной документации
 */
class MarginFiIntegration {

  /**
   * 🔧 СОЗДАНИЕ FLASH LOAN ТРАНЗАКЦИИ
   * @param {Object} marginfiAccount - MarginFi аккаунт
   * @param {Array<TransactionInstruction>} instructions - Инструкции для flash loan
   * @param {Array} addressLookupTableAccounts - ALT аккаунты
   * @returns {Promise<Object>} - Flash loan транзакция
   */
  static async createFlashLoan(marginfiAccount, instructions, addressLookupTableAccounts = []) {
    try {
      console.log('🏦 Создаем MarginFi Flash Loan транзакцию...');

      // 1. ВАЛИДАЦИЯ ВХОДНЫХ ДАННЫХ
      const validationResult = this.validateFlashLoanInputs(
        marginfiAccount, 
        instructions, 
        addressLookupTableAccounts
      );

      if (!validationResult.isValid) {
        throw new Error(`Validation failed: ${validationResult.errors.join(', ')}`);
      }

      // 2. ВАЛИДАЦИЯ И ФИЛЬТРАЦИЯ ИНСТРУКЦИЙ
      const validatedInstructions = this.validateAndFilterInstructions(instructions);

      if (validatedInstructions.length === 0) {
        throw new Error('No valid instructions after validation');
      }

      console.log(`✅ Валидировано ${validatedInstructions.length} инструкций для MarginFi`);

      // 3. СОЗДАНИЕ FLASH LOAN ЧЕРЕЗ ОФИЦИАЛЬНЫЙ API
      const flashLoanTransaction = await this.buildFlashLoanTransaction(
        marginfiAccount,
        validatedInstructions,
        addressLookupTableAccounts
      );

      console.log('✅ MarginFi Flash Loan транзакция создана успешно');
      return flashLoanTransaction;

    } catch (error) {
      console.error(`❌ Ошибка создания MarginFi Flash Loan: ${error.message}`);
      throw new Error(`Failed to create MarginFi flash loan: ${error.message}`);
    }
  }

  /**
   * 🔍 ВАЛИДАЦИЯ ВХОДНЫХ ДАННЫХ ДЛЯ FLASH LOAN
   * @param {Object} marginfiAccount - MarginFi аккаунт
   * @param {Array} instructions - Инструкции
   * @param {Array} addressLookupTableAccounts - ALT аккаунты
   * @returns {Object} - Результат валидации
   */
  static validateFlashLoanInputs(marginfiAccount, instructions, addressLookupTableAccounts) {
    const errors = [];

    try {
      // Проверка MarginFi аккаунта
      if (!marginfiAccount) {
        errors.push('MarginFi account is null or undefined');
      } else {
        // Проверка что у аккаунта есть метод buildFlashLoanTx
        if (typeof marginfiAccount.buildFlashLoanTx !== 'function') {
          errors.push('MarginFi account missing buildFlashLoanTx method');
        }
      }

      // Проверка инструкций
      if (!Array.isArray(instructions)) {
        errors.push('Instructions must be an array');
      } else if (instructions.length === 0) {
        errors.push('Instructions array is empty');
      }

      // Проверка ALT аккаунтов
      if (!Array.isArray(addressLookupTableAccounts)) {
        errors.push('Address lookup table accounts must be an array');
      }

      return {
        isValid: errors.length === 0,
        errors
      };

    } catch (error) {
      errors.push(`Input validation error: ${error.message}`);
      return { isValid: false, errors };
    }
  }

  /**
   * 🔧 ВАЛИДАЦИЯ И ФИЛЬТРАЦИЯ ИНСТРУКЦИЙ
   * @param {Array<TransactionInstruction>} instructions - Инструкции для валидации
   * @returns {Array<TransactionInstruction>} - Валидные инструкции
   */
  static validateAndFilterInstructions(instructions) {
    console.log(`🔍 Валидируем ${instructions.length} инструкций для MarginFi...`);

    // Используем InstructionValidator для проверки совместимости с MarginFi
    const compatibilityCheck = InstructionValidator.validateMarginFiCompatibility(instructions);

    if (!compatibilityCheck.isValid) {
      console.warn(`⚠️ Найдены проблемы совместимости: ${compatibilityCheck.errors.length}`);
      compatibilityCheck.errors.forEach(error => console.warn(`⚠️ ${error}`));
    }

    const validInstructions = compatibilityCheck.compatibleInstructions;

    // Дополнительная фильтрация для MarginFi
    const filteredInstructions = validInstructions.filter(instruction => {
      // 🔥 ИСПРАВЛЕНО: ComputeBudget инструкции ДОЛЖНЫ иметь пустые keys!
      if (!instruction.keys || instruction.keys.length === 0) {
        // Проверяем, это ComputeBudget инструкция?
        const programIdStr = instruction.programId.toString();
        if (programIdStr === 'ComputeBudget111111111111111111111111111111') {
          console.log('✅ Сохраняем ComputeBudget инструкцию (пустые keys - это нормально)');
          return true; // ComputeBudget инструкции ВСЕГДА имеют 0 keys
        } else {
          console.warn('⚠️ Убираем инструкцию с пустыми keys (не ComputeBudget)');
          return false;
        }
      }

      return true;
    });

    console.log(`✅ Отфильтровано ${filteredInstructions.length} валидных инструкций`);
    return filteredInstructions;
  }

  /**
   * 🏗️ ПОСТРОЕНИЕ FLASH LOAN ТРАНЗАКЦИИ
   * @param {Object} marginfiAccount - MarginFi аккаунт
   * @param {Array<TransactionInstruction>} validatedInstructions - Валидные инструкции
   * @param {Array} addressLookupTableAccounts - ALT аккаунты
   * @returns {Promise<Object>} - Flash loan транзакция
   */
  static async buildFlashLoanTransaction(marginfiAccount, validatedInstructions, addressLookupTableAccounts) {
    try {
      console.log('🏗️ Строим Flash Loan транзакцию через официальный MarginFi API...');

      // 🔥 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: ДОБАВЛЯЕМ closeAccount ДЛЯ WSOL
      console.log('🔥 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Проверяем необходимость closeAccount для WSOL...');

      const wsolMint = 'So11111111111111111111111111111111111111112';
      let hasWSolSwap = false;

      // Проверяем есть ли WSOL операции в инструкциях
      for (const instruction of validatedInstructions) {
        if (instruction.accounts) {
          for (const account of instruction.accounts) {
            if (account.pubkey && account.pubkey.toString() === wsolMint) {
              hasWSolSwap = true;
              break;
            }
          }
        }
        if (hasWSolSwap) break;
      }

      console.log(`🔍 WSOL операции обнаружены: ${hasWSolSwap ? 'ДА' : 'НЕТ'}`);

      // Создаем closeAccount инструкции если есть WSOL
      let enhancedInstructions = [...validatedInstructions];

      if (hasWSolSwap) {
        console.log('🔥 Создаем closeAccount инструкции для WSOL...');

        try {
          // Импортируем необходимые модули
          const { PublicKey } = require('@solana/web3.js');
          const { createCloseAccountInstruction, getAssociatedTokenAddress } = require('@solana/spl-token');

          // Получаем публичный ключ пользователя из MarginFi аккаунта
          const userPubKey = marginfiAccount.authority;
          const wsolAccount = await getAssociatedTokenAddress(new PublicKey(wsolMint), userPubKey);

          // Создаем closeAccount инструкцию
          const closeInstruction = createCloseAccountInstruction(
            wsolAccount,  // Аккаунт для закрытия
            userPubKey,   // Получатель lamports (кошелек)
            userPubKey    // Владелец аккаунта
          );

          // Находим позицию для вставки closeAccount (после первого Jupiter swap)
          const insertPosition = Math.floor(validatedInstructions.length / 2);

          console.log(`🔥 Вставляем closeAccount на позицию ${insertPosition} из ${validatedInstructions.length} инструкций`);

          // Вставляем closeAccount инструкцию
          enhancedInstructions.splice(insertPosition, 0, closeInstruction);

          console.log(`✅ closeAccount инструкция добавлена для WSOL аккаунта: ${wsolAccount.toString().slice(0, 8)}...`);
          console.log(`✅ Итого инструкций с closeAccount: ${enhancedInstructions.length}`);
          console.log(`   Оригинальных: ${validatedInstructions.length}, closeAccount: 1`);

        } catch (closeAccountError) {
          console.error(`❌ Ошибка создания closeAccount: ${closeAccountError.message}`);
          console.log(`⚠️ Продолжаем без closeAccount - может вызвать ошибку "insufficient lamports"`);
          // Продолжаем с оригинальными инструкциями
        }
      }

      // Подготавливаем аргументы для MarginFi SDK
      const flashLoanArgs = {
        ixs: enhancedInstructions  // MarginFi ожидает поле 'ixs' с closeAccount
      };

      console.log(`📋 Передаем ${enhancedInstructions.length} инструкций в MarginFi SDK (включая closeAccount)`);
      console.log(`📋 Используем ${addressLookupTableAccounts.length} ALT аккаунтов`);

      // 🔧 ИСПРАВЛЕНО: ИСПОЛЬЗУЕМ АДАПТЕР ВМЕСТО ПРЯМОГО ВЫЗОВА buildFlashLoanTx
      console.log(`🔧 ИСПОЛЬЗУЕМ MARGINFI АДАПТЕР вместо прямого вызова buildFlashLoanTx`);

      // Создаем временный адаптер для этого вызова
      const adapter = new MarginFiBuildFlashLoanTxAdapter(
        marginfiAccount.client.provider.connection,
        marginfiAccount.client.wallet
      );

      const flashLoanTransaction = await adapter.buildFlashLoanTx(
        marginfiAccount,
        flashLoanArgs,
        addressLookupTableAccounts
      );

      if (!flashLoanTransaction) {
        throw new Error('MarginFi buildFlashLoanTx returned null/undefined');
      }

      console.log('✅ Flash Loan транзакция построена через официальный API');
      return flashLoanTransaction;

    } catch (error) {
      console.error(`❌ Ошибка построения Flash Loan транзакции: ${error.message}`);
      
      // Детальная диагностика ошибки
      this.diagnoseFlashLoanError(error, validatedInstructions);
      
      throw error;
    }
  }

  /**
   * 🔍 ДИАГНОСТИКА ОШИБОК FLASH LOAN
   * @param {Error} error - Ошибка от MarginFi SDK
   * @param {Array} instructions - Инструкции которые вызвали ошибку
   */
  static diagnoseFlashLoanError(error, instructions) {
    console.log('🔍 Диагностируем ошибку MarginFi Flash Loan...');

    // Анализ типичных ошибок
    if (error.message.includes('equals is not a function')) {
      console.error('❌ ДИАГНОЗ: Проблема с PublicKey объектами в инструкциях');
      console.error('💡 РЕШЕНИЕ: Проверьте нормализацию инструкций через JupiterInstructionNormalizer');
    }

    if (error.message.includes('encoding overruns')) {
      console.error('❌ ДИАГНОЗ: Проблема с размером транзакции или кодированием');
      console.error('💡 РЕШЕНИЕ: Проверьте размер инструкций и ALT покрытие');
    }

    if (error.message.includes('keys')) {
      console.error('❌ ДИАГНОЗ: Проблема с keys в инструкциях');
      console.error('💡 РЕШЕНИЕ: Проверьте что все инструкции имеют валидные keys');
    }

    // Статистика инструкций
    console.log(`📊 Статистика инструкций:`);
    console.log(`   - Всего инструкций: ${instructions.length}`);
    
    const stats = InstructionValidator.getValidationStats(instructions);
    console.log(`   - Валидных: ${stats.valid}`);
    console.log(`   - Невалидных: ${stats.invalid}`);
    console.log(`   - Процент валидности: ${stats.validPercentage}%`);

    if (stats.errors.length > 0) {
      console.log(`   - Ошибки валидации: ${stats.errors.length}`);
      stats.errors.slice(0, 3).forEach(error => console.log(`     • ${error}`));
    }
  }

  /**
   * 🔧 ПОДГОТОВКА ИНСТРУКЦИЙ ДЛЯ MARGINFI
   * @param {Array<TransactionInstruction>} rawInstructions - Сырые инструкции
   * @returns {Array<TransactionInstruction>} - Подготовленные инструкции
   */
  static prepareInstructionsForMarginFi(rawInstructions) {
    console.log('🔧 Подготавливаем инструкции для MarginFi...');

    // 1. Валидация и фильтрация
    const validatedInstructions = this.validateAndFilterInstructions(rawInstructions);

    // 2. Дополнительная обработка если нужна
    const preparedInstructions = validatedInstructions.map(instruction => {
      // Здесь можно добавить дополнительную обработку если потребуется
      // Но основная цель - НЕ ПАТЧИТЬ, а использовать правильные данные
      return instruction;
    });

    console.log(`✅ Подготовлено ${preparedInstructions.length} инструкций для MarginFi`);
    return preparedInstructions;
  }

  /**
   * 📊 ПОЛУЧЕНИЕ СТАТИСТИКИ ИНТЕГРАЦИИ
   * @param {Array<TransactionInstruction>} instructions - Инструкции для анализа
   * @returns {Object} - Статистика интеграции
   */
  static getIntegrationStats(instructions) {
    const validationStats = InstructionValidator.getValidationStats(instructions);
    const compatibilityCheck = InstructionValidator.validateMarginFiCompatibility(instructions);

    return {
      ...validationStats,
      marginFiCompatible: compatibilityCheck.isValid,
      marginFiCompatibleCount: compatibilityCheck.compatibleInstructions.length,
      marginFiErrors: compatibilityCheck.errors
    };
  }
}

module.exports = {
  MarginFiIntegration
};
