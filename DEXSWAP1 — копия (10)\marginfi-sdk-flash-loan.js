/**
 * 🔥 FLASH LOAN С ОФИЦИАЛЬНЫМ MARGINFI SDK
 * ИСПОЛЬЗУЕМ ПРАВИЛЬНЫЕ WITHDRAW/DEPOSIT МЕТОДЫ
 */

const { Connection, Keypair, PublicKey, TransactionMessage, VersionedTransaction } = require('@solana/web3.js');
const { MarginfiClient, getConfig, MarginfiAccountWrapper } = require('@mrgnlabs/marginfi-client-v2');
const { NodeWallet } = require('@mrgnlabs/mrgn-common');
const DLMM = require('@meteora-ag/dlmm').default;
const { BN } = require('@coral-xyz/anchor');
const fs = require('fs');

/**
 * 🔥 ЗАГРУЗКА ALT ТАБЛИЦ (СКОПИРОВАНО ИЗ ОСНОВНОГО КОДА)
 */
function loadALTTablesDirectly() {
    try {
        console.log('🔥 ЗАГРУЗКА ALT ТАБЛИЦ НАПРЯМУЮ ИЗ ФАЙЛА...');

        const fileData = JSON.parse(fs.readFileSync('correct-alt-tables-cache.json', 'utf8'));

        console.log(`📊 Загружено из файла: ${fileData.totalTables} ALT таблиц`);

        // 🔥 ПРЕОБРАЗУЕМ ОБЪЕКТ tables В МАССИВ
        const formattedALTs = [];
        let totalAccounts = 0;
        let index = 0;

        for (const [tableName, tableData] of Object.entries(fileData.tables)) {
            if (tableData.address && tableData.addresses) {
                const formattedALT = {
                    key: new PublicKey(tableData.address),
                    state: {
                        addresses: tableData.addresses.map(addr => new PublicKey(addr))
                    }
                };
                formattedALTs.push(formattedALT);
                totalAccounts += tableData.addresses.length;
                console.log(`✅ ALT ${index + 1} (${tableName}): ${tableData.addresses.length} адресов`);
                index++;
            }
        }

        console.log(`📊 Всего аккаунтов: ${totalAccounts}`);
        return formattedALTs;

    } catch (error) {
        console.log(`❌ Ошибка загрузки ALT таблиц: ${error.message}`);
        return []; // ВОЗВРАЩАЕМ ПУСТОЙ МАССИВ ПРИ ОШИБКЕ
    }
}

/**
 * 🔥 СОЗДАНИЕ POSITION НА КОНКРЕТНОМ ПУЛЕ
 */
async function createPositionOnPool(poolIndex = 0) {
    // 🔧 ПУЛЫ ИЗ ОСНОВНОГО КОДА
    const POOLS = [
        '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6', // Pool 1
        'AVs9TA4nWDzfPJE9gGVNJMVhcQy3V9PGazuz33BfG2RA', // Pool 2
        'Hs97TCZeuYiJxooo3U73qEHXg3dKpRL4uYKYRryEK9CF'  // Pool 3
    ];

    const poolAddress = POOLS[poolIndex];
    console.log(`🎯 Работаем с пулом ${poolIndex + 1}: ${poolAddress}`);

    return await createPositionWithMarginfiSDK(poolAddress);
}

async function createPositionWithMarginfiSDK(poolAddress) {
    try {
        console.log('🚀 FLASH LOAN С ОФИЦИАЛЬНЫМ MARGINFI SDK...\n');

        // 🔧 НАСТРОЙКА
        // 🔧 НАСТРОЙКА С QUICKNODE
        require('dotenv').config();
        const rpcUrl = process.env.QUICKNODE_RPC_URL || 'https://api.mainnet-beta.solana.com';
        const connection = new Connection(rpcUrl, 'confirmed');
        console.log(`🌐 RPC: ${rpcUrl.slice(0, 50)}...`);
        const secretKey = JSON.parse(fs.readFileSync('wallet.json', 'utf8'));
        const wallet = Keypair.fromSecretKey(new Uint8Array(secretKey));
        
        console.log(`💰 Кошелек: ${wallet.publicKey.toString()}`);

        // 🔧 СОЗДАНИЕ MARGINFI CLIENT
        const nodeWallet = new NodeWallet(wallet);
        const config = getConfig('production');
        const marginfiClient = await MarginfiClient.fetch(config, nodeWallet, connection);
        
        console.log(`🏦 MarginFi Client создан`);

        // 🔧 ПОЛУЧЕНИЕ MARGINFI АККАУНТА
        const marginfiAccountAddress = '********************************************';
        const marginfiAccount = await MarginfiAccountWrapper.fetch(marginfiAccountAddress, marginfiClient);
        
        console.log(`📋 MarginFi аккаунт загружен: ${marginfiAccountAddress}`);

        // 🔍 ПОИСК ВСЕХ ДОСТУПНЫХ БАНКОВ
        console.log(`\n🔍 ПОИСК ДОСТУПНЫХ БАНКОВ:`);
        const allBanks = marginfiClient.banks;

        console.log(`📊 Всего банков: ${allBanks.size}`);
        for (const [mint, bank] of allBanks) {
            const symbol = bank.tokenSymbol || 'Unknown';
            console.log(`   🏛️ Bank: ${bank.address.toString().slice(0, 8)}... | Symbol: ${symbol} | Mint: ${mint.toString().slice(0, 8)}...`);

            // Проверяем тип банка
            if (symbol === 'SOL') {
                console.log(`      ✅ Это SOL банк! Mint: ${mint.toString()}`);
            }
        }

        // 🔧 ПОЛУЧЕНИЕ SOL БАНКА
        const solBank = marginfiClient.getBankByTokenSymbol('SOL');
        if (!solBank) throw new Error('SOL bank не найден');

        console.log(`\n🏛️ Выбранный SOL Bank: ${solBank.address.toString()}`);
        console.log(`🔑 SOL Bank Mint: ${solBank.mint.toString()}`);
        console.log(`💰 SOL Bank Symbol: ${solBank.tokenSymbol}`);

        // Проверяем является ли это нативным SOL или WSOL
        const isNativeSOL = solBank.mint.toString() === '********************************';
        const isWSOL = solBank.mint.toString() === 'So********************************111111112';

        if (isNativeSOL) {
            console.log(`✅ Это НАТИВНЫЙ SOL банк! Займ будет в нативном SOL!`);
        } else if (isWSOL) {
            console.log(`⚠️ Это WSOL банк. Займ будет в WSOL токенах.`);
        } else {
            console.log(`❓ Неизвестный тип SOL банка: ${solBank.mint.toString()}`);
        }

        // 🔧 СОЗДАНИЕ METEORA DLMM
        console.log(`🌊 Создание DLMM Pool: ${poolAddress}`);
        const dlmmPool = await DLMM.create(connection, new PublicKey(poolAddress));

        // 🔧 ПОЛУЧЕНИЕ АКТИВНОГО BIN
        const activeBin = await dlmmPool.getActiveBin();
        console.log(`📊 Активный bin ID: ${activeBin.binId}`);

        // 🔧 НАСТРОЙКА POSITION
        const minBinId = activeBin.binId;
        const maxBinId = activeBin.binId + 1;
        const totalXAmount = new BN(1000); // Минимум для тестирования
        const totalYAmount = new BN(1000); // Минимум для тестирования

        const newPosition = new Keypair();
        console.log(`🔑 Новый position: ${newPosition.publicKey.toString()}`);

        // 🔥 СОЗДАНИЕ FLASH LOAN ТРАНЗАКЦИИ
        console.log('\n🔥 Создание Flash Loan транзакции...');

        // 🔥 ЗАЙМЫ КАК В ОСНОВНОМ БОТЕ
        const borrowAmountUSDC = 2500000; // 2.5M USDC (2,500,000 микро-USDC)
        const borrowAmountSOL = 8.301; // 8.301 SOL
        const withdrawAmount = 0.05; // 0.05 SOL для position

        // 1. СНАЧАЛА СОЗДАЕМ ПОЛЬЗОВАТЕЛЬСКИЕ АККАУНТЫ
        const { getAssociatedTokenAddress } = require('@solana/spl-token');
        const WSOL_MINT = new PublicKey('So********************************111111112');
        const USDC_MINT = new PublicKey('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v');

        // Используем стандартные ATA аккаунты
        const userWSOLAccount = await getAssociatedTokenAddress(WSOL_MINT, wallet.publicKey);
        const userUSDCAccount = await getAssociatedTokenAddress(USDC_MINT, wallet.publicKey);

        // 2. СОЗДАЕМ BORROW ИНСТРУКЦИЮ ВРУЧНУЮ (КАК В ОСНОВНОМ БОТЕ!)
        // MarginFi SDK не переводит токены в пользовательский аккаунт, создаем вручную

        const { TransactionInstruction } = require('@solana/web3.js');

        // 🔥 ПРАВИЛЬНЫЙ DISCRIMINATOR ДЛЯ BORROW (ИЗ ОСНОВНОГО БОТА)
        const borrowDiscriminator = [0x04, 0x7e, 0x74, 0x35, 0x30, 0x05, 0xd4, 0x1f];

        const instructionData = Buffer.alloc(16);
        Buffer.from(borrowDiscriminator).copy(instructionData, 0);
        instructionData.writeBigUInt64LE(BigInt(Math.floor(borrowAmount * 1e9)), 8); // amount в lamports

        // 🔥 ПРАВИЛЬНЫЕ АККАУНТЫ (КАК В ОСНОВНОМ БОТЕ) - ТОКЕНЫ ИДУТ В ПОЛЬЗОВАТЕЛЬСКИЙ АККАУНТ!
        const MARGINFI_GROUP = new PublicKey('4qp6Fx6tnZkY5Wropq9wUYgtFxXKwE6viZxFHg3rdAG8');

        // 🔍 ПРОВЕРЯЕМ ВСЕ АККАУНТЫ ПЕРЕД СОЗДАНИЕМ ИНСТРУКЦИИ
        console.log(`🔍 Проверка аккаунтов для BORROW:`);
        console.log(`   MARGINFI_GROUP: ${MARGINFI_GROUP.toString()}`);
        console.log(`   marginfiAccount: ${marginfiAccount.address.toString()}`);
        console.log(`   wallet: ${wallet.publicKey.toString()}`);
        console.log(`   solBank: ${solBank.address.toString()}`);
        console.log(`   userWSOLAccount: ${userWSOLAccount.toString()}`);
        console.log(`   liquidityVaultAuthority: ${solBank.liquidityVaultAuthority ? solBank.liquidityVaultAuthority.toString() : 'UNDEFINED!'}`);
        console.log(`   liquidityVault: ${solBank.liquidityVault.toString()}`);

        // 🔧 ИСПОЛЬЗУЕМ ФИКСИРОВАННЫЙ VAULT AUTHORITY ИЗ ОСНОВНОГО БОТА
        const vaultAuthority = solBank.liquidityVaultAuthority || new PublicKey('DD3AeAssFvjqTvRTrRAtpfjkBF8FpVKnFuwnMLN9haXD');

        const accounts = [
            { pubkey: MARGINFI_GROUP, isSigner: false, isWritable: false },              // 0: marginfi_group
            { pubkey: marginfiAccount.address, isSigner: false, isWritable: true },      // 1: marginfi_account
            { pubkey: wallet.publicKey, isSigner: true, isWritable: false },             // 2: authority (signer)
            { pubkey: solBank.address, isSigner: false, isWritable: true },              // 3: bank
            { pubkey: userWSOLAccount, isSigner: false, isWritable: true },              // 4: destination_token_account ✅ ПОЛЬЗОВАТЕЛЬСКИЙ!
            { pubkey: vaultAuthority, isSigner: false, isWritable: true },               // 5: bank_liquidity_vault_authority ✅ ИСПРАВЛЕНО!
            { pubkey: solBank.liquidityVault, isSigner: false, isWritable: true },       // 6: bank_liquidity_vault
            { pubkey: new PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA'), isSigner: false, isWritable: false } // 7: token_program
        ];

        const borrowIx = {
            instructions: [new TransactionInstruction({
                programId: new PublicKey('MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA'),
                keys: accounts,
                data: instructionData
            })]
        };

        console.log(`📝 РУЧНАЯ BORROW инструкция создана: ${borrowAmount} SOL → пользовательский WSOL аккаунт`);

        // 🔍 АНАЛИЗИРУЕМ РУЧНУЮ BORROW ИНСТРУКЦИЮ
        console.log(`\n🔍 АНАЛИЗ РУЧНОЙ BORROW ИНСТРУКЦИИ:`);
        console.log(`   Program: MarginFi (MFv2hWf3...)`);
        console.log(`   Keys: ${accounts.length} аккаунтов`);
        console.log(`   Data: ${instructionData.length} байт`);
        console.log(`   Destination: ${userWSOLAccount.toString().slice(0, 8)}... (пользовательский WSOL)`);
        console.log(`   Amount: ${Math.floor(borrowAmount * 1e9)} lamports`);

        // 2. АККАУНТЫ УЖЕ СОЗДАНЫ ВЫШЕ

        console.log(`🔑 User WSOL Account: ${userWSOLAccount.toString()}`);
        console.log(`🔑 User USDC Account: ${userUSDCAccount.toString()}`);

        // 🔍 ПРОВЕРЯЕМ БАЛАНСЫ ДО BORROW
        console.log(`\n🔍 БАЛАНСЫ ДО FLASH LOAN:`);
        try {
            const wsolBalanceBefore = await connection.getTokenAccountBalance(userWSOLAccount);
            console.log(`💰 WSOL баланс ДО: ${wsolBalanceBefore.value.uiAmount} WSOL`);
        } catch (e) {
            console.log(`💰 WSOL аккаунт ДО: не существует или пустой`);
        }

        const solBalanceBefore = await connection.getBalance(wallet.publicKey);
        console.log(`💰 SOL баланс кошелька ДО: ${solBalanceBefore / 1e9} SOL\n`);

        // 3. MARGINFI BORROW УЖЕ СОЗДАЕТ ПРАВИЛЬНЫЙ WSOL АККАУНТ
        // Убираем CloseAccount из BORROW инструкций, чтобы аккаунт остался для Meteora

        console.log(`✅ MarginFi BORROW создает WSOL аккаунт БЕЗ преждевременного закрытия`);

        // ❌ УБИРАЕМ ПРОВЕРКУ БАЛАНСОВ ДО ВЫПОЛНЕНИЯ ТРАНЗАКЦИИ
        // Балансы нужно проверять ПОСЛЕ выполнения транзакции!

        // 4. METEORA POSITION ИНСТРУКЦИИ (ПОЛНЫЙ ПРОЦЕСС!)
        console.log('🔥 Создание ПОЛНОГО ТОРГОВОГО ЦИКЛА...');

        // 1. СОЗДАНИЕ POSITION + ДОБАВЛЕНИЕ ЛИКВИДНОСТИ (КАК В РЕАЛЬНОЙ ТОРГОВЛЕ!)
        const liquidityAmount = new BN(1000000); // 0.001 SOL ликвидности (доступное количество)

        const createPositionTx = await dlmmPool.initializePositionAndAddLiquidityByStrategy({
            positionPubKey: newPosition.publicKey,
            user: wallet.publicKey,
            totalXAmount: liquidityAmount, // 0.05 SOL - РЕАЛЬНАЯ ликвидность!
            totalYAmount: new BN(0), // 0 USDC - односторонняя ликвидность
            strategy: {
                maxBinId,
                minBinId,
                strategyType: 0, // Spot
            },
            userTokenX: userWSOLAccount,
            userTokenY: userUSDCAccount,
        });

        console.log(`💰 Добавляем ликвидность: ${liquidityAmount.toString()} lamports (1.0 SOL)`);
        console.log(`🎯 Реальный размер для торговли! Масштабируется на миллионы!`);

        // 2. REMOVE LIQUIDITY - ИСПОЛЬЗУЕМ RAW ИНСТРУКЦИЮ С ПРАВИЛЬНЫМ DISCRIMINATOR!
        console.log('🔥 Создание removeLiquidity с правильным discriminator...');

        // ИСПОЛЬЗУЕМ ПРАВИЛЬНЫЙ DISCRIMINATOR ИЗ meteora-discriminators.json
        const removeLiquidityDiscriminator = [80, 85, 209, 72, 24, 206, 177, 108]; // remove_liquidity

        // Создаем данные для removeLiquidity
        const removeLiquidityData = Buffer.alloc(32);
        Buffer.from(removeLiquidityDiscriminator).copy(removeLiquidityData, 0);

        // Добавляем параметры (упрощенная структура)
        removeLiquidityData.writeUInt32LE(1, 8); // bin_liquidity_removal count
        removeLiquidityData.writeInt32LE(minBinId, 12); // bin_id
        removeLiquidityData.writeUInt16LE(10000, 16); // bps_to_remove (100%)
        removeLiquidityData.writeUInt8(0, 18); // should_merge_bin_arrays

        const removeLiquidityIx = new TransactionInstruction({
            programId: new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo'),
            keys: [
                { pubkey: newPosition.publicKey, isSigner: false, isWritable: true },    // position
                { pubkey: dlmmPool.pubkey, isSigner: false, isWritable: true },          // lb_pair
                { pubkey: wallet.publicKey, isSigner: true, isWritable: false },         // owner
                { pubkey: userWSOLAccount, isSigner: false, isWritable: true },          // user_token_x
                { pubkey: userUSDCAccount, isSigner: false, isWritable: true },          // user_token_y
                { pubkey: new PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA'), isSigner: false, isWritable: false }, // token_program
            ],
            data: removeLiquidityData
        });

        // 3. CLOSE POSITION - ЗАКРЫВАЕМ POSITION И ВОЗВРАЩАЕМ RENT!
        console.log('🔥 Создание closePosition для возврата rent...');

        const closePositionDiscriminator = [123, 134, 81, 0, 49, 68, 98, 98]; // closePosition
        const closePositionData = Buffer.from(closePositionDiscriminator);

        const closePositionIx = new TransactionInstruction({
            programId: new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo'),
            keys: [
                { pubkey: newPosition.publicKey, isSigner: false, isWritable: true },    // position
                { pubkey: wallet.publicKey, isSigner: true, isWritable: true },          // owner (получит rent)
                { pubkey: new PublicKey('********************************'), isSigner: false, isWritable: false }, // system_program
            ],
            data: closePositionData
        });

        const removeLiquidityTx = {
            instructions: [removeLiquidityIx, closePositionIx] // Полный цикл: remove + close
        };

        console.log('✅ ПОЛНЫЙ ТОРГОВЫЙ ЦИКЛ: ADD → REMOVE → CLOSE → ВОЗВРАТ RENT!');

        // 5. ФИЛЬТРУЕМ ПРОБЛЕМНЫЕ SYNCNATIVE ИНСТРУКЦИИ ИЗ ОБЕИХ ОПЕРАЦИЙ
        const addLiquidityFiltered = createPositionTx.instructions.filter((ix, index) => {
            if (ix.data && ix.data.length === 1 && ix.data[0] === 17) { // SyncNative = 17
                console.log(`⚠️ Убираем проблемную SyncNative из ADD LIQUIDITY #${index}`);
                return false;
            }
            return true;
        });

        const removeLiquidityFiltered = removeLiquidityTx.instructions.filter((ix, index) => {
            if (ix.data && ix.data.length === 1 && ix.data[0] === 17) { // SyncNative = 17
                console.log(`⚠️ Убираем проблемную SyncNative из REMOVE LIQUIDITY #${index}`);
                return false;
            }
            return true;
        });

        // Объединяем все Meteora инструкции
        const meteoraInstructions = [
            ...addLiquidityFiltered,
            ...removeLiquidityTx.instructions // RAW инструкции (removeLiquidity + closePosition)
        ];

        console.log(`📝 ПОЛНЫЙ ТОРГОВЫЙ ЦИКЛ: ADD ${createPositionTx.instructions.length}->${addLiquidityFiltered.length}, REMOVE+CLOSE ${removeLiquidityTx.instructions.length}, ВСЕГО ${meteoraInstructions.length}`);

        // 6. METEORA SDK АВТОМАТИЧЕСКИ УПРАВЛЯЕТ WSOL
        // Не нужны дополнительные инструкции возврата - MarginFi REPAY сам заберет токены

        console.log(`✅ Meteora SDK автоматически управляет WSOL токенами`);

        // 3. REPAY инструкции - возвращаем доступную сумму
        const repayAmount = 1.045; // Возвращаем 1.045 SOL (то что у нас есть в WSOL аккаунте)
        const repayIx = await marginfiAccount.makeRepayIx(repayAmount, solBank.address, true);
        console.log(`📝 REPAY инструкция создана: ${repayAmount} SOL (из ${borrowAmount} SOL займа)`);

        // 🔥 ПРАВИЛЬНАЯ ПОСЛЕДОВАТЕЛЬНОСТЬ: BORROW -> WSOL_TO_SOL -> METEORA -> SOL_TO_WSOL -> REPAY
        // Конвертируем WSOL в нативный SOL чтобы Meteora получила доступ к балансу!

        const { createCloseAccountInstruction, createSyncNativeInstruction, createAssociatedTokenAccountIdempotentInstruction } = require('@solana/spl-token');
        const { SystemProgram } = require('@solana/web3.js');

        // 1. Закрываем WSOL аккаунт → получаем нативный SOL на кошелек
        const closeWSOLIx = createCloseAccountInstruction(
            userWSOLAccount,     // account to close (WSOL аккаунт с займом)
            wallet.publicKey,    // destination (кошелек получит нативный SOL)
            wallet.publicKey     // owner
        );

        // 2. Создаем новый WSOL аккаунт для repay (после Meteora операций)
        const createNewWSOLIx = createAssociatedTokenAccountIdempotentInstruction(
            wallet.publicKey,    // payer
            userWSOLAccount,     // ata (тот же адрес)
            wallet.publicKey,    // owner
            WSOL_MINT           // mint
        );

        // 3. Переводим SOL обратно в WSOL аккаунт для REPAY
        // После closeAccount + closePosition у нас ~1.047 SOL, переводим максимум
        const solForWSol = 1.045; // Переводим 1.045 SOL (оставляем 0.002 SOL на кошельке для fees)
        const solToWSOLIx = SystemProgram.transfer({
            fromPubkey: wallet.publicKey,
            toPubkey: userWSOLAccount,
            lamports: Math.floor(solForWSol * 1e9) // 1.045 SOL для REPAY
        });

        // 4. Синхронизируем WSOL аккаунт
        const syncWSOLIx = createSyncNativeInstruction(userWSOLAccount);

        // 🔧 РАЗДЕЛЯЕМ METEORA ИНСТРУКЦИИ
        console.log(`\n🔍 РАЗДЕЛЕНИЕ METEORA ИНСТРУКЦИЙ:`);
        console.log(`📊 Всего Meteora инструкций: ${meteoraInstructions.length}`);

        // Ищем InitializePosition инструкцию (Meteora DLMM Program)
        const METEORA_PROGRAM = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');
        let initializePositionIndex = -1;

        meteoraInstructions.forEach((ix, index) => {
            console.log(`   Инструкция ${index}: ${ix.programId.toString().slice(0, 8)}...`);
            if (ix.programId.equals(METEORA_PROGRAM) && initializePositionIndex === -1) {
                initializePositionIndex = index;
                console.log(`      ✅ Найдена InitializePosition на позиции ${index}!`);
            }
        });

        if (initializePositionIndex === -1) {
            throw new Error('InitializePosition инструкция не найдена!');
        }

        // Разделяем инструкции
        const preInitializeIx = meteoraInstructions.slice(0, initializePositionIndex);
        const initializePositionIx = meteoraInstructions[initializePositionIndex];
        const postInitializeIx = meteoraInstructions.slice(initializePositionIndex + 1);

        console.log(`✅ Pre-Initialize инструкции: ${preInitializeIx.length}`);
        console.log(`✅ InitializePosition: найдена`);
        console.log(`✅ Post-Initialize инструкции: ${postInitializeIx.length}`);

        const allInstructions = [
            ...borrowIx.instructions,           // 1. Занимаем SOL → пользовательский WSOL аккаунт
            closeWSOLIx,                        // 2. Закрываем WSOL → получаем нативный SOL на кошелек
            ...preInitializeIx,                 // 3. Pre-Initialize инструкции (ComputeBudget и др.)
            initializePositionIx,               // 4. InitializePosition (используя нативный SOL с кошелька!)
            createNewWSOLIx,                    // 5. Создаем новый WSOL аккаунт для AddLiquidityByStrategy2
            solToWSOLIx,                        // 6. Переводим SOL → WSOL для AddLiquidityByStrategy2
            syncWSOLIx,                         // 7. Синхронизируем WSOL аккаунт
            ...postInitializeIx,                // 8. AddLiquidityByStrategy2 и другие (используют пересозданный WSOL!)
            ...repayIx.instructions             // 9. Возвращаем займ
        ];

        console.log(`📊 Всего инструкций: ${allInstructions.length}`);
        console.log(`\n🔄 ПОЛНЫЙ ТОРГОВЫЙ ЦИКЛ С FLASH LOAN:`);
        console.log(`   1. BORROW: MarginFi займ → получаем капитал`);
        console.log(`   2. CLOSE WSOL: Конвертируем WSOL → нативный SOL`);
        console.log(`   3. INITIALIZE POSITION: Создаем торговую позицию`);
        console.log(`   4. ADD LIQUIDITY: Добавляем ликвидность (масштабируется на миллионы!)`);
        console.log(`   5. CREATE WSOL: Пересоздаем WSOL аккаунт`);
        console.log(`   6. SOL→WSOL: Конвертируем SOL → WSOL`);
        console.log(`   7. SYNC: Синхронизируем WSOL аккаунт`);
        console.log(`   8. REMOVE LIQUIDITY: Забираем ликвидность (выходим из позиции)`);
        console.log(`   9. CLOSE POSITION: Закрываем позицию → возвращаем rent`);
        console.log(`   10. REPAY: Возвращаем займ → полный цикл завершен!`);
        console.log(`\n🎯 В РЕАЛЬНОЙ ТОРГОВЛЕ: пропускаете пункт 9 - оставляете position открытым\n`);

        // 🔥 ЗАГРУЖАЕМ ALT ТАБЛИЦЫ ДЛЯ СЖАТИЯ
        console.log('🔧 Загрузка ALT таблиц для сжатия...');
        const altTables = loadALTTablesDirectly();

        // 🔥 СОЗДАНИЕ FLASH LOAN ИНСТРУКЦИЙ
        const startFlashLoanIx = await marginfiAccount.makeBeginFlashLoanIx(allInstructions.length + 1); // +1 для END
        const endFlashLoanIx = await marginfiAccount.makeEndFlashLoanIx([]);

        // 🔥 ПОЛНАЯ СТРУКТУРА С FLASH LOAN
        const fullInstructions = [
            ...startFlashLoanIx.instructions,
            ...allInstructions,
            ...endFlashLoanIx.instructions
        ].filter(ix => ix !== undefined && ix !== null); // Убираем undefined инструкции

        console.log(`📊 Полная структура: ${fullInstructions.length} инструкций`);

        // 🔍 ДИАГНОСТИКА ИНСТРУКЦИЙ
        console.log(`\n🔍 ДИАГНОСТИКА ИНСТРУКЦИЙ:`);
        fullInstructions.forEach((ix, index) => {
            if (!ix) {
                console.log(`❌ Инструкция ${index}: undefined/null`);
            } else if (!ix.programId) {
                console.log(`❌ Инструкция ${index}: нет programId`);
            } else if (!ix.keys) {
                console.log(`❌ Инструкция ${index}: нет keys`);
            } else {
                console.log(`✅ Инструкция ${index}: ${ix.programId.toString().slice(0, 8)}... (${ix.keys.length} keys)`);
                // Проверяем каждый аккаунт
                ix.keys.forEach((key, keyIndex) => {
                    if (!key.pubkey) {
                        console.log(`   ❌ Key ${keyIndex}: нет pubkey`);
                    } else if (typeof key.pubkey.toBase58 !== 'function') {
                        console.log(`   ❌ Key ${keyIndex}: не PublicKey объект`);
                    }
                });
            }
        });

        // 🔥 СОЗДАЕМ VERSIONED TRANSACTION С ALT СЖАТИЕМ
        const { blockhash } = await connection.getLatestBlockhash();

        const messageV0 = new TransactionMessage({
            payerKey: wallet.publicKey,
            recentBlockhash: blockhash,
            instructions: fullInstructions
        }).compileToV0Message(altTables);

        const versionedTx = new VersionedTransaction(messageV0);
        console.log(`📊 Транзакция сжата с ${altTables.length} ALT таблицами`);

        // 🔥 ПОДПИСЫВАЕМ ТРАНЗАКЦИЮ (ТОЛЬКО НУЖНЫЕ SIGNERS!)
        versionedTx.sign([wallet, newPosition]);
        console.log('✅ Flash Loan транзакция создана и подписана!');

        // 🔥 ВЫПОЛНЕНИЕ ТРАНЗАКЦИИ
        console.log('📤 Отправка Flash Loan транзакции...');
        let signature;
        let transactionSuccess = false;

        try {
            signature = await connection.sendTransaction(versionedTx);
            console.log(`📝 Signature: ${signature}`);

            // Ждем подтверждения транзакции
            const confirmation = await connection.confirmTransaction(signature, 'confirmed');

            if (confirmation.value.err) {
                console.log(`❌ Транзакция FAILED: ${JSON.stringify(confirmation.value.err)}`);
                transactionSuccess = false;
            } else {
                console.log(`🎉 УСПЕХ! Position создан на пуле!`);
                console.log(`🔑 Position Address: ${newPosition.publicKey.toString()}`);
                transactionSuccess = true;
            }
        } catch (error) {
            console.log(`❌ Ошибка выполнения транзакции: ${error.message}`);
            transactionSuccess = false;
        }

        // 🔍 ПРОВЕРЯЕМ БАЛАНСЫ ПОСЛЕ ВЫПОЛНЕНИЯ ТРАНЗАКЦИИ (ДАЖЕ ПРИ ОШИБКЕ!)
        console.log(`\n🔍 ПРОВЕРКА БАЛАНСОВ ПОСЛЕ ВЫПОЛНЕНИЯ ТРАНЗАКЦИИ:`);

        try {
            const wsolBalanceAfter = await connection.getTokenAccountBalance(userWSOLAccount);
            console.log(`💰 WSOL баланс ПОСЛЕ: ${wsolBalanceAfter.value.uiAmount} WSOL`);

            // Сравниваем с балансами ДО
            const wsolDiff = wsolBalanceAfter.value.uiAmount - 0.00001;
            console.log(`📊 ИЗМЕНЕНИЕ WSOL: ${wsolDiff > 0 ? '+' : ''}${wsolDiff.toFixed(5)} WSOL`);

            if (wsolDiff > 0) {
                console.log(`✅ ТОКЕНЫ ОТ ЗАЙМА ПОСТУПИЛИ НА БАЛАНС!`);
            } else {
                console.log(`❌ ТОКЕНЫ ОТ ЗАЙМА НЕ ПОСТУПИЛИ НА БАЛАНС!`);
            }
        } catch (e) {
            console.log(`❌ WSOL аккаунт не найден или пустой ПОСЛЕ транзакции`);
        }

        const solBalanceAfter = await connection.getBalance(wallet.publicKey);
        console.log(`💰 SOL баланс кошелька ПОСЛЕ: ${solBalanceAfter / 1e9} SOL`);
        const solDiff = (solBalanceAfter / 1e9) - 0.004437415;
        console.log(`📊 ИЗМЕНЕНИЕ SOL: ${solDiff > 0 ? '+' : ''}${solDiff.toFixed(9)} SOL`);

        // 💾 СОХРАНЕНИЕ РЕЗУЛЬТАТА
        const result = {
            poolAddress: poolAddress,
            positionAddress: newPosition.publicKey.toString(),
            signature: signature,
            timestamp: new Date().toISOString()
        };

        fs.writeFileSync('marginfi-sdk-result.json', JSON.stringify(result, null, 2));
        console.log('💾 Результат сохранен в marginfi-sdk-result.json');

    } catch (error) {
        console.error('❌ ОШИБКА:', error);
        
        if (error.message) {
            console.error('📝 Сообщение:', error.message);
        }
        
        if (error.logs) {
            console.error('📚 Логи:', error.logs);
        }
    }
}

// Запуск
if (require.main === module) {
    console.log('🔥 FLASH LOAN С METEORA POSITION');
    console.log('💡 Создание position на конкретном пуле\n');

    // Получаем номер пула из аргументов командной строки
    const poolIndex = parseInt(process.argv[2]) || 0;
    console.log(`🎯 Запуск для пула ${poolIndex + 1}`);

    createPositionOnPool(poolIndex).catch(console.error);
}

module.exports = { createPositionOnPool, createPositionWithMarginfiSDK };
