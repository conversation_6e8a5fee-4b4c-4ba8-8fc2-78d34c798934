#!/usr/bin/env python3
"""
🎯 MASTER BUG HUNTING COORDINATOR
Главный координатор всей системы поиска уязвимостей
Интеграция 90+ стратегий + Immunefi + Intelligent Retest
"""

import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import logging
from pathlib import Path

# Импорт всех компонентов системы
from unified_data_management_system import UnifiedDataManager, TestTarget
from strategy_integration_engine import StrategyIntegrationEngine
from intelligent_retest_system import IntelligentRetestSystem, RetestReason
from simple_immunefi_parser import SimpleImmunefiBountyParser
from immunefi_prioritizer import ImmunefiBountyPrioritizer

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('master_bug_hunting.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class MasterBugHuntingCoordinator:
    """Главный координатор системы поиска уязвимостей"""
    
    def __init__(self):
        self.data_manager = None
        self.strategy_engine = None
        self.retest_system = None
        self.immunefi_parser = None
        self.prioritizer = None
        
        # Статистика сессии
        self.session_stats = {
            'session_id': f"master_session_{int(time.time())}",
            'start_time': datetime.now(),
            'targets_processed': 0,
            'strategies_executed': 0,
            'vulnerabilities_found': 0,
            'total_execution_time': 0.0,
            'immunefi_programs_imported': 0,
            'retests_scheduled': 0,
            'retests_executed': 0
        }
        
        # Конфигурация
        self.config = {
            'max_concurrent_targets': 5,
            'max_strategies_per_target': 15,
            'retest_interval_minutes': 30,
            'immunefi_refresh_hours': 6,
            'auto_retest_enabled': True,
            'high_value_threshold': 0.8,
            'vulnerability_confidence_threshold': 0.7
        }
    
    async def __aenter__(self):
        """Асинхронная инициализация"""
        logger.info("🚀 Инициализация Master Bug Hunting Coordinator...")
        
        # Инициализация компонентов
        self.data_manager = UnifiedDataManager()
        await self.data_manager.__aenter__()
        
        self.strategy_engine = StrategyIntegrationEngine(self.data_manager)
        await self.strategy_engine.initialize_strategies()
        
        self.retest_system = IntelligentRetestSystem(self.data_manager, self.strategy_engine)
        
        self.immunefi_parser = SimpleImmunefiBountyParser()
        await self.immunefi_parser.__aenter__()
        
        self.prioritizer = ImmunefiBountyPrioritizer()
        
        logger.info("✅ Все компоненты инициализированы")
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Закрытие ресурсов"""
        if self.immunefi_parser:
            await self.immunefi_parser.__aexit__(exc_type, exc_val, exc_tb)
        if self.data_manager:
            await self.data_manager.__aexit__(exc_type, exc_val, exc_tb)
    
    async def run_full_hunting_cycle(self, 
                                   max_targets: Optional[int] = None,
                                   target_types: Optional[List[str]] = None,
                                   strategy_types: Optional[List[str]] = None) -> Dict[str, Any]:
        """Запуск полного цикла поиска уязвимостей"""
        logger.info("🎯 ЗАПУСК ПОЛНОГО ЦИКЛА ПОИСКА УЯЗВИМОСТЕЙ")
        logger.info("=" * 60)
        
        cycle_start_time = time.time()
        
        try:
            # Этап 1: Обновление данных Immunefi
            await self._refresh_immunefi_data()
            
            # Этап 2: Получение целей для тестирования
            targets = await self._get_testing_targets(max_targets, target_types)
            
            # Этап 3: Массовое тестирование
            testing_results = await self._execute_mass_testing(targets, strategy_types)
            
            # Этап 4: Анализ результатов и планирование ретестов
            await self._analyze_and_schedule_retests(testing_results)
            
            # Этап 5: Выполнение ретестов
            if self.config['auto_retest_enabled']:
                await self._execute_scheduled_retests()
            
            # Этап 6: Генерация отчета
            cycle_report = await self._generate_cycle_report(cycle_start_time)
            
            logger.info("🎉 ПОЛНЫЙ ЦИКЛ ЗАВЕРШЕН УСПЕШНО")
            return cycle_report
            
        except Exception as e:
            logger.error(f"❌ Ошибка в полном цикле: {e}")
            raise
    
    async def _refresh_immunefi_data(self):
        """Обновление данных Immunefi"""
        logger.info("📡 Обновление данных Immunefi...")
        
        try:
            # Получение программ Immunefi
            programs = await self.immunefi_parser.fetch_bounty_list()
            
            if programs:
                # Приоритизация программ
                self.prioritizer.load_programs(programs)
                scored_programs = self.prioritizer.calculate_priority_scores()
                
                # Преобразование в формат для базы данных
                immunefi_data = []
                for scored_program in scored_programs:
                    # Поиск исходных данных программы
                    original_program = next(
                        (p for p in programs if p.get('name') == scored_program.program_name),
                        None
                    )
                    
                    if original_program:
                        program_data = original_program.copy()
                        program_data.update({
                            'priority_score': scored_program.total_score,
                            'success_probability': scored_program.success_probability,
                            'estimated_time_hours': scored_program.estimated_time_hours,
                        })
                        immunefi_data.append(program_data)
                
                # Импорт в базу данных
                imported_count = await self.data_manager.import_immunefi_data(immunefi_data)
                self.session_stats['immunefi_programs_imported'] = imported_count
                
                logger.info(f"✅ Импортировано {imported_count} программ Immunefi")
            else:
                logger.warning("⚠️ Не удалось получить программы Immunefi")
                
        except Exception as e:
            logger.error(f"❌ Ошибка обновления Immunefi данных: {e}")
            raise
    
    async def _get_testing_targets(self, max_targets: Optional[int], target_types: Optional[List[str]]) -> List[TestTarget]:
        """Получение целей для тестирования"""
        logger.info("🎯 Получение целей для тестирования...")
        
        # Параметры для получения целей
        limit = max_targets or 50
        min_priority = 0.3  # Минимальный приоритет
        
        targets = []
        
        if target_types:
            # Получаем цели по типам
            for target_type in target_types:
                type_targets = await self.data_manager.get_targets_for_testing(
                    target_type=target_type,
                    min_priority=min_priority,
                    limit=limit // len(target_types)
                )
                targets.extend(type_targets)
        else:
            # Получаем все доступные цели
            targets = await self.data_manager.get_targets_for_testing(
                min_priority=min_priority,
                limit=limit
            )
        
        # Сортируем по приоритету
        targets.sort(key=lambda x: x.priority_score, reverse=True)
        
        logger.info(f"📋 Выбрано {len(targets)} целей для тестирования")
        return targets
    
    async def _execute_mass_testing(self, targets: List[TestTarget], strategy_types: Optional[List[str]]) -> List[Dict[str, Any]]:
        """Выполнение массового тестирования"""
        logger.info(f"🚀 Массовое тестирование {len(targets)} целей...")
        
        # Ограничиваем количество одновременных тестов
        semaphore = asyncio.Semaphore(self.config['max_concurrent_targets'])
        
        # Создаем задачи для параллельного выполнения
        tasks = [
            self._test_single_target(target, strategy_types, semaphore)
            for target in targets
        ]
        
        # Выполняем все задачи
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Обрабатываем результаты
        successful_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"Ошибка тестирования цели {targets[i].name}: {result}")
            else:
                successful_results.append(result)
                self.session_stats['targets_processed'] += 1
                self.session_stats['strategies_executed'] += result['summary']['total_strategies']
                self.session_stats['vulnerabilities_found'] += result['summary']['vulnerabilities_found']
        
        logger.info(f"✅ Завершено тестирование {len(successful_results)}/{len(targets)} целей")
        return successful_results
    
    async def _test_single_target(self, target: TestTarget, strategy_types: Optional[List[str]], semaphore: asyncio.Semaphore) -> Dict[str, Any]:
        """Тестирование одной цели"""
        async with semaphore:
            logger.info(f"🎯 Тестирование: {target.name}")
            
            start_time = time.time()
            
            try:
                # Выполняем все стратегии для цели
                results = await self.strategy_engine.execute_all_strategies(
                    target, 
                    strategy_types=strategy_types
                )
                
                execution_time = time.time() - start_time
                self.session_stats['total_execution_time'] += execution_time
                
                # Фильтруем высокоуверенные уязвимости
                high_confidence_vulns = [
                    r for r in results['results'] 
                    if r.vulnerability_found and r.confidence >= self.config['vulnerability_confidence_threshold']
                ]
                
                if high_confidence_vulns:
                    logger.info(f"🐛 Найдено {len(high_confidence_vulns)} высокоуверенных уязвимостей в {target.name}")
                
                return {
                    'target': target,
                    'summary': results['summary'],
                    'high_confidence_vulnerabilities': high_confidence_vulns,
                    'all_results': results['results'],
                    'execution_time': execution_time
                }
                
            except Exception as e:
                logger.error(f"Ошибка тестирования {target.name}: {e}")
                raise
    
    async def _analyze_and_schedule_retests(self, testing_results: List[Dict[str, Any]]):
        """Анализ результатов и планирование ретестов"""
        logger.info("📊 Анализ результатов и планирование ретестов...")
        
        # Анализируем кандидатов для ретестирования
        retest_candidates = await self.retest_system.analyze_retest_candidates()
        
        # Добавляем специальные ретесты на основе текущих результатов
        for result in testing_results:
            target = result['target']
            
            # Высокоценные цели с уязвимостями - приоритетный ретест
            if (target.priority_score >= self.config['high_value_threshold'] and 
                result['high_confidence_vulnerabilities']):
                
                from intelligent_retest_system import RetestTask
                
                high_value_retest = RetestTask(
                    task_id=f"highval_followup_{target.target_id}_{int(time.time())}",
                    target_id=target.target_id,
                    reason=RetestReason.HIGH_VALUE_TARGET,
                    priority=1.0,
                    strategies_to_use=list(self.strategy_engine.loaded_strategies.keys())[:20],
                    created_at=datetime.now(),
                    scheduled_for=datetime.now() + timedelta(hours=1),
                    metadata={
                        'followup_retest': True,
                        'vulnerabilities_found': len(result['high_confidence_vulnerabilities'])
                    }
                )
                
                await self.retest_system.schedule_retest(high_value_retest)
                self.session_stats['retests_scheduled'] += 1
        
        # Планируем обычные ретесты
        for candidate in retest_candidates[:20]:  # Ограничиваем количество
            await self.retest_system.schedule_retest(candidate)
            self.session_stats['retests_scheduled'] += 1
        
        logger.info(f"📅 Запланировано {self.session_stats['retests_scheduled']} ретестов")
    
    async def _execute_scheduled_retests(self):
        """Выполнение запланированных ретестов"""
        logger.info("🔄 Выполнение запланированных ретестов...")
        
        try:
            # Выполняем очередь ретестов
            await self.retest_system.execute_retest_queue(max_concurrent=3)
            
            # Получаем статистику выполненных ретестов
            retest_stats = await self.retest_system.get_retest_statistics()
            self.session_stats['retests_executed'] = retest_stats.get('completed_tasks', 0)
            
            logger.info(f"✅ Выполнено ретестов: {self.session_stats['retests_executed']}")
            
        except Exception as e:
            logger.error(f"❌ Ошибка выполнения ретестов: {e}")
    
    async def _generate_cycle_report(self, cycle_start_time: float) -> Dict[str, Any]:
        """Генерация отчета о цикле"""
        cycle_duration = time.time() - cycle_start_time
        self.session_stats['cycle_duration'] = cycle_duration
        
        # Получаем общую статистику из базы данных
        db_report = await self.data_manager.generate_testing_report(
            session_id=self.session_stats['session_id']
        )
        
        # Получаем статистику стратегий
        strategy_performance = await self.data_manager.get_strategy_performance()
        
        # Получаем статистику ретестов
        retest_stats = await self.retest_system.get_retest_statistics()
        
        # Формируем итоговый отчет
        final_report = {
            'session_info': self.session_stats,
            'cycle_duration_minutes': cycle_duration / 60,
            'database_statistics': db_report,
            'strategy_performance': strategy_performance,
            'retest_statistics': retest_stats,
            'efficiency_metrics': {
                'targets_per_hour': self.session_stats['targets_processed'] / (cycle_duration / 3600) if cycle_duration > 0 else 0,
                'vulnerabilities_per_hour': self.session_stats['vulnerabilities_found'] / (cycle_duration / 3600) if cycle_duration > 0 else 0,
                'avg_strategies_per_target': self.session_stats['strategies_executed'] / self.session_stats['targets_processed'] if self.session_stats['targets_processed'] > 0 else 0,
                'vulnerability_discovery_rate': self.session_stats['vulnerabilities_found'] / self.session_stats['strategies_executed'] if self.session_stats['strategies_executed'] > 0 else 0
            },
            'recommendations': self._generate_recommendations()
        }
        
        # Сохраняем отчет
        report_filename = f"master_report_{self.session_stats['session_id']}.json"
        with open(report_filename, 'w', encoding='utf-8') as f:
            json.dump(final_report, f, indent=2, ensure_ascii=False, default=str)
        
        logger.info(f"📄 Отчет сохранен: {report_filename}")
        return final_report
    
    def _generate_recommendations(self) -> List[str]:
        """Генерация рекомендаций на основе результатов"""
        recommendations = []
        
        # Анализ эффективности
        if self.session_stats['vulnerabilities_found'] == 0:
            recommendations.append("Рассмотрите расширение набора стратегий или изменение критериев отбора целей")
        
        if self.session_stats['targets_processed'] < 10:
            recommendations.append("Увеличьте количество тестируемых целей для лучшего покрытия")
        
        if self.session_stats['retests_scheduled'] > self.session_stats['retests_executed']:
            recommendations.append("Оптимизируйте выполнение ретестов для повышения эффективности")
        
        # Рекомендации по времени
        avg_time_per_target = self.session_stats['total_execution_time'] / self.session_stats['targets_processed'] if self.session_stats['targets_processed'] > 0 else 0
        if avg_time_per_target > 300:  # Более 5 минут на цель
            recommendations.append("Рассмотрите оптимизацию стратегий для сокращения времени выполнения")
        
        return recommendations
    
    async def get_real_time_status(self) -> Dict[str, Any]:
        """Получение статуса в реальном времени"""
        return {
            'session_id': self.session_stats['session_id'],
            'uptime_minutes': (datetime.now() - self.session_stats['start_time']).total_seconds() / 60,
            'current_stats': self.session_stats,
            'loaded_strategies': len(self.strategy_engine.loaded_strategies) if self.strategy_engine else 0,
            'retest_queue_size': len(self.retest_system.retest_queue) if self.retest_system else 0,
            'active_retests': len(self.retest_system.active_tasks) if self.retest_system else 0
        }

async def main():
    """Демонстрация главного координатора"""
    print("🎯 MASTER BUG HUNTING COORDINATOR")
    print("=" * 60)
    
    async with MasterBugHuntingCoordinator() as coordinator:
        # Получение статуса
        status = await coordinator.get_real_time_status()
        print(f"📊 Статус системы: {status['loaded_strategies']} стратегий загружено")
        
        # Запуск полного цикла (ограниченный для демо)
        report = await coordinator.run_full_hunting_cycle(
            max_targets=5,  # Ограничиваем для демо
            strategy_types=['basic']  # Используем только базовые стратегии для демо
        )
        
        print(f"\n🎉 ЦИКЛ ЗАВЕРШЕН!")
        print(f"   Целей протестировано: {report['session_info']['targets_processed']}")
        print(f"   Стратегий выполнено: {report['session_info']['strategies_executed']}")
        print(f"   Уязвимостей найдено: {report['session_info']['vulnerabilities_found']}")
        print(f"   Время выполнения: {report['cycle_duration_minutes']:.1f} минут")

if __name__ == "__main__":
    asyncio.run(main())
