/**
 * 🎯 ПРАВИЛЬНЫЙ КАЛЬКУЛЯТОР METEORA АРБИТРАЖА
 * 
 * ИСПРАВЛЕННАЯ ЛОГИКА (по твоему примеру):
 * 1. Flash Loan $500K - это ОБЕСПЕЧЕНИЕ
 * 2. Покупаем только $250K SOL (50% от займа)
 * 3. Цена поднимается на +15%
 * 4. Продаем $250K SOL → получаем $287.5K (с бонусом)
 * 5. Slippage 7.1% → остается $270K
 * 6. Продаем $270K в большом пуле → slippage 4.1% → $258.93K
 * 7. Прибыль: $258.93K - $250K = $8.93K
 */

class CorrectMeteoraDualPoolCalculator {
    constructor() {
        // Реальные данные из твоего скриншота
        this.POOLS = {
            SMALL: {
                tvl: 3009838, // $3.01M реальный TVL
                sol_amount: 7489.62,
                usdc_amount: 1696475.00,
                sol_price: 167.44, // $167.44 USDC/SOL
                name: 'Meteora Real Pool (SOL-USDC)'
            },
            LARGE: {
                tvl: 7000000, // $7M большой пул
                name: 'Meteora Large Pool'
            }
        };
        
        this.FLASH_LOAN_FEE = 0.0009; // 0.09%
        this.METEORA_FEE = 0.0004; // 0.04% Meteora комиссия
        this.TRANSACTION_COST = 0.01; // $0.01 за транзакцию
        
        console.log('🎯 ПРАВИЛЬНЫЙ MeteoraDualPoolCalculator инициализирован');
        console.log(`   Реальный пул: $${this.POOLS.SMALL.tvl.toLocaleString()}`);
        console.log(`   SOL цена: $${this.POOLS.SMALL.sol_price}`);
        console.log(`   Большой пул: $${this.POOLS.LARGE.tvl.toLocaleString()}`);
    }

    /**
     * 📈 РАСЧЕТ ПОВЫШЕНИЯ ЦЕНЫ (твоя формула)
     */
    calculatePriceIncrease(buyAmount, poolTvl) {
        const ratio = buyAmount / poolTvl;
        // Твоя формула: $250K в $3M пуле = 8.33% → +15% цены (коэффициент ~1.8)
        const priceIncrease = ratio * 100 * 1.8;
        
        return {
            ratio: (ratio * 100).toFixed(2),
            priceIncrease: priceIncrease.toFixed(2),
            newPoolTvl: poolTvl + buyAmount
        };
    }

    /**
     * 💸 РАСЧЕТ SLIPPAGE (твоя формула)
     */
    calculateSlippage(sellAmount, poolTvl) {
        // Твоя формула: 1% от пула = базис для slippage
        const onePercent = poolTvl / 100;
        const slippagePercent = (sellAmount / onePercent);
        
        return {
            onePercent,
            slippagePercent: slippagePercent.toFixed(2),
            slippageLoss: sellAmount * (slippagePercent / 100)
        };
    }

    /**
     * 🎯 НОВАЯ СТРАТЕГИЯ: ДОБАВЛЕНИЕ ЛИКВИДНОСТИ + АРБИТРАЖ
     */
    calculateNewStrategy(flashLoanAmount) {
        console.log(`\n🎯 НОВАЯ СТРАТЕГИЯ: ЛИКВИДНОСТЬ + АРБИТРАЖ`);
        console.log(`💰 Flash Loan: $${flashLoanAmount.toLocaleString()} (22% от пула)`);
        console.log('=' .repeat(70));

        // Новое распределение средств
        const liquidityAmount = flashLoanAmount * 0.66; // 66% на ликвидность
        const arbitrageAmount = flashLoanAmount * 0.34; // 34% на арбитраж

        console.log(`📊 РАСПРЕДЕЛЕНИЕ СРЕДСТВ:`);
        console.log(`   💧 Добавление ликвидности: $${liquidityAmount.toLocaleString()} (66%)`);
        console.log(`   ⚡ Арбитражная позиция: $${arbitrageAmount.toLocaleString()} (34%)`);

        // 1. Добавляем ликвидность (66% от займа)
        const liquidityImpact = this.calculateLiquidityAddition(liquidityAmount, this.POOLS.SMALL.tvl);

        console.log(`\n💧 ШАГ 1 - ДОБАВЛЕНИЕ ЛИКВИДНОСТИ:`);
        console.log(`   💰 Добавляем: $${liquidityAmount.toLocaleString()}`);
        console.log(`   📈 Новый TVL: $${liquidityImpact.newTvl.toLocaleString()}`);
        console.log(`   🎯 Доля в пуле: ${liquidityImpact.sharePercent}%`);

        // 2. Арбитражная операция (34% от займа)
        const buyAmount = arbitrageAmount;
        const buyResult = this.calculatePriceIncrease(buyAmount, this.POOLS.SMALL.tvl);
        
        console.log(`📈 ШАГ 1 - ПОКУПКА В МАЛЕНЬКОМ ПУЛЕ:`);
        console.log(`   💡 Покупаем: $${buyAmount.toLocaleString()} (${(buyRatio*100)}% от Flash Loan)`);
        console.log(`   📊 Соотношение: ${buyResult.ratio}% от пула`);
        console.log(`   🚀 Повышение цены: +${buyResult.priceIncrease}%`);
        console.log(`   📈 Новый TVL: $${buyResult.newPoolTvl.toLocaleString()}`);
        
        // 2. Продаем ВСЕ купленное SOL с учетом повышенной цены
        const priceBonus = buyAmount * (parseFloat(buyResult.priceIncrease) / 100);
        const grossRevenue = buyAmount + priceBonus;
        
        // Slippage при продаже в маленьком пуле
        const slippageSmall = this.calculateSlippage(buyAmount, buyResult.newPoolTvl);
        const netRevenueSmall = grossRevenue - slippageSmall.slippageLoss - (buyAmount * this.METEORA_FEE);
        
        console.log(`\n💸 ШАГ 2 - ПРОДАЖА В МАЛЕНЬКОМ ПУЛЕ:`);
        console.log(`   💰 Продаем SOL на: $${buyAmount.toLocaleString()}`);
        console.log(`   🎁 Бонус от цены: +$${Math.round(priceBonus).toLocaleString()}`);
        console.log(`   📉 Slippage: ${slippageSmall.slippagePercent}% = -$${Math.round(slippageSmall.slippageLoss).toLocaleString()}`);
        console.log(`   💳 Комиссия Meteora: -$${Math.round(buyAmount * this.METEORA_FEE).toLocaleString()}`);
        console.log(`   ✅ Получаем: $${Math.round(netRevenueSmall).toLocaleString()}`);
        
        // 3. Продаем в большом пуле
        const slippageLarge = this.calculateSlippage(netRevenueSmall, this.POOLS.LARGE.tvl);
        const netRevenueLarge = netRevenueSmall - slippageLarge.slippageLoss - (netRevenueSmall * this.METEORA_FEE);
        
        console.log(`\n🌊 ШАГ 3 - ПРОДАЖА В БОЛЬШОМ ПУЛЕ:`);
        console.log(`   💰 Продаем: $${Math.round(netRevenueSmall).toLocaleString()}`);
        console.log(`   📉 Slippage: ${slippageLarge.slippagePercent}% = -$${Math.round(slippageLarge.slippageLoss).toLocaleString()}`);
        console.log(`   💳 Комиссия Meteora: -$${Math.round(netRevenueSmall * this.METEORA_FEE).toLocaleString()}`);
        console.log(`   ✅ Получаем: $${Math.round(netRevenueLarge).toLocaleString()}`);
        
        // 4. Возврат Flash Loan и расчет прибыли
        const flashLoanFee = flashLoanAmount * this.FLASH_LOAN_FEE;
        const totalToReturn = flashLoanAmount + flashLoanFee;
        
        // ВАЖНО: мы потратили только buyAmount, а не весь Flash Loan!
        const actualSpent = buyAmount;
        const netProfit = netRevenueLarge - actualSpent - this.TRANSACTION_COST;
        const roi = (netProfit / actualSpent * 100);
        
        console.log(`\n💰 ШАГ 4 - ФИНАЛЬНЫЙ РАСЧЕТ:`);
        console.log(`   💸 Потрачено на покупку: $${actualSpent.toLocaleString()}`);
        console.log(`   💵 Получено от продажи: $${Math.round(netRevenueLarge).toLocaleString()}`);
        console.log(`   🏦 Flash Loan комиссия: $${Math.round(flashLoanFee).toLocaleString()}`);
        console.log(`   💳 Транзакционные расходы: $${this.TRANSACTION_COST}`);
        console.log(`   🎯 ЧИСТАЯ ПРИБЫЛЬ: $${Math.round(netProfit).toLocaleString()}`);
        console.log(`   📈 ROI: ${roi.toFixed(2)}%`);
        
        return {
            flashLoanAmount,
            buyAmount: actualSpent,
            steps: {
                buy: {
                    amount: buyAmount,
                    priceIncrease: parseFloat(buyResult.priceIncrease),
                    priceBonus: Math.round(priceBonus)
                },
                sellSmall: {
                    grossRevenue: Math.round(grossRevenue),
                    slippage: parseFloat(slippageSmall.slippagePercent),
                    slippageLoss: Math.round(slippageSmall.slippageLoss),
                    netRevenue: Math.round(netRevenueSmall)
                },
                sellLarge: {
                    amount: Math.round(netRevenueSmall),
                    slippage: parseFloat(slippageLarge.slippagePercent),
                    slippageLoss: Math.round(slippageLarge.slippageLoss),
                    netRevenue: Math.round(netRevenueLarge)
                }
            },
            result: {
                totalSpent: actualSpent,
                totalReceived: Math.round(netRevenueLarge),
                flashLoanFee: Math.round(flashLoanFee),
                netProfit: Math.round(netProfit),
                roi: parseFloat(roi.toFixed(2)),
                profitable: netProfit > 0
            }
        };
    }

    /**
     * 🏆 ПОИСК ОПТИМАЛЬНОГО СООТНОШЕНИЯ ПОКУПКИ
     */
    findOptimalBuyRatio(flashLoanAmount = 500000) {
        console.log(`\n🏆 ПОИСК ОПТИМАЛЬНОГО СООТНОШЕНИЯ ПОКУПКИ`);
        console.log(`💰 Flash Loan: $${flashLoanAmount.toLocaleString()}`);
        console.log('=' .repeat(70));
        
        // Тестируем разные соотношения покупки
        const ratios = [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9];
        
        let bestResult = { result: { netProfit: -Infinity } };
        const results = [];
        
        console.log('📊 АНАЛИЗ РАЗЛИЧНЫХ СООТНОШЕНИЙ:\n');
        
        ratios.forEach(ratio => {
            const result = this.calculateCorrectArbitrage(flashLoanAmount, ratio);
            results.push(result);
            
            if (result.result.netProfit > bestResult.result.netProfit) {
                bestResult = result;
            }
            
            const status = result.result.profitable ? '✅' : '❌';
            const emoji = result.result.profitable ? '💚' : '🔴';
            
            console.log(`${status} ${(ratio*100)}% покупка ($${result.buyAmount.toLocaleString()}): ${emoji} $${result.result.netProfit.toLocaleString()} (${result.result.roi}%)`);
        });
        
        return { bestResult, allResults: results };
    }

    /**
     * 🎯 ПРОВЕРКА ТВОЕГО ПРИМЕРА
     */
    validateYourExample() {
        console.log(`\n🎯 ПРОВЕРКА ТВОЕГО ПРИМЕРА:`);
        console.log(`Flash Loan $500K → Покупка $250K → Прибыль $8,930`);
        console.log('=' .repeat(70));
        
        const result = this.calculateCorrectArbitrage(500000, 0.5); // 50% = $250K
        
        console.log(`\n📊 СРАВНЕНИЕ С ТВОИМИ РАСЧЕТАМИ:`);
        console.log(`   Твой расчет: $8,930 прибыли`);
        console.log(`   Мой расчет: $${result.result.netProfit.toLocaleString()} прибыли`);
        console.log(`   Разница: ${Math.abs(result.result.netProfit - 8930) < 1000 ? '✅ БЛИЗКО' : '❌ ОТЛИЧАЕТСЯ'}`);
        
        return result;
    }
}

/**
 * 🎯 ГЛАВНАЯ ФУНКЦИЯ
 */
async function main() {
    console.log('🎯 ЗАПУСК ПРАВИЛЬНОГО КАЛЬКУЛЯТОРА METEORA АРБИТРАЖА...\n');
    
    const calculator = new CorrectMeteoraDualPoolCalculator();
    
    try {
        // Проверяем твой пример
        const yourExample = calculator.validateYourExample();
        
        // Ищем оптимальное соотношение
        const { bestResult, allResults } = calculator.findOptimalBuyRatio();
        
        console.log('\n🎉 АНАЛИЗ ЗАВЕРШЕН!');
        console.log(`🏆 ОПТИМАЛЬНОЕ СООТНОШЕНИЕ: ${(bestResult.buyAmount / bestResult.flashLoanAmount * 100)}%`);
        console.log(`💰 МАКСИМАЛЬНАЯ ПРИБЫЛЬ: $${bestResult.result.netProfit.toLocaleString()}`);
        console.log(`📈 ROI: ${bestResult.result.roi}%`);
        console.log(`🚀 СТРАТЕГИЯ ПОДТВЕРЖДЕНА!`);
        
        return bestResult;
        
    } catch (error) {
        console.error('❌ Ошибка расчета:', error);
        return null;
    }
}

// Запуск если файл выполняется напрямую
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { CorrectMeteoraDualPoolCalculator };
