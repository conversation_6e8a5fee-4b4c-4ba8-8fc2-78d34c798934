#!/usr/bin/env node

/**
 * 📚 ОФИЦИАЛЬНЫЕ ПРИМЕРЫ METEORA DLMM
 * 
 * Из официальной документации и SDK
 */

const { Connection, PublicKey, Keypair, sendAndConfirmTransaction } = require('@solana/web3.js');
const { BN } = require('@coral-xyz/anchor');
const DLMM = require('@meteora-ag/dlmm');

// 🔧 ОФИЦИАЛЬНЫЙ ПРИМЕР 1: СОЗДАНИЕ ПОЗИЦИИ С БАЛАНСИРОВАННОЙ ЛИКВИДНОСТЬЮ
async function createBalancedPosition(connection, user, dlmmPool) {
    console.log('📊 СОЗДАНИЕ БАЛАНСИРОВАННОЙ ПОЗИЦИИ...');
    
    // Получаем активный bin
    const activeBin = await dlmmPool.getActiveBin();
    console.log(`   Активный bin: ${activeBin.binId}, цена: ${activeBin.price}`);
    
    // Определяем диапазон bins
    const TOTAL_RANGE_INTERVAL = 10; // 10 bins с каждой стороны
    const minBinId = activeBin.binId - TOTAL_RANGE_INTERVAL;
    const maxBinId = activeBin.binId + TOTAL_RANGE_INTERVAL;
    
    console.log(`   Диапазон bins: ${minBinId} до ${maxBinId}`);
    
    // Суммы для добавления
    const totalXAmount = new BN(100 * 10 ** 6); // 100 USDC (6 decimals)
    const totalYAmount = autoFillYByStrategy(
        activeBin.binId,
        dlmmPool.lbPair.binStep,
        totalXAmount,
        activeBin.xAmount,
        activeBin.yAmount,
        minBinId,
        maxBinId,
        StrategyType.Spot // Стратегия распределения
    );
    
    console.log(`   X Amount: ${totalXAmount.toString()}`);
    console.log(`   Y Amount: ${totalYAmount.toString()}`);
    
    // Создаем новую позицию
    const newPosition = new Keypair();
    
    // Создаем транзакцию
    const createPositionTx = await dlmmPool.initializePositionAndAddLiquidityByStrategy({
        positionPubKey: newPosition.publicKey,
        user: user.publicKey,
        totalXAmount,
        totalYAmount,
        strategy: {
            maxBinId,
            minBinId,
            strategyType: StrategyType.Spot, // Равномерное распределение
        },
    });
    
    console.log('   ✅ Транзакция создана');
    return { createPositionTx, newPosition };
}

// 🔧 ОФИЦИАЛЬНЫЙ ПРИМЕР 2: ОДНОСТОРОННЯЯ ЛИКВИДНОСТЬ
async function createOneSidedPosition(connection, user, dlmmPool) {
    console.log('📊 СОЗДАНИЕ ОДНОСТОРОННЕЙ ПОЗИЦИИ...');
    
    const activeBin = await dlmmPool.getActiveBin();
    
    // Диапазон только справа от активного bin (для токена X)
    const TOTAL_RANGE_INTERVAL = 10;
    const minBinId = activeBin.binId;
    const maxBinId = activeBin.binId + TOTAL_RANGE_INTERVAL * 2;
    
    console.log(`   Односторонний диапазон: ${minBinId} до ${maxBinId}`);
    
    // Только токен X (USDC)
    const totalXAmount = new BN(100 * 10 ** 6); // 100 USDC
    const totalYAmount = new BN(0); // 0 SOL
    
    console.log(`   Только X Amount: ${totalXAmount.toString()}`);
    
    const newOneSidePosition = new Keypair();
    
    const createPositionTx = await dlmmPool.initializePositionAndAddLiquidityByStrategy({
        positionPubKey: newOneSidePosition.publicKey,
        user: user.publicKey,
        totalXAmount,
        totalYAmount,
        strategy: {
            maxBinId,
            minBinId,
            strategyType: StrategyType.Spot,
        },
    });
    
    console.log('   ✅ Односторонняя позиция создана');
    return { createPositionTx, newOneSidePosition };
}

// 🔧 ОФИЦИАЛЬНЫЙ ПРИМЕР 3: ДОБАВЛЕНИЕ ЛИКВИДНОСТИ К СУЩЕСТВУЮЩЕЙ ПОЗИЦИИ
async function addLiquidityToExistingPosition(connection, user, dlmmPool, existingPosition) {
    console.log('📊 ДОБАВЛЕНИЕ ЛИКВИДНОСТИ К СУЩЕСТВУЮЩЕЙ ПОЗИЦИИ...');
    
    const activeBin = await dlmmPool.getActiveBin();
    
    const TOTAL_RANGE_INTERVAL = 10;
    const minBinId = activeBin.binId - TOTAL_RANGE_INTERVAL;
    const maxBinId = activeBin.binId + TOTAL_RANGE_INTERVAL;
    
    // Дополнительная ликвидность
    const totalXAmount = new BN(50 * 10 ** 6); // 50 USDC
    const totalYAmount = autoFillYByStrategy(
        activeBin.binId,
        dlmmPool.lbPair.binStep,
        totalXAmount,
        activeBin.xAmount,
        activeBin.yAmount,
        minBinId,
        maxBinId,
        StrategyType.Spot
    );
    
    console.log(`   Добавляем X: ${totalXAmount.toString()}`);
    console.log(`   Добавляем Y: ${totalYAmount.toString()}`);
    
    // Добавляем ликвидность
    const addLiquidityTx = await dlmmPool.addLiquidityByStrategy({
        positionPubKey: existingPosition.publicKey,
        user: user.publicKey,
        totalXAmount,
        totalYAmount,
        strategy: {
            maxBinId,
            minBinId,
            strategyType: StrategyType.Spot,
        },
    });
    
    console.log('   ✅ Ликвидность добавлена');
    return addLiquidityTx;
}

// 🔧 ОФИЦИАЛЬНЫЙ ПРИМЕР 4: УДАЛЕНИЕ ЛИКВИДНОСТИ
async function removeLiquidity(connection, user, dlmmPool, userPosition) {
    console.log('📊 УДАЛЕНИЕ ЛИКВИДНОСТИ...');
    
    // Получаем bins позиции
    const binIdsToRemove = userPosition.positionData.positionBinData.map(
        (bin) => bin.binId
    );
    
    console.log(`   Удаляем из bins: ${binIdsToRemove.join(', ')}`);
    
    // Удаляем 100% ликвидности
    const removeLiquidityTx = await dlmmPool.removeLiquidity({
        position: userPosition.publicKey,
        user: user.publicKey,
        fromBinId: binIdsToRemove[0],
        toBinId: binIdsToRemove[binIdsToRemove.length - 1],
        liquiditiesBpsToRemove: new Array(binIdsToRemove.length).fill(
            new BN(100 * 100) // 100% (в базисных пунктах)
        ),
        shouldClaimAndClose: true, // Забрать комиссии и закрыть позицию
    });
    
    console.log('   ✅ Ликвидность удалена');
    return removeLiquidityTx;
}

// 🔧 ОФИЦИАЛЬНЫЙ ПРИМЕР 5: ПОЛУЧЕНИЕ ПОЗИЦИЙ ПОЛЬЗОВАТЕЛЯ
async function getUserPositions(connection, user, dlmmPool) {
    console.log('📊 ПОЛУЧЕНИЕ ПОЗИЦИЙ ПОЛЬЗОВАТЕЛЯ...');
    
    const { userPositions } = await dlmmPool.getPositionsByUserAndLbPair(
        user.publicKey
    );
    
    console.log(`   Найдено позиций: ${userPositions.length}`);
    
    userPositions.forEach((position, index) => {
        const binData = position.positionData.positionBinData;
        console.log(`   Позиция ${index + 1}:`);
        console.log(`     Адрес: ${position.publicKey.toString()}`);
        console.log(`     Bins: ${binData.length}`);
        
        binData.forEach(bin => {
            console.log(`       Bin ${bin.binId}: X=${bin.xAmount}, Y=${bin.yAmount}`);
        });
    });
    
    return userPositions;
}

// 🔧 ОФИЦИАЛЬНЫЙ ПРИМЕР 6: ПОЛУЧЕНИЕ ИНФОРМАЦИИ О КОМИССИЯХ
async function getSwapFeeInfo(connection, user, dlmmPool, userPositions) {
    console.log('📊 ПОЛУЧЕНИЕ ИНФОРМАЦИИ О КОМИССИЯХ...');
    
    for (const position of userPositions) {
        const swapFee = await DLMM.getClaimableSwapFee({
            connection,
            position: position.publicKey,
        });
        
        console.log(`   Позиция ${position.publicKey.toString().slice(0, 8)}...:`);
        console.log(`     Комиссии X: ${swapFee.feeX.toString()}`);
        console.log(`     Комиссии Y: ${swapFee.feeY.toString()}`);
    }
}

// 🔧 ОФИЦИАЛЬНЫЙ ПРИМЕР 7: ЗАБРАТЬ ВСЕ КОМИССИИ
async function claimAllFees(connection, user, dlmmPool, userPositions) {
    console.log('📊 ЗАБИРАЕМ ВСЕ КОМИССИИ...');
    
    const claimFeeTxs = await dlmmPool.claimAllSwapFee({
        owner: user.publicKey,
        positions: userPositions,
    });
    
    console.log(`   Создано транзакций: ${claimFeeTxs.length}`);
    
    return claimFeeTxs;
}

// 🔧 ВСПОМОГАТЕЛЬНАЯ ФУНКЦИЯ: autoFillYByStrategy (из SDK)
function autoFillYByStrategy(activeBinId, binStep, totalXAmount, activeBinXAmount, activeBinYAmount, minBinId, maxBinId, strategyType) {
    // Упрощенная версия - в реальности используется сложная логика из SDK
    // Для демонстрации возвращаем пропорциональную сумму
    const pricePerLamport = activeBinYAmount / activeBinXAmount;
    return new BN(Math.floor(totalXAmount.toNumber() * pricePerLamport));
}

// 🔧 КОНСТАНТЫ СТРАТЕГИЙ (из SDK)
const StrategyType = {
    Spot: 0,    // Равномерное распределение
    Curve: 1,   // Концентрированное распределение
    BidAsk: 2   // Обратное распределение
};

// 🧪 ДЕМОНСТРАЦИЯ ИСПОЛЬЗОВАНИЯ
async function demonstrateOfficialExamples() {
    console.log('🚀 ДЕМОНСТРАЦИЯ ОФИЦИАЛЬНЫХ ПРИМЕРОВ METEORA DLMM');
    console.log('=' .repeat(80));
    
    // Примечание: Это демонстрационный код
    // В реальности нужно:
    // 1. Подключиться к Solana RPC
    // 2. Создать/загрузить кошелек пользователя
    // 3. Инициализировать DLMM пул
    // 4. Выполнить транзакции
    
    console.log('📚 ОСНОВНЫЕ ФУНКЦИИ DLMM SDK:');
    console.log('   ✅ initializePositionAndAddLiquidityByStrategy - создать позицию');
    console.log('   ✅ addLiquidityByStrategy - добавить ликвидность');
    console.log('   ✅ removeLiquidity - удалить ликвидность');
    console.log('   ✅ getPositionsByUserAndLbPair - получить позиции');
    console.log('   ✅ claimAllSwapFee - забрать комиссии');
    console.log('   ✅ closePosition - закрыть позицию');
    
    console.log('\n🎯 СТРАТЕГИИ РАСПРЕДЕЛЕНИЯ:');
    console.log('   📊 Spot - равномерное распределение');
    console.log('   📈 Curve - концентрированное распределение');
    console.log('   📉 BidAsk - обратное распределение');
    
    console.log('\n💡 КЛЮЧЕВЫЕ КОНЦЕПЦИИ:');
    console.log('   🎯 Bins - дискретные ценовые диапазоны');
    console.log('   🎯 Active Bin - единственный активный bin');
    console.log('   🎯 Односторонняя ликвидность - можно добавлять только один токен');
    console.log('   🎯 Bin Step - разница между bins в базисных пунктах');
    
    console.log('\n✅ ВСЕ ПРИМЕРЫ ОСНОВАНЫ НА ОФИЦИАЛЬНОЙ ДОКУМЕНТАЦИИ!');
}

// Экспорт функций
module.exports = {
    createBalancedPosition,
    createOneSidedPosition,
    addLiquidityToExistingPosition,
    removeLiquidity,
    getUserPositions,
    getSwapFeeInfo,
    claimAllFees,
    StrategyType,
    demonstrateOfficialExamples
};

// Запуск демонстрации
if (require.main === module) {
    demonstrateOfficialExamples().catch(console.error);
}
