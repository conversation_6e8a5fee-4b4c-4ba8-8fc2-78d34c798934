/**
 * 🎯 METEORA DLMM SINGLE-SIDED LIQUIDITY АРБИТРАЖ
 * 
 * ПРАВИЛЬНАЯ СТРАТЕГИЯ:
 * 1. Flash Loan USDC
 * 2. Добавляем односторонную USDC ликвидность в bins ниже текущей цены
 * 3. Продаем SOL → цена падает → наши bins становятся активными
 * 4. Покупаем SOL дешево из нашей ликвидности
 * 5. Продаем SOL дороже в большом пуле
 * 6. Возвращаем Flash Loan + прибыль
 */

class MeteoraDLMMSingleSidedCalculator {
    constructor() {
        // Реальные данные пулов
        this.POOLS = {
            SMALL: {
                tvl: 3000000, // $3M базовый пул
                currentPrice: 167.44, // $167.44 USDC/SOL
                binStep: 0.0025, // 0.25% bin step (25 basis points)
                name: 'Meteora DLMM Pool (SOL-USDC)'
            },
            LARGE: {
                tvl: 7000000, // $7M большой пул
                name: 'Meteora Large Pool'
            }
        };
        
        this.FLASH_LOAN_FEE = 0.0009; // 0.09%
        this.METEORA_BASE_FEE = 0.0005; // 0.05% базовая комиссия
        this.METEORA_DYNAMIC_FEE_MAX = 0.10; // До 10% в волатильности
        this.TRANSACTION_COST = 0.01; // $0.01 за транзакцию
        
        console.log('🎯 DLMM Single-Sided Calculator инициализирован');
        console.log(`   Маленький пул: $${this.POOLS.SMALL.tvl.toLocaleString()}`);
        console.log(`   Текущая цена SOL: $${this.POOLS.SMALL.currentPrice}`);
        console.log(`   Bin Step: ${(this.POOLS.SMALL.binStep * 100).toFixed(2)}%`);
        console.log(`   Большой пул: $${this.POOLS.LARGE.tvl.toLocaleString()}`);
    }

    /**
     * 📊 РАСЧЕТ BIN СТРУКТУРЫ
     */
    calculateBinStructure(currentPrice, binStep, numBins = 20) {
        const bins = [];
        
        // Создаем bins выше и ниже текущей цены
        for (let i = -numBins; i <= numBins; i++) {
            const binPrice = currentPrice * Math.pow(1 + binStep, i);
            bins.push({
                index: i,
                price: binPrice,
                isActive: i === 0,
                side: i < 0 ? 'USDC_ONLY' : i > 0 ? 'SOL_ONLY' : 'BOTH_TOKENS'
            });
        }
        
        return bins;
    }

    /**
     * 💧 РАСЧЕТ ОДНОСТОРОННЕЙ ЛИКВИДНОСТИ
     */
    calculateSingleSidedLiquidity(usdcAmount, targetBins) {
        // Распределяем USDC по bins ниже текущей цены
        const liquidityPerBin = usdcAmount / targetBins.length;
        
        const liquidityDistribution = targetBins.map(bin => ({
            binIndex: bin.index,
            binPrice: bin.price,
            usdcAmount: liquidityPerBin,
            solAmount: 0, // Только USDC
            liquidityValue: liquidityPerBin
        }));
        
        return {
            totalUSDC: usdcAmount,
            totalSOL: 0,
            binsUsed: targetBins.length,
            liquidityPerBin,
            distribution: liquidityDistribution
        };
    }

    /**
     * 📉 РАСЧЕТ ВЛИЯНИЯ ПРОДАЖИ SOL НА ЦЕНУ
     */
    calculatePriceImpactFromSell(solSellAmount, currentPrice, poolTvl) {
        // Упрощенная модель: продажа SOL снижает цену
        const solValueUSD = solSellAmount * currentPrice;
        const impactRatio = solValueUSD / poolTvl;
        
        // Нелинейное влияние на цену (квадратичная функция)
        const priceDropPercent = impactRatio * 100 * 1.5; // Коэффициент 1.5
        const newPrice = currentPrice * (1 - priceDropPercent / 100);
        
        return {
            solSellAmount,
            solValueUSD,
            impactRatio: (impactRatio * 100).toFixed(2),
            priceDropPercent: priceDropPercent.toFixed(2),
            oldPrice: currentPrice,
            newPrice: newPrice.toFixed(2),
            priceDrop: (currentPrice - newPrice).toFixed(2)
        };
    }

    /**
     * 🎯 РАСЧЕТ АРБИТРАЖА ИЗ НАШЕЙ ЛИКВИДНОСТИ
     */
    calculateArbitrageFromOurLiquidity(liquidityDistribution, newPrice) {
        // Находим bins, которые стали активными после падения цены
        // Используем более широкий диапазон для поиска активных bins
        const activeBins = liquidityDistribution.filter(bin =>
            bin.binPrice >= newPrice * 0.90 && bin.binPrice <= newPrice * 1.10
        );

        console.log(`   🔍 Отладка: новая цена $${newPrice}, найдено bins: ${activeBins.length}`);
        if (activeBins.length > 0) {
            console.log(`   📊 Цены активных bins: ${activeBins.map(b => `$${b.binPrice.toFixed(2)}`).join(', ')}`);
        }

        if (activeBins.length === 0) {
            return {
                activeBins: 0,
                availableUSDC: 0,
                solCanBuy: 0,
                avgBuyPrice: 0,
                binsUsed: []
            };
        }

        // Рассчитываем сколько SOL можем купить из нашей ликвидности
        const totalAvailableUSDC = activeBins.reduce((sum, bin) => sum + bin.usdcAmount, 0);
        const avgBuyPrice = activeBins.reduce((sum, bin) => sum + bin.binPrice, 0) / activeBins.length;
        const solCanBuy = totalAvailableUSDC / avgBuyPrice;

        return {
            activeBins: activeBins.length,
            availableUSDC: totalAvailableUSDC,
            avgBuyPrice,
            solCanBuy,
            binsUsed: activeBins.map(b => b.binIndex)
        };
    }

    /**
     * 💸 РАСЧЕТ SLIPPAGE В БОЛЬШОМ ПУЛЕ
     */
    calculateLargePoolSlippage(sellAmount, poolTvl) {
        const onePercent = poolTvl / 100;
        const slippagePercent = (sellAmount / onePercent);
        
        return {
            sellAmount,
            onePercent,
            slippagePercent: slippagePercent.toFixed(2),
            slippageLoss: sellAmount * (slippagePercent / 100)
        };
    }

    /**
     * 🎯 ПОЛНАЯ СТРАТЕГИЯ DLMM АРБИТРАЖА
     */
    calculateDLMMArbitrage(flashLoanAmount = 650000) {
        console.log(`\n🎯 DLMM SINGLE-SIDED АРБИТРАЖ`);
        console.log(`💰 Flash Loan: $${flashLoanAmount.toLocaleString()}`);
        console.log('=' .repeat(70));
        
        // Параметры стратегии
        const liquidityAmount = flashLoanAmount * 0.77; // 77% на ликвидность
        const solSellAmount = 1000; // Продаем 1000 SOL для создания дисбаланса
        const reserveAmount = flashLoanAmount - liquidityAmount;
        
        console.log(`📊 РАСПРЕДЕЛЕНИЕ СРЕДСТВ:`);
        console.log(`   💧 Односторонняя ликвидность: $${liquidityAmount.toLocaleString()} (77%)`);
        console.log(`   🔄 Резерв для операций: $${reserveAmount.toLocaleString()} (23%)`);
        console.log(`   📉 SOL для продажи: ${solSellAmount} SOL`);
        
        // ШАГ 1: Сначала рассчитываем куда упадет цена
        const priceImpactPreview = this.calculatePriceImpactFromSell(
            solSellAmount,
            this.POOLS.SMALL.currentPrice,
            this.POOLS.SMALL.tvl
        );

        const expectedNewPrice = parseFloat(priceImpactPreview.newPrice);

        // ШАГ 2: Создаем bin структуру вокруг ожидаемой цены
        const bins = this.calculateBinStructure(
            expectedNewPrice, // Используем ожидаемую цену как центр
            this.POOLS.SMALL.binStep
        );

        // Выбираем bins вокруг ожидаемой цены (от -5 до +5)
        const targetBins = bins.filter(bin => bin.index >= -5 && bin.index <= 5);
        
        console.log(`\n💧 ШАГ 1 - ДОБАВЛЕНИЕ ОДНОСТОРОННЕЙ ЛИКВИДНОСТИ:`);
        console.log(`   🎯 Ожидаемая цена после продажи: $${expectedNewPrice.toFixed(2)}`);
        console.log(`   📊 Целевые bins: ${targetBins.length} bins вокруг ожидаемой цены`);
        console.log(`   💰 USDC на bin: $${(liquidityAmount / targetBins.length).toLocaleString()}`);
        console.log(`   📊 Диапазон цен: $${Math.min(...targetBins.map(b => b.price)).toFixed(2)} - $${Math.max(...targetBins.map(b => b.price)).toFixed(2)}`);
        
        // ШАГ 2: Добавляем ликвидность
        const liquidityDistribution = this.calculateSingleSidedLiquidity(liquidityAmount, targetBins);
        
        // ШАГ 3: Продаем SOL для создания дисбаланса
        const priceImpact = this.calculatePriceImpactFromSell(
            solSellAmount, 
            this.POOLS.SMALL.currentPrice, 
            this.POOLS.SMALL.tvl
        );
        
        console.log(`\n📉 ШАГ 2 - СОЗДАНИЕ ДИСБАЛАНСА:`);
        console.log(`   🔴 Продаем: ${solSellAmount} SOL ($${Math.round(priceImpact.solValueUSD).toLocaleString()})`);
        console.log(`   📊 Влияние на пул: ${priceImpact.impactRatio}%`);
        console.log(`   📉 Падение цены: -${priceImpact.priceDropPercent}%`);
        console.log(`   💲 Новая цена: $${priceImpact.newPrice} (было $${priceImpact.oldPrice})`);
        
        // ШАГ 4: Покупаем SOL из нашей ликвидности
        const arbitrageOpportunity = this.calculateArbitrageFromOurLiquidity(
            liquidityDistribution.distribution, 
            parseFloat(priceImpact.newPrice)
        );
        
        console.log(`\n⚡ ШАГ 3 - АРБИТРАЖ ИЗ НАШЕЙ ЛИКВИДНОСТИ:`);
        console.log(`   🎯 Активные bins: ${arbitrageOpportunity.activeBins}`);
        console.log(`   💰 Доступно USDC: $${Math.round(arbitrageOpportunity.availableUSDC).toLocaleString()}`);
        console.log(`   📊 Средняя цена покупки: $${arbitrageOpportunity.avgBuyPrice.toFixed(2)}`);
        console.log(`   🪙 Можем купить SOL: ${arbitrageOpportunity.solCanBuy.toFixed(2)}`);
        
        // ШАГ 5: Продаем в большом пуле
        const solToSell = arbitrageOpportunity.solCanBuy;
        const sellValueAtCurrentPrice = solToSell * this.POOLS.SMALL.currentPrice;
        const largePoolSlippage = this.calculateLargePoolSlippage(sellValueAtCurrentPrice, this.POOLS.LARGE.tvl);
        
        const grossRevenue = sellValueAtCurrentPrice;
        const netRevenue = grossRevenue - largePoolSlippage.slippageLoss - (grossRevenue * this.METEORA_BASE_FEE);
        
        console.log(`\n🌊 ШАГ 4 - ПРОДАЖА В БОЛЬШОМ ПУЛЕ:`);
        console.log(`   🪙 Продаем: ${solToSell.toFixed(2)} SOL`);
        console.log(`   💰 По текущей цене: $${Math.round(grossRevenue).toLocaleString()}`);
        console.log(`   📉 Slippage: ${largePoolSlippage.slippagePercent}% = -$${Math.round(largePoolSlippage.slippageLoss).toLocaleString()}`);
        console.log(`   💳 Комиссия: -$${Math.round(grossRevenue * this.METEORA_BASE_FEE).toLocaleString()}`);
        console.log(`   ✅ Получаем: $${Math.round(netRevenue).toLocaleString()}`);
        
        // ШАГ 6: Финальный расчет
        const flashLoanFee = flashLoanAmount * this.FLASH_LOAN_FEE;
        const totalCosts = flashLoanAmount + flashLoanFee + this.TRANSACTION_COST;
        const totalRevenue = netRevenue;
        const netProfit = totalRevenue - arbitrageOpportunity.availableUSDC; // Вычитаем потраченную USDC
        const roi = (netProfit / arbitrageOpportunity.availableUSDC * 100);
        
        console.log(`\n💰 ШАГ 5 - ФИНАЛЬНЫЙ РАСЧЕТ:`);
        console.log(`   💸 Потрачено USDC: $${Math.round(arbitrageOpportunity.availableUSDC).toLocaleString()}`);
        console.log(`   💵 Получено от продажи: $${Math.round(totalRevenue).toLocaleString()}`);
        console.log(`   🏦 Flash Loan комиссия: $${Math.round(flashLoanFee).toLocaleString()}`);
        console.log(`   💳 Транзакционные расходы: $${this.TRANSACTION_COST}`);
        console.log(`   🎯 ЧИСТАЯ ПРИБЫЛЬ: $${Math.round(netProfit).toLocaleString()}`);
        console.log(`   📈 ROI: ${roi.toFixed(2)}%`);
        
        return {
            flashLoanAmount,
            liquidityAmount,
            steps: {
                liquidityProvision: {
                    usdcAmount: liquidityAmount,
                    binsUsed: targetBins.length,
                    priceRange: [targetBins[targetBins.length-1].price, targetBins[0].price]
                },
                priceManipulation: {
                    solSold: solSellAmount,
                    priceDropPercent: parseFloat(priceImpact.priceDropPercent),
                    newPrice: parseFloat(priceImpact.newPrice)
                },
                arbitrage: {
                    usdcUsed: arbitrageOpportunity.availableUSDC,
                    solBought: arbitrageOpportunity.solCanBuy,
                    avgBuyPrice: arbitrageOpportunity.avgBuyPrice
                },
                largePools: {
                    solSold: solToSell,
                    grossRevenue: Math.round(grossRevenue),
                    slippage: parseFloat(largePoolSlippage.slippagePercent),
                    netRevenue: Math.round(netRevenue)
                }
            },
            result: {
                totalSpent: Math.round(arbitrageOpportunity.availableUSDC),
                totalReceived: Math.round(totalRevenue),
                netProfit: Math.round(netProfit),
                roi: parseFloat(roi.toFixed(2)),
                profitable: netProfit > 0
            }
        };
    }

    /**
     * 🏆 ТЕСТИРОВАНИЕ РАЗНЫХ ПАРАМЕТРОВ
     */
    testDifferentParameters() {
        console.log(`\n🏆 ТЕСТИРОВАНИЕ РАЗНЫХ ПАРАМЕТРОВ`);
        console.log('=' .repeat(70));
        
        const flashLoanAmounts = [500000, 650000, 800000, 1000000];
        const results = [];
        
        flashLoanAmounts.forEach(amount => {
            console.log(`\n📊 ТЕСТ FLASH LOAN $${amount.toLocaleString()}:`);
            const result = this.calculateDLMMArbitrage(amount);
            results.push(result);
            
            const status = result.result.profitable ? '✅' : '❌';
            const emoji = result.result.profitable ? '💚' : '🔴';
            console.log(`${status} Flash Loan $${amount.toLocaleString()}: ${emoji} $${result.result.netProfit.toLocaleString()} (${result.result.roi}%)`);
        });
        
        return results;
    }
}

/**
 * 🎯 ГЛАВНАЯ ФУНКЦИЯ
 */
async function main() {
    console.log('🎯 ЗАПУСК DLMM SINGLE-SIDED АРБИТРАЖА...\n');

    const calculator = new MeteoraDLMMSingleSidedCalculator();

    try {
        console.log('🔧 Начинаем расчет базовой стратегии...');
        // Тестируем базовую стратегию
        const baseResult = calculator.calculateDLMMArbitrage();
        console.log('✅ Базовая стратегия рассчитана');
        
        // Тестируем разные параметры (отключено для отладки)
        // const testResults = calculator.testDifferentParameters();
        
        console.log('\n🎉 АНАЛИЗ ЗАВЕРШЕН!');
        console.log(`🏆 БАЗОВАЯ СТРАТЕГИЯ:`);
        console.log(`   💰 Flash Loan: $${baseResult.flashLoanAmount.toLocaleString()}`);
        console.log(`   🎯 Чистая прибыль: $${baseResult.result.netProfit.toLocaleString()}`);
        console.log(`   📈 ROI: ${baseResult.result.roi}%`);
        console.log(`🚀 DLMM СТРАТЕГИЯ ГОТОВА!`);
        
        return baseResult;
        
    } catch (error) {
        console.error('❌ Ошибка расчета:', error);
        return null;
    }
}

// Запуск если файл выполняется напрямую
if (require.main === module) {
    console.log('🚀 Запуск файла...');
    main().then(result => {
        console.log('✅ Выполнение завершено');
    }).catch(error => {
        console.error('❌ Ошибка:', error);
    });
}

module.exports = { MeteoraDLMMSingleSidedCalculator };
