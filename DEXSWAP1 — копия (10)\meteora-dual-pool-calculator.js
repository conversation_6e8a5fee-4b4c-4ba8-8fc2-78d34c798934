/**
 * 🌊 КАЛЬКУЛЯТОР ДВОЙНОГО METEORA АРБИТРАЖА
 * 
 * Алгоритм:
 * 1. Flash Loan USDC
 * 2. Покупаем SOL в маленьком пуле (поднимаем цену)
 * 3. Продаем SOL в большом пуле (по повышенной цене)
 * 4. Возвращаем Flash Loan + прибыль
 */

class MeteoraDualPoolCalculator {
    constructor() {
        // Реальные Meteora пулы (обновлено с реальными данными)
        this.POOLS = {
            SMALL: {
                liquidity: 3009838, // $3.01M реальный TVL
                sol_amount: 7489.62,
                usdc_amount: 1696475.00,
                sol_price: 167.44, // $167.44 USDC/SOL
                name: 'Meteora Real Pool'
            },
            LARGE: {
                liquidity: 7000000, // $7M
                name: 'Meteora Large Pool'
            }
        };
        
        this.FLASH_LOAN_FEE = 0.0009; // 0.09%
        this.METEORA_FEE = 0.0004; // 0.04% Meteora комиссия
        this.TRANSACTION_COST = 0.01; // $0.01 за транзакцию
        
        console.log('🌊 MeteoraDualPoolCalculator инициализирован');
        console.log(`   Пул 1: $${this.POOLS.SMALL.liquidity.toLocaleString()}`);
        console.log(`   Пул 2: $${this.POOLS.LARGE.liquidity.toLocaleString()}`);
    }

    /**
     * 📈 РАСЧЕТ ПОВЫШЕНИЯ ЦЕНЫ ОТ ПОКУПКИ
     */
    calculatePriceIncrease(buyAmount, poolLiquidity) {
        const ratio = buyAmount / poolLiquidity;
        // Твоя формула: при $500K в $3M пуле = 16.67% → +15-16% цены
        const priceIncrease = ratio * 100 * 0.9; // Коэффициент 0.9 для реализма
        
        return {
            ratio: (ratio * 100).toFixed(2),
            priceIncrease: priceIncrease.toFixed(2),
            newPoolLiquidity: poolLiquidity + buyAmount
        };
    }

    /**
     * 💸 РАСЧЕТ SLIPPAGE ПРИ ПРОДАЖЕ
     */
    calculateSlippage(sellAmount, poolLiquidity) {
        // Твоя формула: 1% от пула = slippage базис
        const onePercent = poolLiquidity / 100;
        const slippagePercent = (sellAmount / onePercent);
        
        return {
            onePercent,
            slippagePercent: slippagePercent.toFixed(2),
            slippageLoss: sellAmount * (slippagePercent / 100)
        };
    }

    /**
     * 🎯 ПОЛНЫЙ РАСЧЕТ АРБИТРАЖА (ИСПРАВЛЕННАЯ ЛОГИКА)
     */
    calculateArbitrage(flashLoanAmount) {
        console.log(`\n🎯 РАСЧЕТ АРБИТРАЖА ДЛЯ FLASH LOAN $${flashLoanAmount.toLocaleString()}`);
        console.log('=' .repeat(70));

        // 1. Покупка ЧАСТИ Flash Loan в маленьком пуле (50% как в твоем примере)
        const buyAmount = flashLoanAmount * 0.5; // Покупаем только 50% от займа!
        const buyResult = this.calculatePriceIncrease(buyAmount, this.POOLS.SMALL.liquidity);
        console.log(`📈 ШАГ 1 - ПОКУПКА В МАЛЕНЬКОМ ПУЛЕ:`);
        console.log(`   Покупаем: $${buyAmount.toLocaleString()} (50% от Flash Loan)`);
        console.log(`   Соотношение: ${buyResult.ratio}% от пула`);
        console.log(`   Повышение цены: +${buyResult.priceIncrease}%`);
        console.log(`   Новая ликвидность: $${buyResult.newPoolLiquidity.toLocaleString()}`);

        // 2. Продаем ВСЕ купленное SOL с учетом повышенной цены
        const sellAmountSmall = buyAmount; // Продаем все что купили
        const slippageSmall = this.calculateSlippage(sellAmountSmall, buyResult.newPoolLiquidity);
        
        // Получаем USDC с учетом повышенной цены и slippage
        const priceBonus = sellAmountSmall * (parseFloat(buyResult.priceIncrease) / 100);
        const grossRevenue = sellAmountSmall + priceBonus;
        const netRevenueSmall = grossRevenue - slippageSmall.slippageLoss - (sellAmountSmall * this.METEORA_FEE);
        
        console.log(`\n💸 ШАГ 2 - ПРОДАЖА В МАЛЕНЬКОМ ПУЛЕ:`);
        console.log(`   Продаем: $${sellAmountSmall.toLocaleString()}`);
        console.log(`   Slippage: ${slippageSmall.slippagePercent}%`);
        console.log(`   Бонус от цены: $${Math.round(priceBonus).toLocaleString()}`);
        console.log(`   Получаем: $${Math.round(netRevenueSmall).toLocaleString()}`);
        console.log(`   Чистая фора: ${((netRevenueSmall - buyAmount) / buyAmount * 100).toFixed(1)}%`);

        // 3. Продажа в большом пуле (продаем полученную сумму)
        const slippageLarge = this.calculateSlippage(netRevenueSmall, this.POOLS.LARGE.liquidity);
        const slippageLoss = netRevenueSmall * (parseFloat(slippageLarge.slippagePercent) / 100);
        const netRevenueLarge = netRevenueSmall - slippageLoss - (netRevenueSmall * this.METEORA_FEE);
        
        console.log(`\n🌊 ШАГ 3 - ПРОДАЖА В БОЛЬШОМ ПУЛЕ:`);
        console.log(`   Продаем: $${Math.round(totalForLargePool).toLocaleString()}`);
        console.log(`   Slippage: ${slippageLarge.slippagePercent}%`);
        console.log(`   Заработок: ${earnPercent}% = $${Math.round(earnings).toLocaleString()}`);
        console.log(`   Получаем: $${Math.round(netRevenueLarge).toLocaleString()}`);
        
        // 4. Возврат Flash Loan и расчет прибыли
        const flashLoanFee = flashLoanAmount * this.FLASH_LOAN_FEE;
        const totalToReturn = flashLoanAmount + flashLoanFee;
        const netProfit = netRevenueLarge - totalToReturn - this.TRANSACTION_COST;
        const roi = (netProfit / flashLoanAmount * 100);
        
        console.log(`\n💰 ШАГ 4 - ВОЗВРАТ И ПРИБЫЛЬ:`);
        console.log(`   Flash Loan возврат: $${totalToReturn.toLocaleString()}`);
        console.log(`   Транзакционные расходы: $${this.TRANSACTION_COST}`);
        console.log(`   ЧИСТАЯ ПРИБЫЛЬ: $${Math.round(netProfit).toLocaleString()}`);
        console.log(`   ROI: ${roi.toFixed(2)}%`);
        
        return {
            flashLoanAmount,
            steps: {
                buy: {
                    amount: flashLoanAmount,
                    priceIncrease: parseFloat(buyResult.priceIncrease),
                    newLiquidity: buyResult.newPoolLiquidity
                },
                sellSmall: {
                    amount: sellAmountSmall,
                    slippage: parseFloat(slippageSmall.slippagePercent),
                    revenue: Math.round(netRevenueSmall)
                },
                sellLarge: {
                    amount: Math.round(netRevenueSmall),
                    slippage: parseFloat(slippageLarge.slippagePercent),
                    earnings: Math.round(earnings),
                    revenue: Math.round(netRevenueLarge)
                }
            },
            result: {
                totalRevenue: Math.round(netRevenueLarge),
                flashLoanCost: Math.round(totalToReturn),
                netProfit: Math.round(netProfit),
                roi: parseFloat(roi.toFixed(2)),
                profitable: netProfit > 0
            }
        };
    }

    /**
     * 🏆 ПОИСК ОПТИМАЛЬНОЙ СУММЫ
     */
    findOptimalAmount() {
        console.log('\n🏆 ПОИСК ОПТИМАЛЬНОЙ СУММЫ FLASH LOAN');
        console.log('=' .repeat(70));
        
        // Тестируем разные суммы от $100K до $1M
        const amounts = [
            100000, 150000, 200000, 250000, 300000, 350000, 400000, 450000,
            500000, 550000, 600000, 650000, 700000, 750000, 800000, 850000,
            900000, 950000, 1000000
        ];
        
        let bestResult = { result: { netProfit: -Infinity } };
        const results = [];
        
        console.log('📊 АНАЛИЗ РАЗЛИЧНЫХ СУММ:\n');
        
        amounts.forEach(amount => {
            const result = this.calculateArbitrage(amount);
            results.push(result);
            
            if (result.result.netProfit > bestResult.result.netProfit) {
                bestResult = result;
            }
            
            const status = result.result.profitable ? '✅' : '❌';
            const emoji = result.result.profitable ? '💚' : '🔴';
            
            console.log(`${status} $${amount.toLocaleString()}: ${emoji} $${result.result.netProfit.toLocaleString()} (${result.result.roi}%)`);
        });
        
        return { bestResult, allResults: results };
    }

    /**
     * 📊 ДЕТАЛЬНЫЙ АНАЛИЗ ЛУЧШЕГО РЕЗУЛЬТАТА
     */
    analyzeBestResult(bestResult) {
        console.log('\n📊 ДЕТАЛЬНЫЙ АНАЛИЗ ОПТИМАЛЬНОЙ СТРАТЕГИИ');
        console.log('=' .repeat(70));
        
        const result = bestResult;
        
        console.log(`🎯 ОПТИМАЛЬНЫЕ ПАРАМЕТРЫ:`);
        console.log(`   💰 Flash Loan: $${result.flashLoanAmount.toLocaleString()}`);
        console.log(`   📈 Повышение цены: +${result.steps.buy.priceIncrease}%`);
        console.log(`   💸 Slippage маленький пул: ${result.steps.sellSmall.slippage}%`);
        console.log(`   🌊 Slippage большой пул: ${result.steps.sellLarge.slippage}%`);
        
        console.log(`\n💰 ФИНАНСОВЫЕ РЕЗУЛЬТАТЫ:`);
        console.log(`   🏆 Чистая прибыль: $${result.result.netProfit.toLocaleString()}`);
        console.log(`   📈 ROI: ${result.result.roi}%`);
        console.log(`   ✅ Прибыльность: ${result.result.profitable ? 'ДА' : 'НЕТ'}`);
        
        // Потенциал масштабирования
        const dailyPotential = result.result.netProfit * 20; // 20 операций в день
        const monthlyPotential = dailyPotential * 30;
        
        console.log(`\n🚀 ПОТЕНЦИАЛ МАСШТАБИРОВАНИЯ:`);
        console.log(`   📅 Дневной потенциал (20 операций): $${dailyPotential.toLocaleString()}`);
        console.log(`   📆 Месячный потенциал: $${monthlyPotential.toLocaleString()}`);
        console.log(`   🎯 Годовой потенциал: $${(monthlyPotential * 12).toLocaleString()}`);
        
        return result;
    }

    /**
     * 📈 ГРАФИК ЗАВИСИМОСТИ ПРИБЫЛИ ОТ СУММЫ
     */
    generateProfitChart(results) {
        console.log('\n📈 ЗАВИСИМОСТЬ ПРИБЫЛИ ОТ СУММЫ FLASH LOAN');
        console.log('=' .repeat(70));
        
        console.log('💰 СУММА → ПРИБЫЛЬ → ROI');
        results.forEach(result => {
            const amount = result.flashLoanAmount;
            const profit = result.result.netProfit;
            const roi = result.result.roi;
            
            const bar = profit > 0 ? '█'.repeat(Math.max(1, Math.round(profit / 1000))) : '';
            const status = profit > 0 ? '✅' : '❌';
            
            console.log(`${status} $${amount.toLocaleString()} → $${profit.toLocaleString()} → ${roi}% ${bar}`);
        });
    }
}

/**
 * 🎯 ГЛАВНАЯ ФУНКЦИЯ
 */
async function main() {
    console.log('🌊 ЗАПУСК КАЛЬКУЛЯТОРА METEORA DUAL POOL АРБИТРАЖА...\n');
    
    const calculator = new MeteoraDualPoolCalculator();
    
    try {
        // Поиск оптимальной суммы
        const { bestResult, allResults } = calculator.findOptimalAmount();
        
        // Детальный анализ
        const analysis = calculator.analyzeBestResult(bestResult);
        
        // График зависимости
        calculator.generateProfitChart(allResults);
        
        console.log('\n🎉 АНАЛИЗ ЗАВЕРШЕН!');
        console.log(`🏆 ОПТИМАЛЬНАЯ СУММА: $${bestResult.flashLoanAmount.toLocaleString()}`);
        console.log(`💰 МАКСИМАЛЬНАЯ ПРИБЫЛЬ: $${bestResult.result.netProfit.toLocaleString()}`);
        console.log(`📈 ROI: ${bestResult.result.roi}%`);
        console.log(`🚀 СТРАТЕГИЯ ГОТОВА К РЕАЛИЗАЦИИ!`);
        
        return analysis;
        
    } catch (error) {
        console.error('❌ Ошибка расчета:', error);
        return null;
    }
}

// Запуск если файл выполняется напрямую
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { MeteoraDualPoolCalculator };
