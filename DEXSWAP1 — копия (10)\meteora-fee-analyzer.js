/**
 * 🔍 METEORA FEE ANALYZER V2
 * Анализирует и кэширует комиссии всех DLMM пулов для точного расчета арбитража
 * 🔥 НОВЫЕ ВОЗМОЖНОСТИ:
 * - Локальное кэширование комиссий
 * - Периодическое обновление
 * - Интеграция с основным ботом
 * - Точный расчет прибыльности по каждому пулу
 */

const { Connection, PublicKey } = require('@solana/web3.js');
const DLMM = require('@meteora-ag/dlmm').default;
const fs = require('fs');
const path = require('path');

class MeteoraFeeAnalyzer {
    constructor(connection = null) {
        this.connection = connection || new Connection('https://api.mainnet-beta.solana.com');
        this.poolsData = [];

        // 🔥 КЭШИРОВАНИЕ КОМИССИЙ
        this.cacheFile = path.join(__dirname, 'meteora-fees-cache.json');
        this.feesCache = new Map();
        this.lastUpdateTime = 0;
        this.updateInterval = 60 * 60 * 1000; // 1 час

        // 🎯 НАШИ ОСНОВНЫЕ ПУЛЫ
        this.targetPools = [
            '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6', // Pool 1
            'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y', // Pool 2
            'HTvjzsfX3yU6BUodCjZ5vZkUrAxMDTrBs3CJaq43ashR'  // Pool 3
        ];

        console.log('🔍 Meteora Fee Analyzer V2 инициализирован');
        console.log(`📁 Кэш файл: ${this.cacheFile}`);
        console.log(`🎯 Отслеживаем ${this.targetPools.length} основных пулов`);
    }

    /**
     * 💾 ЗАГРУЗКА КЭША КОМИССИЙ
     */
    loadFeesCache() {
        try {
            if (fs.existsSync(this.cacheFile)) {
                const cacheData = JSON.parse(fs.readFileSync(this.cacheFile, 'utf8'));

                // Конвертируем обратно в Map
                this.feesCache = new Map(Object.entries(cacheData.fees || {}));
                this.lastUpdateTime = cacheData.lastUpdateTime || 0;

                console.log(`✅ Загружен кэш комиссий: ${this.feesCache.size} пулов`);
                console.log(`📅 Последнее обновление: ${new Date(this.lastUpdateTime).toLocaleString()}`);

                return true;
            }
        } catch (error) {
            console.error('❌ Ошибка загрузки кэша:', error.message);
        }

        console.log('📝 Кэш комиссий будет создан заново');
        return false;
    }

    /**
     * 💾 СОХРАНЕНИЕ КЭША КОМИССИЙ
     */
    saveFeesCache() {
        try {
            const cacheData = {
                fees: Object.fromEntries(this.feesCache),
                lastUpdateTime: Date.now(),
                version: '2.0'
            };

            fs.writeFileSync(this.cacheFile, JSON.stringify(cacheData, null, 2));
            console.log(`✅ Кэш комиссий сохранен: ${this.feesCache.size} пулов`);

            return true;
        } catch (error) {
            console.error('❌ Ошибка сохранения кэша:', error.message);
            return false;
        }
    }

    /**
     * 🔄 ПРОВЕРКА НЕОБХОДИМОСТИ ОБНОВЛЕНИЯ КЭША
     */
    needsUpdate() {
        const timeSinceUpdate = Date.now() - this.lastUpdateTime;
        return timeSinceUpdate > this.updateInterval;
    }

    /**
     * 🌪️ ПОЛУЧЕНИЕ ВСЕХ ПУЛОВ METEORA
     */
    async getAllPools() {
        try {
            console.log('🔍 Получение всех Meteora DLMM пулов...');

            const response = await fetch('https://dlmm-api.meteora.ag/pair/all');
            const data = await response.json();

            if (!data || !Array.isArray(data)) {
                throw new Error('Meteora API вернул неверные данные');
            }

            console.log(`✅ Найдено ${data.length} пулов`);
            return data;
        } catch (error) {
            console.error('❌ Ошибка получения пулов:', error.message);
            return [];
        }
    }

    /**
     * 🔍 ПОЛУЧЕНИЕ ДЕТАЛЬНОЙ ИНФОРМАЦИИ О КОМИССИЯХ ПУЛА (С КЭШИРОВАНИЕМ)
     */
    async getPoolFeeDetails(poolAddress, forceUpdate = false) {
        try {
            // Проверяем кэш если не принудительное обновление
            if (!forceUpdate && this.feesCache.has(poolAddress)) {
                const cached = this.feesCache.get(poolAddress);
                console.log(`📋 Используем кэшированные комиссии для ${poolAddress.slice(0, 8)}...`);
                return cached;
            }

            console.log(`🔍 Получаем актуальные комиссии для ${poolAddress.slice(0, 8)}...`);

            const dlmmPool = await DLMM.create(this.connection, new PublicKey(poolAddress));

            // Получаем информацию о комиссиях
            const feeInfo = dlmmPool.getFeeInfo();
            const dynamicFee = dlmmPool.getDynamicFee();

            const feeDetails = {
                address: poolAddress,
                baseFee: feeInfo.baseFee / 10000, // Конвертируем в проценты
                protocolFee: feeInfo.protocolFee / 10000,
                maxFee: feeInfo.maxFee / 10000,
                dynamicFee: parseFloat(dynamicFee.toString()) / 10000,
                totalSwapFee: (feeInfo.baseFee + parseFloat(dynamicFee.toString())) / 10000,
                lastUpdated: Date.now(),
                // 🔥 ДОПОЛНИТЕЛЬНЫЕ ДАННЫЕ ДЛЯ АРБИТРАЖА
                arbitrageCost: ((feeInfo.baseFee + parseFloat(dynamicFee.toString())) / 10000) * 2, // 2 свопа
                isLowFee: ((feeInfo.baseFee + parseFloat(dynamicFee.toString())) / 10000) < 0.0025, // < 0.25%
                isHighFee: ((feeInfo.baseFee + parseFloat(dynamicFee.toString())) / 10000) > 0.01 // > 1%
            };

            // Сохраняем в кэш
            this.feesCache.set(poolAddress, feeDetails);

            console.log(`✅ Комиссии получены: ${(feeDetails.totalSwapFee * 100).toFixed(4)}% (base: ${(feeDetails.baseFee * 100).toFixed(4)}%, dynamic: ${(feeDetails.dynamicFee * 100).toFixed(4)}%)`);

            return feeDetails;
        } catch (error) {
            console.error(`❌ Ошибка получения комиссий для ${poolAddress}:`, error.message);
            return null;
        }
    }

    /**
     * 🎯 ПОЛУЧЕНИЕ КОМИССИЙ КОНКРЕТНОГО ПУЛА (БЫСТРЫЙ ДОСТУП)
     */
    getPoolFee(poolAddress) {
        const cached = this.feesCache.get(poolAddress);
        if (cached) {
            return {
                totalSwapFee: cached.totalSwapFee,
                arbitrageCost: cached.arbitrageCost,
                isLowFee: cached.isLowFee,
                isHighFee: cached.isHighFee,
                lastUpdated: cached.lastUpdated
            };
        }

        // Возвращаем дефолтные значения если нет в кэше
        console.log(`⚠️ Комиссии для ${poolAddress.slice(0, 8)}... не найдены в кэше, используем дефолт 0.25%`);
        return {
            totalSwapFee: 0.0025, // 0.25% дефолт
            arbitrageCost: 0.005,  // 0.5% для арбитража
            isLowFee: true,
            isHighFee: false,
            lastUpdated: 0
        };
    }

    /**
     * 🔥 ОБНОВЛЕНИЕ КОМИССИЙ ОСНОВНЫХ ПУЛОВ
     */
    async updateTargetPoolsFees(forceUpdate = false) {
        try {
            console.log('🔥 Обновление комиссий основных пулов...');

            const results = [];

            for (const poolAddress of this.targetPools) {
                try {
                    const feeDetails = await this.getPoolFeeDetails(poolAddress, forceUpdate);

                    if (feeDetails) {
                        results.push(feeDetails);
                        console.log(`✅ ${poolAddress.slice(0, 8)}...: ${(feeDetails.totalSwapFee * 100).toFixed(4)}% комиссия`);
                    }

                    // Небольшая задержка между запросами
                    await new Promise(resolve => setTimeout(resolve, 200));
                } catch (error) {
                    console.error(`❌ Ошибка обновления ${poolAddress}:`, error.message);
                }
            }

            // Сохраняем кэш после обновления
            this.saveFeesCache();

            console.log(`✅ Обновлено комиссий: ${results.length}/${this.targetPools.length} пулов`);
            return results;

        } catch (error) {
            console.error('❌ Ошибка обновления комиссий основных пулов:', error.message);
            return [];
        }
    }

    /**
     * 🎯 РАСЧЕТ МИНИМАЛЬНОГО СПРЕДА ДЛЯ ПАРЫ ПУЛОВ
     */
    calculateMinSpreadForPair(buyPoolAddress, sellPoolAddress, networkFee = 0.025) {
        const buyPoolFee = this.getPoolFee(buyPoolAddress);
        const sellPoolFee = this.getPoolFee(sellPoolAddress);

        // Общие комиссии: комиссия покупки + комиссия продажи + сетевая комиссия
        const totalFeePercent = buyPoolFee.totalSwapFee + sellPoolFee.totalSwapFee;

        // Для позиции $1000 сетевая комиссия составляет 0.0025%
        const networkFeePercent = networkFee / 1000; // $0.025 от $1000 = 0.0025%

        const minSpread = totalFeePercent + networkFeePercent + 0.001; // +0.1% запас прибыли

        return {
            buyPool: {
                address: buyPoolAddress,
                fee: buyPoolFee.totalSwapFee,
                isLowFee: buyPoolFee.isLowFee
            },
            sellPool: {
                address: sellPoolAddress,
                fee: sellPoolFee.totalSwapFee,
                isLowFee: sellPoolFee.isLowFee
            },
            totalFeePercent: totalFeePercent,
            networkFeePercent: networkFeePercent,
            minSpreadPercent: minSpread,
            isProfitable: (spread) => spread > minSpread,
            recommendation: minSpread < 0.005 ? 'ОТЛИЧНАЯ ПАРА' : minSpread < 0.01 ? 'ХОРОШАЯ ПАРА' : 'ДОРОГАЯ ПАРА'
        };
    }

    /**
     * 📊 АНАЛИЗ ВСЕХ ПУЛОВ SOL-USDC
     */
    async analyzeSolUsdcPools() {
        try {
            console.log('🔍 Анализ всех SOL-USDC пулов...');
            
            const allPools = await this.getAllPools();
            
            // Фильтруем только SOL-USDC пулы
            const solUsdcPools = allPools.filter(pool => {
                const pair = `${pool.mint_x_symbol}/${pool.mint_y_symbol}`;
                return pair === 'SOL/USDC' || pair === 'USDC/SOL';
            });

            console.log(`✅ Найдено ${solUsdcPools.length} SOL-USDC пулов`);

            const poolsWithFees = [];

            for (const pool of solUsdcPools) {
                console.log(`🔍 Анализ пула ${pool.address}...`);
                
                const feeDetails = await this.getPoolFeeDetails(pool.address);
                
                if (feeDetails) {
                    poolsWithFees.push({
                        address: pool.address,
                        name: pool.name,
                        tvl: pool.liquidity || 0,
                        volume24h: pool.volume_24h || 0,
                        fees: feeDetails,
                        arbitrageCost: feeDetails.totalSwapFee * 2, // 2 свапа для арбитража
                        binStep: pool.bin_step
                    });
                }

                // Небольшая задержка чтобы не перегрузить RPC
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            return poolsWithFees;
        } catch (error) {
            console.error('❌ Ошибка анализа пулов:', error.message);
            return [];
        }
    }

    /**
     * 🎯 ПОИСК ОПТИМАЛЬНЫХ ПУЛОВ ДЛЯ АРБИТРАЖА
     */
    findOptimalPoolsForArbitrage(pools, targetSpread = 0.0008) { // 0.08%
        console.log(`🎯 Поиск пулов для спреда ${(targetSpread * 100).toFixed(2)}%...`);
        
        // Сортируем по стоимости арбитража
        const sortedPools = pools.sort((a, b) => a.arbitrageCost - b.arbitrageCost);
        
        const results = {
            profitable: [],
            breakeven: [],
            unprofitable: []
        };

        sortedPools.forEach(pool => {
            const profit = targetSpread - pool.arbitrageCost;
            const profitPercent = (profit * 100).toFixed(4);
            
            if (profit > 0) {
                results.profitable.push({ ...pool, profit, profitPercent });
            } else if (Math.abs(profit) < 0.0001) { // В пределах 0.01%
                results.breakeven.push({ ...pool, profit, profitPercent });
            } else {
                results.unprofitable.push({ ...pool, profit, profitPercent });
            }
        });

        return results;
    }

    /**
     * 📋 КРАСИВЫЙ ВЫВОД РЕЗУЛЬТАТОВ
     */
    printResults(results, targetSpread) {
        console.log('\n' + '='.repeat(80));
        console.log(`🎯 АНАЛИЗ ПУЛОВ ДЛЯ СПРЕДА ${(targetSpread * 100).toFixed(2)}%`);
        console.log('='.repeat(80));

        // Прибыльные пулы
        if (results.profitable.length > 0) {
            console.log('\n✅ ПРИБЫЛЬНЫЕ ПУЛЫ:');
            results.profitable.forEach((pool, index) => {
                console.log(`\n${index + 1}. ${pool.name || pool.address.slice(0, 8)}...`);
                console.log(`   TVL: $${pool.tvl.toLocaleString()}`);
                console.log(`   Base Fee: ${(pool.fees.baseFee * 100).toFixed(4)}%`);
                console.log(`   Dynamic Fee: ${(pool.fees.dynamicFee * 100).toFixed(4)}%`);
                console.log(`   Total Swap Fee: ${(pool.fees.totalSwapFee * 100).toFixed(4)}%`);
                console.log(`   Arbitrage Cost: ${(pool.arbitrageCost * 100).toFixed(4)}%`);
                console.log(`   💰 PROFIT: ${pool.profitPercent}%`);
            });
        }

        // Безубыточные пулы
        if (results.breakeven.length > 0) {
            console.log('\n⚖️ БЕЗУБЫТОЧНЫЕ ПУЛЫ:');
            results.breakeven.forEach((pool, index) => {
                console.log(`\n${index + 1}. ${pool.name || pool.address.slice(0, 8)}...`);
                console.log(`   Arbitrage Cost: ${(pool.arbitrageCost * 100).toFixed(4)}%`);
                console.log(`   ⚖️ BREAKEVEN: ${pool.profitPercent}%`);
            });
        }

        // Убыточные пулы (показываем только топ-5)
        if (results.unprofitable.length > 0) {
            console.log('\n❌ УБЫТОЧНЫЕ ПУЛЫ (топ-5):');
            results.unprofitable.slice(0, 5).forEach((pool, index) => {
                console.log(`\n${index + 1}. ${pool.name || pool.address.slice(0, 8)}...`);
                console.log(`   Arbitrage Cost: ${(pool.arbitrageCost * 100).toFixed(4)}%`);
                console.log(`   ❌ LOSS: ${pool.profitPercent}%`);
            });
        }

        console.log('\n' + '='.repeat(80));
        console.log(`📊 ИТОГО: ${results.profitable.length} прибыльных, ${results.breakeven.length} безубыточных, ${results.unprofitable.length} убыточных`);
        console.log('='.repeat(80));
    }

    /**
     * 🚀 ИНИЦИАЛИЗАЦИЯ АНАЛИЗАТОРА КОМИССИЙ
     */
    async initialize() {
        try {
            console.log('🚀 Инициализация Meteora Fee Analyzer V2...');

            // Загружаем кэш
            this.loadFeesCache();

            // Проверяем нужно ли обновление
            if (this.needsUpdate() || this.feesCache.size === 0) {
                console.log('🔄 Требуется обновление комиссий...');
                await this.updateTargetPoolsFees(true);
            } else {
                console.log('✅ Кэш комиссий актуален');
            }

            // Показываем текущие комиссии
            this.printCurrentFees();

            console.log('✅ Meteora Fee Analyzer V2 готов к работе!');
            return true;

        } catch (error) {
            console.error('❌ Ошибка инициализации:', error.message);
            return false;
        }
    }

    /**
     * 📊 ПОКАЗАТЬ ТЕКУЩИЕ КОМИССИИ ОСНОВНЫХ ПУЛОВ
     */
    printCurrentFees() {
        console.log('\n📊 ТЕКУЩИЕ КОМИССИИ ОСНОВНЫХ ПУЛОВ:');
        console.log('═'.repeat(60));

        this.targetPools.forEach((poolAddress, index) => {
            const fee = this.getPoolFee(poolAddress);
            const status = fee.isLowFee ? '🟢 НИЗКАЯ' : fee.isHighFee ? '🔴 ВЫСОКАЯ' : '🟡 СРЕДНЯЯ';

            console.log(`${index + 1}. ${poolAddress.slice(0, 8)}...`);
            console.log(`   Комиссия: ${(fee.totalSwapFee * 100).toFixed(4)}% ${status}`);
            console.log(`   Арбитраж: ${(fee.arbitrageCost * 100).toFixed(4)}%`);
            console.log(`   Обновлено: ${fee.lastUpdated ? new Date(fee.lastUpdated).toLocaleString() : 'Никогда'}`);
            console.log('');
        });

        console.log('═'.repeat(60));
    }

    /**
     * 🎯 ПОЛУЧИТЬ ОПТИМАЛЬНЫЕ ПАРЫ ДЛЯ АРБИТРАЖА
     */
    getOptimalPairs() {
        const pairs = [];

        // Генерируем все возможные пары
        for (let i = 0; i < this.targetPools.length; i++) {
            for (let j = 0; j < this.targetPools.length; j++) {
                if (i !== j) {
                    const buyPool = this.targetPools[i];
                    const sellPool = this.targetPools[j];

                    const pairAnalysis = this.calculateMinSpreadForPair(buyPool, sellPool);

                    pairs.push({
                        buyPool: buyPool,
                        sellPool: sellPool,
                        minSpread: pairAnalysis.minSpreadPercent,
                        recommendation: pairAnalysis.recommendation,
                        analysis: pairAnalysis
                    });
                }
            }
        }

        // Сортируем по минимальному спреду
        pairs.sort((a, b) => a.minSpread - b.minSpread);

        return pairs;
    }

    /**
     * 🚀 ГЛАВНЫЙ МЕТОД АНАЛИЗА
     */
    async runAnalysis(targetSpread = 0.0008) {
        try {
            console.log('🚀 ЗАПУСК АНАЛИЗА METEORA FEE ANALYZER V2...\n');

            // Инициализируем если еще не сделали
            if (this.feesCache.size === 0) {
                await this.initialize();
            }

            // Показываем оптимальные пары
            const pairs = this.getOptimalPairs();

            console.log('\n🎯 ОПТИМАЛЬНЫЕ ПАРЫ ДЛЯ АРБИТРАЖА:');
            console.log('═'.repeat(80));

            pairs.forEach((pair, index) => {
                console.log(`${index + 1}. КУПИТЬ: ${pair.buyPool.slice(0, 8)}... → ПРОДАТЬ: ${pair.sellPool.slice(0, 8)}...`);
                console.log(`   Минимальный спред: ${(pair.minSpread * 100).toFixed(4)}%`);
                console.log(`   Рекомендация: ${pair.recommendation}`);
                console.log(`   Комиссия покупки: ${(pair.analysis.buyPool.fee * 100).toFixed(4)}%`);
                console.log(`   Комиссия продажи: ${(pair.analysis.sellPool.fee * 100).toFixed(4)}%`);
                console.log('');
            });

            return pairs;
        } catch (error) {
            console.error('❌ Ошибка анализа:', error.message);
        }
    }
}

// Экспорт для использования в других модулях
module.exports = MeteoraFeeAnalyzer;

// Запуск если файл вызван напрямую
if (require.main === module) {
    const analyzer = new MeteoraFeeAnalyzer();

    // 🚀 ПОЛНЫЙ АНАЛИЗ КОМИССИЙ
    analyzer.initialize().then(async () => {
        console.log('\n🎯 ЗАПУСК АНАЛИЗА ОПТИМАЛЬНЫХ ПАР...');

        const pairs = await analyzer.runAnalysis(0.0008);

        if (pairs && pairs.length > 0) {
            console.log(`\n✅ Найдено ${pairs.length} пар для арбитража`);
            console.log(`🏆 ЛУЧШАЯ ПАРА: минимальный спред ${(pairs[0].minSpread * 100).toFixed(4)}%`);
        }

        console.log('\n✅ Анализ завершен!');
        process.exit(0);
    }).catch(error => {
        console.error('❌ Критическая ошибка:', error);
        process.exit(1);
    });
}
