#!/usr/bin/env node

/**
 * 🔥 METEORA DLMM MINIMAL STRUCTURE CREATOR
 * СОЗДАЕТ МИНИМАЛЬНУЮ РАБОЧУЮ СТРУКТУРУ ДАННЫХ ДЛЯ ADDLIQUIDITYBYSTATEGY2
 */

const fs = require('fs');
const { BN } = require('@coral-xyz/anchor');

console.log('🔥 METEORA DLMM MINIMAL STRUCTURE CREATOR');
console.log('📋 Создаем минимальную рабочую структуру данных...\n');

// 🔥 ПОПЫТКА 1: ТОЛЬКО DISCRIMINATOR (МИНИМУМ)
function createMinimalStructure() {
    console.log('🧪 ПОПЫТКА 1: ТОЛЬКО DISCRIMINATOR');
    
    const data = Buffer.alloc(8);
    const discriminator = [3, 221, 149, 218, 111, 141, 118, 213];
    
    discriminator.forEach((byte, index) => {
        data.writeUInt8(byte, index);
    });
    
    console.log(`   Размер: ${data.length} байт`);
    console.log(`   Hex: ${data.toString('hex')}`);
    console.log(`   Array: [${Array.from(data).join(', ')}]`);
    
    return data;
}

// 🔥 ПОПЫТКА 2: DISCRIMINATOR + НУЛИ (ПУСТЫЕ ПАРАМЕТРЫ)
function createZeroParametersStructure() {
    console.log('\n🧪 ПОПЫТКА 2: DISCRIMINATOR + НУЛИ');
    
    const data = Buffer.alloc(64);
    let offset = 0;
    
    // Discriminator
    const discriminator = [3, 221, 149, 218, 111, 141, 118, 213];
    discriminator.forEach(byte => {
        data.writeUInt8(byte, offset);
        offset += 1;
    });
    
    // Все остальное - нули (пустые параметры)
    // Anchor может принять пустые параметры
    
    console.log(`   Размер: ${offset} байт`);
    console.log(`   Hex: ${data.slice(0, offset).toString('hex')}`);
    console.log(`   Array: [${Array.from(data.slice(0, offset)).join(', ')}]`);
    
    return data.slice(0, offset);
}

// 🔥 ПОПЫТКА 3: ANCHOR BORSH SERIALIZATION STYLE
function createBorshStyleStructure(amountX, amountY) {
    console.log('\n🧪 ПОПЫТКА 3: BORSH SERIALIZATION STYLE');
    
    const data = Buffer.alloc(128);
    let offset = 0;
    
    // 1. Discriminator (8 bytes)
    const discriminator = [3, 221, 149, 218, 111, 141, 118, 213];
    discriminator.forEach(byte => {
        data.writeUInt8(byte, offset);
        offset += 1;
    });
    
    // 2. LiquidityParameterByStrategy struct
    // Borsh serialization: struct начинается сразу без дополнительных байтов
    
    // 2.1. amountX (u64, 8 bytes, little-endian)
    const amountXBN = new BN(amountX);
    const amountXBytes = amountXBN.toBuffer('le', 8);
    amountXBytes.copy(data, offset);
    offset += 8;
    
    // 2.2. amountY (u64, 8 bytes, little-endian)
    const amountYBN = new BN(amountY);
    const amountYBytes = amountYBN.toBuffer('le', 8);
    amountYBytes.copy(data, offset);
    offset += 8;
    
    // 2.3. activeId (i32, 4 bytes, little-endian)
    data.writeInt32LE(-4361, offset);
    offset += 4;
    
    // 2.4. maxActiveBinSlippage (u16, 2 bytes, little-endian)
    data.writeUInt16LE(1000, offset);
    offset += 2;
    
    // 2.5. strategyParameters - МИНИМАЛЬНАЯ СТРУКТУРА
    // Возможно это enum с вариантом 0 (простейший)
    data.writeUInt8(0, offset); // Strategy type = 0 (простейший)
    offset += 1;
    
    // 3. RemainingAccountsInfo
    // Возможно это Vec с длиной 0
    data.writeUInt32LE(0, offset); // Vec length = 0
    offset += 4;
    
    console.log(`   Размер: ${offset} байт`);
    console.log(`   Hex: ${data.slice(0, offset).toString('hex')}`);
    console.log(`   Array: [${Array.from(data.slice(0, offset)).join(', ')}]`);
    
    return data.slice(0, offset);
}

// 🔥 ПОПЫТКА 4: КОПИРОВАНИЕ РЕАЛЬНЫХ ДАННЫХ
function createFromRealTransaction() {
    console.log('\n🧪 ПОПЫТКА 4: АНАЛИЗ РЕАЛЬНОЙ ТРАНЗАКЦИИ');
    
    // Из реальной транзакции я знаю что было:
    // amountX: "0"
    // amountY: "***********" 
    // activeId: "-4361"
    
    return createBorshStyleStructure(0, "***********");
}

// 🔥 ТЕСТИРОВАНИЕ ВСЕХ ВАРИАНТОВ
console.log('🔥 ТЕСТИРОВАНИЕ ВСЕХ ВАРИАНТОВ:\n');

const structures = {
    minimal: createMinimalStructure(),
    zeros: createZeroParametersStructure(), 
    borsh: createBorshStyleStructure(1000000 * 1e6, 5500 * 1e9),
    real: createFromRealTransaction()
};

// 🔥 СОЗДАНИЕ JAVASCRIPT КОДА
console.log('\n🔥 JAVASCRIPT КОД ДЛЯ ТЕСТИРОВАНИЯ:\n');

Object.entries(structures).forEach(([name, data]) => {
    console.log(`// ${name.toUpperCase()} STRUCTURE`);
    console.log(`const ${name}Data = Buffer.from([${Array.from(data).join(', ')}]);`);
    console.log('');
});

console.log('// ФУНКЦИЯ ДЛЯ СОЗДАНИЯ BORSH STYLE СТРУКТУРЫ');
console.log('function createMeteoraBorshData(amountX, amountY, activeId = -4361, maxSlippage = 1000) {');
console.log('    const data = Buffer.alloc(128);');
console.log('    let offset = 0;');
console.log('    ');
console.log('    // Discriminator');
console.log('    [3, 221, 149, 218, 111, 141, 118, 213].forEach(byte => {');
console.log('        data.writeUInt8(byte, offset++);');
console.log('    });');
console.log('    ');
console.log('    // LiquidityParameterByStrategy (Borsh style)');
console.log('    const amountXBN = new BN(amountX);');
console.log('    amountXBN.toBuffer("le", 8).copy(data, offset); offset += 8;');
console.log('    ');
console.log('    const amountYBN = new BN(amountY);');
console.log('    amountYBN.toBuffer("le", 8).copy(data, offset); offset += 8;');
console.log('    ');
console.log('    data.writeInt32LE(activeId, offset); offset += 4;');
console.log('    data.writeUInt16LE(maxSlippage, offset); offset += 2;');
console.log('    ');
console.log('    // Strategy (enum = 0)');
console.log('    data.writeUInt8(0, offset); offset += 1;');
console.log('    ');
console.log('    // RemainingAccountsInfo (Vec length = 0)');
console.log('    data.writeUInt32LE(0, offset); offset += 4;');
console.log('    ');
console.log('    return data.slice(0, offset);');
console.log('}');

// Сохранение результатов
const results = {
    timestamp: new Date().toISOString(),
    structures: Object.fromEntries(
        Object.entries(structures).map(([name, data]) => [
            name, 
            {
                hex: data.toString('hex'),
                array: Array.from(data),
                size: data.length
            }
        ])
    )
};

fs.writeFileSync('meteora-minimal-structures.json', JSON.stringify(results, null, 2));

console.log('\n✅ МИНИМАЛЬНЫЕ СТРУКТУРЫ СОЗДАНЫ!');
console.log('📁 Результаты сохранены в meteora-minimal-structures.json');
console.log('🔥 ГОТОВО! ПОПРОБУЙТЕ BORSH STYLE СТРУКТУРУ!');
