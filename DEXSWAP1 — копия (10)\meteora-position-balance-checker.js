/**
 * 🎯 METEORA DLMM POSITION BALANCE CHECKER
 * 

const { Connection, PublicKey, Keypair } = require('@solana/web3.js');
const { BN } = require('@coral-xyz/anchor');
require('dotenv').config();

class MeteoraPositionBalanceChecker {
    constructor(connection, wallet) {
        // 🔥 ИСПОЛЬЗУЕМ QUICKNODE RPC ИЗ .ENV ДЛЯ НАДЕЖНОСТИ!
        const quicknodeUrl = process.env.QUICKNODE_RPC_URL || process.env.QUICKNODE_RPC_URL_BACKUP2;
        if (quicknodeUrl && quicknodeUrl !== 'https://api.mainnet-beta.solana.com') {
            console.log(`🚀 Position Checker: Используем QuickNode RPC: ${quicknodeUrl.slice(0, 50)}...`);
            this.connection = new Connection(quicknodeUrl, 'confirmed');
        } else {
            console.log(`⚠️ Position Checker: Используем переданное соединение`);
            this.connection = connection;
        }
        this.wallet = wallet;
        
        // Импортируем DLMM SDK
        try {
            this.DLMM = require('@meteora-ag/dlmm').default || require('@meteora-ag/dlmm').DLMM;
        } catch (error) {
            console.error('❌ Ошибка импорта DLMM SDK:', error.message);
            this.DLMM = null;
        }
        
        // Кэш для позиций
        this.positionCache = new Map();
    }

    /**
     * 🔍 ПРОВЕРКА БАЛАНСА КОНКРЕТНОЙ ПОЗИЦИИ
     */
    async checkPositionBalance(poolAddress, positionAddress) {
        try {
            console.log(`🔍 Проверяем баланс позиции ${positionAddress.toString().slice(0, 8)}...`);
            
            if (!this.DLMM) {
                throw new Error('DLMM SDK не доступен');
            }

            // Создаем DLMM instance
            const dlmmPool = await this.DLMM.create(this.connection, poolAddress);
            
            // Получаем все позиции пользователя
            const { userPositions } = await dlmmPool.getPositionsByUserAndLbPair(this.wallet.publicKey);
            
            // Ищем конкретную позицию
            const targetPosition = userPositions.find(pos => 
                pos.publicKey.toString() === positionAddress.toString()
            );
            
            if (!targetPosition) {
                console.log(`   ❌ Позиция не найдена среди позиций пользователя`);
                return {
                    exists: false,
                    hasLiquidity: false,
                    isEmpty: true,
                    binData: [],
                    error: 'Position not found'
                };
            }
            
            // Анализируем bin data
            const binData = targetPosition.positionData.positionBinData;
            console.log(`   📊 Найдено bins: ${binData.length}`);
            
            // Проверяем наличие ликвидности
            let totalXAmount = new BN(0);
            let totalYAmount = new BN(0);
            let activeBins = 0;
            
            binData.forEach((bin, index) => {
                const xAmount = new BN(bin.xAmount || 0);
                const yAmount = new BN(bin.yAmount || 0);
                
                totalXAmount = totalXAmount.add(xAmount);
                totalYAmount = totalYAmount.add(yAmount);
                
                if (xAmount.gt(new BN(0)) || yAmount.gt(new BN(0))) {
                    activeBins++;
                    console.log(`   💰 Bin ${bin.binId}: X=${xAmount.toString()}, Y=${yAmount.toString()}`);
                }
            });
            
            const hasLiquidity = totalXAmount.gt(new BN(0)) || totalYAmount.gt(new BN(0));
            const isEmpty = !hasLiquidity;
            
            console.log(`   📈 Общая ликвидность: X=${totalXAmount.toString()}, Y=${totalYAmount.toString()}`);
            console.log(`   🎯 Активных bins: ${activeBins}/${binData.length}`);
            console.log(`   ${hasLiquidity ? '❌ ПОЗИЦИЯ СОДЕРЖИТ ЛИКВИДНОСТЬ' : '✅ ПОЗИЦИЯ ПУСТАЯ'}`);
            
            return {
                exists: true,
                hasLiquidity,
                isEmpty,
                totalXAmount,
                totalYAmount,
                activeBins,
                binData,
                position: targetPosition
            };
            
        } catch (error) {
            console.error(`❌ Ошибка проверки позиции: ${error.message}`);
            return {
                exists: false,
                hasLiquidity: false,
                isEmpty: true,
                binData: [],
                error: error.message
            };
        }
    }

    /**
     * 🧹 ОЧИСТКА ПОЗИЦИИ (УДАЛЕНИЕ ВСЕЙ ЛИКВИДНОСТИ)
     */
    async clearPosition(poolAddress, positionAddress) {
        try {
            console.log(`🧹 Очищаем позицию ${positionAddress.toString().slice(0, 8)}...`);
            
            // Сначала проверяем баланс
            const balanceCheck = await this.checkPositionBalance(poolAddress, positionAddress);
            
            if (!balanceCheck.exists) {
                console.log(`   ⚠️ Позиция не существует, очистка не требуется`);
                return { success: true, cleared: false, reason: 'Position does not exist' };
            }
            
            if (balanceCheck.isEmpty) {
                console.log(`   ✅ Позиция уже пустая, очистка не требуется`);
                return { success: true, cleared: false, reason: 'Position already empty' };
            }
            
            // Создаем DLMM instance
            const dlmmPool = await this.DLMM.create(this.connection, poolAddress);
            
            // Получаем bin IDs для удаления
            const binIdsToRemove = balanceCheck.binData
                .filter(bin => {
                    const xAmount = new BN(bin.xAmount || 0);
                    const yAmount = new BN(bin.yAmount || 0);
                    return xAmount.gt(new BN(0)) || yAmount.gt(new BN(0));
                })
                .map(bin => bin.binId);
            
            if (binIdsToRemove.length === 0) {
                console.log(`   ✅ Нет активных bins для очистки`);
                return { success: true, cleared: false, reason: 'No active bins' };
            }
            
            console.log(`   🎯 Удаляем ликвидность из ${binIdsToRemove.length} bins: ${binIdsToRemove.join(', ')}`);
            
            // Создаем транзакцию удаления ликвидности
            const removeLiquidityTx = await dlmmPool.removeLiquidity({
                position: positionAddress,
                user: this.wallet.publicKey,
                fromBinId: Math.min(...binIdsToRemove),
                toBinId: Math.max(...binIdsToRemove),
                liquiditiesBpsToRemove: new Array(binIdsToRemove.length).fill(new BN(100 * 100)), // 100%
                shouldClaimAndClose: false // НЕ закрываем позицию, только удаляем ликвидность
            });
            
            console.log(`   ✅ Транзакция очистки создана`);
            
            return {
                success: true,
                cleared: true,
                transaction: removeLiquidityTx,
                binsCleared: binIdsToRemove.length,
                binIds: binIdsToRemove
            };
            
        } catch (error) {
            console.error(`❌ Ошибка очистки позиции: ${error.message}`);
            return {
                success: false,
                cleared: false,
                error: error.message
            };
        }
    }

    /**
     * 🚫 СОЗДАНИЕ НОВЫХ ПОЗИЦИЙ ЗАПРЕЩЕНО!
     * Мы работаем только с существующими позициями!
     */
    async createNewPosition() {
        console.error(`🚫 СОЗДАНИЕ НОВЫХ ПОЗИЦИЙ ЗАПРЕЩЕНО!`);
        console.error(`   Мы работаем только с существующими позициями!`);

        return {
            success: false,
            error: 'Creating new positions is forbidden! Use existing positions only.'
        };
    }

    /**
     * 🎯 ОСНОВНАЯ ФУНКЦИЯ: ПОДГОТОВКА СУЩЕСТВУЮЩЕЙ ПОЗИЦИИ ДЛЯ ДОБАВЛЕНИЯ ЛИКВИДНОСТИ
     * 🚫 СОЗДАНИЕ НОВЫХ ПОЗИЦИЙ ЗАПРЕЩЕНО!
     */
    async preparePositionForLiquidity(poolAddress, positionAddress, options = {}) {
        try {
            console.log(`\n🎯 ПОДГОТОВКА СУЩЕСТВУЮЩЕЙ ПОЗИЦИИ ДЛЯ ДОБАВЛЕНИЯ ЛИКВИДНОСТИ...`);
            console.log(`   Pool: ${poolAddress.toString()}`);
            console.log(`   Position: ${positionAddress.toString()}`);

            const {
                clearExisting = true,    // Очистить существующую позицию если не пустая
                allowNonEmpty = false    // Разрешить добавление в непустую позицию
            } = options;

            // Проверяем баланс позиции
            const balanceCheck = await this.checkPositionBalance(poolAddress, positionAddress);

            if (!balanceCheck.exists) {
                console.log(`   ❌ ПОЗИЦИЯ НЕ СУЩЕСТВУЕТ!`);
                console.log(`   🚫 Создание новых позиций запрещено!`);
                return {
                    success: false,
                    error: 'Position does not exist and creating new positions is forbidden'
                };
            }

            if (balanceCheck.isEmpty) {
                console.log(`   ✅ ПОЗИЦИЯ ПУСТАЯ, ГОТОВА К ИСПОЛЬЗОВАНИЮ`);
                return {
                    success: true,
                    useExisting: true,
                    position: null,
                    address: positionAddress,
                    cleared: false,
                    ready: true
                };
            }

            // Позиция содержит ликвидность
            console.log(`   ⚠️ ПОЗИЦИЯ СОДЕРЖИТ ЛИКВИДНОСТЬ!`);
            console.log(`   💰 X Amount: ${balanceCheck.totalXAmount.toString()}`);
            console.log(`   💰 Y Amount: ${balanceCheck.totalYAmount.toString()}`);
            console.log(`   🎯 Активных bins: ${balanceCheck.activeBins}`);

            if (allowNonEmpty) {
                console.log(`   ⚠️ РАЗРЕШЕНО ДОБАВЛЕНИЕ В НЕПУСТУЮ ПОЗИЦИЮ`);
                return {
                    success: true,
                    useExisting: true,
                    position: null,
                    address: positionAddress,
                    cleared: false,
                    ready: true,
                    warning: 'Position contains liquidity but addition is allowed'
                };
            }

            if (clearExisting) {
                console.log(`   🧹 ОЧИЩАЕМ ПОЗИЦИЮ ПЕРЕД ДОБАВЛЕНИЕМ ЛИКВИДНОСТИ...`);
                const clearResult = await this.clearPosition(poolAddress, positionAddress);

                if (clearResult.success && clearResult.cleared) {
                    console.log(`   ✅ ПОЗИЦИЯ ОЧИЩЕНА, ГОТОВА К ИСПОЛЬЗОВАНИЮ`);
                    return {
                        success: true,
                        useExisting: true,
                        position: null,
                        address: positionAddress,
                        cleared: true,
                        ready: true,
                        clearTransaction: clearResult.transaction,
                        binsCleared: clearResult.binsCleared
                    };
                } else {
                    console.log(`   ❌ НЕ УДАЛОСЬ ОЧИСТИТЬ ПОЗИЦИЮ`);
                    return {
                        success: false,
                        error: 'Failed to clear position and creating new positions is forbidden'
                    };
                }
            }

            // По умолчанию - ошибка, так как позиция не пустая и очистка не разрешена
            console.log(`   ❌ ПОЗИЦИЯ НЕ ПУСТАЯ И ОЧИСТКА НЕ РАЗРЕШЕНА`);
            return {
                success: false,
                error: 'Position contains liquidity, clearing not allowed, and creating new positions is forbidden'
            };

        } catch (error) {
            console.error(`❌ Ошибка подготовки позиции: ${error.message}`);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 🎯 ПРОСТАЯ ФУНКЦИЯ ДЛЯ ИНТЕГРАЦИИ В ОСНОВНОЙ КОД
     * Проверяет и при необходимости очищает позицию перед добавлением ликвидности
     */
    async ensurePositionIsEmpty(poolAddress, positionAddress) {
        try {
            console.log(`\n🔍 ПРОВЕРЯЕМ ЧТО ПОЗИЦИЯ ПУСТАЯ...`);
            console.log(`   Pool: ${poolAddress.toString().slice(0, 8)}...`);
            console.log(`   Position: ${positionAddress.toString().slice(0, 8)}...`);

            // Проверяем баланс
            const balanceCheck = await this.checkPositionBalance(poolAddress, positionAddress);

            if (!balanceCheck.exists) {
                console.log(`   ❌ ПОЗИЦИЯ НЕ СУЩЕСТВУЕТ!`);
                throw new Error(`Position ${positionAddress.toString()} does not exist`);
            }

            if (balanceCheck.isEmpty) {
                console.log(`   ✅ ПОЗИЦИЯ ПУСТАЯ, ГОТОВА К ИСПОЛЬЗОВАНИЮ`);
                return {
                    success: true,
                    isEmpty: true,
                    cleared: false,
                    message: 'Position is empty and ready'
                };
            }

            // Позиция содержит ликвидность - очищаем
            console.log(`   ⚠️ ПОЗИЦИЯ СОДЕРЖИТ ЛИКВИДНОСТЬ, ОЧИЩАЕМ...`);
            console.log(`   💰 X: ${balanceCheck.totalXAmount.toString()}, Y: ${balanceCheck.totalYAmount.toString()}`);

            const clearResult = await this.clearPosition(poolAddress, positionAddress);

            if (clearResult.success && clearResult.cleared) {
                console.log(`   ✅ ПОЗИЦИЯ ОЧИЩЕНА, ГОТОВА К ИСПОЛЬЗОВАНИЮ`);
                return {
                    success: true,
                    isEmpty: true,
                    cleared: true,
                    clearTransaction: clearResult.transaction,
                    binsCleared: clearResult.binsCleared,
                    message: 'Position cleared and ready'
                };
            } else {
                console.log(`   ❌ НЕ УДАЛОСЬ ОЧИСТИТЬ ПОЗИЦИЮ`);
                throw new Error(`Failed to clear position: ${clearResult.error}`);
            }

        } catch (error) {
            console.error(`❌ Ошибка проверки позиции: ${error.message}`);
            return {
                success: false,
                isEmpty: false,
                cleared: false,
                error: error.message
            };
        }
    }
}

module.exports = MeteoraPositionBalanceChecker;

// Тестирование если запущен напрямую
if (require.main === module) {
    async function testPositionChecker() {
        console.log('🧪 ТЕСТИРОВАНИЕ POSITION BALANCE CHECKER...\n');

        // 🔥 ИСПОЛЬЗУЕМ QUICKNODE RPC ИЗ .ENV!
        const quicknodeUrl = process.env.QUICKNODE_RPC_URL || process.env.QUICKNODE_RPC_URL_BACKUP2;
        console.log(`🚀 Тест: Используем QuickNode RPC: ${quicknodeUrl.slice(0, 50)}...`);

        const connection = new Connection(quicknodeUrl, 'confirmed');
        const wallet = { publicKey: new PublicKey('bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV') };
        
        const checker = new MeteoraPositionBalanceChecker(connection, wallet);
        
        // Тестовые данные
        const poolAddress = new PublicKey('BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y');
        const positionAddress = new PublicKey('6J6FngedRDg9ZtfctTH4o6895DyNZXDq2i3vtYyREF4U');
        
        // Тест 1: Проверка баланса
        console.log('📊 ТЕСТ 1: Проверка баланса позиции');
        const balanceResult = await checker.checkPositionBalance(poolAddress, positionAddress);
        console.log('Результат:', balanceResult);
        
        // Тест 2: Подготовка позиции
        console.log('\n🎯 ТЕСТ 2: Подготовка позиции для ликвидности');
        const prepareResult = await checker.preparePositionForLiquidity(poolAddress, positionAddress, {
            clearExisting: true,
            allowNonEmpty: false
        });
        console.log('Результат:', prepareResult);
        
        console.log('\n✅ ТЕСТИРОВАНИЕ ЗАВЕРШЕНО!');
    }
    
    testPositionChecker().catch(console.error);
}

// 🔥 ЭКСПОРТ КЛАССА ДЛЯ ИСПОЛЬЗОВАНИЯ В ДРУГИХ МОДУЛЯХ
module.exports = { MeteoraPositionBalanceChecker };
