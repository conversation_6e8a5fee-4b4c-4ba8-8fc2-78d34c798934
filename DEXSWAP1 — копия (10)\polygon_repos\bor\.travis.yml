language: go
go_import_path: github.com/ethereum/go-ethereum
sudo: false
jobs:
  include:
    # This builder create and push the Docker images for all architectures
    - stage: build
      if: type = push
      os: linux
      arch: amd64
      dist: focal
      go: 1.24.x
      env:
        - docker
      services:
        - docker
      git:
        submodules: false # avoid cloning ethereum/tests
      before_install:
        - export DOCKER_CLI_EXPERIMENTAL=enabled
      script:
        - go run build/ci.go dockerx -platform "linux/amd64,linux/arm64,linux/riscv64" -hub ethereum/client-go -upload

    # This builder does the Ubuntu PPA upload
    - stage: build
      if: type = push
      os: linux
      dist: bionic
      go: 1.22.x
      env:
        - ubuntu-ppa
        - GO111MODULE=on
      git:
        submodules: false # avoid cloning ethereum/tests
      addons:
        apt:
          packages:
            - devscripts
            - debhelper
            - dput
            - fakeroot
            - python-bzrlib
            - python-paramiko
      script:
        - echo '|1|7SiYPr9xl3uctzovOTj4gMwAC1M=|t6ReES75Bo/PxlOPJ6/GsGbTrM0= ssh-rsa AAAAB3NzaC1yc2EAAAABIwAAAQEA0aKz5UTUndYgIGG7dQBV+HaeuEZJ2xPHo2DS2iSKvUL4xNMSAY4UguNW+pX56nAQmZKIZZ8MaEvSj6zMEDiq6HFfn5JcTlM80UwlnyKe8B8p7Nk06PPQLrnmQt5fh0HmEcZx+JU9TZsfCHPnX7MNz4ELfZE6cFsclClrKim3BHUIGq//t93DllB+h4O9LHjEUsQ1Sr63irDLSutkLJD6RXchjROXkNirlcNVHH/jwLWR5RcYilNX7S5bIkK8NlWPjsn/8Ua5O7I9/YoE97PpO6i73DTGLh5H9JN/SITwCKBkgSDWUt61uPK3Y11Gty7o2lWsBjhBUm2Y38CBsoGmBw==' >> ~/.ssh/known_hosts
        - go run build/ci.go debsrc -goversion 1.14.2 -upload ethereum/ethereum -sftp-user geth-ci -signer "Go Ethereum Linux Builder <<EMAIL>>"

    # This builder does the Linux Azure uploads
    - stage: build
      if: type = push
      os: linux
      dist: focal
      sudo: required
      go: 1.24.x
      env:
        - azure-linux
      git:
        submodules: false # avoid cloning ethereum/tests
      script:
        # build amd64
        - go run build/ci.go install -dlgo
        - go run build/ci.go archive -type tar -signer LINUX_SIGNING_KEY -signify SIGNIFY_KEY -upload gethstore/builds

        # build 386
        - sudo -E apt-get -yq --no-install-suggests --no-install-recommends install gcc-multilib
        - git status --porcelain
        - go run build/ci.go install -dlgo -arch 386
        - go run build/ci.go archive -arch 386 -type tar -signer LINUX_SIGNING_KEY -signify SIGNIFY_KEY -upload gethstore/builds

        # Switch over GCC to cross compilation (breaks 386, hence why do it here only)
        - sudo -E apt-get -yq --no-install-suggests --no-install-recommends --force-yes install gcc-arm-linux-gnueabi libc6-dev-armel-cross gcc-arm-linux-gnueabihf libc6-dev-armhf-cross gcc-aarch64-linux-gnu libc6-dev-arm64-cross
        - sudo ln -s /usr/include/asm-generic /usr/include/asm

        - GOARM=5 go run build/ci.go install -dlgo -arch arm -cc arm-linux-gnueabi-gcc
        - GOARM=5 go run build/ci.go archive -arch arm -type tar -signer LINUX_SIGNING_KEY -signify SIGNIFY_KEY -upload gethstore/builds
        - GOARM=6 go run build/ci.go install -dlgo -arch arm -cc arm-linux-gnueabi-gcc
        - GOARM=6 go run build/ci.go archive -arch arm -type tar -signer LINUX_SIGNING_KEY -signify SIGNIFY_KEY -upload gethstore/builds
        - GOARM=7 go run build/ci.go install -dlgo -arch arm -cc arm-linux-gnueabihf-gcc
        - GOARM=7 go run build/ci.go archive -arch arm -type tar -signer LINUX_SIGNING_KEY -signify SIGNIFY_KEY -upload gethstore/builds
        - go run build/ci.go install -dlgo -arch arm64 -cc aarch64-linux-gnu-gcc
        - go run build/ci.go archive -arch arm64 -type tar -signer LINUX_SIGNING_KEY -signify SIGNIFY_KEY -upload gethstore/builds

    # This builder does the Android Maven and Azure uploads
    - stage: build
      if: type = push
      os: linux
      dist: bionic
      addons:
        apt:
          packages:
            - openjdk-8-jdk
      env:
        - azure-android
        - maven-android
        - GO111MODULE=on
      git:
        submodules: false # avoid cloning ethereum/tests
      before_install:
        # Install Android and it's dependencies manually, Travis is stale
        - export JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64
        - curl https://dl.google.com/android/repository/commandlinetools-linux-6858069_latest.zip -o android.zip
        - unzip -q android.zip -d $HOME/sdk && rm android.zip
        - mv $HOME/sdk/cmdline-tools $HOME/sdk/latest && mkdir $HOME/sdk/cmdline-tools && mv $HOME/sdk/latest $HOME/sdk/cmdline-tools
        - export PATH=$PATH:$HOME/sdk/cmdline-tools/latest/bin
        - export ANDROID_HOME=$HOME/sdk

        - yes | sdkmanager --licenses >/dev/null
        - sdkmanager "platform-tools" "platforms;android-15" "platforms;android-19" "platforms;android-24" "ndk-bundle"

        # Install Go to allow building with
        - curl https://dl.google.com/go/go1.21.4.linux-amd64.tar.gz | tar -xz
        - export PATH=`pwd`/go/bin:$PATH
        - export GOROOT=`pwd`/go
        - export GOPATH=$HOME/go
      script:
        # Build the Android archive and upload it to Maven Central and Azure
        - mkdir -p $GOPATH/src/github.com/ethereum
        - ln -s `pwd` $GOPATH/src/github.com/ethereum/go-ethereum
        - go run build/ci.go aar -signer ANDROID_SIGNING_KEY -signify SIGNIFY_KEY  -deploy https://oss.sonatype.org -upload gethstore/builds

    # This builder does the OSX Azure, iOS CocoaPods and iOS Azure uploads
    - stage: build
      if: type = push
      os: osx
      osx_image: xcode14.2
      go: 1.23.1 # See https://github.com/ethereum/go-ethereum/pull/30478
      env:
        - azure-osx
      git:
        submodules: false # avoid cloning ethereum/tests
      script:
        - ln -sf /Users/<USER>/gopath/bin/go1.23.1 /usr/local/bin/go # Work around travis go-setup bug
        - go run build/ci.go install -dlgo
        - go run build/ci.go archive -type tar -signer OSX_SIGNING_KEY -signify SIGNIFY_KEY -upload gethstore/builds
        - go run build/ci.go install -dlgo -arch arm64
        - go run build/ci.go archive -arch arm64 -type tar -signer OSX_SIGNING_KEY -signify SIGNIFY_KEY -upload gethstore/builds

    # These builders run the tests
    - stage: build
      if: type = push
      os: linux
      arch: amd64
      dist: focal
      go: 1.24.x
      script:
        - travis_wait 45 go run build/ci.go test $TEST_PACKAGES

    - stage: build
      if: type = pull_request
      os: linux
      arch: arm64
      dist: noble
      go: 1.22.x
      script:
        - travis_wait 45 go run build/ci.go test $TEST_PACKAGES

    - stage: build
      os: linux
      dist: focal
      go: 1.23.x
      script:
        - travis_wait 45 go run build/ci.go test $TEST_PACKAGES

    # This builder does the Ubuntu PPA nightly uploads
    - stage: build
      if: type = cron || (type = push && tag ~= /^v[0-9]/)
      os: linux
      dist: focal
      go: 1.24.x
      env:
        - ubuntu-ppa
      git:
        submodules: false # avoid cloning ethereum/tests
      before_install:
        - sudo -E apt-get -yq --no-install-suggests --no-install-recommends install devscripts debhelper dput fakeroot
      script:
        - echo '|1|7SiYPr9xl3uctzovOTj4gMwAC1M=|t6ReES75Bo/PxlOPJ6/GsGbTrM0= ssh-rsa AAAAB3NzaC1yc2EAAAABIwAAAQEA0aKz5UTUndYgIGG7dQBV+HaeuEZJ2xPHo2DS2iSKvUL4xNMSAY4UguNW+pX56nAQmZKIZZ8MaEvSj6zMEDiq6HFfn5JcTlM80UwlnyKe8B8p7Nk06PPQLrnmQt5fh0HmEcZx+JU9TZsfCHPnX7MNz4ELfZE6cFsclClrKim3BHUIGq//t93DllB+h4O9LHjEUsQ1Sr63irDLSutkLJD6RXchjROXkNirlcNVHH/jwLWR5RcYilNX7S5bIkK8NlWPjsn/8Ua5O7I9/YoE97PpO6i73DTGLh5H9JN/SITwCKBkgSDWUt61uPK3Y11Gty7o2lWsBjhBUm2Y38CBsoGmBw==' >> ~/.ssh/known_hosts
        - go run build/ci.go debsrc -upload ethereum/ethereum -sftp-user geth-ci -signer "Go Ethereum Linux Builder <<EMAIL>>"

    # This builder does the Azure archive purges to avoid accumulating junk
    - stage: build
      if: type = cron
      os: linux
      dist: focal
      go: 1.24.x
      env:
        - azure-purge
      git:
        submodules: false # avoid cloning ethereum/tests
      script:
        - go run build/ci.go purge -store gethstore/builds -days 14

    # This builder executes race tests
    - stage: build
      if: type = cron
      os: linux
      dist: focal
      go: 1.24.x
      env:
        - racetests
      script:
        - travis_wait 60 go run build/ci.go test -race $TEST_PACKAGES
