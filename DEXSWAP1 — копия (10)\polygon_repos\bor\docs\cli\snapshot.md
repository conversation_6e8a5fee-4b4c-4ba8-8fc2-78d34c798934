# snapshot

The ```snapshot``` command groups snapshot related actions:

- [```snapshot prune-state```](./snapshot_prune-state.md): Prune state databases at the given datadir location.

- [```snapshot prune-block```](./snapshot_prune-block.md): Prune ancient chaindata at the given datadir location.

- [```snapshot inspect-ancient-db```](./snapshot_inspect-ancient-db.md): Inspect few fields in ancient datastore.