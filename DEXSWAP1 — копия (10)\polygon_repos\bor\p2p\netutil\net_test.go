// Copyright 2016 The go-ethereum Authors
// This file is part of the go-ethereum library.
//
// The go-ethereum library is free software: you can redistribute it and/or modify
// it under the terms of the GNU Lesser General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// The go-ethereum library is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
// GNU Lesser General Public License for more details.
//
// You should have received a copy of the GNU Lesser General Public License
// along with the go-ethereum library. If not, see <http://www.gnu.org/licenses/>.

package netutil

import (
	"fmt"
	"math/rand"
	"net"
	"net/netip"
	"reflect"
	"testing"
	"testing/quick"

	"github.com/davecgh/go-spew/spew"
)

func TestParseNetlist(t *testing.T) {
	var tests = []struct {
		input    string
		wantErr  string
		wantList *Netlist
	}{
		{
			input:    "",
			wantList: &Netlist{},
		},
		{
			input:    "*********/8",
			wantList: &Netlist{netip.MustParsePrefix("*********/8")},
		},
		{
			input:   "*********/44",
			wantErr: `netip.ParsePrefix("*********/44"): prefix length out of range`,
		},
		{
			input: "*********/16, ***********/24,",
			wantList: &Netlist{
				netip.MustParsePrefix("*********/16"),
				netip.MustParsePrefix("***********/24"),
			},
		},
	}

	for _, test := range tests {
		l, err := ParseNetlist(test.input)
		if err == nil && test.wantErr != "" {
			t.Errorf("%q: got no error, expected %q", test.input, test.wantErr)
			continue
		} else if err != nil && err.Error() != test.wantErr {
			t.Errorf("%q: got error %q, want %q", test.input, err, test.wantErr)
			continue
		}

		if !reflect.DeepEqual(l, test.wantList) {
			spew.Dump(l)
			spew.Dump(test.wantList)
			t.Errorf("%q: got %v, want %v", test.input, l, test.wantList)
		}
	}
}

func TestNilNetListContains(t *testing.T) {
	var list *Netlist
	checkContains(t, list.Contains, list.ContainsAddr, nil, []string{"*******"})
}

func TestIsLAN(t *testing.T) {
	checkContains(t, IsLAN, AddrIsLAN,
		[]string{ // included
			"127.0.0.1",
			"********",
			"*********",
			"**************",
			"***********",
			"fe80::f4a1:8eff:fec5:9d9d",
			"febf::ab32:2233",
			"fc00::4",
			// 4-in-6
			"::ffff:127.0.0.1",
			"::ffff:*********",
		},
		[]string{ // excluded
			"*********",
			"*******",
			"**********",
			"fec0::2233",
			// 4-in-6
			"::ffff:***********",
		},
	)
}

func TestIsSpecialNetwork(t *testing.T) {
	checkContains(t, IsSpecialNetwork, AddrIsSpecialNetwork,
		[]string{ // included
			"0.0.0.0",
			"*******",
			"*********",
			"**********",
			"2001:db8:85a3:8d3:1319:8a2e:370:7348",
			"***************",
			"**********", // IPv4 multicast
			"ff05::1:3",  // IPv6 multicast
			// 4-in-6
			"::ffff:***************",
			"::ffff:*********",
		},
		[]string{ // excluded
			"*********",
			"*******",
			"**********",
			"fec0::2233",
		},
	)
}

func checkContains(t *testing.T, fn func(net.IP) bool, fn2 func(netip.Addr) bool, inc, exc []string) {
	for _, s := range inc {
		if !fn(parseIP(s)) {
			t.Error("returned false for included net.IP", s)
		}
		if !fn2(netip.MustParseAddr(s)) {
			t.Error("returned false for included netip.Addr", s)
		}
	}

	for _, s := range exc {
		if fn(parseIP(s)) {
			t.Error("returned true for excluded net.IP", s)
		}
		if fn2(netip.MustParseAddr(s)) {
			t.Error("returned true for excluded netip.Addr", s)
		}
	}
}

func parseIP(s string) net.IP {
	ip := net.ParseIP(s)
	if ip == nil {
		panic("invalid " + s)
	}

	return ip
}

func TestCheckRelayIP(t *testing.T) {
	tests := []struct {
		sender, addr string
		want         error
	}{
		{"127.0.0.1", "0.0.0.0", errUnspecified},
		{"***********", "0.0.0.0", errUnspecified},
		{"***********", "0.0.0.0", errUnspecified},
		{"127.0.0.1", "***************", errSpecial},
		{"***********", "***************", errSpecial},
		{"***********", "***************", errSpecial},
		{"***********", "**********", errLoopback},
		{"***********", "***********", errLAN},

		{"127.0.0.1", "**********", nil},
		{"127.0.0.1", "***********", nil},
		{"127.0.0.1", "***********", nil},
		{"***********", "***********", nil},
		{"***********", "***********", nil},
		{"***********", "***********", nil},
	}

	for _, test := range tests {
		err := CheckRelayIP(parseIP(test.sender), parseIP(test.addr))
		if err != test.want {
			t.Errorf("%s from %s: got %q, want %q", test.addr, test.sender, err, test.want)
		}
	}
}

func BenchmarkCheckRelayIP(b *testing.B) {
	sender := parseIP("***********")
	addr := parseIP("*********")

	for i := 0; i < b.N; i++ {
		CheckRelayIP(sender, addr)
	}
}

func TestSameNet(t *testing.T) {
	tests := []struct {
		ip, other string
		bits      uint
		want      bool
	}{
		{"0.0.0.0", "0.0.0.0", 32, true},
		{"0.0.0.0", "*******", 0, true},
		{"0.0.0.0", "*******", 31, true},
		{"0.0.0.0", "*******", 32, false},
		{"********", "********", 8, true},
		{"********", "********", 13, true},
		{"********", "********", 15, false},
	}

	for _, test := range tests {
		if ok := SameNet(test.bits, parseIP(test.ip), parseIP(test.other)); ok != test.want {
			t.Errorf("SameNet(%d, %s, %s) == %t, want %t", test.bits, test.ip, test.other, ok, test.want)
		}
	}
}

func ExampleSameNet() {
	// This returns true because the IPs are in the same /24 network:
	fmt.Println(SameNet(24, net.IP{127, 0, 0, 1}, net.IP{127, 0, 0, 3}))
	// This call returns false:
	fmt.Println(SameNet(24, net.IP{127, 3, 0, 1}, net.IP{127, 5, 0, 3}))
	// Output:
	// true
	// false
}

func TestDistinctNetSet(t *testing.T) {
	ops := []struct {
		add, remove string
		fails       bool
	}{
		{add: "127.0.0.1"},
		{add: "*********"},
		{add: "*********", fails: true},
		{add: "**********"},
		{add: "**********"},
		{add: "**********", fails: true},
		{add: "**********", fails: true},
		{add: "**********"},
		{add: "**********"},
		{add: "**********", fails: true},
		// Make room for an address, then add again.
		{remove: "127.0.0.1"},
		{add: "*********"},
		{add: "*********", fails: true},
	}

	set := DistinctNetSet{Subnet: 15, Limit: 2}

	for _, op := range ops {
		var desc string
		if op.add != "" {
			desc = fmt.Sprintf("Add(%s)", op.add)
			if ok := set.Add(parseIP(op.add)); ok != !op.fails {
				t.Errorf("%s == %t, want %t", desc, ok, !op.fails)
			}
		} else {
			desc = fmt.Sprintf("Remove(%s)", op.remove)
			set.Remove(parseIP(op.remove))
		}

		t.Logf("%s: %v", desc, set)
	}
}

func TestDistinctNetSetAddRemove(t *testing.T) {
	cfg := &quick.Config{
		Values: func(s []reflect.Value, rng *rand.Rand) {
			slice := make([]netip.Addr, rng.Intn(20)+1)
			for i := range slice {
				slice[i] = RandomAddr(rng, false)
			}
			s[0] = reflect.ValueOf(slice)
		},
	}
	fn := func(ips []netip.Addr) bool {
		s := DistinctNetSet{Limit: 3, Subnet: 2}
		for _, ip := range ips {
			s.AddAddr(ip)
		}

		for _, ip := range ips {
			s.RemoveAddr(ip)
		}

		return s.Len() == 0
	}

	if err := quick.Check(fn, cfg); err != nil {
		t.Fatal(err)
	}
}
