chain = "amoy"
# identity = "node_name"
# verbosity = 3
# vmdebug = false
datadir = "/var/lib/bor/data"
# ancient = ""
# db.engine = "pebble"
state.scheme = "hash"
# keystore = ""
# "rpc.batchlimit" = 100
# "rpc.returndatalimit" = 100000
syncmode = "full"
gcmode = "archive"
# snapshot = true
# ethstats = ""
# devfakeauthor = false

# ["eth.requiredblocks"]

# [log]
    # vmodule = ""
    # json = false
    # backtrace = ""
    # debug = true

[p2p]
    maxpeers = 200
    port = 30303
    # maxpendpeers = 50
    # bind = "0.0.0.0"
    # nodiscover = false
    # nat = "any"
    # netrestrict = ""
    # nodekey = ""
    # nodekeyhex = ""
    # txarrivalwait = "500ms"
    # txannouncementonly = false
    [p2p.discovery]
        # v5disc = true
        # bootnodesv4 = []
        # bootnodesv5 = []
        bootnodes = [ "enode://0ef8758cafc0063405f3f31fe22f2a3b566aa871bd7cd405e35954ec8aa7237c21e1ccc1f65f1b6099ab36db029362bc2fecf001a771b3d9803bbf1968508cef@35.197.249.21:30303", "enode://c9c8c18cde48b41d46ced0c564496aef721a9b58f8724025a0b1f3f26f1b826f31786f890f8f8781e18b16dbb3c7bff805c7304d1273ac11630ed25a3f0dc41c@34.89.39.114:30303" ]
        static-nodes = [ "enode://0ef8758cafc0063405f3f31fe22f2a3b566aa871bd7cd405e35954ec8aa7237c21e1ccc1f65f1b6099ab36db029362bc2fecf001a771b3d9803bbf1968508cef@35.197.249.21:30303", "enode://c9c8c18cde48b41d46ced0c564496aef721a9b58f8724025a0b1f3f26f1b826f31786f890f8f8781e18b16dbb3c7bff805c7304d1273ac11630ed25a3f0dc41c@34.89.39.114:30303", "enode://5b8d436677fb545b1c3fd1ae84553d478d9d21ad3b06a908b9d34d2df367ead5bb8823d84a370e26bdde8896ba8a870e21ba3a6dce19c0ded086296df5f04f15@35.242.167.175:30303", "enode://5bd810da4f021a974e80cf2be48975d58cafbcfdd971d568ab98250f8568a9457bdc1b7a6d16b5aebfcb9deb0c1ec612f0664d5366c74f5266906a2774dd70f0@34.89.15.223:30303", "enode://a2ec3671e553ba3e711639033912be55fe1e7fa4b61a93f6a1ac0cd3cea34f9d7eec1d718e04049531cf5dd7efc1ac677df1cf0e1f24f5e677706d7bcb3917de@34.105.128.110:30303", "enode://9e15bc58779c32119140d54a8384940b57a10a001506ce173cc4cdb10876b14a2ac9ae91f9389caf9fd385c3b72825f8bbbe937e7e57b1f032561703e900da59@***********:30303", "enode://42203e9b423aba24e1e9386f94d0d0397a42770427e8e9e22f9e2a9523f66abb13b1f5a6addee68ad5986f94a8f6de626f5829492599a2f9484f98e86e26149d@************:30303", "enode://83c235bb4305ecdd5addcbfd09478d2df7cddca9b7eed3ef11b2426fad04ccfe7335279e2371a85696e461dcbe78de6912d07043a912dbd85cb0bb944d78b8d9@************:30303" ]
        # trusted-nodes = []
        dns = [ "enrtree://<EMAIL>" ]

# [heimdall]
    # url = "http://localhost:1317"
    # "bor.without" = false
    # grpc-address = ""

[txpool]
    nolocals = true
    accountslots = 16
    globalslots = 131072
    accountqueue = 64
    globalqueue = 131072
    lifetime = "1h30m0s"
    # locals = []
    # journal = ""
    # rejournal = "1h0m0s"
    # pricelimit = 2********00
    # pricebump = 10

[miner]
    gaslimit = ********
    # gasprice = "2********00"
    # mine = false
    # etherbase = ""
    # extradata = ""
    # recommit = "2m5s"
    # commitinterrupt = true

[jsonrpc]
    ipcpath = "/var/lib/bor/bor.ipc"
    # ipcdisable = false
    # gascap = ********
    # evmtimeout = "5s"
    # txfeecap = 5.0
    # allow-unprotected-txs = false
    # enabledeprecatedpersonal = false
    [jsonrpc.http]
        enabled = true
        port = 8545
        host = "0.0.0.0"
        api = ["eth", "net", "web3", "txpool", "bor"]
        vhosts = ["*"]
        corsdomain = ["*"]
        # prefix = ""
        # ep-size = 40
        # ep-requesttimeout = "0s"
    [jsonrpc.ws]
        enabled = true
        port = 8546
        # prefix = ""
        # host = "localhost"
        # api = ["web3", "net"]
        origins = ["*"]
        # ep-size = 40
        # ep-requesttimeout = "0s"
    # [jsonrpc.graphql]
        # enabled = false
        # port = 0
        # prefix = ""
        # host = ""
        # vhosts = ["*"]
        # corsdomain = ["*"]
    # [jsonrpc.auth]
        # jwtsecret = ""
        # addr = "localhost"
        # port = 8551
        # vhosts = ["localhost"]
    # [jsonrpc.timeouts]
        # read = "10s"
        # write = "30s"
        # idle = "2m0s"

# [gpo]
    # blocks = 20
    # percentile = 60
    # maxheaderhistory = 1024
    # maxblockhistory = 1024
    # maxprice = "********00000"
    # ignoreprice = "2********00"

[telemetry]
    metrics = true
    # expensive = false
    # prometheus-addr = ""
    # opencollector-endpoint = ""
    # [telemetry.influx]
        # influxdb = false
        # endpoint = ""
        # database = ""
        # username = ""
        # password = ""
        # influxdbv2 = false
        # token = ""
        # bucket = ""
        # organization = ""
    # [telemetry.influx.tags]

[cache]
    # cache = 1024
    gc = 0
    snapshot = 20
    # database = 50
    trie = 30
    # journal = "triecache"
    # rejournal = "1h0m0s"
    # noprefetch = false
    # preimages = false
    # txlookuplimit = 2350000
    # timeout = "1h0m0s"
    # fdlimit = 0

# [accounts]
    # unlock = []
    # password = ""
    # allow-insecure-unlock = false
    # lightkdf = false
    # disable-bor-wallet = false

# [grpc]
    # addr = ":3131"

# [developer]
    # dev = false
    # period = 0
    # gaslimit = ********

# [parallelevm]
  # enable = true
  # procs = 8
  # enforce = false

# [pprof]
#   pprof = false
#   port = 6060
#   addr = "127.0.0.1"
#   memprofilerate = 524288
#   blockprofilerate = 0
