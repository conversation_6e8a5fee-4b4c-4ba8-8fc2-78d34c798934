// -*- mode: go -*-

package test

type Aux struct{
    A uint32
}

type Test struct{
    Uint8 *byte `rlp:"nil"`
    Uint8List *byte `rlp:"nilList"`

    Uint32 *uint32 `rlp:"nil"`
    Uint32List *uint32 `rlp:"nilList"`

    Uint64 *uint64 `rlp:"nil"`
    Uint64List *uint64 `rlp:"nilList"`

    String *string `rlp:"nil"`
    StringList *string `rlp:"nilList"`

    ByteArray *[3]byte `rlp:"nil"`
    ByteArrayList *[3]byte `rlp:"nilList"`

    ByteSlice *[]byte `rlp:"nil"`
    ByteSliceList *[]byte `rlp:"nilList"`

    Struct *Aux `rlp:"nil"`
    StructString *Aux `rlp:"nilString"`
}
