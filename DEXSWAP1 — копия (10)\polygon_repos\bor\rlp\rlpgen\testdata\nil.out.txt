package test

import "github.com/ethereum/go-ethereum/rlp"
import "io"

func (obj *Test) EncodeRLP(_w io.Writer) error {
	w := rlp.NewEncoderBuffer(_w)
	_tmp0 := w.List()
	if obj.Uint8 == nil {
		w.Write([]byte{0x80})
	} else {
		w.WriteUint64(uint64((*obj.Uint8)))
	}
	if obj.Uint8List == nil {
		w.Write([]byte{0xC0})
	} else {
		w.WriteUint64(uint64((*obj.Uint8List)))
	}
	if obj.Uint32 == nil {
		w.Write([]byte{0x80})
	} else {
		w.WriteUint64(uint64((*obj.Uint32)))
	}
	if obj.Uint32List == nil {
		w.Write([]byte{0xC0})
	} else {
		w.WriteUint64(uint64((*obj.Uint32List)))
	}
	if obj.Uint64 == nil {
		w.Write([]byte{0x80})
	} else {
		w.WriteUint64((*obj.Uint64))
	}
	if obj.Uint64List == nil {
		w.Write([]byte{0xC0})
	} else {
		w.WriteUint64((*obj.Uint64List))
	}
	if obj.String == nil {
		w.Write([]byte{0x80})
	} else {
		w.WriteString((*obj.String))
	}
	if obj.StringList == nil {
		w.Write([]byte{0xC0})
	} else {
		w.WriteString((*obj.StringList))
	}
	if obj.ByteArray == nil {
		w.Write([]byte{0x80})
	} else {
		w.WriteBytes(obj.ByteArray[:])
	}
	if obj.ByteArrayList == nil {
		w.Write([]byte{0xC0})
	} else {
		w.WriteBytes(obj.ByteArrayList[:])
	}
	if obj.ByteSlice == nil {
		w.Write([]byte{0x80})
	} else {
		w.WriteBytes((*obj.ByteSlice))
	}
	if obj.ByteSliceList == nil {
		w.Write([]byte{0xC0})
	} else {
		w.WriteBytes((*obj.ByteSliceList))
	}
	if obj.Struct == nil {
		w.Write([]byte{0xC0})
	} else {
		_tmp1 := w.List()
		w.WriteUint64(uint64(obj.Struct.A))
		w.ListEnd(_tmp1)
	}
	if obj.StructString == nil {
		w.Write([]byte{0x80})
	} else {
		_tmp2 := w.List()
		w.WriteUint64(uint64(obj.StructString.A))
		w.ListEnd(_tmp2)
	}
	w.ListEnd(_tmp0)
	return w.Flush()
}

func (obj *Test) DecodeRLP(dec *rlp.Stream) error {
	var _tmp0 Test
	{
		if _, err := dec.List(); err != nil {
			return err
		}
		// Uint8:
		var _tmp2 *byte
		if _tmp3, _tmp4, err := dec.Kind(); err != nil {
			return err
		} else if _tmp4 != 0 || _tmp3 != rlp.String {
			_tmp1, err := dec.Uint8()
			if err != nil {
				return err
			}
			_tmp2 = &_tmp1
		}
		_tmp0.Uint8 = _tmp2
		// Uint8List:
		var _tmp6 *byte
		if _tmp7, _tmp8, err := dec.Kind(); err != nil {
			return err
		} else if _tmp8 != 0 || _tmp7 != rlp.List {
			_tmp5, err := dec.Uint8()
			if err != nil {
				return err
			}
			_tmp6 = &_tmp5
		}
		_tmp0.Uint8List = _tmp6
		// Uint32:
		var _tmp10 *uint32
		if _tmp11, _tmp12, err := dec.Kind(); err != nil {
			return err
		} else if _tmp12 != 0 || _tmp11 != rlp.String {
			_tmp9, err := dec.Uint32()
			if err != nil {
				return err
			}
			_tmp10 = &_tmp9
		}
		_tmp0.Uint32 = _tmp10
		// Uint32List:
		var _tmp14 *uint32
		if _tmp15, _tmp16, err := dec.Kind(); err != nil {
			return err
		} else if _tmp16 != 0 || _tmp15 != rlp.List {
			_tmp13, err := dec.Uint32()
			if err != nil {
				return err
			}
			_tmp14 = &_tmp13
		}
		_tmp0.Uint32List = _tmp14
		// Uint64:
		var _tmp18 *uint64
		if _tmp19, _tmp20, err := dec.Kind(); err != nil {
			return err
		} else if _tmp20 != 0 || _tmp19 != rlp.String {
			_tmp17, err := dec.Uint64()
			if err != nil {
				return err
			}
			_tmp18 = &_tmp17
		}
		_tmp0.Uint64 = _tmp18
		// Uint64List:
		var _tmp22 *uint64
		if _tmp23, _tmp24, err := dec.Kind(); err != nil {
			return err
		} else if _tmp24 != 0 || _tmp23 != rlp.List {
			_tmp21, err := dec.Uint64()
			if err != nil {
				return err
			}
			_tmp22 = &_tmp21
		}
		_tmp0.Uint64List = _tmp22
		// String:
		var _tmp26 *string
		if _tmp27, _tmp28, err := dec.Kind(); err != nil {
			return err
		} else if _tmp28 != 0 || _tmp27 != rlp.String {
			_tmp25, err := dec.String()
			if err != nil {
				return err
			}
			_tmp26 = &_tmp25
		}
		_tmp0.String = _tmp26
		// StringList:
		var _tmp30 *string
		if _tmp31, _tmp32, err := dec.Kind(); err != nil {
			return err
		} else if _tmp32 != 0 || _tmp31 != rlp.List {
			_tmp29, err := dec.String()
			if err != nil {
				return err
			}
			_tmp30 = &_tmp29
		}
		_tmp0.StringList = _tmp30
		// ByteArray:
		var _tmp34 *[3]byte
		if _tmp35, _tmp36, err := dec.Kind(); err != nil {
			return err
		} else if _tmp36 != 0 || _tmp35 != rlp.String {
			var _tmp33 [3]byte
			if err := dec.ReadBytes(_tmp33[:]); err != nil {
				return err
			}
			_tmp34 = &_tmp33
		}
		_tmp0.ByteArray = _tmp34
		// ByteArrayList:
		var _tmp38 *[3]byte
		if _tmp39, _tmp40, err := dec.Kind(); err != nil {
			return err
		} else if _tmp40 != 0 || _tmp39 != rlp.List {
			var _tmp37 [3]byte
			if err := dec.ReadBytes(_tmp37[:]); err != nil {
				return err
			}
			_tmp38 = &_tmp37
		}
		_tmp0.ByteArrayList = _tmp38
		// ByteSlice:
		var _tmp42 *[]byte
		if _tmp43, _tmp44, err := dec.Kind(); err != nil {
			return err
		} else if _tmp44 != 0 || _tmp43 != rlp.String {
			_tmp41, err := dec.Bytes()
			if err != nil {
				return err
			}
			_tmp42 = &_tmp41
		}
		_tmp0.ByteSlice = _tmp42
		// ByteSliceList:
		var _tmp46 *[]byte
		if _tmp47, _tmp48, err := dec.Kind(); err != nil {
			return err
		} else if _tmp48 != 0 || _tmp47 != rlp.List {
			_tmp45, err := dec.Bytes()
			if err != nil {
				return err
			}
			_tmp46 = &_tmp45
		}
		_tmp0.ByteSliceList = _tmp46
		// Struct:
		var _tmp51 *Aux
		if _tmp52, _tmp53, err := dec.Kind(); err != nil {
			return err
		} else if _tmp53 != 0 || _tmp52 != rlp.List {
			var _tmp49 Aux
			{
				if _, err := dec.List(); err != nil {
					return err
				}
				// A:
				_tmp50, err := dec.Uint32()
				if err != nil {
					return err
				}
				_tmp49.A = _tmp50
				if err := dec.ListEnd(); err != nil {
					return err
				}
			}
			_tmp51 = &_tmp49
		}
		_tmp0.Struct = _tmp51
		// StructString:
		var _tmp56 *Aux
		if _tmp57, _tmp58, err := dec.Kind(); err != nil {
			return err
		} else if _tmp58 != 0 || _tmp57 != rlp.String {
			var _tmp54 Aux
			{
				if _, err := dec.List(); err != nil {
					return err
				}
				// A:
				_tmp55, err := dec.Uint32()
				if err != nil {
					return err
				}
				_tmp54.A = _tmp55
				if err := dec.ListEnd(); err != nil {
					return err
				}
			}
			_tmp56 = &_tmp54
		}
		_tmp0.StructString = _tmp56
		if err := dec.ListEnd(); err != nil {
			return err
		}
	}
	*obj = _tmp0
	return nil
}
