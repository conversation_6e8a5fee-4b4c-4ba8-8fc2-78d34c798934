// -*- mode: go -*-

package test

type Aux struct {
	A uint64
}

type Test struct {
	Uint64      uint64             `rlp:"optional"`
	Pointer     *uint64            `rlp:"optional"`
	String      string             `rlp:"optional"`
	Slice       []uint64           `rlp:"optional"`
	Array       [3]byte            `rlp:"optional"`
	NamedStruct Aux                `rlp:"optional"`
	AnonStruct  struct{ A string } `rlp:"optional"`
}
