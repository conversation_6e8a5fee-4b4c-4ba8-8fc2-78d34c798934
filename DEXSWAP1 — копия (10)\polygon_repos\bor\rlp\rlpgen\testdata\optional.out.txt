package test

import "github.com/ethereum/go-ethereum/rlp"
import "io"

func (obj *Test) EncodeRLP(_w io.Writer) error {
	w := rlp.NewEncoderBuffer(_w)
	_tmp0 := w.List()
	_tmp1 := obj.Uint64 != 0
	_tmp2 := obj.Pointer != nil
	_tmp3 := obj.String != ""
	_tmp4 := len(obj.Slice) > 0
	_tmp5 := obj.Array != ([3]byte{})
	_tmp6 := obj.NamedStruct != (Aux{})
	_tmp7 := obj.AnonStruct != (struct{ A string }{})
	if _tmp1 || _tmp2 || _tmp3 || _tmp4 || _tmp5 || _tmp6 || _tmp7 {
		w.WriteUint64(obj.Uint64)
	}
	if _tmp2 || _tmp3 || _tmp4 || _tmp5 || _tmp6 || _tmp7 {
		if obj.Pointer == nil {
			w.Write([]byte{0x80})
		} else {
			w.WriteUint64((*obj.Pointer))
		}
	}
	if _tmp3 || _tmp4 || _tmp5 || _tmp6 || _tmp7 {
		w.WriteString(obj.String)
	}
	if _tmp4 || _tmp5 || _tmp6 || _tmp7 {
		_tmp8 := w.List()
		for _, _tmp9 := range obj.Slice {
			w.WriteUint64(_tmp9)
		}
		w.ListEnd(_tmp8)
	}
	if _tmp5 || _tmp6 || _tmp7 {
		w.WriteBytes(obj.Array[:])
	}
	if _tmp6 || _tmp7 {
		_tmp10 := w.List()
		w.WriteUint64(obj.NamedStruct.A)
		w.ListEnd(_tmp10)
	}
	if _tmp7 {
		_tmp11 := w.List()
		w.WriteString(obj.AnonStruct.A)
		w.ListEnd(_tmp11)
	}
	w.ListEnd(_tmp0)
	return w.Flush()
}

func (obj *Test) DecodeRLP(dec *rlp.Stream) error {
	var _tmp0 Test
	{
		if _, err := dec.List(); err != nil {
			return err
		}
		// Uint64:
		if dec.MoreDataInList() {
			_tmp1, err := dec.Uint64()
			if err != nil {
				return err
			}
			_tmp0.Uint64 = _tmp1
			// Pointer:
			if dec.MoreDataInList() {
				_tmp2, err := dec.Uint64()
				if err != nil {
					return err
				}
				_tmp0.Pointer = &_tmp2
				// String:
				if dec.MoreDataInList() {
					_tmp3, err := dec.String()
					if err != nil {
						return err
					}
					_tmp0.String = _tmp3
					// Slice:
					if dec.MoreDataInList() {
						var _tmp4 []uint64
						if _, err := dec.List(); err != nil {
							return err
						}
						for dec.MoreDataInList() {
							_tmp5, err := dec.Uint64()
							if err != nil {
								return err
							}
							_tmp4 = append(_tmp4, _tmp5)
						}
						if err := dec.ListEnd(); err != nil {
							return err
						}
						_tmp0.Slice = _tmp4
						// Array:
						if dec.MoreDataInList() {
							var _tmp6 [3]byte
							if err := dec.ReadBytes(_tmp6[:]); err != nil {
								return err
							}
							_tmp0.Array = _tmp6
							// NamedStruct:
							if dec.MoreDataInList() {
								var _tmp7 Aux
								{
									if _, err := dec.List(); err != nil {
										return err
									}
									// A:
									_tmp8, err := dec.Uint64()
									if err != nil {
										return err
									}
									_tmp7.A = _tmp8
									if err := dec.ListEnd(); err != nil {
										return err
									}
								}
								_tmp0.NamedStruct = _tmp7
								// AnonStruct:
								if dec.MoreDataInList() {
									var _tmp9 struct{ A string }
									{
										if _, err := dec.List(); err != nil {
											return err
										}
										// A:
										_tmp10, err := dec.String()
										if err != nil {
											return err
										}
										_tmp9.A = _tmp10
										if err := dec.ListEnd(); err != nil {
											return err
										}
									}
									_tmp0.AnonStruct = _tmp9
								}
							}
						}
					}
				}
			}
		}
		if err := dec.ListEnd(); err != nil {
			return err
		}
	}
	*obj = _tmp0
	return nil
}
