{"types": {"EIP712Domain": [{"name": "name", "type": "string"}, {"name": "version", "type": "string"}, {"name": "chainId", "type": "uint256"}, {"name": "verifyingContract", "type": "address"}], "Person": [{"name": "name", "type": "string"}, {"name": "wallet", "type": "address"}], "Mail": [{"name": "from", "type": "Person"}, {"name": "to", "type": "Person"}, {"name": "contents", "type": "string"}]}, "primaryType": "Mail", "domain": {"name": "Ether Mail", "version": "1", "chainId": "1", "vFAILFAILerifyingContract": "******************************************"}, "message": {"from": {"name": "Cow", "wallet": "******************************************"}, "to": {"name": "<PERSON>", "wallet": "******************************************"}, "contents": "Hello, <PERSON>!"}}