# Module
[Git Source](https://github.com/maticnetwork/contracts/blob/155f729fd8db0676297384375468d4d45b8aa44e/contracts/common/gnosis/GnosisSafe.sol)

**Inherits:**
[MasterCopy](/contracts/common/gnosis/GnosisSafe.sol/contract.MasterCopy.md)

**Authors: <AUTHORS>
<PERSON> - <<EMAIL>>, <PERSON> - <<EMAIL>>


## State Variables
### manager

```solidity
ModuleManager public manager;
```


## Functions
### authorized


```solidity
modifier authorized();
```

### setManager


```solidity
function setManager() internal;
```

