

# Contents
- [depositManager](/contracts/root/depositManager)
- [predicates](/contracts/root/predicates)
- [stateSyncer](/contracts/root/stateSyncer)
- [withdrawManager](/contracts/root/withdrawManager)
- [IRootChain](IRootChain.sol/interface.IRootChain.md)
- [RootChain](RootChain.sol/contract.RootChain.md)
- [RootChainProxy](RootChainProxy.sol/contract.RootChainProxy.md)
- [RootChainHeader](RootChainStorage.sol/contract.RootChainHeader.md)
- [RootChainStorage](RootChainStorage.sol/contract.RootChainStorage.md)
