# WithdrawManagerProxy
[Git Source](https://github.com/maticnetwork/contracts/blob/155f729fd8db0676297384375468d4d45b8aa44e/contracts/root/withdrawManager/WithdrawManagerProxy.sol)

**Inherits:**
[Proxy](/contracts/common/misc/Proxy.sol/contract.Proxy.md), [WithdrawManagerStorage](/contracts/root/withdrawManager/WithdrawManagerStorage.sol/contract.WithdrawManagerStorage.md)


## Functions
### constructor


```solidity
constructor(address _proxyTo, address _registry, address _rootChain, address _exitNft) public Proxy(_proxyTo);
```

