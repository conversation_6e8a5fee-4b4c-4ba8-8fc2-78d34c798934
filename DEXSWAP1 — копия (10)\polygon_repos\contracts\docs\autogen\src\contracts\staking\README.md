

# Contents
- [slashing](/contracts/staking/slashing)
- [stakeManager](/contracts/staking/stakeManager)
- [validatorShare](/contracts/staking/validatorShare)
- [IStakeManagerEventsHub](EventsHub.sol/contract.IStakeManagerEventsHub.md)
- [EventsHub](EventsHub.sol/contract.EventsHub.md)
- [EventsHubProxy](EventsHubProxy.sol/contract.EventsHubProxy.md)
- [IStakeManagerLocal](StakingInfo.sol/contract.IStakeManagerLocal.md)
- [StakingInfo](StakingInfo.sol/contract.StakingInfo.md)
