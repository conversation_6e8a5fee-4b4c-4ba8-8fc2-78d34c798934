

# Contents
- [IStakeManager](IStakeManager.sol/contract.IStakeManager.md)
- [StakeManager](StakeManager.sol/contract.StakeManager.md)
- [StakeManagerExtension](StakeManagerExtension.sol/contract.StakeManagerExtension.md)
- [StakeManagerProxy](StakeManagerProxy.sol/contract.StakeManagerProxy.md)
- [StakeManagerStorage](StakeManagerStorage.sol/contract.StakeManagerStorage.md)
- [StakeManagerStorageExtension](StakeManagerStorageExtension.sol/contract.StakeManagerStorageExtension.md)
- [StakingNFT](StakingNFT.sol/contract.StakingNFT.md)
