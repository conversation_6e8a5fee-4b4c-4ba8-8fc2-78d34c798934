# StakeManagerStorageExtension
[Git Source](https://github.com/maticnetwork/contracts/blob/155f729fd8db0676297384375468d4d45b8aa44e/contracts/staking/stakeManager/StakeManagerStorageExtension.sol)


## State Variables
### eventsHub

```solidity
address public eventsHub;
```


### rewardPerStake

```solidity
uint256 public rewardPerStake;
```


### extensionCode

```solidity
address public extensionCode;
```


### signers

```solidity
address[] public signers;
```


### CHK_REWARD_PRECISION

```solidity
uint256 constant CHK_REWARD_PRECISION = 100;
```


### prevBlockInterval

```solidity
uint256 public prevBlockInterval;
```


### rewardDecreasePerCheckpoint

```solidity
uint256 public rewardDecreasePerCheckpoint;
```


### maxRewardedCheckpoints

```solidity
uint256 public maxRewardedCheckpoints;
```


### checkpointRewardDelta

```solidity
uint256 public checkpointRewardDelta;
```


