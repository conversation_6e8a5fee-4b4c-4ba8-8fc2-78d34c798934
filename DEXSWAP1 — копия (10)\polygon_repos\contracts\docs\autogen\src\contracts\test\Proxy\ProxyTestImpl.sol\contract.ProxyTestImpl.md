# ProxyTestImpl
[Git Source](https://github.com/maticnetwork/contracts/blob/155f729fd8db0676297384375468d4d45b8aa44e/contracts/test/Proxy/ProxyTestImpl.sol)

**Inherits:**
[Initializable](/contracts/common/mixin/Initializable.sol/contract.Initializable.md)


## State Variables
### a

```solidity
uint256 public a = 1;
```


### b

```solidity
uint256 public b = 2;
```


### ctorInit

```solidity
uint256 public ctorInit;
```


## Functions
### constructor


```solidity
constructor() public;
```

### init


```solidity
function init() public initializer;
```

