# StakeManagerTestable
[Git Source](https://github.com/maticnetwork/contracts/blob/155f729fd8db0676297384375468d4d45b8aa44e/contracts/test/StakeManagerTestable.sol)

**Inherits:**
[StakeManager](/contracts/staking/stakeManager/StakeManager.sol/contract.StakeManager.md)


## Functions
### advanceEpoch


```solidity
function advanceEpoch(uint256 delta) public;
```

### testLockShareContract


```solidity
function testLockShareContract(uint256 validatorId, bool lock) public;
```

### forceFinalizeCommit


```solidity
function forceFinalizeCommit() public;
```

### resetSignerUsed


```solidity
function resetSignerUsed(address signer) public;
```

