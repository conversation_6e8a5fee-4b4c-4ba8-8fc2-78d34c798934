#!/usr/bin/env sh

BOR=bor
DIR=$PWD

mkdir $DIR/data
$BOR --datadir $DIR/data init $DIR/genesis.json
cp -rf $DIR/keystore $DIR/data/

$BOR --datadir $DIR/data \
  --port 30303 \
  --http --http.addr '0.0.0.0' \
  --http.vhosts '*' \
  --http.corsdomain '*' \
  --http.port 9545 \
  --ipcdisable \
  --http.api 'personal,db,eth,net,web3,txpool,miner,admin,bor' \
  --syncmode 'full' \
  --networkid '15001' \
  --unlock '******************************************, ******************************************' \
  --password $DIR/password.txt \
  --allow-insecure-unlock \
  --miner.gastarget '20000000' \
  --miner.gaslimit '20000000' \
  --bor.without<PERSON><PERSON><PERSON> \
  --mine
