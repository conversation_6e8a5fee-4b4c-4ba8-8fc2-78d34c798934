import ethUtils from 'ethereumjs-util'
import sigUtils from 'eth-sig-util'

export function getSig({
  privateKey,
  spender,
  orderId,
  expiration,

  token1,
  amount1,
  token2,
  amount2
}) {
  const orderData = Buffer.concat([
    ethUtils.toBuffer(orderId),
    ethUtils.toBuffer(token2),
    ethUtils.setLengthLeft(amount2, 32)
  ])
  const orderDataHash = ethUtils.keccak256(orderData)
  return getTransferSig({
    privateKey: privateKey,
    spender: spender,
    data: orderDataHash,
    tokenIdOrAmount: amount1,
    tokenAddress: token1,
    expiration: expiration
  })
}

export function getTransferSig({
  privateKey,
  spender,
  data,
  tokenAddress,
  tokenIdOrAmount,
  expiration
}) {
  const typedData = getTransferTypedData({
    tokenAddress,
    tokenIdOrAmount,
    spender,
    data,
    expiration
  })
  const sig = sigUtils.signTypedData(ethUtils.toBuffer(privateKey), {
    data: typedData
  })
  return {
    sig,
    tokenAddress,
    tokenIdOrAmount,
    spender,
    expiration,
    data: ethUtils.toBuffer(data)
  }
}

function getTransferTypedData({
  tokenAddress,
  spender,
  tokenIdOrAmount,
  data,
  expiration
}) {
  return {
    types: {
      EIP712Domain: [
        { name: "name", type: "string" },
        { name: "version", type: "string" },
        { name: "chainId", type: "uint256" },
        { name: "verifyingContract", type: "address" }
      ],
      TokenTransferOrder: [
        { name: "spender", type: "address" },
        { name: "tokenIdOrAmount", type: "uint256" },
        { name: "data", type: "bytes32" },
        { name: "expiration", type: "uint256" }
      ]
    },
    domain: {
      name: "Matic Network",
      version: "1",
      chainId: {{ borChainId }},
      verifyingContract: tokenAddress
    },
    primaryType: "TokenTransferOrder",
    message: {
      spender,
      tokenIdOrAmount,
      data,
      expiration
    }
  }
}
