name: amoy_deb_profiles

on:
  push:
    branches:
      - 'master'
    paths:
      - '**'
    tags:
      - 'v*.*.*'
      - 'v*.*.*-*'

jobs:
  build:
    permissions:
      id-token: write
      contents: write
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      # Variables
      - name: Adding TAG to ENV
        run: echo "GIT_TAG=`echo $(git describe --tags --abbrev=0)`" >> $GITHUB_ENV
      - name: adding version
        run: |
          NUMERIC_VERSION=$( echo ${{ env.GIT_TAG }} | sed 's/[^0-9.]//g' )
          echo "VERSION=$NUMERIC_VERSION" >> $GITHUB_ENV


      - name: Making directory structure for yaml
        run: mkdir -p packaging/deb/heimdalld/var/lib/heimdall/config
      - name: making directory structure for the systemd
        run: mkdir -p packaging/deb/heimdalld/lib/systemd/system
      - name: delete old control file
        run: rm -rf packaging/deb/heimdalld/DEBIAN/control

      # Control file creation
      - name: create control file
        run: |
          touch packaging/deb/heimdalld/DEBIAN/control
          echo "Package: heimdall-profile" >> packaging/deb/heimdalld/DEBIAN/control
          echo "Version: ${{ env.VERSION }}" >> packaging/deb/heimdalld/DEBIAN/control
          echo "Section: base" >> packaging/deb/heimdalld/DEBIAN/control
          echo "Priority: optional" >> packaging/deb/heimdalld/DEBIAN/control
          echo "Architecture: all" >> packaging/deb/heimdalld/DEBIAN/control
          echo "Maintainer: <EMAIL>" >> packaging/deb/heimdalld/DEBIAN/control
          echo "Description: heimdall profile package" >> packaging/deb/heimdalld/DEBIAN/control

#### Sentry Profiles ####
      - name: Setting up heimdalld for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: cp -rp packaging/deb/heimdalld packaging/deb/heimdall-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}
        env:
          ARCH: all
          NODE: sentry
          NETWORK: amoy
      - name: Copying Postinst/Postrm for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: |
          cp -rp packaging/templates/package_scripts/postinst.profile packaging/deb/heimdall-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/DEBIAN/postinst
          cp -rp packaging/templates/package_scripts/postrm.profile packaging/deb/heimdall-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/DEBIAN/postrm
        env:
          ARCH: all
          NODE: sentry
          NETWORK: amoy
      - name: Copying preinst for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: |
          cp -rp packaging/templates/package_scripts/preinst packaging/deb/heimdall-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/DEBIAN/preinst
          sed -i "s/{{NETWORK}}/${{ env.NETWORK }}/g" packaging/deb/heimdall-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/DEBIAN/preinst
        env:
          ARCH: all
          NODE: sentry
          NETWORK: amoy
      - name: Copying heimdall-config for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: cp -rp packaging/templates/config/${{ env.NETWORK }}/heimdall-config.toml packaging/deb/heimdall-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/var/lib/heimdall/config/heimdall-config.toml
        env:
          ARCH: all
          NODE: sentry
          NETWORK: amoy
      - name: Copying config for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: cp -rp packaging/templates/config/${{ env.NETWORK }}/config.toml packaging/deb/heimdall-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/var/lib/heimdall/config/config.toml
        env:
          ARCH: all
          NODE: sentry
          NETWORK: amoy
      - name: Copying systemd file for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: |
          cp -rp packaging/templates/systemd/heimdalld-sentry.service packaging/deb/heimdall-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/lib/systemd/system/heimdalld.service
          sed -i "s/{{NETWORK}}/${{ env.NETWORK }}/g" packaging/deb/heimdall-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/lib/systemd/system/heimdalld.service
        env:
          ARCH: all
          NODE: sentry
          NETWORK: amoy
      - name: Building heimdalld for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }} profile
        run: dpkg-deb --build --root-owner-group packaging/deb/heimdall-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}
        env:
          ARCH: all
          NODE: sentry
          NETWORK: amoy

### Validator Profiles ###
      - name: Setting heimdalld for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: cp -rp packaging/deb/heimdalld packaging/deb/heimdall-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}
        env:
          ARCH: all
          NODE: validator
          NETWORK: amoy
      - name: Copying over the postinst/postrm file for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: |
          cp -rp packaging/templates/package_scripts/postinst.profile packaging/deb/heimdall-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/DEBIAN//postinst
          cp -rp packaging/templates/package_scripts/postrm.profile packaging/deb/heimdall-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/DEBIAN/postrm
        env:
          ARCH: all
          NODE: validator
          NETWORK: amoy
      - name: Copying preinst for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: |
          cp -rp packaging/templates/package_scripts/preinst packaging/deb/heimdall-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/DEBIAN/preinst
          sed -i "s/{{NETWORK}}/${{ env.NETWORK }}/g" packaging/deb/heimdall-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/DEBIAN/preinst
        env:
          ARCH: all
          NODE: validator
          NETWORK: amoy
      - name: Copying heimdall-config for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: cp -rp packaging/templates/config/${{ env.NETWORK }}/heimdall-config.toml packaging/deb/heimdall-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/var/lib/heimdall/config/heimdall-config.toml
        env:
          ARCH: all
          NODE: validator
          NETWORK: amoy
      - name: Copying config for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: cp -rp packaging/templates/config/${{ env.NETWORK }}/config.toml packaging/deb/heimdall-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/var/lib/heimdall/config/config.toml
        env:
          ARCH: all
          NODE: validator
          NETWORK: amoy
      - name: Copying systemd file for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: |
          cp -rp packaging/templates/systemd/heimdalld-validator.service packaging/deb/heimdall-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/lib/systemd/system/heimdalld.service
          sed -i "s/{{NETWORK}}/${{ env.NETWORK }}/g" packaging/deb/heimdall-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/lib/systemd/system/heimdalld.service
        env:
          ARCH: all
          NODE: validator
          NETWORK: amoy
      - name: Building heimdalld for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }} profile
        run: dpkg-deb --build --root-owner-group packaging/deb/heimdall-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}
        env:
          ARCH: all
          NODE: validator
          NETWORK: amoy

      # Shasum
      - name: shasum the heimdall debian profile package
        run: cd packaging/deb/ && sha256sum heimdall-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}.deb > heimdall-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}.deb.checksum
        env:
          ARCH: all
          NODE: sentry
          NETWORK: amoy

      - name: shasum the heimdall debian profile package
        run: cd packaging/deb/ && sha256sum heimdall-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}.deb > heimdall-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}.deb.checksum
        env:
          ARCH: all
          NODE: validator
          NETWORK: amoy

      ############ Check and Upload ##########################
      - name: Confirming package built
        run: ls -ltr packaging/deb/ | grep heimdall

      - name: Release heimdall Packages
        uses: softprops/action-gh-release@v2
        with:
          tag_name: ${{ env.GIT_TAG }}
          prerelease: true
          files: |
            packaging/deb/heimdall-amoy-**.deb
            packaging/deb/heimdall-amoy-**.deb.checksum
