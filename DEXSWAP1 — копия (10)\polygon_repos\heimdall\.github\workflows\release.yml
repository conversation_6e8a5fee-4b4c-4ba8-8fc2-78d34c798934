name: Release

on:
  push:
    branches-ignore:
      - '**'
    tags:
      - 'v*.*.*'
      # to be used by fork patch-releases ^^
      - 'v*.*.*-*'

jobs:
  goreleaser:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: 1.23.x

      - name: Prepare
        id: prepare
        run: |
            TAG=${GITHUB_REF#refs/tags/}
            echo ::set-output name=tag_name::${TAG}

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v1

      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB }}
          password: ${{ secrets.DOCKERHUB_KEY }}

      - name: Run GoReleaser
        run: make release
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          VERSION: ${{ steps.prepare.outputs.tag_name }}
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          DOCKER_USERNAME: ${{ secrets.DOCKERHUB }}
          DOCKER_PASSWORD: ${{ secrets.DOCKERHUB_KEY }}
