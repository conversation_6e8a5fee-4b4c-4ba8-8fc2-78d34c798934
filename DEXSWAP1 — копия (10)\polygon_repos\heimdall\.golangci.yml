# This file configures github.com/golangci/golangci-lint.

run:
  go: '1.22'
  timeout: 20m
  tests: true

linters:
  enable:
    - goconst
    - goimports
    - misspell
    - unconvert
    - bodyclose
    - containedctx
    - contextcheck
    - decorder
    - durationcheck
    - errchk<PERSON>son
    - errname
    # - exhaustive
    # - gocognit
    - gofmt
    # - gomnd
    #- gomoddirectives
    - gosec
    - makezero
    #- nestif
    - nilerr
    - nilnil
    - noctx
    #- nosprintfhostport # TODO: do we use IPv6?
    - paralleltest
    - prealloc
    - predeclared
    #- promlinter
    #- revive
    # - tagliatelle
    - tenv
    - thelper
    - tparallel
    - unconvert
    - unparam
    #- errorlint causes stack overflow. TODO: recheck after each golangci update

linters-settings:
  gofmt:
    simplify: true
    auto-fix: false

  goconst:
    min-len: 3 # minimum length of string constant
    min-occurrences: 2 # minimum number of occurrences
    numbers: true

  nestif:
    min-complexity: 5

  prealloc:
    for-loops: true

  gocritic:
    # Which checks should be enabled; can't be combined with 'disabled-checks';
    # See https://go-critic.github.io/overview#checks-overview
    # To check which checks are enabled run `GL_DEBUG=gocritic ./build/bin/golangci-lint run`
    # By default list of stable checks is used.
    enabled-checks:
      - badLock
      - filepathJoin
      - sortSlice
      - sprintfQuotedString
      - syncMapLoadAndDelete
      - weakCond
      - boolExprSimplify
      - httpNoBody
      - ioutilDeprecated
      - nestingReduce
      - preferFilepathJoin
      - redundantSprint
      - stringConcatSimplify
      - timeExprSimplify
      - typeAssertChain
      - yodaStyleExpr
      - truncateCmp
      - equalFold
      - preferDecodeRune
      - preferFprint
      - preferStringWriter
      - preferWriteByte
      - sliceClear
      #- ruleguard

    # Which checks should be disabled; can't be combined with 'enabled-checks'; default is empty
    disabled-checks:
      - regexpMust
      - exitAfterDefer
      - dupBranchBody
      - singleCaseSwitch
      - unlambda
      - captLocal
      - commentFormatting
      - ifElseChain
      - importShadow
      - builtinShadow

    # Enable multiple checks by tags, run `GL_DEBUG=gocritic golangci-lint run` to see all tags and checks.
    # Empty list by default. See https://github.com/go-critic/go-critic#usage -> section "Tags".
    enabled-tags:
      - performance
      - diagnostic
      - opinionated
      - style
    disabled-tags:
      - experimental
  govet:
    disable:
      - deepequalerrors
      - fieldalignment
      - shadow
      - unsafeptr
    enable-all: true
    settings:
      printf:
        # Run `go tool vet help printf` to see available settings for `printf` analyzer.
        funcs:
          - (github.com/ethereum/go-ethereum/log.Logger).Trace
          - (github.com/ethereum/go-ethereum/log.Logger).Debug
          - (github.com/ethereum/go-ethereum/log.Logger).Info
          - (github.com/ethereum/go-ethereum/log.Logger).Warn
          - (github.com/ethereum/go-ethereum/log.Logger).Error
          - (github.com/ethereum/go-ethereum/log.Logger).Crit

issues:
  exclude-dirs:
    - slashing
    - gov
    - params
  exclude-files:
    - gen_.*.go
    - .*_gen.go
  # default is true. Enables skipping of directories:
  #   vendor$, third_party$, testdata$, examples$, Godeps$, builtin$
  exclude-dirs-use-default: true
  exclude-rules:
    # Exclude some linters from running on tests files.
    - path: test\.go
      linters:
        - gosec
        - gocritic
    - path: checkpoint/simulation/header.go
      linters:
        - gosec
    - path: simulation/simulate.go
      linters:
        - prealloc
  max-issues-per-linter: 0
  max-same-issues: 0
  new-from-rev: origin/develop
