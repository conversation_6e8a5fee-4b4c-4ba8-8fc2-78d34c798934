package rest

import (
	"github.com/cosmos/cosmos-sdk/client/context"
	"github.com/gorilla/mux"
)

// RegisterRoutes - Central function to define routes that get registered by the main application
// RegisterRoutes registers the auth module REST routes.
func RegisterRoutes(cliCtx context.CLIContext, r *mux.Router) {
	r.<PERSON>le<PERSON>unc("/auth/accounts/{address}", QueryAccountRequestHandlerFn(cliCtx)).Methods("GET")
	r.<PERSON>le<PERSON>("/auth/accounts/{address}/sequence", QueryAccountSequenceRequestHandlerFn(cliCtx)).Methods("GET")
	r.<PERSON>("/auth/params", paramsHandlerFn(cliCtx)).Methods("GET")
}
