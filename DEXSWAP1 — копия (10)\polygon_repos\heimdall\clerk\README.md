# Clerk Module

## Table of Contents

* [Preliminary terminology](#preliminary-terminology)
* [Overview](#overview)
* [State-Sync Mechanism](#state-sync-mechanism)
* [How does it work](#how-does-it-work)
* [How to add an event](#how-to-add-an-event)
* [Query commands](#query-commands)

## Preliminary terminology

* A `StateSender` is a contract deployed on L1 (Ethereum) responsible for emitting state-sync events.
* A `StateReceiver` is a contract deployed on L2 (Bor) responsible for receiving state-sync events.
* A `EventRecord` is a record of the state-sync event stored in the heimdall state.

## Overview

Clerk manages generic event records from Ethereum blockchain related to state-sync events, These are specially designed events that are emitted by the StateSender contract on the L1 chain to notify the L2 nodes (<PERSON><PERSON> in case of PoS) about the state changes in the L1. Once the events are processed by the bridge, <PERSON> listens to these events and stores them in the database for further processing.

## State-Sync Mechanism

It's a mechanism for state-management between Ethereum and Bor chain, The events generated are called state-sync events. This is a way to move data from the L1 chain to L2 chain.

![State-Sync Mechanism](state-sync-mechanism.png)

## How does it work

An `EventRecord` is defined by the data structure :

```
type EventRecord struct {
	ID         uint64                `json:"id" yaml:"id"`
	Contract   types.HeimdallAddress `json:"contract" yaml:"contract"`
	Data       types.HexBytes        `json:"data" yaml:"data"`
	TxHash     types.HeimdallHash    `json:"tx_hash" yaml:"tx_hash"`
	LogIndex   uint64                `json:"log_index" yaml:"log_index"`
	ChainID    string                `json:"bor_chain_id" yaml:"bor_chain_id"`
	RecordTime time.Time             `json:"record_time" yaml:"record_time"`
}
```

* `ID` is the unique identifier for the event record, Generated by the `StateSender` contract.
* `Contract` is the address of the contract on the L2 chain on which the event will be processed.
* `Data` is the data of the event which will be processed by the contract.
* `TxHash` is the transaction hash of the event on the L1 chain.
* `LogIndex` is the log index of the event on the L1 chain.
* `ChainID` is the chain id of the bor chain.
* `RecordTime` is the time at which the event was recorded in heimdall state.

The bridge will listen to the state-sync events from L1 and generate a txn with `MsgEventRecord` which is responsible for validating events from `StateSender` contract and storing the `EventRecord` on the heimdall state for bor to use.

```
type MsgEventRecord struct {
	From            types.HeimdallAddress `json:"from"`
	TxHash          types.HeimdallHash    `json:"tx_hash"`
	LogIndex        uint64                `json:"log_index"`
	BlockNumber     uint64                `json:"block_number"`
	ContractAddress types.HeimdallAddress `json:"contract_address"`
	Data            types.HexBytes        `json:"data"`
	ID              uint64                `json:"id"`
	ChainID         string                `json:"bor_chain_id"`
}
```

[Handler](handler.go) for this transaction validates for multiple conditions including `TxHash` and `LogIndex` to ensure that the event exists on L1 and the data is not tampered with, It throws `Older invalid tx found` error if the event is already processed.

Once the event is validated by the Handler, It will go to `SideHandleMsgEventRecord` in each validator node and after verifying the event, The validators will vote with either a `YES` return an error for a failed verification.

Only when there is a majority of `YES` votes, The event will be processed by `PostHandleMsgEventRecord` which will persist the event in the state via keeper.

## How to add an event

A validator can leverage the CLI to add an event to the state in case it's missing and not processed by the bridge, The CLI command is :

```
heimdallcli tx clerk record 
    --id <event-id>
    --contract <contract-address>
    --data <event-data>
    --tx-hash <tx-hash>
    --log-index <log-index>
    --block-number <L1-block-number>
    --bor-chain-id <bor-chain-id>
    --chain-id <heimdall-chain-id>
```

## Query commands

One can run the following query commands from the clerk module :

* `record` - Query for a specific event record.
* `list` - Query a list of event records.
* `isoldtx` - Query if the event record is already processed.


### CLI commands

```
heimdallcli query clerk record --id <event-id>
```

```
heimdallcli query clerk is-old-tx --tx-hash <tx-hash> --log-index <log-index>
```

### REST endpoints

```
curl -X GET "localhost:1317/clerk/event-record/<event-id>"
```

```
curl -X GET "localhost:1317/clerk/event-record/list?from-id=<from-id>&to-time=<time-in-unix>&limit=<limit>"
```

```
curl -X GET "localhost:1317/clerk/isoldtx?tx-hash=<tx-hash>&log-index=<log-index>"
```
