[{"constant": true, "inputs": [], "name": "SYSTEM_ADDRESS", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "lastStateId", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "syncTime", "type": "uint256"}, {"internalType": "bytes", "name": "recordBytes", "type": "bytes"}], "name": "commitState", "outputs": [{"internalType": "bool", "name": "success", "type": "bool"}], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "onStateReceive", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}]