#!/bin/bash
# This is a postinstallation script so the service can be configured and started when requested
#
#sudo systemctl daemon-reload
export HEIMDALL_DIR=/var/lib/heimdall
sudo adduser --disabled-password --disabled-login --shell /usr/sbin/nologin --quiet --system --no-create-home --home /nonexistent heimdall
if [ -d "$HEIMDALL_DIR" ]
then
    echo "Directory $HEIMDALL_DIR exists."
    sudo chown -R heimdall $HEIMDALL_DIR
else
    mkdir -p $HEIMDALL_DIR
    sudo chown -R heimdall $HEIMDALL_DIR
fi
