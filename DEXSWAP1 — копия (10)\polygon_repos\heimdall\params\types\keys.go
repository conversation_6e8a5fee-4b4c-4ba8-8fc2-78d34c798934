package types

const (
	// ModuleName defines the name of the module
	ModuleName = "params"

	// StoreKey is the store key string for bor
	StoreKey = ModuleName

	// RouterKey is the message route for bor
	RouterKey = ModuleName

	// QuerierRoute is the querier route for bor
	QuerierRoute = ModuleName

	// DefaultParamspace default name for parameter store
	DefaultParamspace = ModuleName

	// TStoreKey is the string store key for the param transient store
	TStoreKey = "transient_params"
)
