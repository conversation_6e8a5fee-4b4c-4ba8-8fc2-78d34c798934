

swagger: "2.0"
host: localhost:1317
info:
  title: Heimdall APIs
  description: Package classification HiemdallRest API
  version: 0.0.1
schemes:
- http


basePath: /
consumes:
- application/json
definitions:
  BaseReq:
    properties:
      address:
        description: Address of the sender
        type: string
        x-go-name: From
      chain_id:
        description: Chain ID of Heimdall
        type: string
        x-go-name: ChainID
    required:
    - address
    - chain_id
    type: object
    x-go-package: _/Users/<USER>/heimdall/topup/client/rest
  ContractAddresses:
    properties:
      BorChainId:
        type: string
      matic_token_address:
        type: string
        x-go-name: MaticChainAddress
      root_chain_address:
        type: string
        x-go-name: RootChainAddress
      slash_manager_address:
        type: string
        x-go-name: SlashManagerAddress
      staking_info_address:
        type: string
        x-go-name: StalkignInfoAddress
      staking_manager_address:
        type: string
        x-go-name: StalkingManagerAddress
      state_receiver_address:
        type: string
        x-go-name: StateReceiverAddress
      state_sender_address:
        type: string
        x-go-name: StateSenderAddress
      validator_set_address:
        type: string
        x-go-name: ValidatorSetAddress
    type: object
    x-go-package: _/Users/<USER>/heimdall/chainmanager/client/rest
  DividendAccount:
    properties:
      feeAmount:
        type: string
        x-go-name: FeeAmount
      user:
        type: string
        x-go-name: User
    type: object
    x-go-package: _/Users/<USER>/heimdall/topup/client/rest
  DividendAccountProof:
    properties:
      accountProof:
        type: string
        x-go-name: Proof
      index:
        format: uint64
        type: integer
        x-go-name: Index
      user:
        type: string
        x-go-name: User
    type: object
    x-go-package: _/Users/<USER>/heimdall/topup/client/rest
  SendReqInput:
    properties:
      base_req:
        $ref: '#/definitions/BaseReq'
      bor_chain_id:
        type: string
        x-go-name: BorChainID
      span_id:
        format: uint64
        type: integer
        x-go-name: ID
      start_block:
        format: uint64
        type: integer
        x-go-name: StartBlock
    required:
    - base_req
    - amount
    - span_id
    - start_block
    - bor_chain_id
    type: object
    x-go-package: _/Users/<USER>/heimdall/bor/client/rest
  StdTx:
    properties:
      memo:
        type: string
        x-go-name: Memo
      msg:
        type: object
        x-go-name: Msg
      signature:
        type: string
        x-go-name: Signature
    type: object
    x-go-package: _/Users/<USER>/heimdall/client/tx
  TallyResult:
    properties:
      abstain:
        type: string
        x-go-name: Abstain
      "no":
        type: string
        x-go-name: "No"
      no_with_veto:
        type: string
        x-go-name: NoWithVeto
      "yes":
        type: string
        x-go-name: "Yes"
    type: object
    x-go-package: _/Users/<USER>/heimdall/gov/client/rest
  ValidatorSlashingInfo:
    properties:
      ID:
        format: int64
        type: integer
      IsJailed:
        type: boolean
      SlashedAmount:
        format: int64
        type: integer
    type: object
    x-go-package: _/Users/<USER>/heimdall/slashing/client/rest
  authAccount:
    properties:
      type:
        type: string
        x-go-name: Type
      value:
        $ref: '#/definitions/value'
    type: object
    x-go-package: _/Users/<USER>/heimdall/auth/client/rest
  authAccountSequence:
    properties:
      account_number:
        type: string
        x-go-name: AccountNumber
      address:
        type: string
        x-go-name: Address
      sequence:
        type: string
        x-go-name: Sequence
    type: object
    x-go-package: _/Users/<USER>/heimdall/auth/client/rest
  authAccountSequenceStructure:
    properties:
      height:
        type: string
        x-go-name: Height
      result:
        $ref: '#/definitions/authAccountSequence'
    type: object
    x-go-package: _/Users/<USER>/heimdall/auth/client/rest
  authAccountStructure:
    properties:
      height:
        type: string
        x-go-name: Height
      result:
        $ref: '#/definitions/authAccount'
    type: object
    x-go-package: _/Users/<USER>/heimdall/auth/client/rest
  authParams:
    properties:
      max_memo_characters:
        type: string
        x-go-name: MaxMemoCharacters
      max_tx_gas:
        format: int64
        type: integer
        x-go-name: MaxTxGas
      sig_verify_cost_ed25519:
        format: int64
        type: integer
        x-go-name: SigVerifyCostEd2219
      sig_verify_cost_secp256k1:
        format: int64
        type: integer
        x-go-name: SigVerifyCostSecp256k1
      tx_fees:
        format: int64
        type: integer
        x-go-name: TxFees
      tx_sig_limit:
        format: int64
        type: integer
        x-go-name: TxSigLimit
      tx_size_cost_per_byte:
        format: int64
        type: integer
        x-go-name: TxSizeCostPerByte
    type: object
    x-go-package: _/Users/<USER>/heimdall/auth/client/rest
  authParamsStructure:
    properties:
      height:
        type: string
        x-go-name: Height
      result:
        $ref: '#/definitions/authParams'
    type: object
    x-go-package: _/Users/<USER>/heimdall/auth/client/rest
  bankBalance:
    properties:
      amount:
        description: Amount of token in the bank
        type: string
        x-go-name: Amount
      denom:
        description: Denomination of the token
        type: string
        x-go-name: Denom
    type: object
    x-go-package: _/Users/<USER>/heimdall/bank/client/rest
  bankBalanceByAddress:
    properties:
      height:
        type: string
        x-go-name: Height
      result:
        items:
          $ref: '#/definitions/bankBalance'
        type: array
        x-go-name: Result
    type: object
    x-go-package: _/Users/<USER>/heimdall/bank/client/rest
  borSpan:
    properties:
      height:
        type: string
        x-go-name: Height
      result:
        $ref: '#/definitions/span'
    type: object
    x-go-package: _/Users/<USER>/heimdall/bor/client/rest
  borSpanList:
    properties:
      height:
        type: string
        x-go-name: Height
      result:
        items:
          $ref: '#/definitions/span'
        type: array
        x-go-name: Result
    type: object
    x-go-package: _/Users/<USER>/heimdall/bor/client/rest
  borSpanParams:
    properties:
      height:
        type: string
        x-go-name: Height
      result:
        $ref: '#/definitions/spanParams'
    type: object
    x-go-package: _/Users/<USER>/heimdall/bor/client/rest
  chainManager:
    properties:
      chain_params:
        $ref: '#/definitions/ContractAddresses'
      mainchain_tx_confirmations:
        format: int64
        type: integer
        x-go-name: MainChainConfirmation
      maticchain_tx_confirmations:
        format: int64
        type: integer
        x-go-name: MaticChainConfirmation
    type: object
    x-go-package: _/Users/<USER>/heimdall/chainmanager/client/rest
  chainManagerParams:
    properties:
      height:
        type: string
        x-go-name: Height
      result:
        $ref: '#/definitions/chainManager'
    type: object
    x-go-package: _/Users/<USER>/heimdall/chainmanager/client/rest
  checkpoint:
    properties:
      bor_chain_id:
        type: string
        x-go-name: BorChainId
      end_block:
        format: int64
        type: integer
        x-go-name: EndBlock
      proposer:
        type: string
        x-go-name: Proposer
      root_hash:
        type: string
        x-go-name: RootHash
      start_block:
        format: int64
        type: integer
        x-go-name: StartBlock
      timestamp:
        format: int64
        type: integer
        x-go-name: Timestamp
    type: object
    x-go-package: _/Users/<USER>/heimdall/checkpoint/client/rest
  checkpointAck:
    properties:
      type:
        type: string
        x-go-name: Type
      value:
        $ref: '#/definitions/checkpointAckValue'
    type: object
    x-go-package: _/Users/<USER>/heimdall/checkpoint/client/rest
  checkpointAckInput:
    properties:
      base_req:
        $ref: '#/definitions/BaseReq'
      end_block:
        type: string
        x-go-name: EndBlock
      from:
        type: string
        x-go-name: From
      header_block:
        type: string
        x-go-name: HeaderBlock
      log_index:
        type: string
        x-go-name: LogIndex
      proposer:
        type: string
        x-go-name: Proposer
      root_Hash:
        type: string
        x-go-name: RootHash
      start_block:
        type: string
        x-go-name: StartBlock
      tx_hash:
        type: string
        x-go-name: TxHash
    required:
    - base_req
    type: object
    x-go-package: _/Users/<USER>/heimdall/checkpoint/client/rest
  checkpointAckMsg:
    properties:
      type:
        type: string
        x-go-name: Type
      value:
        $ref: '#/definitions/checkpointAckVal'
    type: object
    x-go-package: _/Users/<USER>/heimdall/checkpoint/client/rest
  checkpointAckVal:
    properties:
      end_block:
        type: string
        x-go-name: EndBlock
      from:
        type: string
        x-go-name: From
      log_index:
        type: string
        x-go-name: LogIndex
      number:
        type: string
        x-go-name: Number
      proposer:
        type: string
        x-go-name: Proposer
      root_Hash:
        type: string
        x-go-name: RootHash
      start_block:
        type: string
        x-go-name: StartBlock
      tx_hash:
        type: string
        x-go-name: TxHash
    type: object
    x-go-package: _/Users/<USER>/heimdall/checkpoint/client/rest
  checkpointAckValue:
    properties:
      memo:
        type: string
        x-go-name: Memo
      msg:
        $ref: '#/definitions/checkpointAckMsg'
      signature:
        type: string
        x-go-name: Signature
    type: object
    x-go-package: _/Users/<USER>/heimdall/checkpoint/client/rest
  checkpointCount:
    properties:
      result:
        format: int64
        type: integer
        x-go-name: Result
    type: object
    x-go-package: _/Users/<USER>/heimdall/checkpoint/client/rest
  checkpointCountResponseStructure:
    properties:
      height:
        type: string
        x-go-name: Height
      result:
        $ref: '#/definitions/checkpointCount'
    type: object
    x-go-package: _/Users/<USER>/heimdall/checkpoint/client/rest
  checkpointListStructure:
    properties:
      height:
        type: string
        x-go-name: Height
      result:
        items:
          $ref: '#/definitions/checkpoint'
        type: array
        x-go-name: Result
    type: object
    x-go-package: _/Users/<USER>/heimdall/checkpoint/client/rest
  checkpointNew:
    properties:
      type:
        type: string
        x-go-name: Type
      value:
        $ref: '#/definitions/checkpointNewValue'
    type: object
    x-go-package: _/Users/<USER>/heimdall/checkpoint/client/rest
  checkpointNewInput:
    properties:
      account_root_hash:
        type: string
        x-go-name: AccountRootHash
      base_req:
        $ref: '#/definitions/BaseReq'
      bor_chain_id:
        type: string
        x-go-name: BorChainID
      end_block:
        type: string
        x-go-name: EndBlock
      proposer:
        type: string
        x-go-name: Proposer
      root_Hash:
        type: string
        x-go-name: RootHash
      start_block:
        type: string
        x-go-name: StartBlock
    type: object
    x-go-package: _/Users/<USER>/heimdall/checkpoint/client/rest
  checkpointNewMsg:
    properties:
      type:
        type: string
        x-go-name: Type
      value:
        $ref: '#/definitions/checkpointNewVal'
    type: object
    x-go-package: _/Users/<USER>/heimdall/checkpoint/client/rest
  checkpointNewVal:
    properties:
      account_root_hash:
        type: string
        x-go-name: AccountRootHash
      bor_chain_id:
        type: string
        x-go-name: BorChainId
      end_block:
        type: string
        x-go-name: EndBlock
      proposer:
        type: string
        x-go-name: Proposer
      root_hash:
        type: string
        x-go-name: RootHash
      start_block:
        type: string
        x-go-name: StartBlock
    type: object
    x-go-package: _/Users/<USER>/heimdall/checkpoint/client/rest
  checkpointNewValue:
    properties:
      memo:
        type: string
        x-go-name: Memo
      msg:
        $ref: '#/definitions/checkpointNewMsg'
      signature:
        type: string
        x-go-name: Signature
    type: object
    x-go-package: _/Users/<USER>/heimdall/checkpoint/client/rest
  checkpointNoAck:
    properties:
      type:
        type: string
        x-go-name: Type
      value:
        $ref: '#/definitions/checkpointNoAckValue'
    type: object
    x-go-package: _/Users/<USER>/heimdall/checkpoint/client/rest
  checkpointNoAckMsg:
    properties:
      type:
        type: string
        x-go-name: Type
      value:
        $ref: '#/definitions/checkpointNoAckVal'
    type: object
    x-go-package: _/Users/<USER>/heimdall/checkpoint/client/rest
  checkpointNoAckVal:
    properties:
      from:
        type: string
        x-go-name: From
    type: object
    x-go-package: _/Users/<USER>/heimdall/checkpoint/client/rest
  checkpointNoAckValue:
    properties:
      memo:
        type: string
        x-go-name: Memo
      msg:
        $ref: '#/definitions/checkpointNoAckMsg'
      signature:
        type: string
        x-go-name: Signature
    type: object
    x-go-package: _/Users/<USER>/heimdall/checkpoint/client/rest
  checkpointParams:
    properties:
      height:
        type: string
        x-go-name: Height
      result:
        $ref: '#/definitions/params'
    type: object
    x-go-package: _/Users/<USER>/heimdall/checkpoint/client/rest
  checkpointStructure:
    properties:
      height:
        type: string
        x-go-name: Height
      result:
        $ref: '#/definitions/checkpoint'
    type: object
    x-go-package: _/Users/<USER>/heimdall/checkpoint/client/rest
  clerkEventById:
    properties:
      height:
        type: string
        x-go-name: Height
      result:
        $ref: '#/definitions/event'
    type: object
    x-go-package: _/Users/<USER>/heimdall/clerk/client/rest
  clerkEventList:
    properties:
      height:
        type: string
        x-go-name: Height
      result:
        items:
          $ref: '#/definitions/event'
        type: array
        x-go-name: Result
    type: object
    x-go-package: _/Users/<USER>/heimdall/clerk/client/rest
  clerkNewEvent:
    properties:
      type:
        type: string
        x-go-name: Type
      value:
        $ref: '#/definitions/clerkNewEventValue'
    type: object
    x-go-package: _/Users/<USER>/heimdall/clerk/client/rest
  clerkNewEventInput:
    properties:
      base_req:
        $ref: '#/definitions/BaseReq'
      block_number:
        type: string
        x-go-name: BlockNumber
      bor_chain_id:
        type: string
        x-go-name: BorChainID
      contract_address:
        type: string
        x-go-name: ContractAddress
      data:
        type: string
        x-go-name: Data
      id:
        type: string
        x-go-name: ID
      log_index:
        type: string
        x-go-name: LogIndex
      tx_hash:
        type: string
        x-go-name: TxHash
    type: object
    x-go-package: _/Users/<USER>/heimdall/clerk/client/rest
  clerkNewEventMsg:
    properties:
      type:
        type: string
        x-go-name: Type
      value:
        $ref: '#/definitions/clerkNewEventVal'
    type: object
    x-go-package: _/Users/<USER>/heimdall/clerk/client/rest
  clerkNewEventVal:
    properties:
      block_number:
        type: string
        x-go-name: BlockNumber
      bor_chain_id:
        type: string
        x-go-name: BorChainID
      contract_address:
        type: string
        x-go-name: ContractAddress
      data:
        type: string
        x-go-name: Data
      from:
        type: string
        x-go-name: From
      id:
        type: string
        x-go-name: ID
      log_index:
        type: string
        x-go-name: LogIndex
      tx_hash:
        type: string
        x-go-name: TxHash
    type: object
    x-go-package: _/Users/<USER>/heimdall/clerk/client/rest
  clerkNewEventValue:
    properties:
      memo:
        type: string
        x-go-name: Memo
      msg:
        $ref: '#/definitions/clerkNewEventMsg'
      signature:
        type: string
        x-go-name: Signature
    type: object
    x-go-package: _/Users/<USER>/heimdall/clerk/client/rest
  coin:
    properties:
      amount:
        type: string
        x-go-name: Amount
      denom:
        type: string
        x-go-name: Denom
    type: object
    x-go-package: _/Users/<USER>/heimdall/gov/client/rest
  content:
    properties:
      type:
        type: string
        x-go-name: Type
      value:
        $ref: '#/definitions/value'
    type: object
    x-go-package: _/Users/<USER>/heimdall/gov/client/rest
  deposit:
    properties:
      amount:
        type: string
        x-go-name: Account
      denom:
        type: string
        x-go-name: Denom
    type: object
    x-go-package: _/Users/<USER>/heimdall/gov/client/rest
  deposited:
    properties:
      amount:
        items:
          $ref: '#/definitions/deposit'
        type: array
        x-go-name: Amount
      depositor:
        type: string
        x-go-name: Depositor
      proposal_id:
        format: int64
        type: integer
        x-go-name: ProposalId
    type: object
    x-go-package: _/Users/<USER>/heimdall/gov/client/rest
  event:
    properties:
      bor_chain_id:
        type: string
        x-go-name: BorChainId
      contract:
        type: string
        x-go-name: Contract
      data:
        type: string
        x-go-name: Data
      id:
        format: int64
        type: integer
        x-go-name: Id
      log_index:
        format: int64
        type: integer
        x-go-name: LogIndex
      record_time:
        type: string
        x-go-name: RecoedTime
      tx_hash:
        type: string
        x-go-name: TxHash
    type: object
    x-go-package: _/Users/<USER>/heimdall/clerk/client/rest
  govDepositStructure:
    properties:
      height:
        type: string
        x-go-name: Height
      result:
        $ref: '#/definitions/deposited'
    type: object
    x-go-package: _/Users/<USER>/heimdall/gov/client/rest
  govDepositsStructure:
    properties:
      height:
        type: string
        x-go-name: Height
      result:
        items:
          $ref: '#/definitions/deposited'
        type: array
        x-go-name: Result
    type: object
    x-go-package: _/Users/<USER>/heimdall/gov/client/rest
  govParameterDeposit:
    properties:
      max_deposit_period:
        type: string
        x-go-name: MaxDepositPeriod
      min_deposit:
        $ref: '#/definitions/deposit'
    type: object
    x-go-package: _/Users/<USER>/heimdall/gov/client/rest
  govParameterDepositStructure:
    properties:
      height:
        type: string
        x-go-name: Height
      result:
        $ref: '#/definitions/govParameterDeposit'
    type: object
    x-go-package: _/Users/<USER>/heimdall/gov/client/rest
  govParameterTallying:
    properties:
      quorum:
        type: string
        x-go-name: Quorum
      threshold:
        type: string
        x-go-name: Threshold
      veto:
        type: string
        x-go-name: Veto
    type: object
    x-go-package: _/Users/<USER>/heimdall/gov/client/rest
  govParameterTallyingStructure:
    properties:
      height:
        type: string
        x-go-name: Height
      result:
        $ref: '#/definitions/govParameterTallying'
    type: object
    x-go-package: _/Users/<USER>/heimdall/gov/client/rest
  govParameterVoting:
    properties:
      voting_period:
        type: string
        x-go-name: VotingPeriod
    type: object
    x-go-package: _/Users/<USER>/heimdall/gov/client/rest
  govParameterVotingStructure:
    properties:
      height:
        type: string
        x-go-name: Height
      result:
        $ref: '#/definitions/govParameterVoting'
    type: object
    x-go-package: _/Users/<USER>/heimdall/gov/client/rest
  govProposalsDepositsInput:
    properties:
      amount:
        items:
          $ref: '#/definitions/coin'
        type: array
        x-go-name: Amount
      base_req:
        $ref: '#/definitions/BaseReq'
      depositor:
        type: string
        x-go-name: Depositor
      validator:
        format: int64
        type: integer
        x-go-name: Validator
    type: object
    x-go-package: _/Users/<USER>/heimdall/gov/client/rest
  govProposalsInput:
    properties:
      base_req:
        $ref: '#/definitions/BaseReq'
      description:
        type: string
        x-go-name: Description
      initial_deposit:
        items:
          $ref: '#/definitions/coin'
        type: array
        x-go-name: InitialDeposit
      proposal_type:
        type: string
        x-go-name: ProposalType
      proposer:
        type: string
        x-go-name: Proposer
      title:
        type: string
        x-go-name: Title
      validator:
        type: string
        x-go-name: Validator
    type: object
    x-go-package: _/Users/<USER>/heimdall/gov/client/rest
  govProposalsStructure:
    properties:
      height:
        type: string
        x-go-name: Height
      result:
        items:
          $ref: '#/definitions/proposal'
        type: array
        x-go-name: Result
    type: object
    x-go-package: _/Users/<USER>/heimdall/gov/client/rest
  govProposalsVotesInput:
    properties:
      base_req:
        $ref: '#/definitions/BaseReq'
      option:
        type: string
        x-go-name: Option
      validator:
        format: int64
        type: integer
        x-go-name: Validator
      voter:
        type: string
        x-go-name: Voter
    type: object
    x-go-package: _/Users/<USER>/heimdall/gov/client/rest
  govProposerStructure:
    properties:
      height:
        type: string
        x-go-name: Height
      result:
        $ref: '#/definitions/proposer'
    type: object
    x-go-package: _/Users/<USER>/heimdall/gov/client/rest
  govVoteStructure:
    properties:
      height:
        type: string
        x-go-name: Height
      result:
        $ref: '#/definitions/vote'
    type: object
    x-go-package: _/Users/<USER>/heimdall/gov/client/rest
  govVotesStructure:
    properties:
      height:
        type: string
        x-go-name: Height
      result:
        items:
          $ref: '#/definitions/vote'
        type: array
        x-go-name: Result
    type: object
    x-go-package: _/Users/<USER>/heimdall/gov/client/rest
  isOldTx:
    properties:
      height:
        type: string
        x-go-name: Height
      result:
        type: boolean
        x-go-name: Result
    type: object
    x-go-package: _/Users/<USER>/heimdall/topup/client/rest
  lastNoAck:
    properties:
      result:
        format: int64
        type: integer
        x-go-name: Result
    type: object
    x-go-package: _/Users/<USER>/heimdall/checkpoint/client/rest
  lastNoAckResponseStructure:
    properties:
      height:
        type: string
        x-go-name: Height
      result:
        $ref: '#/definitions/lastNoAck'
    type: object
    x-go-package: _/Users/<USER>/heimdall/checkpoint/client/rest
  msg:
    properties:
      type:
        type: string
        x-go-name: Type
      value:
        $ref: '#/definitions/val'
    type: object
    x-go-package: _/Users/<USER>/heimdall/bor/client/rest
  output:
    properties:
      type:
        type: string
        x-go-name: Type
      value:
        $ref: '#/definitions/value'
    type: object
    x-go-package: _/Users/<USER>/heimdall/bor/client/rest
  overview:
    properties:
      ack_count:
        format: int64
        type: integer
        x-go-name: AckCount
      checkpoint_buffer:
        $ref: '#/definitions/checkpoint'
      last_noack_time:
        type: string
        x-go-name: LastNoAckTime
      validator_Count:
        format: int64
        type: integer
        x-go-name: ValidatorCount
      validator_set:
        $ref: '#/definitions/validatorSet'
    type: object
    x-go-package: _/Users/<USER>/heimdall/checkpoint/client/rest
  overviewResponseStructure:
    properties:
      height:
        type: string
        x-go-name: Height
      result:
        $ref: '#/definitions/overview'
    type: object
    x-go-package: _/Users/<USER>/heimdall/checkpoint/client/rest
  params:
    properties:
      downtime_jail_duration:
        format: int64
        type: integer
        x-go-name: DowntimeJailDuration
      enable_slashing:
        type: boolean
        x-go-name: EnableSlashing
      jail_fraction_limit:
        type: string
        x-go-name: JailFractionLimit
      max_evidence_age:
        type: string
        x-go-name: MaxEvidenceAge
      min_signed_per_window:
        type: string
        x-go-name: MinSignedPerWindow
      signed_blocks_window:
        format: int64
        type: integer
        x-go-name: SignedBlockWindow
      slash_fraction_double_sign:
        type: string
        x-go-name: SlashFunctionDoubleSign
      slash_fraction_downtime:
        type: string
        x-go-name: SlashFractionDowntime
      slash_fraction_limit:
        type: string
        x-go-name: SlashFractionLimit
    type: object
    x-go-package: _/Users/<USER>/heimdall/slashing/client/rest
  proposal:
    properties:
      content:
        $ref: '#/definitions/content'
      deposit_end_time:
        type: string
        x-go-name: DepositEndTime
      final_tally_result:
        $ref: '#/definitions/TallyResult'
      id:
        type: string
        x-go-name: Id
      proposal_status:
        type: string
        x-go-name: ProposalStatus
      submit_time:
        type: string
        x-go-name: SubmitTime
      total_deposit:
        $ref: '#/definitions/deposit'
      voting_end_time:
        type: string
        x-go-name: VotingEndTime
      voting_start_time:
        type: string
        x-go-name: VotingStartTime
    type: object
    x-go-package: _/Users/<USER>/heimdall/gov/client/rest
  proposer:
    properties:
      proposal_id:
        type: string
        x-go-name: ProposalId
      proposer:
        type: string
        x-go-name: Proposer
    type: object
    x-go-package: _/Users/<USER>/heimdall/gov/client/rest
  slashingCountStructure:
    properties:
      height:
        type: string
        x-go-name: Height
      result:
        format: int64
        type: integer
        x-go-name: Result
    type: object
    x-go-package: _/Users/<USER>/heimdall/slashing/client/rest
  slashingInfosBytesStructure:
    properties:
      height:
        type: string
        x-go-name: Height
      result:
        type: string
        x-go-name: Result
    type: object
    x-go-package: _/Users/<USER>/heimdall/slashing/client/rest
  slashingInfosStructure:
    properties:
      height:
        type: string
        x-go-name: Height
      result:
        items:
          $ref: '#/definitions/slashingSigningInfo'
        type: array
        x-go-name: Result
    type: object
    x-go-package: _/Users/<USER>/heimdall/slashing/client/rest
  slashingLatestInfoByIdStructure:
    properties:
      height:
        type: string
        x-go-name: Height
      result:
        $ref: '#/definitions/ValidatorSlashingInfo'
    type: object
    x-go-package: _/Users/<USER>/heimdall/slashing/client/rest
  slashingLatestInfosStructure:
    properties:
      height:
        type: string
        x-go-name: Height
      result:
        items:
          $ref: '#/definitions/ValidatorSlashingInfo'
        type: array
        x-go-name: Result
    type: object
    x-go-package: _/Users/<USER>/heimdall/slashing/client/rest
  slashingNewTickInput:
    properties:
      ID:
        format: uint64
        type: integer
      base_req:
        $ref: '#/definitions/BaseReq'
      proposer:
        type: string
        x-go-name: Proposer
      slashing_info_bytes:
        type: string
        x-go-name: SlashingInfoBytes
    type: object
    x-go-package: _/Users/<USER>/heimdall/slashing/client/rest
  slashingNewTickMsg:
    properties:
      type:
        type: string
        x-go-name: Type
      value:
        $ref: '#/definitions/slashingNewTickVal'
    type: object
    x-go-package: _/Users/<USER>/heimdall/slashing/client/rest
  slashingNewTickOutput:
    properties:
      type:
        type: string
        x-go-name: Type
      value:
        $ref: '#/definitions/slashingNewTickValue'
    type: object
    x-go-package: _/Users/<USER>/heimdall/slashing/client/rest
  slashingNewTickVal:
    properties:
      id:
        format: uint64
        type: integer
        x-go-name: ID
      proposer:
        type: string
        x-go-name: Proposer
      slashinginfobytes:
        type: string
        x-go-name: SlashingInfoBytes
    type: object
    x-go-package: _/Users/<USER>/heimdall/slashing/client/rest
  slashingNewTickValue:
    properties:
      memo:
        type: string
        x-go-name: Memo
      msg:
        $ref: '#/definitions/slashingNewTickMsg'
      signature:
        type: string
        x-go-name: Signature
    type: object
    x-go-package: _/Users/<USER>/heimdall/slashing/client/rest
  slashingParametersStructure:
    properties:
      height:
        type: string
        x-go-name: Height
      result:
        $ref: '#/definitions/params'
    type: object
    x-go-package: _/Users/<USER>/heimdall/slashing/client/rest
  slashingSigningInfo:
    properties:
      indexOffset:
        format: int64
        type: integer
        x-go-name: IndexOffset
      startHeight:
        format: int64
        type: integer
        x-go-name: StartHeight
      valID:
        format: int64
        type: integer
        x-go-name: ValID
    type: object
    x-go-package: _/Users/<USER>/heimdall/slashing/client/rest
  slashingSigningInfoByIdStructure:
    properties:
      height:
        type: string
        x-go-name: Height
      result:
        $ref: '#/definitions/slashingSigningInfo'
    type: object
    x-go-package: _/Users/<USER>/heimdall/slashing/client/rest
  slashingTickAckInput:
    properties:
      ID:
        format: uint64
        type: integer
      amount:
        format: uint64
        type: integer
        x-go-name: Amount
      base_req:
        $ref: '#/definitions/BaseReq'
      block_number:
        format: uint64
        type: integer
        x-go-name: BlockNumber
      log_index:
        format: uint64
        type: integer
        x-go-name: LogIndex
      tx_hash:
        type: string
        x-go-name: TxHash
    type: object
    x-go-package: _/Users/<USER>/heimdall/slashing/client/rest
  slashingTickAckMsg:
    properties:
      type:
        type: string
        x-go-name: Type
      value:
        $ref: '#/definitions/slashingTickAckVal'
    type: object
    x-go-package: _/Users/<USER>/heimdall/slashing/client/rest
  slashingTickAckOutput:
    properties:
      type:
        type: string
        x-go-name: Type
      value:
        $ref: '#/definitions/slashingTickAckValue'
    type: object
    x-go-package: _/Users/<USER>/heimdall/slashing/client/rest
  slashingTickAckVal:
    properties:
      block_number:
        format: uint64
        type: integer
        x-go-name: BlockNumber
      from:
        type: string
        x-go-name: From
      log_index:
        format: uint64
        type: integer
        x-go-name: LogIndex
      slashed_amount:
        format: uint64
        type: integer
        x-go-name: Amount
      tick_id:
        format: uint64
        type: integer
        x-go-name: ID
      tx_hash:
        type: string
        x-go-name: TxHash
    type: object
    x-go-package: _/Users/<USER>/heimdall/slashing/client/rest
  slashingTickAckValue:
    properties:
      memo:
        type: string
        x-go-name: Memo
      msg:
        $ref: '#/definitions/slashingTickAckMsg'
      signature:
        type: string
        x-go-name: Signature
    type: object
    x-go-package: _/Users/<USER>/heimdall/slashing/client/rest
  slashingUnjailInput:
    properties:
      ID:
        format: uint64
        type: integer
      base_req:
        $ref: '#/definitions/BaseReq'
      block_number:
        format: uint64
        type: integer
        x-go-name: BlockNumber
      log_index:
        format: uint64
        type: integer
        x-go-name: LogIndex
      tx_hash:
        type: string
        x-go-name: TxHash
    type: object
    x-go-package: _/Users/<USER>/heimdall/slashing/client/rest
  slashingUnjailMsg:
    properties:
      type:
        type: string
        x-go-name: Type
      value:
        $ref: '#/definitions/slashingUnjailVal'
    type: object
    x-go-package: _/Users/<USER>/heimdall/slashing/client/rest
  slashingUnjailOutput:
    properties:
      type:
        type: string
        x-go-name: Type
      value:
        $ref: '#/definitions/slashingUnjailValue'
    type: object
    x-go-package: _/Users/<USER>/heimdall/slashing/client/rest
  slashingUnjailVal:
    properties:
      block_number:
        format: uint64
        type: integer
        x-go-name: BlockNumber
      from:
        type: string
        x-go-name: From
      id:
        format: uint64
        type: integer
        x-go-name: ID
      log_index:
        format: uint64
        type: integer
        x-go-name: LogIndex
      tx_hash:
        type: string
        x-go-name: TxHash
    type: object
    x-go-package: _/Users/<USER>/heimdall/slashing/client/rest
  slashingUnjailValue:
    properties:
      memo:
        type: string
        x-go-name: Memo
      msg:
        $ref: '#/definitions/slashingUnjailMsg'
      signature:
        type: string
        x-go-name: Signature
    type: object
    x-go-package: _/Users/<USER>/heimdall/slashing/client/rest
  span:
    properties:
      bor_chain_id:
        type: string
        x-go-name: BorChainId
      end_block:
        format: int64
        type: integer
        x-go-name: EndBlock
      selected_producer:
        items:
          $ref: '#/definitions/validator'
        type: array
        x-go-name: SelectedProducers
      span_id:
        format: int64
        type: integer
        x-go-name: SpanID
      start_block:
        format: int64
        type: integer
        x-go-name: StartBlock
      validator_set:
        $ref: '#/definitions/validatorSet'
    type: object
    x-go-package: _/Users/<USER>/heimdall/bor/client/rest
  spanParams:
    properties:
      producer_count:
        description: type:integer
        format: int64
        type: integer
        x-go-name: ProducerCount
      span_duration:
        description: type:integer
        format: int64
        type: integer
        x-go-name: SpanDuration
      sprint_duration:
        description: type:integer
        format: int64
        type: integer
        x-go-name: SprintDuration
    type: object
    x-go-package: _/Users/<USER>/heimdall/bor/client/rest
  spanSeed:
    properties:
      height:
        type: string
        x-go-name: Height
      result:
        type: string
        x-go-name: Result
    type: object
    x-go-package: _/Users/<USER>/heimdall/bor/client/rest
  stakingNewValidatorInput:
    properties:
      ID:
        format: uint64
        type: integer
      activationEpoch:
        format: uint64
        type: integer
        x-go-name: ActivationEpoch
      amount:
        type: string
        x-go-name: Amount
      base_req:
        $ref: '#/definitions/BaseReq'
      block_number:
        format: uint64
        type: integer
        x-go-name: BlockNumber
      log_index:
        format: uint64
        type: integer
        x-go-name: LogIndex
      nonce:
        format: uint64
        type: integer
        x-go-name: Nonce
      pubKey:
        type: string
        x-go-name: SignerPubKey
      tx_hash:
        type: string
        x-go-name: TxHash
    type: object
    x-go-package: _/Users/<USER>/heimdall/staking/client/rest
  stakingNewValidatorMsg:
    properties:
      type:
        type: string
        x-go-name: Type
      value:
        $ref: '#/definitions/stakingNewValidatorVal'
    type: object
    x-go-package: _/Users/<USER>/heimdall/staking/client/rest
  stakingNewValidatorOutput:
    properties:
      type:
        type: string
        x-go-name: Type
      value:
        $ref: '#/definitions/stakingNewValidatorValue'
    type: object
    x-go-package: _/Users/<USER>/heimdall/staking/client/rest
  stakingNewValidatorVal:
    properties:
      ID:
        format: uint64
        type: integer
      activationEpoch:
        format: uint64
        type: integer
        x-go-name: ActivationEpoch
      amount:
        type: string
        x-go-name: Amount
      block_number:
        format: uint64
        type: integer
        x-go-name: BlockNumber
      from:
        type: string
        x-go-name: From
      log_index:
        format: uint64
        type: integer
        x-go-name: LogIndex
      nonce:
        format: uint64
        type: integer
        x-go-name: Nonce
      pubKey:
        type: string
        x-go-name: SignerPubKey
      tx_hash:
        type: string
        x-go-name: TxHash
    type: object
    x-go-package: _/Users/<USER>/heimdall/staking/client/rest
  stakingNewValidatorValue:
    properties:
      memo:
        type: string
        x-go-name: Memo
      msg:
        $ref: '#/definitions/stakingNewValidatorMsg'
      signature:
        type: string
        x-go-name: Signature
    type: object
    x-go-package: _/Users/<USER>/heimdall/staking/client/rest
  stakingProposerByTimeStructure:
    properties:
      height:
        type: string
        x-go-name: Height
      result:
        items:
          $ref: '#/definitions/validator'
        type: array
        x-go-name: Result
    type: object
    x-go-package: _/Users/<USER>/heimdall/staking/client/rest
  stakingTotalPower:
    properties:
      result:
        format: int64
        type: integer
        x-go-name: Result
    type: object
    x-go-package: _/Users/<USER>/heimdall/staking/client/rest
  stakingTotalPowerStructure:
    properties:
      height:
        type: string
        x-go-name: Height
      result:
        $ref: '#/definitions/stakingTotalPower'
    type: object
    x-go-package: _/Users/<USER>/heimdall/staking/client/rest
  stakingValidatorDeleteInput:
    properties:
      ID:
        format: uint64
        type: integer
      base_req:
        $ref: '#/definitions/BaseReq'
      block_number:
        format: uint64
        type: integer
        x-go-name: BlockNumber
      deactivationEpoch:
        format: uint64
        type: integer
        x-go-name: DeactivationEpoch
      log_index:
        format: uint64
        type: integer
        x-go-name: LogIndex
      nonce:
        format: uint64
        type: integer
        x-go-name: Nonce
      tx_hash:
        type: string
        x-go-name: TxHash
    type: object
    x-go-package: _/Users/<USER>/heimdall/staking/client/rest
  stakingValidatorDeleteMsg:
    properties:
      type:
        type: string
        x-go-name: Type
      value:
        $ref: '#/definitions/stakingValidatorDeleteVal'
    type: object
    x-go-package: _/Users/<USER>/heimdall/staking/client/rest
  stakingValidatorDeleteOutput:
    properties:
      type:
        type: string
        x-go-name: Type
      value:
        $ref: '#/definitions/stakingValidatorDeleteValue'
    type: object
    x-go-package: _/Users/<USER>/heimdall/staking/client/rest
  stakingValidatorDeleteVal:
    properties:
      block_number:
        format: uint64
        type: integer
        x-go-name: BlockNumber
      deactivationEpoch:
        format: uint64
        type: integer
        x-go-name: DeactivationEpoch
      from:
        type: string
        x-go-name: From
      id:
        format: uint64
        type: integer
        x-go-name: ID
      log_index:
        format: uint64
        type: integer
        x-go-name: LogIndex
      nonce:
        format: uint64
        type: integer
        x-go-name: Nonce
      tx_hash:
        type: string
        x-go-name: TxHash
    type: object
    x-go-package: _/Users/<USER>/heimdall/staking/client/rest
  stakingValidatorDeleteValue:
    properties:
      memo:
        type: string
        x-go-name: Memo
      msg:
        $ref: '#/definitions/stakingValidatorDeleteMsg'
      signature:
        type: string
        x-go-name: Signature
    type: object
    x-go-package: _/Users/<USER>/heimdall/staking/client/rest
  stakingValidatorSetStructure:
    properties:
      height:
        type: string
        x-go-name: Height
      result:
        $ref: '#/definitions/validators'
    type: object
    x-go-package: _/Users/<USER>/heimdall/staking/client/rest
  stakingValidatorStakeUpdateInput:
    properties:
      ID:
        format: uint64
        type: integer
      amount:
        type: string
        x-go-name: Amount
      base_req:
        $ref: '#/definitions/BaseReq'
      block_number:
        format: uint64
        type: integer
        x-go-name: BlockNumber
      log_index:
        format: uint64
        type: integer
        x-go-name: LogIndex
      nonce:
        format: uint64
        type: integer
        x-go-name: Nonce
      tx_hash:
        type: string
        x-go-name: TxHash
    type: object
    x-go-package: _/Users/<USER>/heimdall/staking/client/rest
  stakingValidatorStakeUpdateMsg:
    properties:
      type:
        type: string
        x-go-name: Type
      value:
        $ref: '#/definitions/stakingValidatorStakeUpdateVal'
    type: object
    x-go-package: _/Users/<USER>/heimdall/staking/client/rest
  stakingValidatorStakeUpdateOutput:
    properties:
      type:
        type: string
        x-go-name: Type
      value:
        $ref: '#/definitions/stakingValidatorStakeUpdateValue'
    type: object
    x-go-package: _/Users/<USER>/heimdall/staking/client/rest
  stakingValidatorStakeUpdateVal:
    properties:
      ID:
        format: uint64
        type: integer
      amount:
        type: string
        x-go-name: Amount
      block_number:
        format: uint64
        type: integer
        x-go-name: BlockNumber
      from:
        type: string
        x-go-name: From
      log_index:
        format: uint64
        type: integer
        x-go-name: LogIndex
      nonce:
        format: uint64
        type: integer
        x-go-name: Nonce
      tx_hash:
        type: string
        x-go-name: TxHash
    type: object
    x-go-package: _/Users/<USER>/heimdall/staking/client/rest
  stakingValidatorStakeUpdateValue:
    properties:
      memo:
        type: string
        x-go-name: Memo
      msg:
        $ref: '#/definitions/stakingValidatorStakeUpdateMsg'
      signature:
        type: string
        x-go-name: Signature
    type: object
    x-go-package: _/Users/<USER>/heimdall/staking/client/rest
  stakingValidatorStatus:
    properties:
      result:
        type: boolean
        x-go-name: Result
    type: object
    x-go-package: _/Users/<USER>/heimdall/staking/client/rest
  stakingValidatorStatusStructure:
    properties:
      height:
        type: string
        x-go-name: Height
      result:
        $ref: '#/definitions/stakingValidatorStatus'
    type: object
    x-go-package: _/Users/<USER>/heimdall/staking/client/rest
  stakingValidatorStructure:
    properties:
      height:
        type: string
        x-go-name: Height
      result:
        $ref: '#/definitions/validator'
    type: object
    x-go-package: _/Users/<USER>/heimdall/staking/client/rest
  stakingValidatorUpdateInput:
    properties:
      ID:
        format: uint64
        type: integer
      base_req:
        $ref: '#/definitions/BaseReq'
      block_number:
        format: uint64
        type: integer
        x-go-name: BlockNumber
      log_index:
        format: uint64
        type: integer
        x-go-name: LogIndex
      nonce:
        format: uint64
        type: integer
        x-go-name: Nonce
      pubKey:
        type: string
        x-go-name: NewSignerPubKey
      tx_hash:
        type: string
        x-go-name: TxHash
    type: object
    x-go-package: _/Users/<USER>/heimdall/staking/client/rest
  stakingValidatorUpdateMsg:
    properties:
      type:
        type: string
        x-go-name: Type
      value:
        $ref: '#/definitions/stakingValidatorUpdateVal'
    type: object
    x-go-package: _/Users/<USER>/heimdall/staking/client/rest
  stakingValidatorUpdateOutput:
    properties:
      type:
        type: string
        x-go-name: Type
      value:
        $ref: '#/definitions/stakingValidatorUpdateValue'
    type: object
    x-go-package: _/Users/<USER>/heimdall/staking/client/rest
  stakingValidatorUpdateVal:
    properties:
      block_number:
        format: uint64
        type: integer
        x-go-name: BlockNumber
      from:
        type: string
        x-go-name: From
      id:
        format: uint64
        type: integer
        x-go-name: ID
      log_index:
        format: uint64
        type: integer
        x-go-name: LogIndex
      nonce:
        format: uint64
        type: integer
        x-go-name: Nonce
      pub_key:
        type: string
        x-go-name: NewSignerPubKey
      tx_hash:
        type: string
        x-go-name: TxHash
    type: object
    x-go-package: _/Users/<USER>/heimdall/staking/client/rest
  stakingValidatorUpdateValue:
    properties:
      memo:
        type: string
        x-go-name: Memo
      msg:
        $ref: '#/definitions/stakingValidatorUpdateMsg'
      signature:
        type: string
        x-go-name: Signature
    type: object
    x-go-package: _/Users/<USER>/heimdall/staking/client/rest
  topupDividendAccountProofStructure:
    properties:
      height:
        type: string
        x-go-name: Height
      result:
        $ref: '#/definitions/DividendAccountProof'
    type: object
    x-go-package: _/Users/<USER>/heimdall/topup/client/rest
  topupDividendAccountProofVerifyStructure:
    properties:
      height:
        type: string
        x-go-name: Height
      result:
        type: boolean
        x-go-name: Result
    type: object
    x-go-package: _/Users/<USER>/heimdall/topup/client/rest
  topupDividendAccountRootStructure:
    properties:
      height:
        type: string
        x-go-name: Height
      result:
        items:
          format: uint8
          type: integer
        type: array
        x-go-name: Result
    type: object
    x-go-package: _/Users/<USER>/heimdall/topup/client/rest
  topupDividendAccountStructure:
    properties:
      height:
        type: string
        x-go-name: Height
      result:
        $ref: '#/definitions/DividendAccount'
    type: object
    x-go-package: _/Users/<USER>/heimdall/topup/client/rest
  topupFeeInput:
    properties:
      base_req:
        $ref: '#/definitions/BaseReq'
      block_number:
        format: uint64
        type: integer
        x-go-name: BlockNumber
      fee:
        type: string
        x-go-name: Fee
      log_index:
        format: uint64
        type: integer
        x-go-name: LogIndex
      tx_hash:
        type: string
        x-go-name: TxHash
      user:
        type: string
        x-go-name: User
    type: object
    x-go-package: _/Users/<USER>/heimdall/topup/client/rest
  topupFeeMsg:
    properties:
      type:
        type: string
        x-go-name: Type
      value:
        $ref: '#/definitions/topupFeeVal'
    type: object
    x-go-package: _/Users/<USER>/heimdall/topup/client/rest
  topupFeeOutput:
    properties:
      type:
        type: string
        x-go-name: Type
      value:
        $ref: '#/definitions/topupFeeValue'
    type: object
    x-go-package: _/Users/<USER>/heimdall/topup/client/rest
  topupFeeVal:
    properties:
      block_number:
        format: uint64
        type: integer
        x-go-name: BlockNumber
      fee:
        type: string
        x-go-name: Fee
      from_address:
        type: string
        x-go-name: FromAddress
      log_index:
        format: uint64
        type: integer
        x-go-name: LogIndex
      tx_hash:
        type: string
        x-go-name: TxHash
      user:
        type: string
        x-go-name: User
    type: object
    x-go-package: _/Users/<USER>/heimdall/topup/client/rest
  topupFeeValue:
    properties:
      memo:
        type: string
        x-go-name: Memo
      msg:
        $ref: '#/definitions/topupFeeMsg'
      signature:
        type: string
        x-go-name: Signature
    type: object
    x-go-package: _/Users/<USER>/heimdall/topup/client/rest
  topupWithdrawInput:
    properties:
      amount:
        type: string
        x-go-name: Amount
      base_req:
        $ref: '#/definitions/BaseReq'
    type: object
    x-go-package: _/Users/<USER>/heimdall/topup/client/rest
  topupWithdrawMsg:
    properties:
      type:
        type: string
        x-go-name: Type
      value:
        $ref: '#/definitions/topupWithdrawVal'
    type: object
    x-go-package: _/Users/<USER>/heimdall/topup/client/rest
  topupWithdrawOutput:
    properties:
      type:
        type: string
        x-go-name: Type
      value:
        $ref: '#/definitions/topupWithdrawValue'
    type: object
    x-go-package: _/Users/<USER>/heimdall/topup/client/rest
  topupWithdrawVal:
    properties:
      amount:
        type: string
        x-go-name: Amount
      from_address:
        type: string
        x-go-name: FromAddress
    type: object
    x-go-package: _/Users/<USER>/heimdall/topup/client/rest
  topupWithdrawValue:
    properties:
      memo:
        type: string
        x-go-name: Memo
      msg:
        $ref: '#/definitions/topupWithdrawMsg'
      signature:
        type: string
        x-go-name: Signature
    type: object
    x-go-package: _/Users/<USER>/heimdall/topup/client/rest
  txsBroadcastInput:
    properties:
      mode:
        type: string
        x-go-name: Mode
      tx:
        $ref: '#/definitions/StdTx'
    type: object
    x-go-package: _/Users/<USER>/heimdall/client/tx
  txsEncodeInput:
    properties:
      tx:
        $ref: '#/definitions/StdTx'
    type: object
    x-go-package: _/Users/<USER>/heimdall/client/tx
  val:
    properties:
      bor_chain_id:
        type: string
        x-go-name: BorChainId
      end_block:
        type: string
        x-go-name: EndBlock
      proposer:
        type: string
        x-go-name: Proposer
      seed:
        type: string
        x-go-name: Seed
      span_id:
        type: string
        x-go-name: SpanID
      start_block:
        type: string
        x-go-name: StartBlock
    type: object
    x-go-package: _/Users/<USER>/heimdall/bor/client/rest
  validator:
    properties:
      ID:
        format: int64
        type: integer
      accum:
        format: int64
        type: integer
        x-go-name: Accum
      endEpoch:
        format: int64
        type: integer
        x-go-name: EndEpoch
      jailed:
        type: boolean
        x-go-name: Jailed
      last_updated:
        type: string
        x-go-name: Last_Updated
      nonce:
        format: int64
        type: integer
        x-go-name: Nonce
      power:
        format: int64
        type: integer
        x-go-name: Power
      pubKey:
        type: string
        x-go-name: PubKey
      signer:
        type: string
        x-go-name: Signer
      startEpoch:
        format: int64
        type: integer
        x-go-name: StartEpoch
    type: object
    x-go-package: _/Users/<USER>/heimdall/staking/client/rest
  validatorSet:
    properties:
      proposer:
        $ref: '#/definitions/validator'
      validators:
        items:
          $ref: '#/definitions/validator'
        type: array
        x-go-name: Validators
    type: object
    x-go-package: _/Users/<USER>/heimdall/checkpoint/client/rest
  validators:
    properties:
      validators:
        items:
          $ref: '#/definitions/validator'
        type: array
        x-go-name: Validators
    type: object
    x-go-package: _/Users/<USER>/heimdall/staking/client/rest
  value:
    properties:
      memo:
        type: string
        x-go-name: Memo
      msg:
        $ref: '#/definitions/msg'
      signature:
        type: string
        x-go-name: Signature
    type: object
    x-go-package: _/Users/<USER>/heimdall/bor/client/rest
  vote:
    properties:
      option:
        type: string
        x-go-name: Option
      proposal_id:
        type: string
        x-go-name: ProposalId
      voter:
        type: string
        x-go-name: Voter
    type: object
    x-go-package: _/Users/<USER>/heimdall/gov/client/rest

paths:
  /auth/accounts/{address}:
    get:
      operationId: authAccount
      parameters:
      - description: Account Address
        in: path
        name: address
        required: true
        type: string
        x-go-name: Address
      responses:
        "200":
          $ref: '#/responses/authAccountResponse'
      summary: It returns the account details.
      tags:
      - auth
  /auth/accounts/{address}/sequence:
    get:
      description: It returns the account sequence
      operationId: authAccountSequence
      parameters:
      - description: Account Address
        in: path
        name: address
        required: true
        type: string
        x-go-name: Address
      responses:
        "200":
          $ref: '#/responses/authAccountSequenceResponse'
      tags:
      - auth
  /auth/params:
    get:
      operationId: authParams
      responses:
        "200":
          $ref: '#/responses/authParamsResponse'
      summary: It returns the auth parameters.
      tags:
      - auth
  /bank/accounts/{address}/transfers:
    post:
      operationId: bankBalanceTransfer
      parameters:
      - description: Address of the account
        in: path
        name: address
        required: true
        type: string
        x-go-name: Address
      - description: Body
        in: body
        name: input
        required: true
        schema:
          $ref: '#/definitions/SendReqInput'
        x-go-name: Input
      responses:
        "200":
          $ref: '#/responses/bankBalanceTransferResponse'
      summary: It returns the prepared msg for the transfer of balance from one account
        to another.
      tags:
      - bank
  /bank/balances/{address}:
    get:
      description: It returns the matic balance of particular address
      operationId: bankBalanceByAddress
      parameters:
      - description: Address of the account
        in: path
        name: address
        required: true
        type: string
        x-go-name: Address
      - description: Address of the account
        in: query
        name: height
        type: string
        x-go-name: Height
      responses:
        "200":
          $ref: '#/responses/bankBalanceByAddressResponse'
      tags:
      - bank
  /bor/latest-span:
    get:
      description: It returns the latest-span
      operationId: borSpanLatest
      parameters:
      - description: Block Height
        in: query
        name: height
        type: string
        x-go-name: Height
      responses:
        "200":
          $ref: '#/responses/borSpanResponse'
      tags:
      - bor
  /bor/next-span-seed:
    get:
      description: It returns the seed for the next span
      operationId: borNextSpanSeed
      parameters:
      - description: Block Height
        in: query
        name: height
        type: string
        x-go-name: Height
      responses:
        "200":
          $ref: '#/responses/borNextSpanSeedRespose'
      tags:
      - bor
  /bor/params:
    get:
      description: It returns the span parameters
      operationId: borSpanParams
      parameters:
      - description: Block Height
        in: query
        name: height
        type: string
        x-go-name: Height
      responses:
        "200":
          $ref: '#/responses/borSpanParamsResponse'
      tags:
      - bor
  /bor/prepare-next-span:
    get:
      description: It returns the prepared next span
      operationId: borPrepareNextSpan
      parameters:
      - description: Start Block
        format: int64
        in: query
        name: start_block
        required: true
        type: integer
        x-go-name: StartBlock
      - description: Span ID of the span
        format: int64
        in: query
        name: span_id
        required: true
        type: integer
        x-go-name: SpanId
      - description: Chain ID of the network
        format: int64
        in: query
        name: chain_id
        required: true
        type: integer
        x-go-name: ChainId
      - description: Block Height
        in: query
        name: height
        type: string
        x-go-name: Height
      responses:
        "200":
          $ref: '#/responses/borSpanResponse'
      tags:
      - bor
  /bor/propose-span:
    post:
      description: It returns the prepared msg for proposing the span
      operationId: borProposeSpan
      parameters:
      - description: Body
        in: body
        name: input
        required: true
        schema:
          $ref: '#/definitions/SendReqInput'
        x-go-name: Input
      responses:
        "200":
          $ref: '#/responses/borProposeSpanResponse'
      tags:
      - bor
  /bor/span/{id}:
    get:
      description: It returns the span based on ID
      operationId: borSpanById
      parameters:
      - description: Id number of the span
        format: int64
        in: path
        name: id
        required: true
        type: integer
        x-go-name: Id
      - description: Block Height
        in: query
        name: height
        type: string
        x-go-name: Height
      responses:
        "200":
          $ref: '#/responses/borSpanResponse'
      tags:
      - bor
  /bor/span/list:
    get:
      description: It returns the list of Bor Span
      operationId: borSpanList
      parameters:
      - description: Page Number
        format: int64
        in: query
        name: page
        required: true
        type: integer
        x-go-name: Page
      - description: Limit
        format: int64
        in: query
        name: limit
        required: true
        type: integer
        x-go-name: Limit
      - description: Block Height
        in: query
        name: height
        type: string
        x-go-name: Height
      responses:
        "200":
          $ref: '#/responses/borSpanListResponse'
      tags:
      - bor
  /chainmanager/params:
    get:
      description: It returns the chain-manager parameters
      operationId: chainManagerParams
      parameters:
      - description: Block Height
        in: query
        name: height
        type: string
        x-go-name: Height
      responses:
        "200":
          $ref: '#/responses/chainManagerParamsResponse'
      tags:
      - chain-manager
  /checkpoint/ack:
    post:
      description: It returns the prepared msg for ack checkpoint
      operationId: checkpointAck
      parameters:
      - description: Body
        in: body
        name: input
        required: true
        schema:
          $ref: '#/definitions/checkpointAckInput'
        x-go-name: Input
      responses:
        "200":
          $ref: '#/responses/checkpointAckResponse'
      tags:
      - checkpoint
  /checkpoint/new:
    post:
      description: It returns the prepared msg for new checkpoint
      operationId: checkpointNew
      parameters:
      - description: Body
        in: body
        name: input
        required: true
        schema:
          $ref: '#/definitions/checkpointNewInput'
        x-go-name: Input
      responses:
        "200":
          $ref: '#/responses/checkpointNewResponse'
      tags:
      - checkpoint
  /checkpoint/no-ack:
    post:
      description: It returns the prepared msg for no-ack checkpoint
      operationId: checkpointNoAck
      parameters:
      - description: Body
        in: body
        name: input
        required: true
        schema:
          $ref: '#/definitions/checkpointAckInput'
        x-go-name: Input
      responses:
        "200":
          $ref: '#/responses/checkpointNoAckResponse'
      tags:
      - checkpoint
  /checkpoints/{id}:
    get:
      description: It returns the checkpoint by ID
      operationId: checkpointById
      parameters:
      - description: ID of the checkpoint
        format: int64
        in: path
        name: id
        required: true
        type: integer
        x-go-name: Id
      - description: Block Height
        in: query
        name: height
        type: string
        x-go-name: Height
      responses:
        "200":
          $ref: '#/responses/checkpointResponse'
      tags:
      - checkpoint
  /checkpoints/buffer:
    get:
      description: It returns the checkpoint buffer
      operationId: checkpointBuffer
      parameters:
      - description: Block Height
        in: query
        name: height
        type: string
        x-go-name: Height
      responses:
        "200":
          $ref: '#/responses/checkpointResponse'
      tags:
      - checkpoint
  /checkpoints/count:
    get:
      description: It returns the checkpoint counts
      operationId: checkpointCount
      parameters:
      - description: Block Height
        in: query
        name: height
        type: string
        x-go-name: Height
      responses:
        "200":
          $ref: '#/responses/checkpointCountResponse'
      tags:
      - checkpoint
  /checkpoints/last-no-ack:
    get:
      description: It returns the last no ack
      operationId: checkpointLastNoAck
      parameters:
      - description: Block Height
        in: query
        name: height
        type: string
        x-go-name: Height
      responses:
        "200":
          $ref: '#/responses/lastNoAckResponse'
      tags:
      - checkpoint
  /checkpoints/latest:
    get:
      description: It returns the last checkpoint from the store
      operationId: checkpointLatest
      parameters:
      - description: Block Height
        in: query
        name: height
        type: string
        x-go-name: Height
      responses:
        "200":
          $ref: '#/responses/checkpointResponse'
      tags:
      - checkpoint
  /checkpoints/list:
    get:
      description: It returns the checkpoints list
      operationId: checkpointList
      parameters:
      - description: Page number
        format: int64
        in: query
        name: page
        required: true
        type: integer
        x-go-name: Page
      - description: Limit per page
        format: int64
        in: query
        name: limit
        required: true
        type: integer
        x-go-name: Limit
      - description: Block Height
        in: query
        name: height
        type: string
        x-go-name: Height
      responses:
        "200":
          $ref: '#/responses/checkpointListResponse'
      tags:
      - checkpoint
  /checkpoints/params:
    get:
      description: It returns the checkpoint parameters
      operationId: checkpointParams
      parameters:
      - description: Block Height
        in: query
        name: height
        type: string
        x-go-name: Height
      responses:
        "200":
          $ref: '#/responses/checkpointParamsResponse'
      tags:
      - checkpoint
  /checkpoints/prepare:
    get:
      description: It returns the prepared checkpoint
      operationId: checkpointPrepare
      parameters:
      - description: Start Block
        format: int64
        in: query
        name: start
        required: true
        type: integer
        x-go-name: Start
      - description: End Block
        format: int64
        in: query
        name: end
        required: true
        type: integer
        x-go-name: End
      - description: Block Height
        in: query
        name: height
        type: string
        x-go-name: Height
      responses:
        "200":
          $ref: '#/responses/checkpointPrepareResponse'
      tags:
      - checkpoint
  /clerk/event-record/{recordID}:
    get:
      description: It returns the clerk event based on ID
      operationId: clerkEventById
      parameters:
      - description: ID of the checkpoint
        format: int64
        in: path
        name: recordID
        required: true
        type: integer
        x-go-name: Id
      - description: Block Height
        in: query
        name: height
        type: string
        x-go-name: Height
      responses:
        "200":
          $ref: '#/responses/clerkEventByIdResponse'
      tags:
      - clerk
  /clerk/event-record/list:
    get:
      description: It returns the clerk events list
      operationId: clerkEventList
      parameters:
      - description: Page number
        format: int64
        in: query
        name: page
        required: true
        type: integer
        x-go-name: Page
      - description: Limit per page
        format: int64
        in: query
        name: limit
        required: true
        type: integer
        x-go-name: Limit
      - description: Block Height
        in: query
        name: height
        type: string
        x-go-name: Height
      responses:
        "200":
          $ref: '#/responses/clerkEventListResponse'
      tags:
      - clerk
  /clerk/isoldtx:
    get:
      operationId: clerkIsOldTx
      parameters:
      - description: Log Index of the transaction
        format: int64
        in: query
        name: logindex
        required: true
        type: integer
        x-go-name: LogIndex
      - description: Hash of the transaction
        in: query
        name: txhash
        required: true
        type: string
        x-go-name: Txhash
      - description: Block Height
        in: query
        name: height
        type: string
        x-go-name: Height
      responses:
        "200":
          $ref: '#/responses/clerkIsOldTxResponse'
      summary: It checks for whether the transaction is old or new.
      tags:
      - clerk
  /clerk/records:
    post:
      description: It returns the prepared msg for new clerk event
      operationId: clerkNewEvent
      parameters:
      - description: Body
        in: body
        name: input
        required: true
        schema:
          $ref: '#/definitions/clerkNewEventInput'
        x-go-name: Input
      responses:
        "200":
          $ref: '#/responses/clerkNewEventResponse'
      tags:
      - clerk
  /gov/parameters/deposit:
    get:
      description: It returns the gov deposit parameters
      operationId: govParametersDeposit
      parameters:
      - description: Block Height
        in: query
        name: height
        type: string
        x-go-name: Height
      responses:
        "200":
          $ref: '#/responses/govParametersDepositResponse'
      tags:
      - gov
  /gov/parameters/tallying:
    get:
      description: It returns the gov tallying parameters
      operationId: govParametersTallying
      responses:
        "200":
          $ref: '#/responses/govParametersTallyingResponse'
      tags:
      - gov
  /gov/parameters/voting:
    get:
      description: It returns the gov voting parameters
      operationId: govParametersVoting
      responses:
        "200":
          $ref: '#/responses/govParametersVotingResponse'
      tags:
      - gov
  /gov/proposals:
    get:
      description: It returns the gov proposals
      operationId: govProposalsGet
      parameters:
      - description: Proposal Status [DepositPeriod,Passed,Rejected,Failed,VotingPeriod]
        in: query
        name: status
        type: string
        x-go-name: Status
      - description: Limit
        format: int64
        in: query
        name: limit
        type: integer
        x-go-name: Limit
      - description: Voter ID
        format: int64
        in: query
        name: voter
        type: integer
        x-go-name: Voter
      - description: Depositor ID
        format: int64
        in: query
        name: depositor
        type: integer
        x-go-name: Depositor
      responses:
        "200":
          $ref: '#/responses/govProposalsResponse'
      tags:
      - gov
    post:
      description: It returns the prepared msg for gov Proposals
      operationId: govProposals
      parameters:
      - description: Body
        in: body
        name: input
        required: true
        schema:
          $ref: '#/definitions/govProposalsInput'
        x-go-name: Input
      - description: Block Height
        in: query
        name: height
        type: string
        x-go-name: Height
      responses:
        "200":
          $ref: '#/responses/interface%7B%7D'
      tags:
      - gov
  /gov/proposals/{proposal-id}:
    get:
      description: It returns the proposal based on the proposal Id
      operationId: govProposalById
      parameters:
      - description: Proposal ID
        format: int64
        in: path
        name: proposal-id
        required: true
        type: integer
        x-go-name: ProposalId
      responses:
        "200":
          $ref: '#/responses/govProposalResponse'
      tags:
      - gov
  /gov/proposals/{proposal-id}/deposits:
    get:
      description: It returns the gov deposit parameters
      operationId: govDepositByProposalId
      parameters:
      - description: Proposal ID
        format: int64
        in: path
        name: proposal-id
        required: true
        type: integer
        x-go-name: ProposalId
      responses:
        "200":
          $ref: '#/responses/govDepositsResponse'
      tags:
      - gov
    post:
      description: It returns the prepared msg for gov proposal deposit
      operationId: govProposalsDeposits
      parameters:
      - description: Proposal ID
        format: int64
        in: path
        name: proposal-id
        required: true
        type: integer
        x-go-name: ProposalID
      - description: Body
        in: body
        name: input
        required: true
        schema:
          $ref: '#/definitions/govProposalsDepositsInput'
        x-go-name: Input
      responses:
        "200":
          $ref: '#/responses/govProposalsDepositsResponse'
      tags:
      - gov
  /gov/proposals/{proposal-id}/deposits/{depositor}:
    get:
      description: It returns the deposit for a particular proposal based on depositor
        Id
      operationId: govDepositBasedOnDepositor
      parameters:
      - description: Proposal ID
        format: int64
        in: path
        name: proposal-id
        required: true
        type: integer
        x-go-name: ProposalId
      - description: Depositor ID
        format: int64
        in: path
        name: depositor
        required: true
        type: integer
        x-go-name: Depositor
      responses:
        "200":
          $ref: '#/responses/govDepositResponse'
      tags:
      - gov
  /gov/proposals/{proposal-id}/proposer:
    get:
      operationId: govProposerByProposalId
      parameters:
      - description: Proposal ID
        format: int64
        in: path
        name: proposal-id
        required: true
        type: integer
        x-go-name: ProposalId
      responses:
        "200":
          $ref: '#/responses/govProposerResponse'
      summary: It returns the proposer based on the proposal.
      tags:
      - gov
  /gov/proposals/{proposal-id}/tally:
    get:
      description: It returns the tally on the proposal ID
      operationId: govTallyByProposalId
      parameters:
      - description: Proposal ID
        format: int64
        in: path
        name: proposal-id
        required: true
        type: integer
        x-go-name: ProposalId
      responses:
        "200":
          $ref: '#/responses/govTallyResponse'
      tags:
      - gov
  /gov/proposals/{proposal-id}/votes:
    get:
      description: It returns the proposal votes based on proposal id
      operationId: govProposalVotesByProposalId
      parameters:
      - description: Proposal ID
        format: int64
        in: path
        name: proposal-id
        required: true
        type: integer
        x-go-name: ProposalId
      responses:
        "200":
          $ref: '#/responses/govVotesResponse'
      tags:
      - gov
    post:
      description: It returns the prepared msg for gov proposal votes
      operationId: govProposalsVotes
      parameters:
      - description: Proposal Id
        format: int64
        in: path
        name: proposal-id
        required: true
        type: integer
        x-go-name: ProposalId
      - description: Body
        in: body
        name: input
        required: true
        schema:
          $ref: '#/definitions/govProposalsVotesInput'
        x-go-name: Input
      responses:
        "200":
          $ref: '#/responses/interface%7B%7D'
      tags:
      - gov
  /gov/proposals/{proposal-id}/votes/{voter}:
    get:
      operationId: govVotesBasedOnVoterId
      parameters:
      - description: Proposal ID
        format: int64
        in: path
        name: proposal-id
        required: true
        type: integer
        x-go-name: ProposalId
      - description: Voter ID
        format: int64
        in: path
        name: voter
        required: true
        type: integer
        x-go-name: Voter
      responses:
        "200":
          $ref: '#/responses/govVoteResponse'
      summary: It returns the votes on the specific proposal Id based on voter Id.
      tags:
      - gov
  /overview:
    get:
      description: It returns the complete overview
      operationId: overview
      parameters:
      - description: Block Height
        in: query
        name: height
        type: string
        x-go-name: Height
      responses:
        "200":
          $ref: '#/responses/overviewResponse'
      tags:
      - checkpoint
  /slashing/isoldtx:
    get:
      description: It returns whether the transaction is old
      operationId: slashingIsOldTx
      parameters:
      - description: Log Index of the transaction
        format: int64
        in: query
        name: logindex
        required: true
        type: integer
        x-go-name: LogIndex
      - description: Hash of the transaction
        in: query
        name: txhash
        required: true
        type: string
        x-go-name: Txhash
      - description: Block Height
        in: query
        name: height
        type: string
        x-go-name: Height
      responses:
        "200":
          $ref: '#/responses/slashingIsOldTxResponse'
      tags:
      - slashing
  /slashing/latest_slash_info_bytes:
    get:
      description: It returns the latest signing info byte
      operationId: slashingLatestSlashInfoBytes
      parameters:
      - description: Block Height
        in: query
        name: height
        type: string
        x-go-name: Height
      responses:
        "200":
          $ref: '#/responses/slashingInfosBytesResponse'
      tags:
      - slashing
  /slashing/latest_slash_infos:
    get:
      description: It returns the latest signing infos
      operationId: slashingLatestInfos
      parameters:
      - description: Block Height
        in: query
        name: height
        type: string
        x-go-name: Height
      responses:
        "200":
          $ref: '#/responses/slashingLatestInfosResponse'
      tags:
      - slashing
  /slashing/parameters:
    get:
      description: It returns the slashing parameters
      operationId: slashingParameters
      parameters:
      - description: Block Height
        in: query
        name: height
        type: string
        x-go-name: Height
      responses:
        "200":
          $ref: '#/responses/slashingParametersResponse'
      tags:
      - slashing
  /slashing/signing_infos:
    get:
      operationId: slashingInfos
      parameters:
      - description: Block Height
        in: query
        name: height
        type: string
        x-go-name: Height
      responses:
        "200":
          $ref: '#/responses/slashingInfosResponse'
      summary: It returns the signing infos.
      tags:
      - slashing
  /slashing/tick:
    post:
      description: It returns the prepared msg for new tick
      operationId: slashingNewTick
      parameters:
      - description: Body
        in: body
        name: input
        required: true
        schema:
          $ref: '#/definitions/slashingNewTickInput'
        x-go-name: Input
      responses:
        "200":
          $ref: '#/responses/slashingNewTickResponse'
      tags:
      - slashing
  /slashing/tick-count:
    get:
      description: It returns the slashing tick count
      operationId: slashingTickCount
      parameters:
      - description: Block Height
        in: query
        name: height
        type: string
        x-go-name: Height
      responses:
        "200":
          $ref: '#/responses/slashingCountResponse'
      tags:
      - slashing
  /slashing/tick_slash_infos:
    get:
      description: It returns the tick slash infos
      operationId: slashingTickInfos
      parameters:
      - description: Page number
        format: int64
        in: query
        name: page
        required: true
        type: integer
        x-go-name: Page
      - description: Limit per page
        format: int64
        in: query
        name: limit
        required: true
        type: integer
        x-go-name: Limit
      - description: Block Height
        in: query
        name: height
        type: string
        x-go-name: Height
      responses:
        "200":
          $ref: '#/responses/slashingLatestInfosResponse'
      tags:
      - slashing
  /slashing/validators/{id}/latest_slash_info:
    get:
      description: It returns the latest signing infos of the validator based on Id
      operationId: slashingLatestInfoById
      parameters:
      - description: ID of the validator
        format: int64
        in: path
        name: id
        required: true
        type: integer
        x-go-name: Id
      - description: Block Height
        in: query
        name: height
        type: string
        x-go-name: Height
      responses:
        "200":
          $ref: '#/responses/slashingLatestInfoByIdResponse'
      tags:
      - slashing
  /slashing/validators/{id}/signing_info:
    get:
      description: It returns the signing infos of the validator based on Id
      operationId: slashingSigningInfoById
      parameters:
      - description: ID of the validator
        format: int64
        in: path
        name: id
        required: true
        type: integer
        x-go-name: Id
      - description: Block Height
        in: query
        name: height
        type: string
        x-go-name: Height
      responses:
        "200":
          $ref: '#/responses/slashingSigningInfoByIdResponse'
      tags:
      - slashing
  /slashing/validators/{validatorAddr}/unjail:
    post:
      description: It returns the prepared msg for unjail
      operationId: slashingUnjail
      parameters:
      - description: Body
        in: body
        name: input
        required: true
        schema:
          $ref: '#/definitions/slashingUnjailInput'
        x-go-name: Input
      - description: Validator Address
        in: path
        name: validatorAddr
        required: true
        type: string
        x-go-name: ValidatorAddr
      responses:
        "200":
          $ref: '#/responses/slashingUnjailResponse'
      tags:
      - slashing
  /staking/current-proposer:
    get:
      description: It returns proposer for current validator set
      operationId: stakingCurrentProposer
      parameters:
      - description: Block Height
        in: query
        name: height
        type: string
        x-go-name: Height
      responses:
        "200":
          $ref: '#/responses/stakingValidatorResponse'
      tags:
      - staking
  /staking/isoldtx:
    get:
      description: It returns status of the transaction
      operationId: stakingIsOldTx
      parameters:
      - description: Log Index of the transaction
        format: int64
        in: query
        name: logindex
        required: true
        type: integer
        x-go-name: LogIndex
      - description: Hash of the transaction
        in: query
        name: txhash
        required: true
        type: string
        x-go-name: Txhash
      - description: Block Height
        in: query
        name: height
        type: string
        x-go-name: Height
      responses:
        "200":
          $ref: '#/responses/stakingIsOldTxResponse'
      tags:
      - staking
  /staking/proposer/{times}:
    get:
      description: It returns proposer for current validator set by time
      operationId: stakingProposerByTime
      parameters:
      - description: time
        format: int64
        in: path
        name: times
        required: true
        type: integer
        x-go-name: Times
      - description: Block Height
        in: query
        name: height
        type: string
        x-go-name: Height
      responses:
        "200":
          $ref: '#/responses/stakingProposerByTimeResponse'
      tags:
      - staking
  /staking/signer/{address}:
    get:
      description: It returns the signer by address
      operationId: stakingSignerByAddress
      parameters:
      - description: Address of the signer
        in: path
        name: address
        required: true
        type: string
        x-go-name: Address
      - description: Block Height
        in: query
        name: height
        type: string
        x-go-name: Height
      responses:
        "200":
          $ref: '#/responses/stakingValidatorResponse'
      tags:
      - staking
  /staking/totalpower:
    get:
      description: It returns the total power of all the validators
      operationId: stakingTotalPower
      parameters:
      - description: Block Height
        in: query
        name: height
        type: string
        x-go-name: Height
      responses:
        "200":
          $ref: '#/responses/stakingTotalPowerResponse'
      tags:
      - staking
  /staking/validator-set:
    get:
      description: It returns the current validator set
      operationId: stakingValidatorSet
      parameters:
      - description: Block Height
        in: query
        name: height
        type: string
        x-go-name: Height
      responses:
        "200":
          $ref: '#/responses/stakingValidatorSetResponse'
      tags:
      - staking
  /staking/validator-status/{address}:
    get:
      description: It returns the status of the validator
      operationId: stakingValidatorStatus
      parameters:
      - description: Address of the validator
        in: path
        name: address
        required: true
        type: string
        x-go-name: Address
      - description: Block Height
        in: query
        name: height
        type: string
        x-go-name: Height
      responses:
        "200":
          $ref: '#/responses/stakingValidatorStatusResponse'
      tags:
      - staking
  /staking/validator/{id}:
    get:
      description: It returns the staking validator information by id
      operationId: stakingValidatorById
      parameters:
      - description: ID of the validator
        format: int64
        in: path
        name: id
        required: true
        type: integer
        x-go-name: Id
      - description: Block Height
        in: query
        name: height
        type: string
        x-go-name: Height
      responses:
        "200":
          $ref: '#/responses/stakingValidatorResponse'
      tags:
      - staking
  /staking/validators:
    delete:
      description: It returns the prepared msg for deleting the Validator
      operationId: stakingValidatorDelete
      parameters:
      - description: Body
        in: body
        name: input
        required: true
        schema:
          $ref: '#/definitions/stakingValidatorDeleteInput'
        x-go-name: Input
      responses:
        "200":
          $ref: '#/responses/stakingValidatorDeleteResponse'
      tags:
      - staking
    post:
      description: It returns the prepared msg for new validator joining
      operationId: stakingNewValidator
      parameters:
      - description: Body
        in: body
        name: input
        required: true
        schema:
          $ref: '#/definitions/stakingNewValidatorInput'
        x-go-name: Input
      responses:
        "200":
          $ref: '#/responses/stakingNewValidatorResponse'
      tags:
      - staking
    put:
      description: It returns the prepared msg for updating the validator
      operationId: stakingValidatorUpdate
      parameters:
      - description: Body
        in: body
        name: input
        required: true
        schema:
          $ref: '#/definitions/stakingValidatorUpdateInput'
        x-go-name: Input
      responses:
        "200":
          $ref: '#/responses/stakingValidatorUpdateResponse'
      tags:
      - staking
  /staking/validators/stake:
    put:
      description: It returns the prepared msg for updating the validator's stake
      operationId: stakingValidatorStakeUpdate
      parameters:
      - description: Body
        in: body
        name: input
        required: true
        schema:
          $ref: '#/definitions/stakingValidatorStakeUpdateInput'
        x-go-name: Input
      responses:
        "200":
          $ref: '#/responses/stakingValidatorStakeUpdateResponse'
      tags:
      - staking
  /topup/account-proof/{address}:
    get:
      description: It returns the account proof by User Address
      operationId: topupDividendAccountProofByAddress
      parameters:
      - description: Address
        in: path
        name: address
        required: true
        type: string
        x-go-name: Address
      - description: Block Height
        in: query
        name: height
        type: string
        x-go-name: Height
      responses:
        "200":
          $ref: '#/responses/topupDividendAccountProofResponse'
      tags:
      - topup
  /topup/account-proof/{address}/verify:
    get:
      description: It returns true if given Merkle path for dividendAccountID is valid
      operationId: topupDividendAccountProofVerify
      parameters:
      - description: Address
        in: path
        name: address
        required: true
        type: string
        x-go-name: Address
      - description: Block Height
        in: query
        name: height
        type: string
        x-go-name: Height
      responses:
        "200":
          $ref: '#/responses/topupDividendAccountProofVerifyResponse'
      tags:
      - topup
  /topup/dividend-account-root:
    get:
      description: It returns the genesis account roothash
      operationId: topupDividendAccountRoot
      parameters:
      - description: Block Height
        in: query
        name: height
        type: string
        x-go-name: Height
      responses:
        "200":
          $ref: '#/responses/topupDividendAccountRootResponse'
      tags:
      - topup
  /topup/dividend-account/{address}:
    get:
      description: It returns the Dividend Account information by User Address
      operationId: topupDividendAccountByAddress
      parameters:
      - description: Address
        in: path
        name: address
        required: true
        type: string
        x-go-name: Address
      - description: Block Height
        in: query
        name: height
        type: string
        x-go-name: Height
      responses:
        "200":
          $ref: '#/responses/topupDividendAccountResponse'
      tags:
      - topup
  /topup/fee:
    post:
      description: It returns the prepared msg for topup fee
      operationId: topupFee
      parameters:
      - description: Body
        in: body
        name: input
        required: true
        schema:
          $ref: '#/definitions/topupFeeInput'
        x-go-name: Input
      responses:
        "200":
          $ref: '#/responses/topupFeeResponse'
      tags:
      - topup
  /topup/isoldtx:
    get:
      description: It returns whether the transaction is old
      operationId: topupIsOldTx
      parameters:
      - description: Log Index of the transaction
        format: int64
        in: query
        name: logindex
        required: true
        type: integer
        x-go-name: LogIndex
      - description: Hash of the transaction
        in: query
        name: txhash
        required: true
        type: string
        x-go-name: Txhash
      - description: Block Height
        in: query
        name: height
        type: string
        x-go-name: Height
      responses:
        "200":
          $ref: '#/responses/topupIsOldTxResponse'
      tags:
      - topup
  /topup/withdraw:
    post:
      description: It returns the prepared msg for topup withdraw
      operationId: topupWithdraw
      parameters:
      - description: Body
        in: body
        name: input
        required: true
        schema:
          $ref: '#/definitions/topupWithdrawInput'
        x-go-name: Input
      responses:
        "200":
          $ref: '#/responses/topupWithdrawResponse'
      tags:
      - topup
  /txs:
    get:
      description: |-
        QueryTxsRequestHandlerFn implements a REST handler that searches for transactions.
        Genesis transactions are returned if the height parameter is set to zero,
        otherwise the transactions are searched for by events.
      operationId: txsGET
      parameters:
      - format: int64
        in: query
        name: height
        type: integer
        x-go-name: Height
      - format: int64
        in: query
        name: page
        type: integer
        x-go-name: Page
      - format: int64
        in: query
        name: limit
        type: integer
        x-go-name: Limit
      summary: It returns the list of transaction based on page,limit and events specified.
      tags:
      - txs
    post:
      operationId: txsBroadcast
      parameters:
      - description: Body
        in: body
        name: input
        required: true
        schema:
          $ref: '#/definitions/txsBroadcastInput'
        x-go-name: Input
      summary: It broadcast the signed transaction to the network.
      tags:
      - txs
  /txs/{hash}:
    get:
      description: |-
        QueryTxRequestHandlerFn implements a REST handler that queries a transaction
        by hash in a committed block.
      operationId: txsByHash
      parameters:
      - description: Hash
        in: path
        name: hash
        required: true
        type: string
        x-go-name: Hash
      - description: Height
        format: int64
        in: query
        name: height
        type: integer
        x-go-name: Height
      summary: It returns the transaction by hash.
      tags:
      - txs
  /txs/{hash}/commit-proof:
    get:
      description: QueryCommitTxRequestHandlerFn implements a REST handler that queries
        vote, sigs and tx bytes committed block.
      operationId: txsHashCommitProof
      parameters:
      - in: path
        name: hash
        required: true
        type: string
        x-go-name: Hash
      - format: int64
        in: query
        name: height
        type: integer
        x-go-name: Height
      summary: It returns the commit-proof for the transaction.
      tags:
      - txs
  /txs/{hash}/side-tx:
    get:
      description: |-
        It returns the side-tx bytes
        QuerySideTxRequestHandlerFn implements a REST handler that queries sigs, side-tx bytes committed block
      operationId: txsSideTx
      parameters:
      - in: path
        name: hash
        required: true
        type: string
        x-go-name: Hash
      - format: int64
        in: query
        name: height
        type: integer
        x-go-name: Height
      tags:
      - txs
  /txs/encode:
    post:
      description: It Encode the transaction
      operationId: txsEncode
      parameters:
      - description: Body
        in: body
        name: input
        required: true
        schema:
          $ref: '#/definitions/txsEncodeInput'
        x-go-name: Input
      responses: {}
      tags:
      - txs
responses:
  authAccountResponse:
    description: ""
    schema:
      $ref: '#/definitions/authAccountStructure'
  authAccountSequenceResponse:
    description: ""
    schema:
      $ref: '#/definitions/authAccountSequenceStructure'
  authParamsResponse:
    description: It represents the auth params
    schema:
      $ref: '#/definitions/authParamsStructure'
  bankBalanceByAddressResponse:
    description: It represents the bank balance of particluar account
    schema:
      $ref: '#/definitions/bankBalanceByAddress'
  bankBalanceTransferResponse:
    description: It represents transfer msg.
    schema:
      $ref: '#/definitions/output'
  borNextSpanSeedResponse:
    description: It represents the next span seed
    schema:
      $ref: '#/definitions/spanSeed'
  borProposeSpanResponse:
    description: It represents Propose Span msg.
    schema:
      $ref: '#/definitions/output'
  borSpanListResponse:
    description: It represents the list of spans
    schema:
      $ref: '#/definitions/borSpanList'
  borSpanParamsResponse:
    description: It represents the bor span parameters
    schema:
      $ref: '#/definitions/borSpanParams'
  borSpanResponse:
    description: It represents the span
    schema:
      $ref: '#/definitions/borSpan'
  chainManagerParamsResponse:
    description: It represents the bank balance of particluar account
    schema:
      $ref: '#/definitions/chainManagerParams'
  checkpointAckResponse:
    description: It represents Propose Span msg.
    schema:
      $ref: '#/definitions/checkpointAck'
  checkpointCountResponse:
    description: It represents the checkpoint count
    schema:
      $ref: '#/definitions/checkpointCountResponseStructure'
  checkpointListResponse:
    description: It represents the checkpoint list
    schema:
      $ref: '#/definitions/checkpointListStructure'
  checkpointNewResponse:
    description: It represents New checkpoint msg.
    schema:
      $ref: '#/definitions/checkpointNew'
  checkpointNoAckResponse:
    description: It represents Propose Span msg.
    schema:
      $ref: '#/definitions/checkpointNoAck'
  checkpointParamsResponse:
    description: It represents the checkpoint parameters
    schema:
      $ref: '#/definitions/checkpointParams'
  checkpointPrepareResponse:
    description: It represents the checkpoint prepare
    schema:
      $ref: '#/definitions/checkpointParams'
  checkpointResponse:
    description: It represents the checkpoint
    schema:
      $ref: '#/definitions/checkpointStructure'
  clerkEventByIdResponse:
    description: ""
    schema:
      $ref: '#/definitions/clerkEventById'
  clerkEventListResponse:
    description: ""
    schema:
      $ref: '#/definitions/clerkEventList'
  clerkIsOldTxResponse:
    description: ""
    schema:
      $ref: '#/definitions/isOldTx'
  clerkNewEventResponse:
    description: It represents New checkpoint msg.
    schema:
      $ref: '#/definitions/clerkNewEvent'
  govDepositResponse:
    description: It represents the vote response
    schema:
      $ref: '#/definitions/govDepositStructure'
  govDepositsResponse:
    description: It represents the vote response
    schema:
      $ref: '#/definitions/govDepositsStructure'
  govParametersDepositResponse:
    description: It represents the gov deposit parameters
    schema:
      $ref: '#/definitions/govParameterDepositStructure'
  govParametersTallyingResponse:
    description: It represents the gov tallying parameters
    schema:
      $ref: '#/definitions/govParameterTallyingStructure'
  govParametersVotingResponse:
    description: It represents the gov voting parameters
    schema:
      $ref: '#/definitions/govParameterVotingStructure'
  govProposalResponse:
    description: It represents the gov proposal
    schema:
      $ref: '#/definitions/govProposalsStructure'
  govProposalsResponse:
    description: It represents the gov proposals
    schema:
      $ref: '#/definitions/govProposalsStructure'
  govProposerResponse:
    description: It represents the gov proposer
    schema:
      $ref: '#/definitions/govProposerStructure'
  govTallyResponse:
    description: It represents the gov Tally based on Id
    schema:
      $ref: '#/definitions/govProposalsStructure'
  govVoteResponse:
    description: It represents the vote response
    schema:
      $ref: '#/definitions/govVoteStructure'
  govVotesResponse:
    description: It represents the votes responses
    schema:
      $ref: '#/definitions/govVotesStructure'
  lastNoAckResponse:
    description: It represents the last-no-ack
    schema:
      $ref: '#/definitions/lastNoAckResponseStructure'
  overviewResponse:
    description: It represents the overview
    schema:
      $ref: '#/definitions/overviewResponseStructure'
  slashingCountResponse:
    description: It represents the slashing count
    schema:
      $ref: '#/definitions/slashingCountStructure'
  slashingInfosBytesResponse:
    description: It represents the slashing count
    schema:
      $ref: '#/definitions/slashingInfosBytesStructure'
  slashingInfosResponse:
    description: ""
    schema:
      $ref: '#/definitions/slashingInfosStructure'
  slashingIsOldTxResponse:
    description: ""
    schema:
      $ref: '#/definitions/isOldTx'
  slashingLatestInfoByIdResponse:
    description: ""
    schema:
      $ref: '#/definitions/slashingLatestInfoByIdStructure'
  slashingLatestInfosResponse:
    description: ""
    schema:
      $ref: '#/definitions/slashingLatestInfosStructure'
  slashingNewTickResponse:
    description: It represents Propose Span msg.
    schema:
      $ref: '#/definitions/slashingNewTickOutput'
  slashingParametersResponse:
    description: It represents the slashing parameters
    schema:
      $ref: '#/definitions/slashingParametersStructure'
  slashingSigningInfoByIdResponse:
    description: ""
    schema:
      $ref: '#/definitions/slashingSigningInfoByIdStructure'
  slashingTickAckResponse:
    description: It represents Propose Span msg.
    schema:
      $ref: '#/definitions/slashingTickAckOutput'
  slashingUnjailResponse:
    description: It represents unjail msg.
    schema:
      $ref: '#/definitions/slashingUnjailOutput'
  stakingIsOldTxResponse:
    description: ""
    schema:
      $ref: '#/definitions/isOldTx'
  stakingNewValidatorResponse:
    description: It represents Propose Span msg.
    schema:
      $ref: '#/definitions/stakingNewValidatorOutput'
  stakingProposerByTimeResponse:
    description: It represents the proposer based on time
    schema:
      $ref: '#/definitions/stakingProposerByTimeStructure'
  stakingTotalPowerResponse:
    description: It represents the staking total power
    schema:
      $ref: '#/definitions/stakingTotalPowerStructure'
  stakingValidatorDeleteResponse:
    description: It represents validator deleting msg
    schema:
      $ref: '#/definitions/stakingValidatorDeleteOutput'
  stakingValidatorResponse:
    description: It represents the signer by address or id
    schema:
      $ref: '#/definitions/stakingValidatorStructure'
  stakingValidatorSetResponse:
    description: It represents the validator set
    schema:
      $ref: '#/definitions/stakingValidatorSetStructure'
  stakingValidatorStakeUpdateResponse:
    description: It represents validator stake update msg
    schema:
      $ref: '#/definitions/stakingValidatorStakeUpdateOutput'
  stakingValidatorStatusResponse:
    description: It represents the validor status
    schema:
      $ref: '#/definitions/stakingValidatorStatusStructure'
  stakingValidatorUpdateResponse:
    description: It represents validator update msg.
    schema:
      $ref: '#/definitions/stakingValidatorUpdateOutput'
  topupDividendAccountProofResponse:
    description: ""
    schema:
      $ref: '#/definitions/topupDividendAccountProofStructure'
  topupDividendAccountProofVerifyResponse:
    description: ""
    schema:
      $ref: '#/definitions/topupDividendAccountProofVerifyStructure'
  topupDividendAccountResponse:
    description: It represents the dividend account information
    schema:
      $ref: '#/definitions/topupDividendAccountStructure'
  topupDividendAccountRootResponse:
    description: ""
    schema:
      $ref: '#/definitions/topupDividendAccountRootStructure'
  topupFeeResponse:
    description: It represents topup fee msg.
    schema:
      $ref: '#/definitions/topupFeeOutput'
  topupIsOldTxResponse:
    description: ""
    schema:
      $ref: '#/definitions/isOldTx'
  topupWithdrawResponse:
    description: It represents topup withdraw msg.
    schema:
      $ref: '#/definitions/topupWithdrawOutput'




