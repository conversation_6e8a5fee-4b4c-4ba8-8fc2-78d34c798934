package cli

const (
	FlagProposerAddress   = "proposer"
	FlagValidatorAddress  = "validator"
	FlagValidatorID       = "id"
	FlagSignerAddress     = "signer"
	FlagSignerPubkey      = "signer-pubkey"
	FlagNewSignerPubkey   = "new-pubkey"
	FlagAmount            = "staked-amount"
	FlagAcceptDelegation  = "accept-delegation"
	FlagTxHash            = "tx-hash"
	FlagLogIndex          = "log-index"
	FlagActivationEpoch   = "activation-epoch"
	FlagDeactivationEpoch = "deactivation-epoch"
	FlagFeeAmount         = "fee-amount"
	FlagBlockNumber       = "block-number"
	FlagNonce             = "nonce"
	FlagStartEpoch        = "start-epoch"
	FlagEndEpoch          = "end-epoch"
	FlagTimes             = "times"
)
