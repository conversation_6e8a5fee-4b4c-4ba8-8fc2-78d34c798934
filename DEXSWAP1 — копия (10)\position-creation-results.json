{"created": "2025-07-19T13:19:07.993Z", "wallet": "bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV", "marginfiAccount": "********************************************", "results": [{"poolIndex": 1, "poolName": "Pool 1", "error": "Simulation failed. \nMessage: invalid transaction: Transaction contains a duplicate instruction (4) that is not allowed. \n\nCatch the `SendTransactionError` and call `getLogs()` on it for full details.", "timestamp": "2025-07-19T13:19:04.782Z", "status": "failed"}, {"poolIndex": 2, "poolName": "Pool 2", "error": "Simulation failed. \nMessage: invalid transaction: Transaction contains a duplicate instruction (4) that is not allowed. \n\nCatch the `SendTransactionError` and call `getLogs()` on it for full details.", "timestamp": "2025-07-19T13:19:05.970Z", "status": "failed"}, {"poolIndex": 3, "poolName": "Pool 3", "error": "Simulation failed. \nMessage: invalid transaction: Transaction contains a duplicate instruction (4) that is not allowed. \n\nCatch the `SendTransactionError` and call `getLogs()` on it for full details.", "timestamp": "2025-07-19T13:19:07.993Z", "status": "failed"}]}