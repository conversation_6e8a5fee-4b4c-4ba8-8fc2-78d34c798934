/**
 * 🚀 ПРЕДКОМПИЛИРОВАННЫЕ ШАБЛОНЫ ТРАНЗАКЦИЙ
 * ═══════════════════════════════════════════════════════════════
 * 🎯 УЛЬТРА-БЫСТРОЕ СОЗДАНИЕ ТРАНЗАКЦИЙ ЗА 1-2ms!
 */

const { TransactionInstruction, PublicKey, SystemProgram } = require('@solana/web3.js');
const { TOKEN_PROGRAM_ID, createTransferInstruction, createCloseAccountInstruction } = require('@solana/spl-token');

class PrecompiledTransactionTemplates {
    constructor(lowLevelMarginFi, wallet) {
        this.lowLevelMarginFi = lowLevelMarginFi;
        this.wallet = wallet;
        
        // 🚀 ПРЕДКОМПИЛИРОВАННЫЕ ШАБЛОНЫ
        this.templates = {
            computeBudget: null,
            startFlashLoan: null,
            marginFiBorrow: null,
            tokenTransfer1: null,
            closeAccount: null,
            tokenTransfer2: null,
            marginFiRepay: null,
            endFlashLoan: null
        };
        
        // 🎯 КЭШИРОВАННЫЕ АККАУНТЫ ДЛЯ УЛЬТРА-СКОРОСТИ!
        this.cachedAccounts = {
            wsolTokenAccount: null,
            bankVault: new PublicKey('2eicbpitfJXDwqCuFAmPgDP7t2oUotnAzbGzRKLMgSLe'),
            wsolMint: new PublicKey('So11111111111111111111111111111111111111112')
        };

        console.log('🚀 Инициализация предкомпилированных шаблонов...');
    }

    /**
     * 🔧 ПРЕДКОМПИЛЯЦИЯ ВСЕХ ШАБЛОНОВ
     */
    async precompileTemplates() {
        try {
            console.log('🔧 Предкомпиляция шаблонов транзакций...');
            
            // 1. COMPUTE BUDGET (всегда одинаковый)
            this.templates.computeBudget = this.createComputeBudgetTemplate();
            
            // 2. START FLASH LOAN (шаблон с переменным endIndex)
            this.templates.startFlashLoan = this.createStartFlashLoanTemplate();
            
            // 3. MARGINFI BORROW (шаблон с переменным amount)
            this.templates.marginFiBorrow = await this.createMarginFiBorrowTemplate();
            if (!this.templates.marginFiBorrow) {
                throw new Error('Не удалось создать MARGINFI BORROW шаблон!');
            }
            
            // 4. TOKEN TRANSFER 1 (создается динамически)
            this.templates.tokenTransfer1 = null; // Создается динамически

            // 5. CLOSE ACCOUNT (создается динамически)
            this.templates.closeAccount = null; // Создается динамически

            // 6. TOKEN TRANSFER 2 (создается динамически)
            this.templates.tokenTransfer2 = null; // Создается динамически
            
            // 7. MARGINFI REPAY (шаблон с переменным amount)
            this.templates.marginFiRepay = await this.createMarginFiRepayTemplate();
            if (!this.templates.marginFiRepay) {
                throw new Error('Не удалось создать MARGINFI REPAY шаблон!');
            }
            
            // 8. END FLASH LOAN (всегда одинаковый)
            this.templates.endFlashLoan = this.createEndFlashLoanTemplate();

            // 🚀 ПРЕДКЭШИРУЕМ TOKEN ACCOUNT ДЛЯ УЛЬТРА-СКОРОСТИ!
            const { getAssociatedTokenAddress } = require('@solana/spl-token');
            this.cachedAccounts.wsolTokenAccount = await getAssociatedTokenAddress(
                this.cachedAccounts.wsolMint,
                this.wallet.publicKey
            );

            console.log('✅ Все шаблоны предкомпилированы + аккаунты кэшированы!');
            return true;
            
        } catch (error) {
            console.error('❌ Ошибка предкомпиляции шаблонов:', error.message);
            throw error;
        }
    }

    /**
     * ⚡ УЛЬТРА-БЫСТРОЕ СОЗДАНИЕ ТРАНЗАКЦИИ (1-2ms!)
     */
    async createUltraFastTransaction(borrowAmount, sellPoolAddress, buyPoolAddress, sellSwapIx, buySwapIx) {
        const startTime = performance.now();

        try {
            // ⚡ УЛЬТРА-БЫСТРОЕ СОЗДАНИЕ БЕЗ ЛОГИРОВАНИЯ!
            const instructions = [];

            // 1. COMPUTE BUDGET (готовый)
            if (this.templates.computeBudget) {
                instructions.push(this.templates.computeBudget);
            } else {
                console.error('❌ COMPUTE BUDGET шаблон undefined!');
            }

            // 2. START FLASH LOAN (обновляем endIndex)
            if (this.templates.startFlashLoan) {
                const startFlashLoan = this.updateStartFlashLoanEndIndex(this.templates.startFlashLoan, 9);
                instructions.push(startFlashLoan);
            } else {
                console.error('❌ START FLASH LOAN шаблон undefined!');
            }

            // 3. MARGINFI BORROW (обновляем amount)
            if (this.templates.marginFiBorrow) {
                const marginFiBorrow = this.updateMarginFiBorrowAmount(this.templates.marginFiBorrow, borrowAmount);
                instructions.push(marginFiBorrow);
            } else {
                console.error('❌ MARGINFI BORROW шаблон undefined!');
            }
            
            // 4. TOKEN TRANSFER 1 (создаем динамически)
            try {
                const tokenTransfer1 = await this.createDynamicTokenTransfer1(borrowAmount);
                if (tokenTransfer1) instructions.push(tokenTransfer1);
            } catch (error) {
                console.error('❌ Ошибка создания TOKEN TRANSFER 1:', error.message);
            }

            // 5. SELL SWAP (передается готовый)
            if (sellSwapIx) {
                instructions.push(sellSwapIx);
            } else {
                console.error('❌ SELL SWAP инструкция undefined!');
            }

            // 6. CLOSE ACCOUNT (создаем динамически)
            try {
                const closeAccount = await this.createDynamicCloseAccount();
                if (closeAccount) instructions.push(closeAccount);
            } catch (error) {
                console.error('❌ Ошибка создания CLOSE ACCOUNT:', error.message);
            }

            // 7. BUY SWAP (передается готовый)
            if (buySwapIx) {
                instructions.push(buySwapIx);
            } else {
                console.error('❌ BUY SWAP инструкция undefined!');
            }

            // 8. TOKEN TRANSFER 2 (создаем динамически)
            try {
                const tokenTransfer2 = await this.createDynamicTokenTransfer2(borrowAmount);
                if (tokenTransfer2) instructions.push(tokenTransfer2);
            } catch (error) {
                console.error('❌ Ошибка создания TOKEN TRANSFER 2:', error.message);
            }
            
            // 9. MARGINFI REPAY (обновляем amount)
            if (this.templates.marginFiRepay) {
                const marginFiRepay = this.updateMarginFiRepayAmount(this.templates.marginFiRepay, borrowAmount);
                instructions.push(marginFiRepay);
            } else {
                console.error('❌ MARGINFI REPAY шаблон undefined!');
            }

            // 10. END FLASH LOAN (готовый)
            if (this.templates.endFlashLoan) {
                instructions.push(this.templates.endFlashLoan);
            } else {
                console.error('❌ END FLASH LOAN шаблон undefined!');
            }
            
            const creationTime = performance.now() - startTime;
            console.log(`🚀 ПРЕДКОМПИЛИРОВАННЫЕ ШАБЛОНЫ: ${creationTime.toFixed(2)}ms (${instructions.length} инструкций)`);

            // 🛡️ ПРОВЕРЯЕМ НА undefined ПЕРЕД ВОЗВРАТОМ!
            const validInstructions = instructions.filter(ix => ix !== null && ix !== undefined);
            if (validInstructions.length !== instructions.length) {
                console.warn(`⚠️ НАЙДЕНЫ undefined ИНСТРУКЦИИ: ${instructions.length - validInstructions.length}`);
                instructions.forEach((ix, index) => {
                    if (!ix) console.warn(`   ${index}: ${ix}`);
                });
            }

            return validInstructions;
            
        } catch (error) {
            console.error('❌ Ошибка создания ультра-быстрой транзакции:', error.message);
            throw error;
        }
    }

    /**
     * 🔧 СОЗДАНИЕ COMPUTE BUDGET ШАБЛОНА
     */
    createComputeBudgetTemplate() {
        const COMPUTE_BUDGET_PROGRAM_ID = new PublicKey('ComputeBudget111111111111111111111111111111');
        
        return new TransactionInstruction({
            keys: [],
            programId: COMPUTE_BUDGET_PROGRAM_ID,
            data: Buffer.from([0x02, 0x00, 0x00, 0x00, 0x40, 0x42, 0x0f, 0x00]) // 1,000,000 compute units
        });
    }

    /**
     * 🔧 СОЗДАНИЕ START FLASH LOAN ШАБЛОНА
     */
    createStartFlashLoanTemplate() {
        const startFlashLoanDiscriminator = [0x33, 0x1c, 0x9b, 0x8b, 0x8e, 0x0c, 0x4c, 0x6a];
        const INSTRUCTIONS_SYSVAR = new PublicKey('Sysvar1nstructions1111111111111111111111111');
        
        const startData = Buffer.alloc(16);
        startData.set(startFlashLoanDiscriminator, 0);
        // endIndex будет обновляться динамически
        
        return new TransactionInstruction({
            keys: [
                { pubkey: this.lowLevelMarginFi.marginfiAccount, isSigner: false, isWritable: true }, // 🔥 НЕ SIGNER!
                { pubkey: this.wallet.publicKey, isSigner: true, isWritable: true }, // 🔥 ТОЛЬКО WALLET ПОДПИСЫВАЕТ!
                { pubkey: INSTRUCTIONS_SYSVAR, isSigner: false, isWritable: false }
            ],
            programId: this.lowLevelMarginFi.MARGINFI_PROGRAM_ID,
            data: startData
        });
    }

    /**
     * 🔧 ОБНОВЛЕНИЕ endIndex В START FLASH LOAN
     */
    updateStartFlashLoanEndIndex(template, endIndex) {
        // ⚡ УЛЬТРА-БЫСТРОЕ КЛОНИРОВАНИЕ БЕЗ ПРОВЕРОК!
        const newInstruction = new TransactionInstruction({
            keys: template.keys.map(key => ({ ...key })), // Клонируем каждый key
            programId: template.programId,
            data: Buffer.from(template.data) // Клонируем data
        });

        // Обновляем endIndex
        newInstruction.data.writeBigUInt64LE(BigInt(endIndex), 8);
        return newInstruction;
    }

    /**
     * 🔧 СОЗДАНИЕ MARGINFI BORROW ШАБЛОНА
     */
    async createMarginFiBorrowTemplate() {
        try {
            const borrowDiscriminator = [0x68, 0x69, 0x50, 0x3d, 0x38, 0x5b, 0x5e, 0x3b];

            // Получаем аккаунты для borrow
            const accounts = await this.lowLevelMarginFi.getBorrowAccounts(
                this.lowLevelMarginFi.BANKS.SOL,
                this.lowLevelMarginFi.marginfiAccount,
                'SOL'
            );

            if (!accounts || accounts.length === 0) {
                throw new Error('Не удалось получить borrow аккаунты!');
            }
        
            // 🔥 ПРИНУДИТЕЛЬНО УБИРАЕМ isSigner СО ВСЕХ АККАУНТОВ КРОМЕ WALLET!
            const fixedAccounts = accounts.map(acc => {
                if (acc.pubkey.toString() === this.wallet.publicKey.toString()) {
                    return { ...acc, isSigner: true }; // 🔥 ТОЛЬКО WALLET ПОДПИСЫВАЕТ!
                } else {
                    return { ...acc, isSigner: false }; // 🔥 ВСЕ ОСТАЛЬНЫЕ НЕ ПОДПИСЫВАЮТ!
                }
            });

            const borrowData = Buffer.alloc(16);
            borrowData.set(borrowDiscriminator, 0);
            // amount будет обновляться динамически

            return new TransactionInstruction({
                keys: fixedAccounts,
                programId: this.lowLevelMarginFi.MARGINFI_PROGRAM_ID,
                data: borrowData
            });

        } catch (error) {
            console.error('❌ Ошибка создания MARGINFI BORROW шаблона:', error.message);
            throw error;
        }
    }

    /**
     * 🔧 ОБНОВЛЕНИЕ amount В MARGINFI BORROW
     */
    updateMarginFiBorrowAmount(template, amount) {
        // ⚡ УЛЬТРА-БЫСТРОЕ КЛОНИРОВАНИЕ БЕЗ ПРОВЕРОК!
        const newInstruction = new TransactionInstruction({
            keys: template.keys.map(key => ({ ...key })), // Клонируем каждый key
            programId: template.programId,
            data: Buffer.from(template.data) // Клонируем data
        });

        // Обновляем amount
        newInstruction.data.writeBigUInt64LE(BigInt(amount), 8);
        return newInstruction;
    }

    /**
     * 🔧 СОЗДАНИЕ MARGINFI REPAY ШАБЛОНА
     */
    async createMarginFiRepayTemplate() {
        try {
            const repayDiscriminator = [0x8a, 0x47, 0x5c, 0x68, 0x23, 0x4e, 0x6f, 0x44];

            // Получаем аккаунты для repay
            const accounts = await this.lowLevelMarginFi.getRepayAccounts(
                this.lowLevelMarginFi.BANKS.SOL,
                this.lowLevelMarginFi.marginfiAccount,
                'SOL'
            );

            if (!accounts || accounts.length === 0) {
                throw new Error('Не удалось получить repay аккаунты!');
            }
        
            // 🔥 ПРИНУДИТЕЛЬНО УБИРАЕМ isSigner СО ВСЕХ АККАУНТОВ КРОМЕ WALLET!
            const fixedAccounts = accounts.map(acc => {
                if (acc.pubkey.toString() === this.wallet.publicKey.toString()) {
                    return { ...acc, isSigner: true }; // 🔥 ТОЛЬКО WALLET ПОДПИСЫВАЕТ!
                } else {
                    return { ...acc, isSigner: false }; // 🔥 ВСЕ ОСТАЛЬНЫЕ НЕ ПОДПИСЫВАЮТ!
                }
            });

            const repayData = Buffer.alloc(17);
            repayData.set(repayDiscriminator, 0);
            // amount будет обновляться динамически
            repayData[16] = 1; // repayAll = true

            return new TransactionInstruction({
                keys: fixedAccounts,
                programId: this.lowLevelMarginFi.MARGINFI_PROGRAM_ID,
                data: repayData
            });

        } catch (error) {
            console.error('❌ Ошибка создания MARGINFI REPAY шаблона:', error.message);
            throw error;
        }
    }

    /**
     * 🔧 ОБНОВЛЕНИЕ amount В MARGINFI REPAY
     */
    updateMarginFiRepayAmount(template, amount) {
        // ⚡ УЛЬТРА-БЫСТРОЕ КЛОНИРОВАНИЕ БЕЗ ПРОВЕРОК!
        const newInstruction = new TransactionInstruction({
            keys: template.keys.map(key => ({ ...key })), // Клонируем каждый key
            programId: template.programId,
            data: Buffer.from(template.data) // Клонируем data
        });

        // Обновляем amount
        newInstruction.data.writeBigUInt64LE(BigInt(amount), 8);
        return newInstruction;
    }

    /**
     * 🔧 СОЗДАНИЕ ДИНАМИЧЕСКОГО TOKEN TRANSFER 1
     * Transfer из MarginFi bank vault в user token account
     */
    async createDynamicTokenTransfer1(amount) {
        try {
            const { createTransferInstruction } = require('@solana/spl-token');

            // ⚡ ИСПОЛЬЗУЕМ КЭШИРОВАННЫЕ АККАУНТЫ ДЛЯ УЛЬТРА-СКОРОСТИ!

            // ⚡ УЛЬТРА-БЫСТРОЕ СОЗДАНИЕ С КЭШИРОВАННЫМИ АККАУНТАМИ!
            const transferIx = createTransferInstruction(
                this.cachedAccounts.bankVault,       // source (MarginFi bank vault)
                this.cachedAccounts.wsolTokenAccount, // destination (user token account)
                this.wallet.publicKey,               // 🔥 OWNER = WALLET! (НЕ MarginFi аккаунт!)
                amount,                              // amount
                [],                                  // multiSigners
                TOKEN_PROGRAM_ID                     // programId
            );

            // 🔥 ПРИНУДИТЕЛЬНО УБИРАЕМ ЛИШНИЕ ПОДПИСИ!
            transferIx.keys = transferIx.keys.map(key => {
                if (key.pubkey.toString() === this.wallet.publicKey.toString()) {
                    return { ...key, isSigner: true }; // 🔥 ТОЛЬКО WALLET ПОДПИСЫВАЕТ!
                } else {
                    return { ...key, isSigner: false }; // 🔥 ВСЕ ОСТАЛЬНЫЕ НЕ ПОДПИСЫВАЮТ!
                }
            });

            return transferIx;

        } catch (error) {
            console.error('❌ Ошибка создания TokenTransfer1:', error.message);
            throw error;
        }
    }

    /**
     * 🔧 СОЗДАНИЕ ДИНАМИЧЕСКОГО CLOSE ACCOUNT
     * Закрытие временного WSOL аккаунта после первого swap'а
     */
    async createDynamicCloseAccount() {
        try {
            const { createCloseAccountInstruction } = require('@solana/spl-token');

            // ⚡ УЛЬТРА-БЫСТРОЕ СОЗДАНИЕ С КЭШИРОВАННЫМИ АККАУНТАМИ!
            const closeIx = createCloseAccountInstruction(
                this.cachedAccounts.wsolTokenAccount, // account (который закрываем)
                this.wallet.publicKey,                // destination (куда переводим остаток SOL)
                this.wallet.publicKey,                // owner (владелец аккаунта)
                [],                                   // multiSigners
                TOKEN_PROGRAM_ID                      // programId
            );

            // 🔥 ПРИНУДИТЕЛЬНО УБИРАЕМ ЛИШНИЕ ПОДПИСИ!
            closeIx.keys = closeIx.keys.map(key => {
                if (key.pubkey.toString() === this.wallet.publicKey.toString()) {
                    return { ...key, isSigner: true }; // 🔥 ТОЛЬКО WALLET ПОДПИСЫВАЕТ!
                } else {
                    return { ...key, isSigner: false }; // 🔥 ВСЕ ОСТАЛЬНЫЕ НЕ ПОДПИСЫВАЮТ!
                }
            });

            return closeIx;

        } catch (error) {
            console.error('❌ Ошибка создания CloseAccount:', error.message);
            throw error;
        }
    }

    /**
     * 🔧 СОЗДАНИЕ ДИНАМИЧЕСКОГО TOKEN TRANSFER 2
     * Transfer из user token account обратно в MarginFi bank vault для repay
     */
    async createDynamicTokenTransfer2(amount) {
        try {
            const { createTransferInstruction } = require('@solana/spl-token');

            // ⚡ УЛЬТРА-БЫСТРОЕ СОЗДАНИЕ С КЭШИРОВАННЫМИ АККАУНТАМИ!
            const transferIx = createTransferInstruction(
                this.cachedAccounts.wsolTokenAccount, // source (user token account)
                this.cachedAccounts.bankVault,        // destination (MarginFi bank vault)
                this.wallet.publicKey,                // owner (user wallet)
                amount,                               // amount
                [],                                   // multiSigners
                TOKEN_PROGRAM_ID                      // programId
            );

            // 🔥 ПРИНУДИТЕЛЬНО УБИРАЕМ ЛИШНИЕ ПОДПИСИ!
            transferIx.keys = transferIx.keys.map(key => {
                if (key.pubkey.toString() === this.wallet.publicKey.toString()) {
                    return { ...key, isSigner: true }; // 🔥 ТОЛЬКО WALLET ПОДПИСЫВАЕТ!
                } else {
                    return { ...key, isSigner: false }; // 🔥 ВСЕ ОСТАЛЬНЫЕ НЕ ПОДПИСЫВАЮТ!
                }
            });

            return transferIx;

        } catch (error) {
            console.error('❌ Ошибка создания TokenTransfer2:', error.message);
            throw error;
        }
    }

    /**
     * 🔧 СОЗДАНИЕ END FLASH LOAN ШАБЛОНА
     */
    createEndFlashLoanTemplate() {
        const endFlashLoanDiscriminator = [0x69, 0x7c, 0xc9, 0x6a, 0x99, 0x02, 0x08, 0x9c];
        
        const endData = Buffer.alloc(8);
        endData.set(endFlashLoanDiscriminator, 0);
        
        return new TransactionInstruction({
            keys: [
                { pubkey: this.lowLevelMarginFi.marginfiAccount, isSigner: false, isWritable: true }, // 🔥 НЕ SIGNER!
                { pubkey: this.wallet.publicKey, isSigner: true, isWritable: true } // 🔥 ТОЛЬКО WALLET ПОДПИСЫВАЕТ!
            ],
            programId: this.lowLevelMarginFi.MARGINFI_PROGRAM_ID,
            data: endData
        });
    }
}

module.exports = PrecompiledTransactionTemplates;
