/**
 * 🔥 БЫСТРОЕ ОБНОВЛЕНИЕ ALT ТАБЛИЦ
 * Обновляет все ALT таблицы одной командой
 */

const CustomALTUpdater = require('./update-custom-alt');
const ALTLoader = require('./load-alt-table');

async function quickUpdate() {
    console.log(`🚀 БЫСТРОЕ ОБНОВЛЕНИЕ ВСЕХ ALT ТАБЛИЦ`);
    console.log(`${'='.repeat(60)}`);
    
    try {
        // 1. Обновляем Custom ALT
        console.log(`\n🔥 ШАГ 1: ОБНОВЛЕНИЕ CUSTOM ALT`);
        const updater = new CustomALTUpdater();
        const customData = await updater.updateCustomALT();
        
        // 2. Проверяем другие ALT таблицы
        console.log(`\n🔥 ШАГ 2: ПРОВЕРКА ДРУГИХ ALT ТАБЛИЦ`);
        const loader = new ALTLoader();
        
        // Meteora ALT 1 (если нужно)
        const meteoraALT = 'G4fR7p8a4yAHE2i2WeGn8kMWgRjBQNKhKdJhQNjKKKKK';
        try {
            console.log(`   Проверка Meteora ALT 1...`);
            const meteoraData = await loader.loadALTTable(meteoraALT);
            console.log(`   ✅ Meteora ALT 1: ${meteoraData.totalAddresses} адресов`);
        } catch (error) {
            console.log(`   ⚠️ Meteora ALT 1 недоступен: ${error.message}`);
        }
        
        // 3. Итоговая статистика
        console.log(`\n📊 ИТОГОВАЯ СТАТИСТИКА:`);
        console.log(`   Custom ALT: ${customData.totalAddresses} адресов`);
        console.log(`   Файл обновлен: ${customData.updatedAt}`);
        
        console.log(`\n🎉 БЫСТРОЕ ОБНОВЛЕНИЕ ЗАВЕРШЕНО!`);
        
    } catch (error) {
        console.log(`\n💥 ОШИБКА: ${error.message}`);
        process.exit(1);
    }
}

// Запуск
if (require.main === module) {
    quickUpdate().catch(console.error);
}
