#!/usr/bin/env node

/**
 * 🔥 БЫСТРАЯ ПРОВЕРКА ALT НА ДУБЛИРОВАНИЯ
 */

const fs = require('fs');

function quickCheck() {
    console.log('🔥 БЫСТРАЯ ПРОВЕРКА ALT НА ДУБЛИРОВАНИЯ');
    console.log('=' .repeat(60));

    try {
        // 1. Проверяем Meteora ALT
        console.log('📁 ПРОВЕРКА METEORA ALT...');
        
        const meteoraAccounts = new Set();
        
        if (fs.existsSync('./meteora-alt-cache.json')) {
            const meteora = JSON.parse(fs.readFileSync('./meteora-alt-cache.json', 'utf8'));
            console.log(`✅ Meteora: ${meteora.totalTables} таблиц, ${meteora.totalAccounts} аккаунтов`);
            
            meteora.validationResults?.forEach((alt, index) => {
                if (alt.valid && alt.accounts) {
                    const accountSet = new Set();
                    let duplicates = 0;
                    
                    alt.accounts.forEach(account => {
                        if (accountSet.has(account)) {
                            duplicates++;
                        } else {
                            accountSet.add(account);
                        }
                        meteoraAccounts.add(account);
                    });
                    
                    console.log(`   ALT ${index + 1}: ${alt.accounts.length} аккаунтов, ${duplicates} внутренних дублирований`);
                }
            });
        }
        
        // 2. Проверяем MarginFi ALT
        console.log('\n📁 ПРОВЕРКА MARGINFI ALT...');
        
        const marginfiAccounts = new Set();
        
        if (fs.existsSync('./marginfi-alt-cache.json')) {
            const marginfi = JSON.parse(fs.readFileSync('./marginfi-alt-cache.json', 'utf8'));
            console.log(`✅ MarginFi: ${marginfi.totalTables} таблиц, ${marginfi.totalAccounts} аккаунтов`);
            
            marginfi.validationResults?.forEach((alt, index) => {
                if (alt.valid && alt.accounts) {
                    const accountSet = new Set();
                    let duplicates = 0;
                    
                    alt.accounts.forEach(account => {
                        if (accountSet.has(account)) {
                            duplicates++;
                        } else {
                            accountSet.add(account);
                        }
                        marginfiAccounts.add(account);
                    });
                    
                    console.log(`   ALT ${index + 1}: ${alt.accounts.length} аккаунтов, ${duplicates} внутренних дублирований`);
                }
            });
        }
        
        // 3. Проверяем пересечения
        console.log('\n🔍 ПРОВЕРКА ПЕРЕСЕЧЕНИЙ METEORA ↔ MARGINFI...');
        
        const intersection = [];
        meteoraAccounts.forEach(account => {
            if (marginfiAccounts.has(account)) {
                intersection.push(account);
            }
        });
        
        console.log(`📊 ПЕРЕСЕЧЕНИЯ:`);
        console.log(`   Meteora аккаунтов: ${meteoraAccounts.size}`);
        console.log(`   MarginFi аккаунтов: ${marginfiAccounts.size}`);
        console.log(`   Общих аккаунтов: ${intersection.length}`);
        
        if (intersection.length > 0) {
            console.log(`\n❌ ОБЩИЕ АККАУНТЫ (ИСТОЧНИК ДУБЛИРОВАНИЙ):`);
            intersection.slice(0, 10).forEach((account, index) => {
                console.log(`   ${index + 1}. ${account.slice(0, 8)}...${account.slice(-8)}`);
            });
            if (intersection.length > 10) {
                console.log(`   ... и еще ${intersection.length - 10} общих аккаунтов`);
            }
        }

        console.log('\n🎯 ИТОГ:');
        if (intersection.length > 0) {
            console.log(`❌ НАЙДЕНО ${intersection.length} ОБЩИХ АККАУНТОВ МЕЖДУ METEORA И MARGINFI!`);
            console.log(`💡 ЭТО ИСТОЧНИК ОШИБКИ "AccountLoadedTwice"!`);
            console.log(`🔧 РЕШЕНИЕ: Удалить общие аккаунты из одной из систем`);
        } else {
            console.log(`✅ Нет общих аккаунтов между системами`);
            console.log(`💡 Проблема в другом месте`);
        }

    } catch (error) {
        console.error('❌ Ошибка проверки:', error.message);
    }
}

// Функция для проверки конкретных аккаунтов из транзакции
function checkSpecificAccounts() {
    console.log('\n🔍 ПРОВЕРКА КОНКРЕТНЫХ АККАУНТОВ ИЗ ТРАНЗАКЦИИ...');

    // Аккаунты которые часто встречаются в Meteora и MarginFi транзакциях
    const commonAccounts = [
        'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA', // Token Program
        'So********************************111111112',   // SOL
        'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',   // USDC
        '********************************',               // System Program
        'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL',   // Associated Token Program
        'ComputeBudget111111111111111111111111111111',     // Compute Budget
        'Sysvar1nstructions1111111111111111111111111',     // Sysvar Instructions
        'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV',     // User wallet
        '68rtTtSuEPR84Wo1xWGs6ytBttn7JN33Ux8WsDp38FHk',   // User SOL token account
        '********************************************'    // User USDC token account
    ];

    try {
        const meteoraAccounts = new Set();
        const marginfiAccounts = new Set();

        // Загружаем Meteora аккаунты
        if (fs.existsSync('./meteora-alt-cache.json')) {
            const meteora = JSON.parse(fs.readFileSync('./meteora-alt-cache.json', 'utf8'));
            meteora.validationResults?.forEach(alt => {
                if (alt.valid && alt.accounts) {
                    alt.accounts.forEach(account => meteoraAccounts.add(account));
                }
            });
        }

        // Загружаем MarginFi аккаунты
        if (fs.existsSync('./marginfi-alt-cache.json')) {
            const marginfi = JSON.parse(fs.readFileSync('./marginfi-alt-cache.json', 'utf8'));
            marginfi.validationResults?.forEach(alt => {
                if (alt.valid && alt.accounts) {
                    alt.accounts.forEach(account => marginfiAccounts.add(account));
                }
            });
        }

        console.log('\n🔍 ПРОВЕРКА ОБЩИХ АККАУНТОВ:');

        commonAccounts.forEach((account, index) => {
            const inMeteora = meteoraAccounts.has(account);
            const inMarginFi = marginfiAccounts.has(account);
            const shortAccount = `${account.slice(0, 8)}...${account.slice(-8)}`;

            if (inMeteora && inMarginFi) {
                console.log(`❌ ${index + 1}. ${shortAccount} - В ОБЕИХ СИСТЕМАХ! (ДУБЛИРОВАНИЕ!)`);
            } else if (inMeteora) {
                console.log(`🌪️ ${index + 1}. ${shortAccount} - только в Meteora`);
            } else if (inMarginFi) {
                console.log(`🏦 ${index + 1}. ${shortAccount} - только в MarginFi`);
            } else {
                console.log(`✅ ${index + 1}. ${shortAccount} - НЕ в ALT таблицах`);
            }
        });

        // Ищем все пересечения
        const allIntersections = [];
        meteoraAccounts.forEach(account => {
            if (marginfiAccounts.has(account)) {
                allIntersections.push(account);
            }
        });

        console.log(`\n📊 ВСЕГО ПЕРЕСЕЧЕНИЙ: ${allIntersections.length}`);
        if (allIntersections.length > 0) {
            console.log('\n❌ ВСЕ ДУБЛИРУЮЩИЕСЯ АККАУНТЫ:');
            allIntersections.forEach((account, index) => {
                console.log(`   ${index + 1}. ${account.slice(0, 8)}...${account.slice(-8)}`);
            });
        }

    } catch (error) {
        console.error('❌ Ошибка проверки конкретных аккаунтов:', error.message);
    }
}

// Запуск
quickCheck();
checkSpecificAccounts();
