/**
 * 🚀 ПРОФЕССИОНАЛЬНЫЙ RATE LIMITER С ОЧЕРЕДЯМИ
 * Предотвращает перегрузку API и RPC endpoints
 */

class RateLimiter {
    constructor(name, maxRequests, timeWindow) {
        this.name = name;
        this.maxRequests = maxRequests;
        this.timeWindow = timeWindow; // в миллисекундах
        this.requests = [];
        this.queue = [];
        this.processing = false;

        console.log(`🛡️ Rate Limiter создан: ${name} (${maxRequests} req/${timeWindow}ms)`);
    }

    async execute(requestFunction) {
        return new Promise((resolve, reject) => {
            this.queue.push({ requestFunction, resolve, reject });
            this.processQueue();
        });
    }

    async processQueue() {
        if (this.processing || this.queue.length === 0) {
            return;
        }

        this.processing = true;

        while (this.queue.length > 0) {
            // Очищаем старые запросы
            const now = Date.now();
            this.requests = this.requests.filter(time => now - time < this.timeWindow);

            // Проверяем лимит
            if (this.requests.length >= this.maxRequests) {
                const oldestRequest = Math.min(...this.requests);
                const waitTime = this.timeWindow - (now - oldestRequest);

                console.log(`⏳ ${this.name}: Ждем ${waitTime}ms до следующего запроса`);
                await this.sleep(waitTime);
                continue;
            }

            // Выполняем запрос
            const { requestFunction, resolve, reject } = this.queue.shift();
            this.requests.push(now);

            try {
                const result = await requestFunction();
                resolve(result);
            } catch (error) {
                reject(error);
            }

            // Небольшая задержка между запросами
            await this.sleep(100);
        }

        this.processing = false;
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    getStats() {
        const now = Date.now();
        const recentRequests = this.requests.filter(time => now - time < this.timeWindow);
        return {
            name: this.name,
            recentRequests: recentRequests.length,
            maxRequests: this.maxRequests,
            queueLength: this.queue.length,
            utilization: `${Math.round((recentRequests.length / this.maxRequests) * 100)}%`
        };
    }
}

/**
 * 🎯 МЕНЕДЖЕР ВСЕХ RATE LIMITERS
 */
class RateLimiterManager {
    constructor() {
        this.limiters = new Map();
        this.setupLimiters();
    }

    setupLimiters() {
        // ✅ ИСПРАВЛЕНО: НАСТРОЙКИ ДЛЯ ПЛАТНЫХ RPC (QUICKNODE + HELIUS)
        console.log('🔥 НАСТРАИВАЕМ RATE LIMITER ДЛЯ ПЛАТНЫХ RPC!');

        // 🚀 ПРАВИЛЬНЫЕ JUPITER ЛИМИТЫ: 1 ЗАПРОС В 2 СЕКУНДЫ!
        this.limiters.set('jupiter-quotes', new RateLimiter('Jupiter Quotes', 1, 2000)); // 1 запрос в 2 сек
        this.limiters.set('jupiter-swap', new RateLimiter('Jupiter Swap', 1, 2000)); // 1 запрос в 2 сек
        this.limiters.set('jupiter-price', new RateLimiter('Jupiter Price', 1, 2000)); // 1 запрос в 2 сек

        // ✅ УЛЬТРА-КОНСЕРВАТИВНЫЕ RPC ЛИМИТЫ ДЛЯ ИЗБЕЖАНИЯ 429 ОШИБОК:
        // QuickNode БЕСПЛАТНЫЙ: 15 RPS, поэтому используем 2 RPS!
        this.limiters.set('rpc-read', new RateLimiter('RPC Read', 10, 10000)); // ✅ ИСПРАВЛЕНО: 10/10sec = 1/sec
        this.limiters.set('rpc-write', new RateLimiter('RPC Write', 5, 10000)); // ✅ ИСПРАВЛЕНО: 5/10sec = 0.5/sec
        this.limiters.set('rpc-account', new RateLimiter('RPC Account', 5, 10000)); // ✅ ИСПРАВЛЕНО: 5/10sec = 0.5/sec

        // ✅ УЛЬТРА-КОНСЕРВАТИВНЫЕ ЛИМИТЫ ДЛЯ MARGINFI (ИЗБЕГАЕМ 429 ОШИБОК!)
        this.limiters.set('marginfi-init', new RateLimiter('MarginFi Init', 5, 10000)); // ✅ ИСПРАВЛЕНО: 5/10sec = 0.5/sec
        this.limiters.set('marginfi-account', new RateLimiter('MarginFi Account', 5, 10000)); // ✅ ИСПРАВЛЕНО: 5/10sec = 0.5/sec

        console.log('🛡️ Rate Limiter Manager инициализирован ДЛЯ ПЛАТНЫХ RPC!');
        console.log('💰 QuickNode: 500 RPS, Helius Pro: 100+ RPS');
        this.printStats();
    }

    async executeJupiterQuote(requestFunction) {
        return this.limiters.get('jupiter-quotes').execute(requestFunction);
    }

    async executeJupiterSwap(requestFunction) {
        return this.limiters.get('jupiter-swap').execute(requestFunction);
    }

    async executeJupiterPrice(requestFunction) {
        return this.limiters.get('jupiter-price').execute(requestFunction);
    }

    async executeRpcRead(requestFunction) {
        return this.limiters.get('rpc-read').execute(requestFunction);
    }

    async executeRpcWrite(requestFunction) {
        return this.limiters.get('rpc-write').execute(requestFunction);
    }

    async executeRpcAccount(requestFunction) {
        return this.limiters.get('rpc-account').execute(requestFunction);
    }

    // ✅ НОВЫЕ МЕТОДЫ ДЛЯ MARGINFI
    async executeMarginfiInit(requestFunction) {
        return this.limiters.get('marginfi-init').execute(requestFunction);
    }

    async executeMarginfiAccount(requestFunction) {
        return this.limiters.get('marginfi-account').execute(requestFunction);
    }

    // ✅ УНИВЕРСАЛЬНЫЙ МЕТОД С АВТОМАТИЧЕСКИМ ВЫБОРОМ ЛИМИТЕРА
    async executeWithRateLimit(type, requestFunction) {
        const limiterMap = {
            'RPC_READ': 'rpc-read',
            'RPC_WRITE': 'rpc-write',
            'RPC_ACCOUNT': 'rpc-account',
            'MARGINFI_INIT': 'marginfi-init',
            'MARGINFI_ACCOUNT': 'marginfi-account',
            'JUPITER_QUOTES': 'jupiter-quotes',
            'JUPITER_SWAP': 'jupiter-swap'
        };

        const limiterName = limiterMap[type];
        if (!limiterName) {
            console.log(`⚠️ Неизвестный тип rate limiter: ${type}, используем rpc-read`);
            return this.limiters.get('rpc-read').execute(requestFunction);
        }

        return this.limiters.get(limiterName).execute(requestFunction);
    }

    printStats() {
        console.log('\n📊 RATE LIMITER СТАТИСТИКА:');
        for (const limiter of this.limiters.values()) {
            const stats = limiter.getStats();
            console.log(`   ${stats.name}: ${stats.recentRequests}/${stats.maxRequests} (${stats.utilization}) | Queue: ${stats.queueLength}`);
        }
    }

    startStatsMonitoring() {
        setInterval(() => {
            this.printStats();
        }, 30000); // Каждые 30 секунд
    }
}

// Глобальный экземпляр
const rateLimiterManager = new RateLimiterManager();

module.exports = {
    RateLimiter,
    RateLimiterManager,
    rateLimiterManager
};
