/**
 * 🎯 RAYDIUM CONFIGURATION
 * 
 * Конфигурация для работы с Raydium AMM и CLMM
 */

const { PublicKey } = require('@solana/web3.js');

// 🔥 RAYDIUM PROGRAM IDS
const RAYDIUM_PROGRAMS = {
    // Raydium AMM v4
    AMM_V4: new PublicKey('675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8'),
    
    // Raydium CLMM (Concentrated Liquidity)
    CLMM: new PublicKey('5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1'),
    
    // Raydium CPMM (Constant Product Market Maker)
    CPMM: new PublicKey('CPMMoo8L3F4NbTegBCKVNunggL7H1ZpdTHKxQB5qKP1C'),
    
    // Raydium Authority
    AUTHORITY: new PublicKey('5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1'),
};

// 🏊 ПОПУЛЯРНЫЕ RAYDIUM ПУЛЫ
const RAYDIUM_POOLS = {
    // SOL/USDC пулы
    SOL_USDC_AMM: {
        id: new PublicKey('58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2'),
        baseMint: new PublicKey('So11111111111111111111111111111111111111112'), // SOL
        quoteMint: new PublicKey('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'), // USDC
        type: 'AMM_V4'
    },
    
    SOL_USDC_CLMM: {
        id: new PublicKey('61R1ndXxvsWXXkWSyNkCxnzwd3zUNB8Q2ibmkiLPC8ht'),
        baseMint: new PublicKey('So11111111111111111111111111111111111111112'), // SOL
        quoteMint: new PublicKey('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'), // USDC
        type: 'CLMM'
    },
    
    // SOL/USDT пулы
    SOL_USDT_AMM: {
        id: new PublicKey('7XawhbbxtsRcQA8KTkHT9f9nc6d69UwqCDh6U5EEbEmX'),
        baseMint: new PublicKey('So11111111111111111111111111111111111111112'), // SOL
        quoteMint: new PublicKey('Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB'), // USDT
        type: 'AMM_V4'
    }
};

// 🎯 ТОРГОВЫЕ НАСТРОЙКИ
const RAYDIUM_TRADING_CONFIG = {
    // Размеры позиций
    POSITION_SIZES: {
        SMALL: 1000,      // $1,000
        MEDIUM: 5000,     // $5,000
        LARGE: 10000,     // $10,000
        XLARGE: 50000     // $50,000
    },
    
    // Настройки проскальзывания
    SLIPPAGE: {
        LOW: 0.1,         // 0.1%
        MEDIUM: 0.5,      // 0.5%
        HIGH: 1.0,        // 1.0%
        EXTREME: 3.0      // 3.0%
    },
    
    // Лимиты транзакций
    LIMITS: {
        MAX_ACCOUNTS: 64,           // Максимум аккаунтов в транзакции
        MAX_TRANSACTION_SIZE: 1232, // Максимальный размер транзакции в байтах
        MAX_COMPUTE_UNITS: 1400000, // Максимум compute units
        PRIORITY_FEE: 0.0001        // Приоритетная комиссия в SOL
    }
};

// 🔧 ТЕХНИЧЕСКИЕ НАСТРОЙКИ
const RAYDIUM_TECHNICAL_CONFIG = {
    // RPC настройки
    RPC: {
        TIMEOUT: 30000,           // 30 секунд
        RETRY_COUNT: 3,           // 3 попытки
        RETRY_DELAY: 1000         // 1 секунда между попытками
    },
    
    // Кэш настройки
    CACHE: {
        POOL_DATA_TTL: 60000,     // 1 минута
        PRICE_DATA_TTL: 5000,     // 5 секунд
        ACCOUNT_DATA_TTL: 30000   // 30 секунд
    },
    
    // Мониторинг
    MONITORING: {
        HEALTH_CHECK_INTERVAL: 10000,  // 10 секунд
        PRICE_UPDATE_INTERVAL: 1000,   // 1 секунда
        BALANCE_CHECK_INTERVAL: 30000  // 30 секунд
    }
};

// 🎭 СТРАТЕГИИ АРБИТРАЖА
const RAYDIUM_ARBITRAGE_STRATEGIES = {
    // Арбитраж между AMM и CLMM
    AMM_CLMM_ARBITRAGE: {
        enabled: true,
        minProfitUSD: 5,          // Минимальная прибыль $5
        maxPositionUSD: 10000,    // Максимальная позиция $10,000
        slippage: 0.5             // 0.5% проскальзывание
    },
    
    // Арбитраж между разными пулами
    CROSS_POOL_ARBITRAGE: {
        enabled: true,
        minProfitUSD: 10,         // Минимальная прибыль $10
        maxPositionUSD: 25000,    // Максимальная позиция $25,000
        slippage: 1.0             // 1.0% проскальзывание
    },
    
    // Flash loan арбитраж
    FLASH_LOAN_ARBITRAGE: {
        enabled: false,           // Пока отключен
        minProfitUSD: 50,         // Минимальная прибыль $50
        maxPositionUSD: 100000,   // Максимальная позиция $100,000
        slippage: 2.0             // 2.0% проскальзывание
    }
};

// 🚨 БЕЗОПАСНОСТЬ
const RAYDIUM_SAFETY_CONFIG = {
    // Лимиты потерь
    STOP_LOSS: {
        DAILY_LOSS_LIMIT: 1000,   // $1,000 в день
        TRADE_LOSS_LIMIT: 100,    // $100 за сделку
        CONSECUTIVE_LOSSES: 5     // 5 убыточных сделок подряд
    },
    
    // Проверки безопасности
    SAFETY_CHECKS: {
        VERIFY_POOL_AUTHORITY: true,
        CHECK_POOL_LIQUIDITY: true,
        VALIDATE_PRICE_IMPACT: true,
        MONITOR_SLIPPAGE: true
    }
};

module.exports = {
    RAYDIUM_PROGRAMS,
    RAYDIUM_POOLS,
    RAYDIUM_TRADING_CONFIG,
    RAYDIUM_TECHNICAL_CONFIG,
    RAYDIUM_ARBITRAGE_STRATEGIES,
    RAYDIUM_SAFETY_CONFIG
};
