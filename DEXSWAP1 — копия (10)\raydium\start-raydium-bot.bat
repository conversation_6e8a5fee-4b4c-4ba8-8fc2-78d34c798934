@echo off
echo.
echo ========================================
echo 🚀 RAYDIUM BOT LAUNCHER
echo ========================================
echo.

REM Переход в папку raydium
cd /d "%~dp0"

REM Проверка наличия Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js не найден! Установите Node.js
    pause
    exit /b 1
)

REM Проверка наличия основного файла
if not exist "start-raydium-bot.js" (
    echo ❌ Файл start-raydium-bot.js не найден!
    pause
    exit /b 1
)

REM Проверка наличия .env файла
if not exist ".env" (
    echo ⚠️  Файл .env не найден! Создайте файл с настройками
    echo.
    echo Пример .env файла:
    echo WALLET_PRIVATE_KEY=your_private_key_here
    echo QUICKNODE_RPC_URL=your_rpc_url_here
    echo.
    pause
    exit /b 1
)

echo 🔧 Запуск RAYDIUM BOT...
echo.

REM Запуск бота
node start-raydium-bot.js

echo.
echo 🛑 RAYDIUM BOT остановлен
pause
