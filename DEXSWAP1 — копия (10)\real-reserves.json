{"5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6": {"reserveX": "EYj9xKw6ZszwpyNibHY7JD5o3QgTVrSdcBp1fMJhrR9o", "reserveY": "CoaxzEh8p5YyGLcj36Eo3cUThVJxeKCs7qvLAGDYwBcz", "oracle": "59YuGWPunbchD2mbi9U7qvjWQKQReGeepn4ZSr9zz9Li", "tokenX": "So11111111111111111111111111111111111111112", "tokenY": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"}, "BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y": {"reserveX": "DwZz4S1Z1LBXomzmncQRVKCYhjCqSAMQ6RPKbUAadr7H", "reserveY": "4N22J4vW2juHocTntJNmXywSonYjkndCwahjZ2cYLDgb", "oracle": "ETc6tqgLrr7wXsH8u2QBK1CyXHX3kvV6WQjBz4cf3sCj", "tokenX": "So11111111111111111111111111111111111111112", "tokenY": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"}}