# Immunefi Mass Bug Bounty Scanner Requirements

# Основные зависимости для HTTP запросов и асинхронности
aiohttp>=3.8.0
asyncio-throttle>=1.0.2
requests>=2.28.0

# Парсинг HTML и работа с данными
beautifulsoup4>=4.11.0
lxml>=4.9.0
html5lib>=1.1

# Работа с JSON и данными
ujson>=5.6.0
orjson>=3.8.0

# Логирование и мониторинг
colorlog>=6.7.0
rich>=13.0.0

# Работа с датами и временем
python-dateutil>=2.8.0
pytz>=2022.7

# Математические вычисления и анализ
numpy>=1.24.0
scipy>=1.10.0

# Работа с регулярными выражениями (улучшенная версия)
regex>=2022.10.0

# Криптография и хеширование
cryptography>=38.0.0
hashlib-compat>=1.0.0

# Работа с блокчейнами
web3>=6.0.0
solana>=0.30.0
eth-account>=0.8.0
eth-utils>=2.0.0

# Работа с API и веб-сервисами
httpx>=0.24.0
websockets>=10.4

# Обработка изображений и QR кодов (для некоторых тестов)
Pillow>=9.4.0
qrcode>=7.4.0

# Работа с базами данных (для хранения результатов)
sqlite3-utils>=3.30.0
sqlalchemy>=1.4.0

# Работа с конфигурационными файлами
pyyaml>=6.0
toml>=0.10.0
configparser>=5.3.0

# Утилиты для работы с файлами
pathlib2>=2.3.0
watchdog>=2.2.0

# Сетевые утилиты
dnspython>=2.3.0
ipaddress>=1.0.0

# Работа с прокси и VPN
pysocks>=1.7.0
requests[socks]>=2.28.0

# Параллельная обработка
concurrent-futures>=3.1.0
multiprocessing-logging>=0.3.0

# Валидация данных
pydantic>=1.10.0
marshmallow>=3.19.0

# Работа с временными зонами
tzlocal>=4.2

# Утилиты для тестирования
pytest>=7.2.0
pytest-asyncio>=0.20.0
pytest-mock>=3.10.0

# Форматирование и линтинг кода
black>=22.12.0
flake8>=6.0.0
isort>=5.11.0

# Документация
sphinx>=5.3.0
sphinx-rtd-theme>=1.2.0

# Мониторинг производительности
psutil>=5.9.0
memory-profiler>=0.60.0

# Работа с сетевыми протоколами
scapy>=2.5.0
netaddr>=0.8.0

# Дополнительные утилиты для веб-скрапинга
selenium>=4.7.0
playwright>=1.29.0

# Работа с прокси и ротацией IP
fake-useragent>=1.1.0
rotating-proxies>=0.6.0

# Анализ безопасности
bandit>=1.7.0
safety>=2.3.0

# Работа с Git репозиториями
GitPython>=3.1.0
pygit2>=1.11.0

# Дополнительные криптографические библиотеки
pycryptodome>=3.16.0
ecdsa>=0.18.0

# Работа с JWT токенами
PyJWT>=2.6.0
cryptojwt>=1.8.0

# Анализ сетевого трафика
mitmproxy>=9.0.0
pyshark>=0.6.0

# Работа с Docker (для изолированного тестирования)
docker>=6.0.0
docker-compose>=1.29.0

# Машинное обучение для анализа паттернов
scikit-learn>=1.2.0
tensorflow>=2.11.0
torch>=1.13.0

# Обработка естественного языка
nltk>=3.8.0
spacy>=3.5.0

# Визуализация данных
matplotlib>=3.6.0
seaborn>=0.12.0
plotly>=5.12.0

# Работа с Excel файлами
openpyxl>=3.0.0
xlsxwriter>=3.0.0

# Дополнительные утилиты
tqdm>=4.64.0  # Прогресс-бары
click>=8.1.0  # CLI интерфейс
tabulate>=0.9.0  # Форматирование таблиц
