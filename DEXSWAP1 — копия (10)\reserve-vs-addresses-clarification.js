// 🚨 КРИТИЧЕСКОЕ РАЗЪЯСНЕНИЕ: reserveX/Y vs АДРЕСА ДЛЯ ТРАНСФЕРОВ
// НЕ ПУТАЙТЕ ДАННЫЕ С АДРЕСАМИ!

console.log('🚨 КРИТИЧЕСКОЕ РАЗЪЯСНЕНИЕ: RESERVE vs ADDRESSES'.red.bold);
console.log('═'.repeat(70));

console.log(`
❌ ВАШЕ ПРЕДПОЛОЖЕНИЕ:
   "reserveX/reserveY это адреса для трансферов в транзакции"

✅ РЕАЛЬНОСТЬ:
   reserveX/reserveY - это КОЛИЧЕСТВА токенов, НЕ адреса!

🔍 ДАВАЙТЕ РАЗБЕРЕМ РАЗНИЦУ:
`);

console.log('\n📊 ЧТО ТАКОЕ reserveX/reserveY:');
console.log('═'.repeat(70));

const reserveExplanation = {
    reserveX: {
        name: 'reserveX',
        type: 'ЧИСЛО (BN/BigNumber)',
        description: 'КОЛИЧЕСТВО токена X в bin\'е',
        example: '**********',
        humanReadable: '1.5 SOL',
        purpose: 'Показывает сколько SOL ликвидности в этом bin\'е',
        notAnAddress: true
    },
    reserveY: {
        name: 'reserveY', 
        type: 'ЧИСЛО (BN/BigNumber)',
        description: 'КОЛИЧЕСТВО токена Y в bin\'е',
        example: '************',
        humanReadable: '246.375 USDC',
        purpose: 'Показывает сколько USDC ликвидности в этом bin\'е',
        notAnAddress: true
    }
};

Object.entries(reserveExplanation).forEach(([key, reserve]) => {
    console.log(`\n💧 ${reserve.name}:`);
    console.log(`   🔢 Тип: ${reserve.type}`);
    console.log(`   📝 Описание: ${reserve.description}`);
    console.log(`   💡 Пример: ${reserve.example}`);
    console.log(`   👤 Читаемо: ${reserve.humanReadable}`);
    console.log(`   🎯 Назначение: ${reserve.purpose}`);
    console.log(`   ❌ Это НЕ адрес: ${reserve.notAnAddress ? 'ВЕРНО' : 'НЕВЕРНО'}`);
});

console.log('\n🏦 ЧТО ТАКОЕ АДРЕСА ДЛЯ ТРАНСФЕРОВ:');
console.log('═'.repeat(70));

const addressExplanation = {
    userTokenAccountX: {
        name: 'User Token Account X',
        type: 'ПУБЛИЧНЫЙ КЛЮЧ (PublicKey)',
        description: 'Адрес вашего SOL token account\'а',
        example: 'So11111111111111111111111111111111111111112',
        purpose: 'ОТКУДА берутся токены при swap\'е',
        isAddress: true
    },
    userTokenAccountY: {
        name: 'User Token Account Y',
        type: 'ПУБЛИЧНЫЙ КЛЮЧ (PublicKey)',
        description: 'Адрес вашего USDC token account\'а',
        example: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
        purpose: 'КУДА поступают токены после swap\'а',
        isAddress: true
    },
    poolReserveX: {
        name: 'Pool Reserve X Account',
        type: 'ПУБЛИЧНЫЙ КЛЮЧ (PublicKey)',
        description: 'Адрес SOL vault\'а пула',
        example: '8BnEgHoWFysVcuFFX7QztDmzuH8r5ZFvyP3sYwn1XTh6',
        purpose: 'ГДЕ хранятся SOL токены пула',
        isAddress: true
    },
    poolReserveY: {
        name: 'Pool Reserve Y Account',
        type: 'ПУБЛИЧНЫЙ КЛЮЧ (PublicKey)',
        description: 'Адрес USDC vault\'а пула',
        example: '5uWjwMmyD9N6KriB4dYNFnndUBdAMhZjwSJRdVxCMtdx',
        purpose: 'ГДЕ хранятся USDC токены пула',
        isAddress: true
    }
};

Object.entries(addressExplanation).forEach(([key, addr]) => {
    console.log(`\n🏦 ${addr.name}:`);
    console.log(`   🔑 Тип: ${addr.type}`);
    console.log(`   📝 Описание: ${addr.description}`);
    console.log(`   💡 Пример: ${addr.example}`);
    console.log(`   🎯 Назначение: ${addr.purpose}`);
    console.log(`   ✅ Это адрес: ${addr.isAddress ? 'ВЕРНО' : 'НЕВЕРНО'}`);
});

console.log('\n🔍 ПРАКТИЧЕСКИЙ ПРИМЕР РАЗЛИЧИЙ:');
console.log('═'.repeat(70));

console.log(`
📊 BIN ДАННЫЕ (КОЛИЧЕСТВА):
{
    binId: 8388608,
    price: 164.25,
    reserveX: **********,        // 🔢 КОЛИЧЕСТВО: 1.5 SOL
    reserveY: ************,      // 🔢 КОЛИЧЕСТВО: 246.375 USDC
    liquiditySupply: 5000000000  // 🔢 КОЛИЧЕСТВО: общая ликвидность
}

🏦 АДРЕСА ДЛЯ ТРАНЗАКЦИЙ:
{
    userTokenX: new PublicKey("So11111111111111111111111111111111111111112"),     // 🔑 АДРЕС
    userTokenY: new PublicKey("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"),   // 🔑 АДРЕС
    poolReserveX: new PublicKey("8BnEgHoWFysVcuFFX7QztDmzuH8r5ZFvyP3sYwn1XTh6"), // 🔑 АДРЕС
    poolReserveY: new PublicKey("5uWjwMmyD9N6KriB4dYNFnndUBdAMhZjwSJRdVxCMtdx")  // 🔑 АДРЕС
}

🚨 ВИДИТЕ РАЗНИЦУ?
   reserveX/Y = ЧИСЛА (сколько токенов)
   Адреса = ПУБЛИЧНЫЕ КЛЮЧИ (где токены)
`);

console.log('\n🔧 КАК ЭТО ИСПОЛЬЗУЕТСЯ В SWAP ТРАНЗАКЦИИ:');
console.log('═'.repeat(70));

console.log(`
1️⃣ РАСЧЕТ КОТИРОВКИ (используем reserveX/Y):
   const quote = await dlmmPool.swapQuote(
       amount,
       swapYtoX,
       slippage,
       binArrays  // Содержат reserveX/Y как ЧИСЛА
   );
   
   // swapQuote анализирует:
   // - reserveX: сколько SOL доступно
   // - reserveY: сколько USDC доступно
   // - Рассчитывает price impact
   // - Определяет итоговую сумму

2️⃣ СОЗДАНИЕ ТРАНЗАКЦИИ (используем АДРЕСА):
   const swapTx = await dlmmPool.swap({
       inToken: tokenXMint,           // 🔑 АДРЕС токена
       outToken: tokenYMint,          // 🔑 АДРЕС токена
       inAmount: swapAmount,          // 🔢 КОЛИЧЕСТВО
       lbPair: poolAddress,           // 🔑 АДРЕС пула
       user: wallet.publicKey,        // 🔑 АДРЕС пользователя
       userTokenIn: userTokenAccountX,  // 🔑 АДРЕС вашего account'а
       userTokenOut: userTokenAccountY, // 🔑 АДРЕС вашего account'а
       minOutAmount: quote.minOutAmount // 🔢 КОЛИЧЕСТВО
   });

3️⃣ ВНУТРИ ТРАНЗАКЦИИ СОЗДАЮТСЯ ИНСТРУКЦИИ:
   createTransferInstruction({
       source: userTokenAccountX,      // 🔑 ОТКУДА (ваш адрес)
       destination: poolReserveX,      // 🔑 КУДА (адрес пула)
       owner: wallet.publicKey,        // 🔑 КТО (ваш адрес)
       amount: swapAmount             // 🔢 СКОЛЬКО
   });
`);

console.log('\n🎯 КЛЮЧЕВЫЕ РАЗЛИЧИЯ:');
console.log('═'.repeat(70));

const keyDifferences = [
    {
        aspect: 'ТИП ДАННЫХ',
        reserve: 'Число (BN/BigNumber)',
        address: 'Публичный ключ (PublicKey)'
    },
    {
        aspect: 'НАЗНАЧЕНИЕ',
        reserve: 'Показывает КОЛИЧЕСТВО токенов',
        address: 'Указывает ГДЕ находятся токены'
    },
    {
        aspect: 'ИСПОЛЬЗОВАНИЕ',
        reserve: 'Для расчета котировок и ликвидности',
        address: 'Для создания transfer инструкций'
    },
    {
        aspect: 'ПРИМЕР',
        reserve: '********** (1.5 SOL)',
        address: 'So11111111111111111111111111111111111111112'
    },
    {
        aspect: 'В ТРАНЗАКЦИИ',
        reserve: 'Используется для расчетов',
        address: 'Используется в инструкциях'
    }
];

keyDifferences.forEach((diff, index) => {
    console.log(`\n${index + 1}. 🔍 ${diff.aspect}:`);
    console.log(`   💧 Reserve: ${diff.reserve}`);
    console.log(`   🏦 Address: ${diff.address}`);
});

console.log('\n🚨 ПОЧЕМУ ЭТО ВАЖНО ПОНИМАТЬ:');
console.log('═'.repeat(70));

const importanceReasons = [
    {
        reason: 'ПРАВИЛЬНОЕ ИСПОЛЬЗОВАНИЕ API',
        explanation: 'reserveX/Y нельзя использовать как адреса в транзакциях'
    },
    {
        reason: 'ИЗБЕЖАНИЕ ОШИБОК',
        explanation: 'Попытка использовать число как адрес приведет к краху'
    },
    {
        reason: 'ПОНИМАНИЕ АРХИТЕКТУРЫ',
        explanation: 'Данные о ликвидности ≠ адреса для трансферов'
    },
    {
        reason: 'ОТЛАДКА ПРОБЛЕМ',
        explanation: 'Знание разницы поможет найти источник ошибок'
    }
];

importanceReasons.forEach((item, index) => {
    console.log(`\n${index + 1}. ⚠️ ${item.reason}:`);
    console.log(`   ${item.explanation}`);
});

console.log('\n✅ ПРАВИЛЬНОЕ ПОНИМАНИЕ:');
console.log('═'.repeat(70));

console.log(`
🎯 ИТОГОВОЕ ПОНИМАНИЕ:

1️⃣ reserveX/reserveY:
   • ЭТО ЧИСЛА (количества токенов)
   • Показывают ликвидность в bin'е
   • Используются для расчета котировок
   • НЕ ЯВЛЯЮТСЯ адресами

2️⃣ Адреса для трансферов:
   • ЭТО PublicKey объекты
   • Указывают на token account'ы
   • Используются в transfer инструкциях
   • ПОЛУЧАЮТСЯ отдельно через getAssociatedTokenAddress()

3️⃣ В swap транзакции:
   • reserveX/Y → для расчета quote
   • Адреса → для создания инструкций
   • Это РАЗНЫЕ этапы процесса

🚨 КРИТИЧЕСКИ ВАЖНО НЕ ПУТАТЬ ЭТИ КОНЦЕПЦИИ!

💡 АНАЛОГИЯ:
   reserveX/Y = "В банке есть $1000"
   Адреса = "Банк находится по адресу ул. Пушкина, 10"
   
   Нельзя прийти по адресу "$1000" - это не адрес!
   Нельзя снять деньги в количестве "ул. Пушкина, 10" - это не сумма!
`);

module.exports = {
    reserveExplanation,
    addressExplanation,
    keyDifferences,
    importanceReasons
};
