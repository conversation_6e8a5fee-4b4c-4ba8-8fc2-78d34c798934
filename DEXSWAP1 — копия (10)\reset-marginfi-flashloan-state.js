/**
 * 🔧 СБРОС СОСТОЯНИЯ FLASH LOAN В MARGINFI АККАУНТЕ
 * 
 * Проблема: Аккаунт застрял в состоянии ACCOUNT_IN_FLASHLOAN
 * Решение: Принудительно завершить flash loan через lending_account_end_flashloan
 */

const { Connection, PublicKey, Keypair, Transaction } = require('@solana/web3.js');
const { MarginfiClient, getConfig } = require('@mrgnlabs/marginfi-client-v2');
const { NodeWallet } = require('@mrgnlabs/mrgn-common');
const fs = require('fs');

class MarginFiFlashLoanReset {
  constructor() {
    this.connection = new Connection('https://billowing-empty-patron.solana-mainnet.quiknode.pro/c79212a7508ec6a37353793d9be93c917898e858/', 'confirmed');
    this.wallet = null;
    this.marginfiClient = null;
    this.marginfiAccount = null;
  }

  async initialize() {
    try {
      console.log('🔧 ИНИЦИАЛИЗАЦИЯ СБРОСА FLASH LOAN СОСТОЯНИЯ');
      console.log('═══════════════════════════════════════════════════════════════');

      // Загружаем wallet
      const walletData = JSON.parse(fs.readFileSync('wallet.json', 'utf8'));
      const keypair = Keypair.fromSecretKey(new Uint8Array(walletData));
      this.wallet = new NodeWallet(keypair);
      console.log(`💼 Wallet загружен: ${this.wallet.publicKey.toString()}`);

      // Создаем MarginFi client
      const config = getConfig('production');
      this.marginfiClient = await MarginfiClient.fetch(config, this.wallet, this.connection);
      console.log('✅ MarginFi client создан');

      // Получаем MarginFi аккаунт
      const accounts = await this.marginfiClient.getMarginfiAccountsForAuthority();
      if (accounts.length > 0) {
        this.marginfiAccount = accounts[0];
        console.log(`✅ MarginFi аккаунт: ${this.marginfiAccount.address.toString()}`);
      } else {
        throw new Error('MarginFi аккаунты не найдены');
      }

      return true;

    } catch (error) {
      console.error(`❌ Ошибка инициализации: ${error.message}`);
      return false;
    }
  }

  async checkCurrentState() {
    console.log('\n🔍 ПРОВЕРКА ТЕКУЩЕГО СОСТОЯНИЯ');
    console.log('─────────────────────────────────────────────────────────────');

    try {
      // Получаем raw данные аккаунта
      const accountInfo = await this.connection.getAccountInfo(this.marginfiAccount.address);
      if (!accountInfo) {
        console.log('❌ Не удалось получить данные аккаунта');
        return false;
      }

      const flags = accountInfo.data.readUInt8(0);
      console.log(`🏁 Текущие флаги: ${flags} (0x${flags.toString(16)})`);

      const inFlashLoan = (flags & 1) !== 0;
      console.log(`🔥 В состоянии Flash Loan: ${inFlashLoan ? '✅ ДА' : '❌ НЕТ'}`);

      return inFlashLoan;

    } catch (error) {
      console.error(`❌ Ошибка проверки состояния: ${error.message}`);
      return false;
    }
  }

  async resetFlashLoanState() {
    console.log('\n🔧 СБРОС СОСТОЯНИЯ FLASH LOAN');
    console.log('─────────────────────────────────────────────────────────────');

    try {
      // Проверяем текущее состояние
      const inFlashLoan = await this.checkCurrentState();
      if (!inFlashLoan) {
        console.log('✅ Аккаунт уже не в состоянии flash loan');
        return true;
      }

      console.log('🔧 Создаем lending_account_end_flashloan инструкцию через официальный SDK...');

      // ОФИЦИАЛЬНЫЙ МЕТОД: Используем MarginFi SDK makeEndFlashLoanIx
      const projectedActiveBalances = []; // Пустой массив для сброса состояния

      console.log('📊 Создаем end flash loan инструкцию с пустыми projected balances...');
      const endFlashLoanIx = await this.marginfiAccount.makeEndFlashLoanIx(projectedActiveBalances);

      if (!endFlashLoanIx || !endFlashLoanIx.instructions || endFlashLoanIx.instructions.length === 0) {
        throw new Error('Не удалось создать end flash loan инструкцию через SDK');
      }

      console.log(`✅ Официальная end flash loan инструкция создана: ${endFlashLoanIx.instructions.length} инструкций`);

      // Создаем транзакцию
      const transaction = new Transaction();
      endFlashLoanIx.instructions.forEach(ix => transaction.add(ix));

      console.log('📤 Отправляем транзакцию сброса...');

      // Отправляем транзакцию
      const signature = await this.connection.sendTransaction(transaction, [this.wallet.payer], {
        skipPreflight: true,  // 🔥 НИЗКОУРОВНЕВЫЙ РЕЖИМ!
        preflightCommitment: 'processed'
      });

      console.log(`📝 Транзакция отправлена: ${signature}`);

      // Ждем подтверждения
      console.log('⏳ Ждем подтверждения...');
      const confirmation = await this.connection.confirmTransaction(signature, 'confirmed');

      if (confirmation.value.err) {
        throw new Error(`Транзакция провалена: ${JSON.stringify(confirmation.value.err)}`);
      }

      console.log('✅ Транзакция подтверждена!');

      // Проверяем результат
      await new Promise(resolve => setTimeout(resolve, 2000)); // Ждем 2 секунды
      const newState = await this.checkCurrentState();

      if (!newState) {
        console.log('🎉 УСПЕХ! Flash loan состояние сброшено!');
        return true;
      } else {
        console.log('⚠️ Состояние не изменилось, пробуем альтернативный метод');
        return false;
      }

    } catch (error) {
      console.error(`❌ Ошибка сброса состояния: ${error.message}`);

      if (error.message.includes('6037')) {
        console.log('🚨 ОШИБКА 6037: AccountInFlashloan');
        console.log('💡 Официальная документация: "Account in flashloan. Wait or validate state."');
      } else if (error.message.includes('6038')) {
        console.log('🚨 ОШИБКА 6038: IllegalFlashloan');
        console.log('💡 Официальная документация: "Illegal flashloan. Check parameters and permissions."');
      } else if (error.message.includes('6009')) {
        console.log('🚨 ОШИБКА 6009: RiskEngine rejected');
        console.log('💡 Проблема с health factor или коллатералом');
      }

      return false;
    }
  }

  async alternativeReset() {
    console.log('\n🔄 АЛЬТЕРНАТИВНЫЙ МЕТОД СБРОСА');
    console.log('─────────────────────────────────────────────────────────────');

    try {
      console.log('🧪 Метод 1: Принудительный сброс через SDK с дополнительными аккаунтами...');

      // Альтернативный метод: Используем SDK с дополнительными аккаунтами для health check
      const { PublicKey } = require('@solana/web3.js');

      // Получаем все банки для health check
      const allBanks = this.marginfiClient.banks;
      const bankKeys = Array.from(allBanks.keys()).map(key => new PublicKey(key));

      console.log(`📊 Добавляем ${bankKeys.length} банков для health check...`);

      // Создаем end flash loan инструкцию с банками для health check
      const endFlashLoanIx = await this.marginfiAccount.makeEndFlashLoanIx(bankKeys);

      if (!endFlashLoanIx || !endFlashLoanIx.instructions || endFlashLoanIx.instructions.length === 0) {
        throw new Error('Не удалось создать принудительную end flash loan инструкцию');
      }

      console.log(`✅ Принудительная end flash loan инструкция создана: ${endFlashLoanIx.instructions.length} инструкций`);

      const transaction = new Transaction();
      endFlashLoanIx.instructions.forEach(ix => transaction.add(ix));

      console.log('📤 Отправляем принудительный сброс...');

      const signature = await this.connection.sendTransaction(transaction, [this.wallet.payer], {
        skipPreflight: true, // Пропускаем preflight для принудительного выполнения
        preflightCommitment: 'confirmed'
      });

      console.log(`📝 Транзакция отправлена: ${signature}`);

      const confirmation = await this.connection.confirmTransaction(signature, 'confirmed');

      if (confirmation.value.err) {
        console.log('⚠️ Принудительный метод не сработал, пробуем следующий...');
        return await this.tryEmptyFlashLoan();
      }

      console.log('✅ Принудительный сброс выполнен!');

      // Проверяем результат
      await new Promise(resolve => setTimeout(resolve, 2000));
      const newState = await this.checkCurrentState();

      if (!newState) {
        console.log('🎉 УСПЕХ! Принудительный метод сработал!');
        return true;
      } else {
        console.log('⚠️ Принудительный метод не помог, пробуем пустой flash loan...');
        return await this.tryEmptyFlashLoan();
      }

    } catch (error) {
      console.error(`❌ Принудительный метод провалился: ${error.message}`);
      return await this.tryEmptyFlashLoan();
    }
  }

  async tryEmptyFlashLoan() {
    console.log('\n🧪 Метод 2: Пустой flash loan для автоматического сброса...');

    try {
      // Создаем минимальный flash loan который должен автоматически завершиться
      const emptyFlashLoanTx = await this.marginfiAccount.buildFlashLoanTx({
        ixs: [], // Пустые инструкции
        signers: []
      });

      if (!emptyFlashLoanTx) {
        throw new Error('Не удалось создать пустой flash loan');
      }

      console.log('📤 Отправляем пустой flash loan...');

      const signature = await this.connection.sendTransaction(emptyFlashLoanTx, [this.wallet.payer], {
        skipPreflight: true,  // 🔥 НИЗКОУРОВНЕВЫЙ РЕЖИМ!
        preflightCommitment: 'processed'
      });

      console.log(`📝 Транзакция отправлена: ${signature}`);

      const confirmation = await this.connection.confirmTransaction(signature, 'confirmed');

      if (confirmation.value.err) {
        throw new Error(`Транзакция провалена: ${JSON.stringify(confirmation.value.err)}`);
      }

      console.log('✅ Пустой flash loan выполнен!');

      // Проверяем результат
      await new Promise(resolve => setTimeout(resolve, 2000));
      const newState = await this.checkCurrentState();

      if (!newState) {
        console.log('🎉 УСПЕХ! Пустой flash loan метод сработал!');
        return true;
      } else {
        console.log('⚠️ Пустой flash loan метод не помог');
        return false;
      }

    } catch (error) {
      console.error(`❌ Пустой flash loan метод провалился: ${error.message}`);
      return false;
    }
  }

  async performReset() {
    console.log('\n🚀 НАЧИНАЕМ ПРОЦЕДУРУ СБРОСА');
    console.log('═══════════════════════════════════════════════════════════════');

    // Метод 1: Прямой сброс через end flash loan
    console.log('🔧 МЕТОД 1: Прямой сброс через lending_account_end_flashloan');
    const method1Success = await this.resetFlashLoanState();

    if (method1Success) {
      console.log('✅ Метод 1 успешен!');
      return true;
    }

    // Метод 2: Альтернативный сброс через пустой flash loan
    console.log('\n🔧 МЕТОД 2: Альтернативный сброс через пустой flash loan');
    const method2Success = await this.alternativeReset();

    if (method2Success) {
      console.log('✅ Метод 2 успешен!');
      return true;
    }

    console.log('\n❌ ОБА МЕТОДА НЕ СРАБОТАЛИ');
    console.log('💡 Возможные решения:');
    console.log('   1. Подождать некоторое время и попробовать снова');
    console.log('   2. Создать новый MarginFi аккаунт');
    console.log('   3. Обратиться к команде MarginFi за помощью');

    return false;
  }
}

async function main() {
  console.log('🚀 ЗАПУСК СБРОСА MARGINFI FLASH LOAN СОСТОЯНИЯ');
  console.log('═══════════════════════════════════════════════════════════════');
  console.log('📚 ОСНОВАНО НА ОФИЦИАЛЬНОЙ ДОКУМЕНТАЦИИ MARGINFI V2');
  console.log('🔗 https://docs.marginfi.com/mfi-v2');
  console.log('📖 Ошибка 6037: "Account in flashloan. Wait or validate state."');
  console.log('🛠️ Используем официальную инструкцию lending_account_end_flashloan');
  console.log('═══════════════════════════════════════════════════════════════');

  const reset = new MarginFiFlashLoanReset();

  const initialized = await reset.initialize();
  if (!initialized) {
    console.log('❌ Не удалось инициализировать сброс');
    return;
  }

  // Проверяем текущее состояние
  const inFlashLoan = await reset.checkCurrentState();

  if (!inFlashLoan) {
    console.log('✅ Аккаунт не в состоянии flash loan. Сброс не требуется.');
    return;
  }

  console.log('🔧 Аккаунт в состоянии flash loan (флаг 0x43)');
  console.log('🛠️ Начинаем процедуру сброса флага ACCOUNT_IN_FLASHLOAN...');

  const success = await reset.performReset();

  if (success) {
    console.log('\n🎉 FLASH LOAN СОСТОЯНИЕ УСПЕШНО СБРОШЕНО!');
    console.log('✅ Флаг 0x43 (ACCOUNT_IN_FLASHLOAN) снят');
    console.log('🚀 Теперь можно выполнять новые flash loan операции');
    console.log('💰 328.591 SOL flash loan для арбитража теперь доступен!');
  } else {
    console.log('\n❌ НЕ УДАЛОСЬ СБРОСИТЬ СОСТОЯНИЕ');
    console.log('💡 Рекомендации:');
    console.log('   1. Проверьте, что аккаунт действительно застрял');
    console.log('   2. Убедитесь, что нет активных позиций');
    console.log('   3. Попробуйте позже (возможно временная проблема сети)');
    console.log('   4. Обратитесь к официальной поддержке MarginFi');
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = MarginFiFlashLoanReset;
