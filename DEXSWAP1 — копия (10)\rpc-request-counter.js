/**
 * 🔍 RPC REQUEST COUNTER - ТОЧНЫЙ ЗАМЕР ВСЕХ ЗАПРОСОВ
 */

class RPCRequestCounter {
  constructor() {
    this.requests = new Map(); // endpoint -> count
    this.requestsPerSecond = new Map(); // endpoint -> count per second
    this.requestDetails = []; // детальный лог всех запросов
    this.startTime = Date.now();

    // Сброс счетчика каждую секунду
    setInterval(() => {
      this.resetPerSecondCounters();
    }, 1000);

    // Отчет каждые 5 секунд - ОТКЛЮЧЕН
    // setInterval(() => {
    //   this.printReport();
    // }, 5000);

    // 🔥 ПЕРЕХВАТ HTTP ЗАПРОСОВ ОТКЛЮЧЕН
    // this.setupGlobalHttpInterception();
  }

  /**
   * Безопасная сериализация с поддержкой BigInt
   */
  safeStringify(obj) {
    try {
      return JSON.stringify(obj, (key, value) => {
        if (typeof value === 'bigint') {
          return value.toString();
        }
        return value;
      });
    } catch (error) {
      return '[Serialization Error]';
    }
  }

  /**
   * Регистрируем RPC запрос
   */
  logRequest(endpoint, method, params = null) {
    const timestamp = Date.now();
    const relativeTime = timestamp - this.startTime;

    // Увеличиваем общий счетчик
    if (!this.requests.has(endpoint)) {
      this.requests.set(endpoint, 0);
      this.requestsPerSecond.set(endpoint, 0);
    }

    this.requests.set(endpoint, this.requests.get(endpoint) + 1);
    this.requestsPerSecond.set(endpoint, this.requestsPerSecond.get(endpoint) + 1);

    // Сохраняем детали
    this.requestDetails.push({
      timestamp,
      relativeTime,
      endpoint,
      method,
      params: params ? this.safeStringify(params).substring(0, 100) : null
    });

    // Ограничиваем размер лога
    if (this.requestDetails.length > 1000) {
      this.requestDetails = this.requestDetails.slice(-500);
    }

    // 🔥 ЛОГИРОВАНИЕ RPC ЗАПРОСОВ ОТКЛЮЧЕНО
    // console.log(`🚨 RPC ЗАПРОС: ${method} → ${endpoint} (${this.requests.get(endpoint)} total)`);
  }

  /**
   * 🔥 ГЛОБАЛЬНЫЙ ПЕРЕХВАТ HTTP ЗАПРОСОВ - УДАЛЕН
   * Monkey patching удален для безопасности системы
   */
  setupGlobalHttpInterception() {
    // ❌ MONKEY PATCHING УДАЛЕН - НЕ БЕЗОПАСНО
    console.log('⚠️ HTTP перехват отключен для безопасности');
  }

  /**
   * 🔍 АНАЛИЗ HTTP ЗАПРОСА - УДАЛЕНО
   */
  interceptHttpRequest(protocol, args) {
    // ❌ ФУНКЦИЯ УДАЛЕНА - MONKEY PATCHING НЕ БЕЗОПАСЕН
    console.log('⚠️ HTTP перехват отключен');
  }

  /**
   * Сброс счетчиков за секунду
   */
  resetPerSecondCounters() {
    for (const [endpoint] of this.requestsPerSecond) {
      this.requestsPerSecond.set(endpoint, 0);
    }
  }

  /**
   * Печать отчета
   */
  printReport() {
    const runtime = Math.floor((Date.now() - this.startTime) / 1000);

    console.log('\n🔍 RPC REQUEST REPORT');
    console.log('═'.repeat(60));
    console.log(`⏱️  Runtime: ${runtime}s`);

    let totalRequests = 0;
    for (const [endpoint, count] of this.requests) {
      totalRequests += count;
      const perSecond = this.requestsPerSecond.get(endpoint);
      const avgPerSecond = (count / runtime).toFixed(2);

      console.log(`📡 ${endpoint}:`);
      console.log(`   Total: ${count} requests`);
      console.log(`   Current: ${perSecond}/sec`);
      console.log(`   Average: ${avgPerSecond}/sec`);
    }

    console.log(`\n🎯 TOTAL: ${totalRequests} requests (${(totalRequests/runtime).toFixed(2)}/sec avg)`);

    // Последние 10 запросов
    console.log('\n📋 LAST 10 REQUESTS:');
    const recent = this.requestDetails.slice(-10);
    for (const req of recent) {
      const timeStr = `+${Math.floor(req.relativeTime/1000)}s`;
      console.log(`   ${timeStr}: ${req.method} → ${req.endpoint.split('/').pop()}`);
    }
    console.log('═'.repeat(60));
  }

  /**
   * Получить статистику
   */
  getStats() {
    const runtime = Math.floor((Date.now() - this.startTime) / 1000);
    let totalRequests = 0;

    const stats = {};
    for (const [endpoint, count] of this.requests) {
      totalRequests += count;
      stats[endpoint] = {
        total: count,
        avgPerSecond: (count / runtime).toFixed(2),
        currentPerSecond: this.requestsPerSecond.get(endpoint)
      };
    }

    return {
      runtime,
      totalRequests,
      avgRequestsPerSecond: (totalRequests / runtime).toFixed(2),
      endpoints: stats
    };
  }
}

// Глобальный счетчик
const rpcCounter = new RPCRequestCounter();

module.exports = rpcCounter;
