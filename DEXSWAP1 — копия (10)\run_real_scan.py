#!/usr/bin/env python3
"""
РЕАЛЬНЫЙ запуск сканирования Immunefi программ
БЕЗ ЗАГЛУШЕК И СИМУЛЯЦИЙ!
"""

import asyncio
import json
import time
from datetime import datetime
from immunefi_mass_scanner import ImmunefiBountyParser
from immunefi_vulnerability_tester import ImmunefiBountyTester
from immunefi_prioritizer import ImmunefiBountyPrioritizer

async def real_immunefi_scan():
    """РЕАЛЬНОЕ сканирование Immunefi программ"""
    print("🚀 ЗАПУСК РЕАЛЬНОГО СКАНИРОВАНИЯ IMMUNEFI")
    print("=" * 60)
    print("⚠️  ВНИМАНИЕ: Это РЕАЛЬНОЕ тестирование!")
    print("⚠️  Будут отправляться НАСТОЯЩИЕ запросы!")
    print("⚠️  Используйте ответственно!")
    print("=" * 60)
    
    start_time = time.time()
    
    # Этап 1: Получение РЕАЛЬНЫХ данных программ
    print("\n📡 ЭТАП 1: Получение списка программ Immunefi...")
    
    async with ImmunefiBountyParser() as parser:
        programs = await parser.fetch_bounty_list()
        
        if not programs:
            print("❌ Не удалось получить программы!")
            return
        
        print(f"✅ Получено {len(programs)} РЕАЛЬНЫХ программ")
        
        # Сохранение списка программ
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        programs_file = f"real_programs_{timestamp}.json"
        
        with open(programs_file, 'w', encoding='utf-8') as f:
            json.dump(programs, f, indent=2, ensure_ascii=False)
        
        print(f"💾 Программы сохранены в {programs_file}")
    
    # Этап 2: Приоритизация программ
    print("\n📊 ЭТАП 2: Приоритизация программ...")
    
    prioritizer = ImmunefiBountyPrioritizer()
    prioritizer.load_programs(programs)
    scored_programs = prioritizer.calculate_priority_scores()
    
    # Показываем топ-10 программ
    top_programs = scored_programs[:10]
    print(f"\n🏆 ТОП-10 ПРИОРИТЕТНЫХ ПРОГРАММ:")
    print("-" * 80)
    
    for i, program in enumerate(top_programs, 1):
        print(f"{i:2d}. {program.program_name}")
        print(f"    💰 Оценка награды: {program.reward_score:.3f}")
        print(f"    🎯 Вероятность успеха: {program.success_probability:.1%}")
        print(f"    ⏱️  Время: {program.estimated_time_hours:.1f}ч")
        print()
    
    # Этап 3: РЕАЛЬНОЕ тестирование
    print("\n🔍 ЭТАП 3: РЕАЛЬНОЕ тестирование уязвимостей...")
    print("⚠️  Начинаем отправку НАСТОЯЩИХ запросов!")
    
    # Выбираем топ-5 программ для тестирования
    test_programs = top_programs[:5]
    all_vulnerabilities = []
    
    async with ImmunefiBountyTester() as tester:
        for i, scored_program in enumerate(test_programs, 1):
            # Находим исходные данные программы
            original_program = next(
                (p for p in programs if p.get('name') == scored_program.program_name),
                None
            )
            
            if not original_program:
                continue
            
            print(f"\n[{i}/5] 🎯 Тестирование: {scored_program.program_name}")
            print(f"      URL: {original_program.get('url', 'N/A')}")
            
            try:
                # РЕАЛЬНОЕ тестирование программы
                vulnerabilities = await tester.test_program(original_program)
                
                if vulnerabilities:
                    print(f"      🐛 Найдено {len(vulnerabilities)} потенциальных уязвимостей!")
                    
                    for vuln in vulnerabilities:
                        print(f"         - {vuln.vulnerability_type} ({vuln.severity})")
                    
                    all_vulnerabilities.extend(vulnerabilities)
                else:
                    print(f"      ✅ Уязвимости не найдены")
                
                # Задержка между программами
                await asyncio.sleep(2)
                
            except Exception as e:
                print(f"      ❌ Ошибка тестирования: {e}")
                continue
    
    # Этап 4: Сохранение результатов
    print(f"\n💾 ЭТАП 4: Сохранение результатов...")
    
    # Сохранение уязвимостей
    if all_vulnerabilities:
        vulnerabilities_data = []
        for vuln in all_vulnerabilities:
            vuln_dict = {
                'program_name': vuln.program_name,
                'vulnerability_type': vuln.vulnerability_type,
                'severity': vuln.severity,
                'description': vuln.description,
                'proof_of_concept': vuln.proof_of_concept,
                'impact': vuln.impact,
                'recommendation': vuln.recommendation,
                'contract_address': vuln.contract_address,
                'timestamp': vuln.timestamp.isoformat(),
            }
            vulnerabilities_data.append(vuln_dict)
        
        vulns_file = f"real_vulnerabilities_{timestamp}.json"
        with open(vulns_file, 'w', encoding='utf-8') as f:
            json.dump(vulnerabilities_data, f, indent=2, ensure_ascii=False)
        
        print(f"🐛 Уязвимости сохранены в {vulns_file}")
    
    # Генерация отчета
    report = generate_real_report(programs, all_vulnerabilities, start_time)
    
    report_file = f"real_scan_report_{timestamp}.md"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"📄 Отчет сохранен в {report_file}")
    
    # Финальная статистика
    elapsed_time = time.time() - start_time
    print(f"\n🎉 СКАНИРОВАНИЕ ЗАВЕРШЕНО!")
    print(f"⏱️  Время выполнения: {elapsed_time:.1f} секунд")
    print(f"📊 Протестировано программ: {len(test_programs)}")
    print(f"🐛 Найдено уязвимостей: {len(all_vulnerabilities)}")
    
    if all_vulnerabilities:
        severity_counts = {}
        for vuln in all_vulnerabilities:
            severity_counts[vuln.severity] = severity_counts.get(vuln.severity, 0) + 1
        
        print(f"\n📈 Распределение по серьезности:")
        for severity, count in sorted(severity_counts.items()):
            print(f"   {severity}: {count}")

def generate_real_report(programs, vulnerabilities, start_time):
    """Генерация отчета о реальном сканировании"""
    elapsed_time = time.time() - start_time
    
    report = f"""# ОТЧЕТ О РЕАЛЬНОМ СКАНИРОВАНИИ IMMUNEFI

**Дата:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**Время выполнения:** {elapsed_time:.1f} секунд

## 📊 ОБЩАЯ СТАТИСТИКА

- **Всего программ получено:** {len(programs)}
- **Программ протестировано:** {min(5, len(programs))}
- **Найдено уязвимостей:** {len(vulnerabilities)}

## 🎯 ПРОТЕСТИРОВАННЫЕ ПРОГРАММЫ

"""
    
    # Добавляем информацию о программах
    for i, program in enumerate(programs[:5], 1):
        report += f"### {i}. {program.get('name', 'Unknown')}\n"
        report += f"- **URL:** {program.get('url', 'N/A')}\n"
        report += f"- **Максимальная награда:** {program.get('max_bounty', 'Private')}\n"
        report += f"- **Последнее обновление:** {program.get('last_updated', 'Unknown')}\n"
        
        # Контракты
        contracts = program.get('contracts', [])
        if contracts:
            report += f"- **Контракты:** {', '.join(contracts[:3])}\n"
            if len(contracts) > 3:
                report += f"  (и еще {len(contracts) - 3})\n"
        
        report += "\n"
    
    # Добавляем найденные уязвимости
    if vulnerabilities:
        report += "## 🐛 НАЙДЕННЫЕ УЯЗВИМОСТИ\n\n"
        
        for i, vuln in enumerate(vulnerabilities, 1):
            report += f"### {i}. {vuln.program_name} - {vuln.vulnerability_type}\n"
            report += f"**Серьезность:** {vuln.severity}\n"
            report += f"**Описание:** {vuln.description}\n"
            report += f"**Воздействие:** {vuln.impact}\n"
            report += f"**Рекомендация:** {vuln.recommendation}\n"
            
            if vuln.contract_address:
                report += f"**Контракт:** {vuln.contract_address}\n"
            
            report += f"**Proof of Concept:**\n```\n{vuln.proof_of_concept}\n```\n\n"
    else:
        report += "## ✅ УЯЗВИМОСТИ НЕ НАЙДЕНЫ\n\n"
        report += "В ходе тестирования критических уязвимостей обнаружено не было.\n\n"
    
    # Добавляем рекомендации
    report += """## 💡 РЕКОМЕНДАЦИИ

1. **Продолжить тестирование:** Протестировать больше программ для увеличения покрытия
2. **Углубленный анализ:** Провести ручной анализ найденных уязвимостей
3. **Верификация:** Проверить все найденные уязвимости перед отправкой отчетов
4. **Ответственное раскрытие:** Сообщать об уязвимостях через официальные каналы

## ⚠️ ВАЖНО

Этот отчет содержит результаты автоматизированного сканирования.
Все найденные уязвимости требуют ручной верификации перед отправкой в программы bug bounty.
Используйте результаты ответственно и в соответствии с правилами программ.

---
*Отчет сгенерирован автоматически системой Immunefi Mass Scanner*
"""
    
    return report

async def quick_test():
    """Быстрый тест одной программы"""
    print("🚀 БЫСТРЫЙ ТЕСТ ОДНОЙ ПРОГРАММЫ")
    
    # Тестовая программа (замените на реальную)
    test_program = {
        'name': 'Test Program',
        'url': 'https://immunefi.com/bounty/test/',
        'contracts': ['0x1234567890123456789012345678901234567890'],
        'endpoints': ['https://api.test.com'],
        'max_bounty': '$100K',
    }
    
    async with ImmunefiBountyTester() as tester:
        print(f"🎯 Тестирование: {test_program['name']}")
        
        vulnerabilities = await tester.test_program(test_program)
        
        if vulnerabilities:
            print(f"🐛 Найдено {len(vulnerabilities)} уязвимостей:")
            for vuln in vulnerabilities:
                print(f"  - {vuln.vulnerability_type} ({vuln.severity})")
        else:
            print("✅ Уязвимости не найдены")

def main():
    """Главная функция"""
    print("IMMUNEFI REAL SCANNER")
    print("=" * 40)
    print("1. Полное сканирование")
    print("2. Быстрый тест")
    print("3. Выход")
    
    choice = input("\nВыберите опцию (1-3): ").strip()
    
    if choice == "1":
        print("\n⚠️  ВНИМАНИЕ: Запускается РЕАЛЬНОЕ сканирование!")
        confirm = input("Продолжить? (y/N): ").strip().lower()
        if confirm == 'y':
            asyncio.run(real_immunefi_scan())
        else:
            print("Отменено пользователем")
    
    elif choice == "2":
        asyncio.run(quick_test())
    
    elif choice == "3":
        print("До свидания!")
    
    else:
        print("Неверный выбор!")

if __name__ == "__main__":
    main()
