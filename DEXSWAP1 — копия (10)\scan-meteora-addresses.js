/**
 * 🔍 СКАНИРОВАНИЕ ВСЕХ METEORA АДРЕСОВ ИЗ 3 ПУЛОВ
 * Собираем ВСЕ уникальные адреса для добавления в кастомную ALT таблицу
 */

const { Connection, PublicKey, Keypair } = require('@solana/web3.js');
const fs = require('fs');
require('dotenv').config({ path: '.env.solana' });

// Импортируем наш РАБОТАЮЩИЙ Meteora SDK
const MeteoraHybridImplementation = require('./meteora-hybrid-implementation.js');

async function scanMeteoraPools() {
    console.log('🔍 СКАНИРОВАНИЕ ВСЕХ METEORA АДРЕСОВ ИЗ 3 ПУЛОВ');
    console.log('=' .repeat(80));

    try {
        // 1. Подключение к RPC
        const connection = new Connection(process.env.QUICKNODE_RPC_URL);
        const wallet = Keypair.generate(); // Dummy wallet
        console.log('✅ Подключение к RPC установлено');

        // 2. Инициализация Meteora SDK
        const meteoraSDK = new MeteoraHybridImplementation(connection, wallet);
        console.log('✅ Meteora SDK инициализирован');
        
        // Инициализируем DLMM инстансы
        await meteoraSDK.initializeDLMMInstances();
        console.log('✅ DLMM инстансы инициализированы');

        // 3. Наши 3 Meteora пула
        const pools = [
            {
                name: 'Pool 1',
                address: 'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y'
            },
            {
                name: 'Pool 2', 
                address: '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6'
            },
            {
                name: 'Pool 3',
                address: 'HTvjzsfX3yU6BUodCjZ5vZkUrAxMDTrBs3CJaq43ashR'
            }
        ];

        // 4. Собираем ВСЕ уникальные адреса
        const allUniqueAddresses = new Set();
        const poolDetails = {};

        for (const pool of pools) {
            console.log(`\n${'='.repeat(60)}`);
            console.log(`🎯 СКАНИРОВАНИЕ ${pool.name}: ${pool.address}`);
            console.log(`${'='.repeat(60)}`);

            try {
                // Создаем тестовую swap инструкцию для получения всех адресов
                const testAmount = 100000; // 0.1 SOL
                const minOut = 1;

                console.log(`📋 Создание swap инструкции для ${pool.name}...`);
                const swapResult = await meteoraSDK.createStableSwapInstruction(
                    pool.address, 
                    testAmount, 
                    minOut
                );

                if (!swapResult || !swapResult.instruction) {
                    console.log(`❌ Не удалось создать swap инструкцию для ${pool.name}`);
                    continue;
                }

                const instruction = swapResult.instruction;
                console.log(`✅ Swap инструкция создана: ${instruction.keys.length} аккаунтов`);

                // Собираем все адреса из инструкции
                const poolAddresses = [];
                for (const account of instruction.keys) {
                    const address = account.pubkey.toString();
                    poolAddresses.push(address);
                    allUniqueAddresses.add(address);
                }

                // Сохраняем детали пула
                poolDetails[pool.name] = {
                    poolAddress: pool.address,
                    totalAccounts: instruction.keys.length,
                    addresses: poolAddresses
                };

                console.log(`📊 Собрано адресов из ${pool.name}: ${poolAddresses.length}`);
                
                // Показываем первые 10 адресов
                console.log(`📋 Первые 10 адресов из ${pool.name}:`);
                for (let i = 0; i < Math.min(10, poolAddresses.length); i++) {
                    console.log(`   ${i + 1}. ${poolAddresses[i]}`);
                }

            } catch (error) {
                console.log(`❌ Ошибка сканирования ${pool.name}: ${error.message}`);
                poolDetails[pool.name] = {
                    poolAddress: pool.address,
                    error: error.message
                };
            }
        }

        // 5. Анализируем результаты
        console.log(`\n${'='.repeat(80)}`);
        console.log('📊 РЕЗУЛЬТАТЫ СКАНИРОВАНИЯ:');
        console.log(`${'='.repeat(80)}`);

        console.log(`🔍 Всего уникальных адресов: ${allUniqueAddresses.size}`);
        
        // Конвертируем в массив для удобства
        const uniqueAddressesArray = Array.from(allUniqueAddresses);

        // 6. Фильтруем адреса (убираем системные программы которые уже есть в ALT)
        const systemAddresses = new Set([
            'So11111111111111111111111111111111111111112', // SOL
            'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // USDC
            'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA', // Token Program
            '11111111111111111111111111111111', // System Program
            'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo', // Meteora Program
            'MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr', // Memo Program
            'ComputeBudget111111111111111111111111111111', // Compute Budget
            'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL' // Associated Token Program
        ]);

        const newAddressesToAdd = uniqueAddressesArray.filter(addr => !systemAddresses.has(addr));
        
        console.log(`🔍 Системных адресов (уже в ALT): ${uniqueAddressesArray.length - newAddressesToAdd.length}`);
        console.log(`🎯 НОВЫХ адресов для добавления: ${newAddressesToAdd.length}`);

        // 7. Сохраняем результаты в файл
        const scanResults = {
            timestamp: new Date().toISOString(),
            totalUniqueAddresses: allUniqueAddresses.size,
            systemAddressesFiltered: uniqueAddressesArray.length - newAddressesToAdd.length,
            newAddressesToAdd: newAddressesToAdd.length,
            poolDetails: poolDetails,
            allUniqueAddresses: uniqueAddressesArray,
            newAddressesToAdd: newAddressesToAdd
        };

        const outputFile = 'meteora-addresses-scan.json';
        fs.writeFileSync(outputFile, JSON.stringify(scanResults, null, 2));
        console.log(`✅ Результаты сохранены в: ${outputFile}`);

        // 8. Показываем адреса для добавления
        console.log(`\n📋 НОВЫЕ АДРЕСА ДЛЯ ДОБАВЛЕНИЯ В КАСТОМНУЮ ALT ТАБЛИЦУ:`);
        newAddressesToAdd.forEach((addr, i) => {
            console.log(`   ${i + 1}. ${addr}`);
        });

        // 9. Проверяем лимиты ALT таблицы
        const maxALTSize = 256; // Максимум адресов в ALT таблице
        console.log(`\n📊 АНАЛИЗ ЛИМИТОВ ALT ТАБЛИЦЫ:`);
        console.log(`   Текущий размер кастомной ALT: ~18 адресов`);
        console.log(`   Новых адресов: ${newAddressesToAdd.length}`);
        console.log(`   Итоговый размер: ~${18 + newAddressesToAdd.length}`);
        console.log(`   Лимит ALT таблицы: ${maxALTSize}`);

        if (18 + newAddressesToAdd.length > maxALTSize) {
            console.log('🚨 ПРЕВЫШЕН ЛИМИТ ALT ТАБЛИЦЫ!');
            console.log('💡 Нужно создать дополнительную ALT таблицу или оптимизировать');
        } else {
            console.log('✅ Размер в пределах лимита ALT таблицы');
        }

        console.log(`\n${'='.repeat(80)}`);
        console.log('🎯 СЛЕДУЮЩИЙ ШАГ: Запустите add-meteora-to-alt.js для добавления адресов');
        console.log(`${'='.repeat(80)}`);

        return newAddressesToAdd;

    } catch (error) {
        console.error('❌ Ошибка сканирования:', error.message);
        console.error(error.stack);
        return [];
    }
}

// Запуск сканирования
if (require.main === module) {
    scanMeteoraPools();
}

module.exports = { scanMeteoraPools };
