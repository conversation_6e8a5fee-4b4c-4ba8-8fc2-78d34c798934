#!/usr/bin/env node

/**
 * 📊 SDK VERSION MONITOR
 * 
 * Мониторинг изменений в Meteora DLMM SDK и Program ID
 */

const https = require('https');
const fs = require('fs');
const path = require('path');

class SDKVersionMonitor {
    constructor() {
        // 📦 ОТСЛЕЖИВАЕМЫЕ ПАКЕТЫ
        this.PACKAGES = {
            '@meteora-ag/dlmm': {
                name: '@meteora-ag/dlmm',
                registry: 'https://registry.npmjs.org/@meteora-ag/dlmm',
                currentVersion: '1.5.4',
                critical: true
            },
            '@meteora-ag/dlmm-sdk-public': {
                name: '@meteora-ag/dlmm-sdk-public',
                registry: 'https://registry.npmjs.org/@meteora-ag/dlmm-sdk-public',
                currentVersion: 'unknown',
                critical: false
            }
        };

        // 🔑 ОТСЛЕЖИВАЕМЫЕ PROGRAM IDS
        this.PROGRAM_IDS = {
            METEORA_DLMM: {
                name: '<PERSON>eor<PERSON> DLMM',
                address: 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo',
                source: 'https://docs.meteora.ag/',
                lastChecked: null
            },
            MARGINFI: {
                name: 'MarginFi',
                address: 'MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA',
                source: 'https://docs.marginfi.com/',
                lastChecked: null
            },
            JUPITER_V6: {
                name: 'Jupiter V6',
                address: 'JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4',
                source: 'https://docs.jup.ag/',
                lastChecked: null
            }
        };

        // 📁 ФАЙЛ ИСТОРИИ
        this.HISTORY_FILE = 'sdk-version-history.json';
        this.loadHistory();

        console.log('📊 SDK VERSION MONITOR ИНИЦИАЛИЗИРОВАН');
    }

    /**
     * 📁 ЗАГРУЗКА ИСТОРИИ
     */
    loadHistory() {
        try {
            if (fs.existsSync(this.HISTORY_FILE)) {
                const data = fs.readFileSync(this.HISTORY_FILE, 'utf8');
                this.history = JSON.parse(data);
            } else {
                this.history = {
                    packages: {},
                    programIds: {},
                    lastUpdate: null
                };
            }
        } catch (error) {
            console.error('❌ Ошибка загрузки истории:', error.message);
            this.history = { packages: {}, programIds: {}, lastUpdate: null };
        }
    }

    /**
     * 💾 СОХРАНЕНИЕ ИСТОРИИ
     */
    saveHistory() {
        try {
            this.history.lastUpdate = new Date().toISOString();
            fs.writeFileSync(this.HISTORY_FILE, JSON.stringify(this.history, null, 2));
        } catch (error) {
            console.error('❌ Ошибка сохранения истории:', error.message);
        }
    }

    /**
     * 🌐 HTTP GET ЗАПРОС
     */
    httpGet(url) {
        return new Promise((resolve, reject) => {
            https.get(url, (res) => {
                let data = '';
                res.on('data', (chunk) => data += chunk);
                res.on('end', () => {
                    try {
                        resolve(JSON.parse(data));
                    } catch (error) {
                        resolve(data);
                    }
                });
            }).on('error', reject);
        });
    }

    /**
     * 📦 ПРОВЕРКА ВЕРСИИ NPM ПАКЕТА
     */
    async checkPackageVersion(packageInfo) {
        console.log(`\n📦 ПРОВЕРКА ${packageInfo.name}...`);
        
        try {
            const data = await this.httpGet(packageInfo.registry);
            const latestVersion = data['dist-tags']?.latest;
            
            if (!latestVersion) {
                console.log('   ❌ Не удалось получить версию');
                return null;
            }

            console.log(`   📌 Текущая версия: ${packageInfo.currentVersion}`);
            console.log(`   🆕 Последняя версия: ${latestVersion}`);

            const isUpdated = packageInfo.currentVersion === latestVersion;
            console.log(`   ${isUpdated ? '✅ АКТУАЛЬНО' : '⚠️ ТРЕБУЕТ ОБНОВЛЕНИЯ'}`);

            // Сохраняем в историю
            if (!this.history.packages[packageInfo.name]) {
                this.history.packages[packageInfo.name] = [];
            }

            const historyEntry = {
                date: new Date().toISOString(),
                currentVersion: packageInfo.currentVersion,
                latestVersion: latestVersion,
                isUpdated: isUpdated,
                critical: packageInfo.critical
            };

            this.history.packages[packageInfo.name].push(historyEntry);

            // Показываем изменения
            if (!isUpdated) {
                console.log('   📋 CHANGELOG (последние изменения):');
                const versionInfo = data.versions[latestVersion];
                if (versionInfo?.description) {
                    console.log(`      ${versionInfo.description}`);
                }
                if (versionInfo?.publishedAt) {
                    console.log(`      Опубликовано: ${versionInfo.publishedAt}`);
                }
            }

            return {
                package: packageInfo.name,
                currentVersion: packageInfo.currentVersion,
                latestVersion: latestVersion,
                isUpdated: isUpdated,
                critical: packageInfo.critical,
                updateRequired: !isUpdated
            };

        } catch (error) {
            console.error(`   ❌ Ошибка проверки ${packageInfo.name}:`, error.message);
            return null;
        }
    }

    /**
     * 🔑 ПРОВЕРКА PROGRAM ID (упрощенная)
     */
    async checkProgramId(programInfo) {
        console.log(`\n🔑 ПРОВЕРКА ${programInfo.name}...`);
        
        // В реальности нужно проверять через RPC или документацию
        // Пока просто логируем текущий адрес
        console.log(`   📍 Адрес: ${programInfo.address}`);
        console.log(`   📚 Источник: ${programInfo.source}`);
        console.log(`   ✅ АДРЕС АКТУАЛЕН (проверка вручную)`);

        // Сохраняем в историю
        if (!this.history.programIds[programInfo.name]) {
            this.history.programIds[programInfo.name] = [];
        }

        const historyEntry = {
            date: new Date().toISOString(),
            address: programInfo.address,
            source: programInfo.source,
            status: 'CHECKED'
        };

        this.history.programIds[programInfo.name].push(historyEntry);

        return {
            program: programInfo.name,
            address: programInfo.address,
            status: 'ACTIVE',
            lastChecked: new Date().toISOString()
        };
    }

    /**
     * 📊 ПОЛНАЯ ПРОВЕРКА ВСЕХ КОМПОНЕНТОВ
     */
    async runFullCheck() {
        console.log('📊 ЗАПУСК ПОЛНОЙ ПРОВЕРКИ SDK И PROGRAM IDS');
        console.log('=' .repeat(80));

        const results = {
            packages: [],
            programIds: [],
            summary: {
                packagesChecked: 0,
                packagesOutdated: 0,
                criticalUpdates: 0,
                programIdsChecked: 0
            }
        };

        // 1. Проверка NPM пакетов
        console.log('\n1️⃣ ПРОВЕРКА NPM ПАКЕТОВ');
        for (const [key, packageInfo] of Object.entries(this.PACKAGES)) {
            const result = await this.checkPackageVersion(packageInfo);
            if (result) {
                results.packages.push(result);
                results.summary.packagesChecked++;
                
                if (result.updateRequired) {
                    results.summary.packagesOutdated++;
                    if (result.critical) {
                        results.summary.criticalUpdates++;
                    }
                }
            }
        }

        // 2. Проверка Program IDs
        console.log('\n2️⃣ ПРОВЕРКА PROGRAM IDS');
        for (const [key, programInfo] of Object.entries(this.PROGRAM_IDS)) {
            const result = await this.checkProgramId(programInfo);
            if (result) {
                results.programIds.push(result);
                results.summary.programIdsChecked++;
            }
        }

        // 3. Сохранение истории
        this.saveHistory();

        // 4. Итоги
        console.log('\n📊 ИТОГИ ПРОВЕРКИ:');
        console.log('=' .repeat(50));
        console.log(`📦 Пакетов проверено: ${results.summary.packagesChecked}`);
        console.log(`⚠️ Требуют обновления: ${results.summary.packagesOutdated}`);
        console.log(`🚨 Критических обновлений: ${results.summary.criticalUpdates}`);
        console.log(`🔑 Program IDs проверено: ${results.summary.programIdsChecked}`);

        // 5. Рекомендации
        console.log('\n💡 РЕКОМЕНДАЦИИ:');
        if (results.summary.criticalUpdates > 0) {
            console.log('   🚨 КРИТИЧНО: Обновите критические пакеты немедленно!');
            results.packages.forEach(pkg => {
                if (pkg.critical && pkg.updateRequired) {
                    console.log(`      npm install ${pkg.package}@${pkg.latestVersion}`);
                }
            });
        }
        
        if (results.summary.packagesOutdated > 0) {
            console.log('   📦 Рекомендуется обновить устаревшие пакеты');
        }
        
        if (results.summary.packagesOutdated === 0) {
            console.log('   ✅ Все пакеты актуальны!');
        }

        console.log('   📁 История сохранена в sdk-version-history.json');

        return results;
    }

    /**
     * 📋 ПОКАЗАТЬ ИСТОРИЮ ИЗМЕНЕНИЙ
     */
    showHistory() {
        console.log('\n📋 ИСТОРИЯ ИЗМЕНЕНИЙ:');
        console.log('=' .repeat(50));

        // История пакетов
        console.log('\n📦 NPM ПАКЕТЫ:');
        Object.entries(this.history.packages).forEach(([packageName, history]) => {
            console.log(`\n   ${packageName}:`);
            history.slice(-3).forEach(entry => {
                const date = new Date(entry.date).toLocaleDateString();
                const status = entry.isUpdated ? '✅' : '⚠️';
                console.log(`      ${date}: ${entry.currentVersion} → ${entry.latestVersion} ${status}`);
            });
        });

        // История Program IDs
        console.log('\n🔑 PROGRAM IDS:');
        Object.entries(this.history.programIds).forEach(([programName, history]) => {
            console.log(`\n   ${programName}:`);
            history.slice(-3).forEach(entry => {
                const date = new Date(entry.date).toLocaleDateString();
                console.log(`      ${date}: ${entry.address} (${entry.status})`);
            });
        });
    }

    /**
     * 🔄 АВТОМАТИЧЕСКИЙ МОНИТОРИНГ
     */
    async startAutoMonitoring(intervalHours = 24) {
        console.log(`\n🔄 ЗАПУСК АВТОМАТИЧЕСКОГО МОНИТОРИНГА (каждые ${intervalHours}ч)...`);
        
        const intervalMs = intervalHours * 60 * 60 * 1000;
        
        // Первая проверка
        await this.runFullCheck();
        
        // Периодические проверки
        setInterval(async () => {
            console.log('\n⏰ АВТОМАТИЧЕСКАЯ ПРОВЕРКА...');
            await this.runFullCheck();
        }, intervalMs);
        
        console.log('   ✅ Автоматический мониторинг запущен');
    }
}

// 🧪 ТЕСТОВЫЙ ЗАПУСК
if (require.main === module) {
    async function runMonitoring() {
        const monitor = new SDKVersionMonitor();
        
        // Показываем историю
        monitor.showHistory();
        
        // Запускаем полную проверку
        await monitor.runFullCheck();
        
        // Опционально: запуск автоматического мониторинга
        // await monitor.startAutoMonitoring(24);
    }
    
    runMonitoring().catch(console.error);
}

module.exports = SDKVersionMonitor;
