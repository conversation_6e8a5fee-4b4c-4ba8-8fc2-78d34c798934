/**
 * 🚀 СКРИПТ ДЛЯ ПЕРВОНАЧАЛЬНОЙ НАСТРОЙКИ JUPITER ALT ТАБЛИЦ
 * Запускается ОДИН РАЗ для получения и сохранения Jupiter ALT таблиц
 */

const JupiterALTManager = require('./jupiter-alt-manager');
const { Connection, Keypair } = require('@solana/web3.js');

async function setupJupiterALT() {
    console.log('🚀 НАСТРОЙКА JUPITER ALT ТАБЛИЦ ДЛЯ SOL/USDC ПАР...');
    console.log('=' .repeat(60));

    // Создаем dummy connection и wallet для получения ALT таблиц
    const connection = new Connection('https://api.mainnet-beta.solana.com');
    const wallet = Keypair.generate();

    const jupiterALT = new JupiterALTManager(connection, wallet);
    
    try {
        // 1. Получаем Jupiter ALT таблицы
        console.log('📡 Шаг 1: Получение Jupiter ALT таблиц...');
        const altAddresses = await jupiterALT.updateJupiterALTTables();
        
        if (altAddresses.length === 0) {
            console.log('❌ Не удалось получить Jupiter ALT таблицы');
            return;
        }
        
        // 2. Проверяем что сохранилось
        console.log('\n📋 Шаг 2: Проверка сохраненных данных...');
        const info = jupiterALT.getInfo();
        
        console.log(`✅ Сохранено ALT таблиц: ${info.count}`);
        console.log(`📁 Файл: ${info.filePath}`);
        console.log(`💾 Кешировано: ${info.cached ? 'ДА' : 'НЕТ'}`);
        
        // 3. Показываем адреса
        console.log('\n📋 Шаг 3: Список Jupiter ALT адресов:');
        info.addresses.forEach((address, index) => {
            console.log(`   ${index + 1}. ${address}`);
        });
        
        console.log('\n' + '=' .repeat(60));
        console.log('🎉 JUPITER ALT ТАБЛИЦЫ УСПЕШНО НАСТРОЕНЫ!');
        console.log('📝 Теперь они будут загружаться автоматически вместе с другими ALT таблицами');
        
    } catch (error) {
        console.error('❌ Ошибка настройки Jupiter ALT таблиц:', error.message);
        console.error(error.stack);
    }
}

// Запускаем настройку
if (require.main === module) {
    setupJupiterALT();
}

module.exports = setupJupiterALT;
