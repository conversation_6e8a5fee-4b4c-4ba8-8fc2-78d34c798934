#!/usr/bin/env node

/**
 * 🔧 ПРОСТОЙ СКРИПТ ДЛЯ СБРОСА ДОЛГА MARGINFI
 * Владелец: bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV
 */

const { Connection, Keypair } = require('@solana/web3.js');
const { MarginfiClient, getConfig } = require('@mrgnlabs/marginfi-client-v2');
const { NodeWallet } = require('@mrgnlabs/mrgn-common');
const fs = require('fs');

async function main() {
  console.log('🔥 ПРОСТОЙ СБРОС ДОЛГА MARGINFI');
  console.log('👤 Владелец: bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV');
  console.log('═'.repeat(60));

  try {
    // 🔗 Подключение к RPC
    console.log('🔗 Подключение к QuickNode RPC...');
    const connection = new Connection(
      'https://sparkling-patient-market.solana-mainnet.quiknode.pro/146de9e433ca3b8f3473c0bd41742b24429dfc43/',
      'confirmed'
    );
    console.log('✅ RPC подключение создано');

    // 🔑 Загрузка wallet из .env.solana
    console.log('🔑 Загрузка wallet...');
    
    const envPath = '.env.solana';
    if (!fs.existsSync(envPath)) {
      throw new Error('❌ Файл .env.solana не найден');
    }

    const envContent = fs.readFileSync(envPath, 'utf8');
    const privateKeyMatch = envContent.match(/WALLET_PRIVATE_KEY=(.+)/);
    
    if (!privateKeyMatch) {
      throw new Error('❌ WALLET_PRIVATE_KEY не найден в .env.solana');
    }

    const privateKeyBase58 = privateKeyMatch[1].trim();
    console.log(`🔑 Найден приватный ключ, длина: ${privateKeyBase58.length}`);

    // 🔧 Декодирование приватного ключа (base58)
    let keypair;
    try {
      // Простое base58 декодирование
      const decode = (str) => {
        const alphabet = '**********************************************************';
        let decoded = 0n;
        for (let char of str) {
          decoded = decoded * 58n + BigInt(alphabet.indexOf(char));
        }
        const bytes = [];
        while (decoded > 0n) {
          bytes.unshift(Number(decoded % 256n));
          decoded = decoded / 256n;
        }
        return new Uint8Array(bytes);
      };

      const secretKey = decode(privateKeyBase58);
      keypair = Keypair.fromSecretKey(secretKey);
      
    } catch (error) {
      throw new Error(`❌ Ошибка декодирования ключа: ${error.message}`);
    }

    const wallet = new NodeWallet(keypair);
    console.log(`✅ Wallet загружен: ${wallet.publicKey.toString()}`);

    // Проверяем что это правильный кошелек
    const expectedAddress = 'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV';
    if (wallet.publicKey.toString() !== expectedAddress) {
      throw new Error(`❌ Неправильный кошелек! Ожидался: ${expectedAddress}, получен: ${wallet.publicKey.toString()}`);
    }

    // 🏦 Инициализация MarginFi
    console.log('🏦 Инициализация MarginFi client...');
    const config = getConfig('production');
    const client = await MarginfiClient.fetch(config, wallet, connection);
    console.log('✅ MarginFi client инициализирован');

    // 📋 Получение аккаунтов
    console.log('📋 Поиск MarginFi аккаунтов...');
    const accounts = await client.getMarginfiAccountsForAuthority();
    
    if (accounts.length === 0) {
      console.log('❌ MarginFi аккаунты не найдены');
      return;
    }

    console.log(`✅ Найдено аккаунтов: ${accounts.length}`);
    
    for (let i = 0; i < accounts.length; i++) {
      const account = accounts[i];
      console.log(`📊 Аккаунт ${i + 1}: ${account.address.toString()}`);
      
      // Перезагружаем данные аккаунта
      await account.reload();
      
      // Проверяем долги
      const activeBalances = account.activeBalances;
      console.log(`   💰 Активных балансов: ${activeBalances.length}`);
      
      let hasDebt = false;
      
      for (const balance of activeBalances) {
        const bank = client.getBankByPk(balance.bankPk);
        if (!bank) continue;

        const { assets, liabilities } = balance.computeQuantity(bank);
        
        if (liabilities.gt(0)) {
          hasDebt = true;
          const symbol = bank.tokenSymbol || 'UNKNOWN';
          console.log(`   💸 ДОЛГ ${symbol}: ${liabilities.toString()}`);
          
          try {
            console.log(`   🔧 Погашение долга ${symbol}...`);
            
            // ОФИЦИАЛЬНЫЙ СПОСОБ: repay с repayAll=true
            const signature = await account.repay(
              liabilities.toNumber(),
              bank.address,
              true  // repayAll = true
            );
            
            console.log(`   ✅ Долг ${symbol} погашен!`);
            console.log(`   🔗 Транзакция: ${signature}`);
            console.log(`   🌐 https://solscan.io/tx/${signature}`);
            
          } catch (error) {
            console.log(`   ❌ Ошибка погашения ${symbol}: ${error.message}`);
          }
        }
      }
      
      if (!hasDebt) {
        console.log(`   ✅ Долгов нет!`);
      }
    }

    console.log('\n🎉 ПРОВЕРКА ЗАВЕРШЕНА!');

  } catch (error) {
    console.error('❌ ОШИБКА:', error.message);
    process.exit(1);
  }
}

main().catch(console.error);
