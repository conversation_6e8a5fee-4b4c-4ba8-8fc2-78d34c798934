const fs = require('fs');

/**
 * 🔥 ПРОСТОЙ АНАЛИЗ ПОКРЫТИЯ ИЗ ЛОГА
 * ИЗВЛЕКАЕМ ВСЕ КЛЮЧИ ИЗ ПОСЛЕДНЕГО ЗАПУСКА
 */

console.log('🔥 ПРОСТОЙ АНАЛИЗ ПОКРЫТИЯ ИЗ ЛОГА');

// Загружаем ALT конфигурацию
let altConfig;
try {
    altConfig = JSON.parse(fs.readFileSync('final-custom-alt-config.json', 'utf8'));
    console.log('✅ ALT конфигурация загружена');
} catch (error) {
    console.log('❌ Не удалось загрузить ALT конфигурацию:', error.message);
    process.exit(1);
}

// Создаём Set всех адресов в ALT таблицах
const altAddresses = new Set();
altConfig.addresses.forEach(addr => {
    altAddresses.add(addr.address);
});

console.log(`📊 Всего адресов в ALT: ${altAddresses.size}`);

/**
 * 🔍 АНАЛИЗ КЛЮЧЕЙ ИЗ ЛОГА ПОСЛЕДНЕГО ЗАПУСКА
 */
function analyzeFromLog() {
    console.log('\n🔍 АНАЛИЗ КЛЮЧЕЙ ИЗ ЛОГА...');
    
    // Ключи из SWAP инструкций (самые большие)
    const swapKeys = [
        // BUY SOL SWAP (Инструкция 15)
        'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y', // Pool
        'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo', // Program
        'DwZz4S1Z1LBXomzmncQRVKCYhjCqSAMQ6RPKbUAadr7H', // ReserveX
        '4N22J4vW2juHocTntJNmXywSonYjkndCwahjZ2cYLDgb', // ReserveY
        'ETc6tqgLrr7wXsH8u2QBK1CyXHX3kvV6WQjBz4cf3sCj', // Oracle
        'D1ZN9Wj1f4Q1nzjxhC6AuVcwjzch6ZQQ4x4KvKm8Qg7P', // Event Authority
        'Dbw8mACQNf6ywKKLBFBYyKQBms8VJfviGU5jkEEXg1Fy', // Bin Array 1
        '3WMYy9V9LqhkzMbKaFJtLoWNvFRc8T8sK2vKjGhF8xQz', // Bin Array 2
        'HZzNfgApNVVyPc8C4nzjxhC6AuVcwjzch6ZQQ4x4KvKm', // Bin Array 3
        '8A4Crui8NVVyPc8C4nzjxhC6AuVcwjzch6ZQQ4x4KvKm', // Bin Array 4
        
        // SELL SOL SWAP (Инструкция 16)
        '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6', // Pool
        'EYj9xKw6ZszwpyNibHY7JD5o3QgTVrSdcBp1fMJhrR9o', // ReserveX
        'CoaxzEh8p5YyGLcj36Eo3cUThVJxeKCs7qvLAGDYwBcz', // ReserveY
        '59YuGWPunbchD2mbi9U7qvjWQKQReGeepn4ZSr9zz9Li', // Oracle
        '2mGnsXcGNVVyPc8C4nzjxhC6AuVcwjzch6ZQQ4x4KvKm', // Bin Array 1
        'GBDuzqBgNVVyPc8C4nzjxhC6AuVcwjzch6ZQQ4x4KvKm', // Bin Array 2
        '7xmtz8hDNVVyPc8C4nzjxhC6AuVcwjzch6ZQQ4x4KvKm', // Bin Array 3
        '6dvANXbq79ETRLSEpN8jBGjPfAWsbarCnEKnA7GezSzF', // Bin Array 4
        
        // Meteora Positions (из лога)
        '6kL6XehuRvDY25byUKz6i5VFRVWdrbdDiDgbSUg2dfcW', // Position 1 (новый)
        '6gUQUWWkjz6snSEeS7un3vt14zFDRCxx752ypWrjNG6A', // Position 2
        'AcM5ss3Usz2aA122dBjztrpeT8HPKD1u3obffJ4DNaH2', // Position 3
        '4hE4o3aX5BMwkkjX8LUX3NdzJTszUtuJLFUZe2WuDMQF', // Bin Array
        
        // Дополнительные ключи из больших инструкций
        'MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr',   // Memo Program
        'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL',   // Associated Token Program
        'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA',   // Token Program
        '11111111111111111111111111111111',               // System Program
        'So11111111111111111111111111111111111111112',    // WSOL
        'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'    // USDC
    ];
    
    console.log(`🔍 Анализируем ${swapKeys.length} ключей из больших инструкций`);
    
    const analysis = {
        totalKeys: swapKeys.length,
        coveredKeys: [],
        uncoveredKeys: [],
        dynamicKeys: []
    };
    
    swapKeys.forEach((key, index) => {
        const isCovered = altAddresses.has(key);
        const isDynamic = key.includes('11111111111111111111111111111111') || 
                         key === 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA' ||
                         key === 'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL' ||
                         key === 'MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr';
        
        const keyInfo = {
            index: index + 1,
            address: key,
            shortAddress: key.slice(0,8) + '...' + key.slice(-8),
            isCovered: isCovered,
            isDynamic: isDynamic,
            category: getKeyCategory(key)
        };
        
        if (isDynamic) {
            analysis.dynamicKeys.push(keyInfo);
        } else if (isCovered) {
            analysis.coveredKeys.push(keyInfo);
        } else {
            analysis.uncoveredKeys.push(keyInfo);
        }
        
        const status = isCovered ? '✅' : (isDynamic ? '🔄' : '❌');
        console.log(`${status} ${index + 1}: ${keyInfo.shortAddress} (${keyInfo.category})`);
    });
    
    // Выводим статистику
    console.log('\n📊 СТАТИСТИКА ПОКРЫТИЯ:');
    console.log(`📋 Всего ключей: ${analysis.totalKeys}`);
    console.log(`✅ Покрыто ALT: ${analysis.coveredKeys.length}`);
    console.log(`❌ НЕ покрыто: ${analysis.uncoveredKeys.length}`);
    console.log(`🔄 Динамические: ${analysis.dynamicKeys.length}`);
    
    const staticKeys = analysis.totalKeys - analysis.dynamicKeys.length;
    const coveragePercent = staticKeys > 0 ? Math.round((analysis.coveredKeys.length / staticKeys) * 100) : 100;
    console.log(`📈 Покрытие статических: ${coveragePercent}%`);
    
    // Детальный список непокрытых
    if (analysis.uncoveredKeys.length > 0) {
        console.log('\n🚨 НЕПОКРЫТЫЕ СТАТИЧЕСКИЕ КЛЮЧИ:');
        analysis.uncoveredKeys.forEach(key => {
            console.log(`   ❌ ${key.shortAddress} (${key.category})`);
            console.log(`      Полный адрес: ${key.address}`);
        });
        
        // Создаём файл для добавления в ALT
        const keysToAdd = analysis.uncoveredKeys.map(key => ({
            address: key.address,
            name: `${key.category} Key`,
            category: key.category
        }));
        
        fs.writeFileSync('uncovered-keys-to-add.json', JSON.stringify({
            timestamp: new Date().toISOString(),
            totalKeys: keysToAdd.length,
            analysis: analysis,
            keysToAdd: keysToAdd
        }, null, 2));
        
        console.log(`\n💾 Непокрытые ключи сохранены в uncovered-keys-to-add.json`);
        console.log(`🔥 ГОТОВО К ДОБАВЛЕНИЮ: ${keysToAdd.length} КЛЮЧЕЙ`);
    }
    
    return analysis;
}

function getKeyCategory(address) {
    if (address.includes('11111111111111111111111111111111')) return 'SYSTEM_PROGRAM';
    if (address === 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA') return 'TOKEN_PROGRAM';
    if (address === 'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL') return 'ASSOCIATED_TOKEN_PROGRAM';
    if (address === 'MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr') return 'MEMO_PROGRAM';
    if (address === 'So11111111111111111111111111111111111111112') return 'WSOL_MINT';
    if (address === 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v') return 'USDC_MINT';
    if (address === 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo') return 'METEORA_PROGRAM';
    if (address.startsWith('BGm1tav') || address.startsWith('5rCf1DM')) return 'METEORA_POOL';
    if (address.includes('Reserve') || address.startsWith('DwZz4S1Z') || address.startsWith('EYj9xKw6')) return 'METEORA_RESERVE';
    if (address.startsWith('ETc6tqgL') || address.startsWith('59YuGWPu')) return 'METEORA_ORACLE';
    if (address.startsWith('6kL6Xehu') || address.startsWith('6gUQUWWk') || address.startsWith('AcM5ss3U')) return 'METEORA_POSITION';
    if (address.includes('Bin') || address.startsWith('Dbw8mACQ') || address.startsWith('2mGnsXcG')) return 'METEORA_BIN_ARRAY';
    if (address.startsWith('D1ZN9Wj1')) return 'METEORA_EVENT_AUTHORITY';
    return 'UNKNOWN';
}

// Запускаем анализ
const result = analyzeFromLog();

console.log('\n🎉 АНАЛИЗ ЗАВЕРШЁН!');
console.log(`📊 Найдено ${result.uncoveredKeys.length} непокрытых ключей для добавления в ALT`);

process.exit(0);
