/**
 * 🔥 ПРОСТОЕ ТОЧНОЕ СРАВНЕНИЕ КЛЮЧЕЙ
 */

const { Connection, PublicKey } = require('@solana/web3.js');
const fs = require('fs');
require('dotenv').config({ path: '.env.solana' });

async function simpleKeysComparison() {
    try {
        console.log('🔥 ПРОСТОЕ ТОЧНОЕ СРАВНЕНИЕ КЛЮЧЕЙ');
        console.log('=' .repeat(80));

        // 1. Подключение к RPC
        const connection = new Connection(process.env.QUICKNODE_RPC_URL);
        console.log('✅ Подключение к QUICKNODE RPC установлено');

        // 2. Загружаем ALT таблицу
        const customALTAddress = new PublicKey('FAeyUf4AdG7vTQ2Q89r9hWBkPdYkykJKG4CxgqArBGXe');
        console.log(`📋 Кастомная ALT таблица: ${customALTAddress.toString()}`);

        const altAccount = await connection.getAddressLookupTable(customALTAddress);
        if (!altAccount || !altAccount.value) {
            throw new Error('ALT таблица не найдена!');
        }

        const altAddresses = altAccount.value.state.addresses.map(addr => addr.toString());
        console.log(`📊 Адресов в ALT: ${altAddresses.length}`);

        // 3. Загружаем 26 непокрытых ключей
        const uncoveredData = JSON.parse(fs.readFileSync('all-24-instructions-uncovered-keys.json', 'utf8'));
        const uncoveredKeys = uncoveredData.keysToAdd.map(key => key.address);
        console.log(`📊 Непокрытых ключей из анализа: ${uncoveredKeys.length}`);

        // 4. ТОЧНОЕ СРАВНЕНИЕ
        console.log('\n🔍 ТОЧНОЕ СРАВНЕНИЕ:');
        console.log('=' .repeat(80));

        let inALT = 0;
        let notInALT = 0;
        const keysToAdd = [];

        uncoveredKeys.forEach((key, index) => {
            const keyData = uncoveredData.keysToAdd[index];
            
            if (altAddresses.includes(key)) {
                console.log(`✅ ${index + 1}. УЖЕ В ALT: ${key.slice(0,8)}...${key.slice(-8)} (${keyData.category})`);
                inALT++;
            } else {
                console.log(`❌ ${index + 1}. НЕ В ALT: ${key.slice(0,8)}...${key.slice(-8)} (${keyData.category})`);
                notInALT++;
                keysToAdd.push(key);
            }
        });

        // 5. ИТОГОВАЯ СТАТИСТИКА
        console.log('\n📊 ИТОГОВАЯ СТАТИСТИКА:');
        console.log('=' .repeat(80));
        console.log(`📋 Всего непокрытых ключей: ${uncoveredKeys.length}`);
        console.log(`✅ Уже в ALT: ${inALT}`);
        console.log(`❌ НЕ в ALT: ${notInALT}`);
        console.log(`📊 Покрытие: ${((inALT / uncoveredKeys.length) * 100).toFixed(1)}%`);
        console.log(`💰 Потенциальная экономия: ${notInALT * 31} байт`);

        // 6. КЛЮЧИ ДЛЯ ДОБАВЛЕНИЯ
        if (keysToAdd.length > 0) {
            console.log('\n🔥 КЛЮЧИ ДЛЯ ДОБАВЛЕНИЯ:');
            console.log('=' .repeat(80));
            keysToAdd.forEach((key, index) => {
                const keyData = uncoveredData.keysToAdd.find(k => k.address === key);
                console.log(`${index + 1}. ${key} (${keyData?.category || 'UNKNOWN'})`);
            });
        }

        // 7. Сохраняем результат
        const result = {
            timestamp: new Date().toISOString(),
            altSize: altAddresses.length,
            analysis: {
                totalUncoveredKeys: uncoveredKeys.length,
                inALT: inALT,
                notInALT: notInALT,
                coveragePercent: parseFloat(((inALT / uncoveredKeys.length) * 100).toFixed(1))
            },
            keysToAdd: keysToAdd,
            byteSavings: notInALT * 31
        };

        fs.writeFileSync('simple-keys-comparison-result.json', JSON.stringify(result, null, 2));
        console.log('\n✅ Результат сохранен в: simple-keys-comparison-result.json');

        console.log(`\n${'='.repeat(80)}`);
        console.log('🎯 ТОЧНОЕ СРАВНЕНИЕ ЗАВЕРШЕНО!');
        console.log(`✅ В ALT: ${inALT}/${uncoveredKeys.length} (${((inALT / uncoveredKeys.length) * 100).toFixed(1)}%)`);
        console.log(`❌ ОСТАЛОСЬ ДОБАВИТЬ: ${notInALT} КЛЮЧЕЙ`);
        console.log(`💰 ЭКОНОМИЯ: ${notInALT * 31} БАЙТ`);
        console.log(`${'='.repeat(80)}`);

    } catch (error) {
        console.error('❌ Ошибка:', error.message);
    }
}

if (require.main === module) {
    simpleKeysComparison();
}

module.exports = { simpleKeysComparison };
