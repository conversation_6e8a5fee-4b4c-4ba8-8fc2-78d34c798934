{"timestamp": "2025-06-25T20:54:06.117Z", "totalBanks": 65, "importantBanksCount": 9, "mainBanks": {"WBTC": {"address": "BKsfDJCMbYep6gr9pq8PsmJbb5XGLHbAJzUV8vmorz7a", "mint": "********************************************", "symbol": "WBTC", "borrowLimit": "**********", "borrowCapacity": "*********.16896628469758934152341208344305199286969845008976", "depositCapacity": "***********.0055196503731437894548934008841599005802498685552", "canBorrow": true}, "USDT": {"address": "HmpMfL8942u22htC4EMiWgLX931g3sacXFR6KjuLgKLV", "mint": "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB", "symbol": "USDT", "borrowLimit": "**************", "borrowCapacity": "**************.8279426768835069728587013342609340294631410308", "depositCapacity": "**************.599106612739658493907643529019880649887195588", "canBorrow": true}, "ETH": {"address": "BkUyfXjbBBALcfZvw76WAFRvYQ21xxMWWeoPtJrUqG3z", "mint": "7vfCXTUXx5WJV5JADk17DUJ4ksgau7utNKj4b963voxs", "symbol": "ETH", "borrowLimit": "***********", "borrowCapacity": "***********.6732529727047773927508477243977301874020279857706", "depositCapacity": "946244832080.79273646202920256807529630724879794152412456224", "canBorrow": true}, "USDC": {"address": "2s37akK2eyBbp8DZgCm7RtsaEz8eJP3Nxd4urLHQv7yB", "mint": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v", "symbol": "USDC", "borrowLimit": "***********0000", "borrowCapacity": "173838121842179.6383999716291303404085407788118882956361448", "depositCapacity": "***************.35825021332193010197298117106394566020592176", "canBorrow": true}, "SOL": {"address": "CCKtUs6Cgwo4aaQUmBPmyoApH2gUDErxNZCAntD6LYGh", "mint": "So11111111111111111111111111111111111111112", "symbol": "SOL", "borrowLimit": "**************0", "borrowCapacity": "***************.197171307714443432127380421963058239996381", "depositCapacity": "****************.6831795449093600091003573410196081182696088", "canBorrow": true}}, "allBorrowableBanks": {"MOBILE": {"address": "2ZScBCNKfE6X6fGcBtB2uBuvZqUE3cjKQmRv8wVXTN5B", "mint": "mb1eu7TzEc71KxDpsmsKoucSSuuoGLv1drys1oP2jh6", "symbol": "MOBILE", "borrowLimit": "**************", "canBorrow": true, "index": 1}, "MOTHER": {"address": "2z8C1CCoJBKMLCNbaMWXuTYKHjcdHQBVth5CHsSQq611", "mint": "3S8qX1MsMqRbiwKg2cQyx7nis1oHMgaCuc9c4VfvVdPN", "symbol": "MOTHER", "borrowLimit": "************", "canBorrow": true, "index": 2}, "hSOL": {"address": "GJCi1uj3kYPZ64puA5sLUiCQfFapxT2xnREzrbDzFkYY", "mint": "he1iusmfkpAdwvxLNGV8Y1iSbj4rUy6yMhEA3fotn9A", "symbol": "hSOL", "borrowLimit": "*************", "canBorrow": true, "index": 3}, "JUP": {"address": "Guu5uBc8k1WK1U2ihGosNaCy57LSgCkpWAabtzQqrQf8", "mint": "JUPyiwrYJFskUPiHa7hkeR8VUtAeFoSYbKedZNsDvCN", "symbol": "JUP", "borrowLimit": "***********", "canBorrow": true, "index": 4}, "sUSD": {"address": "FWZbU8TSPyjyrWQASzujo7FjgF9f3GEkjaFAtbKWqjMH", "mint": "susdabGDNbhrnCa6ncrYo81u4s9GM8ecK2UwMyZiq4X", "symbol": "sUSD", "borrowLimit": "**********000", "canBorrow": true, "index": 5}, "$WIF": {"address": "9dpu8KL5ABYiD3WP2Cnajzg1XaotcJvZspv29Y1Y3tn1", "mint": "EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm", "symbol": "$WIF", "borrowLimit": "**********0", "canBorrow": true, "index": 6}, "SHDW": {"address": "2Ux4yKTTxQf14MzxRPUyybw9akqHC5jnViF1iKWZRxMb", "mint": "SHDWyBxihqiCj6YekG2GUr7wqKLeLAMK1gHZck9pL6y", "symbol": "SHDW", "borrowLimit": "**************", "canBorrow": true, "index": 7}, "JLP": {"address": "Amtw3n7GZe5SWmyhMhaFhDTi39zbTkLeWErBsmZXwpDa", "mint": "27G8MtK7VtTcCHkpASjSDdkWWYfoqT6ggEuKidVJidD4", "symbol": "JLP", "borrowLimit": "***********00", "canBorrow": true, "index": 9}, "USDY": {"address": "5WL5CnjWT71NPgj8iQR2FU5H3FsVci9cnsHSNJzxj788", "mint": "A1KLoBrKBde8Ty9qtNQUtq3C2ortoC3u7twggz7sEto6", "symbol": "USDY", "borrowLimit": "9668000000", "canBorrow": true, "index": 10}, "BODEN": {"address": "5xVGr3pAWDtWPLcf6YsQTjKm6pGqLnJrENQXGajdP2wZ", "mint": "3psH1Mj1f7yUfaD5gh6Zj7epE8hhrMkMETgv5TshQA4o", "symbol": "BODEN", "borrowLimit": "1**************", "canBorrow": true, "index": 12}, "STEP": {"address": "3UwWCseMeKfoVtesjJycA7AYSE65MeKh1wK98FfeG8kM", "mint": "StepAscQoEioFxxWGnh2sLBDFp9d8rvKz2Yp39iDpyT", "symbol": "STEP", "borrowLimit": "***********0000", "canBorrow": true, "index": 13}, "PYUSD": {"address": "8UEiPmgZHXXEDrqLS3oiTxQxTbeYTtPbeMBxAd2XGbpu", "mint": "2b1kV6DkPAnxd5ixfnxCpjxmKwqjjaYmCZfHsFu24GXo", "symbol": "PYUSD", "borrowLimit": "***********000", "canBorrow": true, "index": 16}, "GUAC": {"address": "44digRwKFeyiqDaxJRE6iag4cbXECKjG54v5ozxdu5mu", "mint": "AZsHEMXd36Bj1EMNXhowJajpUXzrKcK57wW4ZGXVa7yR", "symbol": "GUAC", "borrowLimit": "**************000", "canBorrow": true, "index": 17}, "stSOL": {"address": "7TZABdVVzqtGwgtqHM6VS8E34LtFq4dogNvTWEH9QaaM", "mint": "7dHbWXmci3dT8UFYWYZweBLXgycu7Y3iL6trKn1Y7ARj", "symbol": "stSOL", "borrowLimit": "**************", "canBorrow": true, "index": 18}, "bSOL": {"address": "6hS9i46WyTq1KXcoa2Chas2Txh9TJAVr6n1t3tnrE23K", "mint": "bSo13r4TkiE4KumL71LsHTPpL2euBYLFx6h9HP3piy1", "symbol": "bSOL", "borrowLimit": "**************0", "canBorrow": true, "index": 20}, "CLOUD": {"address": "4kNXetv8hSv9PzvzPZzEs1CTH6ARRRi2b8h6jk1ad1nP", "mint": "CLoUDKc4Ane7HeQcPpE3YHnznRxhMimJ4MyaUqyHFzAu", "symbol": "CLOUD", "borrowLimit": "2**************0", "canBorrow": true, "index": 21}, "JitoSOL": {"address": "Bohoc1ikHLD7xKJuzTyiTyCwzaL5N7ggJQu75A8mKYM8", "mint": "J1toso1uCk3RLmjorhTtrVwY9HJ7X8V9yYac6Y7kGCPn", "symbol": "JitoSOL", "borrowLimit": "**************0", "canBorrow": true, "index": 24}, "wstETH": {"address": "9g3Tug2WbDwekghbPn2u3V84tvikAMBZiFbPUfkjwKNH", "mint": "ZScHuTtqZukUrtZS43teTKGs2VqkKL8k4QCouR2n6Uo", "symbol": "wstETH", "borrowLimit": "2000000000", "canBorrow": true, "index": 26}, "WEN": {"address": "7aoit6hVmaqWn2VjhmDo5qev6QXjsJJf4q5RTd7yczZj", "mint": "WENWENvqqNya429ubCdR81ZmD69brwQaaBYY6p3LCpk", "symbol": "WEN", "borrowLimit": "10499790000000", "canBorrow": true, "index": 28}, "Neiro": {"address": "GpNuWCghujQQNyyX1qv2p2r7WpX2hD6UyPnN8rRDGLs", "mint": "CTJf74cTo3cw8acFP1YXF3QpsQUUBGBjh2k2e8xsZ6UL", "symbol": "<PERSON><PERSON>", "borrowLimit": "*************", "canBorrow": true, "index": 29}, "ORE": {"address": "CQVtZK8rpL4M6JQzXxkPhadkVDFChfmdyw9MAXM3oFZT", "mint": "oreoN2tQbHXVaZsr3pf66A48miqcBXCDJozganhEJgz", "symbol": "ORE", "borrowLimit": "42000000000", "canBorrow": true, "index": 31}, "OPOS": {"address": "4TBA2upbfULV5ryM8LxSqreztonu5xLYxN6qBEzHXR5f", "mint": "BqVHWpwUDgMik5gbTciFfozadpE2oZth5bxCDrgbDt52", "symbol": "OPOS", "borrowLimit": "*************", "canBorrow": true, "index": 34}, "BNSOL": {"address": "FVVKPocxQqJNjDTjzvT3HFXte5oarfp29vJ9tqjAPUW4", "mint": "BNso1VUJnh4zcfpZa6986Ea66P6TCp59hvtNJ8b1X85", "symbol": "BNSOL", "borrowLimit": "************0", "canBorrow": true, "index": 35}, "INF": {"address": "AwLRW3aPMMftXEjgWhTkYwM9CGBHdtKecvahCJZBwAqY", "mint": "5oVNBeEEQvYi1cX3ir8Dx5n1P7pdxydbGF2X4TxVusJm", "symbol": "INF", "borrowLimit": "************00", "canBorrow": true, "index": 38}, "JTO": {"address": "EdB7YADw4XUt6wErT8kHGCUok4mnTpWGzPUU9rWDebzb", "mint": "jtojtomepa8beP8AuQc6eXt5FriJwfFMwQx2v2f9mCL", "symbol": "JTO", "borrowLimit": "**************00", "canBorrow": true, "index": 40}, "USDS": {"address": "FDsf8sj6SoV313qrA91yms3u5b3P4hBxEPvanVs8LtJV", "mint": "USDSwr9ApdHk5bvJKMjzff41FfuX8bSxdKcR81vTwcA", "symbol": "USDS", "borrowLimit": "4***********0", "canBorrow": true, "index": 41}, "bonkSOL": {"address": "4ujXMjmbw5nJBmBYVSfQj5ATjRwCitcrsFQaYo7UqNRY", "mint": "BonK1YhkXEGLZzwtcvRTip3gAL9nCeQD7ppZBLXhtTs", "symbol": "bonkSOL", "borrowLimit": "***********0", "canBorrow": true, "index": 44}, "compassSOL": {"address": "F541dsSsc6xYzHfvLfyEuLfM6a8JdwGaVpyWm2GxcJsA", "mint": "Comp4ssDzXcLeu2MnLuGNNFC4cmLPMng8qWHPvzAMU1h", "symbol": "compassSOL", "borrowLimit": "**********00", "canBorrow": true, "index": 48}, "JSOL": {"address": "5wZz2MV3dFJVq3Wp4tBoqrgrSGZqeLCdLE1L4w6okm9g", "mint": "7Q2afV64in6N6SeZsAAB81TJzwDoD6zpqmHkzi9Dcavn", "symbol": "JSOL", "borrowLimit": "**********00", "canBorrow": true, "index": 49}, "WBTC": {"address": "BKsfDJCMbYep6gr9pq8PsmJbb5XGLHbAJzUV8vmorz7a", "mint": "********************************************", "symbol": "WBTC", "borrowLimit": "**********", "canBorrow": true, "index": 50}, "LFG": {"address": "9ojzV5xFHtx2h2GhKRSgCwJK3BLswczdiiLW3hsyRE5c", "mint": "LFG1ezantSY2LPX8jRz2qa31pPEhpwN9msFDzZw4T9Q", "symbol": "LFG", "borrowLimit": "3832886160000000", "canBorrow": true, "index": 51}, "ISC": {"address": "BDo6z3urnxkMBPWP1PTcPDMNnvgTtEqtKtf219exvA87", "mint": "J9BcrQfX4p9D1bvLzRNCbMDv8f44a9LFdeqNE4Yk2WMD", "symbol": "ISC", "borrowLimit": "***********", "canBorrow": true, "index": 54}, "SAMO": {"address": "5HSLxQN34V9jLihfBDwNLguDKWEPDBL7QBG5JKcAQ7ne", "mint": "7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU", "symbol": "SAMO", "borrowLimit": "1662062000000000", "canBorrow": true, "index": 56}, "jucySOL": {"address": "GH7ZQbAP8G4xDXaYb7PcGXnqBDBCMKF7d9XhZCk1w1pG", "mint": "jucy5XJ76pHVvtPZb5TKRcGQExkwit2P5s4vY8UzmpC", "symbol": "jucySOL", "borrowLimit": "**********00", "canBorrow": true, "index": 59}, "HNT": {"address": "JBcir4DPRPYVUpks9hkS1jtHMXejfeBo4xJGv3AYYHg6", "mint": "hntyVP6YFm1Hg25TN9WGLqM12b8TQmcknKrdu1oxWux", "symbol": "HNT", "borrowLimit": "**************0", "canBorrow": true, "index": 61}, "KIN": {"address": "GZK3yC3Kfn1ykFhLryzeKqemRNZ3wpZgWhbh5b5ygGML", "mint": "kinXdEcpDQeHPEuQnqmUgtYykqKGVFq6CeVX5iAHJq6", "symbol": "KIN", "borrowLimit": "480000000000", "canBorrow": true, "index": 62}, "USDG": {"address": "Dj2CwMF3GM7mMT5hcyGXKuYSQ2kQ5zaVCkA1zX1qaTva", "mint": "2u1tszSeqZ3qBWF3uNGPFc8TzMk2tdiwknnRMWGWjGWH", "symbol": "USDG", "borrowLimit": "80000000000", "canBorrow": true, "index": 64}, "tBTC": {"address": "AyeEyCiBU2CzZmxz3f3o3vk2hvMsM5G3t3D6vhLsiFkf", "mint": "6DNSN2BJsaPFdFFc1zP37kkeNe4Usc1Sqkzr9C9vPWcU", "symbol": "tBTC", "borrowLimit": "*********", "canBorrow": true, "index": 65}, "BLZE": {"address": "6Fk3bzhqmUqupk6sN5CbfYMdafvyzDdqDNHW5CsJzq8K", "mint": "BLZEEuZUBVqFhj8adcCFPJvPVCiCyVmh3hkJMrU8KuJA", "symbol": "BLZE", "borrowLimit": "73807292000000000", "canBorrow": true, "index": 68}, "USDT": {"address": "HmpMfL8942u22htC4EMiWgLX931g3sacXFR6KjuLgKLV", "mint": "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB", "symbol": "USDT", "borrowLimit": "**************", "canBorrow": true, "index": 69}, "bbSOL": {"address": "4YipZHMNQjip1LrG3uF2fj1G5ieWQ9QRQRy1jhAWWKUZ", "mint": "Bybit2vBJGhPF52GBdNaQfUJ6ZpThSgHBobjWZpLPb4B", "symbol": "bbSOL", "borrowLimit": "************0", "canBorrow": true, "index": 70}, "TNSR": {"address": "9KbkQsu4EGAeM7ZxvwsZcpxoekZyg5LTk1BF5SAMPXdY", "mint": "TNSRxcUxoT9xBG3de7PiJyTDYu7kskLqcpddxnEJAS6", "symbol": "TNSR", "borrowLimit": "**************0", "canBorrow": true, "index": 71}, "PRCL": {"address": "D1vPUa8JhMpxi5qjZeYLCJvHHQbAZ8pdfXum3AwWAk5H", "mint": "4LLbsb5ReP3yEtYzmXewyGjcir5uXtKFURtaEUVC2AHs", "symbol": "PRCL", "borrowLimit": "**********0", "canBorrow": true, "index": 72}, "DITH": {"address": "9cizJpitym7CGL2QauvgG4BtTvSeADvppUpwGTPRc6De", "mint": "E1kvzJNxShvvWTrudokpzuc789vRiDXfXG3duCuY6ooE", "symbol": "DITH", "borrowLimit": "201657000000000", "canBorrow": true, "index": 75}, "META": {"address": "H6bfRmfZPoxDDs8eoVBgouTPowwyv7opfBbHd5KUmuUz", "mint": "METADDFL6wWMWEoKTFJwcThTbUmtarRJZjRpzUvkxhr", "symbol": "META", "borrowLimit": "63000000000", "canBorrow": true, "index": 76}, "SNS": {"address": "Emb5g6cEbRU2Yq9ZQnKhRDQLNRLSMWdgYRvJrBHzH6yB", "mint": "SNSNkV9zfG5ZKWQs6x4hxvBRV6s8SqMfSGCtECDvdMd", "symbol": "SNS", "borrowLimit": "421229000000000", "canBorrow": true, "index": 77}, "DRIFT": {"address": "DcpTd5URXYj77dxgCRouVJ71E6gBE32rk1ofW2M9boju", "mint": "DriFtupJYLTosbwoN8koMbEYSx54aFAVLddWsbksjwg7", "symbol": "DRIFT", "borrowLimit": "***********", "canBorrow": true, "index": 81}, "mSOL": {"address": "22DcjMZrMwC5Bpa5AGBsmjc5V9VuQrXG6N9ZtdUNyYGE", "mint": "mSoLzYCxHdYgdzU16g5QSh3i5K3z3KZK7ytfqcJm7So", "symbol": "mSOL", "borrowLimit": "**************0", "canBorrow": true, "index": 83}, "RLB": {"address": "BsrjGaXJNmzXBK855wXev2Jb846AbyRCoA6R8TnpfrNM", "mint": "RLBxxFkseAZ4RgJH3Sqn8jXxhmGoz9jWxDNJMh8pL7a", "symbol": "RLB", "borrowLimit": "10000000", "canBorrow": true, "index": 84}, "MNDE": {"address": "5oNLkC42jSSrLER4tYjax99zkaGJegV1FjAtEbw81Xs6", "mint": "MNDEFzGvMt87ueuHvVU9VcTqsAP5b3fTGPsHuuPA5ey", "symbol": "MNDE", "borrowLimit": "122704000000000", "canBorrow": true, "index": 85}, "ETH": {"address": "BkUyfXjbBBALcfZvw76WAFRvYQ21xxMWWeoPtJrUqG3z", "mint": "7vfCXTUXx5WJV5JADk17DUJ4ksgau7utNKj4b963voxs", "symbol": "ETH", "borrowLimit": "***********", "canBorrow": true, "index": 86}, "RENDER": {"address": "EbuSnXdFz1R4VPdaJ96KQQQmeYgZTHSzpNW94Tw1PE3H", "mint": "rndrizKT3MK1iimdxRdWabcF7Zg7AR5T4nud4EkHBof", "symbol": "RENDER", "borrowLimit": "7***********0", "canBorrow": true, "index": 87}, "NOS": {"address": "G1pNtooUWPad3zCJLGAtjD3Zu9K56PrRpmvVB6AED1Tr", "mint": "nosXBVoaCTtYdLvKY6Csb4AC8JCdQKKAaWYtx2ZMoo7", "symbol": "NOS", "borrowLimit": "9615000000", "canBorrow": true, "index": 90}, "UXD": {"address": "BeNBJrAh1tZg5sqgt8D6AWKJLD5KkBrfZvtcgd7EuiAR", "mint": "7kbnvuGBxxj8AG9qp8Scn56muWGaRaFqxg1FsRp3PaFT", "symbol": "UXD", "borrowLimit": "4000000000000", "canBorrow": true, "index": 91}, "Bonk": {"address": "DeyH7QxWvnbbaVB4zFrf4hoq7Q8z1ZT14co42BGwGtfM", "mint": "DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263", "symbol": "Bonk", "borrowLimit": "2640570785700000", "canBorrow": true, "index": 92}, "laineSOL": {"address": "2GDC61nu79ZHQBiwMx2BjarEEDZop81Ym9598a5WcCXJ", "mint": "LAinEtNLgpmCP9Rvsf5Hn8W6EhNiKLZQti1xfWMLy6X", "symbol": "laineSOL", "borrowLimit": "**********00", "canBorrow": true, "index": 93}, "LST": {"address": "DMoqjmsuoru986HgfjqrKEvPv8YBufvBGADHUonkadC5", "mint": "LSTxxxnJzKDFSLr4dUkPcmCf5VyryEqzPLz5j4bpxFp", "symbol": "LST", "borrowLimit": "**************", "canBorrow": true, "index": 94}, "HONEY": {"address": "HWA59PLUXJmgrjGPWE2eH1381Wnz512qocV4PtyqhKqs", "mint": "4vMsoUT2BWatFweudnQM1xedRLfJgJ7hswhcpz4xgBTy", "symbol": "HONEY", "borrowLimit": "5252**********", "canBorrow": true, "index": 95}, "ORCA": {"address": "3Hd1VzDCd8xEzcqj6CHqqjeKdzKQPMczGnkSbpaubf3W", "mint": "orcaEKTdK7LKz57vaAYr9QeNsVEPfiu6QeMU1kektZE", "symbol": "ORCA", "borrowLimit": "**********0", "canBorrow": true, "index": 97}, "picoSOL": {"address": "ARwrmohp8qMCkxQNiq5di4pTnTWhvXhAc7c18tZ877Kg", "mint": "picobAEvs6w7QEknPce34wAE4gknZA9v5tTonnmHYdX", "symbol": "picoSOL", "borrowLimit": "**********000", "canBorrow": true, "index": 98}, "USDC": {"address": "2s37akK2eyBbp8DZgCm7RtsaEz8eJP3Nxd4urLHQv7yB", "mint": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v", "symbol": "USDC", "borrowLimit": "***********0000", "canBorrow": true, "index": 100}, "jupSOL": {"address": "8LaUZadNqtzuCG7iCvZd7d5cbquuYfv19KjAg6GPuuCb", "mint": "jupSoLaHXQiZZTSfEWMTRRgpnyFm8f6sZdosWBjx93v", "symbol": "jupSOL", "borrowLimit": "1*************", "canBorrow": true, "index": 102}, "W": {"address": "EYp4j7oHV2SfEGSE3GJ4MjsCL33CzmqLTdvTCdacQ9uG", "mint": "85VBFQZC9TZkfaptBWjvUw7YbZjy52A6mjtPGjstQAmQ", "symbol": "W", "borrowLimit": "**************", "canBorrow": true, "index": 103}, "PYTH": {"address": "E4td8i8PT2BZkMygzW4MGHCv2KPPs57dvz5W2ZXf9Twu", "mint": "HZ1JovNiVvGrGNiiYvEozEVgZ58xaU3RKwX8eACQBCt3", "symbol": "PYTH", "borrowLimit": "**************", "canBorrow": true, "index": 104}, "SOL": {"address": "CCKtUs6Cgwo4aaQUmBPmyoApH2gUDErxNZCAntD6LYGh", "mint": "So11111111111111111111111111111111111111112", "symbol": "SOL", "borrowLimit": "**************0", "canBorrow": true, "index": 106}}, "importantBanks": {"JUP": {"address": "Guu5uBc8k1WK1U2ihGosNaCy57LSgCkpWAabtzQqrQf8", "mint": "JUPyiwrYJFskUPiHa7hkeR8VUtAeFoSYbKedZNsDvCN", "symbol": "JUP", "borrowLimit": "***********", "borrowCapacity": "-310001.636250847369686994046116860024677504564125447056", "depositCapacity": "**************.000266854047100301825043997771448211692845184", "canBorrow": true, "index": 4}, "stSOL": {"address": "7TZABdVVzqtGwgtqHM6VS8E34LtFq4dogNvTWEH9QaaM", "mint": "7dHbWXmci3dT8UFYWYZweBLXgycu7Y3iL6trKn1Y7ARj", "symbol": "stSOL", "borrowLimit": "**************", "borrowCapacity": "**************.93293293910324920457188046687577321141532528976", "depositCapacity": "***************.8939363957817884280244163244813006192028338496", "canBorrow": true, "index": 18}, "WBTC": {"address": "BKsfDJCMbYep6gr9pq8PsmJbb5XGLHbAJzUV8vmorz7a", "mint": "********************************************", "symbol": "WBTC", "borrowLimit": "**********", "borrowCapacity": "*********.16896628469758934152341208344305199286969845008976", "depositCapacity": "***********.0055196503731437894548934008841599005802498685552", "canBorrow": true, "index": 50}, "BONK": {"address": "BZAm4qGscR8gg5bmWrEq6BTofgaZPbg7Fwfa7rFghEXL", "mint": "6B8hZSupE5mcACmjzozP6C1DR2uaCCtmrGqcYWC6SBCc", "symbol": "BONK", "borrowLimit": "0", "borrowCapacity": "0", "depositCapacity": "2**************0", "canBorrow": false, "index": 66}, "USDT": {"address": "HmpMfL8942u22htC4EMiWgLX931g3sacXFR6KjuLgKLV", "mint": "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB", "symbol": "USDT", "borrowLimit": "**************", "borrowCapacity": "**************.8279426768835069728587013342609340294631410308", "depositCapacity": "**************.599106612739658493907643529019880649887195588", "canBorrow": true, "index": 69}, "mSOL": {"address": "22DcjMZrMwC5Bpa5AGBsmjc5V9VuQrXG6N9ZtdUNyYGE", "mint": "mSoLzYCxHdYgdzU16g5QSh3i5K3z3KZK7ytfqcJm7So", "symbol": "mSOL", "borrowLimit": "**************0", "borrowCapacity": "493977802862044.152047784101680323577120809388046212003202856", "depositCapacity": "1934046697524502.18078065436880422774938921213933086914322441", "canBorrow": true, "index": 83}, "ETH": {"address": "BkUyfXjbBBALcfZvw76WAFRvYQ21xxMWWeoPtJrUqG3z", "mint": "7vfCXTUXx5WJV5JADk17DUJ4ksgau7utNKj4b963voxs", "symbol": "ETH", "borrowLimit": "***********", "borrowCapacity": "***********.6732529727047773927508477243977301874020279857706", "depositCapacity": "946244832080.79273646202920256807529630724879794152412456224", "canBorrow": true, "index": 86}, "USDC": {"address": "2s37akK2eyBbp8DZgCm7RtsaEz8eJP3Nxd4urLHQv7yB", "mint": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v", "symbol": "USDC", "borrowLimit": "***********0000", "borrowCapacity": "173838121842179.6383999716291303404085407788118882956361448", "depositCapacity": "***************.35825021332193010197298117106394566020592176", "canBorrow": true, "index": 100}, "SOL": {"address": "CCKtUs6Cgwo4aaQUmBPmyoApH2gUDErxNZCAntD6LYGh", "mint": "So11111111111111111111111111111111111111112", "symbol": "SOL", "borrowLimit": "**************0", "borrowCapacity": "***************.197171307714443432127380421963058239996381", "depositCapacity": "****************.6831795449093600091003573410196081182696088", "canBorrow": true, "index": 106}}}