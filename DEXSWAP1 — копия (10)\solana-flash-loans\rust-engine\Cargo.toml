[package]
name = "solana-arbitrage-engine"
version = "0.1.0"
edition = "2021"

[lib]
crate-type = ["cdylib", "rlib"]

[dependencies]
# Solana
solana-sdk = "1.17"
solana-client = "1.17"
solana-quic-client = "1.17"

# Async runtime
tokio = { version = "1.0", features = ["full"] }
futures = "0.3"

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Performance
rayon = "1.7"
dashmap = "5.4"
lru = "0.12"
parking_lot = "0.12"

# Networking
reqwest = { version = "0.11", features = ["json"] }

# FFI for Node.js integration
napi = { version = "2.0", features = ["full"] }
napi-derive = "2.0"

# Logging
tracing = "0.1"
tracing-subscriber = "0.3"

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# Time handling
chrono = { version = "0.4", features = ["serde"] }

[build-dependencies]
napi-build = "2.0"
