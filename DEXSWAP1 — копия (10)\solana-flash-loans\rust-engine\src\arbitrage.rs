/*!
 * 🧮 ARBITRAGE CALCULATOR
 * Высокопроизводительный поиск арбитражных возможностей
 */

use std::collections::HashMap;
use anyhow::Result;
use serde::{Deserialize, Serialize};
use rayon::prelude::*;
use crate::price_monitoring::PriceData;

/// 🎯 Арбитражная возможность
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ArbitrageOpportunity {
    pub token: String,
    pub buy_dex: String,
    pub sell_dex: String,
    pub buy_price: f64,
    pub sell_price: f64,
    pub gross_profit_percent: f64,
    pub net_profit_percent: f64,
    pub estimated_profit_usd: f64,
    pub optimal_position_size: f64,
    pub fees: FeeBreakdown,
    pub liquidity_tier: String,
    pub confidence_score: f64,
}

/// 💸 Разбивка комиссий
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FeeBreakdown {
    pub dex_fees_percent: f64,
    pub flash_loan_fee_percent: f64,
    pub network_fee_usd: f64,
    pub slippage_percent: f64,
    pub total_fees_percent: f64,
}

/// 🧮 Калькулятор арбитража
pub struct ArbitrageCalculator {
    min_profit_percent: f64,
    max_position_size: f64,
    fee_config: FeeConfig,
}

/// ⚙️ Конфигурация комиссий
#[derive(Debug, Clone)]
struct FeeConfig {
    dex_fees: HashMap<String, f64>,
    flash_loan_fee: f64,
    network_fee_usd: f64,
    slippage: f64,
}

impl ArbitrageCalculator {
    /// Создание нового калькулятора
    pub fn new() -> Self {
        let mut dex_fees = HashMap::new();
        dex_fees.insert("Jupiter".to_string(), 0.0);    // Агрегатор, комиссия включена
        dex_fees.insert("Orca".to_string(), 0.0004);    // 0.02-0.05% (РЕАЛЬНЫЕ комиссии!)
        dex_fees.insert("Raydium".to_string(), 0.0005); // 0.01-0.05% (РЕАЛЬНЫЕ комиссии!)
        dex_fees.insert("Saber".to_string(), 0.0004);   // 0.04% стейблкоины
        dex_fees.insert("OpenBook V2".to_string(), 0.0005); // 0.05% максимум
        dex_fees.insert("Meteora".to_string(), 0.0004); // 0.01-0.04% (РЕАЛЬНЫЕ комиссии!)
        dex_fees.insert("Lifinity".to_string(), 0.0005); // 0.05% максимум
        dex_fees.insert("Aldrin".to_string(), 0.0005);  // 0.05% максимум

        Self {
            min_profit_percent: 0.3,
            max_position_size: 2000.0,
            fee_config: FeeConfig {
                dex_fees,
                flash_loan_fee: 0.0000, // 0.00% MarginFi (НАВСЕГДА БЕЗ КОМИССИИ!)
                network_fee_usd: 0.007, // $0.007 реальная комиссия Solana
                slippage: 0.0005, // 0.05%
            },
        }
    }

    /// Поиск арбитражных возможностей
    pub async fn find_opportunities(&self, prices: &HashMap<String, PriceData>) -> Result<Vec<ArbitrageOpportunity>> {
        // Группируем цены по токенам
        let mut token_prices: HashMap<String, Vec<&PriceData>> = HashMap::new();

        for price_data in prices.values() {
            token_prices
                .entry(price_data.token.clone())
                .or_insert_with(Vec::new)
                .push(price_data);
        }

        // Параллельный поиск возможностей для каждого токена
        let opportunities: Vec<ArbitrageOpportunity> = token_prices
            .par_iter()
            .flat_map(|(token, token_price_list)| {
                self.find_token_opportunities(token, token_price_list)
            })
            .filter(|opp| opp.net_profit_percent > self.min_profit_percent)
            .collect();

        // Сортируем по прибыльности
        let mut sorted_opportunities = opportunities;
        sorted_opportunities.sort_by(|a, b| {
            b.net_profit_percent.partial_cmp(&a.net_profit_percent).unwrap()
        });

        Ok(sorted_opportunities)
    }

    /// Поиск возможностей для конкретного токена
    fn find_token_opportunities(&self, token: &str, prices: &[&PriceData]) -> Vec<ArbitrageOpportunity> {
        if prices.len() < 2 {
            return Vec::new();
        }

        let mut opportunities = Vec::new();

        // Сравниваем все пары DEX
        for (i, buy_price_data) in prices.iter().enumerate() {
            for sell_price_data in prices.iter().skip(i + 1) {
                // Проверяем арбитраж в обе стороны
                if let Some(opp) = self.calculate_arbitrage_opportunity(
                    token,
                    buy_price_data,
                    sell_price_data,
                ) {
                    opportunities.push(opp);
                }

                if let Some(opp) = self.calculate_arbitrage_opportunity(
                    token,
                    sell_price_data,
                    buy_price_data,
                ) {
                    opportunities.push(opp);
                }
            }
        }

        opportunities
    }

    /// Расчет конкретной арбитражной возможности
    fn calculate_arbitrage_opportunity(
        &self,
        token: &str,
        buy_data: &PriceData,
        sell_data: &PriceData,
    ) -> Option<ArbitrageOpportunity> {
        if buy_data.price >= sell_data.price {
            return None;
        }

        let gross_profit_percent = ((sell_data.price - buy_data.price) / buy_data.price) * 100.0;

        // Расчет комиссий
        let buy_dex_fee = self.fee_config.dex_fees.get(&buy_data.dex).unwrap_or(&0.003);
        let sell_dex_fee = self.fee_config.dex_fees.get(&sell_data.dex).unwrap_or(&0.003);

        let fees = FeeBreakdown {
            dex_fees_percent: (buy_dex_fee + sell_dex_fee) * 100.0,
            flash_loan_fee_percent: self.fee_config.flash_loan_fee * 100.0,
            network_fee_usd: self.fee_config.network_fee_usd,
            slippage_percent: self.fee_config.slippage * 100.0 * 2.0, // Покупка + продажа
            total_fees_percent: 0.0, // Будет рассчитано ниже
        };

        // Расчет оптимального размера позиции
        let optimal_position_size = self.calculate_optimal_position_size(
            buy_data,
            sell_data,
            gross_profit_percent,
        );

        // Расчет сетевых комиссий в процентах от позиции
        let network_fee_percent = (fees.network_fee_usd / optimal_position_size) * 100.0;

        let total_fees_percent = fees.dex_fees_percent
            + fees.flash_loan_fee_percent
            + fees.slippage_percent
            + network_fee_percent;

        let net_profit_percent = gross_profit_percent - total_fees_percent;

        if net_profit_percent <= 0.0 {
            return None;
        }

        let estimated_profit_usd = (optimal_position_size * net_profit_percent) / 100.0;

        // Определение уровня ликвидности
        let liquidity_tier = self.determine_liquidity_tier(buy_data, sell_data);

        // Расчет уверенности
        let confidence_score = self.calculate_confidence_score(buy_data, sell_data, net_profit_percent);

        Some(ArbitrageOpportunity {
            token: token.to_string(),
            buy_dex: buy_data.dex.clone(),
            sell_dex: sell_data.dex.clone(),
            buy_price: buy_data.price,
            sell_price: sell_data.price,
            gross_profit_percent,
            net_profit_percent,
            estimated_profit_usd,
            optimal_position_size,
            fees: FeeBreakdown {
                total_fees_percent,
                ..fees
            },
            liquidity_tier,
            confidence_score,
        })
    }

    /// Расчет оптимального размера позиции
    fn calculate_optimal_position_size(
        &self,
        buy_data: &PriceData,
        sell_data: &PriceData,
        profit_percent: f64,
    ) -> f64 {
        // Базовый размер на основе ликвидности
        let liquidity_factor = (buy_data.liquidity.min(sell_data.liquidity) / 1_000_000.0).min(1.0);
        let base_size = self.max_position_size * liquidity_factor;

        // Корректировка на основе прибыльности
        let profit_multiplier = (profit_percent / self.min_profit_percent).min(2.0);

        (base_size * profit_multiplier).min(self.max_position_size)
    }

    /// Определение уровня ликвидности
    fn determine_liquidity_tier(&self, buy_data: &PriceData, sell_data: &PriceData) -> String {
        let min_liquidity = buy_data.liquidity.min(sell_data.liquidity);

        if min_liquidity > 10_000_000.0 {
            "tier1".to_string()
        } else if min_liquidity > 1_000_000.0 {
            "tier2".to_string()
        } else {
            "tier3".to_string()
        }
    }

    /// Расчет уверенности в возможности
    fn calculate_confidence_score(
        &self,
        buy_data: &PriceData,
        sell_data: &PriceData,
        profit_percent: f64,
    ) -> f64 {
        let mut score = 0.0;

        // Фактор ликвидности (0-40 баллов)
        let min_liquidity = buy_data.liquidity.min(sell_data.liquidity);
        score += (min_liquidity / 10_000_000.0).min(1.0) * 40.0;

        // Фактор прибыльности (0-30 баллов)
        score += (profit_percent / 2.0).min(1.0) * 30.0;

        // Фактор объема (0-20 баллов)
        let min_volume = buy_data.volume_24h.min(sell_data.volume_24h);
        score += (min_volume / 1_000_000.0).min(1.0) * 20.0;

        // Фактор свежести данных (0-10 баллов)
        let now = chrono::Utc::now().timestamp() as u64;
        let max_age = (now - buy_data.timestamp.max(sell_data.timestamp)).max(1);
        let freshness = (60.0 / max_age as f64).min(1.0); // 60 секунд = максимальная свежесть
        score += freshness * 10.0;

        score.min(100.0)
    }

    /// Получение статистики производительности
    pub fn get_performance_stats(&self) -> HashMap<String, String> {
        HashMap::from([
            ("min_profit_percent".to_string(), self.min_profit_percent.to_string()),
            ("max_position_size".to_string(), self.max_position_size.to_string()),
            ("flash_loan_fee".to_string(), format!("{}%", self.fee_config.flash_loan_fee * 100.0)),
            ("network_fee".to_string(), format!("${}", self.fee_config.network_fee_usd)),
            ("slippage".to_string(), format!("{}%", self.fee_config.slippage * 100.0)),
        ])
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_arbitrage_calculator_creation() {
        let calc = ArbitrageCalculator::new();
        assert_eq!(calc.min_profit_percent, 0.3);
        assert_eq!(calc.max_position_size, 2000.0);
    }

    #[test]
    fn test_opportunity_calculation() {
        let calc = ArbitrageCalculator::new();

        let buy_data = PriceData {
            token: "SOL".to_string(),
            dex: "Orca".to_string(),
            price: 100.0,
            timestamp: chrono::Utc::now().timestamp() as u64,
            volume_24h: 1_000_000.0,
            liquidity: 5_000_000.0,
        };

        let sell_data = PriceData {
            token: "SOL".to_string(),
            dex: "Raydium".to_string(),
            price: 101.0,
            timestamp: chrono::Utc::now().timestamp() as u64,
            volume_24h: 800_000.0,
            liquidity: 3_000_000.0,
        };

        let opportunity = calc.calculate_arbitrage_opportunity("SOL", &buy_data, &sell_data);

        assert!(opportunity.is_some());
        let opp = opportunity.unwrap();
        assert_eq!(opp.token, "SOL");
        assert_eq!(opp.buy_dex, "Orca");
        assert_eq!(opp.sell_dex, "Raydium");
        assert!(opp.gross_profit_percent > 0.0);
    }

    #[test]
    fn test_liquidity_tier() {
        let calc = ArbitrageCalculator::new();

        let high_liquidity = PriceData {
            token: "SOL".to_string(),
            dex: "Test".to_string(),
            price: 100.0,
            timestamp: 0,
            volume_24h: 0.0,
            liquidity: 15_000_000.0,
        };

        let low_liquidity = PriceData {
            token: "SOL".to_string(),
            dex: "Test".to_string(),
            price: 100.0,
            timestamp: 0,
            volume_24h: 0.0,
            liquidity: 500_000.0,
        };

        assert_eq!(calc.determine_liquidity_tier(&high_liquidity, &high_liquidity), "tier1");
        assert_eq!(calc.determine_liquidity_tier(&low_liquidity, &low_liquidity), "tier3");
    }
}
