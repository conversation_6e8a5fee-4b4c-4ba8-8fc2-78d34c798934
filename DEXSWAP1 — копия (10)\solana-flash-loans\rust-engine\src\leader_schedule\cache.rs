/*!
 * 💾 LEADER SCHEDULE CACHE
 * Высокопроизводительное кеширование расписаний лидеров
 */

use super::{LeaderSchedule, LeaderPrediction, LeaderScheduleResult};
use dashmap::DashMap;
use lru::LruCache;
use parking_lot::RwLock;
use std::collections::HashMap;
use std::num::NonZeroUsize;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tracing::{debug, info};

/// 💾 Кеш расписаний лидеров
pub struct LeaderCache {
    /// Кеш расписаний по эпохам
    schedule_cache: DashMap<u64, Arc<LeaderSchedule>>,
    /// Кеш предсказаний
    prediction_cache: RwLock<LruCache<u64, Arc<LeaderPrediction>>>,
    /// Кеш лидеров слотов
    slot_leader_cache: DashMap<u64, String>,
    /// Обратный индекс: лидер -> слоты
    leader_slots_index: DashMap<String, Vec<u64>>,
    /// Статистика кеша
    stats: RwLock<CacheStats>,
    /// Конфигурация
    config: CacheConfig,
}

/// 📊 Статистика кеша
#[derive(Debug, Clone)]
pub struct CacheStats {
    /// Количество попаданий в кеш расписаний
    pub schedule_hits: u64,
    /// Количество промахов в кеш расписаний
    pub schedule_misses: u64,
    /// Количество попаданий в кеш предсказаний
    pub prediction_hits: u64,
    /// Количество промахов в кеш предсказаний
    pub prediction_misses: u64,
    /// Количество попаданий в кеш слотов
    pub slot_hits: u64,
    /// Количество промахов в кеш слотов
    pub slot_misses: u64,
    /// Время последнего обновления
    pub last_update: Instant,
    /// Общее количество операций
    pub total_operations: u64,
}

impl Default for CacheStats {
    fn default() -> Self {
        Self {
            schedule_hits: 0,
            schedule_misses: 0,
            prediction_hits: 0,
            prediction_misses: 0,
            slot_hits: 0,
            slot_misses: 0,
            last_update: Instant::now(),
            total_operations: 0,
        }
    }
}

/// ⚙️ Конфигурация кеша
#[derive(Debug, Clone)]
pub struct CacheConfig {
    /// Максимальное количество расписаний в кеше
    pub max_schedules: usize,
    /// Максимальное количество предсказаний в кеше
    pub max_predictions: usize,
    /// Максимальное количество слотов в кеше
    pub max_slots: usize,
    /// TTL для расписаний (секунды)
    pub schedule_ttl_seconds: u64,
    /// TTL для предсказаний (секунды)
    pub prediction_ttl_seconds: u64,
    /// TTL для слотов (секунды)
    pub slot_ttl_seconds: u64,
    /// Интервал очистки устаревших записей (секунды)
    pub cleanup_interval_seconds: u64,
}

impl Default for CacheConfig {
    fn default() -> Self {
        Self {
            max_schedules: 10,
            max_predictions: 1000,
            max_slots: 10000,
            schedule_ttl_seconds: 3600, // 1 час
            prediction_ttl_seconds: 300, // 5 минут
            slot_ttl_seconds: 600, // 10 минут
            cleanup_interval_seconds: 60, // 1 минута
        }
    }
}

impl LeaderCache {
    /// Создание нового кеша
    pub fn new(config: CacheConfig) -> Self {
        let prediction_cache_size = NonZeroUsize::new(config.max_predictions)
            .unwrap_or_else(|| NonZeroUsize::new(1000).unwrap());

        Self {
            schedule_cache: DashMap::new(),
            prediction_cache: RwLock::new(LruCache::new(prediction_cache_size)),
            slot_leader_cache: DashMap::new(),
            leader_slots_index: DashMap::new(),
            stats: RwLock::new(CacheStats::default()),
            config,
        }
    }

    /// Получение расписания из кеша
    pub fn get_schedule(&self, epoch: u64) -> Option<Arc<LeaderSchedule>> {
        let mut stats = self.stats.write();
        stats.total_operations += 1;

        if let Some(schedule) = self.schedule_cache.get(&epoch) {
            stats.schedule_hits += 1;
            debug!("Cache hit for schedule epoch {}", epoch);
            Some(schedule.clone())
        } else {
            stats.schedule_misses += 1;
            debug!("Cache miss for schedule epoch {}", epoch);
            None
        }
    }

    /// Сохранение расписания в кеш
    pub fn put_schedule(&self, schedule: LeaderSchedule) -> LeaderScheduleResult<()> {
        let epoch = schedule.epoch;
        let schedule_arc = Arc::new(schedule);

        // Обновляем кеш расписаний
        self.schedule_cache.insert(epoch, schedule_arc.clone());

        // Обновляем индекс слотов
        self.update_slot_index(&schedule_arc)?;

        // Обновляем обратный индекс
        self.update_leader_index(&schedule_arc)?;

        debug!("Cached schedule for epoch {}", epoch);

        // Очищаем старые записи если превышен лимит
        if self.schedule_cache.len() > self.config.max_schedules {
            self.cleanup_old_schedules();
        }

        Ok(())
    }

    /// Получение предсказания из кеша
    pub fn get_prediction(&self, slot: u64) -> Option<Arc<LeaderPrediction>> {
        let mut prediction_cache = self.prediction_cache.write();
        let mut stats = self.stats.write();
        stats.total_operations += 1;

        if let Some(prediction) = prediction_cache.get(&slot) {
            stats.prediction_hits += 1;
            debug!("Cache hit for prediction slot {}", slot);
            Some(prediction.clone())
        } else {
            stats.prediction_misses += 1;
            debug!("Cache miss for prediction slot {}", slot);
            None
        }
    }

    /// Сохранение предсказания в кеш
    pub fn put_prediction(&self, prediction: LeaderPrediction) -> LeaderScheduleResult<()> {
        let slot = prediction.current_slot;
        let prediction_arc = Arc::new(prediction);

        {
            let mut prediction_cache = self.prediction_cache.write();
            prediction_cache.put(slot, prediction_arc);
        }

        debug!("Cached prediction for slot {}", slot);
        Ok(())
    }

    /// Получение лидера слота из кеша
    pub fn get_slot_leader(&self, slot: u64) -> Option<String> {
        let mut stats = self.stats.write();
        stats.total_operations += 1;

        if let Some(leader) = self.slot_leader_cache.get(&slot) {
            stats.slot_hits += 1;
            debug!("Cache hit for slot leader {}", slot);
            Some(leader.clone())
        } else {
            stats.slot_misses += 1;
            debug!("Cache miss for slot leader {}", slot);
            None
        }
    }

    /// Сохранение лидера слота в кеш
    pub fn put_slot_leader(&self, slot: u64, leader: String) {
        self.slot_leader_cache.insert(slot, leader.clone());
        debug!("Cached slot leader {} for slot {}", leader, slot);

        // Очищаем старые записи если превышен лимит
        if self.slot_leader_cache.len() > self.config.max_slots {
            self.cleanup_old_slots();
        }
    }

    /// Получение слотов лидера
    pub fn get_leader_slots(&self, leader: &str) -> Option<Vec<u64>> {
        self.leader_slots_index.get(leader).map(|slots| slots.clone())
    }

    /// Обновление индекса слотов
    fn update_slot_index(&self, schedule: &LeaderSchedule) -> LeaderScheduleResult<()> {
        for (leader, slot_indices) in &schedule.schedule {
            for &slot_index in slot_indices {
                let absolute_slot = schedule.first_slot + slot_index;
                self.slot_leader_cache.insert(absolute_slot, leader.clone());
            }
        }
        Ok(())
    }

    /// Обновление обратного индекса
    fn update_leader_index(&self, schedule: &LeaderSchedule) -> LeaderScheduleResult<()> {
        for (leader, slot_indices) in &schedule.schedule {
            let absolute_slots: Vec<u64> = slot_indices
                .iter()
                .map(|&slot_index| schedule.first_slot + slot_index)
                .collect();

            self.leader_slots_index.insert(leader.clone(), absolute_slots);
        }
        Ok(())
    }

    /// Очистка старых расписаний
    fn cleanup_old_schedules(&self) {
        let current_time = chrono::Utc::now().timestamp() as u64;
        let ttl = self.config.schedule_ttl_seconds;

        let mut to_remove = Vec::new();

        for entry in self.schedule_cache.iter() {
            let schedule = entry.value();
            if current_time - schedule.created_at > ttl {
                to_remove.push(*entry.key());
            }
        }

        for epoch in to_remove {
            self.schedule_cache.remove(&epoch);
            info!("Removed expired schedule for epoch {}", epoch);
        }
    }

    /// Очистка старых слотов
    fn cleanup_old_slots(&self) {
        // Удаляем самые старые слоты (простая стратегия FIFO)
        let current_len = self.slot_leader_cache.len();
        let target_len = self.config.max_slots * 80 / 100; // Удаляем до 80% от лимита

        if current_len > target_len {
            let mut slots_to_remove: Vec<u64> = self.slot_leader_cache
                .iter()
                .map(|entry| *entry.key())
                .collect();

            slots_to_remove.sort();
            let remove_count = current_len - target_len;

            for slot in slots_to_remove.into_iter().take(remove_count) {
                self.slot_leader_cache.remove(&slot);
            }

            info!("Cleaned up {} old slot entries", remove_count);
        }
    }

    /// Принудительная очистка кеша
    pub fn clear(&self) {
        self.schedule_cache.clear();
        self.prediction_cache.write().clear();
        self.slot_leader_cache.clear();
        self.leader_slots_index.clear();

        let mut stats = self.stats.write();
        *stats = CacheStats::default();

        info!("Cache cleared");
    }

    /// Получение статистики кеша
    pub fn get_stats(&self) -> CacheStats {
        let mut stats = self.stats.read().clone();
        stats.last_update = Instant::now();
        stats
    }

    /// Получение детальной информации о кеше
    pub fn get_cache_info(&self) -> HashMap<String, String> {
        let stats = self.get_stats();
        let schedule_hit_rate = if stats.schedule_hits + stats.schedule_misses > 0 {
            (stats.schedule_hits as f64 / (stats.schedule_hits + stats.schedule_misses) as f64) * 100.0
        } else {
            0.0
        };

        let prediction_hit_rate = if stats.prediction_hits + stats.prediction_misses > 0 {
            (stats.prediction_hits as f64 / (stats.prediction_hits + stats.prediction_misses) as f64) * 100.0
        } else {
            0.0
        };

        let slot_hit_rate = if stats.slot_hits + stats.slot_misses > 0 {
            (stats.slot_hits as f64 / (stats.slot_hits + stats.slot_misses) as f64) * 100.0
        } else {
            0.0
        };

        HashMap::from([
            ("schedule_cache_size".to_string(), self.schedule_cache.len().to_string()),
            ("prediction_cache_size".to_string(), self.prediction_cache.read().len().to_string()),
            ("slot_cache_size".to_string(), self.slot_leader_cache.len().to_string()),
            ("leader_index_size".to_string(), self.leader_slots_index.len().to_string()),
            ("schedule_hit_rate".to_string(), format!("{:.2}%", schedule_hit_rate)),
            ("prediction_hit_rate".to_string(), format!("{:.2}%", prediction_hit_rate)),
            ("slot_hit_rate".to_string(), format!("{:.2}%", slot_hit_rate)),
            ("total_operations".to_string(), stats.total_operations.to_string()),
        ])
    }

    /// Запуск фоновой очистки
    pub async fn start_background_cleanup(&self) {
        let cleanup_interval = Duration::from_secs(self.config.cleanup_interval_seconds);

        loop {
            tokio::time::sleep(cleanup_interval).await;

            self.cleanup_old_schedules();
            self.cleanup_old_slots();

            debug!("Background cache cleanup completed");
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::collections::HashMap;

    #[test]
    fn test_cache_creation() {
        let config = CacheConfig::default();
        let cache = LeaderCache::new(config);

        let stats = cache.get_stats();
        assert_eq!(stats.total_operations, 0);
    }

    #[test]
    fn test_schedule_cache() {
        let config = CacheConfig::default();
        let cache = LeaderCache::new(config);

        let mut schedule_map = HashMap::new();
        schedule_map.insert("validator1".to_string(), vec![0, 1, 2, 3]);

        let schedule = LeaderSchedule {
            epoch: 100,
            schedule: schedule_map,
            created_at: chrono::Utc::now().timestamp() as u64,
            first_slot: 819200,
            last_slot: 827391,
        };

        // Тест промаха
        assert!(cache.get_schedule(100).is_none());

        // Сохранение в кеш
        cache.put_schedule(schedule).unwrap();

        // Тест попадания
        assert!(cache.get_schedule(100).is_some());

        let stats = cache.get_stats();
        assert_eq!(stats.schedule_hits, 1);
        assert_eq!(stats.schedule_misses, 1);
    }

    #[test]
    fn test_slot_leader_cache() {
        let config = CacheConfig::default();
        let cache = LeaderCache::new(config);

        // Тест промаха
        assert!(cache.get_slot_leader(1000).is_none());

        // Сохранение в кеш
        cache.put_slot_leader(1000, "validator1".to_string());

        // Тест попадания
        assert_eq!(cache.get_slot_leader(1000), Some("validator1".to_string()));

        let stats = cache.get_stats();
        assert_eq!(stats.slot_hits, 1);
        assert_eq!(stats.slot_misses, 1);
    }

    #[test]
    fn test_cache_clear() {
        let config = CacheConfig::default();
        let cache = LeaderCache::new(config);

        cache.put_slot_leader(1000, "validator1".to_string());
        assert!(cache.get_slot_leader(1000).is_some());

        cache.clear();
        assert!(cache.get_slot_leader(1000).is_none());

        let stats = cache.get_stats();
        assert_eq!(stats.total_operations, 0);
    }
}
