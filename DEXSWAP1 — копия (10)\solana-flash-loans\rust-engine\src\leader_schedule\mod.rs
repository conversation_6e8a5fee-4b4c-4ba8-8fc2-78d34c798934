/*!
 * 🎯 LEADER SCHEDULE MODULE
 * Высокопроизводительный мониторинг расписания лидеров Solana
 *
 * Основано на официальной документации Solana RPC API:
 * - getLeaderSchedule: возвращает расписание лидеров для эпохи
 * - getEpochInfo: информация о текущей эпохе
 * - getEpochSchedule: конфигурация эпох
 * - getSlot: текущий слот
 */

pub mod monitor;
pub mod predictor;
pub mod cache;
pub mod rpc_client;

pub use monitor::LeaderScheduleMonitor;
pub use predictor::LeaderPredictor;
pub use cache::LeaderCache;

use serde::{Deserialize, Serialize};
use std::collections::HashMap;

// Добавляем chrono для работы с временем
// use chrono; // Используется в других модулях

/// 📊 Информация об эпохе
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EpochInfo {
    /// Абсолютный номер слота
    pub absolute_slot: u64,
    /// Высота блока
    pub block_height: u64,
    /// Номер эпохи
    pub epoch: u64,
    /// Индекс слота в эпохе (0-based)
    pub slot_index: u64,
    /// Количество слотов в эпохе
    pub slots_in_epoch: u64,
    /// Количество транзакций
    pub transaction_count: Option<u64>,
}

/// ⚙️ Расписание эпох
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EpochSchedule {
    /// Слотов в эпохе (обычно 8192 для mainnet)
    pub slots_per_epoch: u64,
    /// Смещение для расписания лидеров
    pub leader_schedule_slot_offset: u64,
    /// Включен ли warmup период
    pub warmup: bool,
    /// Первая нормальная эпоха
    pub first_normal_epoch: u64,
    /// Первый нормальный слот
    pub first_normal_slot: u64,
}

/// 🎯 Расписание лидеров для эпохи
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LeaderSchedule {
    /// Номер эпохи
    pub epoch: u64,
    /// Мапа: validator_pubkey -> [slot_indices]
    /// slot_indices относительно начала эпохи
    pub schedule: HashMap<String, Vec<u64>>,
    /// Время создания расписания
    pub created_at: u64,
    /// Первый слот эпохи
    pub first_slot: u64,
    /// Последний слот эпохи
    pub last_slot: u64,
}

/// 📈 Информация о лидере слота
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SlotLeaderInfo {
    /// Номер слота
    pub slot: u64,
    /// Публичный ключ лидера
    pub leader: String,
    /// Время начала слота (Unix timestamp в миллисекундах)
    pub start_time: u64,
    /// Время окончания слота (Unix timestamp в миллисекундах)
    pub end_time: u64,
    /// Является ли текущим лидером
    pub is_current: bool,
    /// Номер эпохи
    pub epoch: u64,
    /// Индекс в эпохе
    pub epoch_slot_index: u64,
}

/// 🔮 Предсказание лидеров
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LeaderPrediction {
    /// Текущий слот
    pub current_slot: u64,
    /// Предсказанные лидеры
    pub predicted_leaders: Vec<SlotLeaderInfo>,
    /// Время создания предсказания
    pub created_at: u64,
    /// Уверенность в предсказании (0.0-1.0)
    pub confidence: f64,
}

/// ⚡ Оптимальное время подключения
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OptimalConnectionTiming {
    /// Целевой слот
    pub target_slot: u64,
    /// Лидер целевого слота
    pub target_leader: String,
    /// Время до подключения (миллисекунды)
    pub time_until_connection: u64,
    /// Время до активации лидера (миллисекунды)
    pub time_until_activation: u64,
    /// Рекомендуемое время подключения (Unix timestamp)
    pub recommended_connection_time: u64,
    /// Приоритет подключения (0-100)
    pub priority: u8,
}

/// 📊 Статистика производительности
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceStats {
    /// Время последнего обновления
    pub last_update: u64,
    /// Размер кеша расписаний
    pub schedule_cache_size: usize,
    /// Размер кеша предсказаний
    pub prediction_cache_size: usize,
    /// Количество успешных предсказаний
    pub successful_predictions: u64,
    /// Общее количество предсказаний
    pub total_predictions: u64,
    /// Точность предсказаний (0.0-1.0)
    pub prediction_accuracy: f64,
    /// Среднее время обновления (миллисекунды)
    pub avg_update_time: f64,
    /// Количество ошибок RPC
    pub rpc_errors: u64,
    /// Время работы системы (секунды)
    pub uptime_seconds: u64,
}

/// ❌ Ошибки модуля
#[derive(Debug, thiserror::Error)]
pub enum LeaderScheduleError {
    #[error("RPC error: {0}")]
    RpcError(String),

    #[error("Invalid epoch: {0}")]
    InvalidEpoch(u64),

    #[error("Invalid slot: {0}")]
    InvalidSlot(u64),

    #[error("Schedule not found for epoch: {0}")]
    ScheduleNotFound(u64),

    #[error("Cache error: {0}")]
    CacheError(String),

    #[error("Serialization error: {0}")]
    SerializationError(String),

    #[error("Network error: {0}")]
    NetworkError(String),

    #[error("Timeout error: operation took too long")]
    TimeoutError,

    #[error("Configuration error: {0}")]
    ConfigError(String),
}

/// 🎯 Результат операции
pub type LeaderScheduleResult<T> = Result<T, LeaderScheduleError>;

/// 🔧 Конфигурация модуля
#[derive(Debug, Clone)]
pub struct LeaderScheduleConfig {
    /// URL RPC эндпоинта
    pub rpc_url: String,
    /// Таймаут RPC запросов (миллисекунды)
    pub rpc_timeout_ms: u64,
    /// Интервал обновления (миллисекунды)
    pub update_interval_ms: u64,
    /// Размер кеша расписаний
    pub schedule_cache_size: usize,
    /// Размер кеша предсказаний
    pub prediction_cache_size: usize,
    /// Количество слотов для предсказания
    pub prediction_slots_ahead: u64,
    /// Время подготовки перед активацией лидера (миллисекунды)
    pub leader_preparation_time_ms: u64,
    /// Включить детальное логирование
    pub enable_detailed_logging: bool,
    /// Максимальное количество повторных попыток RPC
    pub max_rpc_retries: u32,
    /// Задержка между повторными попытками (миллисекунды)
    pub retry_delay_ms: u64,
}

impl Default for LeaderScheduleConfig {
    fn default() -> Self {
        Self {
            rpc_url: "https://api.mainnet-beta.solana.com".to_string(),
            rpc_timeout_ms: 5000,
            update_interval_ms: 100,
            schedule_cache_size: 10,
            prediction_cache_size: 1000,
            prediction_slots_ahead: 100,
            leader_preparation_time_ms: 800, // 2 слота * 400ms
            enable_detailed_logging: false,
            max_rpc_retries: 3,
            retry_delay_ms: 100,
        }
    }
}

/// 🧮 Утилиты для расчетов
pub mod utils {
    use super::*;

    /// Константы Solana
    pub const SLOT_DURATION_MS: u64 = 400;
    pub const SLOTS_PER_LEADER_ROTATION: u64 = 4;
    pub const DEFAULT_SLOTS_PER_EPOCH: u64 = 8192;

    /// Расчет времени слота
    pub fn slot_to_timestamp(slot: u64, genesis_timestamp: u64) -> u64 {
        genesis_timestamp + (slot * SLOT_DURATION_MS)
    }

    /// Расчет слота по времени
    pub fn timestamp_to_slot(timestamp: u64, genesis_timestamp: u64) -> u64 {
        if timestamp <= genesis_timestamp {
            return 0;
        }
        (timestamp - genesis_timestamp) / SLOT_DURATION_MS
    }

    /// Расчет эпохи по слоту
    pub fn slot_to_epoch(slot: u64, slots_per_epoch: u64) -> u64 {
        slot / slots_per_epoch
    }

    /// Расчет первого слота эпохи
    pub fn epoch_to_first_slot(epoch: u64, slots_per_epoch: u64) -> u64 {
        epoch * slots_per_epoch
    }

    /// Расчет последнего слота эпохи
    pub fn epoch_to_last_slot(epoch: u64, slots_per_epoch: u64) -> u64 {
        ((epoch + 1) * slots_per_epoch) - 1
    }

    /// Расчет индекса слота в эпохе
    pub fn slot_to_epoch_index(slot: u64, slots_per_epoch: u64) -> u64 {
        slot % slots_per_epoch
    }

    /// Проверка валидности слота
    pub fn is_valid_slot(slot: u64, current_slot: u64, max_future_slots: u64) -> bool {
        slot <= current_slot + max_future_slots
    }

    /// Расчет времени до слота
    pub fn time_until_slot(target_slot: u64, current_slot: u64) -> Option<u64> {
        if target_slot <= current_slot {
            return None;
        }
        Some((target_slot - current_slot) * SLOT_DURATION_MS)
    }

    /// Расчет оптимального времени подключения
    pub fn calculate_optimal_connection_time(
        target_slot: u64,
        current_slot: u64,
        preparation_time_ms: u64,
    ) -> Option<u64> {
        let time_until_target = time_until_slot(target_slot, current_slot)?;

        if time_until_target <= preparation_time_ms {
            return Some(0); // Подключаться немедленно
        }

        Some(time_until_target - preparation_time_ms)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use super::utils::*;

    #[test]
    fn test_slot_calculations() {
        let genesis_timestamp = 1609459200000; // Примерное время генезиса
        let slot = 1000;

        let timestamp = slot_to_timestamp(slot, genesis_timestamp);
        let calculated_slot = timestamp_to_slot(timestamp, genesis_timestamp);

        assert_eq!(slot, calculated_slot);
    }

    #[test]
    fn test_epoch_calculations() {
        let slots_per_epoch = 8192;
        let slot = 16384; // Второй эпох

        let epoch = slot_to_epoch(slot, slots_per_epoch);
        assert_eq!(epoch, 2);

        let first_slot = epoch_to_first_slot(epoch, slots_per_epoch);
        assert_eq!(first_slot, 16384);

        let last_slot = epoch_to_last_slot(epoch, slots_per_epoch);
        assert_eq!(last_slot, 24575);

        let epoch_index = slot_to_epoch_index(slot, slots_per_epoch);
        assert_eq!(epoch_index, 0);
    }

    #[test]
    fn test_timing_calculations() {
        let current_slot = 1000;
        let target_slot = 1010;

        let time_until = time_until_slot(target_slot, current_slot);
        assert_eq!(time_until, Some(4000)); // 10 слотов * 400ms

        let connection_time = calculate_optimal_connection_time(
            target_slot,
            current_slot,
            800, // 2 слота подготовки
        );
        assert_eq!(connection_time, Some(3200)); // 4000 - 800
    }

    #[test]
    fn test_config_default() {
        let config = LeaderScheduleConfig::default();
        assert_eq!(config.update_interval_ms, 100);
        assert_eq!(config.leader_preparation_time_ms, 800);
        assert_eq!(config.prediction_slots_ahead, 100);
    }
}
