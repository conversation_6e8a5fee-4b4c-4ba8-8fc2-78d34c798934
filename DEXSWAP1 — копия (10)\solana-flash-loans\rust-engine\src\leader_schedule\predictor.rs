/*!
 * 🔮 LEADER PREDICTOR
 * Высокопроизводительное предсказание лидеров слотов
 */

use super::{
    EpochSchedule, LeaderSchedule, SlotLeaderInfo, LeaderPrediction,
    OptimalConnectionTiming, LeaderScheduleError, LeaderScheduleResult,
    rpc_client::SolanaRpcClient, cache::LeaderCache, utils
};
use std::collections::HashMap;
use std::sync::Arc;
use tracing::{debug, info, warn};

/// 🔮 Предиктор лидеров
pub struct LeaderPredictor {
    rpc_client: Arc<SolanaRpcClient>,
    cache: Arc<LeaderCache>,
    epoch_schedule: Option<EpochSchedule>,
    genesis_timestamp: u64,
}

impl LeaderPredictor {
    /// Создание нового предиктора
    pub fn new(rpc_client: Arc<SolanaRpcClient>, cache: Arc<LeaderCache>) -> Self {
        Self {
            rpc_client,
            cache,
            epoch_schedule: None,
            genesis_timestamp: 1609459200000, // Примерное время генезиса mainnet
        }
    }

    /// Инициализация предиктора
    pub async fn initialize(&mut self) -> LeaderScheduleResult<()> {
        info!("Initializing leader predictor");

        // Получаем расписание эпох
        self.epoch_schedule = Some(self.rpc_client.get_epoch_schedule().await?);

        info!("Leader predictor initialized successfully");
        Ok(())
    }

    /// Предсказание следующих лидеров
    pub async fn predict_next_leaders(&self, slots_ahead: u64) -> LeaderScheduleResult<Vec<SlotLeaderInfo>> {
        debug!("Predicting next {} leaders", slots_ahead);

        // Проверяем кеш предсказаний
        let current_slot = self.rpc_client.get_slot().await?;
        if let Some(cached_prediction) = self.cache.get_prediction(current_slot) {
            if cached_prediction.predicted_leaders.len() >= slots_ahead as usize {
                debug!("Using cached prediction for {} slots", slots_ahead);
                return Ok(cached_prediction.predicted_leaders[..slots_ahead as usize].to_vec());
            }
        }

        let epoch_schedule = self.epoch_schedule
            .as_ref()
            .ok_or_else(|| LeaderScheduleError::ConfigError("Epoch schedule not initialized".to_string()))?;

        let mut leaders = Vec::new();
        let mut processed_epochs = HashMap::new();

        for i in 1..=slots_ahead {
            let target_slot = current_slot + i;
            let leader_info = self.get_slot_leader_info(target_slot, epoch_schedule, &mut processed_epochs).await?;
            leaders.push(leader_info);
        }

        // Кешируем предсказание
        let prediction = LeaderPrediction {
            current_slot,
            predicted_leaders: leaders.clone(),
            created_at: chrono::Utc::now().timestamp() as u64,
            confidence: self.calculate_prediction_confidence(&leaders),
        };

        if let Err(e) = self.cache.put_prediction(prediction) {
            warn!("Failed to cache prediction: {}", e);
        }

        debug!("Predicted {} leaders successfully", leaders.len());
        Ok(leaders)
    }

    /// Получение информации о лидере конкретного слота
    async fn get_slot_leader_info(
        &self,
        slot: u64,
        epoch_schedule: &EpochSchedule,
        processed_epochs: &mut HashMap<u64, Arc<LeaderSchedule>>,
    ) -> LeaderScheduleResult<SlotLeaderInfo> {
        // Проверяем кеш слотов
        if let Some(cached_leader) = self.cache.get_slot_leader(slot) {
            return Ok(self.create_slot_leader_info(slot, cached_leader, epoch_schedule));
        }

        // Определяем эпоху слота
        let epoch = utils::slot_to_epoch(slot, epoch_schedule.slots_per_epoch);

        // Получаем расписание эпохи
        let schedule = if let Some(cached_schedule) = processed_epochs.get(&epoch) {
            cached_schedule.clone()
        } else if let Some(cached_schedule) = self.cache.get_schedule(epoch) {
            processed_epochs.insert(epoch, cached_schedule.clone());
            cached_schedule
        } else {
            // Загружаем расписание с RPC
            let first_slot_of_epoch = utils::epoch_to_first_slot(epoch, epoch_schedule.slots_per_epoch);
            let schedule = self.rpc_client.get_leader_schedule(Some(first_slot_of_epoch)).await?;

            // Кешируем расписание
            if let Err(e) = self.cache.put_schedule(schedule.clone()) {
                warn!("Failed to cache schedule for epoch {}: {}", epoch, e);
            }

            let schedule_arc = Arc::new(schedule);
            processed_epochs.insert(epoch, schedule_arc.clone());
            schedule_arc
        };

        // Находим лидера слота
        let slot_index = utils::slot_to_epoch_index(slot, epoch_schedule.slots_per_epoch);
        let leader = self.find_leader_for_slot_index(&schedule, slot_index)?;

        // Кешируем результат
        self.cache.put_slot_leader(slot, leader.clone());

        Ok(self.create_slot_leader_info(slot, leader, epoch_schedule))
    }

    /// Поиск лидера для индекса слота в эпохе
    fn find_leader_for_slot_index(&self, schedule: &LeaderSchedule, slot_index: u64) -> LeaderScheduleResult<String> {
        for (validator, slot_indices) in &schedule.schedule {
            if slot_indices.contains(&slot_index) {
                return Ok(validator.clone());
            }
        }

        Err(LeaderScheduleError::InvalidSlot(slot_index))
    }

    /// Создание информации о лидере слота
    fn create_slot_leader_info(&self, slot: u64, leader: String, epoch_schedule: &EpochSchedule) -> SlotLeaderInfo {
        let epoch = utils::slot_to_epoch(slot, epoch_schedule.slots_per_epoch);
        let epoch_slot_index = utils::slot_to_epoch_index(slot, epoch_schedule.slots_per_epoch);
        let start_time = utils::slot_to_timestamp(slot, self.genesis_timestamp);
        let end_time = start_time + utils::SLOT_DURATION_MS;

        SlotLeaderInfo {
            slot,
            leader,
            start_time,
            end_time,
            is_current: false, // Будет обновлено при необходимости
            epoch,
            epoch_slot_index,
        }
    }

    /// Расчет уверенности в предсказании
    fn calculate_prediction_confidence(&self, leaders: &[SlotLeaderInfo]) -> f64 {
        if leaders.is_empty() {
            return 0.0;
        }

        // Базовая уверенность зависит от того, насколько далеко в будущее мы предсказываем
        let max_slot = leaders.iter().map(|l| l.slot).max().unwrap_or(0);
        let min_slot = leaders.iter().map(|l| l.slot).min().unwrap_or(0);
        let prediction_range = max_slot - min_slot;

        // Чем меньше диапазон, тем выше уверенность
        let range_factor = if prediction_range <= 10 {
            1.0
        } else if prediction_range <= 100 {
            0.9
        } else if prediction_range <= 1000 {
            0.8
        } else {
            0.7
        };

        // Проверяем разнообразие лидеров (больше разнообразия = выше уверенность)
        let unique_leaders: std::collections::HashSet<_> = leaders.iter().map(|l| &l.leader).collect();
        let diversity_factor = (unique_leaders.len() as f64 / leaders.len() as f64).min(1.0);

        range_factor * diversity_factor
    }

    /// Получение оптимального времени подключения
    pub async fn get_optimal_connection_timing(&self, target_slot: u64) -> LeaderScheduleResult<OptimalConnectionTiming> {
        debug!("Calculating optimal connection timing for slot {}", target_slot);

        let current_slot = self.rpc_client.get_slot().await?;

        if target_slot <= current_slot {
            return Err(LeaderScheduleError::InvalidSlot(target_slot));
        }

        let epoch_schedule = self.epoch_schedule
            .as_ref()
            .ok_or_else(|| LeaderScheduleError::ConfigError("Epoch schedule not initialized".to_string()))?;

        // Получаем лидера целевого слота
        let mut processed_epochs = HashMap::new();
        let leader_info = self.get_slot_leader_info(target_slot, epoch_schedule, &mut processed_epochs).await?;

        // Рассчитываем времена
        let time_until_activation = utils::time_until_slot(target_slot, current_slot)
            .ok_or_else(|| LeaderScheduleError::InvalidSlot(target_slot))?;

        let preparation_time = 800; // 2 слота подготовки
        let time_until_connection = if time_until_activation > preparation_time {
            time_until_activation - preparation_time
        } else {
            0
        };

        let current_timestamp = chrono::Utc::now().timestamp_millis() as u64;
        let recommended_connection_time = current_timestamp + time_until_connection;

        // Рассчитываем приоритет
        let priority = self.calculate_connection_priority(target_slot, current_slot, &leader_info);

        Ok(OptimalConnectionTiming {
            target_slot,
            target_leader: leader_info.leader,
            time_until_connection,
            time_until_activation,
            recommended_connection_time,
            priority,
        })
    }

    /// Расчет приоритета подключения
    fn calculate_connection_priority(&self, target_slot: u64, current_slot: u64, leader_info: &SlotLeaderInfo) -> u8 {
        let slots_ahead = target_slot - current_slot;

        // Базовый приоритет зависит от близости слота
        let proximity_priority = if slots_ahead <= 5 {
            100
        } else if slots_ahead <= 20 {
            80
        } else if slots_ahead <= 100 {
            60
        } else {
            40
        };

        // Корректировка на основе позиции в ротации лидера
        let rotation_position = leader_info.epoch_slot_index % utils::SLOTS_PER_LEADER_ROTATION;
        let rotation_bonus = if rotation_position == 0 {
            10 // Первый слот лидера - высокий приоритет
        } else if rotation_position == 1 {
            5  // Второй слот - средний приоритет
        } else {
            0  // Остальные слоты - без бонуса
        };

        (proximity_priority + rotation_bonus).min(100) as u8
    }

    /// Получение статистики предиктора
    pub fn get_stats(&self) -> HashMap<String, String> {
        let cache_info = self.cache.get_cache_info();
        let mut stats = HashMap::new();

        stats.insert("genesis_timestamp".to_string(), self.genesis_timestamp.to_string());
        stats.insert("epoch_schedule_loaded".to_string(), self.epoch_schedule.is_some().to_string());

        // Добавляем статистику кеша
        for (key, value) in cache_info {
            stats.insert(format!("cache_{}", key), value);
        }

        if let Some(epoch_schedule) = &self.epoch_schedule {
            stats.insert("slots_per_epoch".to_string(), epoch_schedule.slots_per_epoch.to_string());
            stats.insert("warmup_enabled".to_string(), epoch_schedule.warmup.to_string());
        }

        stats
    }

    /// Проверка здоровья предиктора
    pub async fn health_check(&self) -> bool {
        // Проверяем RPC соединение
        if !self.rpc_client.health_check().await {
            return false;
        }

        // Проверяем инициализацию
        if self.epoch_schedule.is_none() {
            return false;
        }

        // Пробуем предсказать одного лидера
        match self.predict_next_leaders(1).await {
            Ok(leaders) => !leaders.is_empty(),
            Err(_) => false,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::leader_schedule::cache::CacheConfig;

    #[tokio::test]
    async fn test_predictor_creation() {
        let rpc_client = Arc::new(
            SolanaRpcClient::new(
                "https://api.devnet.solana.com".to_string(),
                5000,
                3,
                100,
            ).unwrap()
        );

        let cache = Arc::new(LeaderCache::new(CacheConfig::default()));
        let predictor = LeaderPredictor::new(rpc_client, cache);

        assert!(predictor.epoch_schedule.is_none());
    }

    #[test]
    fn test_confidence_calculation() {
        let rpc_client = Arc::new(
            SolanaRpcClient::new(
                "https://api.devnet.solana.com".to_string(),
                5000,
                3,
                100,
            ).unwrap()
        );

        let cache = Arc::new(LeaderCache::new(CacheConfig::default()));
        let predictor = LeaderPredictor::new(rpc_client, cache);

        let leaders = vec![
            SlotLeaderInfo {
                slot: 1000,
                leader: "validator1".to_string(),
                start_time: 0,
                end_time: 400,
                is_current: false,
                epoch: 0,
                epoch_slot_index: 0,
            },
            SlotLeaderInfo {
                slot: 1001,
                leader: "validator2".to_string(),
                start_time: 400,
                end_time: 800,
                is_current: false,
                epoch: 0,
                epoch_slot_index: 1,
            },
        ];

        let confidence = predictor.calculate_prediction_confidence(&leaders);
        assert!(confidence > 0.0 && confidence <= 1.0);
    }

    #[test]
    fn test_connection_priority() {
        let rpc_client = Arc::new(
            SolanaRpcClient::new(
                "https://api.devnet.solana.com".to_string(),
                5000,
                3,
                100,
            ).unwrap()
        );

        let cache = Arc::new(LeaderCache::new(CacheConfig::default()));
        let predictor = LeaderPredictor::new(rpc_client, cache);

        let leader_info = SlotLeaderInfo {
            slot: 1005,
            leader: "validator1".to_string(),
            start_time: 0,
            end_time: 400,
            is_current: false,
            epoch: 0,
            epoch_slot_index: 0, // Первый слот лидера
        };

        let priority = predictor.calculate_connection_priority(1005, 1000, &leader_info);
        assert!(priority > 100); // Должен быть высокий приоритет
    }
}
