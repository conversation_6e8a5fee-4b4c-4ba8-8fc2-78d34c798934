// 🦀 ПРОСТОЙ ТЕСТ RUST КОМПИЛЯЦИИ
// Проверяем, что Rust работает корректно

#[tokio::main]
async fn main() {
    println!("🦀 Rust is working!");
    println!("📊 Testing basic functionality...");
    
    // Тест базовых типов
    let numbers: Vec<u64> = (1..=10).collect();
    let sum: u64 = numbers.iter().sum();
    println!("✅ Sum of 1-10: {}", sum);
    
    // Тест async
    println!("✅ Async runtime working");
    
    // Тест HTTP клиента
    match reqwest::get("https://httpbin.org/get").await {
        Ok(response) => {
            println!("✅ HTTP request successful: {}", response.status());
        }
        Err(e) => {
            println!("⚠️  HTTP request failed: {}", e);
        }
    }
    
    println!("🎉 All tests completed!");
}
