// 🚨 РЕАЛЬНОСТЬ СПРЕДОВ - ПОЧЕМУ УБЫТОК В ВЕБ-ВЕРСИИ?
// Анализ ваших новых данных с влиянием цены 0.01%

console.log('🚨 АНАЛИЗ РЕАЛЬНОСТИ СПРЕДОВ'.red.bold);
console.log('═'.repeat(60));

// 📊 НОВЫЕ ДАННЫЕ ИЗ ВАШИХ СКРИНШОТОВ
const updatedPoolData = {
    poolA_Large: {
        name: 'Pool A (Большой)',
        tvl: 2698665.00,
        solAmount: 7358.53,
        usdcAmount: 1489580.00,
        currentPrice: 164.29,
        priceImpact: 0.01, // 0.01% влияние цены
        
        // Реальные котировки из интерфейса
        solQuote: 1313.006965,      // $1,313 за 8 SOL = $164.125/SOL
        usdcQuote: 7.983765727,     // 7.98 SOL за $1,311 = $164.39/SOL
        
        fees: {
            base: 0.1,
            protocol: 0.005005,
            dynamic: 0.1001
        }
    },
    poolB_Small: {
        name: 'Pool B (Маленький)',
        tvl: 935496.31,
        solAmount: 2408.59,
        usdcAmount: 540264.18,
        currentPrice: 164.28,
        priceImpact: 0.01, // 0.01% влияние цены
        
        // Реальные котировки из интерфейса  
        solQuote: 1313.907932,      // $1,313.90 за 8 SOL = $164.24/SOL
        usdcQuote: 1313.869828669972, // $1,313.87 за 8 SOL = $164.23/SOL
        
        fees: {
            base: 0.01,
            protocol: 0.00516625,
            dynamic: 0.0103125
        }
    }
};

console.log('\n📊 ОБНОВЛЕННЫЕ ДАННЫЕ:');
console.log('═'.repeat(60));

Object.entries(updatedPoolData).forEach(([key, pool]) => {
    console.log(`\n${pool.name}:`);
    console.log(`   TVL: $${pool.tvl.toLocaleString()}`);
    console.log(`   Current Price: $${pool.currentPrice}`);
    console.log(`   Price Impact: ${pool.priceImpact}%`);
    console.log(`   SOL Quote: $${pool.solQuote} (за 8 SOL)`);
    console.log(`   USDC Quote: ${pool.usdcQuote} (за $1,313)`);
    
    // Пересчитываем в цену за 1 SOL
    const solPriceFromQuote = pool.solQuote / 8;
    const usdcPriceFromQuote = 1313 / pool.usdcQuote;
    
    console.log(`   Цена SOL (продажа): $${solPriceFromQuote.toFixed(6)}`);
    console.log(`   Цена SOL (покупка): $${usdcPriceFromQuote.toFixed(6)}`);
    
    const bidAskSpread = solPriceFromQuote - usdcPriceFromQuote;
    console.log(`   Bid-Ask спред: $${bidAskSpread.toFixed(6)} (${((bidAskSpread / usdcPriceFromQuote) * 100).toFixed(4)}%)`);
});

console.log('\n🚨 ПРОБЛЕМА: ПОЧЕМУ УБЫТОК?');
console.log('═'.repeat(60));

const poolA = updatedPoolData.poolA_Large;
const poolB = updatedPoolData.poolB_Small;

// Реальные цены из котировок
const poolA_sellPrice = poolA.solQuote / 8;  // $164.125
const poolA_buyPrice = 1313 / poolA.usdcQuote; // $164.39

const poolB_sellPrice = poolB.solQuote / 8;  // $164.24
const poolB_buyPrice = 1313 / poolB.usdcQuote; // $164.23

console.log(`
🔍 РЕАЛЬНЫЕ ЦЕНЫ ИЗ КОТИРОВОК:

Pool A (Большой):
   Продажа SOL: $${poolA_sellPrice.toFixed(6)}
   Покупка SOL: $${poolA_buyPrice.toFixed(6)}
   Внутренний спред: $${(poolA_sellPrice - poolA_buyPrice).toFixed(6)}

Pool B (Маленький):  
   Продажа SOL: $${poolB_sellPrice.toFixed(6)}
   Покупка SOL: $${poolB_buyPrice.toFixed(6)}
   Внутренний спред: $${(poolB_sellPrice - poolB_buyPrice).toFixed(6)}
`);

// 🚨 АНАЛИЗ ВОЗМОЖНЫХ АРБИТРАЖЕЙ
console.log('\n🚨 АНАЛИЗ АРБИТРАЖНЫХ ВОЗМОЖНОСТЕЙ:');
console.log('═'.repeat(60));

const scenarios = [
    {
        name: 'A→B (Продать в A, купить в B)',
        sellPrice: poolA_sellPrice,
        buyPrice: poolB_buyPrice,
        spread: poolA_sellPrice - poolB_buyPrice
    },
    {
        name: 'B→A (Продать в B, купить в A)', 
        sellPrice: poolB_sellPrice,
        buyPrice: poolA_buyPrice,
        spread: poolB_sellPrice - poolA_buyPrice
    }
];

scenarios.forEach(scenario => {
    const spreadPercent = (scenario.spread / scenario.buyPrice) * 100;
    console.log(`\n${scenario.name}:`);
    console.log(`   Продажа: $${scenario.sellPrice.toFixed(6)}`);
    console.log(`   Покупка: $${scenario.buyPrice.toFixed(6)}`);
    console.log(`   Спред: $${scenario.spread.toFixed(6)} (${spreadPercent.toFixed(4)}%)`);
    
    if (scenario.spread > 0) {
        console.log(`   ✅ Потенциальная прибыль!`.green);
    } else {
        console.log(`   ❌ Убыток: $${Math.abs(scenario.spread).toFixed(6)}`.red);
    }
});

console.log('\n🔍 ДЕТАЛЬНЫЙ АНАЛИЗ УБЫТКОВ:');
console.log('═'.repeat(60));

// Симуляция арбитража с $1000
const testAmount = 1000;

scenarios.forEach(scenario => {
    if (scenario.spread <= 0) return;
    
    console.log(`\n💰 СИМУЛЯЦИЯ ${scenario.name} с $${testAmount}:`);
    
    // Покупаем SOL
    const solBought = testAmount / scenario.buyPrice;
    console.log(`   1. Покупаем: ${solBought.toFixed(6)} SOL за $${scenario.buyPrice.toFixed(6)}/SOL`);
    console.log(`      Тратим: $${testAmount}`);
    
    // Продаем SOL
    const usdcReceived = solBought * scenario.sellPrice;
    console.log(`   2. Продаем: ${solBought.toFixed(6)} SOL за $${scenario.sellPrice.toFixed(6)}/SOL`);
    console.log(`      Получаем: $${usdcReceived.toFixed(2)}`);
    
    // Комиссии
    const buyFees = testAmount * (poolA.fees.base + poolA.fees.protocol + poolA.fees.dynamic) / 100;
    const sellFees = usdcReceived * (poolB.fees.base + poolB.fees.protocol + poolB.fees.dynamic) / 100;
    const totalFees = buyFees + sellFees;
    
    console.log(`   3. Комиссии:`);
    console.log(`      Покупка: $${buyFees.toFixed(4)}`);
    console.log(`      Продажа: $${sellFees.toFixed(4)}`);
    console.log(`      Всего: $${totalFees.toFixed(4)}`);
    
    // Итоговый результат
    const netResult = usdcReceived - testAmount - totalFees;
    const netResultPercent = (netResult / testAmount) * 100;
    
    console.log(`   4. ИТОГ:`);
    if (netResult > 0) {
        console.log(`      ✅ Прибыль: $${netResult.toFixed(4)} (${netResultPercent.toFixed(4)}%)`.green);
    } else {
        console.log(`      ❌ Убыток: $${Math.abs(netResult).toFixed(4)} (${Math.abs(netResultPercent).toFixed(4)}%)`.red);
    }
});

console.log('\n🚨 ОСНОВНЫЕ ПРИЧИНЫ УБЫТКОВ:');
console.log('═'.repeat(60));

console.log(`
1️⃣ BID-ASK СПРЕД ВНУТРИ ПУЛОВ:
   - Pool A: Продажа $${poolA_sellPrice.toFixed(6)} vs Покупка $${poolA_buyPrice.toFixed(6)}
   - Разница: $${(poolA_buyPrice - poolA_sellPrice).toFixed(6)} (${(((poolA_buyPrice - poolA_sellPrice) / poolA_sellPrice) * 100).toFixed(4)}%)
   
   - Pool B: Продажа $${poolB_sellPrice.toFixed(6)} vs Покупка $${poolB_buyPrice.toFixed(6)}  
   - Разница: $${(poolB_buyPrice - poolB_sellPrice).toFixed(6)} (${(((poolB_buyPrice - poolB_sellPrice) / poolB_sellPrice) * 100).toFixed(4)}%)

2️⃣ КОМИССИИ НАКАПЛИВАЮТСЯ:
   - Pool A: ${(poolA.fees.base + poolA.fees.protocol + poolA.fees.dynamic).toFixed(4)}%
   - Pool B: ${(poolB.fees.base + poolB.fees.protocol + poolB.fees.dynamic).toFixed(4)}%
   - Общие комиссии: ~${((poolA.fees.base + poolA.fees.protocol + poolA.fees.dynamic + poolB.fees.base + poolB.fees.protocol + poolB.fees.dynamic)).toFixed(4)}%

3️⃣ ВЛИЯНИЕ НА ЦЕНУ (PRICE IMPACT):
   - Даже 0.01% влияние съедает маленькие спреды
   - Большие объемы → больше влияние → больше потери

4️⃣ ВРЕМЕННЫЕ ЗАДЕРЖКИ:
   - Цены меняются между транзакциями
   - Арбитражные боты уже исполнили возможность
   - MEV (Maximum Extractable Value) конкуренция
`);

console.log('\n💡 РЕШЕНИЯ ДЛЯ ПРИБЫЛЬНОГО АРБИТРАЖА:');
console.log('═'.repeat(60));

console.log(`
✅ АТОМАРНЫЕ FLASH LOAN ТРАНЗАКЦИИ:
   - Все операции в одной транзакции
   - Нет временных задержек
   - Защита от изменения цен

✅ ПОИСК БОЛЬШИХ СПРЕДОВ:
   - Минимум 0.5-1% для покрытия всех комиссий
   - Фокус на аномалии и временные дисбалансы
   - Мониторинг новых токенов и событий

✅ ОПТИМИЗАЦИЯ КОМИССИЙ:
   - Использование пулов с низкими комиссиями
   - Batch транзакции для экономии gas
   - Приоритетные комиссии для быстрого исполнения

✅ УЛУЧШЕННЫЙ АНАЛИЗ:
   - Учет реального price impact
   - Анализ глубины ликвидности
   - Предсказание движения цен

🎯 ВЫВОД: Ваши 0.01% спреды слишком малы для покрытия:
   - Bid-ask спредов (~0.16%)
   - Комиссий (~0.2-0.3%)
   - Price impact (~0.01-0.1%)
   
   НУЖНЫ СПРЕДЫ МИНИМУМ 0.5-1% ДЛЯ ПРИБЫЛЬНОСТИ!
`);

console.log('\n🎯 РЕКОМЕНДАЦИИ:');
console.log('═'.repeat(60));

console.log(`
1. 🔍 ИЩИТЕ БОЛЬШИЕ АНОМАЛИИ (>1%)
   - Новые листинги токенов
   - Крупные новости/события  
   - Технические сбои пулов
   - Ликвидации больших позиций

2. ⚡ ИСПОЛЬЗУЙТЕ АТОМАРНЫЕ ТРАНЗАКЦИИ
   - Flash loans для устранения рисков
   - Все операции в одном блоке
   - Автоматический откат при неудаче

3. 🤖 АВТОМАТИЗИРУЙТЕ ОБНАРУЖЕНИЕ
   - Мониторинг в реальном времени
   - Алерты на большие спреды
   - Автоматическое исполнение

4. 📊 УЛУЧШИТЕ АНАЛИЗ
   - Учет всех скрытых комиссий
   - Реальный price impact
   - Конкуренция с другими ботами

🚀 ИНТЕГРИРУЕМ ЭТО В BMETEORA.JS!
`);
