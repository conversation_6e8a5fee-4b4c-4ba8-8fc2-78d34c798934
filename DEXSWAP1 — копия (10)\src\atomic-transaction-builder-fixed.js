/**
 * ⚛️ ATOMIC TRANSACTION BUILDER - НОВАЯ МОДУЛЬНАЯ АРХИТЕКТУРА
 * ═══════════════════════════════════════════════════════════════
 * 🎯 ЦЕЛЬ: Только финальная сборка транзакции из готовых инструкций
 * 📋 АРХИТЕКТУРА: InstructionProcessor → AtomicTransactionBuilder
 *
 * ПРАВИЛЬНАЯ ПОСЛЕДОВАТЕЛЬНОСТЬ:
 * 1. InstructionProcessor → обрабатывает все инструкции и ALT
 * 2. AtomicTransactionBuilder → получает готовые инструкции
 * 3. Solana SDK → создает финальную транзакцию
 *
 * ❌ НЕ ДЕЛАЕМ: обработку инструкций, конвертацию, дедупликацию
 * ✅ ДЕЛАЕМ: только финальную сборку транзакции
 */

// 🔥 УБРАНО: Все патчи MarginFi SDK - используем оригинальную реализацию
console.log(`✅ ИСПОЛЬЗУЕМ ОРИГИНАЛЬНУЮ MARGINFI SDK БЕЗ ПАТЧЕЙ!`);

// 🔥 ОФИЦИАЛЬНЫЙ ВАЛИДАТОР ALT (РЕШАЕТ ОШИБКУ "invalid owner")
const OfficialALTValidator = require('../official-alt-validator.js');

// 🔥 ИМПОРТИРУЕМ ФУНКЦИЮ НОРМАЛИЗАЦИИ ОДИН РАЗ ДЛЯ ВСЕГО ФАЙЛА
const MarginfiFlashLoanModule = require('../solana-flash-loans/marginfi-flash-loan.js');
const normalizeInstructions = MarginfiFlashLoanModule.normalizeInstructions;

if (typeof normalizeInstructions !== 'function') {
  throw new Error('КРИТИЧЕСКАЯ ОШИБКА: normalizeInstructions функция не найдена при импорте!');
}

console.log(`✅ ФУНКЦИЯ НОРМАЛИЗАЦИИ ИМПОРТИРОВАНА ГЛОБАЛЬНО ДЛЯ AtomicTransactionBuilder`);

const {
  Connection,
  PublicKey,
  TransactionMessage,
  VersionedTransaction,
  AddressLookupTableAccount,
  ComputeBudgetProgram
} = require('@solana/web3.js');

const InstructionProcessor = require('../instruction-processor.js');

// 🔥 ИМПОРТ COMPLETE FLASH LOAN STRUCTURE
const CompleteFlashLoanStructure = require('../complete-flash-loan-structure.js');
// 🔥 УДАЛЕНО: OracleRefreshManager и HealthFactorManager - дублирующиеся функции

// 🔥 ЕДИНЫЙ МОДУЛЬ РАСЧЕТА РАЗМЕРА ТРАНЗАКЦИЙ
const {
  calculateTransactionSize,
  logTransactionSizeBreakdown,
  SOLANA_LIMITS,
  ALT_CONFIG
} = require('./transaction-size-calculator');

class AtomicTransactionBuilderFixed {
  constructor(connection, wallet) {
    this.connection = connection;
    this.wallet = wallet;
    this.userPublicKey = wallet?.publicKey || null;
    this.marginfiAccount = null; // Будет установлен позже

    // Инициализируем InstructionProcessor для обработки инструкций
    this.instructionProcessor = new InstructionProcessor(connection);

    // 🔥 ИНИЦИАЛИЗИРУЕМ COMPLETE FLASH LOAN STRUCTURE
    this.completeFlashLoanStructure = new CompleteFlashLoanStructure(wallet, null, connection);

    // 🔧 ИНИЦИАЛИЗИРУЕМ MARGINFI buildFlashLoanTx АДАПТЕР
    const { MarginFiBuildFlashLoanTxAdapter } = require('../marginfi-buildFlashLoanTx-adapter.js');
    this.marginfiAdapter = new MarginFiBuildFlashLoanTxAdapter(connection, wallet);

    // 🔥 ПОЛУЧАЕМ ALT MANAGER ОТ COMPLETE FLASH LOAN STRUCTURE
    this.altManager = this.completeFlashLoanStructure.altManager;
    console.log(`🔥 ALT Manager получен от Complete Flash Loan Structure: ${!!this.altManager}`);

    // 🔥 УДАЛЕНО: OracleRefreshManager - MarginFi SDK сам управляет oracle обновлениями
    // this.oracleRefreshManager = new OracleRefreshManager(connection, wallet);

    // 🔥 УДАЛЕНО: Health Factor Manager - дублирующаяся функция

    // 🔥 ИНИЦИАЛИЗИРУЕМ ОФИЦИАЛЬНЫЙ ВАЛИДАТОР ALT (РЕШАЕТ ОШИБКУ "invalid owner")
    this.altValidator = new OfficialALTValidator(connection);

    console.log(`⚛️ Atomic Transaction Builder инициализирован`);
    console.log(`   Wallet: ${this.userPublicKey?.toString()?.slice(0, 8)}...`);
    console.log(`✅ InstructionProcessor подключен`);
    console.log(`🔥 Complete Flash Loan Structure подключен`);
    console.log(`🔥 ALT Manager подключен от Complete Flash Loan Structure`);
    console.log(`🔥 Official ALT Validator подключен`);
  }

  // 🔥 ИСПОЛЬЗУЕМ ЦЕНТРАЛИЗОВАННУЮ СИСТЕМУ КОНВЕРТАЦИИ!
  convertUsdToNativeAmount(usdAmount, tokenSymbol) {
    const { convertUsdToNativeAmount } = require('../centralized-amount-converter.js');
    return convertUsdToNativeAmount(usdAmount, tokenSymbol);
  }

  // 🛡️ СИСТЕМА ЗАЩИТЫ ОТ УБЫТКОВ ПО ОФИЦИАЛЬНОЙ ДОКУМЕНТАЦИИ ANCHOR
  validateProfitability(tradeData) {
    console.log('🛡️ ПРОВЕРКА ЗАЩИТЫ ОТ УБЫТКОВ');
    console.log('═══════════════════════════════════════════════════════════════');
    console.log(`💰 Ожидаемая прибыль: $${tradeData.expectedProfitUsd?.toFixed(4) || '0.0000'}`);
    console.log(`📊 Ожидаемый ROI: ${tradeData.expectedProfitPercent?.toFixed(4) || '0.0000'}%`);
    console.log(`💸 Комиссия транзакции: ${tradeData.transactionFeeSol?.toFixed(6) || '0.005000'} SOL`);
    console.log(`🎯 Размер позиции: $${tradeData.positionSizeUsd?.toFixed(2) || '50000.00'}`);

    // КОНФИГУРАЦИЯ ЗАЩИТЫ ОТ УБЫТКОВ (РЕАЛИСТИЧНЫЕ НАСТРОЙКИ ДЛЯ АРБИТРАЖА)
    const PROTECTION_CONFIG = {
      MIN_PROFIT_USD: 1.00,        // Минимальная прибыль в USD
      MIN_PROFIT_PERCENT: 0.015,   // Минимальная прибыль в процентах (1.5 базисных пункта)
      MAX_LOSS_USD: 5.00,          // Максимальные потери в USD (стоп-лосс)
      MAX_LOSS_PERCENT: 0.5,       // Максимальные потери в процентах
      MAX_TRANSACTION_FEE_SOL: 0.01, // Максимальная комиссия транзакции
      MIN_ROI_PERCENT: 0.002       // Минимальный ROI (0.002% = 2 базисных пункта)
    };

    const validationResults = [];

    // ПРОВЕРКА 1: Минимальная прибыль в USD
    const expectedProfitUsd = tradeData.expectedProfitUsd || 0;
    const minProfitCheck = expectedProfitUsd >= PROTECTION_CONFIG.MIN_PROFIT_USD;
    validationResults.push({
      name: 'Минимальная прибыль USD',
      passed: minProfitCheck,
      message: minProfitCheck
        ? `$${expectedProfitUsd.toFixed(4)} >= $${PROTECTION_CONFIG.MIN_PROFIT_USD}`
        : `$${expectedProfitUsd.toFixed(4)} < $${PROTECTION_CONFIG.MIN_PROFIT_USD} (НЕДОСТАТОЧНО!)`
    });

    // ПРОВЕРКА 2: Минимальная прибыль в процентах
    const expectedProfitPercent = tradeData.expectedProfitPercent || 0;
    const minPercentCheck = expectedProfitPercent >= PROTECTION_CONFIG.MIN_PROFIT_PERCENT;
    validationResults.push({
      name: 'Минимальная прибыль %',
      passed: minPercentCheck,
      message: minPercentCheck
        ? `${expectedProfitPercent.toFixed(4)}% >= ${PROTECTION_CONFIG.MIN_PROFIT_PERCENT}%`
        : `${expectedProfitPercent.toFixed(4)}% < ${PROTECTION_CONFIG.MIN_PROFIT_PERCENT}% (НЕДОСТАТОЧНО!)`
    });

    // ПРОВЕРКА 3: ROI (Return on Investment)
    const positionSizeUsd = tradeData.positionSizeUsd || 50000;
    const roi = (expectedProfitUsd / positionSizeUsd) * 100;
    const roiCheck = roi >= PROTECTION_CONFIG.MIN_ROI_PERCENT;
    validationResults.push({
      name: 'ROI (Return on Investment)',
      passed: roiCheck,
      message: roiCheck
        ? `ROI ${roi.toFixed(4)}% >= ${PROTECTION_CONFIG.MIN_ROI_PERCENT}%`
        : `НИЗКИЙ ROI: ${roi.toFixed(4)}% < ${PROTECTION_CONFIG.MIN_ROI_PERCENT}%`
    });

    // ИТОГОВОЕ РЕШЕНИЕ
    const allPassed = validationResults.every(result => result.passed);
    const failedChecks = validationResults.filter(result => !result.passed);

    console.log('');
    console.log('📋 РЕЗУЛЬТАТЫ ПРОВЕРОК:');
    console.log('─────────────────────────────────────────────────────────────');
    validationResults.forEach(result => {
      console.log(`${result.passed ? '✅' : '❌'} ${result.name}: ${result.message}`);
    });

    console.log('');
    console.log('🎯 ИТОГОВОЕ РЕШЕНИЕ:');
    console.log('═══════════════════════════════════════════════════════════════');

    if (allPassed) {
      console.log('✅ ВСЕ ПРОВЕРКИ ПРОЙДЕНЫ - ТРАНЗАКЦИЯ РАЗРЕШЕНА!');
      console.log('🚀 Сделка будет выполнена');
      return {
        allowed: true,
        reason: 'Все проверки защиты от убытков пройдены',
        checks: validationResults
      };
    } else {
      console.log('❌ ЗАЩИТА ОТ УБЫТКОВ АКТИВИРОВАНА - ТРАНЗАКЦИЯ ОТКЛОНЕНА!');
      console.log('🛡️ Сделка будет отменена для защиты от потерь');
      console.log('');
      console.log('🚨 ПРИЧИНЫ ОТКЛОНЕНИЯ:');
      failedChecks.forEach(check => {
        console.log(`   • ${check.name}: ${check.message}`);
      });

      return {
        allowed: false,
        reason: 'Защита от убытков активирована',
        failedChecks: failedChecks,
        checks: validationResults
      };
    }
  }

  /**
   * 🔧 Установка MarginFi аккаунта для Flash Loans
   */
  setMarginFiAccount(marginfiAccount) {
    this.marginfiAccount = marginfiAccount;
    console.log(`✅ MarginFi аккаунт установлен для Flash Loans`);

    // 🔥 УДАЛЕНО: Health Factor Manager - дублирующаяся функция
    if (marginfiAccount && marginfiAccount.client) {
      this.marginfiClient = marginfiAccount.client;
      console.log(`✅ MarginFi client установлен через marginfiAccount`);
    }
  }

  /**
   * 🔧 УСТАНОВКА MARGINFI FLASH LOAN МОДУЛЯ
   */
  setMarginFiFlashLoan(marginfiFlashLoan) {
    this.marginfiFlashLoan = marginfiFlashLoan;

    // 🔥 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: УСТАНАВЛИВАЕМ MARGINFI CLIENT И ACCOUNT
    if (marginfiFlashLoan) {
      this.marginfiClient = marginfiFlashLoan.client;
      this.marginfiAccount = marginfiFlashLoan.marginfiAccount;
      console.log('✅ MarginFi Flash Loan модуль установлен в атомарный строитель');
      console.log(`   Client: ${!!this.marginfiClient}`);
      console.log(`   Account: ${!!this.marginfiAccount}`);

      // 🔥 УДАЛЕНО: Health Factor Manager - дублирующаяся функция
    } else {
      console.log('⚠️ MarginFi Flash Loan модуль не передан');
    }
  }

  /**
   * 🚫 ДУБЛИРУЮЩАЯ ИНИЦИАЛИЗАЦИЯ MARGINFI УДАЛЕНА!
   *
   * ПРИЧИНА ДУБЛИРОВАНИЯ: Этот метод создавал второй экземпляр MarginFi
   * РЕШЕНИЕ: Используем ТОЛЬКО real-trading-executor.js для инициализации
   * ПОЛУЧЕНИЕ: Через setMarginFiFlashLoan() от real-trading-executor.js
   */
  async initializeMarginFi() {
    console.log('🚫 ДУБЛИРУЮЩАЯ ИНИЦИАЛИЗАЦИЯ MARGINFI ОТКЛЮЧЕНА!');
    console.log('💡 ИСПОЛЬЗУЙТЕ real-trading-executor.js для инициализации MarginFi');
    console.log('💡 ПОЛУЧЕНИЕ через setMarginFiFlashLoan() от real-trading-executor.js');
    return false; // Отключаем дублирующую инициализацию
  }

  // 🔥 УДАЛЕНО: Дублирующаяся функция setMarginFiClient - это мусор!

  /**
   * 🪐 УСТАНОВКА JUPITER SWAP INSTRUCTIONS
   */
  setJupiterSwapInstructions(jupiterSwapInstructions) {
    this.jupiterSwapInstructions = jupiterSwapInstructions;
    console.log(`✅ Jupiter Swap Instructions установлен в атомарный строитель`);
  }

  // 🔥 УДАЛЕНО: Дублирующаяся функция createOracleUpdateInstruction - это мусор!

  // 🔥 УДАЛЕН: Дублирующий метод setMarginFiFlashLoan - использовать основной метод выше!

  /**
   * 🔥 ФИЛЬТРАЦИЯ ДУБЛИРУЮЩИХ ATA ИНСТРУКЦИЙ
   */
  filterDuplicateATAInstructions(instructions) {
    console.log(`🔍 Фильтруем дублирующие ATA инструкции...`);

    const seenATAs = new Set();
    const filteredInstructions = [];

    for (const instruction of instructions) {
      // Проверяем если это ATA инструкция
      if (instruction.programId.toString() === 'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL') {
        // Получаем адрес создаваемого ATA (первый аккаунт)
        const ataAddress = instruction.keys[0]?.pubkey?.toString();

        if (ataAddress && seenATAs.has(ataAddress)) {
          console.log(`❌ Пропускаем дублирующую ATA инструкцию: ${ataAddress.slice(0, 8)}...`);
          continue;
        }

        if (ataAddress) {
          seenATAs.add(ataAddress);
          console.log(`✅ Добавляем ATA инструкцию: ${ataAddress.slice(0, 8)}...`);
        }
      }

      filteredInstructions.push(instruction);
    }

    console.log(`🎯 Отфильтровано ${instructions.length - filteredInstructions.length} дублирующих ATA инструкций`);
    return filteredInstructions;
  }

  /**
   * ⚛️ НОВЫЙ МЕТОД: СОЗДАНИЕ АТОМАРНОЙ ТРАНЗАКЦИИ ЧЕРЕЗ INSTRUCTION PROCESSOR
   */
  async createAtomicTransactionWithInstructionProcessor(swap1Params, swap2Params, tokenMint) {
    try {
      console.log(`⚛️ Создаем атомарную транзакцию через InstructionProcessor...`);
      console.log(`   Swap 1: ${swap1Params.inputMint.slice(0, 8)}... → ${swap1Params.outputMint.slice(0, 8)}...`);
      console.log(`   Swap 2: ${swap2Params.inputMint.slice(0, 8)}... → ${swap2Params.outputMint.slice(0, 8)}...`);

      // ШАГ 1: Получаем RAW данные от Jupiter API С ОПТИМИЗАЦИЯМИ ДЛЯ FLASH LOAN
      const JupiterSwapInstructions = require('../jupiter-swap-instructions.js');
      const jupiterSwap = new JupiterSwapInstructions(this.connection, this.wallet);

      console.log(`🚨 СОЗДАЕМ ОПТИМИЗИРОВАННЫЕ JUPITER SWAPS ДЛЯ FLASH LOAN`);
      console.log(`   Каждый swap ограничен 30 ключами`);
      console.log(`   Отключены wrap/unwrap инструкции`);
      console.log(`   Включены все ALT оптимизации`);

      const jupiterRawData = await jupiterSwap.createTwoJupiterSwapsForMultihop(swap1Params, swap2Params);

      // ШАГ 2: Обрабатываем через InstructionProcessor (конвертация + дедупликация + ALT)
      const processedData = await this.instructionProcessor.processForAtomicBuilder(
        jupiterRawData,
        this.userPublicKey,
        tokenMint,
        this.marginfiAccount // Передаем MarginFi аккаунт для Flash Loans
      );

      // ШАГ 3: 🚨 ИСПОЛЬЗУЕМ buildFlashLoanTx ЕСЛИ ЕСТЬ MARGINFI АККАУНТ!
      let transaction;
      if (this.marginfiAccount && processedData.analysis.hasFlashLoan) {
        console.log(`🚨 ИСПОЛЬЗУЕМ buildFlashLoanTx для создания Flash Loan транзакции!`);

        // 🚨 ИСПРАВЛЕНИЕ: Получаем банк через MarginFi аккаунт
        console.log(`🔍 Получаем банк для токена: ${tokenMint}`);

        // Используем client из MarginFi аккаунта
        const marginfiClient = this.marginfiAccount.client;
        let bank = marginfiClient.getBankByMint(new PublicKey(tokenMint));

        if (!bank) {
          console.log(`❌ Банк для токена ${tokenMint} не найден!`);
          console.log(`🔍 Попробуем найти USDC банк как альтернативу...`);

          // Альтернатива: используем USDC банк
          const usdcBank = marginfiClient.getBankByTokenSymbol('USDC');
          if (!usdcBank) {
            throw new Error(`Ни банк для ${tokenMint}, ни USDC банк не найдены!`);
          }

          console.log(`✅ Используем USDC банк: ${usdcBank.address.toString()}`);
          bank = usdcBank;
        } else {
          console.log(`✅ Банк найден: ${bank.address.toString()}`);
        }

        // 🔥 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: ЗАГРУЖАЕМ РЕАЛЬНЫЕ ALT ТАБЛИЦЫ!
        console.log(`🔥 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: ЗАГРУЖАЕМ РЕАЛЬНЫЕ ALT ТАБЛИЦЫ!`);

        // � КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: ВСЕГДА ЗАГРУЖАЕМ ВСЕ ALT ЧЕРЕЗ НОВЫЙ MANAGER!
        console.log(`🔥 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: ВСЕГДА ЗАГРУЖАЕМ ВСЕ ALT!`);
        console.log(`📊 Текущее состояние ALT:`);
        console.log(`   addressLookupTableAccounts: ${processedData.addressLookupTableAccounts?.length || 0}`);
        console.log(`   addressLookupTableAddresses: ${processedData.addressLookupTableAddresses?.length || 0}`);

        // 🚨 ПРОБЛЕМА: Jupiter может вернуть только 1 ALT в addressLookupTableAccounts,
        // но 7 адресов в addressLookupTableAddresses!
        console.log(`🚨 ПРОБЛЕМА НАЙДЕНА: Jupiter возвращает неполные ALT!`);
        console.log(`� РЕШЕНИЕ: Загружаем ВСЕ ALT из addressLookupTableAddresses`);

        // Извлекаем ВСЕ ALT адреса из Jupiter ответа
        const altAddresses = [];
        if (processedData.addressLookupTableAddresses && processedData.addressLookupTableAddresses.length > 0) {
          altAddresses.push(...processedData.addressLookupTableAddresses);
          console.log(`✅ Найдено ${altAddresses.length} ALT адресов в Jupiter ответе`);
        }

        // 🔥 ЗАГРУЖАЕМ ВСЕ ALT ЧЕРЕЗ НОВЫЙ ПОЛНЫЙ ALT MANAGER
        console.log(`🔥 ЗАГРУЖАЕМ ВСЕ ALT ЧЕРЕЗ НОВЫЙ ПОЛНЫЙ ALT MANAGER...`);

        // Загружаем все ALT через новый altManager
        if (this.altManager && this.altManager.loadAllALT) {
          try {
            // Используем новый метод loadAllALT для полной загрузки
            const loadedALT = await this.altManager.loadAllALT(processedData, marginfiData);
            processedData.addressLookupTableAccounts = loadedALT;
            console.log(`✅ НОВЫЙ ALT MANAGER загрузил ${loadedALT.length} ALT таблиц`);

            // Показываем статистику
            const stats = this.altManager.stats;
            console.log(`   🎯 Jupiter ALT: ${stats.jupiterALT}`);
            console.log(`   🏦 MarginFi ALT: ${stats.marginfiALT}`);
            console.log(`   🔥 Кастомная ALT: ${stats.customALT}`);
            console.log(`   📋 Другие ALT: ${stats.otherALT}`);
            console.log(`   🔑 Всего адресов: ${stats.totalAddresses}`);
            console.log(`   💾 Экономия: ${stats.compressionSavings} байт`);

          } catch (altError) {
            console.log(`❌ Ошибка загрузки ALT через НОВЫЙ MANAGER: ${altError.message}`);
            throw new Error(`ALT Manager failed: ${altError.message}`);
          }
        } else {
          console.log(`❌ НОВЫЙ altManager.loadAllALT недоступен!`);
          throw new Error(`ALT Manager loadAllALT method not available`);
        }

        // 🔥 СЕЛЕКТИВНЫЙ ВЫБОР ALT + КАСТОМНАЯ ALT ДЛЯ 18 ПОСТОЯННЫХ КЛЮЧЕЙ
        console.log(`🔥 ПРИМЕНЯЕМ СЕЛЕКТИВНЫЙ ВЫБОР ALT + КАСТОМНАЯ ALT...`);

        // 🔍 ДЕТАЛЬНАЯ ДИАГНОСТИКА ЗАГРУЖЕННЫХ ALT ТАБЛИЦ
        console.log(`🔍 ДЕТАЛЬНАЯ ДИАГНОСТИКА ЗАГРУЖЕННЫХ ALT ТАБЛИЦ:`);
        console.log(`   📊 Количество ALT: ${processedData.addressLookupTableAccounts.length}`);

        // Собираем статистику Jupiter ALT
        let totalJupiterKeys = 0;
        processedData.addressLookupTableAccounts.forEach((alt, index) => {
          const keyCount = alt.state.addresses.length;
          totalJupiterKeys += keyCount;
          console.log(`   🔥 ALT ${index + 1}: ${keyCount} ключей (${alt.key.toString().slice(0, 8)}...${alt.key.toString().slice(-8)})`);
          console.log(`      📍 Полный адрес: ${alt.key.toString()}`);
          console.log(`      📋 Первые 5 ключей: ${alt.state.addresses.slice(0, 5).map(addr => addr.toString().slice(0, 8) + '...').join(', ')}`);
        });

        console.log(`📊 АНАЛИЗ JUPITER ALT (ДО СЕЛЕКЦИИ):`);
        console.log(`   ALT таблиц: ${processedData.addressLookupTableAccounts.length}`);
        console.log(`   Всего ключей в Jupiter ALT: ${totalJupiterKeys}`);

        // 🔥 ИСПОЛЬЗУЕМ ВСЕ ALT БЕЗ СЕЛЕКТИВНОГО ВЫБОРА!
        console.log(`🔥 ИСПОЛЬЗУЕМ ВСЕ ЗАГРУЖЕННЫЕ ALT БЕЗ ФИЛЬТРАЦИИ!`);
        console.log(`💡 ПРИЧИНА: Селективный выбор может отфильтровать важные ALT`);

        const selectedALT = [...processedData.addressLookupTableAccounts];
        console.log(`✅ ВСЕ ALT ВКЛЮЧЕНЫ: ${selectedALT.length} таблиц`);

        // Используем все ALT без фильтрации
        const enhancedALT = selectedALT;

        console.log(`🎯 ФИНАЛЬНАЯ КОНФИГУРАЦИЯ ALT:`);
        console.log(`   ВСЕГО ALT: ${enhancedALT.length}`);
        console.log(`   🔥 ВСЕ ALT ВКЛЮЧЕНЫ БЕЗ ФИЛЬТРАЦИИ`);
        console.log(`   🔥 МАКСИМАЛЬНОЕ СЖАТИЕ ТРАНЗАКЦИИ`);

        transaction = await this.createFlashLoanTransaction(
          this.marginfiAccount,
          swap1Params.amount, // 🔥 ИСПРАВЛЕНО: ИСПОЛЬЗУЕМ РЕАЛЬНУЮ СУММУ ИЗ ПАРАМЕТРОВ!
          bank,
          processedData.instructions,
          enhancedALT // 🔥 ИСПОЛЬЗУЕМ РАСШИРЕННЫЕ ALT!
        );
      } else {
        console.log(`⚠️ Создаем обычную транзакцию БЕЗ Flash Loan`);
        transaction = await this.createFinalTransaction(
          processedData.instructions,
          processedData.addressLookupTableAccounts
        );
      }

      console.log(`✅ Атомарная транзакция создана через InstructionProcessor:`);
      console.log(`   Инструкций: ${processedData.instructions.length}`);
      console.log(`   ALT аккаунтов: ${processedData.addressLookupTableAccounts.length}`);
      console.log(`   Размер: ${transaction.serialize().length} байт`);

      // 🚨 ОТПРАВКА ПРОИСХОДИТ В real-trading-executor.js!
      console.log(`✅ Транзакция создана и готова к отправке в real-trading-executor.js`);
      console.log(`📋 Тип транзакции: ${transaction.constructor.name}`);
      console.log(`📋 Размер: ${transaction.serialize().length} байт`);

      // Помечаем что транзакция создана но НЕ отправлена (отправка в real-trading-executor.js)
      transaction._signature = null;
      transaction._confirmed = false;
      transaction._readyToSend = true;

      return transaction;

    } catch (error) {
      console.error(`❌ Ошибка создания атомарной транзакции через InstructionProcessor: ${error.message}`);
      throw error;
    }
  }

  /**
   * ⚛️ СТАРЫЙ МЕТОД: СОЗДАНИЕ АТОМАРНОЙ ТРАНЗАКЦИИ С ДВУМЯ JUPITER СВАПАМИ (УСТАРЕЛ!)
   * ❌ ПРОБЛЕМА: НЕ использует InstructionProcessor, вызывает ошибку pubkey.toBase58
   */
  async createAtomicTransactionWithTwoJupiterSwaps(swap1Params, swap2Params, marginfiInstructions = null) {
    console.log(`❌ УСТАРЕВШИЙ МЕТОД! Используйте createAtomicTransactionWithInstructionProcessor`);

    // Перенаправляем на новый метод
    return await this.createAtomicTransactionWithInstructionProcessor(
      swap1Params,
      swap2Params,
      swap1Params.inputMint
    );
  }

  /**
   * 🔨 НОВЫЙ МЕТОД: ФИНАЛЬНАЯ СБОРКА ТРАНЗАКЦИИ (с ALT поддержкой)
   */
  async createFinalTransaction(instructions, addressLookupTableAccounts) {
    try {
      console.log(`🔨 Создаем финальную транзакцию...`);
      console.log(`   Инструкций: ${instructions.length}`);
      console.log(`   ALT аккаунтов: ${addressLookupTableAccounts.length}`);

      // ШАГ 1: Получаем свежий blockhash
      const { blockhash } = await this.connection.getLatestBlockhash();

      // 🚨 ДИАГНОСТИКА ALT ЭФФЕКТИВНОСТИ ПЕРЕД СЖАТИЕМ
      const allInstructionKeys = new Set();
      instructions.forEach(ix => {
        ix.keys.forEach(key => {
          allInstructionKeys.add(key.pubkey.toString());
        });
      });

      const allAltKeys = new Set();
      addressLookupTableAccounts.forEach(alt => {
        if (alt.state && alt.state.addresses) {
          alt.state.addresses.forEach(addr => {
            allAltKeys.add(addr.toString());
          });
        }
      });

      const coveredKeys = Array.from(allInstructionKeys).filter(key => allAltKeys.has(key));
      const uncoveredKeys = Array.from(allInstructionKeys).filter(key => !allAltKeys.has(key));

      console.log(`🚨 ALT ЭФФЕКТИВНОСТЬ ДИАГНОСТИКА:`);
      console.log(`   Всего ключей в инструкциях: ${allInstructionKeys.size}`);
      console.log(`   Ключей покрыто ALT: ${coveredKeys.length}`);
      console.log(`   Ключей НЕ покрыто ALT: ${uncoveredKeys.length}`);
      console.log(`   Эффективность сжатия: ${((coveredKeys.length / allInstructionKeys.size) * 100).toFixed(1)}%`);

      if (uncoveredKeys.length > 0) {
        console.log(`⚠️ НЕПОКРЫТЫЕ КЛЮЧИ (первые 10):`);
        uncoveredKeys.slice(0, 10).forEach((key, i) => {
          console.log(`     ${i + 1}. ${key.slice(0, 8)}... (${this.getKeyDescription(key)})`);
        });

        // 🚨 КАСТОМНАЯ ALT НЕ РАБОТАЕТ - УВЕЛИЧИВАЕТ РАЗМЕР ТРАНЗАКЦИИ!
        console.log(`⚠️ КАСТОМНАЯ ALT ОТКЛЮЧЕНА - она увеличивает размер транзакции!`);
        console.log(`🔧 НУЖНО ИСПОЛЬЗОВАТЬ РЕАЛЬНЫЕ ALT ИЗ JUPITER И MARGINFI`);

        // 🎯 АНАЛИЗ: Почему ALT неэффективна?
        console.log(`📊 АНАЛИЗ НЕЭФФЕКТИВНОСТИ ALT:`);
        console.log(`   - Jupiter ALT покрывает только Jupiter-специфичные ключи`);
        console.log(`   - MarginFi ключи НЕ включены в Jupiter ALT`);
        console.log(`   - System Program и User Wallet НЕ включены в Jupiter ALT`);
        console.log(`   - Нужны СПЕЦИАЛИЗИРОВАННЫЕ ALT для MarginFi + Jupiter комбинаций`);

        // 🚨 КРИТИЧЕСКАЯ ПРОБЛЕМА: Размер транзакции близок к лимиту
        const estimatedSize = 32 + (uncoveredKeys.length * 32); // Примерная оценка
        console.log(`⚠️ КРИТИЧНО: Транзакция может превысить лимит 1232 байт!`);
        console.log(`   Непокрытых ключей: ${uncoveredKeys.length} × 32 байт = ${uncoveredKeys.length * 32} байт`);
        console.log(`   Базовый размер: ~200 байт`);
        console.log(`   Примерный размер: ~${200 + uncoveredKeys.length * 32} байт`);

        if (uncoveredKeys.length > 25) {
          console.log(`🚨 ОПАСНО: Слишком много непокрытых ключей (${uncoveredKeys.length} > 25)!`);
          console.log(`   Транзакция может быть отклонена из-за превышения лимита размера`);
        }
      }

      // ШАГ 2: Создаем TransactionMessage С ALT поддержкой
      const messageV0 = new TransactionMessage({
        payerKey: this.userPublicKey,
        recentBlockhash: blockhash,
        instructions: instructions
      }).compileToV0Message(addressLookupTableAccounts);

      // ШАГ 3: Создаем VersionedTransaction
      const transaction = new VersionedTransaction(messageV0);

      // ШАГ 4: Подписываем транзакцию
      transaction.sign([this.wallet]);

      console.log(`✅ Финальная транзакция создана и подписана`);
      console.log(`   Размер: ${transaction.serialize().length} байт`);

      return transaction;

    } catch (error) {
      console.error(`❌ Ошибка создания финальной транзакции: ${error.message}`);
      throw error;
    }
  }

  /**
   * ⚛️ СТАРЫЙ МЕТОД: СОЗДАНИЕ VERSIONED TRANSACTION (УСТАРЕЛ!)
   * ❌ ПРОБЛЕМА: НЕ поддерживает ALT, вызывает ошибку pubkey.toBase58
   */
  async createVersionedTransaction(instructions, jupiterData) {
    console.log(`❌ УСТАРЕВШИЙ МЕТОД! Используйте createFinalTransaction`);

    // Перенаправляем на новый метод (без ALT для совместимости)
    return await this.createFinalTransaction(instructions, []);
  }

  /**
   * ⚛️ СОЗДАНИЕ ПРОСТОЙ ТРАНЗАКЦИИ БЕЗ ALT
   */
  async createSimpleTransaction(instructions) {
    try {
      console.log(`⚛️ Создаем простую транзакцию без ALT...`);
      console.log(`   Инструкций: ${instructions.length}`);

      // 🔥 НЕ ДОБАВЛЯЕМ COMPUTE BUDGET - ОН УЖЕ ЗАМЕНЕН В JUPITER ИНСТРУКЦИЯХ!
      console.log(`💡 Compute budget уже заменен в Jupiter инструкциях на 250K CU!`);

      const allInstructions = [...instructions];

      // Создаем транзакцию без ALT
      const transaction = await this.createVersionedTransaction(allInstructions, []);

      console.log(`✅ Простая транзакция создана`);
      return transaction;

    } catch (error) {
      console.error(`❌ Ошибка создания простой транзакции: ${error.message}`);
      throw error;
    }
  }

  /**
   * � ЖЕСТКО ЗАШИТЫЙ ПОРЯДОК ИНСТРУКЦИЙ - НИКОГДА НЕ МЕНЯЕТСЯ!
   * �🚨 ОФИЦИАЛЬНАЯ РЕАЛИЗАЦИЯ FLASH LOAN ЧЕРЕЗ buildFlashLoanTx
   * Использует официальный метод MarginFi SDK по документации
   */
  async createFlashLoanTransaction(marginfiAccount, amount, bank, jupiterInstructions, addressLookupTableAccounts = []) {
    // 🔧 ИСПРАВЛЕНИЕ: Определяем finalMarginfiAccount в начале функции для доступности во всех блоках
    let finalMarginfiAccount = marginfiAccount;

    try {
      console.log(`🔒 ЖЕСТКО ЗАШИТЫЙ ПОРЯДОК: НИКОГДА НЕ МЕНЯЕТСЯ!`);
      console.log(`🚨 ОФИЦИАЛЬНАЯ РЕАЛИЗАЦИЯ: Создаем Flash Loan через buildFlashLoanTx`);
      console.log(`   Сумма займа: ${amount}`);
      console.log(`   Банк: ${bank.address.toString()}`);
      console.log(`   Jupiter инструкций: ${jupiterInstructions.length}`);
      console.log(`   ALT таблиц: ${addressLookupTableAccounts.length}`);

      // 🔒 ПРОВЕРЯЕМ ЧТО КАСТОМНЫЙ ALT ДЕЙСТВИТЕЛЬНО ПЕРЕДАН
      console.log(`🔒 АНАЛИЗ ПЕРЕДАННЫХ ALT ТАБЛИЦ:`);
      addressLookupTableAccounts.forEach((alt, index) => {
        const keyCount = alt.state?.addresses?.length || 0;
        const keyPreview = alt.key?.toString()?.slice(0, 8) || 'unknown';
        console.log(`   ALT ${index + 1}: ${keyCount} ключей, key: ${keyPreview}...`);
      });

      if (addressLookupTableAccounts.length === 0) {
        console.log(`❌ КРИТИЧЕСКАЯ ОШИБКА: ALT НЕ ПЕРЕДАНЫ В createFlashLoanTransaction!`);
      } else {
        console.log(`✅ ALT ПЕРЕДАНЫ ПРАВИЛЬНО: ${addressLookupTableAccounts.length} таблиц`);
      }

      // � ЖЕСТКО ЗАШИТЫЙ ПОРЯДОК ИНСТРУКЦИЙ - НИКОГДА НЕ МЕНЯЕТСЯ!
      // ========================================================================
      // ПОРЯДОК: 1. Oracle → 2. Compute Budget → 3. Setup → 4. Borrow → 5. Jupiter Swaps → 6. Repay → 7. Cleanup
      // ========================================================================
      console.log(`🔒 ПРИМЕНЯЕМ ЖЕСТКО ЗАШИТЫЙ ПОРЯДОК ИНСТРУКЦИЙ:`);
      console.log(`   1. Compute Budget (250K CU)`);
      console.log(`   2. Setup Instructions`);
      console.log(`   3. Flash Loan Borrow`);
      console.log(`   4. Jupiter Swaps (2 swaps)`);
      console.log(`   5. Flash Loan Repay`);
      console.log(`   6. Cleanup Instructions`);

      // 🏥 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: ИСПОЛЬЗУЕМ ПУСТОЙ АККАУНТ ДЛЯ FLASH LOANS (Error 6009)
      console.log(`🏥 ПРОВЕРЯЕМ MARGINFI АККАУНТ ДЛЯ FLASH LOANS...`);

      // 🔥 УДАЛЕНО: Дублирующаяся проверка health factor
      console.log(`✅ Используем существующий реальный аккаунт: ${marginfiAccount.address.toString()}`);

      // 🔥 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: НЕ ограничиваем сумму займа!
      // amount уже правильно рассчитан в createFullArbitrageTransaction
      console.log(`💰 Используем рассчитанную сумму займа: ${amount} microUSDC ($${(amount/1000000).toFixed(2)})`);

      // 🔍 ПРОВЕРЯЕМ АЛЬТЕРНАТИВНЫЕ БАНКИ ЕСЛИ ОСНОВНОЙ НЕ ПОДХОДИТ
      console.log(`🔍 Проверяем доступность альтернативных банков...`);

      let selectedBank = bank;
      let bankSwitched = false;

      try {
        // Проверяем ликвидность текущего банка
        const currentCapacity = bank.computeRemainingCapacity();
        const decimals = bank.mintDecimals || 9;
        const borrowCapacityUI = currentCapacity.borrowCapacity.div(Math.pow(10, decimals));
        const requestedAmountUI = amount / Math.pow(10, decimals);

        console.log(`💰 Текущий банк ${bank.tokenSymbol}: ${borrowCapacityUI.toString()} доступно, ${requestedAmountUI} требуется`);

        // Если недостаточно ликвидности, ищем альтернативы
        if (borrowCapacityUI.lt(requestedAmountUI)) {
          console.log(`⚠️ Недостаточная ликвидность в основном банке, ищем альтернативы...`);

          // Получаем все банки того же токена
          const allBanks = this.marginfiClient.banks;
          const alternativeBanks = [];

          for (const [bankAddress, bankData] of allBanks) {
            if (bankData.tokenSymbol === bank.tokenSymbol && !bankData.address.equals(bank.address)) {
              try {
                const altCapacity = bankData.computeRemainingCapacity();
                const altBorrowCapacityUI = altCapacity.borrowCapacity.div(Math.pow(10, bankData.mintDecimals || 9));

                if (altBorrowCapacityUI.gte(requestedAmountUI)) {
                  alternativeBanks.push({
                    bank: bankData,
                    capacity: altBorrowCapacityUI.toNumber()
                  });
                }
              } catch (e) {
                // Игнорируем банки с ошибками
              }
            }
          }

          if (alternativeBanks.length > 0) {
            // Выбираем банк с наибольшей ликвидностью
            alternativeBanks.sort((a, b) => b.capacity - a.capacity);
            selectedBank = alternativeBanks[0].bank;
            bankSwitched = true;

            console.log(`✅ Переключились на альтернативный банк:`);
            console.log(`   Адрес: ${selectedBank.address.toString()}`);
            console.log(`   Ликвидность: ${alternativeBanks[0].capacity.toFixed(6)} ${bank.tokenSymbol}`);
          } else {
            console.log(`❌ Альтернативные банки не найдены, продолжаем с основным`);
          }
        }
      } catch (capacityError) {
        console.log(`⚠️ Ошибка проверки ликвидности: ${capacityError.message}`);
      }

      // Обновляем bank если переключились
      if (bankSwitched) {
        bank = selectedBank;
        console.log(`🔄 Используем альтернативный банк: ${bank.address.toString()}`);
      }

      // 🚨 ПРОВЕРЯЕМ ЧТО ОФИЦИАЛЬНЫЙ МЕТОД СУЩЕСТВУЕТ
      if (typeof marginfiAccount.buildFlashLoanTx !== 'function') {
        throw new Error('buildFlashLoanTx метод не найден в MarginFi SDK - возможно устаревшая версия');
      }

      // 🚨 ИСПОЛЬЗУЕМ ОФИЦИАЛЬНЫЙ МЕТОД buildFlashLoanTx ПО ДОКУМЕНТАЦИИ!
      console.log(`🔧 Создаем BORROW и REPAY инструкции по официальному примеру...`);
      console.log(`📋 По документации: "const borrowIx = await marginfiAccount.makeBorrowIx(amount, bank.address)"`);
      console.log(`📋 По документации: "const repayIx = await marginfiAccount.makeRepayIx(amount, bank.address, true)"`);

      // 🔥 УДАЛЕНО: Дублирующаяся проверка Oracle и Health Factor - это мусор!

        // 3. ПРОВЕРКА ЛИКВИДНОСТИ БАНКА (Error 6027 - borrow cap exceeded)
        console.log(`🏦 Проверяем ликвидность банка...`);

        // Используем официальный метод computeRemainingCapacity() из документации
        const bankCapacity = bank.computeRemainingCapacity();
        const borrowCapacityRaw = bankCapacity.borrowCapacity;

        // Конвертируем в UI формат (учитываем decimals)
        const decimals = bank.mintDecimals || 9; // SOL = 9 decimals, USDC = 6 decimals
        const borrowCapacityUI = borrowCapacityRaw.div(Math.pow(10, decimals));
        const requestedAmountUI = amount / Math.pow(10, decimals);

        console.log(`💰 Доступная ликвидность: ${borrowCapacityUI.toString()} токенов`);
        console.log(`💰 Запрашиваемая сумма: ${requestedAmountUI} токенов`);
        console.log(`💰 Лимит займов банка: ${bank.config?.borrowLimit?.toString() || 'НЕ УСТАНОВЛЕН'}`);

        // Проверяем достаточность ликвидности
        if (borrowCapacityUI.lt(requestedAmountUI)) {
          const errorMsg = `🚨 ОШИБКА 6027 - Bank borrow cap exceeded!
            Банк: ${bank.address.toString()}
            Доступно: ${borrowCapacityUI.toString()} токенов
            Запрошено: ${requestedAmountUI} токенов
            Дефицит: ${(requestedAmountUI - borrowCapacityUI.toNumber()).toFixed(6)} токенов`;

          console.log(errorMsg);

          // Автоматически уменьшаем сумму до доступной ликвидности
          const maxAvailableAmount = borrowCapacityUI.multipliedBy(0.95); // 95% от доступного для безопасности
          const adjustedAmount = Math.floor(maxAvailableAmount.toNumber() * Math.pow(10, decimals));

          console.log(`🔧 АВТОМАТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Уменьшаем сумму до ${maxAvailableAmount.toString()} токенов`);
          console.log(`🔧 Скорректированная сумма: ${adjustedAmount} lamports`);

          amount = adjustedAmount;
        }

        console.log(`✅ ВСЕ ПРОВЕРКИ ПРОЙДЕНЫ: ${amount} lamports`);

      } catch (validationError) {
        console.log(`❌ Ошибка валидации: ${validationError.message}`);
        // Используем минимальную безопасную сумму
        amount = 1000000; // 0.001 SOL или 1 USDC
        console.log(`🔧 Используем минимальную безопасную сумму: ${amount} lamports`);
      }

      // 🚨 ДОПОЛНИТЕЛЬНАЯ ПРОВЕРКА: Безопасные числа для JavaScript
      const { BN } = require('bn.js');
      let safeAmount = amount;

      // Ограничиваем amount до безопасного размера для JavaScript Number
      const MAX_SAFE_AMOUNT = Number.MAX_SAFE_INTEGER / 1000; // Безопасный лимит

      if (typeof amount === 'number' && amount > MAX_SAFE_AMOUNT) {
        console.log(`⚠️ ИСПРАВЛЕНИЕ: amount ${amount} превышает безопасный лимит ${MAX_SAFE_AMOUNT}`);
        safeAmount = MAX_SAFE_AMOUNT;
        console.log(`✅ Ограничено до безопасного размера: ${safeAmount}`);
      } else if (amount > MAX_SAFE_AMOUNT) {
        console.log(`⚠️ БЕЗОПАСНОСТЬ: Ограничиваем большое число ${amount} до ${MAX_SAFE_AMOUNT}`);
        safeAmount = MAX_SAFE_AMOUNT;
        console.log(`✅ Ограничено до безопасного размера: ${safeAmount}`);
      }

      console.log(`🔍 ФИНАЛЬНАЯ ДИАГНОСТИКА:
        Исходный amount: ${amount}
        Безопасный amount: ${safeAmount}
        Тип: ${typeof safeAmount}`);



      try {
        // Проверяем есть ли у текущего аккаунта долги
        const balances = finalMarginfiAccount.activeBalances || [];
        let hasExistingDebt = false;

        console.log(`📊 Проверяем текущий аккаунт: ${balances.length} активных балансов`);

        for (const balance of balances) {
          if (balance.liabilityShares && balance.liabilityShares.gt && balance.liabilityShares.gt(0)) {
            hasExistingDebt = true;
            console.log(`🚨 Обнаружен долг в банке: ${balance.bankPk.toString()}`);
            break;
          }
        }

        if (hasExistingDebt) {
          console.log(`🚨 ТЕКУЩИЙ АККАУНТ ИМЕЕТ ДОЛГИ!`);
          console.log(`💡 ИСПОЛЬЗУЕМ СУЩЕСТВУЮЩИЙ АККАУНТ НЕСМОТРЯ НА ДОЛГИ`);
          console.log(`⚠️ Flash Loans могут работать с аккаунтами с долгами при правильном расчете`);
        } else {
          console.log(`✅ Текущий аккаунт не имеет долгов - идеально для Flash Loans`);
        }

        console.log(`✅ ИСПОЛЬЗУЕМ ТОЛЬКО СУЩЕСТВУЮЩИЙ РЕАЛЬНЫЙ АККАУНТ: ${finalMarginfiAccount.address.toString()}`);

      } catch (accountError) {
        console.log(`❌ Ошибка работы с аккаунтами: ${accountError.message}`);
        console.log(`🔧 Продолжаем с текущим аккаунтом, но с минимальной суммой займа`);

        // Уменьшаем сумму займа до минимума для безопасности
        safeAmount = Math.min(safeAmount, 1000); // Минимальная сумма
      }

      // 🔥 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: НЕ ИСПОЛЬЗУЕМ makeBorrowIx НАПРЯМУЮ!
      console.log(`🔥 ИСПРАВЛЕНИЕ ERROR 6009: НЕ используем makeBorrowIx напрямую!`);
      console.log(`💡 ПРИЧИНА: makeBorrowIx вызывает health check ВНЕ flash loan контекста`);
      console.log(`✅ РЕШЕНИЕ: Используем ТОЛЬКО buildFlashLoanTx для атомарных операций`);

      // НЕ создаем borrow инструкцию - она будет создана внутри buildFlashLoanTx!

      // 🔥 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: НЕ ИСПОЛЬЗУЕМ makeRepayIx НАПРЯМУЮ!
      console.log(`🔥 ИСПРАВЛЕНИЕ ERROR 6009: НЕ используем makeRepayIx напрямую!`);
      console.log(`💡 ПРИЧИНА: Все borrow/repay инструкции должны быть внутри buildFlashLoanTx`);
      console.log(`✅ РЕШЕНИЕ: buildFlashLoanTx создаст ВСЕ инструкции атомарно`);

      // НЕ создаем repay инструкцию - она будет создана внутри buildFlashLoanTx!

      // 🔒 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: ИСПОЛЬЗУЕМ ТОЛЬКО ЗАФИКСИРОВАННЫЕ ИНСТРУКЦИИ!
      console.log(`🔒 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Принудительное использование зафиксированных инструкций!`);
      console.log(`💡 ПРОБЛЕМА: Разные модули создают разные инструкции (12 vs 19)`);
      console.log(`🔧 РЕШЕНИЕ: Используем ТОЛЬКО зафиксированную конфигурацию!`);

      // 🔧 НОРМАЛИЗУЕМ ПЕРЕДАННЫЕ JUPITER ИНСТРУКЦИИ
      console.log(`🔧 НОРМАЛИЗАЦИЯ ПЕРЕДАННЫХ JUPITER ИНСТРУКЦИЙ`);

      if (!jupiterInstructions || !Array.isArray(jupiterInstructions)) {
        throw new Error('КРИТИЧЕСКАЯ ОШИБКА: jupiterInstructions не является массивом!');
      }

      console.log(`📊 Получено Jupiter инструкций: ${jupiterInstructions.length}`);

      // 🔧 НОРМАЛИЗУЕМ ИНСТРУКЦИИ
      const normalizedJupiterInstructions = normalizeInstructions(jupiterInstructions);
      console.log(`✅ Нормализовано инструкций: ${normalizedJupiterInstructions.length}`);

      // 🔍 КРИТИЧЕСКАЯ ПРОВЕРКА РЕЗУЛЬТАТА ПРИНУДИТЕЛЬНОЙ НОРМАЛИЗАЦИИ
      console.log(`🔍 КРИТИЧЕСКАЯ ПРОВЕРКА: Проверяем каждую принудительно нормализованную инструкцию...`);
      normalizedJupiterInstructions.forEach((ix, index) => {
        if (typeof ix.programId?.equals !== 'function') {
          console.log(`🚨 КРИТИЧЕСКАЯ ОШИБКА: Инструкция ${index + 1} не имеет programId.equals после принудительной нормализации!`);
          console.log(`   programId тип: ${typeof ix.programId}`);
          console.log(`   programId значение: ${ix.programId}`);
          throw new Error(`Инструкция ${index + 1}: programId.equals не является функцией после принудительной нормализации`);
        }
      });

      console.log(`✅ ВСЕ ИНСТРУКЦИИ ПРОШЛИ КРИТИЧЕСКУЮ ПРОВЕРКУ ПОСЛЕ ПРИНУДИТЕЛЬНОЙ НОРМАЛИЗАЦИИ!`);

      // 🔍 БЫСТРАЯ ПРОВЕРКА ПЕРВОЙ ИНСТРУКЦИИ
      if (normalizedJupiterInstructions.length > 0) {
        const firstIx = normalizedJupiterInstructions[0];
        console.log(`🔍 Быстрая проверка первой инструкции:`);
        console.log(`   programId тип: ${typeof firstIx.programId}`);
        console.log(`   programId.equals доступен: ${typeof firstIx.programId?.equals}`);
        console.log(`   это TransactionInstruction: ${firstIx.constructor?.name}`);
      }

      // 🔍 ДЕТАЛЬНАЯ ПРОВЕРКА РЕЗУЛЬТАТА НОРМАЛИЗАЦИИ
      console.log(`🔍 ДЕТАЛЬНАЯ ПРОВЕРКА РЕЗУЛЬТАТА НОРМАЛИЗАЦИИ:`);
      normalizedJupiterInstructions.forEach((ix, index) => {
        console.log(`   Нормализованная инструкция ${index + 1}:`);
        console.log(`     programId тип: ${typeof ix.programId}`);
        console.log(`     programId.equals доступен: ${typeof ix.programId?.equals}`);
        console.log(`     это TransactionInstruction: ${ix.constructor?.name}`);
        console.log(`     keys длина: ${ix.keys?.length || 0}`);
        console.log(`     data длина: ${ix.data?.length || 0}`);

        // 🚨 КРИТИЧЕСКАЯ ПРОВЕРКА: ЕСТЬ ЛИ АККАУНТЫ?
        if (!ix.keys || ix.keys.length === 0) {
          // 🔥 ИСПРАВЛЕНО: ComputeBudget инструкции ДОЛЖНЫ иметь 0 ключей!
          const programIdStr = ix.programId.toString();
          if (programIdStr === 'ComputeBudget111111111111111111111111111111') {
            console.log(`     ✅ ComputeBudget инструкция с 0 ключей - это нормально!`);
          } else {
            console.log(`     ❌ КРИТИЧЕСКАЯ ОШИБКА: Инструкция ${index + 1} НЕ ИМЕЕТ АККАУНТОВ!`);
            console.log(`     💡 Это приведет к ошибке "encoding overruns Uint8Array"`);
          }
        } else {
          console.log(`     ✅ Аккаунты присутствуют: ${ix.keys.length}`);
        }
      });

      // 🚀 БЫСТРАЯ ПРОВЕРКА ПРИБЫЛЬНОСТИ (УЖЕ ВЫПОЛНЕНА ПАРАЛЛЕЛЬНО!)
      console.log(`\n🚀 БЫСТРАЯ ПРОВЕРКА: Jupiter quote уже проверен на прибыльность параллельно!`);
      console.log(`✅ Прибыльность уже подтверждена в параллельной проверке - пропускаем дублирование`);

      // 🔧 ИСПРАВЛЕНО: Прибыльность уже проверена в isOpportunityProfitable() перед вызовом этой функции

      console.log(`🔥 ИСПРАВЛЕНИЕ: Передаем ТОЛЬКО нормализованные Jupiter инструкции в buildFlashLoanTx`);
      console.log(`💡 ПРИЧИНА: buildFlashLoanTx САМ создаст borrow и repay инструкции`);

      const allInstructions = [
        ...normalizedJupiterInstructions    // НОРМАЛИЗОВАННЫЕ Jupiter swaps (borrow/repay создаст buildFlashLoanTx)
      ];

      // 🔥 ДОПОЛНИТЕЛЬНАЯ ПРОВЕРКА: Убеждаемся что ВСЕ инструкции нормализованы
      console.log(`🔥 ДОПОЛНИТЕЛЬНАЯ ПРОВЕРКА: Проверяем ВСЕ инструкции в allInstructions...`);
      allInstructions.forEach((ix, index) => {
        if (typeof ix.programId?.equals !== 'function') {
          console.log(`🚨 КРИТИЧЕСКАЯ ОШИБКА: allInstructions[${index}] не имеет programId.equals!`);
          console.log(`   programId тип: ${typeof ix.programId}`);
          console.log(`   programId значение: ${ix.programId}`);
          throw new Error(`allInstructions[${index}]: programId.equals не является функцией`);
        }
      });

      console.log(`✅ ВСЕ ИНСТРУКЦИИ В allInstructions ПРОШЛИ ПРОВЕРКУ!`);

      console.log(`🎯 АТОМАРНАЯ ОПЕРАЦИЯ: займ и возврат в одной транзакции!`);
      console.log(`💡 Flash loan гарантирует: либо ВСЕ успешно, либо ВСЕ откатывается`);
      console.log(`💡 НЕТ реальных долгов - только атомарные операции!`);

      console.log(`🔧 Вызываем ОФИЦИАЛЬНЫЙ marginfiAccount.buildFlashLoanTx()...`);
      console.log(`📋 По документации: "buildFlashLoanTx({ ixs: [...jupiterInstructions] })"`);
      console.log(`   Всего инструкций: ${allInstructions.length} (только Jupiter - MarginFi SDK сам добавит все что нужно)`);
      console.log(`💡 buildFlashLoanTx САМ добавит borrow и repay инструкции!`);

      // � КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: buildFlashLoanTx ТРЕБУЕТ BORROW + REPAY ИНСТРУКЦИИ!
      console.log(`🔥 ИСПРАВЛЕНИЕ: По официальной документации buildFlashLoanTx требует borrow + repay инструкции!`);
      console.log(`� Официальный пример: buildFlashLoanTx({ ixs: [...borrowIx.instructions, ...repayIx.instructions] })`);

      // 🔥 СОЗДАЕМ ВСЕ FLASH LOAN ИНСТРУКЦИИ (ПО ОФИЦИАЛЬНОЙ ДОКУМЕНТАЦИИ ChatGPT)
      console.log(`� ИСПРАВЛЕНИЕ: Создаем BEGIN и END flash loan инструкции!`);

      // 🔥 УДАЛЯЕМ СТАРЫЙ КОД - СОЗДАЕМ ВСЕ ИНСТРУКЦИИ НИЖЕ

      // 3. Создаем BORROW инструкции
      console.log(`�💰 Создаем borrow инструкции для ${amount} micro-USDC...`);
      const borrowIx = await marginfiAccount.makeBorrowIx(amount, bank.address);
      console.log(`✅ Borrow инструкции созданы: ${borrowIx.instructions.length} инструкций`);

      // 4. Создаем REPAY инструкции
      console.log(`💰 Создаем repay инструкции с repayAll=true (ОФИЦИАЛЬНАЯ ДОКУМЕНТАЦИЯ)...`);
      const repayIx = await marginfiAccount.makeRepayIx(amount, bank.address, true); // ✅ ОФИЦИАЛЬНАЯ ДОКУМЕНТАЦИЯ: repayAll=true!
      console.log(`✅ Repay инструкции созданы: ${repayIx.instructions.length} инструкций`);
      console.log(`💡 repayAll=true автоматически рассчитает точную сумму для возврата`);

      // 🔥 НЕ СОЗДАЕМ END ИНСТРУКЦИИ - buildFlashLoanTx сделает это автоматически

      // 🔥 ВОЗВРАЩАЕМСЯ К ПОЛНОМУ ПОДХОДУ - ПРОСТОЙ НЕ СОЗДАЕТ BORROW!
      console.log(`🔥 ПРОБЛЕМА: Простой подход НЕ создает borrow инструкции!`);
      console.log(`� РЕШЕНИЕ: Используем полный подход с makeBorrowIx + makeRepayIx`);
      console.log(`📚 ПРАВИЛЬНАЯ ПОСЛЕДОВАТЕЛЬНОСТЬ: begin → borrow → arbitrage → repay → end`);

      // 🔥 ИСПРАВЛЕНИЕ: buildFlashLoanTx автоматически создает begin/end инструкции!
      // НЕ создаем beginIx и endIx вручную - buildFlashLoanTx сделает это сам
      console.log(`🔥 buildFlashLoanTx автоматически создаст begin/end инструкции с правильными endIndex`);

      // 🔒 ЖЕСТКО ЗАШИТЫЙ ПОРЯДОК ИНСТРУКЦИЙ - НИКОГДА НЕ МЕНЯЕТСЯ!
      // ========================================================================
      // КРИТИЧЕСКИ ВАЖНЫЙ ПОРЯДОК: НИКОГДА НЕ МЕНЯТЬ!
      // ========================================================================
      // 🔥 ИСПРАВЛЕНИЕ: buildFlashLoanTx АВТОМАТИЧЕСКИ создает begin/end инструкции!
      // Передаем ТОЛЬКО внутренние инструкции: borrow + arbitrage + repay
      // 🔧 СОЗДАЕМ БАЗОВЫЕ ИНСТРУКЦИИ БЕЗ ОБФУСКАЦИИ
      const baseFlashLoanInstructions = [
        ...borrowIx.instructions,     // 1. 🔒 ЗАЙМ USDC (ЖЕСТКО ЗАШИТО)
        ...allInstructions,           // 2. 🔒 АРБИТРАЖ Jupiter swaps (ЖЕСТКО ЗАШИТО)
        ...repayIx.instructions,      // 3. 🔒 ВОЗВРАТ USDC (ЖЕСТКО ЗАШИТО)
      ];

      // 🔒 ПРОВЕРКА ЖЕСТКО ЗАШИТОГО ПОРЯДКА
      console.log(`🔒 ЖЕСТКО ЗАШИТЫЙ ПОРЯДОК ПРИМЕНЕН УСПЕШНО!`);
      console.log(`🔒 ПОРЯДОК: buildFlashLoanTx автоматически добавит BEGIN → BORROW → JUPITER → REPAY → END`);

      console.log(`🔥 ИСПРАВЛЕНА АРХИТЕКТУРА: buildFlashLoanTx создает begin/end автоматически!`);

      console.log(`📋 Базовые инструкции для flash loan: ${baseFlashLoanInstructions.length}`);
      console.log(`   - Borrow: ${borrowIx.instructions.length}`);
      console.log(`   - Arbitrage: ${allInstructions.length}`);
      console.log(`   - Repay: ${repayIx.instructions.length}`);
      console.log(`   - Begin/End: будут добавлены buildFlashLoanTx автоматически`);

      // 🔒 ДИНАМИЧЕСКАЯ ОБФУСКАЦИЯ С 10 БАЙТ ЗАПАСОМ (ПРАВИЛЬНАЯ ЛОГИКА)
      console.log(`🔒 ПРИМЕНЯЕМ ДИНАМИЧЕСКУЮ ОБФУСКАЦИЮ С 10 БАЙТ ЗАПАСОМ...`);

      // 🔥 ИСПОЛЬЗУЕМ ЕДИНЫЙ МОДУЛЬ РАСЧЕТА РАЗМЕРА!
      console.log(`🔥 РАСЧЕТ РАЗМЕРА ЧЕРЕЗ ЕДИНЫЙ МОДУЛЬ...`);

      // Конвертируем инструкции в формат для расчета
      const customInstructions = baseFlashLoanInstructions.map(ix => ({
        keys: ix.keys || [],
        data: ix.data || Buffer.alloc(0)
      }));

      // Рассчитываем размер через единый модуль
      const sizeResult = calculateTransactionSize({
        signers: 3,
        instructions: ['FLASH_LOAN_BEGIN', 'FLASH_LOAN_BORROW', 'JUPITER_SWAP', 'JUPITER_SWAP', 'FLASH_LOAN_REPAY', 'FLASH_LOAN_END'],
        customInstructions: customInstructions,
        useALT: true,
        obfuscationLevel: 0
      });

      const baseSizeWithoutObfuscation = sizeResult.totalSize;

      // 🔥 ЛОГИРОВАНИЕ ЧЕРЕЗ ЕДИНЫЙ МОДУЛЬ
      console.log(`🔥 РЕЗУЛЬТАТ РАСЧЕТА ЧЕРЕЗ ЕДИНЫЙ МОДУЛЬ:`);
      logTransactionSizeBreakdown(sizeResult);

      // 🔍 ДЕТАЛЬНАЯ ДИАГНОСТИКА ИНСТРУКЦИЙ
      console.log(`🔍 ДЕТАЛЬНАЯ ДИАГНОСТИКА ${baseFlashLoanInstructions.length} ИНСТРУКЦИЙ:`);
      baseFlashLoanInstructions.forEach((ix, index) => {
        const keysCount = ix.keys ? ix.keys.length : 0;
        let dataSize = 0;
        if (ix.data) {
          if (Buffer.isBuffer(ix.data)) {
            dataSize = ix.data.length;
          } else if (Array.isArray(ix.data)) {
            dataSize = ix.data.length;
          } else if (typeof ix.data === 'object' && ix.data.length !== undefined) {
            dataSize = ix.data.length;
          }
        }
        const ixSize = 1 + 1 + keysCount + 4 + dataSize;
        console.log(`   [${index}] keys: ${keysCount}, data: ${dataSize} байт, total: ${ixSize} байт`);
      });
      console.log(`🔒 ЛИМИТ SOLANA: ${SOLANA_LIMITS.MAX_TRANSACTION_SIZE} байт`);
      console.log(`🔒 ЦЕЛЕВОЙ РАЗМЕР: ${SOLANA_LIMITS.RECOMMENDED_TARGET} байт`);

      // 🔧 РАСЧЕТ ДОСТУПНОГО МЕСТА ЧЕРЕЗ ЕДИНЫЙ МОДУЛЬ
      const availableSpace = sizeResult.availableSpace;
      let actualObfuscationSize = 0;
      let obfuscationLevel = 0;

      console.log(`🔍 ДОСТУПНО МЕСТА ДЛЯ ОБФУСКАЦИИ: ${availableSpace} байт`);

      // 🚨 ВРЕМЕННО ОТКЛЮЧАЕМ ОБФУСКАЦИЮ ДЛЯ ИСПРАВЛЕНИЯ "encoding overruns"
      console.log(`🚨 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: ОТКЛЮЧАЕМ ОБФУСКАЦИЮ для исправления "encoding overruns"`);
      console.log(`💡 ПРИЧИНА: Реальный размер транзакции больше расчетного`);
      console.log(`🔧 РЕШЕНИЕ: Сначала исправляем размер, потом добавляем обфускацию`);

      actualObfuscationSize = 0;
      obfuscationLevel = 0;
      console.log(`🔒 ОБФУСКАЦИЯ ОТКЛЮЧЕНА: Доступно ${availableSpace} байт, но отключаем для безопасности`);

      // 🔧 СОЗДАЕМ ФИНАЛЬНЫЕ ИНСТРУКЦИИ С ОБФУСКАЦИЕЙ
      let allFlashLoanInstructions = [...baseFlashLoanInstructions];

      if (actualObfuscationSize > 0 && obfuscationLevel > 0) {
        console.log(`🎭 ДОБАВЛЯЕМ ОБФУСКАЦИЮ: ${actualObfuscationSize} байт, уровень ${obfuscationLevel}`);

        // Создаем простые обфускационные инструкции в зависимости от уровня
        const obfuscationInstructions = [];

        if (obfuscationLevel >= 1) {
          // Минимальная обфускация: ФИКСИРОВАННАЯ Priority Fee
          obfuscationInstructions.push(
            ComputeBudgetProgram.setComputeUnitPrice({
              microLamports: 5000 // 🔧 ФИКСИРОВАННАЯ КОМИССИЯ 5000 microLamports
            })
          );
        }

        if (obfuscationLevel >= 2) {
          // Compute Budget: ФИКСИРОВАННЫЙ лимит
          obfuscationInstructions.push(
            ComputeBudgetProgram.setComputeUnitLimit({
              units: 1400000 // 🔧 ФИКСИРОВАННЫЙ лимит 1.4M units
            })
          );
        }

        // Добавляем обфускацию в начало (перед основными инструкциями)
        allFlashLoanInstructions = [
          ...obfuscationInstructions,
          ...baseFlashLoanInstructions
        ];

        console.log(`✅ Добавлено ${obfuscationInstructions.length} обфускационных инструкций`);
      } else {
        console.log(`⚠️ Обфускация пропущена: недостаточно места или отключена`);
      }

      // 🔥 ФИНАЛЬНЫЙ РАСЧЕТ ЧЕРЕЗ ЕДИНЫЙ МОДУЛЬ С ОБФУСКАЦИЕЙ
      const finalSizeResult = calculateTransactionSize({
        signers: 3,
        instructions: ['FLASH_LOAN_BEGIN', 'FLASH_LOAN_BORROW', 'JUPITER_SWAP', 'JUPITER_SWAP', 'FLASH_LOAN_REPAY', 'FLASH_LOAN_END'],
        customInstructions: allFlashLoanInstructions.map(ix => ({
          keys: ix.keys || [],
          data: ix.data || Buffer.alloc(0)
        })),
        useALT: true,
        obfuscationLevel: actualObfuscationSize > 0 ? obfuscationLevel : 0
      });

      console.log(`🔒 ФИНАЛЬНЫЙ РАЗМЕР (с обфускацией): ${finalSizeResult.totalSize} байт`);
      logTransactionSizeBreakdown(finalSizeResult);

      // 🚨 КРИТИЧЕСКАЯ ПРОВЕРКА ЧЕРЕЗ ЕДИНЫЙ МОДУЛЬ
      if (!finalSizeResult.isValid) {
        console.log(`❌ КРИТИЧЕСКАЯ ОШИБКА: Транзакция превышает лимит!`);
        throw new Error(`Transaction too large: ${finalSizeResult.totalSize} bytes > ${SOLANA_LIMITS.MAX_TRANSACTION_SIZE} bytes limit`);
      }

      if (finalSizeResult.availableSpace < 50) {
        console.log(`🚨 ПРЕДУПРЕЖДЕНИЕ: Мало свободного места (${finalSizeResult.availableSpace} байт)!`);
      }

      // 🔥 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: ПРИНУДИТЕЛЬНАЯ НОРМАЛИЗАЦИЯ ПЕРЕД buildFlashLoanTx!
      console.log(`🔥 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Принудительная нормализация перед buildFlashLoanTx!`);
      console.log(`💡 ПРОБЛЕМА: ix.programId.equals is not a function в MarginFi SDK`);
      console.log(`💡 ПРИЧИНА: MarginFi SDK ожидает правильные TransactionInstruction объекты`);

      // 🔥 ИСПОЛЬЗУЕМ ГЛОБАЛЬНО ИМПОРТИРОВАННУЮ ФУНКЦИЮ НОРМАЛИЗАЦИИ

      // 🔥 ПРИНУДИТЕЛЬНО НОРМАЛИЗУЕМ ВСЕ ИНСТРУКЦИИ ПЕРЕД buildFlashLoanTx!
      console.log(`🔥 ПРИНУДИТЕЛЬНАЯ НОРМАЛИЗАЦИЯ ${allFlashLoanInstructions.length} инструкций перед buildFlashLoanTx...`);
      const normalizedAllInstructions = normalizeInstructions(allFlashLoanInstructions);
      console.log(`✅ Принудительно нормализовано ${normalizedAllInstructions.length} инструкций для buildFlashLoanTx`);

      // 🔍 КРИТИЧЕСКАЯ ПРОВЕРКА КАЖДОЙ ИНСТРУКЦИИ ПЕРЕД buildFlashLoanTx
      console.log(`🔍 КРИТИЧЕСКАЯ ПРОВЕРКА: Проверяем каждую инструкцию перед buildFlashLoanTx...`);
      normalizedAllInstructions.forEach((ix, index) => {
        if (typeof ix.programId?.equals !== 'function') {
          console.log(`🚨 КРИТИЧЕСКАЯ ОШИБКА: Инструкция ${index + 1} не имеет programId.equals перед buildFlashLoanTx!`);
          console.log(`   programId тип: ${typeof ix.programId}`);
          console.log(`   programId значение: ${ix.programId}`);
          throw new Error(`Инструкция ${index + 1}: programId.equals не является функцией перед buildFlashLoanTx`);
        }
      });

      console.log(`✅ ВСЕ ИНСТРУКЦИИ ПРОШЛИ КРИТИЧЕСКУЮ ПРОВЕРКУ ПЕРЕД buildFlashLoanTx!`);

      // 🔥 КРИТИЧЕСКАЯ ДИАГНОСТИКА ALT ПЕРЕД buildFlashLoanTx!
      console.log(`🔥 КРИТИЧЕСКАЯ ДИАГНОСТИКА ALT ПЕРЕД buildFlashLoanTx:`);
      console.log(`   ALT таблиц передано: ${addressLookupTableAccounts.length}`);

      if (addressLookupTableAccounts.length === 0) {
        console.log(`❌ КРИТИЧЕСКАЯ ОШИБКА: ALT НЕ ПЕРЕДАНЫ В buildFlashLoanTx!`);
        console.log(`💡 ПРИЧИНА: Без ALT транзакция будет превышать лимит 1232 байт`);
        throw new Error('ALT таблицы не переданы в buildFlashLoanTx - транзакция превысит лимит размера');
      }

      // Проверяем каждую ALT таблицу
      let totalAltKeys = 0;
      addressLookupTableAccounts.forEach((alt, index) => {
        const keyCount = alt.state?.addresses?.length || 0;
        totalAltKeys += keyCount;
        console.log(`   ALT ${index + 1}: ${keyCount} ключей, адрес: ${alt.key.toString().slice(0, 8)}...`);

        if (keyCount === 0) {
          console.log(`❌ КРИТИЧЕСКАЯ ОШИБКА: ALT ${index + 1} ПУСТАЯ!`);
        }
      });

      console.log(`   📊 Всего ключей в ALT: ${totalAltKeys}`);

      if (totalAltKeys === 0) {
        console.log(`❌ КРИТИЧЕСКАЯ ОШИБКА: ВСЕ ALT ТАБЛИЦЫ ПУСТЫЕ!`);
        throw new Error('Все ALT таблицы пустые - транзакция превысит лимит размера');
      }

      // Проверяем покрытие ключей инструкций
      const instructionKeys = new Set();
      normalizedAllInstructions.forEach(ix => {
        if (ix.keys && Array.isArray(ix.keys)) {
          ix.keys.forEach(key => {
            instructionKeys.add(key.pubkey.toString());
          });
        }
      });

      const altKeys = new Set();
      addressLookupTableAccounts.forEach(alt => {
        if (alt.state && alt.state.addresses) {
          alt.state.addresses.forEach(addr => {
            altKeys.add(addr.toString());
          });
        }
      });

      const coveredKeys = Array.from(instructionKeys).filter(key => altKeys.has(key));
      const uncoveredKeys = Array.from(instructionKeys).filter(key => !altKeys.has(key));

      console.log(`   📊 Ключей в инструкциях: ${instructionKeys.size}`);
      console.log(`   ✅ Покрыто ALT: ${coveredKeys.length}`);
      console.log(`   ❌ НЕ покрыто ALT: ${uncoveredKeys.length}`);

      // 🔍 ДЕТАЛЬНАЯ ДИАГНОСТИКА НЕПОКРЫТЫХ КЛЮЧЕЙ
      if (uncoveredKeys.length > 0) {
        console.log(`🔍 ДЕТАЛЬНАЯ ДИАГНОСТИКА НЕПОКРЫТЫХ КЛЮЧЕЙ:`);
        console.log(`   Первые 10 непокрытых ключей:`);
        uncoveredKeys.slice(0, 10).forEach((key, i) => {
          console.log(`     ${i + 1}. ${key.slice(0, 8)}...${key.slice(-8)}`);
        });
      }

      // 🚨 КРИТИЧЕСКАЯ ПРОБЛЕМА: ALT НЕ ПОКРЫВАЮТ КЛЮЧИ!
      if (uncoveredKeys.length > 0) {
        console.log(`🚨 КРИТИЧЕСКАЯ ПРОБЛЕМА: ${uncoveredKeys.length} ключей НЕ покрыты ALT!`);
        console.log(`💡 ПРИЧИНА: ALT таблицы не содержат ключи из Jupiter инструкций`);

        // 🔥 ИЗВЛЕКАЕМ ПРАВИЛЬНЫЕ ALT АДРЕСА ИЗ JUPITER ИНСТРУКЦИЙ
        console.log(`🔥 ИЗВЛЕКАЕМ ALT АДРЕСА ИЗ JUPITER ИНСТРУКЦИЙ...`);
        const jupiterAltAddresses = new Set();

        normalizedAllInstructions.forEach((ix, index) => {
          if (ix.addressLookupTableAddresses && ix.addressLookupTableAddresses.length > 0) {
            console.log(`   Инструкция ${index}: ${ix.addressLookupTableAddresses.length} ALT адресов`);
            ix.addressLookupTableAddresses.forEach(addr => jupiterAltAddresses.add(addr));
          }
        });

        const jupiterAltArray = Array.from(jupiterAltAddresses);
        console.log(`🔥 НАЙДЕНО ${jupiterAltArray.length} уникальных Jupiter ALT адресов:`);
        jupiterAltArray.forEach((addr, i) => {
          console.log(`     ${i + 1}. ${addr.slice(0, 8)}...${addr.slice(-8)}`);
        });

        if (jupiterAltArray.length > 0) {
          console.log(`🔧 ПРИНУДИТЕЛЬНО ЗАГРУЖАЕМ ПРАВИЛЬНЫЕ JUPITER ALT...`);

          try {
            // Загружаем ПРАВИЛЬНЫЕ Jupiter ALT
            const correctJupiterALT = await this.altManager.loadJupiterALT(jupiterAltArray);
            if (correctJupiterALT && correctJupiterALT.length > 0) {
              console.log(`✅ ЗАГРУЖЕНО ${correctJupiterALT.length} ПРАВИЛЬНЫХ Jupiter ALT!`);

              // ЗАМЕНЯЕМ старые ALT на правильные
              allJupiterALT.length = 0;
              allJupiterALT.push(...correctJupiterALT);

              // Пересчитываем покрытие с ПРАВИЛЬНЫМИ ALT
              const correctAltKeys = new Set();
              correctJupiterALT.forEach(alt => {
                alt.addresses.forEach(addr => correctAltKeys.add(addr));
              });

              const finalCoveredKeys = Array.from(instructionKeys).filter(key => correctAltKeys.has(key));
              const finalUncoveredKeys = Array.from(instructionKeys).filter(key => !correctAltKeys.has(key));

              console.log(`🔄 ФИНАЛЬНЫЙ ПЕРЕСЧЕТ С ПРАВИЛЬНЫМИ ALT:`);
              console.log(`   ✅ Покрыто: ${finalCoveredKeys.length}/${instructionKeys.size} (${((finalCoveredKeys.length / instructionKeys.size) * 100).toFixed(1)}%)`);
              console.log(`   ❌ НЕ покрыто: ${finalUncoveredKeys.length}/${instructionKeys.size}`);

              if (finalUncoveredKeys.length <= 6) {
                console.log(`✅ ПРОБЛЕМА РЕШЕНА! Непокрытых ключей: ${finalUncoveredKeys.length} ≤ 6`);
              } else {
                console.log(`❌ ПРОБЛЕМА НЕ РЕШЕНА! Непокрытых ключей: ${finalUncoveredKeys.length} > 6`);
                console.log(`🔍 Первые 10 непокрытых ключей:`);
                finalUncoveredKeys.slice(0, 10).forEach((key, i) => {
                  console.log(`     ${i + 1}. ${key.slice(0, 8)}...${key.slice(-8)}`);
                });
              }

            } else {
              console.log(`❌ Не удалось загрузить правильные Jupiter ALT`);
            }
          } catch (altError) {
            console.log(`❌ Ошибка загрузки правильных Jupiter ALT: ${altError.message}`);
          }
        } else {
          console.log(`❌ НЕ НАЙДЕНО ALT адресов в Jupiter инструкциях!`);
          console.log(`🔧 ПРИНУДИТЕЛЬНО ЗАГРУЖАЕМ СТАНДАРТНЫЕ JUPITER ALT...`);

          try {
            // Принудительно загружаем стандартные Jupiter ALT таблицы
            const standardJupiterALT = await this.altManager.loadJupiterALT();
            if (standardJupiterALT && standardJupiterALT.length > 0) {
              console.log(`✅ ЗАГРУЖЕНО ${standardJupiterALT.length} СТАНДАРТНЫХ Jupiter ALT!`);

              // ЗАМЕНЯЕМ старые ALT на стандартные Jupiter ALT
              allJupiterALT.length = 0;
              allJupiterALT.push(...standardJupiterALT);

              // Пересчитываем покрытие со стандартными Jupiter ALT
              const standardAltKeys = new Set();
              standardJupiterALT.forEach(alt => {
                alt.addresses.forEach(addr => standardAltKeys.add(addr));
              });

              const finalCoveredKeys = Array.from(instructionKeys).filter(key => standardAltKeys.has(key));
              const finalUncoveredKeys = Array.from(instructionKeys).filter(key => !standardAltKeys.has(key));

              console.log(`🔄 ПЕРЕСЧЕТ СО СТАНДАРТНЫМИ JUPITER ALT:`);
              console.log(`   ✅ Покрыто: ${finalCoveredKeys.length}/${instructionKeys.size} (${((finalCoveredKeys.length / instructionKeys.size) * 100).toFixed(1)}%)`);
              console.log(`   ❌ НЕ покрыто: ${finalUncoveredKeys.length}/${instructionKeys.size}`);

              if (finalUncoveredKeys.length <= 6) {
                console.log(`✅ ПРОБЛЕМА РЕШЕНА! Непокрытых ключей: ${finalUncoveredKeys.length} ≤ 6`);
              } else {
                console.log(`❌ ПРОБЛЕМА НЕ РЕШЕНА! Непокрытых ключей: ${finalUncoveredKeys.length} > 6`);
                console.log(`🔍 Первые 10 непокрытых ключей:`);
                finalUncoveredKeys.slice(0, 10).forEach((key, i) => {
                  console.log(`     ${i + 1}. ${key.slice(0, 8)}...${key.slice(-8)}`);
                });
              }

            } else {
              console.log(`❌ Не удалось загрузить стандартные Jupiter ALT`);
            }
          } catch (altError) {
            console.log(`❌ Ошибка загрузки стандартных Jupiter ALT: ${altError.message}`);
          }
        }

        // Показываем первые 10 непокрытых ключей для диагностики
        console.log(`   🔍 Первые 10 непокрытых ключей:`);
        uncoveredKeys.slice(0, 10).forEach((key, index) => {
          console.log(`      ${index + 1}. ${key.slice(0, 8)}...${key.slice(-8)}`);
        });
      }

      // 🔥 ИСПОЛЬЗУЕМ ОФИЦИАЛЬНЫЙ buildFlashLoanTx С НОРМАЛИЗОВАННЫМИ ИНСТРУКЦИЯМИ!
      console.log(`🔧 Вызываем marginfiAccount.buildFlashLoanTx() с НОРМАЛИЗОВАННЫМИ инструкциями...`);

      // 🔥 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: ДОБАВЛЯЕМ WALLET КАК SIGNER ДЛЯ JUPITER tokenLedgerInstruction!
      console.log(`🔥 ИСПРАВЛЕНИЕ "Provided owner is not allowed": Добавляем wallet как signer`);
      console.log(`💡 ПРИЧИНА: Jupiter tokenLedgerInstruction требует владельца token account`);
      console.log(`✅ РЕШЕНИЕ: Указываем wallet.payer как signer для token operations`);

      // 🔥 КРИТИЧЕСКАЯ ПРОВЕРКА ПЕРЕД buildFlashLoanTx: КАЖДАЯ ИНСТРУКЦИЯ ДОЛЖНА ИМЕТЬ programId.equals!
      console.log(`🔥 КРИТИЧЕСКАЯ ПРОВЕРКА ПЕРЕД buildFlashLoanTx: Проверяем каждую инструкцию...`);
      normalizedAllInstructions.forEach((ix, index) => {
        if (typeof ix.programId?.equals !== 'function') {
          console.log(`🚨 КРИТИЧЕСКАЯ ОШИБКА: Инструкция ${index + 1} не имеет programId.equals перед buildFlashLoanTx!`);
          console.log(`   programId тип: ${typeof ix.programId}`);
          console.log(`   programId значение: ${ix.programId}`);
          console.log(`   ix тип: ${typeof ix}`);
          console.log(`   ix конструктор: ${ix.constructor?.name}`);
          throw new Error(`КРИТИЧЕСКАЯ ОШИБКА: Инструкция ${index + 1} не имеет programId.equals перед buildFlashLoanTx`);
        }
      });
      console.log(`✅ ВСЕ ${normalizedAllInstructions.length} ИНСТРУКЦИЙ ПРОШЛИ КРИТИЧЕСКУЮ ПРОВЕРКУ ПЕРЕД buildFlashLoanTx!`);

      // 🔧 ИСПРАВЛЕНО: Используем адаптер вместо прямого вызова buildFlashLoanTx
      console.log(`🔧 ИСПОЛЬЗУЕМ MARGINFI АДАПТЕР вместо прямого вызова buildFlashLoanTx`);
      const flashLoanTx = await this.marginfiAdapter.buildFlashLoanTx(marginfiAccount, {
        ixs: normalizedAllInstructions, // ✅ НОРМАЛИЗОВАННЫЕ И ПРОВЕРЕННЫЕ ИНСТРУКЦИИ!
        signers: [],                    // 🔥 ИСПРАВЛЕНО: Пустой массив (wallet добавляется автоматически)
        repayAll: true                  // 🔥 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: repayAll: true для правильного flash loan!
      }, addressLookupTableAccounts);   // ✅ ALT для сжатия

      console.log(`✅ buildFlashLoanTx выполнен успешно!`);

      console.log(`✅ VersionedTransaction создана напрямую с ALT сжатием`);

      // 🔍 КРИТИЧЕСКАЯ ДИАГНОСТИКА СОЗДАННОЙ ТРАНЗАКЦИИ
      console.log(`🔍 КРИТИЧЕСКАЯ ДИАГНОСТИКА СОЗДАННОЙ ТРАНЗАКЦИИ:`);
      console.log(`   Тип транзакции: ${flashLoanTx.constructor.name}`);
      console.log(`   Версия: ${flashLoanTx.version}`);

      if (flashLoanTx.message) {
        console.log(`   Message тип: ${flashLoanTx.message.constructor.name}`);
        console.log(`   Инструкций в message: ${flashLoanTx.message.instructions?.length || 'undefined'}`);
        console.log(`   Account keys: ${flashLoanTx.message.staticAccountKeys?.length || 'undefined'} статических`);
        console.log(`   ALT в message: ${flashLoanTx.message.addressTableLookups?.length || 'undefined'}`);

        // Проверяем ALT в созданной транзакции
        if (flashLoanTx.message.addressTableLookups) {
          flashLoanTx.message.addressTableLookups.forEach((lookup, index) => {
            console.log(`     ALT ${index + 1}: ${lookup.accountKey.toString().slice(0, 8)}... (${lookup.readonlyIndexes?.length || 0} readonly, ${lookup.writableIndexes?.length || 0} writable)`);
          });
        }
      }

      // 🔍 ПРОВЕРЯЕМ РЕАЛЬНЫЙ РАЗМЕР ТРАНЗАКЦИИ ПЕРЕД SERIALIZE
      console.log(`🔍 ПРОВЕРЯЕМ РЕАЛЬНЫЙ РАЗМЕР ТРАНЗАКЦИИ ПЕРЕД SERIALIZE:`);

      try {
        // Пытаемся сериализовать для проверки размера
        const serialized = flashLoanTx.message.serialize();
        console.log(`✅ Сериализация успешна: ${serialized.length} байт`);

        if (serialized.length > 1232) {
          console.log(`❌ КРИТИЧЕСКАЯ ОШИБКА: Размер ${serialized.length} байт превышает лимит 1232!`);
          console.log(`💡 ПРИЧИНА: ALT не применяются правильно или недостаточно сжатия`);
          throw new Error(`Транзакция слишком большая: ${serialized.length} байт > 1232 байт`);
        } else {
          console.log(`✅ Размер в пределах лимита: ${serialized.length}/1232 байт (запас: ${1232 - serialized.length})`);
        }
      } catch (serializeError) {
        console.log(`❌ ОШИБКА СЕРИАЛИЗАЦИИ: ${serializeError.message}`);
        console.log(`💡 ПРИЧИНА: Транзакция не может быть сериализована`);
        throw serializeError;
      }
      console.log(`🔍 ПРОВЕРЯЕМ РЕАЛЬНЫЙ РАЗМЕР ТРАНЗАКЦИИ:`);
      console.log(`   Внутренних инструкций: ${allFlashLoanInstructions.length}`);
      console.log(`   ALT таблиц: ${addressLookupTableAccounts.length}`);

      try {
        const txSize = flashLoanTx.serialize().length;
        console.log(`✅ РЕАЛЬНЫЙ РАЗМЕР ТРАНЗАКЦИИ: ${txSize} байт`);
        console.log(`   Лимит Solana: 1232 байт`);
        console.log(`   Превышение: ${txSize > 1232 ? 'ДА' : 'НЕТ'} (${txSize - 1232} байт)`);

        // 🔍 ДЕТАЛЬНАЯ ДИАГНОСТИКА РАЗМЕРА
        console.log(`🔍 ДИАГНОСТИКА РАЗМЕРА:`);
        console.log(`   Расчетный размер: ${finalSizeResult.totalSize} байт`);
        console.log(`   Реальный размер: ${txSize} байт`);
        console.log(`   Разница: ${txSize - finalSizeResult.totalSize} байт`);
        console.log(`   Ошибка расчета: ${((txSize - finalSizeResult.totalSize) / finalSizeResult.totalSize * 100).toFixed(1)}%`);

        if (txSize <= 1232) {
          console.log(`🎉 ТРАНЗАКЦИЯ ПОМЕЩАЕТСЯ В ЛИМИТ!`);
        } else {
          console.log(`❌ ТРАНЗАКЦИЯ ПРЕВЫШАЕТ ЛИМИТ НА ${txSize - 1232} байт`);
        }
      } catch (serializeError) {
        console.log(`❌ ОШИБКА ПРИ SERIALIZE: ${serializeError.message}`);

        if (serializeError.message.includes('encoding overruns Uint8Array')) {
          console.log(`🚨 КРИТИЧЕСКАЯ ОШИБКА: Транзакция превышает лимит Solana!`);
          console.log(`💡 ПРИЧИНА: Реальный размер больше 1232 байт`);
          console.log(`🔧 РЕШЕНИЕ: Нужно уменьшить количество инструкций или отключить обфускацию`);

          // Пробрасываем ошибку дальше для обработки
          throw new Error(`Transaction too large: encoding overruns Uint8Array`);
        }

        console.log(`💡 Транзакция слишком большая для serialize`);
      }

      console.log(`✅ ОФИЦИАЛЬНЫЙ Flash Loan создан через MarginFi SDK!`);
      console.log(`   Тип: ${flashLoanTx.constructor.name}`);
      console.log(`   🎯 ПРАВИЛЬНАЯ СТРУКТУРА СОЗДАНА ОФИЦИАЛЬНЫМ SDK!`);

      // 🚨 БЕЗОПАСНАЯ ПРОВЕРКА РАЗМЕРА БЕЗ SERIALIZE
      console.log(`\n📊 АНАЛИЗ ОФИЦИАЛЬНОГО FLASH LOAN (БЕЗ SERIALIZE):`);
      console.log(`   Тип: ${flashLoanTx.constructor.name}`);
      console.log(`   🎯 ПРАВИЛЬНАЯ СТРУКТУРА СОЗДАНА ОФИЦИАЛЬНЫМ SDK!`);

      // Пытаемся получить размер безопасно
      let txSize = 'неизвестно';
      let sizeCheckPassed = false;

      try {
        // 🔍 СНАЧАЛА ПРОБУЕМ БЕЗ ПОДПИСЕЙ (более безопасно)
        const unsignedSize = flashLoanTx.serialize({
          requireAllSignatures: false,
          verifySignatures: false
        }).length;

        console.log(`   ✅ Размер БЕЗ подписей: ${unsignedSize} байт`);

        if (unsignedSize <= 1232) {
          // Если без подписей помещается, пробуем с подписями
          try {
            txSize = flashLoanTx.serialize().length;
            sizeCheckPassed = true;
            console.log(`   ✅ Размер С подписями: ${txSize} байт`);
          } catch (signedError) {
            console.log(`   ⚠️ Ошибка с подписями: ${signedError.message}`);
            txSize = unsignedSize;
            sizeCheckPassed = false;

            if (signedError.message.includes('encoding overruns')) {
              console.log(`   🚨 ПРОБЛЕМА: Подписи вызывают "encoding overruns"!`);
              console.log(`   💡 РЕШЕНИЕ: Транзакция будет работать, но нужно подписывать осторожно`);
            }
          }
        } else {
          console.log(`   ❌ Даже без подписей превышает лимит!`);
          txSize = unsignedSize;
          sizeCheckPassed = false;
        }

        console.log(`   Лимит: 1232 байт`);
        console.log(`   Превышение: ${typeof txSize === 'number' && txSize > 1232 ? 'ДА' : 'НЕТ'} (${typeof txSize === 'number' ? txSize - 1232 : 'N/A'} байт)`);
        console.log(`   Запас: ${typeof txSize === 'number' && txSize <= 1232 ? (1232 - txSize) : 0} байт`);
        console.log(`   Может сериализоваться: ${sizeCheckPassed ? 'ДА' : 'НЕТ'}`);

      } catch (sizeError) {
        console.log(`   ❌ КРИТИЧЕСКАЯ ОШИБКА СЕРИАЛИЗАЦИИ: ${sizeError.message}`);

        if (sizeError.message.includes('encoding overruns')) {
          console.log(`   🚨 ОБНАРУЖЕНА ОШИБКА "encoding overruns Uint8Array"!`);
          console.log(`   💡 ПРИЧИНА: Транзакция превышает внутренние буферы Solana web3.js`);
          console.log(`   🔧 РЕШЕНИЯ:`);
          console.log(`      1. Уменьшить сумму swap (меньше маршрутов)`);
          console.log(`      2. Использовать onlyDirectRoutes=true в Jupiter`);
          console.log(`      3. Использовать maxAccounts=20 или меньше`);
          console.log(`      4. Разбить операцию на несколько транзакций`);
          console.log(`      5. Использовать более эффективное ALT сжатие`);

          // Возвращаем ошибку с подробным объяснением
          throw new Error(`Transaction too complex for Solana buffers. Reduce instruction count or improve ALT compression.`);
        } else {
          throw sizeError;
        }
      }

      console.log(`🎉 УСПЕШНО! createFlashLoanTransaction ЗАВЕРШЕН!`);
      console.log(`   Тип возвращаемой транзакции: ${flashLoanTx?.constructor?.name || 'undefined'}`);
      console.log(`   instanceof VersionedTransaction: ${flashLoanTx instanceof VersionedTransaction}`);
      console.log(`   Метод sign: ${typeof flashLoanTx?.sign}`);
      console.log(`   Метод serialize: ${typeof flashLoanTx?.serialize}`);

      // 🔥 ИСПОЛЬЗУЕМ COMPLETE FLASH LOAN STRUCTURE ВМЕСТО СТАРОЙ СИСТЕМЫ
      console.log(`🔥 Создание новой транзакции через Complete Flash Loan Structure...`);

      try {
        // Обновляем marginfiAccountAddress если доступен
        if (marginfiAccount?.address) {
          this.completeFlashLoanStructure.marginfiAccountAddress = marginfiAccount.address;
        }

        // Создаем полную транзакцию через наш централизованный сборщик
        const result = await this.completeFlashLoanStructure.createCompleteFlashLoanTransactionWithALT();

        console.log(`✅ Complete Flash Loan Structure создал транзакцию:`);
        console.log(`   📊 Инструкций: ${result.instructions.length}`);
        console.log(`   🗜️ ALT таблиц: ${result.addressLookupTableAccounts.length}`);
        console.log(`   📊 Всего адресов в ALT: ${result.compressionStats.totalAddresses}`);

        // Создаем VersionedTransaction из результата
        const { VersionedTransaction, TransactionMessage } = require('@solana/web3.js');
        const { blockhash } = await this.connection.getLatestBlockhash('confirmed');

        const messageV0 = new TransactionMessage({
          payerKey: this.userPublicKey,
          recentBlockhash: blockhash,
          instructions: result.instructions
        }).compileToV0Message(result.addressLookupTableAccounts);

        const optimizedTransaction = new VersionedTransaction(messageV0);

        // Подписываем транзакцию (включая newPosition если есть)
        const signers = [this.wallet];
        if (this.completeFlashLoanStructure.newPosition) {
          signers.push(this.completeFlashLoanStructure.newPosition);
        }
        optimizedTransaction.sign(signers);

        const optimizedResult = {
          transaction: optimizedTransaction,
          instructions: result.instructions,
          addressLookupTableAccounts: result.addressLookupTableAccounts,
          finalSize: optimizedTransaction.serialize().length,
          availableSpace: 1232 - optimizedTransaction.serialize().length,
          compressionStats: result.compressionStats
        };

        // 🔥 ОБНОВЛЯЕМ МАСТЕР С РЕАЛЬНЫМИ ДАННЫМИ ALT ЗАГРУЗКИ
        const altLoadingStatus = {
          jupiterALTCount: lookupTables.length,
          totalALTTables: lookupTables.length,
          altAddresses: lookupTables.map(alt => alt.key?.toString() || 'неизвестно'),
          customALTAddress: 'FAeyUf4AdG7vTQ2Q89r9hWBkPdYkykJKG4CxgqArBGXe',
          compressionWorking: lookupTables.length > 0,
          loadingMethod: lookupTables.length > 0 ? 'РЕАЛЬНАЯ ЗАГРУЗКА' : 'ЗАГЛУШКИ',
          transactionSize: optimizedResult.finalSize,
          compressionSavings: lookupTables.length * 32 * 10 // Примерная экономия
        };

        console.log(`🔥 ОБНОВЛЯЕМ МАСТЕР С РЕАЛЬНЫМИ ДАННЫМИ ALT:`);
        console.log(`   📊 ALT таблиц: ${altLoadingStatus.totalALTTables}`);
        console.log(`   🔥 Сжатие работает: ${altLoadingStatus.compressionWorking ? 'ДА' : 'НЕТ'}`);
        console.log(`   📋 Метод загрузки: ${altLoadingStatus.loadingMethod}`);
        console.log(`   💾 Размер транзакции: ${altLoadingStatus.transactionSize} байт`);

        // Возвращаем новую транзакцию от Complete Flash Loan Structure
        return optimizedResult.transaction;

      } catch (completeFlashLoanError) {
        console.log(`⚠️ Complete Flash Loan Structure ошибка (не критично): ${completeFlashLoanError.message}`);
        // Возвращаем оригинальную транзакцию даже при ошибке Complete Flash Loan Structure
        return flashLoanTx;
      }
  }

  /**
   * 🔧 УСТАНОВКА ALT MANAGER (ПЕРЕДАЧА ССЫЛКИ)
   */
  setAltManager(altManager) {
    this.altManager = altManager;
    console.log(`🔧 ALT Manager установлен в атомарный строитель`);
  }

  /**
   * 🏦 ГЛАВНЫЙ МЕТОД: СОЗДАНИЕ FLASH LOAN ТРАНЗАКЦИИ (ТРЕБУЕТСЯ СИСТЕМОЙ!)
   * Этот метод вызывается из real-trading-executor.js
   */
  async buildAtomicFlashLoanTransaction(params) {
    try {
      console.log(`🏦 buildAtomicFlashLoanTransaction вызван`);
      console.log(`   inputMint: ${params.inputMint}`);
      console.log(`   outputMint: ${params.outputMint}`);
      console.log(`   amount: ${params.amount}`);
      console.log(`   slippageBps: ${params.slippageBps}`);

      // 🔥 УДАЛЕНО: Дублирующаяся инициализация Health Factor Manager

      // Проверяем есть ли MarginFi аккаунт для Flash Loans
      if (!this.marginfiAccount) {
        console.log('⚠️ MarginFi аккаунт не установлен - создаем транзакцию БЕЗ Flash Loans');

        // Создаем обычную Jupiter транзакцию без Flash Loans
        const swap1Params = {
          inputMint: params.inputMint,
          outputMint: params.outputMint,
          amount: params.amount,
          slippageBps: params.slippageBps
        };

        const swap2Params = {
          inputMint: params.outputMint,
          outputMint: params.inputMint,
          amount: params.amount, // Будет пересчитан
          slippageBps: params.slippageBps
        };

        return await this.createAtomicTransactionWithTwoJupiterSwaps(
          swap1Params,
          swap2Params,
          null // Без MarginFi инструкций
        );
      }

      // Создаем Flash Loan транзакцию с MarginFi
      console.log('✅ MarginFi аккаунт найден - создаем Flash Loan транзакцию');

      const swap1Params = {
        inputMint: params.inputMint,
        outputMint: params.outputMint,
        amount: params.amount,
        slippageBps: params.slippageBps
      };

      const swap2Params = {
        inputMint: params.outputMint,
        outputMint: params.inputMint,
        amount: params.amount, // Будет пересчитан
        slippageBps: params.slippageBps
      };

      return await this.createAtomicTransactionWithInstructionProcessor(
        swap1Params,
        swap2Params,
        params.inputMint // tokenMint для Flash Loan
      );

    } catch (error) {
      console.error(`❌ Ошибка buildAtomicFlashLoanTransaction: ${error.message}`);
      throw error;
    }
  }

  /**
   * ⚛️ СОЗДАНИЕ ПОЛНОЙ АРБИТРАЖНОЙ ТРАНЗАКЦИИ (ПРАВИЛЬНАЯ АРХИТЕКТУРА)
   */
  async createFullArbitrageTransaction(opportunity, tradeAmount) {
    try {
      console.log(`⚛️ createFullArbitrageTransaction вызван`);
      console.log(`   Opportunity: ${opportunity?.token || 'неизвестно'}`);
      console.log(`   Trade Amount: ${tradeAmount || 'неизвестно'}`);

      // 🔥 СНАЧАЛА СОЗДАЕМ ВСЕ ИНСТРУКЦИИ, ПОТОМ ПЕРЕДАЕМ В COMPLETE FLASH LOAN STRUCTURE
      console.log(`🔥 Создание всех инструкций для Flash Loan арбитража...`);

      // 🧠 ИСПОЛЬЗУЕМ УМНЫЙ АНАЛИЗАТОР ДЛЯ ОПРЕДЕЛЕНИЯ РАЗМЕРА ПОЗИЦИИ!
      let flashLoanAmount = tradeAmount || 10000; // По умолчанию $10,000

      // 🔥 КОНВЕРТИРУЕМ USD В USDC МИКРО-ЕДИНИЦЫ (6 decimals)
      if (flashLoanAmount < 1000) {
        // Если меньше $1000, скорее всего это уже в USDC
        flashLoanAmount = flashLoanAmount;
      } else {
        // Если больше $1000, это USD - конвертируем в USDC
        flashLoanAmount = flashLoanAmount; // Для USDC 1 USD = 1 USDC
      }

      console.log(`🧠 УМНЫЙ АНАЛИЗАТОР: Flash Loan сумма $${flashLoanAmount.toLocaleString()}`);
      console.log(`💡 ЛОГИКА: Занимаем USDC → USDC→SOL (Jupiter) → возвращаем USDC`);
      console.log(`🔧 МЕТОД: Прямое создание VersionedTransaction с динамическим размером позиции`);

      // 🔥 ИСПОЛЬЗУЕМ ОФИЦИАЛЬНУЮ ПОСЛЕДОВАТЕЛЬНОСТЬ MARGINFI:
      // Получаем MarginFi client из нашего объекта
      console.log(`🔍 ДИАГНОСТИКА MarginFi client в createFullArbitrageTransaction:`);
      console.log(`   this.marginfiClient: ${!!this.marginfiClient}`);
      console.log(`   this.marginfiAccount: ${!!this.marginfiAccount}`);
      console.log(`   this.marginfiFlashLoan: ${!!this.marginfiFlashLoan}`);

      const marginfiClient = this.marginfiClient;
      if (!marginfiClient) {
        console.log(`❌ КРИТИЧЕСКАЯ ОШИБКА: MarginFi client недоступен!`);
        console.log(`🔍 ДИАГНОСТИКА ПРОБЛЕМЫ:`);
        console.log(`   this.marginfiClient: ${this.marginfiClient}`);
        console.log(`   this.marginfiFlashLoan: ${!!this.marginfiFlashLoan}`);
        console.log(`   this.marginfiFlashLoan?.client: ${!!this.marginfiFlashLoan?.client}`);

        // 🔥 ПОПЫТКА ВОССТАНОВЛЕНИЯ: Получаем client из marginfiFlashLoan
        if (this.marginfiFlashLoan && this.marginfiFlashLoan.client) {
          console.log(`🔧 ВОССТАНОВЛЕНИЕ: Используем client из marginfiFlashLoan...`);
          this.marginfiClient = this.marginfiFlashLoan.client;
          console.log(`✅ MarginFi client восстановлен из marginfiFlashLoan!`);
        } else {
          throw new Error("MarginFi client not available");
        }
      }

      // 🔥 ОФИЦИАЛЬНЫЙ СПОСОБ: ПОЛУЧАЕМ ИЛИ СОЗДАЕМ ACCOUNT
      console.log('🔍 Получаем MarginFi accounts для пользователя...');
      const marginfiAccounts = await marginfiClient.getMarginfiAccountsForAuthority();

      let marginfiAccount;
      if (marginfiAccounts.length === 0) {
        console.log('❌ MarginFi account не найден!');
        throw new Error('No MarginFi account found. Please create an account first.');
      } else {
        // 🔥 ПРОВЕРЯЕМ КАЖДЫЙ АККАУНТ НА НАЛИЧИЕ ДОЛГОВ
        console.log(`🔍 НАЙДЕНО ${marginfiAccounts.length} существующих аккаунтов, проверяем их состояние...`);

        let cleanAccount = null;

        for (let i = 0; i < marginfiAccounts.length; i++) {
          const account = marginfiAccounts[i];
          console.log(`📊 Проверяем аккаунт ${i + 1}: ${account.address.toString()}`);

          try {
            // 🔥 ПРОВЕРЯЕМ РЕАЛЬНЫЕ БАЛАНСЫ ВМЕСТО activeBalances!
            let hasRealDebt = false;

            if (account.balances && account.balances.length > 0) {
              for (const balance of account.balances) {
                const liabilities = parseFloat(balance.liabilityShares.toString());
                if (liabilities > 0) {
                  hasRealDebt = true;
                  console.log(`🚨 РЕАЛЬНЫЙ ДОЛГ: ${liabilities} (Bank: ${balance.bankPk.toString().slice(0, 8)}...)`);
                  break;
                }
              }
            }

            if (!hasRealDebt) {
              console.log(`✅ АККАУНТ ЧИСТЫЙ! НЕТ РЕАЛЬНЫХ ДОЛГОВ! Используем его для flash loans`);
              cleanAccount = account;
              break;
            } else {
              console.log(`⚠️ Аккаунт имеет реальные долги - пропускаем`);
            }
          } catch (error) {
            console.log(`⚠️ Ошибка проверки аккаунта: ${error.message}`);
          }
        }

        if (cleanAccount) {
          marginfiAccount = cleanAccount;
          console.log(`✅ ИСПОЛЬЗУЕМ ЧИСТЫЙ аккаунт: ${marginfiAccount.address.toString()}`);
        } else {
          console.log(`🚨 ВСЕ АККАУНТЫ ИМЕЮТ ДОЛГИ!`);
          console.log(`✅ ИСПОЛЬЗУЕМ ПЕРВЫЙ СУЩЕСТВУЮЩИЙ АККАУНТ НЕСМОТРЯ НА ДОЛГИ`);
          marginfiAccount = marginfiAccounts[0];
          console.log(`✅ Используем аккаунт: ${marginfiAccount.address.toString()}`);
        }
      }

      const usdcBank = marginfiClient.getBankByTokenSymbol("USDC");
      if (!usdcBank) throw new Error("USDC bank not found");

      // 🔥 ПРАВИЛЬНАЯ НОВАЯ АРХИТЕКТУРА: СОЗДАЕМ JUPITER ИНСТРУКЦИИ ДЛЯ FLASH LOAN!
      const jupiterParams = {
        inputMint: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // USDC (займ)
        outputMint: 'So********************************111111112', // SOL (покупаем)
        amount: this.convertUsdToNativeAmount(flashLoanAmount, 'USDC'), // ИСПРАВЛЕНО: Правильная конвертация
        slippageBps: 300  // 🔥 ИСПРАВЛЕНО: 3% slippage для DEX
      };

      console.log(`💰 Flash Loan сумма: $${flashLoanAmount} = ${jupiterParams.amount} microUSDC`);

      console.log(`🔗 Получение Jupiter swap инструкций для ${jupiterParams.inputMint} → ${jupiterParams.outputMint}, сумма: ${jupiterParams.amount}`);

      // 🔥 ПРАВИЛЬНЫЙ СПОСОБ: Создаем экземпляр класса JupiterSwapInstructions
      const JupiterSwapInstructions = require('../jupiter-swap-instructions');
      const jupiterSwap = new JupiterSwapInstructions(this.connection, this.wallet);

      // Используем правильный метод класса для получения инструкций
      const jupiterSwapData = await jupiterSwap.createJupiterInstructionsForArbitrage(
        jupiterParams.inputMint,
        jupiterParams.outputMint,
        jupiterParams.amount,
        jupiterParams.slippageBps
      );

      // 🔥 ПРАВИЛЬНОЕ ИЗВЛЕЧЕНИЕ: Включаем ВСЕ инструкции (включая compute budget)
      const jupiterInstructions = [];

      // � ИСПРАВЛЕНИЕ: ИЗВЛЕКАЕМ ALT ИЗ ГОТОВОЙ JUPITER ТРАНЗАКЦИИ (КАК В СТАРОМ КОДЕ)
      let jupiterALT = [];

      try {
        console.log(`🔥 ПОЛУЧАЕМ готовую транзакцию от Jupiter через /swap endpoint...`);

        // Используем quote из Jupiter swap data
        const cleanQuote = { ...jupiterSwapData.quote };
        if (cleanQuote.minimumOutAmount) {
          console.log(`🔥 ИСПРАВЛЕНИЕ ERROR 6009: Удаляем minimumOutAmount (${cleanQuote.minimumOutAmount})`);
          delete cleanQuote.minimumOutAmount;
        }

        // 🔥 ПОЛУЧАЕМ ИНСТРУКЦИИ ОТ JUPITER ЧЕРЕЗ /swap-instructions ENDPOINT
        // 💡 ОФИЦИАЛЬНАЯ ДОКУМЕНТАЦИЯ: /swap-instructions возвращает cleanupInstruction
        const swapResponse = await fetch('https://quote-api.jup.ag/v6/swap-instructions', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            quoteResponse: cleanQuote,
            userPublicKey: this.userPublicKey.toString(),
            wrapAndUnwrapSol: true, // 🔥 КРИТИЧЕСКИ ВАЖНО для cleanupInstruction!
            dynamicComputeUnitLimit: true,
            prioritizationFeeLamports: 'auto'
          })
        });

        if (!swapResponse.ok) {
          throw new Error(`Jupiter /swap API error: ${swapResponse.status}`);
        }

        const swapData = await swapResponse.json();

        if (!swapData.swapInstruction) {
          throw new Error('Jupiter не вернул swap инструкцию');
        }

        // 🔥 ОБРАБАТЫВАЕМ ОТВЕТ ОТ /swap-instructions ENDPOINT
        console.log(`✅ Jupiter swap-instructions получены:`);
        console.log(`   setupInstructions: ${swapData.setupInstructions?.length || 0}`);
        console.log(`   swapInstruction: ${swapData.swapInstruction ? 'ЕСТЬ' : 'НЕТ'}`);
        console.log(`   cleanupInstruction: ${swapData.cleanupInstruction ? 'ЕСТЬ' : 'НЕТ'}`);
        console.log(`   computeBudgetInstructions: ${swapData.computeBudgetInstructions?.length || 0}`);
        console.log(`   addressLookupTableAddresses: ${swapData.addressLookupTableAddresses?.length || 0}`);

        // 🔥 ИЗВЛЕКАЕМ ALT ИЗ ОТВЕТА /swap-instructions
        if (swapData.addressLookupTableAddresses && swapData.addressLookupTableAddresses.length > 0) {
          console.log(`🔥 ПОЛУЧАЕМ ALT от Jupiter: ${swapData.addressLookupTableAddresses.length} адресов`);

          // 🔥 ИСПРАВЛЕНО: Используем новый метод loadJupiterALT
          try {
            const altAccounts = await this.altManager.loadJupiterALT(swapData.addressLookupTableAddresses);
            if (altAccounts && altAccounts.length > 0) {
              jupiterALT = altAccounts;
              console.log(`✅ Jupiter ALT получены: ${jupiterALT.length} таблиц`);
            }
          } catch (altError) {
            console.log(`⚠️ Ошибка загрузки Jupiter ALT: ${altError.message}`);
            console.log(`💡 Продолжаем без Jupiter ALT`);
          }
        } else {
          console.log(`⚠️ Jupiter не вернул ALT адреса`);
        }

        // 🔥 ДЕСЕРИАЛИЗУЕМ ИНСТРУКЦИИ ОТ /swap-instructions
        const { TransactionInstruction, PublicKey } = require('@solana/web3.js');

        const deserializeInstruction = (instruction) => {
          return new TransactionInstruction({
            programId: new PublicKey(instruction.programId),
            keys: instruction.accounts.map((key) => ({
              pubkey: new PublicKey(key.pubkey),
              isSigner: key.isSigner,
              isWritable: key.isWritable,
            })),
            data: Buffer.from(instruction.data, "base64"),
          });
        };

        // Добавляем все инструкции в правильном порядке согласно официальной документации
        if (swapData.computeBudgetInstructions) {
          swapData.computeBudgetInstructions.forEach(ix => {
            jupiterInstructions.push(deserializeInstruction(ix));
          });
          console.log(`✅ Добавлено ${swapData.computeBudgetInstructions.length} compute budget инструкций`);
        }

        if (swapData.setupInstructions) {
          swapData.setupInstructions.forEach(ix => {
            jupiterInstructions.push(deserializeInstruction(ix));
          });
          console.log(`✅ Добавлено ${swapData.setupInstructions.length} setup инструкций`);
        }

        if (swapData.swapInstruction) {
          jupiterInstructions.push(deserializeInstruction(swapData.swapInstruction));
          console.log(`✅ Добавлена главная swap инструкция`);
        }

        // 🔥 КРИТИЧЕСКИ ВАЖНО: Добавляем cleanupInstruction!
        if (swapData.cleanupInstruction) {
          console.log(`🎉 НАЙДЕНА cleanupInstruction от Jupiter API!`);
          jupiterInstructions.push(deserializeInstruction(swapData.cleanupInstruction));
          console.log(`✅ cleanupInstruction ДОБАВЛЕНА в транзакцию!`);
        } else {
          console.log(`⚠️ Jupiter API не вернул cleanupInstruction`);
          console.log(`   Это нормально если output mint не SOL или wrapAndUnwrapSol = false`);
        }

        console.log(`✅ Десериализовано ${jupiterInstructions.length} инструкций от Jupiter`);

        // Обновляем jupiterSwapData для совместимости с остальным кодом
        jupiterSwapData.swapData = swapData;
        jupiterSwapData.cleanupInstruction = swapData.cleanupInstruction ? deserializeInstruction(swapData.cleanupInstruction) : null;

      } catch (altError) {
        console.log(`⚠️ Ошибка получения Jupiter ALT из готовой транзакции: ${altError.message}`);
        console.log(`💡 Продолжаем без Jupiter ALT`);
        jupiterALT = [];
      }

      // ✅ ИНСТРУКЦИИ УЖЕ ДОБАВЛЕНЫ В НОВОМ БЛОКЕ ВЫШЕ
      // Включая: computeBudgetInstructions, setupInstructions, swapInstruction, cleanupInstruction
      console.log(`✅ Все Jupiter инструкции уже добавлены через /swap-instructions endpoint`);
      console.log(`   Включая cleanupInstruction если она была возвращена Jupiter API`);
      console.log(`   Общее количество инструкций: ${jupiterInstructions.length}`);

      if (!jupiterInstructions || jupiterInstructions.length === 0) {
        throw new Error('❌ Не удалось получить Jupiter swap инструкции');
      }

      // 🔥 ОФИЦИАЛЬНЫЙ ПОДХОД: СОЗДАЕМ ВТОРОЙ SWAP ЧЕРЕЗ /swap-instructions API!
      console.log(`🔥 ОФИЦИАЛЬНЫЙ ПОДХОД: Создаем ВТОРОЙ SWAP через /swap-instructions API!`);
      console.log(`💡 ЛОГИКА: Flash Loan USDC → Swap1 USDC→SOL → Swap2 SOL→USDC → Возврат USDC`);
      console.log(`🔧 МЕТОД: Два отдельных /swap-instructions запроса по официальной документации`);

      // 🔥 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: ИСПОЛЬЗУЕМ РЕАЛЬНЫЙ OUTPUT AMOUNT ИЗ ПЕРВОГО SWAP!
      // ПРОБЛЕМА: Код использует quoted amount вместо реального output amount
      // РЕШЕНИЕ: Извлекаем реальный output amount из swap events или instructions
      console.log(`🔍 ДИАГНОСТИКА jupiterSwapData для извлечения РЕАЛЬНОГО OUTPUT AMOUNT:`);
      console.log(`   jupiterSwapData: ${typeof jupiterSwapData}`);
      console.log(`   jupiterSwapData.swapInstruction: ${typeof jupiterSwapData?.swapInstruction}`);
      console.log(`   jupiterSwapData.quote: ${typeof jupiterSwapData?.quote}`);

      let realSolAmount;

      // 🎯 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: ИСПОЛЬЗУЕМ ВСЕ SOL ИЗ TOKEN ACCOUNT!
      // ПРОБЛЕМА: Система заранее рассчитывает amount, но в атомарной транзакции
      // второй swap должен использовать ВСЕ SOL, которые получились от первого swap!

      // 🔥 РЕШЕНИЕ: Используем МАКСИМАЛЬНЫЙ БАЛАНС SOL token account
      // Это гарантирует, что второй swap использует ТОЧНО ТО, что получил первый swap

      console.log(`🎯 АТОМАРНАЯ ТРАНЗАКЦИЯ: Второй swap использует ВСЕ SOL из token account`);
      console.log(`📊 Логика: Первый swap → SOL token account → Второй swap (ВСЕ SOL)`);

      // 🔥 ИСПОЛЬЗУЕМ СПЕЦИАЛЬНЫЙ ПАРАМЕТР ДЛЯ МАКСИМАЛЬНОГО БАЛАНСА
      // В атомарной транзакции amount должен быть максимально возможным
      const estimatedSolAmount = jupiterSwapData?.quote?.outAmount ?
        parseInt(jupiterSwapData.quote.outAmount) :
        Math.floor((flashLoanAmount / 1000000 / 147.1) * **********);

      console.log(`💰 Оценочное количество SOL: ${estimatedSolAmount / **********} SOL`);
      console.log(`🎯 НО второй swap будет использовать РЕАЛЬНЫЙ баланс token account!`);

      // Получаем предварительную котировку для второго swapa для расчета защиты
      const preliminarySecondQuote = await jupiterSwap.getJupiterQuote(
        'So********************************111111112', // SOL
        'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // USDC
        estimatedSolAmount,
        25, // Стандартный slippage для предварительной котировки
        40
      );
      if (!preliminarySecondQuote) {
        throw new Error('Не удалось получить предварительную котировку для второго swapa');
      }

      // 🛡️ ЗАЩИТА ОТ УБЫТКОВ: РАСЧЕТ ЖЕСТКОГО SLIPPAGE ДЛЯ МИНИМАЛЬНОЙ ПРИБЫЛИ
      // Займ + минимальная прибыль $1 = минимальный возврат
      const borrowedAmountUSD = flashLoanAmount; // Уже в USD
      const MINIMUM_PROFIT_USD = 1.00;
      const minimumReturnUSD = borrowedAmountUSD + MINIMUM_PROFIT_USD;

      // Рассчитываем максимально допустимый slippage для получения минимальной прибыли
      const expectedReturnUSD = parseInt(preliminarySecondQuote.outAmount) / 1000000;
      const maxAllowedLossUSD = expectedReturnUSD - minimumReturnUSD;
      const maxAllowedSlippageBps = Math.floor((maxAllowedLossUSD / expectedReturnUSD) * 10000);

      // 🛡️ ЖЕСТКОЕ ОГРАНИЧЕНИЕ: Минимум 5 bps (0.05%) для защиты от убытков
      const protectiveSlippageBps = Math.max(5, Math.min(maxAllowedSlippageBps, 25));

      console.log(`🛡️ ЗАЩИТА ОТ УБЫТКОВ ЧЕРЕЗ JUPITER DYNAMIC SLIPPAGE:`);
      console.log(`   Займ: $${borrowedAmountUSD.toFixed(2)}`);
      console.log(`   Минимальная прибыль: $${MINIMUM_PROFIT_USD.toFixed(2)}`);
      console.log(`   Ожидаемый возврат: $${expectedReturnUSD.toFixed(6)}`);
      console.log(`   Максимальная потеря: $${maxAllowedLossUSD.toFixed(6)}`);
      console.log(`   Защитный slippage: ${protectiveSlippageBps} bps (${(protectiveSlippageBps/100).toFixed(2)}%)`);

      // 🔥 КРИТИЧЕСКИЕ ПАРАМЕТРЫ ДЛЯ ВТОРОГО SWAP (АТОМАРНАЯ ТРАНЗАКЦИЯ)
      const secondQuoteParams = {
        inputMint: 'So********************************111111112', // SOL
        outputMint: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // USDC
        amount: estimatedSolAmount, // 🎯 Оценочная сумма для котировки
        slippageBps: protectiveSlippageBps  // 🛡️ ЗАЩИТНЫЙ slippage для минимальной прибыли
      };

      console.log(`📊 Параметры второго swapa:`, secondQuoteParams);

      // Получаем финальную котировку для второго swapa с защитным slippage
      const secondQuote = await jupiterSwap.getJupiterQuote(
        secondQuoteParams.inputMint,
        secondQuoteParams.outputMint,
        secondQuoteParams.amount,
        secondQuoteParams.slippageBps,
        secondQuoteParams.maxAccounts
      );
      if (!secondQuote) {
        throw new Error('Не удалось получить котировку для второго swapa');
      }

      console.log(`✅ Котировка для второго swapa получена: ${secondQuote.outAmount} USDC`);

      // 🔥 ИСПОЛЬЗУЕМ ТОТ ЖЕ МЕТОД ЧТО И ДЛЯ ПЕРВОГО SWAP!
      // ПРИЧИНА: createJupiterInstructionsForArbitrage НЕ добавляет Token Ledger
      console.log(`🔥 ВТОРОЙ SWAP: используем createJupiterInstructionsForArbitrage (как первый swap)`);

      // 🛡️ ИСПРАВЛЕНИЕ: dynamicSlippage НЕ ПОДДЕРЖИВАЕТСЯ в /swap-instructions endpoint
      // Используем только обычный slippageBps для защиты от убытков
      const secondSwapInstructions = await jupiterSwap.createJupiterInstructionsForArbitrage(
        secondQuoteParams.inputMint,
        secondQuoteParams.outputMint,
        secondQuoteParams.amount,
        secondQuoteParams.slippageBps, // 🛡️ ЗАЩИТНЫЙ slippage уже рассчитан
        null // spreadPercent
      );

      if (!secondSwapInstructions || !secondSwapInstructions.swapInstruction) {
        throw new Error('Не удалось создать инструкции для второго swapa');
      }

      console.log(`✅ ВТОРОЙ swap создан через createJupiterInstructionsForArbitrage БЕЗ Token Ledger!`);

      // 🔒 ИЗВЛЕКАЕМ ALT ИЗ ВТОРОГО JUPITER SWAP!
      let secondJupiterALT = [];

      if (secondSwapInstructions.addressLookupTableAccounts) {
        secondJupiterALT = secondSwapInstructions.addressLookupTableAccounts;
        console.log(`🔒 ВТОРОЙ JUPITER ALT: ${secondJupiterALT.length} таблиц`);
      } else {
        console.log(`⚠️ ВТОРОЙ JUPITER ALT НЕ НАЙДЕН`);
      }



      // 🔒 ОБЪЕДИНЯЕМ ВСЕ JUPITER ALT ТАБЛИЦЫ
      const allJupiterALT = [...(jupiterALT || []), ...(secondJupiterALT || [])];
      console.log(`🔒 ОБЩИЙ JUPITER ALT: ${allJupiterALT.length} таблиц`);

      // 🔥 ДОБАВЛЯЕМ ИНСТРУКЦИИ ВТОРОГО SWAPA В ПРАВИЛЬНОМ ПОРЯДКЕ
      console.log(`🔥 ДОБАВЛЯЕМ ИНСТРУКЦИИ ВТОРОГО SWAPA:`);
      console.log(`   Структура secondSwapInstructions: ${Object.keys(secondSwapInstructions || {}).join(', ')}`);

      // ⚠️ ПРОПУСКАЕМ Compute Budget инструкции второго swapa - избегаем дубликатов!
      if (secondSwapInstructions.swapData?.computeBudgetInstructions) {
        console.log(`⚠️ ПРОПУЩЕНО ${secondSwapInstructions.swapData.computeBudgetInstructions.length} дублирующих Compute Budget инструкций второго swapa`);
      }

      // Setup инструкции второго swap
      if (secondSwapInstructions.swapData?.setupInstructions) {
        jupiterInstructions.push(...secondSwapInstructions.swapData.setupInstructions);
        console.log(`✅ Добавлено ${secondSwapInstructions.swapData.setupInstructions.length} setup инструкций второго swapa`);
      }

      // Основная swap инструкция второго swap
      if (secondSwapInstructions.swapInstruction) {
        jupiterInstructions.push(secondSwapInstructions.swapInstruction);
        console.log(`✅ Добавлена основная swap инструкция второго swapa`);
      }

      // 🔥 КРИТИЧЕСКИ ВАЖНО: Cleanup инструкция второго swap
      console.log(`🔍 ДЕТАЛЬНАЯ ОТЛАДКА cleanupInstruction второго swap:`);
      console.log(`   secondSwapInstructions.cleanupInstruction: ${secondSwapInstructions.cleanupInstruction ? 'ЕСТЬ' : 'НЕТ'}`);
      console.log(`   secondSwapInstructions.swapData?.cleanupInstruction: ${secondSwapInstructions.swapData?.cleanupInstruction ? 'ЕСТЬ' : 'НЕТ'}`);

      if (secondSwapInstructions.cleanupInstruction) {
        jupiterInstructions.push(secondSwapInstructions.cleanupInstruction);
        console.log(`🎉 ДОБАВЛЕНА cleanupInstruction второго swapa!`);
      } else if (secondSwapInstructions.swapData?.cleanupInstruction) {
        jupiterInstructions.push(secondSwapInstructions.swapData.cleanupInstruction);
        console.log(`🎉 ДОБАВЛЕНА cleanupInstruction второго swapa из swapData!`);
      } else {
        console.log(`⚠️ Второй swap не вернул cleanupInstruction`);
        console.log(`   Это может быть нормально если output mint не SOL`);
      }

      console.log(`🔄 ИТОГО Jupiter инструкций: ${jupiterInstructions.length} (включая второй swap)`);

      // 🔥 ПРАВИЛЬНАЯ НОВАЯ АРХИТЕКТУРА: ИСПОЛЬЗУЕМ createFlashLoanTransaction!
      console.log(`🔥 ПРАВИЛЬНАЯ НОВАЯ АРХИТЕКТУРА: Используем createFlashLoanTransaction!`);
      console.log(`💡 ЛОГИКА: StartFlashloan → Jupiter Swaps → EndFlashloan`);
      console.log(`   📊 Jupiter инструкций: ${jupiterInstructions.length}`);
      console.log(`   💰 Flash Loan сумма: ${flashLoanAmount} USDC`);

      // � СОЗДАЕМ КАСТОМНЫЙ ALT ДЛЯ MARGINFI КЛЮЧЕЙ
      console.log(`🔒 СОЗДАЕМ КАСТОМНЫЙ ALT ДЛЯ MARGINFI КЛЮЧЕЙ...`);

      // Собираем все ключи из Jupiter инструкций
      const allJupiterKeys = new Set();
      if (allJupiterALT && Array.isArray(allJupiterALT)) {
        allJupiterALT.forEach(alt => {
          if (alt.state && alt.state.addresses) {
            alt.state.addresses.forEach(addr => {
              allJupiterKeys.add(addr.toString());
            });
          }
        });
      }

      // Собираем все ключи из инструкций
      const allInstructionKeys = new Set();
      if (jupiterInstructions && Array.isArray(jupiterInstructions)) {
        jupiterInstructions.forEach(ix => {
          if (ix.keys && Array.isArray(ix.keys)) {
            ix.keys.forEach(key => {
              allInstructionKeys.add(key.pubkey.toString());
            });
          }
        });
      }

      // Находим ключи НЕ покрытые Jupiter ALT
      const uncoveredKeys = [];
      if (allInstructionKeys && allInstructionKeys.size > 0) {
        allInstructionKeys.forEach(key => {
          if (!allJupiterKeys.has(key)) {
            uncoveredKeys.push(key);
          }
        });
      }

      console.log(`🔒 АНАЛИЗ ALT ПОКРЫТИЯ:`);
      console.log(`   Jupiter ALT ключей: ${allJupiterKeys.size}`);
      console.log(`   Всего ключей в инструкциях: ${allInstructionKeys.size}`);
      console.log(`   Непокрытых ключей: ${uncoveredKeys.length}`);

      // 🔥 ИСПРАВЛЕНО: НЕ СОЗДАЕМ ФИКТИВНЫЕ ALT!
      // ПРИЧИНА ОШИБКИ: "Transaction loads an address table account with an invalid owner"
      // РЕШЕНИЕ: Используем только валидные ALT или создаем реальные ALT
      // 🔥 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: ЗАГРУЖАЕМ ВСЕ ALT ТАБЛИЦЫ!
      console.log(`🔥 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Загружаем ВСЕ ALT таблицы через ALT Manager!`);

      let finalALT = [];

      // 🚨 КРИТИЧЕСКАЯ ОТЛАДКА ALT MANAGER
      console.log(`🚨 КРИТИЧЕСКАЯ ОТЛАДКА ALT MANAGER:`);
      console.log(`   this.altManager существует: ${!!this.altManager}`);
      console.log(`   this.altManager тип: ${this.altManager ? this.altManager.constructor.name : 'undefined'}`);
      console.log(`   this.altManager.loadAllALT существует: ${!!(this.altManager && this.altManager.loadAllALT)}`);
      console.log(`   this.altManager.loadJupiterALT существует: ${!!(this.altManager && this.altManager.loadJupiterALT)}`);
      console.log(`   this.altManager.loadCustomALT существует: ${!!(this.altManager && this.altManager.loadCustomALT)}`);
      console.log(`   allJupiterALT длина: ${(allJupiterALT || []).length}`);
      console.log(`   this.marginfiClient существует: ${!!this.marginfiClient}`);

      // 🔍 ДЕТАЛЬНАЯ ДИАГНОСТИКА ALT MANAGER
      if (this.altManager) {
        console.log(`🔍 ДЕТАЛЬНАЯ ДИАГНОСТИКА ALT MANAGER:`);
        console.log(`   Доступные методы: ${Object.getOwnPropertyNames(Object.getPrototypeOf(this.altManager)).filter(name => name !== 'constructor').join(', ')}`);
      }

      // Если есть ALT Manager, используем его для загрузки всех реальных ALT
      if (this.altManager && this.altManager.loadAllALT) {
        try {
          console.log(`🚀 ИСПОЛЬЗУЕМ ALT MANAGER ДЛЯ ЗАГРУЗКИ ВСЕХ ALT...`);

          // Создаем Jupiter response для ALT Manager
          const jupiterResponse = {
            addressLookupTableAddresses: (allJupiterALT || []).map(alt => alt.key.toString())
          };

          console.log(`📋 Jupiter response для ALT Manager:`);
          console.log(`   addressLookupTableAddresses: ${jupiterResponse.addressLookupTableAddresses.length}`);
          jupiterResponse.addressLookupTableAddresses.forEach((addr, i) => {
            console.log(`     ${i + 1}. ${addr.slice(0, 8)}...`);
          });

          // Загружаем ВСЕ ALT через ALT Manager
          console.log(`🔄 Вызываем this.altManager.loadAllALT()...`);
          finalALT = await this.altManager.loadAllALT(jupiterResponse, this.marginfiClient);
          console.log(`✅ ALT MANAGER ВЕРНУЛ: ${finalALT ? finalALT.length : 'null'} ALT таблиц`);

          if (!finalALT || finalALT.length === 0) {
            throw new Error(`ALT Manager returned empty result - no fallback allowed`);
          }

          // Показываем статистику
          if (this.altManager.stats) {
            const stats = this.altManager.stats;
            console.log(`📊 СТАТИСТИКА ALT MANAGER:`);
            console.log(`   🎯 Jupiter ALT: ${stats.jupiterALT || 0}`);
            console.log(`   🏦 MarginFi ALT: ${stats.marginfiALT || 0}`);
            console.log(`   🔥 Кастомная ALT: ${stats.customALT || 0}`);
            console.log(`   📋 Другие ALT: ${stats.otherALT || 0}`);
            console.log(`   🔑 Всего адресов: ${stats.totalAddresses || 0}`);
            console.log(`   💾 Экономия: ${stats.compressionSavings || 0} байт`);
          }

        } catch (altError) {
          console.log(`❌ КРИТИЧЕСКАЯ ОШИБКА ALT Manager: ${altError.message}`);
          console.log(`❌ Stack: ${altError.stack}`);
          console.log(`🔧 Fallback: используем только Jupiter ALT`);
          finalALT = [...(allJupiterALT || [])];
        }
      } else {
        console.log(`🚨 КРИТИЧЕСКАЯ ПРОБЛЕМА: ALT Manager недоступен!`);
        console.log(`   this.altManager: ${this.altManager}`);
        console.log(`   loadAllALT метод: ${this.altManager ? this.altManager.loadAllALT : 'N/A'}`);
        console.log(`🔧 Используем только Jupiter ALT`);
        finalALT = [...(allJupiterALT || [])];
      }
      if (uncoveredKeys.length > 0) {
        console.log(`⚠️ НАЙДЕНО ${uncoveredKeys.length} НЕПОКРЫТЫХ КЛЮЧЕЙ`);
        console.log(`🚫 НЕ СОЗДАЕМ ФИКТИВНЫЕ ALT - ЭТО ВЫЗЫВАЕТ ОШИБКУ "invalid owner"`);
        console.log(`💡 РЕКОМЕНДАЦИЯ: Создайте реальную ALT или используйте обычные ключи`);

        // Логируем непокрытые ключи для анализа
        console.log(`📋 НЕПОКРЫТЫЕ КЛЮЧИ:`);
        uncoveredKeys.slice(0, 10).forEach((key, index) => {
          console.log(`   ${index + 1}. ${key.slice(0, 8)}...`);
        });
        if (uncoveredKeys.length > 10) {
          console.log(`   ... и еще ${uncoveredKeys.length - 10} ключей`);
        }
      }

      console.log(`🔒 ФИНАЛЬНЫЙ ALT: ${finalALT.length} таблиц`);

      // 🔥 СОХРАНЯЕМ ВСЕ ALT ДЛЯ COMPLETE FLASH LOAN STRUCTURE
      this.allLoadedALT = finalALT;
      console.log(`🔥 СОХРАНЕНЫ ВСЕ ALT ДЛЯ COMPLETE FLASH LOAN STRUCTURE: ${this.allLoadedALT.length} таблиц`);

      // � ДЕТАЛЬНЫЙ АНАЛИЗ ФИНАЛЬНОГО ALT
      console.log(`🔒 ДЕТАЛЬНЫЙ АНАЛИЗ ФИНАЛЬНОГО ALT:`);
      finalALT.forEach((alt, index) => {
        const keyCount = alt.state?.addresses?.length || 0;
        console.log(`   ALT ${index + 1}: ${keyCount} ключей, key: ${alt.key.toString().slice(0, 8)}...`);
      });

      // ��🔥 ИСПОЛЬЗУЕМ ПРАВИЛЬНЫЙ МЕТОД createFlashLoanTransaction С JUPITER ИНСТРУКЦИЯМИ И ALT!
      console.log(`🔒 ПЕРЕДАЕМ В createFlashLoanTransaction:`);
      console.log(`   Jupiter инструкций: ${jupiterInstructions.length}`);
      console.log(`   ALT таблиц: ${finalALT.length}`);
      console.log(`   Flash Loan сумма: ${flashLoanAmount}`);

      console.log(`🔥 ВЫЗЫВАЕМ createFlashLoanTransaction С ПАРАМЕТРАМИ:`);
      console.log(`   marginfiAccount: ${!!marginfiAccount}`);
      console.log(`   flashLoanAmount: ${flashLoanAmount}`);
      console.log(`   usdcBank: ${!!usdcBank}`);
      console.log(`   jupiterInstructions: ${jupiterInstructions?.length || 0}`);
      console.log(`   finalALT: ${finalALT?.length || 0}`);

      // 🔥 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: НОРМАЛИЗУЕМ ИНСТРУКЦИИ ДО ПЕРЕДАЧИ В createFlashLoanTransaction!
      console.log(`🔥 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Нормализуем Jupiter инструкции ДО createFlashLoanTransaction!`);
      console.log(`💡 ПРОБЛЕМА: MarginFi SDK вызывает projectActiveBalancesNoCpi ДО нормализации инструкций`);

      // 🔥 ИСПОЛЬЗУЕМ ГЛОБАЛЬНО ИМПОРТИРОВАННУЮ ФУНКЦИЮ НОРМАЛИЗАЦИИ

      console.log(`🔍 ДИАГНОСТИКА JUPITER ИНСТРУКЦИЙ ПЕРЕД НОРМАЛИЗАЦИЕЙ:`);
      console.log(`   Всего инструкций: ${jupiterInstructions ? jupiterInstructions.length : 0}`);

      jupiterInstructions.forEach((ix, index) => {
        console.log(`   Инструкция ${index + 1}:`);
        console.log(`     programId тип: ${typeof ix.programId}`);
        console.log(`     programId значение: ${ix.programId}`);
        console.log(`     programId.equals доступен: ${typeof ix.programId?.equals}`);
        console.log(`     это TransactionInstruction: ${ix.constructor?.name}`);
      });

      // 🔥 НОРМАЛИЗУЕМ ИНСТРУКЦИИ ПРЯМО ЗДЕСЬ!
      console.log(`🔥 НОРМАЛИЗУЕМ ИНСТРУКЦИИ ПРЯМО ПЕРЕД createFlashLoanTransaction!`);
      const normalizedJupiterInstructions = normalizeInstructions(jupiterInstructions);
      console.log(`✅ Нормализовано ${normalizedJupiterInstructions.length} инструкций`);

      // 🔍 КРИТИЧЕСКАЯ ПРОВЕРКА РЕЗУЛЬТАТА НОРМАЛИЗАЦИИ
      console.log(`🔍 КРИТИЧЕСКАЯ ПРОВЕРКА: Проверяем каждую нормализованную инструкцию...`);
      normalizedJupiterInstructions.forEach((ix, index) => {
        console.log(`   Нормализованная инструкция ${index + 1}:`);
        console.log(`     programId тип: ${typeof ix.programId}`);
        console.log(`     programId.equals доступен: ${typeof ix.programId?.equals}`);
        console.log(`     это TransactionInstruction: ${ix.constructor?.name}`);
        console.log(`     programId instanceof PublicKey: ${ix.programId instanceof PublicKey}`);

        // 🚨 КРИТИЧЕСКАЯ ПРОВЕРКА: Если programId не имеет метод equals, принудительно конвертируем
        if (!ix.programId || typeof ix.programId.equals !== 'function') {
          console.log(`🚨 КРИТИЧЕСКАЯ ОШИБКА: programId не имеет метод equals после нормализации!`);
          console.log(`   Тип: ${typeof ix.programId}`);
          console.log(`   Значение: ${ix.programId}`);
          console.log(`   equals метод: ${typeof ix.programId?.equals}`);
          throw new Error(`Инструкция ${index + 1}: programId не имеет метод equals после нормализации`);
        }

        // 🚨 ПРОВЕРЯЕМ НАЛИЧИЕ МЕТОДА equals
        if (typeof ix.programId.equals !== 'function') {
          console.log(`🚨 КРИТИЧЕСКАЯ ОШИБКА: programId.equals не является функцией!`);
          throw new Error(`Инструкция ${index + 1}: programId.equals не является функцией`);
        }
      });

      console.log(`✅ ВСЕ ИНСТРУКЦИИ ПРОШЛИ КРИТИЧЕСКУЮ ПРОВЕРКУ!`);

      // 🔥 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: ЗАГРУЖАЕМ ВСЕ ALT ТАБЛИЦЫ!
      console.log(`🔥 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Загружаем ВСЕ ALT таблицы для createFullArbitrageTransaction!`);

      let allALTForFlashLoan = [];

      // Если есть сохраненные ALT из предыдущих операций, используем их
      if (this.allLoadedALT && this.allLoadedALT.length > 0) {
        allALTForFlashLoan = this.allLoadedALT;
        console.log(`✅ ИСПОЛЬЗУЕМ СОХРАНЕННЫЕ ALT: ${allALTForFlashLoan.length} таблиц`);
      }
      // Иначе загружаем ALT через ALT Manager
      else if (this.altManager && typeof this.altManager.loadAllALT === 'function') {
        try {
          console.log(`🚀 ЗАГРУЖАЕМ ALT ЧЕРЕЗ ALT MANAGER...`);
          console.log(`🔍 ДИАГНОСТИКА ALT MANAGER:`);
          console.log(`   this.altManager: ${!!this.altManager}`);
          console.log(`   this.altManager.constructor.name: ${this.altManager.constructor.name}`);
          console.log(`   this.altManager.loadAllALT: ${typeof this.altManager.loadAllALT}`);
          console.log(`   this.marginfiClient: ${!!this.marginfiClient}`);

          // Создаем пустой Jupiter response для загрузки всех ALT
          const jupiterResponse = {
            addressLookupTableAddresses: []
          };

          // Загружаем ВСЕ ALT через ALT Manager
          console.log(`🔥 ВЫЗЫВАЕМ this.altManager.loadAllALT()...`);
          allALTForFlashLoan = await this.altManager.loadAllALT(jupiterResponse, this.marginfiClient);
          console.log(`✅ ALT MANAGER загрузил ${allALTForFlashLoan.length} ALT таблиц`);

          // Сохраняем для будущего использования
          this.allLoadedALT = allALTForFlashLoan;

        } catch (altError) {
          console.log(`❌ Ошибка загрузки ALT: ${altError.message}`);
          console.log(`❌ Stack trace: ${altError.stack}`);
          allALTForFlashLoan = [];
        }
      } else {
        console.log(`⚠️ ALT Manager недоступен или метод loadAllALT отсутствует`);
        console.log(`🔍 ДИАГНОСТИКА:`);
        console.log(`   this.altManager: ${!!this.altManager}`);
        console.log(`   this.altManager?.constructor?.name: ${this.altManager?.constructor?.name}`);
        console.log(`   typeof this.altManager?.loadAllALT: ${typeof this.altManager?.loadAllALT}`);

        // 🔥 FALLBACK: Пытаемся получить ALT от Complete Flash Loan Structure
        if (this.completeFlashLoanStructure && this.completeFlashLoanStructure.altManager) {
          try {
            console.log(`🔥 FALLBACK: Используем Complete Flash Loan Structure ALT Manager...`);
            const jupiterResponse = { addressLookupTableAddresses: [] };
            allALTForFlashLoan = await this.completeFlashLoanStructure.altManager.loadAllALT(jupiterResponse, this.marginfiClient);
            console.log(`✅ FALLBACK: Complete Flash Loan Structure загрузил ${allALTForFlashLoan.length} ALT таблиц`);
            this.allLoadedALT = allALTForFlashLoan;
          } catch (fallbackError) {
            console.log(`❌ FALLBACK ошибка: ${fallbackError.message}`);
            allALTForFlashLoan = [];
          }
        } else {
          allALTForFlashLoan = [];
        }
      }

      console.log(`🔒 ФИНАЛЬНЫЙ ALT ДЛЯ createFlashLoanTransaction: ${allALTForFlashLoan.length} таблиц`);

      // 🔥 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Передаем сумму в microUSDC!
      const flashLoanAmountMicroUSDC = this.convertUsdToNativeAmount(flashLoanAmount, 'USDC') // ИСПРАВЛЕНО: Правильная конвертация;
      console.log(`🔧 ИСПРАВЛЕНИЕ: Передаем ${flashLoanAmountMicroUSDC} microUSDC вместо ${flashLoanAmount}`);

      const result = await this.createFlashLoanTransaction(
        marginfiAccount,
        flashLoanAmountMicroUSDC, // 🔥 ИСПРАВЛЕНО: Передаем в microUSDC!
        usdcBank,
        normalizedJupiterInstructions, // ✅ ПЕРЕДАЕМ НОРМАЛИЗОВАННЫЕ ИНСТРУКЦИИ!
        allALTForFlashLoan // 🔒 ПЕРЕДАЕМ ВСЕ ALT (Jupiter + кастомные)
      );

      console.log(`🎉 createFullArbitrageTransaction ЗАВЕРШЕН УСПЕШНО!`);
      console.log(`   Результат: ${result?.constructor?.name || 'undefined'}`);

      return result;

    } catch (error) {
      console.error(`❌ Ошибка createFullArbitrageTransaction: ${error.message}`);
      console.error(`❌ Stack trace: ${error.stack}`);
      throw error;
    }
  }

  // 🔍 ОПИСАНИЕ КЛЮЧЕЙ ДЛЯ ДИАГНОСТИКИ
  getKeyDescription(key) {
    const keyDescriptions = {
      '********************************': 'System Program',
      'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA': 'Token Program',
      'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL': 'Associated Token Program',
      'MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA': 'MarginFi Program',
      'So********************************111111112': 'Wrapped SOL',
      'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v': 'USDC',
      'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB': 'USDT'
    };

    if (keyDescriptions[key]) {
      return keyDescriptions[key];
    }

    // Проверяем если это наш wallet
    if (key === this.userPublicKey.toString()) {
      return 'User Wallet';
    }

    // Проверяем если это MarginFi аккаунт
    if (this.marginfiAccount && key === this.marginfiAccount.toString()) {
      return 'MarginFi Account';
    }

    return 'Unknown';
  }

  // 🔧 СОЗДАНИЕ КАСТОМНОЙ ALT ДЛЯ ОПТИМИЗАЦИИ
  createCustomALT(uncoveredKeys) {
    const priorityKeys = [
      '********************************', // System Program
      'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA', // Token Program
      'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL', // Associated Token Program
      'MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA', // MarginFi Program
      'JUP6LkbZpGQy6XUSV3XkzpHQQdQeaDCp2k2B6Rp4Y5fC', // Jupiter Program
      this.userPublicKey.toString(), // User Wallet
    ];

    // Добавляем MarginFi аккаунт если есть
    if (this.marginfiAccount) {
      priorityKeys.push(this.marginfiAccount.toString());
    }

    // 🚨 КРИТИЧЕСКОЕ УЛУЧШЕНИЕ: Добавляем ВСЕ непокрытые ключи до лимита 30
    const allCandidates = [...priorityKeys];

    // Добавляем остальные непокрытые ключи (до лимита 30 ключей в ALT)
    if (uncoveredKeys && Array.isArray(uncoveredKeys)) {
      uncoveredKeys.forEach(key => {
        if (!allCandidates.includes(key) && allCandidates.length < 30) {
          allCandidates.push(key);
        }
      });
    }

    // Фильтруем только те ключи которые действительно не покрыты
    const customKeys = allCandidates.filter(key => uncoveredKeys.includes(key));

    console.log(`🔧 Кастомная ALT будет содержать ${customKeys.length} ключей (лимит 30):`);
    if (customKeys && Array.isArray(customKeys) && customKeys.length > 0) {
      customKeys.slice(0, 10).forEach((key, i) => {
        console.log(`     ${i + 1}. ${key.slice(0, 8)}... (${this.getKeyDescription(key)})`);
      });
    }

    if (customKeys.length > 10) {
      console.log(`     ... и еще ${customKeys.length - 10} ключей`);
    }

    return customKeys;
  }
}

module.exports = AtomicTransactionBuilderFixed;
