/**
 * 🎼 BUNDLE ORCHESTRATOR
 * Оркестратор для координации всех bundle операций
 */

const ParallelBundleManager = require('./parallel-bundle-manager');

class BundleOrchestrator {
  constructor(connection, wallet, mevIntegration, marginfiFlashLoan = null, mainSystem = null, atomicTransactionBuilder = null) {
    // ✅ КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: ПЕРЕДАЕМ MARGINFI, MAIN SYSTEM И АТОМАРНЫЙ СТРОИТЕЛЬ В BUNDLE MANAGER!
    this.bundleManager = new ParallelBundleManager(connection, wallet, mevIntegration, null, marginfiFlashLoan, mainSystem, atomicTransactionBuilder);

    // Сохраняем атомарный строитель для прямого доступа
    this.atomicTransactionBuilder = atomicTransactionBuilder;

    console.log(`🔥 Bundle Orchestrator: атомарный строитель ${atomicTransactionBuilder ? 'ПЕРЕДАН' : 'НЕ ПЕРЕДАН'}`);
    this.isRunning = false;
    this.opportunityQueue = [];
    this.processingPromises = new Map();
    this.opportunityResults = new Map(); // Хранение результатов обработки
    this.maxConcurrentProcessing = 10; // 🚀 МАКСИМУМ: 10 одновременных обработок для нескольких пар!
    this.queueProcessingInterval = 1; // 🚀 МГНОВЕННО: 1мс интервал для максимальной скорости!
    this.maxQueueSize = 50; // 🚀 УВЕЛИЧЕНО: 50 возможностей для нескольких пар одновременно!

    // Статистика оркестратора
    this.orchestratorStats = {
      totalQueued: 0,
      totalProcessed: 0,
      totalExpired: 0,
      averageQueueTime: 0,
      maxQueueLength: 0,
      startTime: Date.now()
    };

    console.log('🎼 Bundle Orchestrator инициализирован');
    console.log(`⚡ Максимум одновременных обработок: ${this.maxConcurrentProcessing}`);
    console.log(`🔄 Интервал обработки очереди: ${this.queueProcessingInterval}ms`);
  }

  // Запуск оркестратора
  async start() {
    if (this.isRunning) {
      console.log('⚠️ Bundle Orchestrator уже запущен');
      return;
    }

    this.isRunning = true;
    this.orchestratorStats.startTime = Date.now();

    console.log('🎼 Bundle Orchestrator запущен');

    // Запускаем обработчик очереди
    this.processQueue();

    // Запускаем периодический вывод статистики
    this.startStatsReporting();
  }

  // Остановка оркестратора
  async stop() {
    if (!this.isRunning) {
      console.log('⚠️ Bundle Orchestrator уже остановлен');
      return;
    }

    console.log('🛑 Остановка Bundle Orchestrator...');
    this.isRunning = false;

    // Ждем завершения всех активных операций
    const activePromises = Array.from(this.processingPromises.values());
    if (activePromises.length > 0) {
      console.log(`⏳ Ожидание завершения ${activePromises.length} активных операций...`);
      await Promise.allSettled(activePromises);
    }

    // Очищаем очередь
    const queuedCount = this.opportunityQueue.length;
    this.opportunityQueue = [];

    console.log('🛑 Bundle Orchestrator остановлен');
    console.log(`📊 Финальная статистика: обработано ${this.orchestratorStats.totalProcessed}, в очереди было ${queuedCount}`);
  }

  // Добавление возможности в очередь
  addOpportunity(opportunity) {
    try {
      // 🔥 ПРИНУДИТЕЛЬНАЯ ОЧИСТКА ПЕРЕПОЛНЕННОЙ ОЧЕРЕДИ
      if (this.opportunityQueue.length >= this.maxQueueSize) {
        console.log(`🚨 ОЧЕРЕДЬ ПЕРЕПОЛНЕНА! Очищаем ${this.opportunityQueue.length} возможностей`);
        this.opportunityQueue = []; // Полная очистка

        // Также очищаем зависшие промисы
        console.log(`🧹 Очищаем ${this.processingPromises.size} зависших промисов`);
        this.processingPromises.clear();
        this.opportunityResults.clear();
      }

      // Проверяем минимальные требования
      if (!this.validateOpportunity(opportunity)) {
        return false;
      }

      const queuedOpportunity = {
        ...opportunity,
        queueTimestamp: Date.now(),
        priority: this.calculatePriority(opportunity),
        // 🔥 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: ИСПОЛЬЗУЕМ СУЩЕСТВУЮЩИЙ ID!
        // ПРОБЛЕМА: Bundle Orchestrator создавал новый ID, но система ждала старый ID
        // РЕШЕНИЕ: Используем ID из возможности, если он есть, иначе создаем новый
        id: opportunity.id || this.generateOpportunityId(opportunity)
      };

      this.opportunityQueue.push(queuedOpportunity);
      this.orchestratorStats.totalQueued++;

      // Сортируем по приоритету (высокий приоритет первым)
      this.opportunityQueue.sort((a, b) => b.priority - a.priority);

      // Обновляем максимальную длину очереди
      this.orchestratorStats.maxQueueLength = Math.max(
        this.orchestratorStats.maxQueueLength,
        this.opportunityQueue.length
      );

      console.log(`📥 [${queuedOpportunity.id}] Добавлена возможность: ${opportunity.tokenSymbol}`);
      console.log(`📊 Очередь: ${this.opportunityQueue.length}, Приоритет: ${queuedOpportunity.priority.toFixed(2)}`);

      return true;

    } catch (error) {
      console.error(`❌ Ошибка добавления возможности: ${error.message}`);
      return false;
    }
  }

  // Валидация возможности
  validateOpportunity(opportunity) {
    // Проверяем обязательные поля
    if (!opportunity.tokenSymbol || !opportunity.spread || !opportunity.amount) {
      console.log(`❌ Неполные данные возможности: ${JSON.stringify(opportunity)}`);
      return false;
    }

    // ✅ КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: ИСПОЛЬЗУЕМ ЦЕНТРАЛИЗОВАННЫЙ КОНФИГ!
    const { TRADING_CONFIG } = require('../../trading-config.js');
    if (opportunity.spread < TRADING_CONFIG.MIN_SPREAD_PERCENT) {
      console.log(`❌ Спред слишком мал: ${opportunity.spread.toFixed(3)}% < ${TRADING_CONFIG.MIN_SPREAD_PERCENT}%`);
      return false;
    }

    // Проверяем минимальную сумму - СНИЖЕНА до $50
    if (opportunity.amount < 50) { // Минимум $50
      console.log(`❌ Сумма слишком мала: $${opportunity.amount} < $50`);
      return false;
    }

    // Проверяем наличие DEX информации
    if (!opportunity.buyDex || !opportunity.sellDex) {
      console.log(`❌ Отсутствует информация о DEX: buy=${opportunity.buyDex}, sell=${opportunity.sellDex}`);
      return false;
    }

    return true;
  }

  // Обработка очереди
  async processQueue() {
    while (this.isRunning) {
      try {
        // 🔧 ДИАГНОСТИКА: Показываем состояние очереди и промисов
        if (this.opportunityQueue.length > 0) {
          console.log(`🔍 ДИАГНОСТИКА ОЧЕРЕДИ: активных промисов ${this.processingPromises.size}/${this.maxConcurrentProcessing}, в очереди ${this.opportunityQueue.length}`);
        }

        // Проверяем можем ли обработать новые возможности
        if (this.processingPromises.size < this.maxConcurrentProcessing && this.opportunityQueue.length > 0) {
          const opportunity = this.opportunityQueue.shift();

          // Проверяем актуальность (не старше 5 секунд)
          const age = Date.now() - opportunity.queueTimestamp;
          if (age < 5000) {
            // Запускаем асинхронную обработку
            this.processOpportunityAsync(opportunity);
          } else {
            console.log(`⏰ [${opportunity.id}] Возможность устарела: ${age}ms`);
            this.orchestratorStats.totalExpired++;
          }
        }

        // Очищаем завершенные промисы
        this.cleanupCompletedPromises();

        // 🚀 МГНОВЕННАЯ ОБРАБОТКА: БЕЗ ПАУЗ для максимальной скорости нескольких пар!
        // await new Promise(resolve => setTimeout(resolve, this.queueProcessingInterval));

      } catch (error) {
        console.error(`❌ Ошибка в обработчике очереди: ${error.message}`);
        await new Promise(resolve => setTimeout(resolve, 100)); // Пауза при ошибке
      }
    }

    console.log('🔄 Обработчик очереди остановлен');
  }

  // Асинхронная обработка возможности
  async processOpportunityAsync(opportunity) {
    const queueTime = Date.now() - opportunity.queueTimestamp;

    // Обновляем статистику времени в очереди
    this.orchestratorStats.averageQueueTime =
      (this.orchestratorStats.averageQueueTime * this.orchestratorStats.totalProcessed + queueTime) /
      (this.orchestratorStats.totalProcessed + 1);

    try {
      console.log(`🚀 [${opportunity.id}] Начало обработки (в очереди: ${queueTime}ms)`);
      console.log(`🔍 Возможность:`, {
        token: opportunity.tokenSymbol || opportunity.token,
        buyDex: opportunity.buyDex,
        sellDex: opportunity.sellDex,
        spread: opportunity.spread,
        amount: opportunity.amount
      });

      // 🚀 ОБРАБОТКА С ДЕТАЛЬНОЙ ДИАГНОСТИКОЙ
      const processingPromise = this.bundleManager.processOpportunity(opportunity);
      processingPromise.createdAt = Date.now();

      this.processingPromises.set(opportunity.id, processingPromise);

      console.log(`⏳ Ожидание результата обработки...`);
      const result = await processingPromise;

      console.log(`✅ Результат обработки:`, {
        success: result.success,
        error: result.error,
        reason: result.reason
      });

      this.orchestratorStats.totalProcessed++;

      if (result.success) {
        console.log(`✅ [${opportunity.id}] Успешно обработано`);
        console.log(`💰 Прибыль: $${result.profit || 'N/A'}`);
        console.log(`🔗 Signature: ${result.signature || 'N/A'}`);
      } else {
        console.log(`❌ [${opportunity.id}] Обработка провалена: ${result.reason || result.error}`);
      }

      // ✅ КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Сохраняем результат для получения
      this.opportunityResults.set(opportunity.id, result);

      // Автоматически удаляем результат через 60 секунд
      setTimeout(() => {
        this.opportunityResults.delete(opportunity.id);
      }, 60000);

      return result;

    } catch (error) {
      console.error(`💥 [${opportunity.id}] Ошибка: ${error.message}`);
      this.orchestratorStats.totalProcessed++;

      const errorResult = {
        success: false,
        error: error.message,
        opportunityId: opportunity.id
      };

      // ✅ КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Сохраняем результат ошибки
      this.opportunityResults.set(opportunity.id, errorResult);

      // Автоматически удаляем результат через 60 секунд
      setTimeout(() => {
        this.opportunityResults.delete(opportunity.id);
      }, 60000);

      return errorResult;

    } finally {
      // 🔥 ПРОСТАЯ ОЧИСТКА
      this.processingPromises.delete(opportunity.id);
    }
  }

  // 🚀 УСКОРЕННАЯ ОЧИСТКА ДЛЯ МГНОВЕННЫХ СДЕЛОК
  cleanupCompletedPromises() {
    const now = Date.now();

    // 🚀 БЫСТРАЯ ОЧИСТКА: Удаляем промисы старше 1 секунды для мгновенных сделок
    for (const [id, promiseData] of this.processingPromises.entries()) {
      const createdAt = promiseData.createdAt || (now - 10000);
      const age = now - createdAt;

      if (age > 1000) { // 🚀 1 секунда для мгновенных сделок по нескольким парам
        this.processingPromises.delete(id);
        this.opportunityResults.delete(id);
        console.log(`🚀 Быстрая очистка промиса: ${id} (${age}ms)`);
      }
    }

    // 🔥 ЭКСТРЕННАЯ ОЧИСТКА: Если все слоты заняты - очищаем ВСЕ
    if (this.processingPromises.size >= this.maxConcurrentProcessing) {
      console.log(`🚨 DEADLOCK: очищаем все ${this.processingPromises.size} промисов`);
      this.processingPromises.clear();
      this.opportunityResults.clear();
    }

    // 🔥 НОВОЕ: Принудительная очистка если очередь слишком большая
    if (this.opportunityQueue.length > this.maxQueueSize * 2) {
      console.log(`🚨 ПЕРЕПОЛНЕНИЕ: очищаем очередь ${this.opportunityQueue.length} возможностей`);
      this.opportunityQueue = [];
    }
  }

  // Расчет приоритета возможности
  calculatePriority(opportunity) {
    try {
      // Базовый приоритет = спред * сумма
      let priority = opportunity.spread * opportunity.amount;

      // Бонус за большие суммы
      if (opportunity.amount > 1000) {
        priority *= 1.2;
      }

      // Бонус за высокие спреды
      const { TRADING_CONFIG } = require('../../trading-config.js');
      if (opportunity.spread > TRADING_CONFIG.MIN_SPREAD_PERCENT) {
        priority *= 1.5;
      }

      // Штраф за время жизни (если есть timestamp)
      if (opportunity.timestamp) {
        const ageSeconds = (Date.now() - opportunity.timestamp) / 1000;
        const ageFactor = Math.max(0.1, 1 - ageSeconds / 10); // Снижается со временем
        priority *= ageFactor;
      }

      return priority;

    } catch (error) {
      console.error(`❌ Ошибка расчета приоритета: ${error.message}`);
      return 1; // Минимальный приоритет
    }
  }

  // Генерация ID возможности
  generateOpportunityId(opportunity) {
    const timestamp = Date.now().toString(36);
    const symbol = opportunity.tokenSymbol.replace('/', '-');
    const random = Math.random().toString(36).substring(2, 5);

    return `${symbol}-${timestamp}-${random}`;
  }

  // Запуск периодического вывода статистики
  startStatsReporting() {
    if (this.statsInterval) {
      clearInterval(this.statsInterval);
    }

    this.statsInterval = setInterval(() => {
      if (this.isRunning) {
        this.logStats();
      }
    }, 30000); // Каждые 30 секунд
  }

  // Вывод статистики
  logStats() {
    const stats = this.getStats();
    const runtime = (Date.now() - this.orchestratorStats.startTime) / 1000 / 60;

    console.log('\n🎼 BUNDLE ORCHESTRATOR - СТАТИСТИКА');
    console.log('═'.repeat(50));
    console.log(`⏱️  Время работы: ${runtime.toFixed(1)} минут`);
    console.log(`📥 В очереди: ${stats.queueLength}`);
    console.log(`⚡ Обрабатывается: ${stats.activeProcessing}`);
    console.log(`📊 Обработано: ${stats.totalProcessed}/${stats.totalQueued}`);
    console.log(`✅ Успешность: ${stats.bundleSuccessRate}%`);
    console.log(`💰 Общая прибыль: $${stats.totalProfit}`);
    console.log(`⏱️  Среднее время в очереди: ${stats.averageQueueTime}ms`);
    console.log(`📈 Макс. длина очереди: ${stats.maxQueueLength}`);
    console.log('═'.repeat(50));
  }

  // Получение полной статистики
  getStats() {
    const bundleStats = this.bundleManager.getStats();

    return {
      // Статистика оркестратора
      ...this.orchestratorStats,
      queueLength: this.opportunityQueue.length,
      activeProcessing: this.processingPromises.size,
      isRunning: this.isRunning,

      // Статистика bundle менеджера
      ...bundleStats,
      bundleSuccessRate: bundleStats.successRate,

      // Вычисляемые поля
      processingRate: this.orchestratorStats.totalProcessed > 0 ?
        (this.orchestratorStats.totalProcessed / ((Date.now() - this.orchestratorStats.startTime) / 1000 / 60)).toFixed(2) : 0
    };
  }

  // Очистка очереди
  clearQueue() {
    const cleared = this.opportunityQueue.length;
    this.opportunityQueue = [];
    console.log(`🧹 Очищено возможностей из очереди: ${cleared}`);
    return cleared;
  }

  // Принудительная обработка следующей возможности
  async forceProcessNext() {
    if (this.opportunityQueue.length === 0) {
      console.log('⚠️ Очередь пуста, нечего обрабатывать');
      return null;
    }

    const opportunity = this.opportunityQueue.shift();
    console.log(`🔧 Принудительная обработка: ${opportunity.id}`);

    return await this.processOpportunityAsync(opportunity);
  }

  // Получение информации об очереди
  getQueueInfo() {
    return {
      length: this.opportunityQueue.length,
      opportunities: this.opportunityQueue.map(opp => ({
        id: opp.id,
        tokenSymbol: opp.tokenSymbol,
        spread: opp.spread,
        amount: opp.amount,
        priority: opp.priority,
        age: Date.now() - opp.queueTimestamp
      }))
    };
  }

  // ✅ КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Получение результата обработки возможности
  getOpportunityResult(opportunityId) {
    return this.opportunityResults.get(opportunityId);
  }

  // Получение всех результатов
  getAllResults() {
    return Array.from(this.opportunityResults.entries()).map(([id, result]) => ({
      id,
      ...result
    }));
  }

  // Очистка старых результатов
  clearOldResults(maxAge = 300000) { // 5 минут по умолчанию
    const now = Date.now();
    for (const [id, result] of this.opportunityResults.entries()) {
      if (result.timestamp && (now - result.timestamp) > maxAge) {
        this.opportunityResults.delete(id);
      }
    }
  }
}

module.exports = BundleOrchestrator;
