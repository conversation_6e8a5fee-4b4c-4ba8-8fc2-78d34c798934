use anyhow::Result;
use rusqlite::{Connection, params};
use std::sync::Arc;
use tokio::sync::Mutex;

use crate::scanner::Vulnerability;

#[derive(Debug, Clone)]
pub struct Database {
    connection: Arc<Mutex<Connection>>,
}

#[derive(Debug)]
pub struct Exploit {
    pub id: String,
    pub vulnerability_id: String,
    pub title: String,
    pub description: String,
    pub proof_of_concept: String,
    pub estimated_reward: u64,
    pub status: String,
    pub submission_id: Option<String>,
    pub created_at: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug)]
pub struct HuntingStatistics {
    pub programs_scanned: u64,
    pub vulnerabilities_found: u64,
    pub critical_bugs: u64,
    pub exploits_generated: u64,
    pub reports_submitted: u64,
    pub potential_rewards: u64,
    pub confirmed_rewards: u64,
}

impl Database {
    pub async fn new(db_path: &str) -> Result<Self> {
        let conn = Connection::open(db_path)?;
        
        let db = Self {
            connection: Arc::new(Mutex::new(conn)),
        };
        
        db.initialize_tables().await?;
        
        Ok(db)
    }
    
    async fn initialize_tables(&self) -> Result<()> {
        let conn = self.connection.lock().await;
        
        // Таблица уязвимостей
        conn.execute(
            "CREATE TABLE IF NOT EXISTS vulnerabilities (
                id TEXT PRIMARY KEY,
                program_id TEXT NOT NULL,
                vulnerability_type TEXT NOT NULL,
                severity TEXT NOT NULL,
                description TEXT NOT NULL,
                estimated_reward INTEGER NOT NULL,
                confidence REAL NOT NULL,
                proof_of_concept TEXT,
                discovered_at TEXT NOT NULL
            )",
            [],
        )?;
        
        // Таблица эксплойтов
        conn.execute(
            "CREATE TABLE IF NOT EXISTS exploits (
                id TEXT PRIMARY KEY,
                vulnerability_id TEXT NOT NULL,
                title TEXT NOT NULL,
                description TEXT NOT NULL,
                proof_of_concept TEXT NOT NULL,
                estimated_reward INTEGER NOT NULL,
                status TEXT NOT NULL DEFAULT 'created',
                submission_id TEXT,
                created_at TEXT NOT NULL,
                FOREIGN KEY(vulnerability_id) REFERENCES vulnerabilities(id)
            )",
            [],
        )?;
        
        // Таблица отправленных отчетов
        conn.execute(
            "CREATE TABLE IF NOT EXISTS submissions (
                id TEXT PRIMARY KEY,
                exploit_id TEXT NOT NULL,
                bug_bounty_program TEXT NOT NULL,
                submission_id TEXT,
                status TEXT NOT NULL DEFAULT 'submitted',
                response TEXT,
                reward_amount INTEGER,
                submitted_at TEXT NOT NULL,
                FOREIGN KEY(exploit_id) REFERENCES exploits(id)
            )",
            [],
        )?;
        
        // Таблица статистики
        conn.execute(
            "CREATE TABLE IF NOT EXISTS statistics (
                id INTEGER PRIMARY KEY,
                programs_scanned INTEGER DEFAULT 0,
                vulnerabilities_found INTEGER DEFAULT 0,
                critical_bugs INTEGER DEFAULT 0,
                exploits_generated INTEGER DEFAULT 0,
                reports_submitted INTEGER DEFAULT 0,
                potential_rewards INTEGER DEFAULT 0,
                confirmed_rewards INTEGER DEFAULT 0,
                last_updated TEXT NOT NULL
            )",
            [],
        )?;
        
        // Инициализируем статистику если её нет
        conn.execute(
            "INSERT OR IGNORE INTO statistics (id, last_updated) VALUES (1, ?)",
            params![chrono::Utc::now().to_rfc3339()],
        )?;
        
        Ok(())
    }
    
    pub async fn save_vulnerability(&self, vulnerability: &Vulnerability) -> Result<()> {
        let conn = self.connection.lock().await;
        
        conn.execute(
            "INSERT OR REPLACE INTO vulnerabilities 
             (id, program_id, vulnerability_type, severity, description, 
              estimated_reward, confidence, proof_of_concept, discovered_at)
             VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9)",
            params![
                vulnerability.id,
                vulnerability.program_id,
                format!("{:?}", vulnerability.vulnerability_type),
                vulnerability.severity,
                vulnerability.description,
                vulnerability.estimated_reward as i64,
                vulnerability.confidence,
                vulnerability.proof_of_concept,
                vulnerability.discovered_at.to_rfc3339(),
            ],
        )?;
        
        // Обновляем статистику
        conn.execute(
            "UPDATE statistics SET 
             vulnerabilities_found = vulnerabilities_found + 1,
             critical_bugs = critical_bugs + CASE WHEN ? = 'Critical' THEN 1 ELSE 0 END,
             potential_rewards = potential_rewards + ?,
             last_updated = ?
             WHERE id = 1",
            params![
                vulnerability.severity,
                vulnerability.estimated_reward as i64,
                chrono::Utc::now().to_rfc3339(),
            ],
        )?;
        
        Ok(())
    }
    
    pub async fn get_all_vulnerabilities(&self) -> Result<Vec<Vulnerability>> {
        let conn = self.connection.lock().await;
        
        let mut stmt = conn.prepare(
            "SELECT id, program_id, vulnerability_type, severity, description,
                    estimated_reward, confidence, proof_of_concept, discovered_at
             FROM vulnerabilities
             ORDER BY estimated_reward DESC"
        )?;
        
        let vulnerability_iter = stmt.query_map([], |row| {
            Ok(Vulnerability {
                id: row.get(0)?,
                program_id: row.get(1)?,
                vulnerability_type: match row.get::<_, String>(2)?.as_str() {
                    "Reentrancy" => crate::scanner::VulnerabilityType::Reentrancy,
                    "IntegerOverflow" => crate::scanner::VulnerabilityType::IntegerOverflow,
                    "ValidationBypass" => crate::scanner::VulnerabilityType::ValidationBypass,
                    "MemoryCorruption" => crate::scanner::VulnerabilityType::MemoryCorruption,
                    "AccessControl" => crate::scanner::VulnerabilityType::AccessControl,
                    "LogicError" => crate::scanner::VulnerabilityType::LogicError,
                    "BufferOverflow" => crate::scanner::VulnerabilityType::BufferOverflow,
                    _ => crate::scanner::VulnerabilityType::UnauthorizedAccess,
                },
                severity: row.get(3)?,
                description: row.get(4)?,
                estimated_reward: row.get::<_, i64>(5)? as u64,
                confidence: row.get(6)?,
                proof_of_concept: row.get(7)?,
                discovered_at: chrono::DateTime::parse_from_rfc3339(&row.get::<_, String>(8)?)
                    .unwrap().with_timezone(&chrono::Utc),
            })
        })?;
        
        let mut vulnerabilities = Vec::new();
        for vulnerability in vulnerability_iter {
            vulnerabilities.push(vulnerability?);
        }
        
        Ok(vulnerabilities)
    }
    
    pub async fn save_exploit(&self, exploit: &Exploit) -> Result<()> {
        let conn = self.connection.lock().await;
        
        conn.execute(
            "INSERT OR REPLACE INTO exploits 
             (id, vulnerability_id, title, description, proof_of_concept,
              estimated_reward, status, submission_id, created_at)
             VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9)",
            params![
                exploit.id,
                exploit.vulnerability_id,
                exploit.title,
                exploit.description,
                exploit.proof_of_concept,
                exploit.estimated_reward as i64,
                exploit.status,
                exploit.submission_id,
                exploit.created_at.to_rfc3339(),
            ],
        )?;
        
        // Обновляем статистику
        conn.execute(
            "UPDATE statistics SET 
             exploits_generated = exploits_generated + 1,
             last_updated = ?
             WHERE id = 1",
            params![chrono::Utc::now().to_rfc3339()],
        )?;
        
        Ok(())
    }
    
    pub async fn get_all_exploits(&self) -> Result<Vec<Exploit>> {
        let conn = self.connection.lock().await;
        
        let mut stmt = conn.prepare(
            "SELECT id, vulnerability_id, title, description, proof_of_concept,
                    estimated_reward, status, submission_id, created_at
             FROM exploits
             WHERE status != 'submitted'
             ORDER BY estimated_reward DESC"
        )?;
        
        let exploit_iter = stmt.query_map([], |row| {
            Ok(Exploit {
                id: row.get(0)?,
                vulnerability_id: row.get(1)?,
                title: row.get(2)?,
                description: row.get(3)?,
                proof_of_concept: row.get(4)?,
                estimated_reward: row.get::<_, i64>(5)? as u64,
                status: row.get(6)?,
                submission_id: row.get(7)?,
                created_at: chrono::DateTime::parse_from_rfc3339(&row.get::<_, String>(8)?)
                    .unwrap().with_timezone(&chrono::Utc),
            })
        })?;
        
        let mut exploits = Vec::new();
        for exploit in exploit_iter {
            exploits.push(exploit?);
        }
        
        Ok(exploits)
    }
    
    pub async fn update_exploit_status(&self, exploit_id: &str, status: &str, submission_id: &str) -> Result<()> {
        let conn = self.connection.lock().await;
        
        conn.execute(
            "UPDATE exploits SET status = ?, submission_id = ? WHERE id = ?",
            params![status, submission_id, exploit_id],
        )?;
        
        if status == "submitted" {
            conn.execute(
                "UPDATE statistics SET 
                 reports_submitted = reports_submitted + 1,
                 last_updated = ?
                 WHERE id = 1",
                params![chrono::Utc::now().to_rfc3339()],
            )?;
        }
        
        Ok(())
    }
    
    pub async fn get_hunting_statistics(&self) -> Result<HuntingStatistics> {
        let conn = self.connection.lock().await;
        
        let mut stmt = conn.prepare(
            "SELECT programs_scanned, vulnerabilities_found, critical_bugs,
                    exploits_generated, reports_submitted, potential_rewards, confirmed_rewards
             FROM statistics WHERE id = 1"
        )?;
        
        let stats = stmt.query_row([], |row| {
            Ok(HuntingStatistics {
                programs_scanned: row.get::<_, i64>(0)? as u64,
                vulnerabilities_found: row.get::<_, i64>(1)? as u64,
                critical_bugs: row.get::<_, i64>(2)? as u64,
                exploits_generated: row.get::<_, i64>(3)? as u64,
                reports_submitted: row.get::<_, i64>(4)? as u64,
                potential_rewards: row.get::<_, i64>(5)? as u64,
                confirmed_rewards: row.get::<_, i64>(6)? as u64,
            })
        })?;
        
        Ok(stats)
    }
}
