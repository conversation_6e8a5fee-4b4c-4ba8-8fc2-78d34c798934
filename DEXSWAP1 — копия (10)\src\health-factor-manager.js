/**
 * 🏥 HEALTH FACTOR MANAGER
 * 
 * Официальное решение для MarginFi Error 6009 "RiskEngine rejected due to either bad health"
 * 
 * Основано на официальной документации MarginFi:
 * - Error 6009: "RiskEngine rejected due to either bad health or stale oracles"
 * - Health Factor = Assets / (Liabilities + 1)
 * - Минимальный Health Factor для операций: 1.1 (110%)
 * 
 * <AUTHOR> Error 6009 Fix Team
 * @version 2.0.0
 */

const { PublicKey } = require('@solana/web3.js');

class HealthFactorManager {
  constructor(marginfiClient, wallet) {
    this.marginfiClient = marginfiClient;
    this.wallet = wallet;
    
    // Константы из MarginFi документации
    this.MIN_HEALTH_FACTOR = 1.1; // 110% минимальный health factor
    this.SAFE_HEALTH_FACTOR = 1.5; // 150% безопасный health factor
    this.CRITICAL_HEALTH_FACTOR = 1.05; // 105% критический health factor
    
    console.log('🏥 Health Factor Manager инициализирован');
    console.log(`   💚 Минимальный Health Factor: ${this.MIN_HEALTH_FACTOR}`);
    console.log(`   💛 Безопасный Health Factor: ${this.SAFE_HEALTH_FACTOR}`);
    console.log(`   ❤️ Критический Health Factor: ${this.CRITICAL_HEALTH_FACTOR}`);
  }

  /**
   * 🏥 ПРОВЕРКА HEALTH FACTOR MARGINFI АККАУНТА
   * 
   * Официальное решение для Error 6009 из документации MarginFi
   */
  async checkAccountHealth(marginfiAccount) {
    console.log(`🏥 Проверяем health factor аккаунта: ${marginfiAccount.address.toString()}`);

    // 🔍 ОТЛАДКА: Показываем доступные методы
    console.log(`🔍 Доступные методы аккаунта:`, Object.getOwnPropertyNames(Object.getPrototypeOf(marginfiAccount)));
    console.log(`🔍 Тип аккаунта:`, marginfiAccount.constructor.name);

    try {
      // 🔧 ИСПРАВЛЕНИЕ: Правильный API MarginFi для получения health данных
      let assets = 0;
      let liabilities = 0;
      let healthFactor = 0;

      try {
        // Пробуем новый API
        if (typeof marginfiAccount.computeHealthComponents === 'function') {
          const healthComponents = await marginfiAccount.computeHealthComponents();
          assets = healthComponents.assets || 0;
          liabilities = healthComponents.liabilities || 0;
        }
        // Пробуем альтернативный API
        else if (typeof marginfiAccount.getHealthComponents === 'function') {
          const healthComponents = await marginfiAccount.getHealthComponents();
          assets = healthComponents.assets || 0;
          liabilities = healthComponents.liabilities || 0;
        }
        // Пробуем прямое получение health factor
        else if (typeof marginfiAccount.getHealthFactor === 'function') {
          healthFactor = await marginfiAccount.getHealthFactor();
          console.log(`📊 Health Factor получен напрямую: ${healthFactor}`);
        }
        // Fallback - используем балансы
        else {
          console.log(`🔧 Используем fallback метод через балансы...`);
          const balances = marginfiAccount.balances || [];

          for (const balance of balances) {
            if (balance.active) {
              const assetValue = balance.assetShares?.toNumber() || 0;
              const liabilityValue = balance.liabilityShares?.toNumber() || 0;

              assets += assetValue;
              liabilities += liabilityValue;
            }
          }
        }
      } catch (apiError) {
        console.log(`⚠️ Ошибка API health components: ${apiError.message}`);
        console.log(`🔧 Используем консервативную оценку...`);

        // Консервативная оценка - считаем что аккаунт здоров если нет явных долгов
        assets = 1;
        liabilities = 0;
      }
      
      // 2. Рассчитываем health factor по официальной формуле если не получили напрямую
      // Health Factor = Assets / (Liabilities + 1)
      if (healthFactor === 0) {
        healthFactor = assets > 0 ? assets / (liabilities + 1) : 0;
      }
      
      console.log(`💰 Активы: $${assets.toFixed(2)}`);
      console.log(`💸 Долги: $${liabilities.toFixed(2)}`);
      console.log(`🏥 Health Factor: ${healthFactor.toFixed(4)}`);

      // 3. Определяем статус здоровья
      let status = 'HEALTHY';
      let isHealthy = true;
      let reason = '';

      if (liabilities === 0) {
        status = 'NO_DEBT';
        console.log(`✅ Аккаунт без долгов - здоровье отличное`);
      } else if (healthFactor >= this.SAFE_HEALTH_FACTOR) {
        status = 'SAFE';
        console.log(`💚 Здоровье безопасное: ${healthFactor.toFixed(4)} >= ${this.SAFE_HEALTH_FACTOR}`);
      } else if (healthFactor >= this.MIN_HEALTH_FACTOR) {
        status = 'ACCEPTABLE';
        console.log(`💛 Здоровье приемлемое: ${healthFactor.toFixed(4)} >= ${this.MIN_HEALTH_FACTOR}`);
      } else if (healthFactor >= this.CRITICAL_HEALTH_FACTOR) {
        status = 'CRITICAL';
        isHealthy = false;
        reason = 'CRITICAL_HEALTH';
        console.log(`🧡 Критическое здоровье: ${healthFactor.toFixed(4)} >= ${this.CRITICAL_HEALTH_FACTOR}`);
      } else {
        status = 'UNHEALTHY';
        isHealthy = false;
        reason = 'BAD_HEALTH';
        console.log(`❤️ Плохое здоровье: ${healthFactor.toFixed(4)} < ${this.CRITICAL_HEALTH_FACTOR}`);
      }

      return {
        isHealthy,
        healthFactor,
        assets,
        liabilities,
        status,
        reason
      };

    } catch (error) {
      console.error(`❌ Ошибка проверки health factor: ${error.message}`);
      return {
        isHealthy: false,
        healthFactor: 0,
        assets: 0,
        liabilities: 0,
        status: 'ERROR',
        reason: 'HEALTH_CHECK_ERROR',
        error: error.message
      };
    }
  }

  /**
   * 🔧 ИСПРАВЛЕНИЕ ПЛОХОГО HEALTH FACTOR
   */
  async fixBadHealth(marginfiAccount) {
    console.log(`🔧 Исправляем плохой health factor...`);
    
    try {
      const healthCheck = await this.checkAccountHealth(marginfiAccount);
      
      if (healthCheck.isHealthy) {
        console.log(`✅ Health factor уже в норме: ${healthCheck.healthFactor.toFixed(4)}`);
        return true;
      }

      console.log(`🚨 Обнаружен плохой health factor: ${healthCheck.healthFactor.toFixed(4)}`);
      console.log(`💡 Применяем стратегии исправления...`);

      // Стратегия 1: Погашение долгов
      if (healthCheck.liabilities > 0) {
        console.log(`💸 Стратегия 1: Погашение долгов ($${healthCheck.liabilities.toFixed(2)})`);
        
        const repaySuccess = await this.repayDebts(marginfiAccount);
        if (repaySuccess) {
          const newHealthCheck = await this.checkAccountHealth(marginfiAccount);
          if (newHealthCheck.isHealthy) {
            console.log(`✅ Health factor исправлен погашением долгов: ${newHealthCheck.healthFactor.toFixed(4)}`);
            return true;
          }
        }
      }

      // Стратегия 2: Добавление collateral
      console.log(`💰 Стратегия 2: Добавление collateral`);
      const collateralSuccess = await this.addCollateral(marginfiAccount);
      if (collateralSuccess) {
        const newHealthCheck = await this.checkAccountHealth(marginfiAccount);
        if (newHealthCheck.isHealthy) {
          console.log(`✅ Health factor исправлен добавлением collateral: ${newHealthCheck.healthFactor.toFixed(4)}`);
          return true;
        }
      }

      // ❌ СОЗДАНИЕ НОВЫХ АККАУНТОВ ОТКЛЮЧЕНО!
      console.log(`❌ Стратегия 3 ОТКЛЮЧЕНА: Создание новых аккаунтов запрещено`);
      console.log(`💡 ИСПОЛЬЗУЙТЕ ТОЛЬКО СУЩЕСТВУЮЩИЕ РЕАЛЬНЫЕ АККАУНТЫ!`);
      // НЕ СОЗДАЕМ НОВЫЕ АККАУНТЫ

      console.log(`❌ Не удалось исправить health factor`);
      return false;

    } catch (error) {
      console.error(`❌ Ошибка исправления health factor: ${error.message}`);
      return false;
    }
  }

  /**
   * 💸 ПОГАШЕНИЕ ДОЛГОВ ДЛЯ УЛУЧШЕНИЯ HEALTH FACTOR
   */
  async repayDebts(marginfiAccount) {
    console.log(`💸 Погашаем долги для улучшения health factor...`);
    
    try {
      // Получаем все балансы с долгами
      const balances = marginfiAccount.balances;
      const debtBalances = balances.filter(balance => 
        balance.liabilityShares && balance.liabilityShares.gt(0)
      );

      if (debtBalances.length === 0) {
        console.log(`✅ Долгов не найдено`);
        return true;
      }

      console.log(`💸 Найдено ${debtBalances.length} долгов для погашения`);

      // Погашаем каждый долг
      for (const debtBalance of debtBalances) {
        try {
          const bank = this.marginfiClient.getBankByPk(debtBalance.bankPk);
          const tokenSymbol = bank.tokenSymbol || 'UNKNOWN';
          
          console.log(`💸 Погашаем долг ${tokenSymbol}...`);
          
          // Создаем инструкцию погашения с repayAll=true
          const repayIx = await marginfiAccount.makeRepayIx(
            0, // amount = 0 означает погасить все
            bank.address,
            true // repayAll = true
          );

          if (repayIx && repayIx.instructions.length > 0) {
            console.log(`✅ Долг ${tokenSymbol} погашен`);
          }

        } catch (repayError) {
          console.error(`❌ Ошибка погашения долга: ${repayError.message}`);
          
          if (repayError.message.includes('insufficient funds')) {
            console.log(`💡 Недостаточно средств для погашения долга`);
          }
        }
      }

      return true;

    } catch (error) {
      console.error(`❌ Ошибка погашения долгов: ${error.message}`);
      return false;
    }
  }

  /**
   * 💰 ДОБАВЛЕНИЕ COLLATERAL ДЛЯ УЛУЧШЕНИЯ HEALTH FACTOR
   */
  async addCollateral(marginfiAccount) {
    console.log(`💰 Добавляем collateral для улучшения health factor...`);
    
    try {
      // Проверяем баланс SOL в кошельке
      const solBalance = await this.marginfiClient.connection.getBalance(this.wallet.publicKey);
      const solBalanceUI = solBalance / 1e9; // Конвертируем в SOL
      
      console.log(`💰 Баланс SOL в кошельке: ${solBalanceUI.toFixed(6)} SOL`);

      if (solBalanceUI < 0.01) {
        console.log(`⚠️ Недостаточно SOL для добавления collateral`);
        return false;
      }

      // Добавляем небольшое количество SOL как collateral
      const depositAmount = Math.min(0.005, solBalanceUI * 0.1); // 0.005 SOL или 10% от баланса
      const depositAmountLamports = Math.floor(depositAmount * 1e9);

      console.log(`💰 Добавляем ${depositAmount.toFixed(6)} SOL как collateral...`);

      // Находим SOL банк
      const solBank = this.marginfiClient.getBankByMint(new PublicKey('So11111111111111111111111111111111111111112'));
      if (!solBank) {
        console.log(`❌ SOL банк не найден`);
        return false;
      }

      // Создаем инструкцию депозита
      const depositIx = await marginfiAccount.makeDepositIx(
        depositAmountLamports,
        solBank.address
      );

      if (depositIx && depositIx.instructions.length > 0) {
        console.log(`✅ Collateral добавлен: ${depositAmount.toFixed(6)} SOL`);
        return true;
      }

      return false;

    } catch (error) {
      console.error(`❌ Ошибка добавления collateral: ${error.message}`);
      return false;
    }
  }

  /**
   * 🆕 СОЗДАНИЕ НОВОГО ЗДОРОВОГО MARGINFI АККАУНТА
   */
  async createHealthyAccount() {
    console.log(`❌ СОЗДАНИЕ НОВЫХ АККАУНТОВ ОТКЛЮЧЕНО!`);
    console.log(`💡 ИСПОЛЬЗУЙТЕ ТОЛЬКО СУЩЕСТВУЮЩИЕ РЕАЛЬНЫЕ АККАУНТЫ!`);

    // НЕ СОЗДАЕМ НОВЫЕ АККАУНТЫ - ВОЗВРАЩАЕМ NULL
    return null;
  }

  /**
   * 🏥 РЕКОМЕНДАЦИИ ПО УЛУЧШЕНИЮ HEALTH FACTOR
   */
  getHealthRecommendations(healthCheck) {
    const recommendations = [];

    if (healthCheck.isHealthy) {
      recommendations.push('✅ Health factor в норме - никаких действий не требуется');
      return recommendations;
    }

    if (healthCheck.liabilities > 0) {
      recommendations.push('💸 Погасите часть долгов для улучшения health factor');
      recommendations.push(`💡 Требуется погасить ~$${(healthCheck.liabilities * 0.2).toFixed(2)} долгов`);
    }

    if (healthCheck.assets < healthCheck.liabilities * this.MIN_HEALTH_FACTOR) {
      const requiredAssets = healthCheck.liabilities * this.MIN_HEALTH_FACTOR;
      const additionalAssets = requiredAssets - healthCheck.assets;
      recommendations.push('💰 Добавьте больше collateral');
      recommendations.push(`💡 Требуется добавить ~$${additionalAssets.toFixed(2)} collateral`);
    }

    recommendations.push('🆕 Альтернатива: создайте новый MarginFi аккаунт');
    recommendations.push('⏰ Подождите обновления oracle данных');

    return recommendations;
  }

  /**
   * 🏥 АВТОМАТИЧЕСКАЯ ПРОВЕРКА И ИСПРАВЛЕНИЕ HEALTH FACTOR
   */
  async ensureHealthyAccount(marginfiAccount) {
    console.log(`🏥 АВТОМАТИЧЕСКАЯ ПРОВЕРКА И ИСПРАВЛЕНИЕ HEALTH FACTOR...`);
    
    try {
      // 1. Проверяем текущий health factor
      const healthCheck = await this.checkAccountHealth(marginfiAccount);
      
      if (healthCheck.isHealthy) {
        console.log(`✅ Health factor в норме - продолжаем операцию`);
        return { success: true, account: marginfiAccount, healthCheck };
      }

      console.log(`🚨 Обнаружен плохой health factor - применяем исправления...`);
      
      // 2. Показываем рекомендации
      const recommendations = this.getHealthRecommendations(healthCheck);
      console.log(`💡 РЕКОМЕНДАЦИИ:`);
      recommendations.forEach(rec => console.log(`   ${rec}`));

      // 3. Пытаемся исправить автоматически
      const fixResult = await this.fixBadHealth(marginfiAccount);
      
      if (fixResult === true) {
        console.log(`✅ Health factor успешно исправлен`);
        return { success: true, account: marginfiAccount, healthCheck };
      } else if (fixResult && fixResult.address) {
        console.log(`✅ Использован существующий здоровый аккаунт`);
        return { success: true, account: fixResult, healthCheck, existingAccount: true };
      } else {
        console.log(`❌ Не удалось исправить health factor автоматически`);
        return { 
          success: false, 
          account: marginfiAccount, 
          healthCheck, 
          recommendations,
          reason: 'CANNOT_FIX_HEALTH'
        };
      }

    } catch (error) {
      console.error(`❌ Ошибка автоматического исправления health factor: ${error.message}`);
      return { 
        success: false, 
        account: marginfiAccount, 
        healthCheck: null,
        reason: 'HEALTH_CHECK_ERROR',
        error: error.message
      };
    }
  }
}

module.exports = { HealthFactorManager };
