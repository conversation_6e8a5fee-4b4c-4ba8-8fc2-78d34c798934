/**
 * 🔍 JUPITER TRANSACTION VALIDATOR
 * Проверка инструкций и транзакций через Jupiter API
 */

const { PublicKey, Connection } = require('@solana/web3.js');

class JupiterTransactionValidator {
  constructor(connection) {
    this.connection = connection;
    this.jupiterApiBase = 'https://lite-api.jup.ag/swap/v1';
  }

  /**
   * 🔍 ПРОВЕРКА КОТИРОВКИ JUPITER
   */
  async validateQuote(inputMint, outputMint, amount, slippageBps = 50) {
    try {
      console.log(`🔍 Проверяем котировку Jupiter...`);
      console.log(`   📊 ${inputMint} → ${outputMint}`);
      console.log(`   💰 Сумма: ${amount}`);
      console.log(`   📈 Slippage: ${slippageBps} BPS`);

      const quoteUrl = `${this.jupiterApiBase}/quote?inputMint=${inputMint}&outputMint=${outputMint}&amount=${amount}&slippageBps=${slippageBps}&restrictIntermediateTokens=true&maxAccounts=64`;

      const response = await fetch(quoteUrl);
      const quote = await response.json();

      if (quote.error) {
        console.log(`❌ Ошибка котировки: ${quote.error}`);
        return { valid: false, error: quote.error, quote: null };
      }

      console.log(`✅ Котировка получена:`);
      console.log(`   💰 Входная сумма: ${quote.inAmount}`);
      console.log(`   💰 Выходная сумма: ${quote.outAmount}`);
      console.log(`   📊 Price Impact: ${quote.priceImpactPct}%`);
      console.log(`   🛣️ Маршрутов: ${quote.routePlan?.length || 0}`);
      console.log(`   ⏱️ Время: ${quote.timeTaken}s`);

      return { valid: true, error: null, quote };

    } catch (error) {
      console.log(`❌ Ошибка проверки котировки: ${error.message}`);
      return { valid: false, error: error.message, quote: null };
    }
  }

  /**
   * 🔧 ПРОВЕРКА SWAP ИНСТРУКЦИЙ
   */
  async validateSwapInstructions(quote, userPublicKey) {
    try {
      console.log(`🔧 Проверяем swap инструкции...`);
      console.log(`   👤 Пользователь: ${userPublicKey}`);

      const response = await fetch(`${this.jupiterApiBase}/swap-instructions`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          quoteResponse: quote,
          userPublicKey: userPublicKey,
          wrapAndUnwrapSol: true,
          skipUserAccountsRpcCalls: true,
          dynamicComputeUnitLimit: true,
          dynamicSlippage: true,
          prioritizationFeeLamports: {
            priorityLevelWithMaxLamports: {
              maxLamports: 0,        // 🔥 ТЕСТ: 0 lamports для тестирования!
              priorityLevel: "none"  // 🔥 ТЕСТ: Никаких priority fees!
            }
          }
        })
      });

      const result = await response.json();

      if (result.error) {
        console.log(`❌ Ошибка инструкций: ${result.error}`);
        return { valid: false, error: result.error, instructions: null };
      }

      // Анализ структуры ответа
      const analysis = this.analyzeInstructions(result);

      console.log(`✅ Swap инструкции получены:`);
      console.log(`   🧮 Compute Budget: ${analysis.computeBudgetCount} инструкций`);
      console.log(`   🔧 Setup: ${analysis.setupCount} инструкций`);
      console.log(`   🔄 Swap: ${analysis.hasSwap ? 'ДА' : 'НЕТ'}`);
      console.log(`   🧹 Cleanup: ${analysis.hasCleanup ? 'ДА' : 'НЕТ'}`);
      console.log(`   📊 Всего аккаунтов: ${analysis.totalAccounts}`);
      console.log(`   🗂️ ALT таблиц: ${analysis.altCount}`);
      console.log(`   💻 Compute Units: ${result.computeUnitLimit}`);
      console.log(`   💰 Priority Fee: ${result.prioritizationFeeLamports} lamports`);

      return { valid: true, error: null, instructions: result, analysis };

    } catch (error) {
      console.log(`❌ Ошибка проверки инструкций: ${error.message}`);
      return { valid: false, error: error.message, instructions: null };
    }
  }

  /**
   * 📊 АНАЛИЗ ИНСТРУКЦИЙ
   */
  analyzeInstructions(result) {
    let totalAccounts = 0;

    // Compute Budget инструкции
    const computeBudgetCount = result.computeBudgetInstructions?.length || 0;
    if (result.computeBudgetInstructions) {
      result.computeBudgetInstructions.forEach(ix => {
        totalAccounts += ix.accounts?.length || 0;
      });
    }

    // Setup инструкции
    const setupCount = result.setupInstructions?.length || 0;
    if (result.setupInstructions) {
      result.setupInstructions.forEach(ix => {
        totalAccounts += ix.accounts?.length || 0;
      });
    }

    // Swap инструкция
    const hasSwap = !!result.swapInstruction;
    if (result.swapInstruction) {
      totalAccounts += result.swapInstruction.accounts?.length || 0;
    }

    // Cleanup инструкция
    const hasCleanup = !!result.cleanupInstruction;
    if (result.cleanupInstruction) {
      totalAccounts += result.cleanupInstruction.accounts?.length || 0;
    }

    // ALT таблицы
    const altCount = result.addressLookupTableAddresses?.length || 0;

    return {
      computeBudgetCount,
      setupCount,
      hasSwap,
      hasCleanup,
      totalAccounts,
      altCount,
      isValid: hasSwap && totalAccounts > 0
    };
  }

  /**
   * 🔥 НИЗКОУРОВНЕВЫЙ ПОДХОД: НЕ СИМУЛИРУЕМ!
   */
  async simulateTransaction(transaction, addressLookupTableAccounts = []) {
    console.log('🔥 SIMULATION ОТКЛЮЧЕНА! НИЗКОУРОВНЕВЫЙ РЕЖИМ!');
    console.log('💡 В низкоуровневом коде мы НЕ симулируем - отправляем напрямую!');

    return {
      success: true,
      logs: ['🔥 SIMULATION DISABLED - LOW LEVEL MODE'],
      unitsConsumed: 0,
      error: null
    };

      console.log(`📊 РЕЗУЛЬТАТ СИМУЛЯЦИИ:`);
      console.log(`   ✅ Успех: ${!simulationResult.value.err}`);
      console.log(`   💻 Compute Units: ${simulationResult.value.unitsConsumed || 'N/A'}`);
      console.log(`   📝 Логов: ${simulationResult.value.logs?.length || 0}`);

      if (simulationResult.value.err) {
        console.log(`❌ ОШИБКА СИМУЛЯЦИИ:`, simulationResult.value.err);
        console.log(`📝 ЛОГИ ОШИБКИ:`);
        if (simulationResult.value.logs) {
          simulationResult.value.logs.forEach((log, i) => {
            console.log(`   ${i}: ${log}`);
          });
        }

        return {
          valid: false,
          error: simulationResult.value.err,
          result: simulationResult.value,
          logs: simulationResult.value.logs || []
        };
      }

      console.log(`✅ Симуляция успешна:`);
      console.log(`   💻 Compute Units: ${simulationResult.value.unitsConsumed}`);
      console.log(`   📝 Логов: ${simulationResult.value.logs?.length || 0}`);

      return {
        valid: true,
        error: null,
        result: simulationResult.value,
        logs: simulationResult.value.logs || []
      };

    } catch (error) {
      console.log(`❌ Ошибка симуляции транзакции: ${error.message}`);
      console.log(`📝 Stack trace:`, error.stack);
      return {
        valid: false,
        error: error.message,
        logs: [],
        exception: error
      };
    }
  }

  /**
   * 🔍 ПРОВЕРКА РАЗМЕРА ТРАНЗАКЦИИ
   */
  validateTransactionSize(transaction) {
    try {
      const serialized = transaction.serialize();
      const size = serialized.length;

      console.log(`📏 Размер транзакции: ${size} байт`);

      if (size > 1232) {
        console.log(`❌ Транзакция слишком большая: ${size} > 1232 байт`);
        return { valid: false, size, error: 'Transaction too large' };
      }

      if (size > 1200) {
        console.log(`⚠️ Транзакция близка к лимиту: ${size} байт`);
      } else {
        console.log(`✅ Размер транзакции в норме: ${size} байт`);
      }

      return { valid: true, size, error: null };

    } catch (error) {
      console.log(`❌ Ошибка проверки размера: ${error.message}`);
      return { valid: false, size: 0, error: error.message };
    }
  }

  /**
   * 🔍 ПОЛНАЯ ПРОВЕРКА JUPITER ТРАНЗАКЦИИ
   */
  async validateJupiterTransaction(inputMint, outputMint, amount, userPublicKey, slippageBps = 50) {
    console.log(`🔍 === ПОЛНАЯ ПРОВЕРКА JUPITER ТРАНЗАКЦИИ ===`);

    const results = {
      quote: null,
      instructions: null,
      simulation: null,
      size: null,
      overall: false
    };

    // 1. Проверка котировки
    const quoteResult = await this.validateQuote(inputMint, outputMint, amount, slippageBps);
    results.quote = quoteResult;

    if (!quoteResult.valid) {
      console.log(`❌ Проверка провалена на этапе котировки`);
      return results;
    }

    // 2. Проверка инструкций
    const instructionsResult = await this.validateSwapInstructions(quoteResult.quote, userPublicKey);
    results.instructions = instructionsResult;

    if (!instructionsResult.valid) {
      console.log(`❌ Проверка провалена на этапе инструкций`);
      return results;
    }

    // 3. Проверка анализа инструкций
    if (!instructionsResult.analysis.isValid) {
      console.log(`❌ Инструкции не прошли анализ валидности`);
      return results;
    }

    console.log(`✅ ВСЕ ПРОВЕРКИ JUPITER ПРОЙДЕНЫ УСПЕШНО!`);
    results.overall = true;

    return results;
  }

  /**
   * 🔍 ПРОВЕРКА СОВМЕСТИМОСТИ С MARGINFI FLASH LOAN
   */
  async validateMarginFiCompatibility(jupiterInstructions, marginFiAccounts = 44) {
    console.log(`🏦 Проверяем совместимость с MarginFi Flash Loan...`);

    const jupiterAccounts = jupiterInstructions.analysis.totalAccounts;
    const totalAccounts = jupiterAccounts + marginFiAccounts;

    console.log(`   🪐 Jupiter аккаунтов: ${jupiterAccounts}`);
    console.log(`   🏦 MarginFi аккаунтов: ${marginFiAccounts}`);
    console.log(`   📊 Всего аккаунтов: ${totalAccounts}`);

    // Проверка лимитов
    const needsALT = totalAccounts > 64;
    const altAvailable = jupiterInstructions.instructions.addressLookupTableAddresses?.length > 0;

    if (needsALT && !altAvailable) {
      console.log(`❌ Нужны ALT для ${totalAccounts} аккаунтов, но ALT не предоставлены`);
      return {
        compatible: false,
        error: 'ALT required but not provided',
        totalAccounts,
        needsALT: true,
        altAvailable: false
      };
    }

    if (totalAccounts > 256) {
      console.log(`❌ Слишком много аккаунтов: ${totalAccounts} > 256`);
      return {
        compatible: false,
        error: 'Too many accounts even with ALT',
        totalAccounts,
        needsALT,
        altAvailable
      };
    }

    console.log(`✅ Совместимость с MarginFi подтверждена`);
    console.log(`   🗂️ ALT нужны: ${needsALT ? 'ДА' : 'НЕТ'}`);
    console.log(`   🗂️ ALT доступны: ${altAvailable ? 'ДА' : 'НЕТ'}`);

    return {
      compatible: true,
      error: null,
      totalAccounts,
      needsALT,
      altAvailable,
      recommendation: needsALT ? 'Use Address Lookup Tables' : 'Standard transaction OK'
    };
  }

  /**
   * 🧪 ТЕСТОВАЯ ПРОВЕРКА JUPITER API
   */
  async testJupiterAPI() {
    console.log(`🧪 === ТЕСТИРОВАНИЕ JUPITER API ===`);

    // Тестовые параметры
    const SOL_MINT = "So11111111111111111111111111111111111111112";
    const USDC_MINT = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v";
    const TEST_AMOUNT = "*********"; // 0.1 SOL
    const TEST_WALLET = "HiA7DqQ9xpAEVQ7Vu8kA3fqWHYrKrc78xx3FTjGbfgJU";

    const result = await this.validateJupiterTransaction(
      SOL_MINT,
      USDC_MINT,
      TEST_AMOUNT,
      TEST_WALLET
    );

    if (result.overall) {
      console.log(`✅ JUPITER API РАБОТАЕТ КОРРЕКТНО!`);
    } else {
      console.log(`❌ JUPITER API НЕ РАБОТАЕТ!`);
    }

    return result;
  }
}

module.exports = { JupiterTransactionValidator };
