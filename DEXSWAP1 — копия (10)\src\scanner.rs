use anyhow::Result;
use log::{info, warn, error, debug};
use solana_client::rpc_client::RpcClient;
use solana_sdk::pubkey::Pubkey;
use std::collections::HashMap;
use std::str::FromStr;
use std::sync::Arc;
use tokio::sync::Mutex;
use rayon::prelude::*;

use crate::config::Config;

// Добавляем недостающий импорт для uuid
use uuid;

#[derive(Debug, Clone)]
pub struct Vulnerability {
    pub id: String,
    pub program_id: String,
    pub vulnerability_type: VulnerabilityType,
    pub severity: String,
    pub description: String,
    pub estimated_reward: u64,
    pub confidence: f64,
    pub proof_of_concept: Option<String>,
    pub discovered_at: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug, Clone)]
pub enum VulnerabilityType {
    Reentrancy,
    IntegerOverflow,
    ValidationBypass,
    MemoryCorruption,
    AccessControl,
    LogicError,
    BufferOverflow,
    UnauthorizedAccess,
}

pub struct VulnerabilityScanner {
    rpc_client: RpcClient,
    config: Config,
    known_programs: Arc<Mutex<HashMap<String, ProgramInfo>>>,
    scan_results: Arc<Mutex<Vec<Vulnerability>>>,
}

#[derive(Debug, Clone)]
struct ProgramInfo {
    program_id: Pubkey,
    name: String,
    category: String,
    tvl: Option<u64>,
    last_updated: chrono::DateTime<chrono::Utc>,
}

impl VulnerabilityScanner {
    pub async fn new(config: Config) -> Result<Self> {
        let rpc_client = RpcClient::new(config.rpc_url.clone());
        
        Ok(Self {
            rpc_client,
            config,
            known_programs: Arc::new(Mutex::new(HashMap::new())),
            scan_results: Arc::new(Mutex::new(Vec::new())),
        })
    }
    
    pub async fn scan_all_programs(&self) -> Result<Vec<Vulnerability>> {
        info!("🔍 Starting comprehensive program scanning");
        
        // Загружаем список всех известных программ
        self.load_known_programs().await?;
        
        let programs = self.known_programs.lock().await;
        let program_list: Vec<ProgramInfo> = programs.values().cloned().collect();
        drop(programs);
        
        info!("📊 Found {} programs to scan", program_list.len());
        
        // Сканируем программы параллельно
        let vulnerabilities: Vec<Vulnerability> = program_list
            .par_iter()
            .map(|program| {
                tokio::runtime::Handle::current().block_on(async {
                    self.scan_single_program(program).await
                })
            })
            .filter_map(|result| result.ok())
            .flatten()
            .collect();
        
        info!("✅ Scanning completed. Found {} vulnerabilities", vulnerabilities.len());
        
        Ok(vulnerabilities)
    }
    
    async fn load_known_programs(&self) -> Result<()> {
        info!("📚 Loading known Solana programs database");
        
        let mut programs = self.known_programs.lock().await;
        
        // Основные DeFi протоколы
        let defi_programs = vec![
            ("JUP4Fb2cqiRUcaTHdrPC8h2gNsA2ETXiPDD33WcGuJB", "Jupiter", "DEX Aggregator"),
            ("9xQeWvG816bUx9EPjHmaT23yvVM2ZWbrrpZb9PusVFin", "Serum", "DEX"),
            ("675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8", "Raydium", "AMM"),
            ("whirLbMiicVdio4qvUfM5KAg6Ct8VwpYzGff3uctyCc", "Orca", "AMM"),
            ("LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo", "Meteora", "DLMM"),
            ("MarBmsSgKXdrN1egZf5sqe1TMai9K1rChYNDJgjq7aD", "Marinade", "Liquid Staking"),
            ("mv3ekLzLbnVPNxjSKvqBpU3ZeZXPQdEC3bp5MDEBG68", "Mango", "Margin Trading"),
            ("Eo7WjKq67rjJQSZxS6z3YkapzY3eMj6Xy8X5EQVn5UaB", "Meteora Dynamic AMM", "AMM"),
        ];
        
        // SPL программы
        let spl_programs = vec![
            ("TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA", "SPL Token", "Token Standard"),
            ("ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL", "Associated Token", "Token Accounts"),
            ("Memo1UhkJRfHyvLMcVucJwxXeuD728EqVDDwQDxFMNo", "SPL Memo", "Memo Program"),
        ];
        
        // Системные программы
        let system_programs = vec![
            ("11111111111111111111111111111111", "System Program", "System"),
            ("Vote111111111111111111111111111111111111111", "Vote Program", "Consensus"),
            ("Stake11111111111111111111111111111111111111", "Stake Program", "Staking"),
            ("BPFLoaderUpgradeab1e11111111111111111111111", "BPF Loader", "Program Loader"),
        ];
        
        // Добавляем все программы в базу
        for (program_id_str, name, category) in defi_programs.into_iter()
            .chain(spl_programs.into_iter())
            .chain(system_programs.into_iter()) {
            
            if let Ok(program_id) = Pubkey::from_str(program_id_str) {
                programs.insert(program_id_str.to_string(), ProgramInfo {
                    program_id,
                    name: name.to_string(),
                    category: category.to_string(),
                    tvl: None,
                    last_updated: chrono::Utc::now(),
                });
            }
        }
        
        // Автоматически обнаруживаем дополнительные программы
        self.discover_additional_programs(&mut programs).await?;
        
        info!("✅ Loaded {} programs for scanning", programs.len());
        
        Ok(())
    }
    
    async fn discover_additional_programs(&self, programs: &mut HashMap<String, ProgramInfo>) -> Result<()> {
        info!("🔍 Auto-discovering additional programs");
        
        // Получаем последние блоки и анализируем транзакции
        let recent_blockhash = self.rpc_client.get_latest_blockhash().await?;
        let slot = self.rpc_client.get_slot().await?;
        
        // Анализируем последние 100 слотов
        for i in 0..100 {
            if let Ok(block) = self.rpc_client.get_block(slot - i).await {
                if let Some(transactions) = block.transactions {
                    for tx in transactions {
                        if let Some(transaction) = tx.transaction {
                            for instruction in transaction.message.instructions {
                                let program_id = transaction.message.account_keys
                                    .get(instruction.program_id_index as usize);
                                
                                if let Some(pubkey) = program_id {
                                    let program_id_str = pubkey.to_string();
                                    
                                    if !programs.contains_key(&program_id_str) {
                                        // Новая программа найдена
                                        programs.insert(program_id_str.clone(), ProgramInfo {
                                            program_id: *pubkey,
                                            name: format!("Unknown Program {}", &program_id_str[..8]),
                                            category: "Unknown".to_string(),
                                            tvl: None,
                                            last_updated: chrono::Utc::now(),
                                        });
                                        
                                        debug!("🆕 Discovered new program: {}", program_id_str);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        
        Ok(())
    }
    
    async fn scan_single_program(&self, program: &ProgramInfo) -> Result<Vec<Vulnerability>> {
        debug!("🔍 Scanning program: {} ({})", program.name, program.program_id);
        
        let mut vulnerabilities = Vec::new();
        
        // Получаем данные программы
        let account = match self.rpc_client.get_account(&program.program_id).await {
            Ok(account) => account,
            Err(e) => {
                warn!("Failed to get account for {}: {}", program.program_id, e);
                return Ok(vulnerabilities);
            }
        };
        
        // Анализируем байт-код программы
        if let Some(vuln) = self.analyze_bytecode(&account.data, program).await? {
            vulnerabilities.push(vuln);
        }
        
        // Проверяем известные паттерны уязвимостей
        vulnerabilities.extend(self.check_vulnerability_patterns(&account.data, program).await?);
        
        // Анализируем права доступа
        if let Some(vuln) = self.analyze_access_control(program).await? {
            vulnerabilities.push(vuln);
        }
        
        // Проверяем на переполнения
        vulnerabilities.extend(self.check_overflow_patterns(&account.data, program).await?);
        
        // Анализируем логику программы
        vulnerabilities.extend(self.analyze_program_logic(&account.data, program).await?);
        
        if !vulnerabilities.is_empty() {
            info!("🚨 Found {} vulnerabilities in {}", vulnerabilities.len(), program.name);
        }
        
        Ok(vulnerabilities)
    }
    
    async fn analyze_bytecode(&self, bytecode: &[u8], program: &ProgramInfo) -> Result<Option<Vulnerability>> {
        // Анализ байт-кода на предмет подозрительных паттернов
        
        // Проверяем на небезопасные syscalls
        let dangerous_syscalls = [
            &[0x48, 0x89, 0xe5], // mov %rsp, %rbp
            &[0x0f, 0x05],       // syscall
            &[0xff, 0xe0],       // jmp %rax
        ];
        
        for pattern in &dangerous_syscalls {
            if self.find_pattern_in_bytes(bytecode, pattern) {
                return Ok(Some(Vulnerability {
                    id: uuid::Uuid::new_v4().to_string(),
                    program_id: program.program_id.to_string(),
                    vulnerability_type: VulnerabilityType::MemoryCorruption,
                    severity: "High".to_string(),
                    description: "Dangerous syscall pattern detected in bytecode".to_string(),
                    estimated_reward: 100_000,
                    confidence: 0.75,
                    proof_of_concept: None,
                    discovered_at: chrono::Utc::now(),
                }));
            }
        }
        
        Ok(None)
    }
    
    async fn check_vulnerability_patterns(&self, bytecode: &[u8], program: &ProgramInfo) -> Result<Vec<Vulnerability>> {
        let mut vulnerabilities = Vec::new();
        
        // Паттерны reentrancy
        let reentrancy_patterns = [
            b"invoke_signed",
            b"invoke",
            b"cross_program",
        ];
        
        for pattern in &reentrancy_patterns {
            if self.find_pattern_in_bytes(bytecode, pattern) {
                vulnerabilities.push(Vulnerability {
                    id: uuid::Uuid::new_v4().to_string(),
                    program_id: program.program_id.to_string(),
                    vulnerability_type: VulnerabilityType::Reentrancy,
                    severity: "Critical".to_string(),
                    description: "Potential reentrancy vulnerability detected".to_string(),
                    estimated_reward: 500_000,
                    confidence: 0.65,
                    proof_of_concept: None,
                    discovered_at: chrono::Utc::now(),
                });
            }
        }
        
        // Паттерны integer overflow
        let overflow_patterns = [
            b"checked_add",
            b"checked_sub", 
            b"checked_mul",
            b"wrapping_add",
            b"wrapping_sub",
        ];
        
        let has_safe_arithmetic = overflow_patterns.iter()
            .any(|pattern| self.find_pattern_in_bytes(bytecode, pattern));
        
        if !has_safe_arithmetic && bytecode.len() > 1000 {
            vulnerabilities.push(Vulnerability {
                id: uuid::Uuid::new_v4().to_string(),
                program_id: program.program_id.to_string(),
                vulnerability_type: VulnerabilityType::IntegerOverflow,
                severity: "High".to_string(),
                description: "No safe arithmetic operations detected".to_string(),
                estimated_reward: 250_000,
                confidence: 0.55,
                proof_of_concept: None,
                discovered_at: chrono::Utc::now(),
            });
        }
        
        Ok(vulnerabilities)
    }
    
    async fn analyze_access_control(&self, program: &ProgramInfo) -> Result<Option<Vulnerability>> {
        // Анализируем права доступа к программе
        
        let account = self.rpc_client.get_account(&program.program_id).await?;
        
        // Проверяем, является ли программа исполняемой
        if !account.executable {
            return Ok(Some(Vulnerability {
                id: uuid::Uuid::new_v4().to_string(),
                program_id: program.program_id.to_string(),
                vulnerability_type: VulnerabilityType::AccessControl,
                severity: "Medium".to_string(),
                description: "Program is not marked as executable".to_string(),
                estimated_reward: 50_000,
                confidence: 0.90,
                proof_of_concept: None,
                discovered_at: chrono::Utc::now(),
            }));
        }
        
        Ok(None)
    }
    
    async fn check_overflow_patterns(&self, bytecode: &[u8], program: &ProgramInfo) -> Result<Vec<Vulnerability>> {
        let mut vulnerabilities = Vec::new();
        
        // Ищем потенциально опасные арифметические операции
        let arithmetic_opcodes = [
            0x01, // ADD
            0x29, // SUB  
            0x0f, 0xaf, // IMUL
        ];
        
        for i in 0..bytecode.len().saturating_sub(2) {
            if arithmetic_opcodes.contains(&bytecode[i]) {
                // Проверяем, есть ли проверка переполнения рядом
                let has_overflow_check = (i.saturating_sub(10)..=i.saturating_add(10))
                    .any(|j| j < bytecode.len() && 
                         (bytecode.get(j..j+4) == Some(b"chec") || 
                          bytecode.get(j..j+4) == Some(b"wrap")));
                
                if !has_overflow_check {
                    vulnerabilities.push(Vulnerability {
                        id: uuid::Uuid::new_v4().to_string(),
                        program_id: program.program_id.to_string(),
                        vulnerability_type: VulnerabilityType::IntegerOverflow,
                        severity: "High".to_string(),
                        description: format!("Unchecked arithmetic operation at offset {}", i),
                        estimated_reward: 200_000,
                        confidence: 0.70,
                        proof_of_concept: None,
                        discovered_at: chrono::Utc::now(),
                    });
                }
            }
        }
        
        Ok(vulnerabilities)
    }
    
    async fn analyze_program_logic(&self, bytecode: &[u8], program: &ProgramInfo) -> Result<Vec<Vulnerability>> {
        let mut vulnerabilities = Vec::new();
        
        // Анализируем логические ошибки
        
        // Проверяем на отсутствие проверок владельца
        let has_owner_check = self.find_pattern_in_bytes(bytecode, b"owner") ||
                             self.find_pattern_in_bytes(bytecode, b"signer");
        
        if !has_owner_check && program.category == "DeFi" {
            vulnerabilities.push(Vulnerability {
                id: uuid::Uuid::new_v4().to_string(),
                program_id: program.program_id.to_string(),
                vulnerability_type: VulnerabilityType::ValidationBypass,
                severity: "Critical".to_string(),
                description: "No owner/signer validation detected in DeFi program".to_string(),
                estimated_reward: 750_000,
                confidence: 0.80,
                proof_of_concept: None,
                discovered_at: chrono::Utc::now(),
            });
        }
        
        Ok(vulnerabilities)
    }
    
    fn find_pattern_in_bytes(&self, haystack: &[u8], needle: &[u8]) -> bool {
        haystack.windows(needle.len()).any(|window| window == needle)
    }
}
