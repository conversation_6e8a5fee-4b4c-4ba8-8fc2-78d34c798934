#!/usr/bin/env node

/**
 * 🎯 UNIFIED CACHE MANAGER
 *
 * Единая централизованная система кеширования для всех компонентов
 * Решает проблему дублирования кеширования в 3-4 местах
 */

class UnifiedCacheManager {
  constructor() {
    // Singleton pattern
    if (UnifiedCacheManager.instance) {
      return UnifiedCacheManager.instance;
    }

    // Типизированные кеши с разными TTL
    this.caches = {
      // Jupiter quotes - УВЕЛИЧЕННЫЙ TTL для стабильности
      jupiterQuotes: {
        data: new Map(),
        ttl: 10000, // 🔥 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: 10 секунд вместо 1 секунды!
        stats: { hits: 0, misses: 0, sets: 0 }
      },

      // Цены токенов - средний TTL
      tokenPrices: {
        data: new Map(),
        ttl: 5000, // 5 секунд для цен токенов
        stats: { hits: 0, misses: 0, sets: 0 }
      },

      // RPC данные - длинный TTL
      rpcData: {
        data: new Map(),
        ttl: 30000, // 30 секунд для RPC данных
        stats: { hits: 0, misses: 0, sets: 0 }
      },

      // Account info - средний TTL
      accountInfo: {
        data: new Map(),
        ttl: 10000, // 10 секунд для информации об аккаунтах
        stats: { hits: 0, misses: 0, sets: 0 }
      },

      // Pool data - очень короткий TTL для арбитража
      poolData: {
        data: new Map(),
        ttl: 500, // 500мс для данных пулов (критично для арбитража)
        stats: { hits: 0, misses: 0, sets: 0 }
      }
    };

    // Общая статистика
    this.globalStats = {
      totalHits: 0,
      totalMisses: 0,
      totalSets: 0,
      startTime: Date.now()
    };

    // Автоочистка устаревших записей каждые 30 секунд
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 30000);

    UnifiedCacheManager.instance = this;
    console.log('🎯 UnifiedCacheManager инициализирован');
  }

  /**
   * 📊 ПОЛУЧЕНИЕ ДАННЫХ ИЗ КЕША
   */
  get(cacheType, key) {
    const cache = this.caches[cacheType];
    if (!cache) {
      throw new Error(`Неизвестный тип кеша: ${cacheType}`);
    }

    const cached = cache.data.get(key);

    if (cached && (Date.now() - cached.timestamp) < cache.ttl) {
      // Cache hit
      cache.stats.hits++;
      this.globalStats.totalHits++;
      return cached.value;
    }

    // Cache miss
    cache.stats.misses++;
    this.globalStats.totalMisses++;

    // Удаляем устаревшую запись
    if (cached) {
      cache.data.delete(key);
    }

    return null;
  }

  /**
   * 💾 СОХРАНЕНИЕ ДАННЫХ В КЕШ
   */
  set(cacheType, key, value) {
    const cache = this.caches[cacheType];
    if (!cache) {
      throw new Error(`Неизвестный тип кеша: ${cacheType}`);
    }

    cache.data.set(key, {
      value,
      timestamp: Date.now()
    });

    cache.stats.sets++;
    this.globalStats.totalSets++;
  }

  /**
   * 🗑️ УДАЛЕНИЕ ИЗ КЕША
   */
  delete(cacheType, key) {
    const cache = this.caches[cacheType];
    if (!cache) {
      throw new Error(`Неизвестный тип кеша: ${cacheType}`);
    }

    return cache.data.delete(key);
  }

  /**
   * 🧹 ОЧИСТКА УСТАРЕВШИХ ЗАПИСЕЙ
   */
  cleanup() {
    let totalCleaned = 0;
    const now = Date.now();

    Object.entries(this.caches).forEach(([cacheType, cache]) => {
      let cleaned = 0;

      for (const [key, entry] of cache.data.entries()) {
        if (now - entry.timestamp >= cache.ttl) {
          cache.data.delete(key);
          cleaned++;
        }
      }

      totalCleaned += cleaned;

      if (cleaned > 0) {
        console.log(`🧹 Очищено ${cleaned} записей из ${cacheType} кеша`);
      }
    });

    if (totalCleaned > 0) {
      console.log(`🧹 Общая очистка: ${totalCleaned} устаревших записей`);
    }
  }

  /**
   * 🧹 АЛИАС ДЛЯ СОВМЕСТИМОСТИ - ОЧИСТКА УСТАРЕВШИХ ЗАПИСЕЙ
   */
  clearExpiredEntries() {
    return this.cleanup();
  }

  /**
   * 📊 ПОЛУЧЕНИЕ СТАТИСТИКИ
   */
  getStats() {
    const cacheStats = {};

    Object.entries(this.caches).forEach(([cacheType, cache]) => {
      const total = cache.stats.hits + cache.stats.misses;
      const hitRate = total > 0 ? (cache.stats.hits / total * 100).toFixed(2) : 0;

      cacheStats[cacheType] = {
        size: cache.data.size,
        hits: cache.stats.hits,
        misses: cache.stats.misses,
        sets: cache.stats.sets,
        hitRate: `${hitRate}%`,
        ttl: `${cache.ttl}ms`
      };
    });

    const globalTotal = this.globalStats.totalHits + this.globalStats.totalMisses;
    const globalHitRate = globalTotal > 0 ?
      (this.globalStats.totalHits / globalTotal * 100).toFixed(2) : 0;

    return {
      caches: cacheStats,
      global: {
        ...this.globalStats,
        hitRate: `${globalHitRate}%`,
        uptime: `${Math.round((Date.now() - this.globalStats.startTime) / 1000)}s`
      }
    };
  }

  /**
   * 🔧 СПЕЦИАЛИЗИРОВАННЫЕ МЕТОДЫ ДЛЯ JUPITER QUOTES
   * ⚠️ ВНИМАНИЕ: ПО ОФИЦИАЛЬНОЙ ДОКУМЕНТАЦИИ НЕ РЕКОМЕНДУЕТСЯ КЭШИРОВАТЬ JUPITER КОТИРОВКИ!
   */
  getJupiterQuote(inputMint, outputMint, amount) {
    // 🔇 УБИРАЕМ МУСОРНЫЕ ПРЕДУПРЕЖДЕНИЯ!

    // 🔥 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: УНИВЕРСАЛЬНЫЙ ПОИСК КОТИРОВОК!
    // Пробуем ВСЕ возможные варианты ключей

    const SOL_MINT = 'So11111111111111111111111111111111111111112';
    const USDC_MINT = 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v';
    const USDT_MINT = 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB';

    console.log(`🔍 UnifiedCacheManager.getJupiterQuote: УНИВЕРСАЛЬНЫЙ ПОИСК`);
    console.log(`   input: ${inputMint.slice(0,8)}...`);
    console.log(`   output: ${outputMint.slice(0,8)}...`);
    console.log(`   amount: ${amount}`);

    // Список всех возможных вариантов поиска
    const searchVariants = [
      // Точное совпадение
      `${inputMint}_${outputMint}_${amount}`,

      // С amount=1000000000 (как сохраняет jupiter-api-client.js)
      `${inputMint}_${outputMint}_1000000000`,

      // Обратный порядок токенов
      `${outputMint}_${inputMint}_${amount}`,
      `${outputMint}_${inputMint}_1000000000`,

      // SOL→USDC/USDT направления (как сохраняет основная система)
      `${SOL_MINT}_${USDC_MINT}_1000000000`,
      `${SOL_MINT}_${USDT_MINT}_1000000000`,

      // Все возможные комбинации для SOL/USDC
      `${SOL_MINT}_${USDC_MINT}_${amount}`,
      `${USDC_MINT}_${SOL_MINT}_${amount}`,
      `${USDC_MINT}_${SOL_MINT}_1000000000`,

      // Все возможные комбинации для SOL/USDT
      `${SOL_MINT}_${USDT_MINT}_${amount}`,
      `${USDT_MINT}_${SOL_MINT}_${amount}`,
      `${USDT_MINT}_${SOL_MINT}_1000000000`
    ];

    // Пробуем каждый вариант
    for (let i = 0; i < searchVariants.length; i++) {
      const key = searchVariants[i];
      const result = this.get('jupiterQuotes', key);

      if (result) {
        console.log(`✅ НАЙДЕНА котировка по варианту ${i + 1}: ${key.slice(0,50)}...`);
        return result;
      }
    }

    // Если ничего не найдено, показываем отладку
    const allKeys = Array.from(this.caches.jupiterQuotes.data.keys());
    console.log(`❌ Котировка НЕ НАЙДЕНА! Доступные ключи в кэше (${allKeys.length}):`);
    allKeys.slice(0, 5).forEach((k, i) => console.log(`     ${i + 1}. ${k.slice(0,60)}...`));
    if (allKeys.length > 5) {
      console.log(`     ... и еще ${allKeys.length - 5} ключей`);
    }

    return null;
  }

  setJupiterQuote(inputMint, outputMint, amount, quote) {
    // 🔇 УБИРАЕМ МУСОРНЫЕ ПРЕДУПРЕЖДЕНИЯ!

    const key = `${inputMint}_${outputMint}_${amount}`;

    // 🔇 УБИРАЕМ МУСОРНЫЕ ЛОГИ КОТИРОВОК - СОХРАНЯЕМ МОЛЧА!
    // console.log(`💾 UnifiedCacheManager.setJupiterQuote:`);
    // console.log(`   Ключ: ${key.slice(0,50)}...`);
    // console.log(`   Quote: ${quote ? 'ЕСТЬ' : 'НЕТ'}`);
    // console.log(`   outAmount: ${quote?.outAmount || 'N/A'}`);

    this.set('jupiterQuotes', key, quote);

    // 🔇 УБИРАЕМ ПРОВЕРКУ СОХРАНЕНИЯ
    // const saved = this.get('jupiterQuotes', key);
    // console.log(`   Проверка сохранения: ${saved ? 'УСПЕШНО' : 'ПРОВАЛ'}`);
  }

  /**
   * 🔧 СПЕЦИАЛИЗИРОВАННЫЕ МЕТОДЫ ДЛЯ ЦЕН ТОКЕНОВ
   */
  getTokenPrice(tokenMints) {
    const key = Array.isArray(tokenMints) ? tokenMints.join(',') : tokenMints;
    return this.get('tokenPrices', key);
  }

  setTokenPrice(tokenMints, prices) {
    const key = Array.isArray(tokenMints) ? tokenMints.join(',') : tokenMints;
    this.set('tokenPrices', key, prices);
  }

  /**
   * 🔧 СПЕЦИАЛИЗИРОВАННЫЕ МЕТОДЫ ДЛЯ RPC ДАННЫХ
   */
  getRPCData(method, params) {
    const key = `${method}_${JSON.stringify(params)}`;
    return this.get('rpcData', key);
  }

  setRPCData(method, params, data) {
    const key = `${method}_${JSON.stringify(params)}`;
    this.set('rpcData', key, data);
  }

  /**
   * 🔧 СПЕЦИАЛИЗИРОВАННЫЕ МЕТОДЫ ДЛЯ ACCOUNT INFO
   */
  getAccountInfo(address) {
    return this.get('accountInfo', address);
  }

  setAccountInfo(address, info) {
    this.set('accountInfo', address, info);
  }

  /**
   * 🔧 СПЕЦИАЛИЗИРОВАННЫЕ МЕТОДЫ ДЛЯ ДАННЫХ ПУЛОВ
   */
  getPoolData(poolAddress, customTTL = null) {
    if (customTTL) {
      // Временно изменяем TTL для этого запроса
      const originalTTL = this.caches.poolData.ttl;
      this.caches.poolData.ttl = customTTL;
      const result = this.get('poolData', poolAddress);
      this.caches.poolData.ttl = originalTTL;
      return result;
    }
    return this.get('poolData', poolAddress);
  }

  setPoolData(poolAddress, data, customTTL = null) {
    if (customTTL) {
      // Сохраняем с кастомным TTL
      this.caches.poolData.data.set(poolAddress, {
        value: data,
        timestamp: Date.now(),
        customTTL: customTTL
      });
      this.caches.poolData.stats.sets++;
      this.globalStats.totalSets++;
    } else {
      this.set('poolData', poolAddress, data);
    }
  }

  /**
   * 🎯 ИНВАЛИДАЦИЯ СВЯЗАННЫХ ДАННЫХ
   */
  invalidateRelated(cacheType, pattern) {
    const cache = this.caches[cacheType];
    if (!cache) return;

    let invalidated = 0;

    for (const key of cache.data.keys()) {
      if (key.includes(pattern)) {
        cache.data.delete(key);
        invalidated++;
      }
    }

    if (invalidated > 0) {
      console.log(`🔄 Инвалидировано ${invalidated} записей в ${cacheType} по паттерну: ${pattern}`);
    }
  }

  /**
   * 🧹 ПОЛНАЯ ОЧИСТКА КЕША
   */
  clear(cacheType = null) {
    if (cacheType) {
      const cache = this.caches[cacheType];
      if (cache) {
        const size = cache.data.size;
        cache.data.clear();
        console.log(`🧹 Очищен кеш ${cacheType}: ${size} записей`);
      }
    } else {
      // Очищаем все кеши
      Object.entries(this.caches).forEach(([type, cache]) => {
        const size = cache.data.size;
        cache.data.clear();
        console.log(`🧹 Очищен кеш ${type}: ${size} записей`);
      });
    }
  }

  /**
   * 🛑 ОСТАНОВКА МЕНЕДЖЕРА
   */
  destroy() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }

    this.clear(); // Очищаем все кеши
    UnifiedCacheManager.instance = null;
    console.log('🛑 UnifiedCacheManager остановлен');
  }
}

// Экспорт singleton instance
module.exports = new UnifiedCacheManager();
