#!/usr/bin/env node

/**
 * 🔥 ЗАПУСК РЕВОЛЮЦИОННОЙ ТОРГОВОЙ СИСТЕМЫ
 * 
 * Этот скрипт запускает революционную архитектуру с:
 * - Custom Flash Loan System (0% комиссия)
 * - Low-Level MarginFi Integration (обход ограничений)
 * - Native Meteora DLMM Integration (прямой доступ)
 * - Stealth режим (невидимость для защитных механизмов)
 */

console.log('🔥 РЕВОЛЮЦИОННАЯ ТОРГОВАЯ СИСТЕМА ЗАПУСКАЕТСЯ...'.red.bold);
console.log('🚀 Custom Flash Loan System активна');
console.log('🛡️ Stealth режим включен');
console.log('💰 0% комиссия Flash Loan');
console.log('📅 Время запуска:', new Date().toISOString());

const colors = require('colors');

// Проверяем аргументы командной строки
const args = process.argv.slice(2);
const mode = args[0] || 'meteora';

console.log(`🎯 Режим запуска: ${mode}`.cyan);

switch (mode.toLowerCase()) {
    case 'meteora':
    case 'm':
        console.log('🌪️ Запуск Meteora Revolutionary Mode...'.yellow);
        require('./BMeteora.js');
        break;
        
    case 'main':
    case 'full':
    case 'f':
        console.log('🚀 Запуск Full Revolutionary System...'.green);
        // Устанавливаем флаг революционного режима
        process.env.REVOLUTIONARY_MODE = 'true';
        require('./real-solana-rpc-websocket.js');
        break;
        
    case 'test':
    case 't':
        console.log('🧪 Запуск Revolutionary Test Mode...'.blue);
        require('./test-revolutionary-arbitrage.js');
        break;
        
    default:
        console.log('❌ Неизвестный режим!'.red);
        console.log('📖 Доступные режимы:');
        console.log('   meteora, m    - Meteora Revolutionary Mode');
        console.log('   main, full, f - Full Revolutionary System');
        console.log('   test, t       - Revolutionary Test Mode');
        console.log('');
        console.log('🔥 Примеры запуска:');
        console.log('   node start-revolutionary-trading.js meteora');
        console.log('   node start-revolutionary-trading.js main');
        console.log('   node start-revolutionary-trading.js test');
        process.exit(1);
}
