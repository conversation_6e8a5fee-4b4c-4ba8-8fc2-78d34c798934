@echo off
echo 🛑 ЗАПУСК БЕЗОПАСНОГО БОТА БЕЗ РЕАЛЬНЫХ ТРАНЗАКЦИЙ!
echo ═══════════════════════════════════════════════════════════════
echo 🧪 РЕЖИМ: Только симуляция и тестирование
echo 🛑 РЕАЛЬНЫЕ ТРАНЗАКЦИИ: Полностью отключены
echo ✅ БЕЗОПАСНОСТЬ: Максимальная
echo ═══════════════════════════════════════════════════════════════
echo.

echo 🔧 Останавливаем все существующие процессы Node.js...
taskkill /F /IM node.exe >nul 2>&1

echo ✅ Все процессы остановлены!
echo.

echo 🚀 Запускаем безопасную версию бота...
node test-bot-safe.js

pause
