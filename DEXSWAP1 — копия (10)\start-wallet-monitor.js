/**
 * 🚀 БЫСТРЫЙ ЗАПУСК WALLET MONITOR
 * ═══════════════════════════════════
 * 
 * Простой скрипт для запуска мониторинга кошелька
 */

const AdvancedWalletMonitor = require('./advanced-wallet-monitor');

console.log('🔥 ЗАПУСК WALLET MONITOR...'.red.bold);
console.log('═'.repeat(50));

// Твой кошелек из скриншота
const WALLET_ADDRESS = 'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV';

async function startMonitoring() {
    try {
        console.log(`🎯 Мониторинг кошелька: ${WALLET_ADDRESS}`.cyan);
        console.log(`📡 Подключение к Solana WebSocket...`.yellow);
        
        const monitor = new AdvancedWalletMonitor(WALLET_ADDRESS);
        await monitor.start();
        
        console.log('\n✅ МОНИТОРИНГ ЗАПУЩЕН!'.green.bold);
        console.log('🔔 Теперь вы будете получать уведомления о всех транзакциях в реальном времени!'.green);
        console.log('\n💡 Команды:'.yellow);
        console.log('   Ctrl+C - остановить мониторинг'.gray);
        
        // Обработка выхода
        process.on('SIGINT', async () => {
            console.log('\n🛑 Остановка мониторинга...'.red);
            await monitor.stop();
            console.log('👋 До свидания!'.yellow);
            process.exit(0);
        });
        
        // Держим процесс активным
        process.stdin.resume();
        
    } catch (error) {
        console.error('❌ Ошибка запуска мониторинга:', error);
        process.exit(1);
    }
}

// Запуск
startMonitoring();
