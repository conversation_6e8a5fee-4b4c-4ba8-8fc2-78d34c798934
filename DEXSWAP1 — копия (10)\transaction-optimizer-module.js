#!/usr/bin/env node

/**
 * 🔧 TRANSACTION OPTIMIZER MODULE
 *
 * Главный модуль для сборки и оптимизации транзакций.
 * Извлечен из duplicate-instruction-fixer.js для использования в BMeteora.js
 * 
 * ФУНКЦИИ:
 * - Сборка оптимизированных транзакций
 * - Удаление дубликатов инструкций
 * - Анализ размера транзакций
 * - RPC симуляция
 */

const { Connection, Keypair, AddressLookupTableAccount } = require('@solana/web3.js');
const bs58 = require('bs58');

// 🔧 ИМПОРТ МОДУЛЕЙ
const TransactionBuilderModule = require('./transaction-builder-module.js');

// 🔍 ИМПОРТ ДИАГНОСТИЧЕСКИХ ИНСТРУМЕНТОВ
const TransactionSizeDiagnostic = require('./transaction-size-diagnostic.js');
const CompleteFlashLoanWithLiquidity = require('./complete-flash-loan-with-liquidity.js');

require('dotenv').config();

class TransactionOptimizerModule {
    constructor(connection = null, wallet = null, binCacheManager = null, parentBot = null) {
        // 🌐 CONNECTION К SOLANA
        this.connection = connection || new Connection(
            process.env.QUICKNODE_RPC_URL || process.env.SOLANA_RPC_URL,
            'confirmed'
        );

        // 🔑 WALLET
        this.wallet = wallet;

        // 🚀 BIN CACHE MANAGER (ПЕРЕДАЕТСЯ ИЗ BMETEORA.js)
        this.binCacheManager = binCacheManager;

        // 🚀 PARENT BOT ДЛЯ КЭШИРОВАННОГО BLOCKHASH
        this.parentBot = parentBot;

        // 🔧 МОДУЛЬ СБОРКИ ТРАНЗАКЦИЙ
        this.transactionBuilder = null;

        // 🔥 ПОЛНАЯ СИСТЕМА ДЛЯ ТЕСТИРОВАНИЯ
        this.flashLoanSystem = null;

        // 🔍 ДИАГНОСТИЧЕСКИЕ ИНСТРУМЕНТЫ
        this.diagnostic = new TransactionSizeDiagnostic();

        console.log('🔧 Transaction Optimizer Module инициализирован');
        if (this.binCacheManager) {
            console.log('✅ BinCacheManager передан в TransactionOptimizerModule');
        } else {
            console.log('⚠️ BinCacheManager НЕ передан в TransactionOptimizerModule');
        }
    }

    /**
     * 🔑 ИНИЦИАЛИЗАЦИЯ МОДУЛЯ
     */
    async initialize(wallet = null) {
        console.log('\n🔑 ИНИЦИАЛИЗАЦИЯ TRANSACTION OPTIMIZER...');

        // Если wallet не передан, создаем из env
        if (!this.wallet && !wallet) {
            const privateKeyBytes = bs58.default.decode(process.env.WALLET_PRIVATE_KEY);
            this.wallet = Keypair.fromSecretKey(privateKeyBytes);
        } else if (wallet) {
            this.wallet = wallet;
        }

        console.log(`✅ Кошелек: ${this.wallet.publicKey.toString()}`);

        // 🔧 ИНИЦИАЛИЗАЦИЯ TRANSACTION BUILDER
        this.transactionBuilder = new TransactionBuilderModule();
        await this.transactionBuilder.initialize(this.wallet);
        console.log('✅ Transaction Builder инициализирован');

        // 🔥 ИНИЦИАЛИЗАЦИЯ FLASH LOAN СИСТЕМЫ С BIN CACHE MANAGER!
        this.flashLoanSystem = new CompleteFlashLoanWithLiquidity(this.connection, this.binCacheManager);
        await this.flashLoanSystem.initialize(this.wallet);
        console.log('✅ Flash Loan система инициализирована С BIN CACHE MANAGER!');

        console.log('🚀 Transaction Optimizer готов к работе!');
    }

    /**
     * 🔥 ГЛАВНАЯ ФУНКЦИЯ: СОЗДАНИЕ DUAL BANK FLASH LOAN ТРАНЗАКЦИИ
     * Это основная функция которую будет вызывать BMeteora.js
     * НИКАКИХ FALLBACK - ТОЛЬКО DUAL BANK FLASH LOAN!
     */
    async createOptimizedTransaction(arbitrageParams = null) {
        console.log('\n🔥 СОЗДАНИЕ DUAL BANK FLASH LOAN ТРАНЗАКЦИИ...');
        console.log('🚫 НИКАКИХ FALLBACK - ТОЛЬКО DUAL BANK СИСТЕМА!');

        try {
            // 🔥 ПАРАМЕТРЫ DUAL BANK FLASH LOAN (ТЕСТОВЫЕ СУММЫ КАК В УСПЕШНОЙ ТРАНЗАКЦИИ!)
            const dualBankParams = arbitrageParams || {
                usdcAmount: 25000 * 1e6,    // $25,000 USDC (тестовая сумма)
                solAmount: 140 * 1e9,       // 140 SOL (как в успешной транзакции)
                targetPosition: 20000,      // $20K позиция
                solPrice: 183,              // $183 за SOL
                additionalCosts: 100        // $100 дополнительные расходы
            };

            console.log(`💰 DUAL BANK FLASH LOAN ПАРАМЕТРЫ:`);
            console.log(`   USDC займ: $${(dualBankParams.usdcAmount / 1e6).toLocaleString()}`);
            console.log(`   SOL займ: ${(dualBankParams.solAmount / 1e9).toLocaleString()} SOL`);
            console.log(`   Целевая позиция: $${dualBankParams.targetPosition.toLocaleString()}`);

            // 🔥 СОЗДАЕМ DUAL BANK FLASH LOAN ТРАНЗАКЦИЮ
            console.log('🔥 Сборка Dual Bank Flash Loan транзакции...');
            const result = await this.createDualBankFlashLoanTransaction(dualBankParams);

            if (!result.success) {
                throw new Error(`Не удалось создать dual bank транзакцию: ${result.error}`);
            }

            // 🔧 БЕЗОПАСНОЕ ПОЛУЧЕНИЕ ИНСТРУКЦИЙ И ALT ТАБЛИЦ
            const originalInstructions = result.instructions || [];
            const rawAltTables = result.altTables || [];

            // 🚨 ПРОВЕРЯЕМ ЧТО ИНСТРУКЦИИ ПОЛУЧЕНЫ
            if (!originalInstructions || !Array.isArray(originalInstructions) || originalInstructions.length === 0) {
                throw new Error('Не получены инструкции от Dual Bank Flash Loan');
            }

            // 🔧 БЕЗОПАСНОЕ ПРЕОБРАЗОВАНИЕ ALT ТАБЛИЦ В ПРАВИЛЬНЫЙ ФОРМАТ
            let altTables = [];
            if (rawAltTables && Array.isArray(rawAltTables) && rawAltTables.length > 0) {
                altTables = rawAltTables
                    .filter(alt => {
                        // 🔧 ПРОВЕРЯЕМ ВАЛИДНОСТЬ ALT ТАБЛИЦЫ
                        if (!alt || !alt.key || !alt.state || !alt.state.addresses) {
                            console.log('⚠️ Пропускаем невалидную ALT таблицу:', alt);
                            return false;
                        }
                        return true;
                    })
                    .map(alt => {
                        try {
                            return new AddressLookupTableAccount({
                                key: alt.key,
                                state: {
                                    addresses: alt.state.addresses
                                }
                            });
                        } catch (error) {
                            console.log(`⚠️ Ошибка создания ALT таблицы: ${error.message}`);
                            return null;
                        }
                    })
                    .filter(alt => alt !== null); // Убираем null значения

                console.log(`✅ Создано ${altTables.length} валидных ALT таблиц из ${rawAltTables.length}`);
            } else {
                console.log('⚠️ ALT таблицы отсутствуют или пусты');
            }

            console.log(`📊 ОРИГИНАЛЬНАЯ ТРАНЗАКЦИЯ:`);
            console.log(`   📋 Инструкций: ${originalInstructions.length}`);
            console.log(`   🗜️ ALT таблиц: ${altTables.length}`);
            
            // 🚫 ВРЕМЕННО ОТКЛЮЧАЕМ ДЕДУПЛИКАЦИЮ ДЛЯ ОТЛАДКИ ПОРЯДКА ИНСТРУКЦИЙ!
            console.log('🚫 ДЕДУПЛИКАЦИЯ ОТКЛЮЧЕНА ДЛЯ ОТЛАДКИ ПОРЯДКА!');

            let finalInstructions = originalInstructions;
            const duplicates = []; // ПУСТОЙ МАССИВ ДЛЯ СОВМЕСТИМОСТИ

            console.log('✅ ИСПОЛЬЗУЕМ ОРИГИНАЛЬНЫЙ ПОРЯДОК ИНСТРУКЦИЙ БЕЗ ДЕДУПЛИКАЦИИ!');

            // 📊 АНАЛИЗ РАЗМЕРА ТРАНЗАКЦИИ
            const sizeAnalysis = await this.analyzeSizeWithALT(finalInstructions, altTables);
            
            return {
                success: true,
                instructions: finalInstructions,
                altTables: altTables,
                sizeAnalysis: sizeAnalysis,
                duplicatesRemoved: duplicates.length,
                originalSize: originalInstructions.length,
                optimizedSize: finalInstructions.length
            };
            
        } catch (error) {
            console.error(`❌ ОШИБКА СОЗДАНИЯ ТРАНЗАКЦИИ: ${error.message}`);
            return { 
                success: false, 
                error: error.message,
                instructions: [],
                altTables: []
            };
        }
    }

    /**
     * 🧪 ТЕСТИРОВАНИЕ ТРАНЗАКЦИИ ЧЕРЕЗ RPC SIMULATE
     */
    async simulateTransaction(instructions, altTables) {
        console.log('\n🌐 СИМУЛЯЦИЯ ТРАНЗАКЦИИ ЧЕРЕЗ RPC...');

        try {
            // 🔧 ПРОВЕРЯЕМ ВХОДНЫЕ ПАРАМЕТРЫ
            if (!instructions || !Array.isArray(instructions) || instructions.length === 0) {
                throw new Error('Инструкции отсутствуют или пусты');
            }

            if (!this.wallet || !this.wallet.publicKey) {
                throw new Error('Кошелек не инициализирован');
            }

            console.log(`📋 Инструкций для симуляции: ${instructions.length}`);
            console.log(`🗜️ ALT таблиц: ${altTables ? altTables.length : 0}`);

            // 🔧 ДОПОЛНИТЕЛЬНАЯ ВАЛИДАЦИЯ ИНСТРУКЦИЙ
            const validInstructions = [];
            for (let i = 0; i < instructions.length; i++) {
                const instruction = instructions[i];

                if (!instruction) {
                    console.log(`⚠️ Пропускаем null/undefined инструкцию ${i}`);
                    continue;
                }

                if (!instruction.programId) {
                    console.log(`⚠️ Пропускаем инструкцию ${i} с null/undefined programId`);
                    console.log(`   Инструкция:`, instruction);
                    continue;
                }

                if (!instruction.keys || !Array.isArray(instruction.keys)) {
                    console.log(`⚠️ Пропускаем инструкцию ${i} с некорректными keys`);
                    continue;
                }

                // Проверяем, что все keys валидны
                let validKeys = true;
                for (const key of instruction.keys) {
                    if (!key || !key.pubkey || typeof key.pubkey.toString !== 'function') {
                        console.log(`⚠️ Инструкция ${i} содержит некорректный key:`, key);
                        validKeys = false;
                        break;
                    }
                }

                if (!validKeys) {
                    console.log(`⚠️ Пропускаем инструкцию ${i} из-за некорректных keys`);
                    continue;
                }

                validInstructions.push(instruction);
            }

            console.log(`✅ Валидных инструкций: ${validInstructions.length} из ${instructions.length}`);

            if (validInstructions.length === 0) {
                throw new Error('Нет валидных инструкций для симуляции');
            }

            // Используем только валидные инструкции
            instructions = validInstructions;
            // ⚡ КЭШИРОВАННЫЙ BLOCKHASH ИЛИ RPC FALLBACK!
            let blockhash;
            if (this.parentBot && typeof this.parentBot.getCachedBlockhash === 'function') {
                const cached = this.parentBot.getCachedBlockhash();
                if (cached && cached.blockhash) {
                    blockhash = cached.blockhash;
                    console.log('⚡ Используем кэшированный blockhash (optimizer)');
                } else {
                    console.log('⚠️ НЕТ КЭШИРОВАННОГО BLOCKHASH! ИСПОЛЬЗУЕМ RPC FALLBACK!');
                    const { blockhash: rpcBlockhash } = await this.connection.getLatestBlockhash('confirmed');
                    blockhash = rpcBlockhash;
                }
            } else {
                console.log('⚠️ НЕТ parentBot! ИСПОЛЬЗУЕМ RPC FALLBACK!');
                const { blockhash: rpcBlockhash } = await this.connection.getLatestBlockhash('confirmed');
                blockhash = rpcBlockhash;
            }
            
            // 🔧 СОЗДАЕМ ТРАНЗАКЦИЮ
            const { VersionedTransaction, TransactionMessage } = require('@solana/web3.js');
            
            const message = new TransactionMessage({
                payerKey: this.wallet.publicKey,
                recentBlockhash: blockhash,
                instructions: instructions
            });

            let transaction;
            if (altTables && altTables.length > 0) {
                console.log(`🔥 ИСПОЛЬЗУЕМ ALT СЖАТИЕ: ${altTables.length} таблиц`);
                altTables.forEach((alt, index) => {
                    // 🔧 БЕЗОПАСНАЯ ПРОВЕРКА ALT СТРУКТУРЫ
                    if (alt && alt.state && alt.state.addresses && Array.isArray(alt.state.addresses)) {
                        console.log(`   ALT ${index + 1}: ${alt.state.addresses.length} адресов`);
                    } else {
                        console.log(`   ALT ${index + 1}: ⚠️ Некорректная структура ALT таблицы`);
                    }
                });

                // 🔧 ФИЛЬТРУЕМ ТОЛЬКО ВАЛИДНЫЕ ALT ТАБЛИЦЫ
                const validAltTables = altTables.filter(alt =>
                    alt && alt.state && alt.state.addresses && Array.isArray(alt.state.addresses)
                );

                if (validAltTables.length > 0) {
                    console.log(`✅ Используем ${validAltTables.length} валидных ALT таблиц из ${altTables.length}`);
                    try {
                        const compiledMessage = message.compileToV0Message(validAltTables);
                        if (!compiledMessage) {
                            throw new Error('compileToV0Message вернул null/undefined');
                        }
                        transaction = new VersionedTransaction(compiledMessage);
                        console.log(`✅ V0 транзакция создана с ALT сжатием`);
                    } catch (error) {
                        console.log(`❌ Ошибка создания V0 транзакции: ${error.message}`);
                        console.log(`🔄 Переходим к Legacy транзакции...`);
                        const compiledMessage = message.compileToLegacyMessage();
                        if (!compiledMessage) {
                            throw new Error('compileToLegacyMessage вернул null/undefined');
                        }
                        transaction = new VersionedTransaction(compiledMessage);
                    }
                } else {
                    console.log(`⚠️ Нет валидных ALT таблиц - используем Legacy транзакцию`);
                    const compiledMessage = message.compileToLegacyMessage();
                    if (!compiledMessage) {
                        throw new Error('compileToLegacyMessage вернул null/undefined');
                    }
                    transaction = new VersionedTransaction(compiledMessage);
                }
            } else {
                console.log(`⚠️ ALT таблицы отсутствуют - используем Legacy транзакцию`);
                try {
                    const compiledMessage = message.compileToLegacyMessage();
                    if (!compiledMessage) {
                        throw new Error('compileToLegacyMessage вернул null/undefined');
                    }
                    transaction = new VersionedTransaction(compiledMessage);
                    console.log(`✅ Legacy транзакция создана`);
                } catch (error) {
                    console.log(`❌ Ошибка создания Legacy транзакции: ${error.message}`);
                    throw error;
                }
            }

            // 🔧 ПРОВЕРЯЕМ ТРАНЗАКЦИЮ ПЕРЕД ПОДПИСАНИЕМ
            if (!transaction) {
                throw new Error('Транзакция не создана (transaction is null/undefined)');
            }

            if (!transaction.message) {
                throw new Error('Транзакция не содержит message');
            }

            console.log(`🔍 Проверка транзакции:`);
            console.log(`   ✅ Transaction создана: ${!!transaction}`);
            console.log(`   ✅ Message создано: ${!!transaction.message}`);
            console.log(`   ✅ Wallet готов: ${!!this.wallet}`);

            // 🔑 ПОДПИСЫВАЕМ ТРАНЗАКЦИЮ
            try {
                transaction.sign([this.wallet]);
                console.log(`✅ Транзакция подписана`);
            } catch (error) {
                console.log(`❌ Ошибка подписания транзакции: ${error.message}`);
                throw error;
            }

            // 📏 ПРОВЕРЯЕМ РАЗМЕР ТРАНЗАКЦИИ
            let serializedSize;
            try {
                const serialized = transaction.serialize();
                if (!serialized) {
                    throw new Error('Сериализация вернула null/undefined');
                }
                serializedSize = serialized.length;
                console.log(`📏 РАЗМЕР ТРАНЗАКЦИИ: ${serializedSize} байт (лимит: 1232)`);
                console.log(`📊 ЗАПАС: ${1232 - serializedSize} байт`);
            } catch (error) {
                console.log(`❌ Ошибка сериализации транзакции: ${error.message}`);
                throw error;
            }

            if (serializedSize > 1232) {
                console.log(`🚨 ПРЕВЫШЕНИЕ ЛИМИТА: ${serializedSize - 1232} байт!`);
            } else {
                console.log(`✅ ТРАНЗАКЦИЯ ПОМЕЩАЕТСЯ В ЛИМИТ`);
            }

            // 🌐 СИМУЛИРУЕМ ТРАНЗАКЦИЮ
            console.log('🌐 Отправка на симуляцию...');
            const simulation = await this.connection.simulateTransaction(transaction, {
                commitment: 'confirmed',
                replaceRecentBlockhash: true
            });

            // 📊 АНАЛИЗ РЕЗУЛЬТАТОВ
            if (simulation.value.err) {
                console.log('❌ СИМУЛЯЦИЯ ПРОВАЛЕНА:');
                console.log(`   🚨 Ошибка: ${JSON.stringify(simulation.value.err)}`);
                
                if (simulation.value.logs) {
                    console.log('📋 ЛОГИ СИМУЛЯЦИИ:');
                    simulation.value.logs.forEach((log, i) => {
                        console.log(`   ${i + 1}. ${log}`);
                    });
                }
                
                return { success: false, error: simulation.value.err, logs: simulation.value.logs };
            } else {
                console.log('✅ СИМУЛЯЦИЯ УСПЕШНА!');
                console.log(`   💰 Использовано Compute Units: ${simulation.value.unitsConsumed || 'N/A'}`);
                
                return { 
                    success: true, 
                    unitsConsumed: simulation.value.unitsConsumed,
                    logs: simulation.value.logs 
                };
            }
            
        } catch (error) {
            console.error(`❌ ОШИБКА СИМУЛЯЦИИ: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 🔥 СОЗДАНИЕ DUAL BANK FLASH LOAN ТРАНЗАКЦИИ
     * ОСНОВНОЙ МЕТОД ДЛЯ СОЗДАНИЯ FLASH LOAN С 2Х БАНКОВ
     */
    async createDualBankFlashLoanTransaction(params) {
        console.log('\n🔥 СОЗДАНИЕ DUAL BANK FLASH LOAN ТРАНЗАКЦИИ...');
        console.log('🏦 ИСПОЛЬЗУЕМ 2 БАНКА: USDC + WSOL');

        try {
            // 🏦 БАНКИ MARGINFI (ПРАВИЛЬНЫЕ ИЗ КЭША!)
            const { PublicKey } = require('@solana/web3.js');
            const BANKS = {
                USDC: new PublicKey('2s37akK2eyBbp8DZgCm7RtsaEz8eJP3Nxd4urLHQv7yB'), // ✅ ПРАВИЛЬНЫЙ USDC банк MarginFi
                WSOL: new PublicKey('CCKtUs6Cgwo4aaQUmBPmyoApH2gUDErxNZCAntD6LYGh')  // ✅ ПРАВИЛЬНЫЙ WSOL банк MarginFi
            };

            // 🎯 ПУЛЫ METEORA (ТОЛЬКО РЕАЛЬНЫЕ АДРЕСА!)
            const POOLS = {
                CHEAP_POOL: new PublicKey(params.cheapPool || 'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y'),     // РЕАЛЬНЫЙ WSOL-USDC пул
                EXPENSIVE_POOL: new PublicKey(params.expensivePool || 'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y') // ТОТ ЖЕ РЕАЛЬНЫЙ ПУЛ!
            };

            console.log(`🏦 БАНКИ:`);
            console.log(`   USDC: ${BANKS.USDC.toString().slice(0, 8)}...`);
            console.log(`   WSOL: ${BANKS.WSOL.toString().slice(0, 8)}...`);
            console.log(`🎯 ПУЛЫ (УМНЫЙ АНАЛИЗАТОР):`);
            console.log(`   Дешевый пул (SOL): ${POOLS.CHEAP_POOL.toString().slice(0, 8)}... (цена: $${params.cheapPrice?.toFixed(2) || 'N/A'})`);
            console.log(`   Дорогой пул (USDC): ${POOLS.EXPENSIVE_POOL.toString().slice(0, 8)}... (цена: $${params.expensivePrice?.toFixed(2) || 'N/A'})`);

            // 🔥 СОЗДАЕМ DUAL BANK FLASH LOAN ЧЕРЕЗ LOW-LEVEL MARGINFI
            console.log('🔥 Создание Dual Bank Flash Loan через Low-Level MarginFi...');
            const flashLoanStructure = await this.buildDualBankFlashLoanStructure(params, BANKS, POOLS);

            if (!flashLoanStructure.success) {
                throw new Error(`Не удалось создать структуру: ${flashLoanStructure.error}`);
            }

            console.log(`✅ DUAL BANK FLASH LOAN ТРАНЗАКЦИЯ СОЗДАНА:`);
            console.log(`   📋 Инструкций: ${flashLoanStructure.instructions.length}`);
            console.log(`   🏦 USDC займ: $${(params.usdcAmount / 1e6).toLocaleString()}`);
            console.log(`   🏦 SOL займ: ${(params.solAmount / 1e9).toLocaleString()} SOL`);
            console.log(`   🎯 Добавление ликвидности на 2 пула`);
            console.log(`   💰 Ожидаемая прибыль: $100,000`);

            return {
                success: true,
                instructions: flashLoanStructure.instructions,
                altTables: flashLoanStructure.altTables || [],
                banks: BANKS,
                pools: POOLS,
                params: params,
                type: 'DUAL_BANK_FLASH_LOAN'
            };

        } catch (error) {
            console.error(`❌ ОШИБКА СОЗДАНИЯ DUAL BANK FLASH LOAN ТРАНЗАКЦИИ: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 🔍 АНАЛИЗ ДУБЛИКАТОВ ИНСТРУКЦИЙ
     */
    analyzeDuplicates(instructions) {
        const seen = new Map();
        const duplicates = [];

        instructions.forEach((instruction, index) => {
            // Создаем уникальный ключ для инструкции
            const key = this.createInstructionKey(instruction);
            
            if (seen.has(key)) {
                duplicates.push({
                    index,
                    instruction,
                    duplicateOf: seen.get(key),
                    key
                });
            } else {
                seen.set(key, index);
            }
        });

        if (duplicates.length > 0) {
            console.log(`🔍 НАЙДЕНО ${duplicates.length} ДУБЛИКАТОВ:`);
            duplicates.forEach((dup, i) => {
                console.log(`   ${i + 1}. Инструкция ${dup.index} дублирует ${dup.duplicateOf}`);
            });
        }

        return duplicates;
    }

    /**
     * 🔧 УДАЛЕНИЕ ДУБЛИКАТОВ
     */
    removeDuplicates(instructions) {
        const seen = new Set();
        const cleanInstructions = [];

        instructions.forEach((instruction, index) => {
            // 🔧 НОРМАЛИЗАЦИЯ ИНСТРУКЦИИ: ИЗВЛЕКАЕМ РЕАЛЬНУЮ ИНСТРУКЦИЮ ИЗ ОБЪЕКТА
            let actualInstruction = instruction;
            if (instruction.instructions && Array.isArray(instruction.instructions) && instruction.instructions.length > 0) {
                actualInstruction = instruction.instructions[0];
            }

            const key = this.createInstructionKey(actualInstruction);

            if (!seen.has(key)) {
                seen.add(key);
                // 🔧 ВОЗВРАЩАЕМ РЕАЛЬНУЮ ИНСТРУКЦИЮ, А НЕ ОБЪЕКТ-ОБЕРТКУ
                cleanInstructions.push(actualInstruction);
            } else {
                const programIdStr = (actualInstruction && actualInstruction.programId)
                    ? actualInstruction.programId.toString().slice(0, 8)
                    : 'UNDEFINED';
                console.log(`   🗑️ Удален дубликат ${index}: ${programIdStr}`);
            }
        });

        return cleanInstructions;
    }

    /**
     * 🔑 СОЗДАНИЕ УНИКАЛЬНОГО КЛЮЧА ДЛЯ ИНСТРУКЦИИ
     */
    createInstructionKey(instruction) {
        // 🔧 СТРОГАЯ ВАЛИДАЦИЯ ИНСТРУКЦИИ ПЕРЕД СОЗДАНИЕМ КЛЮЧА
        if (!instruction) {
            console.error('❌ КРИТИЧЕСКАЯ ОШИБКА: instruction является null/undefined в createInstructionKey!');
            return 'UNDEFINED_INSTRUCTION';
        }

        // 🔧 ИСПРАВЛЕНИЕ: ЕСЛИ ПЕРЕДАН ОБЪЕКТ С ПОЛЕМ instructions, ИЗВЛЕКАЕМ ПЕРВУЮ ИНСТРУКЦИЮ
        let actualInstruction = instruction;
        if (instruction.instructions && Array.isArray(instruction.instructions) && instruction.instructions.length > 0) {
            console.log('🔧 ОБНАРУЖЕН ОБЪЕКТ С ПОЛЕМ instructions - ИЗВЛЕКАЕМ ПЕРВУЮ ИНСТРУКЦИЮ');
            actualInstruction = instruction.instructions[0];
        }

        if (!actualInstruction.programId) {
            console.error('❌ КРИТИЧЕСКАЯ ОШИБКА: instruction.programId является null/undefined в createInstructionKey!');
            console.error('   Инструкция:', instruction);
            return 'UNDEFINED_PROGRAM_ID';
        }

        // 🔧 БЕЗОПАСНОЕ СОЗДАНИЕ КЛЮЧА НА ОСНОВЕ programId, accounts и data
        const accountKeys = (actualInstruction.keys && Array.isArray(actualInstruction.keys))
            ? actualInstruction.keys.map(key => {
                if (!key || !key.pubkey) {
                    console.error('❌ КРИТИЧЕСКАЯ ОШИБКА: key.pubkey является null/undefined в createInstructionKey!');
                    return 'UNDEFINED_PUBKEY:false:false';
                }
                return `${key.pubkey.toString()}:${key.isSigner}:${key.isWritable}`;
              }).join('|')
            : '';

        const dataHex = actualInstruction.data ? Buffer.from(actualInstruction.data).toString('hex') : '';

        return `${actualInstruction.programId.toString()}:${accountKeys}:${dataHex}`;
    }

    /**
     * 📊 АНАЛИЗ РАЗМЕРА ТРАНЗАКЦИИ С ALT
     */
    async analyzeSizeWithALT(instructions, altTables) {
        try {
            const analysis = await this.diagnostic.analyzeTransactionSize(
                instructions,
                altTables,
                this.wallet.publicKey
            );
            
            console.log(`📊 АНАЛИЗ РАЗМЕРА ТРАНЗАКЦИИ:`);
            console.log(`   📋 Инструкций: ${instructions.length}`);
            console.log(`   📏 Размер: ${analysis.totalSize} байт`);
            console.log(`   🗜️ ALT таблиц: ${altTables.length}`);
            console.log(`   ✅ Статус: ${analysis.totalSize <= 1232 ? 'ПОМЕЩАЕТСЯ' : 'ПРЕВЫШАЕТ ЛИМИТ'}`);
            
            return analysis;
            
        } catch (error) {
            console.error(`❌ Ошибка анализа размера: ${error.message}`);
            return { totalSize: 0, error: error.message };
        }
    }

    /**
     * 🔥 ПОСТРОЕНИЕ СТРУКТУРЫ DUAL BANK FLASH LOAN
     * СОЗДАЕТ ИНСТРУКЦИИ ДЛЯ FLASH LOAN С 2Х БАНКОВ
     */
    async buildDualBankFlashLoanStructure(params, banks, pools) {
        console.log('\n🔥 ПОСТРОЕНИЕ DUAL BANK FLASH LOAN СТРУКТУРЫ...');

        try {
            // 🔥 СТРУКТУРА DUAL BANK FLASH LOAN:
            // 1. lending_account_start_flashloan (endIndex)
            // 2. lending_account_borrow (USDC банк) - $1,001,520
            // 3. lending_account_borrow (SOL банк) - 5,500 SOL
            // 4. Добавление ликвидности на Meteora Pool 1 ($500K USDC + SOL)
            // 5. Добавление ликвидности на Meteora Pool 2 ($500K USDC + SOL)
            // 6. Арбитражные операции между пулами
            // 7. Удаление ликвидности с Pool 1
            // 8. Удаление ликвидности с Pool 2
            // 9. lending_account_repay (USDC банк) - repayAll=true
            // 10. lending_account_repay (SOL банк) - repayAll=true
            // 11. lending_account_end_flashloan

            console.log('🔥 СОЗДАНИЕ ИНСТРУКЦИЙ ЧЕРЕЗ LOW-LEVEL MARGINFI...');

            // 🔥 ИСПОЛЬЗУЕМ LOW-LEVEL MARGINFI ДЛЯ СОЗДАНИЯ DUAL BANK FLASH LOAN
            if (!this.flashLoanSystem || !this.flashLoanSystem.lowLevelMarginFi) {
                throw new Error('Low-Level MarginFi система не инициализирована');
            }

            const lowLevel = this.flashLoanSystem.lowLevelMarginFi;

            // 🔥 СОЗДАЕМ DUAL BANK FLASH LOAN ИНСТРУКЦИИ ЧЕРЕЗ НОВЫЙ МЕТОД
            console.log('🔥 Вызов createDualBankFlashLoanInstructions...');
            const dualBankResult = await lowLevel.createDualBankFlashLoanInstructions(
                params,    // параметры займов
                banks,     // USDC и SOL банки
                pools,     // Meteora пулы
                []         // арбитражные инструкции (пока пустые)
            );

            if (!dualBankResult.success) {
                throw new Error(`Не удалось создать Dual Bank Flash Loan: ${dualBankResult.error}`);
            }

            console.log(`✅ DUAL BANK FLASH LOAN ИНСТРУКЦИИ СОЗДАНЫ:`);
            console.log(`   📋 Всего инструкций: ${dualBankResult.instructions.length}`);
            console.log(`   🏦 Банков: 2 (USDC + SOL)`);
            console.log(`   🎯 Пулов: 2 (Meteora 1 + 2)`);
            console.log(`   💰 USDC займ: $${(params.usdcAmount / 1e6).toLocaleString()}`);
            console.log(`   💰 SOL займ: ${(params.solAmount / 1e9).toLocaleString()} SOL`);
            console.log(`   📊 Структура: ${dualBankResult.breakdown.start} start + ${dualBankResult.breakdown.borrow} borrow + ${dualBankResult.breakdown.liquidity} liquidity + ${dualBankResult.breakdown.arbitrage} arbitrage + ${dualBankResult.breakdown.repay} repay + ${dualBankResult.breakdown.end} end`);

            // 🔥 ПОЛУЧАЕМ ALT ТАБЛИЦЫ ИЗ LOW-LEVEL MARGINFI
            let altTables = [];
            try {
                if (lowLevel.masterController && typeof lowLevel.masterController.getLoadedALTTables === 'function') {
                    const rawAltTables = lowLevel.masterController.getLoadedALTTables();
                    if (rawAltTables && Array.isArray(rawAltTables)) {
                        altTables = rawAltTables.filter(alt =>
                            alt && alt.key && alt.state && alt.state.addresses
                        );
                        console.log(`🔥 ПОЛУЧЕНО ALT ТАБЛИЦ: ${altTables.length} валидных из ${rawAltTables.length}`);
                    } else {
                        console.log('⚠️ ALT таблицы не получены или не являются массивом');
                    }
                } else {
                    console.log('⚠️ Master Controller недоступен или метод getLoadedALTTables отсутствует');
                }
            } catch (error) {
                console.log(`⚠️ Ошибка получения ALT таблиц: ${error.message}`);
                altTables = [];
            }

            return {
                success: true,
                instructions: dualBankResult.instructions,
                altTables: altTables,
                structure: 'DUAL_BANK_FLASH_LOAN',
                banks: banks,
                pools: pools,
                breakdown: dualBankResult.breakdown,
                message: 'DUAL BANK FLASH LOAN РЕАЛЬНЫЕ ИНСТРУКЦИИ СОЗДАНЫ!'
            };

        } catch (error) {
            console.error(`❌ ОШИБКА ПОСТРОЕНИЯ DUAL BANK СТРУКТУРЫ: ${error.message}`);
            return { success: false, error: error.message };
        }
    }
}

module.exports = TransactionOptimizerModule;
