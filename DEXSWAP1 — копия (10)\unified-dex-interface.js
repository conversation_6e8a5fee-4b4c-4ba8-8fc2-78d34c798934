#!/usr/bin/env node

/**
 * 🎯 УНИФИЦИРОВАННЫЙ DEX ИНТЕРФЕЙС
 * ═══════════════════════════════════════════════════════════════
 * 🔥 ЦЕЛЬ: Единый интерфейс для работы с Meteora, Raydium, Orca
 * 🚀 ФУНКЦИИ: Получение цен, создание swap инструкций, арбитраж
 * 🛡️ БЕЗОПАСНОСТЬ: Валидация, обработка ошибок, fallback
 */

const { PublicKey, BN } = require('@solana/web3.js');
const colors = require('colors');

// 🔥 ИМПОРТ СУЩЕСТВУЮЩИХ ИНТЕГРАЦИЙ
const MeteoraHybridImplementation = require('./meteora-hybrid-implementation.js');
const RaydiumCLMMIntegration = require('./raydium-clmm-integration.js');
const OrcaWhirlpoolsIntegration = require('./orca-whirlpools-integration.js');

// 🔥 ИМПОРТ КОНФИГУРАЦИИ
const {
    TRADING_CONFIG,
    getTokenMint,
    getMeteoraPools,
    formatAmountInUSD
} = require('./trading-config.js');

// 🔥 ИМПОРТ КОНВЕРТЕРА СУММ
const { convertUsdToNativeAmount, convertNativeToUsdAmount } = require('./centralized-amount-converter.js');

/**
 * 🎯 УНИФИЦИРОВАННЫЙ DEX ИНТЕРФЕЙС
 */
class UnifiedDexInterface {
    constructor(connection, wallet, universalCacheManager = null) {
        this.connection = connection;
        this.wallet = wallet;
        this.universalCacheManager = universalCacheManager;
        // 🔄 ОБРАТНАЯ СОВМЕСТИМОСТЬ
        this.binCacheManager = universalCacheManager;

        // 🔥 ИНИЦИАЛИЗАЦИЯ DEX ИНТЕГРАЦИЙ
        this.meteora = null;
        this.raydium = null;
        this.orca = null;

        // 📊 СТАТИСТИКА
        this.stats = {
            meteoraQueries: 0,
            raydiumQueries: 0,
            orcaQueries: 0,
            totalArbitrageOpportunities: 0,
            successfulTrades: 0
        };

        // 🎯 ПОДДЕРЖИВАЕМЫЕ DEX
        this.supportedDexes = ['meteora', 'raydium', 'orca'];
        this.activeDexes = ['meteora']; // Начинаем только с Meteora

        console.log('🎯 Унифицированный DEX интерфейс создан');
        console.log(`   Поддерживаемые DEX: ${this.supportedDexes.join(', ')}`);
    }

    /**
     * 🚀 ИНИЦИАЛИЗАЦИЯ ВСЕХ DEX ИНТЕГРАЦИЙ
     */
    async initialize() {
        try {
            console.log('🚀 Инициализация унифицированного DEX интерфейса...'.yellow);

            // 1. 🌪️ METEORA (уже работает)
            console.log('🌪️ Инициализация Meteora DLMM...');
            this.meteora = new MeteoraHybridImplementation(this.connection, this.wallet, this.binCacheManager);
            await this.meteora.initializeDLMMInstances();
            console.log('✅ Meteora DLMM инициализирован');

            // 2. ⚡ RAYDIUM (будет добавлен)
            console.log('⚡ Инициализация Raydium CLMM...');
            try {
                await this.initializeRaydium();
                this.activeDexes.push('raydium');
                console.log('✅ Raydium CLMM инициализирован');
            } catch (error) {
                console.log(`⚠️ Raydium недоступен: ${error.message}`);
            }

            // 3. 🌊 ORCA (будет добавлен)
            console.log('🌊 Инициализация Orca Whirlpools...');
            try {
                await this.initializeOrca();
                this.activeDexes.push('orca');
                console.log('✅ Orca Whirlpools инициализирован');
            } catch (error) {
                console.log(`⚠️ Orca недоступен: ${error.message}`);
            }

            console.log(`🎯 Активные DEX: ${this.activeDexes.join(', ')}`);
            console.log('✅ Унифицированный DEX интерфейс готов к работе!'.green);

            return true;

        } catch (error) {
            console.error('❌ Ошибка инициализации унифицированного DEX интерфейса:', error.message);
            throw error;
        }
    }

    /**
     * ⚡ ИНИЦИАЛИЗАЦИЯ RAYDIUM CLMM
     */
    async initializeRaydium() {
        try {
            this.raydium = new RaydiumCLMMIntegration(this.connection, this.wallet, this.universalCacheManager);
            const success = await this.raydium.initialize();

            if (!success) {
                throw new Error('Raydium CLMM инициализация не удалась');
            }

            return true;
        } catch (error) {
            console.log(`⚠️ Ошибка инициализации Raydium: ${error.message}`);
            throw error;
        }
    }

    /**
     * 🌊 ИНИЦИАЛИЗАЦИЯ ORCA WHIRLPOOLS
     */
    async initializeOrca() {
        try {
            this.orca = new OrcaWhirlpoolsIntegration(this.connection, this.wallet, this.universalCacheManager);
            const success = await this.orca.initialize();

            if (!success) {
                throw new Error('Orca Whirlpools инициализация не удалась');
            }

            return true;
        } catch (error) {
            console.log(`⚠️ Ошибка инициализации Orca: ${error.message}`);
            throw error;
        }
    }

    /**
     * 💰 ПОЛУЧЕНИЕ ЦЕН СО ВСЕХ АКТИВНЫХ DEX
     */
    async getAllPrices() {
        try {
            console.log('💰 Получение цен со всех активных DEX...');

            const allPrices = new Map();

            // 🌪️ METEORA ЦЕНЫ
            if (this.activeDexes.includes('meteora') && this.meteora) {
                try {
                    const meteoraPrices = await this.meteora.getAllPricesWithArbitrage();
                    this.stats.meteoraQueries++;

                    for (const [key, price] of meteoraPrices.entries()) {
                        allPrices.set(`meteora_${key}`, {
                            dex: 'meteora',
                            price: price,
                            source: 'meteora_dlmm',
                            timestamp: Date.now()
                        });
                    }

                    console.log(`✅ Meteora: ${meteoraPrices.size} цен получено`);
                } catch (error) {
                    console.log(`⚠️ Ошибка получения цен Meteora: ${error.message}`);
                }
            }

            // ⚡ RAYDIUM ЦЕНЫ (будет добавлено)
            if (this.activeDexes.includes('raydium') && this.raydium) {
                try {
                    const raydiumPrices = await this.getRaydiumPrices();
                    this.stats.raydiumQueries++;

                    for (const [key, price] of raydiumPrices.entries()) {
                        allPrices.set(`raydium_${key}`, {
                            dex: 'raydium',
                            price: price,
                            source: 'raydium_clmm',
                            timestamp: Date.now()
                        });
                    }

                    console.log(`✅ Raydium: ${raydiumPrices.size} цен получено`);
                } catch (error) {
                    console.log(`⚠️ Ошибка получения цен Raydium: ${error.message}`);
                }
            }

            // 🌊 ORCA ЦЕНЫ (будет добавлено)
            if (this.activeDexes.includes('orca') && this.orca) {
                try {
                    const orcaPrices = await this.getOrcaPrices();
                    this.stats.orcaQueries++;

                    for (const [key, price] of orcaPrices.entries()) {
                        allPrices.set(`orca_${key}`, {
                            dex: 'orca',
                            price: price,
                            source: 'orca_whirlpools',
                            timestamp: Date.now()
                        });
                    }

                    console.log(`✅ Orca: ${orcaPrices.size} цен получено`);
                } catch (error) {
                    console.log(`⚠️ Ошибка получения цен Orca: ${error.message}`);
                }
            }

            console.log(`💰 Всего цен получено: ${allPrices.size}`);
            return allPrices;

        } catch (error) {
            console.error('❌ Ошибка получения цен со всех DEX:', error.message);
            return new Map();
        }
    }

    /**
     * ⚡ ПОЛУЧЕНИЕ ЦЕН RAYDIUM
     */
    async getRaydiumPrices() {
        if (!this.raydium) {
            return new Map();
        }
        return await this.raydium.getAllPrices();
    }

    /**
     * 🌊 ПОЛУЧЕНИЕ ЦЕН ORCA
     */
    async getOrcaPrices() {
        if (!this.orca) {
            return new Map();
        }
        return await this.orca.getAllPrices();
    }

    /**
     * 🔍 ПОИСК АРБИТРАЖНЫХ ВОЗМОЖНОСТЕЙ МЕЖДУ DEX
     */
    async findArbitrageOpportunities() {
        try {
            console.log('🔍 Поиск арбитражных возможностей между DEX...');

            const allPrices = await this.getAllPrices();
            const opportunities = [];

            // Группируем цены по токенам
            const pricesByToken = this.groupPricesByToken(allPrices);

            // Ищем арбитражные возможности для каждого токена
            for (const [token, prices] of pricesByToken.entries()) {
                if (prices.length < 2) continue; // Нужно минимум 2 DEX

                // Сортируем по цене
                prices.sort((a, b) => a.price - b.price);

                const cheapest = prices[0];
                const mostExpensive = prices[prices.length - 1];

                const spread = mostExpensive.price - cheapest.price;
                const spreadPercent = (spread / cheapest.price) * 100;

                if (spreadPercent >= TRADING_CONFIG.MIN_SPREAD_PERCENT) {
                    opportunities.push({
                        token: token,
                        buyDex: cheapest.dex,
                        sellDex: mostExpensive.dex,
                        buyPrice: cheapest.price,
                        sellPrice: mostExpensive.price,
                        spread: spreadPercent,
                        spreadUsd: spread,
                        timestamp: Date.now()
                    });

                    console.log(`🎯 Арбитраж найден: ${token}`);
                    console.log(`   Купить: ${cheapest.dex} по $${cheapest.price.toFixed(4)}`);
                    console.log(`   Продать: ${mostExpensive.dex} по $${mostExpensive.price.toFixed(4)}`);
                    console.log(`   Спред: ${spreadPercent.toFixed(4)}%`);
                }
            }

            this.stats.totalArbitrageOpportunities += opportunities.length;
            console.log(`🔍 Найдено арбитражных возможностей: ${opportunities.length}`);

            return opportunities;

        } catch (error) {
            console.error('❌ Ошибка поиска арбитражных возможностей:', error.message);
            return [];
        }
    }

    /**
     * 📊 ГРУППИРОВКА ЦЕН ПО ТОКЕНАМ
     */
    groupPricesByToken(allPrices) {
        const grouped = new Map();

        for (const [key, priceData] of allPrices.entries()) {
            // Извлекаем токен из ключа (например, "meteora_SOL/USDC_address" -> "SOL/USDC")
            const tokenMatch = key.match(/_(SOL\/USDC|USDC\/SOL)_/);
            if (!tokenMatch) continue;

            const token = tokenMatch[1];

            if (!grouped.has(token)) {
                grouped.set(token, []);
            }

            grouped.get(token).push(priceData);
        }

        return grouped;
    }

    /**
     * 🚀 СОЗДАНИЕ SWAP ИНСТРУКЦИИ ДЛЯ УКАЗАННОГО DEX
     */
    async createSwapInstruction(dex, poolAddress, amount, direction, minAmountOut = 1) {
        try {
            console.log(`🚀 Создание swap инструкции: ${dex} ${direction}`);

            switch (dex) {
                case 'meteora':
                    if (!this.meteora) throw new Error('Meteora не инициализирован');
                    return await this.meteora.createStableSwapInstruction(
                        poolAddress,
                        amount,
                        minAmountOut,
                        direction === 'buy', // true для USDC→SOL, false для SOL→USDC
                        null, // userSolAccount (будет создан автоматически)
                        null  // userUsdcAccount (будет создан автоматически)
                    );

                case 'raydium':
                    if (!this.raydium) throw new Error('Raydium не инициализирован');
                    const tokenIn = direction === 'buy' ? getTokenMint('USDC') : getTokenMint('SOL');
                    const tokenOut = direction === 'buy' ? getTokenMint('SOL') : getTokenMint('USDC');
                    return await this.raydium.createSwapInstruction(
                        poolAddress,
                        amount,
                        tokenIn,
                        tokenOut,
                        0.5 // 0.5% slippage
                    );

                case 'orca':
                    if (!this.orca) throw new Error('Orca не инициализирован');
                    const orcaTokenIn = direction === 'buy' ? getTokenMint('USDC') : getTokenMint('SOL');
                    const orcaTokenOut = direction === 'buy' ? getTokenMint('SOL') : getTokenMint('USDC');
                    return await this.orca.createSwapInstruction(
                        poolAddress,
                        amount,
                        orcaTokenIn,
                        orcaTokenOut,
                        50 // 50 basis points = 0.5% slippage
                    );

                default:
                    throw new Error(`Неподдерживаемый DEX: ${dex}`);
            }

        } catch (error) {
            console.error(`❌ Ошибка создания swap инструкции ${dex}:`, error.message);
            throw error;
        }
    }

    /**
     * 📊 ПОЛУЧЕНИЕ СТАТИСТИКИ
     */
    getStats() {
        return {
            ...this.stats,
            activeDexes: this.activeDexes,
            supportedDexes: this.supportedDexes,
            uptime: Date.now() - (this.startTime || Date.now())
        };
    }

    /**
     * 🔄 СБРОС СТАТИСТИКИ
     */
    resetStats() {
        this.stats = {
            meteoraQueries: 0,
            raydiumQueries: 0,
            orcaQueries: 0,
            totalArbitrageOpportunities: 0,
            successfulTrades: 0
        };
        console.log('📊 Статистика сброшена');
    }
}

module.exports = UnifiedDexInterface;
