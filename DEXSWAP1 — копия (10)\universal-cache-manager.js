#!/usr/bin/env node

/**
 * 🚀 УНИВЕРСАЛЬНЫЙ МЕНЕДЖЕР КЭША ДЛЯ ВСЕХ DEX
 * ═══════════════════════════════════════════════════════════════
 * 🎯 ЦЕЛЬ: Единое кэширование bin arrays для Meteora, Orca, Raydium
 * 🔥 ФУНКЦИИ: Загрузка, сохранение, обновление кэша всех DEX
 * 💾 СТРУКТУРА: Отдельные файлы кэша для каждого DEX
 * ⚡ СКОРОСТЬ: Мгновенная загрузка вместо медленных RPC запросов
 */

const fs = require('fs').promises;
const path = require('path');
const colors = require('colors');

class UniversalCacheManager {
    constructor() {
        this.cacheDir = './cache';
        this.cacheFiles = {
            meteora: path.join(this.cacheDir, 'meteora-bin-arrays.json'),
            orca: path.join(this.cacheDir, 'orca-tick-arrays.json'),
            raydium: path.join(this.cacheDir, 'raydium-tick-arrays.json'),
            metadata: path.join(this.cacheDir, 'cache-metadata.json')
        };
        
        this.cache = {
            meteora: new Map(),
            orca: new Map(),
            raydium: new Map()
        };
        
        this.metadata = {
            lastUpdate: {},
            poolCounts: {},
            version: '1.0.0'
        };
        
        console.log('🚀 Универсальный менеджер кэша инициализирован');
    }

    /**
     * 🔧 ИНИЦИАЛИЗАЦИЯ КЭША
     */
    async initialize() {
        try {
            // Создаем директорию кэша если не существует
            await this.ensureCacheDirectory();
            
            // Загружаем существующие кэши
            await this.loadAllCaches();
            
            console.log('✅ Универсальный кэш загружен:');
            console.log(`   📊 Meteora: ${this.cache.meteora.size} пулов`);
            console.log(`   🌊 Orca: ${this.cache.orca.size} пулов`);
            console.log(`   ⚡ Raydium: ${this.cache.raydium.size} пулов`);
            
            return true;
        } catch (error) {
            console.log(`❌ Ошибка инициализации кэша: ${error.message}`);
            return false;
        }
    }

    /**
     * 📁 СОЗДАНИЕ ДИРЕКТОРИИ КЭША
     */
    async ensureCacheDirectory() {
        try {
            await fs.access(this.cacheDir);
        } catch {
            await fs.mkdir(this.cacheDir, { recursive: true });
            console.log(`📁 Создана директория кэша: ${this.cacheDir}`);
        }
    }

    /**
     * 📥 ЗАГРУЗКА ВСЕХ КЭШЕЙ
     */
    async loadAllCaches() {
        const loadPromises = [
            this.loadCache('meteora'),
            this.loadCache('orca'),
            this.loadCache('raydium'),
            this.loadMetadata()
        ];
        
        await Promise.allSettled(loadPromises);
    }

    /**
     * 📥 ЗАГРУЗКА КЭША КОНКРЕТНОГО DEX
     */
    async loadCache(dexName) {
        try {
            const filePath = this.cacheFiles[dexName];
            const data = await fs.readFile(filePath, 'utf8');
            const cacheData = JSON.parse(data);
            
            // Конвертируем объект обратно в Map
            this.cache[dexName] = new Map(Object.entries(cacheData.pools || {}));
            
            console.log(`✅ ${dexName.toUpperCase()} кэш загружен: ${this.cache[dexName].size} пулов`);
            
        } catch (error) {
            console.log(`⚠️ ${dexName.toUpperCase()} кэш не найден, создаем новый`);
            this.cache[dexName] = new Map();
        }
    }

    /**
     * 📥 ЗАГРУЗКА МЕТАДАННЫХ
     */
    async loadMetadata() {
        try {
            const data = await fs.readFile(this.cacheFiles.metadata, 'utf8');
            this.metadata = { ...this.metadata, ...JSON.parse(data) };
        } catch (error) {
            console.log('⚠️ Метаданные кэша не найдены, создаем новые');
        }
    }

    /**
     * 💾 СОХРАНЕНИЕ КЭША КОНКРЕТНОГО DEX
     */
    async saveCache(dexName) {
        try {
            const filePath = this.cacheFiles[dexName];
            const cacheData = {
                version: this.metadata.version,
                lastUpdate: new Date().toISOString(),
                poolCount: this.cache[dexName].size,
                pools: Object.fromEntries(this.cache[dexName])
            };
            
            await fs.writeFile(filePath, JSON.stringify(cacheData, null, 2));
            
            // Обновляем метаданные
            this.metadata.lastUpdate[dexName] = cacheData.lastUpdate;
            this.metadata.poolCounts[dexName] = cacheData.poolCount;
            
            console.log(`💾 ${dexName.toUpperCase()} кэш сохранен: ${cacheData.poolCount} пулов`);
            
        } catch (error) {
            console.log(`❌ Ошибка сохранения ${dexName} кэша: ${error.message}`);
        }
    }

    /**
     * 💾 СОХРАНЕНИЕ МЕТАДАННЫХ
     */
    async saveMetadata() {
        try {
            await fs.writeFile(this.cacheFiles.metadata, JSON.stringify(this.metadata, null, 2));
        } catch (error) {
            console.log(`❌ Ошибка сохранения метаданных: ${error.message}`);
        }
    }

    /**
     * 💾 СОХРАНЕНИЕ ВСЕХ КЭШЕЙ
     */
    async saveAllCaches() {
        const savePromises = [
            this.saveCache('meteora'),
            this.saveCache('orca'),
            this.saveCache('raydium'),
            this.saveMetadata()
        ];
        
        await Promise.allSettled(savePromises);
        console.log('💾 Все кэши сохранены');
    }

    /**
     * 📊 METEORA: Добавление bin array в кэш
     */
    addMeteoraPool(poolAddress, binArrays) {
        this.cache.meteora.set(poolAddress, {
            binArrays: binArrays,
            lastUpdate: new Date().toISOString(),
            type: 'meteora_dlmm'
        });
    }

    /**
     * 🌊 ORCA: Добавление tick array в кэш
     */
    addOrcaPool(poolAddress, tickArrays) {
        this.cache.orca.set(poolAddress, {
            tickArrays: tickArrays,
            lastUpdate: new Date().toISOString(),
            type: 'orca_whirlpool'
        });
    }

    /**
     * ⚡ RAYDIUM: Добавление tick array в кэш
     */
    addRaydiumPool(poolAddress, tickArrays) {
        this.cache.raydium.set(poolAddress, {
            tickArrays: tickArrays,
            lastUpdate: new Date().toISOString(),
            type: 'raydium_clmm'
        });
    }

    /**
     * 🔍 ПОЛУЧЕНИЕ ДАННЫХ ИЗ КЭША
     */
    getPoolData(dexName, poolAddress) {
        return this.cache[dexName]?.get(poolAddress) || null;
    }

    /**
     * 🔍 ПРОВЕРКА НАЛИЧИЯ ПУЛА В КЭШЕ
     */
    hasPool(dexName, poolAddress) {
        return this.cache[dexName]?.has(poolAddress) || false;
    }

    /**
     * 📊 СТАТИСТИКА КЭША
     */
    getStats() {
        return {
            meteora: {
                pools: this.cache.meteora.size,
                lastUpdate: this.metadata.lastUpdate.meteora || 'Никогда'
            },
            orca: {
                pools: this.cache.orca.size,
                lastUpdate: this.metadata.lastUpdate.orca || 'Никогда'
            },
            raydium: {
                pools: this.cache.raydium.size,
                lastUpdate: this.metadata.lastUpdate.raydium || 'Никогда'
            },
            total: this.cache.meteora.size + this.cache.orca.size + this.cache.raydium.size
        };
    }

    /**
     * 🧹 ОЧИСТКА УСТАРЕВШЕГО КЭША
     */
    async cleanOldCache(maxAgeHours = 24) {
        const cutoffTime = new Date(Date.now() - maxAgeHours * 60 * 60 * 1000);
        let cleaned = 0;
        
        for (const [dexName, cache] of Object.entries(this.cache)) {
            for (const [poolAddress, data] of cache.entries()) {
                const lastUpdate = new Date(data.lastUpdate);
                if (lastUpdate < cutoffTime) {
                    cache.delete(poolAddress);
                    cleaned++;
                }
            }
        }
        
        if (cleaned > 0) {
            console.log(`🧹 Очищено ${cleaned} устаревших записей кэша`);
            await this.saveAllCaches();
        }
        
        return cleaned;
    }
}

module.exports = UniversalCacheManager;
