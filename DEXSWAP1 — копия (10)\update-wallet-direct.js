/**
 * 🔐 ПРЯМОЕ ОБНОВЛЕНИЕ WALLET.JSON С ПРАВИЛЬНЫМ ПРИВАТНЫМ КЛЮЧОМ
 */

const { Keypair } = require('@solana/web3.js');
const fs = require('fs');

// Приватный ключ из .env.solana
const privateKeyBase58 = '2LsQ2H5p5Las865haJACjSnFC8FoFGa8rmq5KRKHVwmh16WPqgCqYbdR6Gy57LrJGYyw4RbvryLAgCEufdtkbZXu';
const expectedAddress = 'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV';

console.log('🔐 ОБНОВЛЕНИЕ WALLET.JSON');
console.log('═'.repeat(50));

// Base58 декодировка (из теста мы знаем что это работает)
function base58Decode(str) {
  const alphabet = '**********************************************************';
  let decoded = 0n;
  let multi = 1n;
  
  for (let i = str.length - 1; i >= 0; i--) {
    const char = str[i];
    const index = alphabet.indexOf(char);
    if (index === -1) throw new Error(`Invalid character: ${char}`);
    decoded += BigInt(index) * multi;
    multi *= 58n;
  }
  
  // Конвертируем в байты
  const bytes = [];
  while (decoded > 0n) {
    bytes.unshift(Number(decoded % 256n));
    decoded = decoded / 256n;
  }
  
  // Добавляем ведущие нули
  for (let i = 0; i < str.length && str[i] === '1'; i++) {
    bytes.unshift(0);
  }
  
  return new Uint8Array(bytes);
}

try {
  // Декодируем приватный ключ
  const privateKeyBytes = base58Decode(privateKeyBase58);
  console.log(`✅ Приватный ключ декодирован: ${privateKeyBytes.length} байт`);

  // Проверяем что это правильный ключ
  const keypair = Keypair.fromSecretKey(privateKeyBytes);
  const address = keypair.publicKey.toString();
  
  console.log(`📍 Адрес из приватного ключа: ${address}`);
  console.log(`📍 Ожидаемый адрес:           ${expectedAddress}`);
  
  const addressMatch = address === expectedAddress;
  console.log(`🔍 Адреса совпадают: ${addressMatch ? '✅ ДА' : '❌ НЕТ'}`);

  if (addressMatch) {
    // Создаем резервную копию
    if (fs.existsSync('wallet.json')) {
      fs.copyFileSync('wallet.json', 'wallet.json.backup');
      console.log('✅ Создана резервная копия: wallet.json.backup');
    }
    
    // Конвертируем в массив для wallet.json
    const walletJsonArray = Array.from(privateKeyBytes);
    
    // Записываем новый wallet.json
    fs.writeFileSync('wallet.json', JSON.stringify(walletJsonArray, null, 2));
    console.log('✅ Wallet.json обновлен с правильным приватным ключом!');
    
    // Проверяем что новый wallet.json работает
    const newWalletData = JSON.parse(fs.readFileSync('wallet.json', 'utf8'));
    const newKeypair = Keypair.fromSecretKey(new Uint8Array(newWalletData));
    const newAddress = newKeypair.publicKey.toString();
    
    console.log('\n🎯 ПРОВЕРКА РЕЗУЛЬТАТА:');
    console.log(`📍 Новый адрес из wallet.json: ${newAddress}`);
    console.log(`🔍 Правильный адрес: ${newAddress === expectedAddress ? '✅ ДА' : '❌ НЕТ'}`);
    
    if (newAddress === expectedAddress) {
      console.log('\n🎉 УСПЕХ! Wallet.json теперь содержит правильный приватный ключ!');
      console.log('💰 Система теперь будет читать баланс с правильного адреса!');
      console.log('🚀 Запустите систему - баланс должен показать 0.068070 SOL!');
    } else {
      console.log('\n❌ ОШИБКА: Что-то пошло не так при записи wallet.json');
    }
    
  } else {
    console.log('\n❌ ОШИБКА: Приватный ключ не соответствует ожидаемому адресу!');
  }

} catch (error) {
  console.error(`❌ Ошибка: ${error.message}`);
}
