#!/usr/bin/env node

/**
 * 🔥 ПРОВЕРКА ОФИЦИАЛЬНЫХ MARGINFI DISCRIMINATORS
 * 
 * Вычисляет discriminators по официальной формуле Anchor:
 * discriminator = SHA256("global:method_name").slice(0, 8)
 */

const crypto = require('crypto');

console.log('🔥 ПРОВЕРКА ОФИЦИАЛЬНЫХ MARGINFI DISCRIMINATORS');
console.log('='.repeat(60));

// Функция для вычисления Anchor discriminator
function calculateAnchorDiscriminator(methodName) {
    const input = `global:${methodName}`;
    const hash = crypto.createHash('sha256').update(input).digest();
    const discriminator = hash.slice(0, 8);
    return discriminator;
}

// Проверяем Flash Loan discriminators
const methods = [
    'lending_account_start_flashloan',
    'lending_account_end_flashloan'
];

console.log('📋 ВЫЧИСЛЕНИЕ ОФИЦИАЛЬНЫХ DISCRIMINATORS:');
console.log('');

methods.forEach(method => {
    const discriminator = calculateAnchorDiscriminator(method);
    const bytes = Array.from(discriminator);
    const hex = discriminator.toString('hex');
    
    console.log(`📋 Метод: ${method}`);
    console.log(`   Input: "global:${method}"`);
    console.log(`   Discriminator: [${bytes.map(b => '0x' + b.toString(16).padStart(2, '0')).join(', ')}]`);
    console.log(`   Hex: ${hex}`);
    console.log('');
});

// Проверяем наши текущие discriminators
const ourDiscriminators = {
    START: [0x0e, 0x83, 0x21, 0xdc, 0x51, 0xba, 0xb4, 0x6b],
    END: [0x69, 0x7c, 0xc9, 0x6a, 0x99, 0x02, 0x08, 0x9c]
};

console.log('🔧 НАШИ ТЕКУЩИЕ DISCRIMINATORS:');
console.log(`   START: [${ourDiscriminators.START.map(b => '0x' + b.toString(16).padStart(2, '0')).join(', ')}]`);
console.log(`   END: [${ourDiscriminators.END.map(b => '0x' + b.toString(16).padStart(2, '0')).join(', ')}]`);

// Сравниваем
const calculatedStart = calculateAnchorDiscriminator('lending_account_start_flashloan');
const calculatedEnd = calculateAnchorDiscriminator('lending_account_end_flashloan');

const startMatch = Buffer.from(ourDiscriminators.START).equals(calculatedStart);
const endMatch = Buffer.from(ourDiscriminators.END).equals(calculatedEnd);

console.log('');
console.log('🎯 РЕЗУЛЬТАТ ПРОВЕРКИ:');
console.log(`   START Flash Loan: ${startMatch ? '✅ ПРАВИЛЬНЫЙ' : '❌ НЕПРАВИЛЬНЫЙ'}`);
console.log(`   END Flash Loan: ${endMatch ? '✅ ПРАВИЛЬНЫЙ' : '❌ НЕПРАВИЛЬНЫЙ'}`);
console.log(`   Общий результат: ${startMatch && endMatch ? '✅ ВСЕ ПРАВИЛЬНО' : '❌ ЕСТЬ ОШИБКИ'}`);

if (!startMatch) {
    console.log('');
    console.log('❌ НЕПРАВИЛЬНЫЙ START DISCRIMINATOR:');
    console.log(`   Наш: [${ourDiscriminators.START.map(b => '0x' + b.toString(16).padStart(2, '0')).join(', ')}]`);
    console.log(`   Правильный: [${Array.from(calculatedStart).map(b => '0x' + b.toString(16).padStart(2, '0')).join(', ')}]`);
}

if (!endMatch) {
    console.log('');
    console.log('❌ НЕПРАВИЛЬНЫЙ END DISCRIMINATOR:');
    console.log(`   Наш: [${ourDiscriminators.END.map(b => '0x' + b.toString(16).padStart(2, '0')).join(', ')}]`);
    console.log(`   Правильный: [${Array.from(calculatedEnd).map(b => '0x' + b.toString(16).padStart(2, '0')).join(', ')}]`);
}

console.log('');
console.log('🔥 ФЛАГИ FLASH LOAN:');
console.log('   START Flash Loan: ПОДНИМАЕТ флаг IN_FLASH_LOAN');
console.log('   END Flash Loan: ОПУСКАЕТ флаг IN_FLASH_LOAN');
console.log('   Это обеспечивает атомарность операции без залога!');
