#!/usr/bin/env python3
"""
🔍 VULNERABILITY VERIFIER
Система верификации найденных уязвимостей
"""

import asyncio
import sqlite3
import json
import time
from datetime import datetime
from typing import Dict, List, Any, Optional
import logging
import aiohttp
from dataclasses import dataclass

# Настройка логирования
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class VulnerabilityReport:
    """Отчет о уязвимости"""
    vulnerability_id: str
    target_name: str
    strategy_name: str
    vulnerability_type: str
    severity: str
    confidence: float
    raw_data: Dict[str, Any]
    proof_of_concept: str
    verification_status: str = "pending"
    verification_details: Dict[str, Any] = None
    real_vulnerability: bool = False

class VulnerabilityVerifier:
    """Система верификации уязвимостей"""
    
    def __init__(self, db_path: str = "unified_bug_hunting.db"):
        self.db_path = db_path
        self.session = None
        
    async def __aenter__(self):
        """Асинхронный контекстный менеджер"""
        connector = aiohttp.TCPConnector(limit=5)
        timeout = aiohttp.ClientTimeout(total=30)
        self.session = aiohttp.ClientSession(connector=connector, timeout=timeout)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Закрытие ресурсов"""
        if self.session:
            await self.session.close()
    
    async def verify_all_vulnerabilities(self) -> Dict[str, Any]:
        """Верификация всех найденных уязвимостей"""
        logger.info("🔍 ЗАПУСК ВЕРИФИКАЦИИ УЯЗВИМОСТЕЙ")
        logger.info("=" * 60)
        
        # Получение всех уязвимостей из базы данных
        vulnerabilities = await self._get_vulnerabilities_from_db()
        
        if not vulnerabilities:
            logger.warning("❌ Уязвимости не найдены в базе данных")
            return {"total": 0, "verified": 0, "real": 0}
        
        logger.info(f"📊 Найдено {len(vulnerabilities)} уязвимостей для верификации")
        
        verification_results = {
            "total_vulnerabilities": len(vulnerabilities),
            "verified_count": 0,
            "real_vulnerabilities": 0,
            "false_positives": 0,
            "verification_details": [],
            "summary_by_strategy": {},
            "summary_by_target": {}
        }
        
        # Верификация каждой уязвимости
        for i, vuln in enumerate(vulnerabilities, 1):
            logger.info(f"🔍 [{i}/{len(vulnerabilities)}] Верификация: {vuln.target_name} - {vuln.strategy_name}")
            
            verification_result = await self._verify_single_vulnerability(vuln)
            verification_results["verification_details"].append(verification_result)
            
            if verification_result["verified"]:
                verification_results["verified_count"] += 1
                
                if verification_result["real_vulnerability"]:
                    verification_results["real_vulnerabilities"] += 1
                else:
                    verification_results["false_positives"] += 1
            
            # Статистика по стратегиям
            strategy = vuln.strategy_name
            if strategy not in verification_results["summary_by_strategy"]:
                verification_results["summary_by_strategy"][strategy] = {
                    "total": 0, "verified": 0, "real": 0, "false_positive": 0
                }
            
            verification_results["summary_by_strategy"][strategy]["total"] += 1
            if verification_result["verified"]:
                verification_results["summary_by_strategy"][strategy]["verified"] += 1
                if verification_result["real_vulnerability"]:
                    verification_results["summary_by_strategy"][strategy]["real"] += 1
                else:
                    verification_results["summary_by_strategy"][strategy]["false_positive"] += 1
            
            # Статистика по целям
            target = vuln.target_name
            if target not in verification_results["summary_by_target"]:
                verification_results["summary_by_target"][target] = {
                    "total": 0, "verified": 0, "real": 0, "false_positive": 0
                }
            
            verification_results["summary_by_target"][target]["total"] += 1
            if verification_result["verified"]:
                verification_results["summary_by_target"][target]["verified"] += 1
                if verification_result["real_vulnerability"]:
                    verification_results["summary_by_target"][target]["real"] += 1
                else:
                    verification_results["summary_by_target"][target]["false_positive"] += 1
            
            # Небольшая пауза между проверками
            await asyncio.sleep(0.1)
        
        # Генерация итогового отчета
        await self._generate_verification_report(verification_results)
        
        return verification_results
    
    async def _get_vulnerabilities_from_db(self) -> List[VulnerabilityReport]:
        """Получение уязвимостей из базы данных"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # Получаем все результаты с найденными уязвимостями
            cursor.execute("""
                SELECT tr.result_id, tr.target_id, tr.strategy_name, tr.strategy_type,
                       tr.vulnerability_type, tr.severity, tr.confidence, tr.raw_data,
                       tr.proof_of_concept, tt.name as target_name
                FROM test_results tr
                JOIN test_targets tt ON tr.target_id = tt.target_id
                WHERE tr.vulnerability_found = 1
                ORDER BY tr.confidence DESC, tr.test_timestamp DESC
            """)
            
            vulnerabilities = []
            for row in cursor.fetchall():
                vuln = VulnerabilityReport(
                    vulnerability_id=row['result_id'],
                    target_name=row['target_name'],
                    strategy_name=row['strategy_name'],
                    vulnerability_type=row['vulnerability_type'],
                    severity=row['severity'],
                    confidence=row['confidence'],
                    raw_data=json.loads(row['raw_data'] or '{}'),
                    proof_of_concept=row['proof_of_concept'] or ''
                )
                vulnerabilities.append(vuln)
            
            conn.close()
            return vulnerabilities
            
        except Exception as e:
            logger.error(f"Ошибка получения уязвимостей из БД: {e}")
            return []
    
    async def _verify_single_vulnerability(self, vuln: VulnerabilityReport) -> Dict[str, Any]:
        """Верификация одной уязвимости"""
        verification_result = {
            "vulnerability_id": vuln.vulnerability_id,
            "target_name": vuln.target_name,
            "strategy_name": vuln.strategy_name,
            "verified": False,
            "real_vulnerability": False,
            "verification_method": "",
            "details": {},
            "confidence_assessment": "",
            "recommendation": ""
        }
        
        try:
            # Анализ типа стратегии для определения метода верификации
            if "shannon_entropy" in vuln.strategy_name.lower():
                verification_result = await self._verify_entropy_analysis(vuln, verification_result)
            
            elif "quantum" in vuln.strategy_name.lower():
                verification_result = await self._verify_quantum_analysis(vuln, verification_result)
            
            elif "future" in vuln.strategy_name.lower() or "consciousness" in vuln.strategy_name.lower():
                verification_result = await self._verify_experimental_analysis(vuln, verification_result)
            
            elif "neural" in vuln.strategy_name.lower() or "ai" in vuln.strategy_name.lower():
                verification_result = await self._verify_ai_analysis(vuln, verification_result)
            
            else:
                verification_result = await self._verify_generic_analysis(vuln, verification_result)
            
            verification_result["verified"] = True
            
        except Exception as e:
            logger.error(f"Ошибка верификации {vuln.vulnerability_id}: {e}")
            verification_result["details"]["error"] = str(e)
        
        return verification_result
    
    async def _verify_entropy_analysis(self, vuln: VulnerabilityReport, result: Dict) -> Dict:
        """Верификация энтропийного анализа"""
        result["verification_method"] = "entropy_analysis"
        
        # Анализируем данные энтропии
        entropy_value = vuln.raw_data.get('shannon_entropy', 0)
        threshold = vuln.raw_data.get('entropy_threshold', 0.7)
        
        if entropy_value > threshold:
            # Высокая энтропия может указывать на сложность или потенциальные проблемы
            result["real_vulnerability"] = entropy_value > 0.8  # Очень высокая энтропия
            result["confidence_assessment"] = f"Энтропия {entropy_value:.3f} {'превышает' if entropy_value > 0.8 else 'близка к'} критическому уровню"
            
            if entropy_value > 0.9:
                result["recommendation"] = "КРИТИЧНО: Требует немедленного анализа - аномально высокая энтропия"
            elif entropy_value > 0.8:
                result["recommendation"] = "ВЫСОКИЙ: Рекомендуется детальный анализ кода"
            else:
                result["recommendation"] = "СРЕДНИЙ: Возможна ложная тревога, требует проверки"
        else:
            result["real_vulnerability"] = False
            result["confidence_assessment"] = f"Энтропия {entropy_value:.3f} в пределах нормы"
            result["recommendation"] = "НИЗКИЙ: Вероятно ложное срабатывание"
        
        result["details"] = {
            "entropy_value": entropy_value,
            "threshold": threshold,
            "analysis_type": "mathematical_entropy"
        }
        
        return result
    
    async def _verify_quantum_analysis(self, vuln: VulnerabilityReport, result: Dict) -> Dict:
        """Верификация квантового анализа"""
        result["verification_method"] = "quantum_analysis"
        
        # Анализируем квантовые метрики
        quantum_states = vuln.raw_data.get('quantum_states', 0)
        superposition = vuln.raw_data.get('superposition_analysis', False)
        
        # Квантовые методы часто дают экспериментальные результаты
        if quantum_states > 6:  # Много квантовых состояний
            result["real_vulnerability"] = quantum_states > 8
            result["confidence_assessment"] = f"Обнаружено {quantum_states} квантовых состояний"
            
            if quantum_states > 10:
                result["recommendation"] = "ЭКСПЕРИМЕНТАЛЬНО: Высокая сложность системы, требует исследования"
            else:
                result["recommendation"] = "ИССЛЕДОВАНИЕ: Интересные паттерны, возможна уязвимость"
        else:
            result["real_vulnerability"] = False
            result["confidence_assessment"] = f"Недостаточно квантовых состояний ({quantum_states})"
            result["recommendation"] = "НИЗКИЙ: Квантовый анализ не выявил аномалий"
        
        result["details"] = {
            "quantum_states": quantum_states,
            "superposition_analysis": superposition,
            "analysis_type": "quantum_experimental"
        }
        
        return result
    
    async def _verify_experimental_analysis(self, vuln: VulnerabilityReport, result: Dict) -> Dict:
        """Верификация экспериментальных методов"""
        result["verification_method"] = "experimental_analysis"
        
        # Экспериментальные методы требуют особого подхода
        complexity_score = vuln.raw_data.get('complexity_score', 0)
        temporal_factor = vuln.raw_data.get('temporal_factor', 0)
        experimental = vuln.raw_data.get('experimental', False)
        
        if experimental and complexity_score > 10:
            # Экспериментальные методы могут находить нестандартные паттерны
            result["real_vulnerability"] = complexity_score > 20 and temporal_factor > 0.7
            result["confidence_assessment"] = f"Экспериментальный анализ: сложность {complexity_score}, фактор {temporal_factor:.2f}"
            
            if result["real_vulnerability"]:
                result["recommendation"] = "ЭКСПЕРИМЕНТАЛЬНО: Обнаружены нестандартные паттерны, требует исследования"
            else:
                result["recommendation"] = "ИССЛЕДОВАНИЕ: Интересные данные, но недостаточно для уязвимости"
        else:
            result["real_vulnerability"] = False
            result["confidence_assessment"] = "Экспериментальные методы не выявили значимых аномалий"
            result["recommendation"] = "НИЗКИЙ: Экспериментальные данные в пределах нормы"
        
        result["details"] = {
            "complexity_score": complexity_score,
            "temporal_factor": temporal_factor,
            "experimental": experimental,
            "analysis_type": "experimental_future"
        }
        
        return result
    
    async def _verify_ai_analysis(self, vuln: VulnerabilityReport, result: Dict) -> Dict:
        """Верификация AI анализа"""
        result["verification_method"] = "ai_analysis"
        
        # AI методы анализируют семантические уязвимости
        semantic_vulns = vuln.raw_data.get('semantic_vulnerabilities', [])
        ai_analysis = vuln.raw_data.get('ai_analysis', False)
        model_type = vuln.raw_data.get('model_type', 'unknown')
        
        if ai_analysis and len(semantic_vulns) > 0:
            result["real_vulnerability"] = len(semantic_vulns) > 2  # Множественные находки
            result["confidence_assessment"] = f"AI обнаружил {len(semantic_vulns)} семантических проблем"
            
            if result["real_vulnerability"]:
                result["recommendation"] = "AI: Множественные семантические проблемы требуют анализа"
            else:
                result["recommendation"] = "AI: Найдены минорные семантические особенности"
        else:
            result["real_vulnerability"] = False
            result["confidence_assessment"] = "AI анализ не выявил семантических проблем"
            result["recommendation"] = "НИЗКИЙ: AI не обнаружил значимых проблем"
        
        result["details"] = {
            "semantic_vulnerabilities": semantic_vulns,
            "ai_analysis": ai_analysis,
            "model_type": model_type,
            "analysis_type": "ai_semantic"
        }
        
        return result
    
    async def _verify_generic_analysis(self, vuln: VulnerabilityReport, result: Dict) -> Dict:
        """Верификация общих методов"""
        result["verification_method"] = "generic_analysis"
        
        # Общий анализ на основе уверенности и типа
        confidence = vuln.confidence
        analysis_type = vuln.raw_data.get('analysis_type', 'unknown')
        
        if confidence > 0.8:
            result["real_vulnerability"] = True
            result["confidence_assessment"] = f"Высокая уверенность ({confidence:.1%})"
            result["recommendation"] = "ВЫСОКИЙ: Требует детального анализа"
        elif confidence > 0.6:
            result["real_vulnerability"] = False  # Возможно ложное срабатывание
            result["confidence_assessment"] = f"Средняя уверенность ({confidence:.1%})"
            result["recommendation"] = "СРЕДНИЙ: Требует дополнительной проверки"
        else:
            result["real_vulnerability"] = False
            result["confidence_assessment"] = f"Низкая уверенность ({confidence:.1%})"
            result["recommendation"] = "НИЗКИЙ: Вероятно ложное срабатывание"
        
        result["details"] = {
            "confidence": confidence,
            "analysis_type": analysis_type,
            "verification_type": "confidence_based"
        }
        
        return result
    
    async def _generate_verification_report(self, results: Dict[str, Any]):
        """Генерация отчета верификации"""
        report_filename = f"vulnerability_verification_report_{int(time.time())}.json"
        
        # Добавляем метрики
        total = results["total_vulnerabilities"]
        real = results["real_vulnerabilities"]
        false_pos = results["false_positives"]
        
        results["metrics"] = {
            "accuracy_rate": real / total if total > 0 else 0,
            "false_positive_rate": false_pos / total if total > 0 else 0,
            "verification_rate": results["verified_count"] / total if total > 0 else 0
        }
        
        # Сохранение отчета
        with open(report_filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)
        
        logger.info(f"💾 Отчет верификации сохранен: {report_filename}")

async def main():
    """Главная функция верификации"""
    print("🔍 VULNERABILITY VERIFICATION SYSTEM")
    print("=" * 60)
    
    async with VulnerabilityVerifier() as verifier:
        results = await verifier.verify_all_vulnerabilities()
        
        print(f"\n📊 РЕЗУЛЬТАТЫ ВЕРИФИКАЦИИ:")
        print(f"   Всего уязвимостей: {results['total_vulnerabilities']}")
        print(f"   Верифицировано: {results['verified_count']}")
        print(f"   Реальных уязвимостей: {results['real_vulnerabilities']}")
        print(f"   Ложных срабатываний: {results['false_positives']}")
        
        if results['total_vulnerabilities'] > 0:
            accuracy = results['real_vulnerabilities'] / results['total_vulnerabilities'] * 100
            false_pos_rate = results['false_positives'] / results['total_vulnerabilities'] * 100
            
            print(f"\n📈 МЕТРИКИ:")
            print(f"   Точность: {accuracy:.1f}%")
            print(f"   Ложные срабатывания: {false_pos_rate:.1f}%")
            
            # Топ стратегии по точности
            print(f"\n🎯 ЭФФЕКТИВНОСТЬ СТРАТЕГИЙ:")
            for strategy, stats in results['summary_by_strategy'].items():
                if stats['total'] > 0:
                    accuracy = stats['real'] / stats['total'] * 100
                    print(f"   {strategy}: {stats['real']}/{stats['total']} ({accuracy:.1f}%)")

if __name__ == "__main__":
    asyncio.run(main())
