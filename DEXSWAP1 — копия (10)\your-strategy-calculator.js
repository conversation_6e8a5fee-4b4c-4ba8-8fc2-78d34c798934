#!/usr/bin/env node

/**
 * 🎯 КАЛЬКУЛЯТОР ТВОЕЙ СТРАТЕГИИ
 * 
 * 🔥 ПОШАГОВЫЙ АНАЛИЗ:
 * 1. Flash Loan $1.8M wSOL
 * 2. Конвертация $1.4M wSOL → USDC
 * 3. Добавление $1.4M USDC ликвидности
 * 4. Продажа $400K wSOL по высокой цене
 * 5. Покупка SOL на все USDC
 * 6. Вывод ликвидности $1.4M
 * 7. Возврат займа $1.8M wSOL
 */

class YourStrategyCalculator {
    constructor() {
        // 📊 ИСХОДНЫЕ ДАННЫЕ
        this.POOL_DATA = {
            tvl: 7164218,           // $7.16M TVL
            sol_amount: 21920.46,   // SOL токенов
            usdc_amount: 3396139,   // USDC токенов
            current_price: 171.97,  // Текущая цена SOL
            dynamic_fee_current: 0.00401157, // 0.401157%
            max_dynamic_fee: 0.10   // 10% максимум
        };

        // 💰 ПАРАМЕТРЫ СТРАТЕГИИ (ИСПРАВЛЕНО!)
        this.STRATEGY = {
            flash_loan_amount: 1800000,     // $1.8M USDC (не wSOL!)
            liquidity_amount: 1400000,      // $1.4M USDC односторонняя ликвидность
            trading_amount: 400000,         // $400K USDC для покупки SOL
            target_pool_tvl: 7000000        // $7M большой пул для покупки
        };

        console.log('🎯 КАЛЬКУЛЯТОР ТВОЕЙ СТРАТЕГИИ ИНИЦИАЛИЗИРОВАН');
        console.log(`💰 Flash Loan: $${this.STRATEGY.flash_loan_amount.toLocaleString()}`);
        console.log(`🏊 Ликвидность: $${this.STRATEGY.liquidity_amount.toLocaleString()}`);
        console.log(`⚡ Торговля: $${this.STRATEGY.trading_amount.toLocaleString()}`);
    }

    /**
     * 📈 ШАГ 1-3: ВЛИЯНИЕ ДОБАВЛЕНИЯ ЛИКВИДНОСТИ НА ЦЕНУ
     */
    calculateLiquidityImpact() {
        const liquidityRatio = this.STRATEGY.liquidity_amount / this.POOL_DATA.usdc_amount;
        
        // Влияние на цену (консервативная модель)
        const baseImpact = liquidityRatio * 0.25;
        const nonLinearImpact = Math.pow(liquidityRatio, 1.1) * 0.15;
        const totalImpactPercent = (baseImpact + nonLinearImpact) * 100;
        
        const newPrice = this.POOL_DATA.current_price * (1 + totalImpactPercent / 100);
        const priceIncrease = newPrice - this.POOL_DATA.current_price;
        
        // Новая доля в пуле
        const newPoolTvl = this.POOL_DATA.tvl + this.STRATEGY.liquidity_amount;
        const lpShare = this.STRATEGY.liquidity_amount / newPoolTvl;
        
        return {
            impactPercent: totalImpactPercent,
            oldPrice: this.POOL_DATA.current_price,
            newPrice,
            priceIncrease,
            lpShare: lpShare * 100,
            newPoolTvl
        };
    }

    /**
     * 💰 ШАГ 4: ПРОДАЖА wSOL ПО ВЫСОКОЙ ЦЕНЕ
     */
    calculateWSolSale(newPrice) {
        const saleAmount = this.STRATEGY.trading_amount;
        const oldValue = saleAmount; // По старой цене
        const newValue = (saleAmount / this.POOL_DATA.current_price) * newPrice; // По новой цене
        const extraProfit = newValue - oldValue;
        
        // Slippage при продаже
        const slippage = 0.005; // 0.5%
        const slippageLoss = newValue * slippage;
        const netProceeds = newValue - slippageLoss;
        
        return {
            saleAmount,
            oldValue,
            newValue,
            extraProfit,
            slippageLoss,
            netProceeds,
            usdcReceived: netProceeds
        };
    }

    /**
     * 🛒 ШАГ 5: ПОКУПКА SOL В БОЛЬШОМ ПУЛЕ
     */
    calculateSolPurchase(usdcAmount, lpShare) {
        // Dynamic Fee 10% в нашем пуле
        const dynamicFee = 0.10;
        const feeAmount = usdcAmount * dynamicFee;
        const lpFeeIncome = feeAmount * (lpShare / 100); // Наша доля от комиссии
        
        // Покупка в большом пуле (меньший slippage)
        const largPoolSlippage = 0.002; // 0.2%
        const effectiveAmount = usdcAmount - feeAmount;
        const slippageLoss = effectiveAmount * largPoolSlippage;
        const netUsdcForSol = effectiveAmount - slippageLoss;
        
        // Количество SOL
        const solReceived = netUsdcForSol / this.POOL_DATA.current_price;
        
        return {
            usdcAmount,
            dynamicFee: dynamicFee * 100,
            feeAmount,
            lpFeeIncome,
            largPoolSlippage: largPoolSlippage * 100,
            slippageLoss,
            netUsdcForSol,
            solReceived
        };
    }

    /**
     * 🏊 ШАГ 6: ВЫВОД ОДНОСТОРОННЕЙ ЛИКВИДНОСТИ (БЕЗ IMPERMANENT LOSS!)
     */
    calculateLiquidityWithdrawal(newPrice, lpShare) {
        // 🔥 ОДНОСТОРОННЯЯ ЛИКВИДНОСТЬ = НЕТ IMPERMANENT LOSS!
        // Добавили только USDC → получаем только USDC + комиссии

        const originalUsdcValue = this.STRATEGY.liquidity_amount;

        // Накопленные комиссии от торговли
        const tradingFeeIncome = 11676; // Из расчета bins

        // Дополнительные комиссии от других торговцев (консервативно)
        const additionalFees = originalUsdcValue * 0.0005; // 0.05% за время

        const totalAccumulatedFees = tradingFeeIncome + additionalFees;

        return {
            impermanentLoss: 0,         // НЕТ IL при односторонней ликвидности!
            originalValue: originalUsdcValue,
            valueAfterIL: originalUsdcValue, // Без изменений
            usdcReceived: originalUsdcValue, // Получаем все USDC обратно
            solReceived: 0,             // Никакого SOL
            accumulatedFees: totalAccumulatedFees,
            totalValue: originalUsdcValue + totalAccumulatedFees
        };
    }

    /**
     * 🔄 ШАГ 7: ПРОВЕРКА ВОЗВРАТА ЗАЙМА (В USDC!)
     */
    calculateLoanRepayment(usdcFromSale, usdcFromWithdrawal) {
        const totalUsdcAvailable = usdcFromSale + usdcFromWithdrawal;
        const usdcNeeded = this.STRATEGY.flash_loan_amount; // Нужно вернуть $1.8M USDC

        const surplus = totalUsdcAvailable - usdcNeeded;
        const canRepay = surplus >= 0;

        return {
            usdcNeeded,
            totalUsdcAvailable,
            surplus,
            canRepay,
            surplusValue: surplus
        };
    }

    /**
     * 🚀 ПОЛНЫЙ АНАЛИЗ СТРАТЕГИИ
     */
    runFullAnalysis() {
        console.log('\n🚀 ПОЛНЫЙ АНАЛИЗ ТВОЕЙ СТРАТЕГИИ');
        console.log('=' .repeat(80));

        // ШАГ 1-3: Добавление ликвидности
        const liquidityImpact = this.calculateLiquidityImpact();
        
        console.log('\n📈 ШАГ 1-3: ДОБАВЛЕНИЕ ЛИКВИДНОСТИ');
        console.log(`   Влияние на цену: +${liquidityImpact.impactPercent.toFixed(2)}%`);
        console.log(`   Старая цена: $${liquidityImpact.oldPrice}`);
        console.log(`   Новая цена: $${liquidityImpact.newPrice.toFixed(2)}`);
        console.log(`   Твоя доля в пуле: ${liquidityImpact.lpShare.toFixed(2)}%`);

        // ШАГ 4: Продажа wSOL
        const wsolSale = this.calculateWSolSale(liquidityImpact.newPrice);
        
        console.log('\n💰 ШАГ 4: ПРОДАЖА wSOL ПО ВЫСОКОЙ ЦЕНЕ');
        console.log(`   Продаем: $${wsolSale.saleAmount.toLocaleString()}`);
        console.log(`   Дополнительная прибыль: $${wsolSale.extraProfit.toFixed(0)}`);
        console.log(`   USDC получено: $${wsolSale.usdcReceived.toFixed(0)}`);

        // ШАГ 5: Покупка SOL
        const solPurchase = this.calculateSolPurchase(wsolSale.usdcReceived, liquidityImpact.lpShare);
        
        console.log('\n🛒 ШАГ 5: ПОКУПКА SOL В БОЛЬШОМ ПУЛЕ');
        console.log(`   USDC для покупки: $${solPurchase.usdcAmount.toFixed(0)}`);
        console.log(`   Dynamic Fee: ${solPurchase.dynamicFee}% = $${solPurchase.feeAmount.toFixed(0)}`);
        console.log(`   Твой доход от комиссии: $${solPurchase.lpFeeIncome.toFixed(0)}`);
        console.log(`   SOL получено: ${solPurchase.solReceived.toFixed(2)}`);

        // ШАГ 6: Вывод ликвидности
        const withdrawal = this.calculateLiquidityWithdrawal(liquidityImpact.newPrice, liquidityImpact.lpShare);
        
        console.log('\n🏊 ШАГ 6: ВЫВОД ОДНОСТОРОННЕЙ ЛИКВИДНОСТИ');
        console.log(`   Impermanent Loss: ${withdrawal.impermanentLoss}% (НЕТ IL!)`);
        console.log(`   USDC получено: $${withdrawal.usdcReceived.toLocaleString()}`);
        console.log(`   SOL получено: ${withdrawal.solReceived} (только USDC)`);
        console.log(`   Накопленные комиссии: $${withdrawal.accumulatedFees.toFixed(0)}`);

        // ШАГ 7: Возврат займа (в USDC)
        const repayment = this.calculateLoanRepayment(
            solPurchase.usdcAmount, // USDC от продажи SOL
            withdrawal.usdcReceived  // USDC от вывода ликвидности
        );
        
        console.log('\n🔄 ШАГ 7: ВОЗВРАТ ЗАЙМА');
        console.log(`   Нужно USDC: $${repayment.usdcNeeded.toLocaleString()}`);
        console.log(`   Доступно USDC: $${repayment.totalUsdcAvailable.toLocaleString()}`);
        console.log(`   ${repayment.canRepay ? '✅ МОЖЕМ ВЕРНУТЬ' : '❌ НЕ ХВАТАЕТ'}`);
        console.log(`   Профицит/дефицит: $${repayment.surplus.toFixed(0)}`);

        // ИТОГОВАЯ ПРИБЫЛЬ
        const totalProfit = wsolSale.extraProfit + solPurchase.lpFeeIncome + withdrawal.accumulatedFees + repayment.surplus;
        const roi = (totalProfit / this.STRATEGY.flash_loan_amount) * 100;

        console.log('\n🎯 ИТОГОВЫЙ РЕЗУЛЬТАТ');
        console.log('=' .repeat(60));
        console.log(`   💰 Общая прибыль: $${totalProfit.toFixed(0)}`);
        console.log(`   📈 ROI: ${roi.toFixed(2)}%`);
        console.log(`   ✅ Стратегия ${repayment.canRepay ? 'РАБОТАЕТ' : 'ТРЕБУЕТ ДОРАБОТКИ'}`);

        return {
            liquidityImpact,
            wsolSale,
            solPurchase,
            withdrawal,
            repayment,
            totalProfit,
            roi,
            viable: repayment.canRepay
        };
    }
}

// 🚀 ЗАПУСК АНАЛИЗА
if (require.main === module) {
    const calculator = new YourStrategyCalculator();
    const results = calculator.runFullAnalysis();
    
    if (results.viable) {
        console.log('\n🎉 СТРАТЕГИЯ ЖИЗНЕСПОСОБНА!');
        console.log(`💰 Ожидаемая прибыль: $${results.totalProfit.toFixed(0)}`);
        console.log(`📈 ROI: ${results.roi.toFixed(2)}%`);
    } else {
        console.log('\n⚠️ СТРАТЕГИЯ ТРЕБУЕТ ДОРАБОТКИ');
        console.log('🔧 Рекомендации:');
        console.log('   - Уменьшить размер flash loan');
        console.log('   - Увеличить торговую часть');
        console.log('   - Пересмотреть пропорции');
    }
}

module.exports = YourStrategyCalculator;
