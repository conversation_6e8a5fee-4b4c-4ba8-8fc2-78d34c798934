# 🔥 ОФИЦИАЛЬНОЕ РЕШЕНИЕ ОШИБКИ ALT: "invalid owner"

## 📋 АНАЛИЗ ОШИБКИ

**Ошибка**: `failed to simulate transaction: invalid transaction: Transaction loads an address table account with an invalid owner`

**Причина**: Транзакция пытается загрузить Address Lookup Table (ALT) с неправильным владельцем программы.

---

## 🔍 КОРНЕВАЯ ПРИЧИНА (ПО ОФИЦИАЛЬНЫМ ИСТОЧНИКАМ)

### 📚 Официальная документация Solana:
- **Источник**: https://docs.anza.xyz/runtime/programs
- **ALT Program ID**: `AddressLookupTab1e1111111111111111111111111`
- **Ошибка в коде**: https://github.com/solana-labs/solana/blob/master/sdk/src/transaction/error.rs

### 🔧 Структура ALT (из исходного кода Solana):
```rust
// Минимальный размер ALT аккаунта
const LOOKUP_TABLE_META_SIZE: usize = 56;

// Discriminator для ALT (первый байт)
const ALT_DISCRIMINATOR: u8 = 1;

// Официальный Program ID
const ALT_PROGRAM_ID: &str = "AddressLookupTab1e1111111111111111111111111";
```

---

## 🚨 ПРОБЛЕМЫ В ТЕКУЩЕМ КОДЕ

### 1. **Фиктивные ALT в atomic-transaction-builder-fixed.js:2453**
```javascript
// ❌ ПРОБЛЕМА: Фиктивный ключ System Program
const customALT = {
  key: new PublicKey('11111111111111111111111111111111'), // Фиктивный ключ
  state: {
    addresses: uncoveredKeys.slice(0, 256).map(key => new PublicKey(key))
  }
};
```

### 2. **Неправильная валидация ALT**
- Код не проверяет владельца ALT перед использованием
- Смешивает реальные ALT с фиктивными
- Не проверяет discriminator и размер данных

---

## ✅ ОФИЦИАЛЬНОЕ РЕШЕНИЕ

### 🔧 1. ПРАВИЛЬНАЯ ВАЛИДАЦИЯ ALT

```javascript
/**
 * 🔥 ОФИЦИАЛЬНАЯ ВАЛИДАЦИЯ ALT ПО ДОКУМЕНТАЦИИ SOLANA
 * Источник: https://docs.anza.xyz/runtime/programs
 */
async function validateALT(connection, altAddress) {
  try {
    const pubkey = new PublicKey(altAddress);
    const accountInfo = await connection.getAccountInfo(pubkey);

    if (!accountInfo) {
      return { valid: false, reason: 'Account not found' };
    }

    // 1. Проверяем минимальный размер (LOOKUP_TABLE_META_SIZE = 56)
    if (accountInfo.data.length < 56) {
      return { valid: false, reason: `Too small: ${accountInfo.data.length} < 56 bytes` };
    }

    // 2. Проверяем правильный owner (официальный ALT Program ID)
    const ALT_PROGRAM_ID = 'AddressLookupTab1e1111111111111111111111111';
    if (accountInfo.owner.toString() !== ALT_PROGRAM_ID) {
      return { valid: false, reason: `Wrong owner: ${accountInfo.owner.toString()}` };
    }

    // 3. Проверяем discriminator (первый байт должен быть 1)
    const discriminator = accountInfo.data[0];
    if (discriminator !== 1) {
      return { valid: false, reason: `Wrong discriminator: ${discriminator} != 1` };
    }

    return { valid: true, reason: 'Valid ALT' };

  } catch (error) {
    return { valid: false, reason: `Error: ${error.message}` };
  }
}
```

### 🔧 2. БЕЗОПАСНАЯ ЗАГРУЗКА ALT

```javascript
/**
 * 🔥 ОФИЦИАЛЬНАЯ ЗАГРУЗКА ALT (ТОЛЬКО ВАЛИДНЫЕ)
 * Источник: https://solana.com/developers/guides/advanced/lookup-tables
 */
async function loadValidALT(connection, altAddress) {
  console.log(`🔍 Проверяем ALT: ${altAddress.slice(0, 8)}...`);
  
  // Сначала валидируем
  const validation = await validateALT(connection, altAddress);
  if (!validation.valid) {
    console.log(`   ❌ НЕВАЛИДНЫЙ ALT: ${validation.reason}`);
    return null;
  }

  try {
    // Используем официальный метод Solana
    const altPublicKey = new PublicKey(altAddress);
    const lookupTableResponse = await connection.getAddressLookupTable(altPublicKey);

    if (!lookupTableResponse.value) {
      console.log(`   ❌ ALT не найден через getAddressLookupTable`);
      return null;
    }

    console.log(`   ✅ ВАЛИДНЫЙ ALT: ${lookupTableResponse.value.state.addresses.length} адресов`);
    return lookupTableResponse.value;

  } catch (error) {
    console.log(`   ❌ Ошибка загрузки: ${error.message}`);
    return null;
  }
}
```

### 🔧 3. ФИЛЬТРАЦИЯ JUPITER ALT

```javascript
/**
 * 🔥 БЕЗОПАСНАЯ ФИЛЬТРАЦИЯ JUPITER ALT
 * Jupiter API может возвращать смешанный список адресов
 */
async function filterValidJupiterALT(connection, jupiterALTAddresses) {
  console.log(`🔍 Фильтруем ${jupiterALTAddresses.length} Jupiter ALT...`);
  
  const validALTs = [];
  
  for (const altAddress of jupiterALTAddresses) {
    const alt = await loadValidALT(connection, altAddress);
    if (alt) {
      validALTs.push(alt);
    }
  }
  
  console.log(`✅ Найдено ${validALTs.length} валидных ALT из ${jupiterALTAddresses.length}`);
  return validALTs;
}
```

---

## 🔧 ИСПРАВЛЕНИЯ В КОДЕ

### 1. **Удалить фиктивные ALT**
```javascript
// ❌ УДАЛИТЬ ЭТО:
const customALT = {
  key: new PublicKey('11111111111111111111111111111111'), // Фиктивный ключ
  state: {
    addresses: uncoveredKeys.slice(0, 256).map(key => new PublicKey(key))
  }
};
```

### 2. **Использовать только реальные ALT**
```javascript
// ✅ ПРАВИЛЬНО:
// Загружаем только валидные ALT из Jupiter
const validJupiterALT = await filterValidJupiterALT(connection, jupiterALTAddresses);

// Создаем реальную кастомную ALT если нужно
if (uncoveredKeys.length > 0) {
  const customALTAddress = await createRealALT(connection, wallet, uncoveredKeys);
  if (customALTAddress) {
    const customALT = await loadValidALT(connection, customALTAddress);
    if (customALT) {
      validJupiterALT.push(customALT);
    }
  }
}
```

---

## 🎯 ПЛАН ВНЕДРЕНИЯ

### Шаг 1: Обновить валидацию ALT
- [ ] Добавить функцию `validateALT()`
- [ ] Добавить функцию `loadValidALT()`
- [ ] Добавить функцию `filterValidJupiterALT()`

### Шаг 2: Исправить atomic-transaction-builder-fixed.js
- [ ] Удалить фиктивные ALT (строка 2453)
- [ ] Заменить на валидацию ALT
- [ ] Использовать только реальные ALT

### Шаг 3: Обновить все ALT менеджеры
- [ ] complete-alt-manager.js
- [ ] official-alt-manager.js
- [ ] jupiter-marginfi-alt-manager.js

---

## 📚 ИСТОЧНИКИ

1. **Официальная документация Solana**: https://docs.anza.xyz/runtime/programs
2. **ALT Program ID**: `AddressLookupTab1e1111111111111111111111111`
3. **Исходный код ошибки**: https://github.com/solana-labs/solana/blob/master/sdk/src/transaction/error.rs
4. **Структура ALT**: LOOKUP_TABLE_META_SIZE = 56 байт, discriminator = 1

---

## 🔥 КРИТИЧНО

**НЕ ИСПОЛЬЗУЙТЕ ФИКТИВНЫЕ ALT В ПРОДАКШЕНЕ!**

Solana runtime проверяет владельца каждого ALT аккаунта. Фиктивные ALT с неправильным владельцем вызывают ошибку `invalid owner`.

Используйте только:
1. ✅ Реальные ALT из Jupiter API (после валидации)
2. ✅ Созданные вами ALT с правильным Program ID
3. ✅ Официальные ALT от протоколов (MarginFi, Orca, etc.)
