# 🔍 ОФИЦИАЛЬНОЕ РУКОВОДСТВО ПО CLEANUP INSTRUCTION

## 📚 **ОФИЦИАЛЬНАЯ ДОКУМЕНТАЦИЯ JUPITER API V6**

### **🎯 ЧТО ТАКОЕ cleanupInstruction?**

Согласно официальной документации Jupiter API v6:

> **`cleanupInstruction`** - Unwrap the SOL if `wrapAndUnwrapSol = true`.

### **📋 СТРУКТУРА ОТВЕТА /swap-instructions:**

```javascript
const {
  tokenLedgerInstruction, // If you are using `useTokenLedger = true`.
  computeBudgetInstructions, // The necessary instructions to setup the compute budget.
  setupInstructions, // Setup missing ATA for the users.
  swapInstruction: swapInstructionPayload, // The actual swap instruction.
  cleanupInstruction, // Unwrap the SOL if `wrapAndUnwrapSol = true`.
  addressLookupTableAddresses, // The lookup table addresses that you can use if you are using versioned transaction.
} = instructions;
```

## 🔧 **ПРАВИЛЬНАЯ ОБРАБОТКА cleanupInstruction**

### **1. ДЕСЕРИАЛИЗАЦИЯ ИНСТРУКЦИИ:**

```javascript
const deserializeInstruction = (instruction) => {
  return new TransactionInstruction({
    programId: new PublicKey(instruction.programId),
    keys: instruction.accounts.map((key) => ({
      pubkey: new PublicKey(key.pubkey),
      isSigner: key.isSigner,
      isWritable: key.isWritable,
    })),
    data: Buffer.from(instruction.data, "base64"),
  });
};
```

### **2. ДОБАВЛЕНИЕ В ТРАНЗАКЦИЮ:**

```javascript
const messageV0 = new TransactionMessage({
  payerKey: payerPublicKey,
  recentBlockhash: blockhash,
  instructions: [
    // uncomment if needed: ...setupInstructions.map(deserializeInstruction),
    deserializeInstruction(swapInstructionPayload),
    // uncomment if needed: deserializeInstruction(cleanupInstruction),
  ],
}).compileToV0Message(addressLookupTableAccounts);
```

## 🚨 **КРИТИЧЕСКИЕ УСЛОВИЯ ДЛЯ cleanupInstruction**

### **✅ КОГДА cleanupInstruction ПОЯВЛЯЕТСЯ:**

1. **`wrapAndUnwrapSol: true`** (обязательно!)
2. **Output mint = SOL** или создается WSOL account
3. **Jupiter создает временный WSOL account**

### **❌ КОГДА cleanupInstruction НЕ ПОЯВЛЯЕТСЯ:**

1. **`wrapAndUnwrapSol: false`**
2. **Output mint не SOL и не создается WSOL**
3. **Используется существующий WSOL account**

## 🔍 **АНАЛИЗ НАШЕЙ ПРОБЛЕМЫ**

### **🧪 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ:**

✅ **Jupiter API РАБОТАЕТ ПРАВИЛЬНО:**
- USDC -> SOL: cleanupInstruction **ЕСТЬ**
- SOL -> USDC: cleanupInstruction **ЕСТЬ** 
- wrapAndUnwrapSol: false: cleanupInstruction **НЕТ**

### **🚨 ПРОБЛЕМА В НАШЕМ КОДЕ:**

Наш код **НЕ ДОБАВЛЯЕТ** cleanupInstruction в финальную транзакцию!

## 🔧 **ИСПРАВЛЕНИЯ В НАШЕМ КОДЕ**

### **1. jupiter-swap-instructions.js - ✅ ПРАВИЛЬНО**

```javascript
// ✅ ПРАВИЛЬНО извлекает cleanupInstruction
const result = {
  cleanupInstruction: swapData.cleanupInstruction, // ✅ ЕСТЬ
  // ...
};
```

### **2. atomic-transaction-builder-fixed.js - ❌ ПРОБЛЕМА**

**БЫЛО (неправильно):**
```javascript
// ❌ НЕ ДОБАВЛЯЕТ cleanupInstruction в транзакцию
const allInstructions = [
  ...normalizedJupiterInstructions // Только swap, без cleanup
];
```

**СТАЛО (исправлено):**
```javascript
// ✅ ДОБАВЛЯЕТ cleanupInstruction
if (jupiterSwapData.cleanupInstruction) {
  console.log(`✅ Добавляем cleanupInstruction от Jupiter API`);
  jupiterInstructions.push(jupiterSwapData.cleanupInstruction);
} else if (jupiterSwapData.swapData?.cleanupInstruction) {
  console.log(`✅ Добавляем cleanupInstruction из swapData`);
  jupiterInstructions.push(jupiterSwapData.swapData.cleanupInstruction);
}
```

## 📊 **ОФИЦИАЛЬНАЯ СХЕМА SWAGGER**

```yaml
SwapInstructionsResponse:
  type: object
  properties:
    cleanupInstruction:
      description: |
        - To unwrap the SOL if `wrapAndUnwrapSol = true`.
      $ref: '#/components/schemas/Instruction'
  required:
    - computeBudgetInstructions
    - setupInstructions
    - swapInstruction
    - addressLookupTableAddresses
```

**ВАЖНО:** `cleanupInstruction` **НЕ** в required полях!

## 🎯 **ПРАВИЛЬНЫЙ ПОРЯДОК ИНСТРУКЦИЙ**

### **ОФИЦИАЛЬНЫЙ ПРИМЕР:**
```javascript
instructions: [
  ...setupInstructions.map(deserializeInstruction),     // 1. Setup ATA
  deserializeInstruction(swapInstructionPayload),       // 2. Swap
  deserializeInstruction(cleanupInstruction),           // 3. Cleanup (closeAccount)
]
```

### **НАШ ИСПРАВЛЕННЫЙ КОД:**
```javascript
const jupiterInstructions = [];

// 1. Setup instructions
if (jupiterSwapData.swapData?.setupInstructions) {
  jupiterInstructions.push(...jupiterSwapData.swapData.setupInstructions);
}

// 2. Main swap instruction
if (jupiterSwapData.swapInstruction) {
  jupiterInstructions.push(jupiterSwapData.swapInstruction);
}

// 3. Cleanup instruction (ИСПРАВЛЕНО!)
if (jupiterSwapData.cleanupInstruction) {
  jupiterInstructions.push(jupiterSwapData.cleanupInstruction);
}
```

## 🎉 **ОЖИДАЕМЫЙ РЕЗУЛЬТАТ**

После исправлений:
1. ✅ cleanupInstruction будет правильно добавляться в транзакцию
2. ✅ closeAccount будет выполняться автоматически
3. ✅ SOL будет возвращаться в кошелек после каждого swap'а
4. ✅ Временные WSOL accounts будут закрываться

## 🔗 **ОФИЦИАЛЬНЫЕ ССЫЛКИ**

- [Jupiter API v6 Documentation](https://dev.jup.ag/docs/old/apis/swap-api)
- [Build Swap Transaction Guide](https://dev.jup.ag/docs/swap-api/build-swap-transaction)
- [Official Jupiter Examples](https://github.com/jup-ag/jupiter-quote-api-node)
- [Swagger API Schema](https://github.com/jup-ag/jupiter-quote-api-node/blob/main/swagger.yaml)
