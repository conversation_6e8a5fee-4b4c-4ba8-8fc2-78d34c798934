/**
 * 🔥 POOL BC - ИНИЦИАЛИЗАЦИЯ POSITION АККАУНТОВ ДЛЯ ПУЛОВ 2 И 3
 * ОТДЕЛЬНЫЙ СКРИПТ - НЕ СВЯЗАН С ОСНОВНЫМ КОДОМ!
 * ИСПОЛЬЗУЕТ FLASH LOAN ДЛЯ МИНИМАЛЬНОЙ ПЛАТЫ
 */

const { Connection, PublicKey, TransactionInstruction, ComputeBudgetProgram, Transaction, sendAndConfirmTransaction } = require('@solana/web3.js');
const { Keypair } = require('@solana/web3.js');
const fs = require('fs');

class PoolBCInitializer {
    constructor(wallet, marginfiAccountAddress, connection) {
        this.wallet = wallet;
        this.marginfiAccountAddress = new PublicKey(marginfiAccountAddress);
        this.connection = connection;

        // 🔥 КОНСТАНТЫ ИЗ ОСНОВНОГО КОДА
        this.MARGINFI_PROGRAM = new PublicKey('MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA');
        this.METEORA_DLMM_PROGRAM = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');
        this.TOKEN_PROGRAM = new PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA');

        // 🔥 MARGINFI GROUP
        this.MARGINFI_GROUP = new PublicKey('4qp6Fx6tnZkY5Wropq9wUYgtFxXKwE6viZxFHg3rdAG8');
        
        // 🔥 БАНКИ
        this.BANKS = {
            SOL: new PublicKey('CCKtUs6Cgwo4aaQUmBPmyoApH2gUDErxNZCAntD6LYGh')
        };

        // 🔥 VAULT АККАУНТЫ
        this.VAULTS = {
            SOL: {
                liquidityVault: new PublicKey('2eicbpitfJXDwqCuFAmPgDP7t2oUotnAzbGzRKLMgSLe'),
                vaultAuthority: new PublicKey('DD3AeAssFvjqTvRTrRAtpfjkBF8FpVKnFuwnMLN9haXD'),
                userTokenAccount: new PublicKey('68rtTtSuEPR84Wo1xWGs6ytBttn7JN33Ux8WsDp38FHk')
            }
        };

        // 🔥 POOL ADDRESSES
        this.POOL_2_ADDRESS = 'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y';
        this.POOL_3_ADDRESS = 'HTvjzsfX3yU6BUodCjZ5vZkUrAxMDTrBs3CJaq43ashR';
    }

    /**
     * 🚀 СОЗДАНИЕ ТРАНЗАКЦИИ ИНИЦИАЛИЗАЦИИ POOL B + C
     */
    async createPoolBCInitTransaction() {
        console.log('🔥 СОЗДАНИЕ ТРАНЗАКЦИИ ИНИЦИАЛИЗАЦИИ POOL B + C...');
        
        const instructions = [];

        // 0-1: ComputeBudget
        const computeUnitLimitIx = ComputeBudgetProgram.setComputeUnitLimit({
            units: 1400000
        });
        instructions.push(computeUnitLimitIx);
        
        const computeUnitPriceIx = ComputeBudgetProgram.setComputeUnitPrice({
            microLamports: 0
        });
        instructions.push(computeUnitPriceIx);

        // 2: START Flash Loan
        const endIndex = 8; // END Flash Loan на позиции 8 (БЕЗ WITHDRAW!)
        const startFlashLoanIx = this.createStartFlashLoanInstruction(endIndex);
        instructions.push(startFlashLoanIx);

        // 3: BORROW SOL (минимум для инициализации 2 пулов)
        const borrowAmount = **********; // 2 SOL
        const borrowSOLIx = this.createBorrowInstruction(borrowAmount, this.BANKS.SOL);
        instructions.push(borrowSOLIx);

        // 4: INITIALIZE POSITION + ADD LIQUIDITY POOL 2
        const initAndAddLiquidityPool2Ix = this.createInitializePositionAndAddLiquidityInstruction(2);
        instructions.push(initAndAddLiquidityPool2Ix);

        // 5: INITIALIZE POSITION + ADD LIQUIDITY POOL 3
        const initAndAddLiquidityPool3Ix = this.createInitializePositionAndAddLiquidityInstruction(3);
        instructions.push(initAndAddLiquidityPool3Ix);

        // 6: REMOVE LIQUIDITY POOL 2
        const removeLiquidityPool2Ix = this.createRemoveLiquidityInstruction(2);
        instructions.push(removeLiquidityPool2Ix);

        // 7: REMOVE LIQUIDITY POOL 3
        const removeLiquidityPool3Ix = this.createRemoveLiquidityInstruction(3);
        instructions.push(removeLiquidityPool3Ix);

        // 8: END Flash Loan (ДОЛЖЕН БЫТЬ НА ПОЗИЦИИ endIndex!)
        const endFlashLoanIx = this.createEndFlashLoanInstruction();
        instructions.push(endFlashLoanIx);

        // 9: REPAY SOL (ПОСЛЕ END FLASH LOAN!)
        const repaySOLIx = this.createRepayInstruction(this.BANKS.SOL, true);
        instructions.push(repaySOLIx);

        console.log(`✅ Создано ${instructions.length} инструкций для Pool B + C`);
        return instructions;
    }

    /**
     * 🔧 START FLASH LOAN (СКОПИРОВАНО ИЗ ОСНОВНОГО КОДА!)
     */
    createStartFlashLoanInstruction(endIndex) {
        console.log(`🔧 START Flash Loan с endIndex: ${endIndex}`);

        // 🔥 ПРАВИЛЬНЫЙ DISCRIMINATOR ИЗ УСПЕШНОЙ ТРАНЗАКЦИИ
        const instructionData = Buffer.alloc(16);
        const correctDiscriminator = [14, 131, 33, 220, 81, 186, 180, 107]; // 0x0e8321dc51bab46b
        Buffer.from(correctDiscriminator).copy(instructionData, 0);
        instructionData.writeBigUInt64LE(BigInt(endIndex), 8);

        // 🔥 ПРАВИЛЬНЫЕ АККАУНТЫ (КАК В РАБОЧЕЙ ТРАНЗАКЦИИ!)
        const accounts = [
            { pubkey: this.marginfiAccountAddress, isSigner: false, isWritable: true },  // MarginFi Account
            { pubkey: this.wallet.publicKey, isSigner: true, isWritable: true },         // Authority ✅ WRITABLE!
            { pubkey: new PublicKey('Sysvar1nstructions1111111111111111111111111'), isSigner: false, isWritable: false } // Instructions Sysvar
        ];

        return new TransactionInstruction({
            programId: this.MARGINFI_PROGRAM,
            keys: accounts,
            data: instructionData
        });
    }

    /**
     * 🔧 BORROW INSTRUCTION (СКОПИРОВАНО ИЗ ОСНОВНОГО КОДА!)
     */
    createBorrowInstruction(amount, bankAddress) {
        console.log(`🔧 BORROW ${amount} от банка ${bankAddress.toString().slice(0,8)}...`);

        // 🔥 ОПРЕДЕЛЯЕМ КАКОЙ БАНК (ТОЛЬКО SOL ДЛЯ PoolBC)
        const vaultInfo = this.VAULTS.SOL;

        console.log(`   💰 Токен: SOL`);
        console.log(`   🏦 Vault: ${vaultInfo.liquidityVault.toString().slice(0,8)}...`);
        console.log(`   👤 User Account: ${vaultInfo.userTokenAccount.toString().slice(0,8)}...`);

        // 🔥 ПРАВИЛЬНЫЙ DISCRIMINATOR ДЛЯ BORROW
        const borrowDiscriminator = [0x04, 0x7e, 0x74, 0x35, 0x30, 0x05, 0xd4, 0x1f];

        const instructionData = Buffer.alloc(16);
        Buffer.from(borrowDiscriminator).copy(instructionData, 0);
        instructionData.writeBigUInt64LE(BigInt(amount), 8);

        // 🔥 ПРАВИЛЬНЫЕ АККАУНТЫ ДЛЯ BORROW
        const accounts = [
            { pubkey: this.MARGINFI_GROUP, isSigner: false, isWritable: false },        // 0: marginfi_group
            { pubkey: new PublicKey(this.marginfiAccountAddress), isSigner: false, isWritable: true }, // 1: marginfi_account
            { pubkey: this.wallet.publicKey, isSigner: true, isWritable: false },       // 2: authority (signer)
            { pubkey: bankAddress, isSigner: false, isWritable: true },                 // 3: bank
            { pubkey: vaultInfo.userTokenAccount, isSigner: false, isWritable: true },  // 4: destination_token_account
            { pubkey: vaultInfo.vaultAuthority, isSigner: false, isWritable: true },    // 5: bank_liquidity_vault_authority
            { pubkey: vaultInfo.liquidityVault, isSigner: false, isWritable: true },    // 6: bank_liquidity_vault
            { pubkey: this.TOKEN_PROGRAM, isSigner: false, isWritable: false }          // 7: token_program
        ];

        return new TransactionInstruction({
            programId: this.MARGINFI_PROGRAM,
            keys: accounts,
            data: instructionData
        });
    }

    /**
     * 🔧 WITHDRAW INSTRUCTION
     */
    createWithdrawInstruction(tokenType, amount) {
        const discriminator = [183, 18, 70, 156, 148, 109, 161, 34]; // lendingAccountWithdraw
        const instructionData = Buffer.concat([
            Buffer.from(discriminator),
            Buffer.from(amount.toString(16).padStart(16, '0'), 'hex').reverse(),
            Buffer.from([1]) // withdrawAll = false
        ]);

        return new TransactionInstruction({
            keys: [
                { pubkey: this.marginfiAccountAddress, isSigner: false, isWritable: true },
                { pubkey: this.BANKS.SOL, isSigner: false, isWritable: true },
                { pubkey: this.VAULTS.SOL.userTokenAccount, isSigner: false, isWritable: true },
                { pubkey: this.VAULTS.SOL.liquidityVault, isSigner: false, isWritable: true },
                { pubkey: this.TOKEN_PROGRAM, isSigner: false, isWritable: false },
                { pubkey: this.wallet.publicKey, isSigner: true, isWritable: false }
            ],
            programId: this.MARGINFI_PROGRAM,
            data: instructionData
        });
    }

    /**
     * 🔧 INITIALIZE POSITION + ADD LIQUIDITY (ОДНА ИНСТРУКЦИЯ!)
     */
    createInitializePositionAndAddLiquidityInstruction(poolNumber) {
        // TODO: Реализовать на основе успешных примеров
        // Пока возвращаем заглушку
        const discriminator = [3, 221, 149, 218, 111, 141, 118, 213]; // addLiquidityByStrategy2
        const instructionData = Buffer.from(discriminator);

        const poolAddress = poolNumber === 2 ? this.POOL_2_ADDRESS : this.POOL_3_ADDRESS;

        return new TransactionInstruction({
            keys: [
                { pubkey: new PublicKey(poolAddress), isSigner: false, isWritable: true },
                { pubkey: this.wallet.publicKey, isSigner: true, isWritable: true }
            ],
            programId: this.METEORA_DLMM_PROGRAM,
            data: instructionData
        });
    }

    /**
     * 🔧 REMOVE LIQUIDITY
     */
    createRemoveLiquidityInstruction(poolNumber) {
        // TODO: Реализовать
        const discriminator = [80, 85, 209, 72, 24, 206, 177, 108]; // removeLiquidityByRange2
        const instructionData = Buffer.from(discriminator);

        const poolAddress = poolNumber === 2 ? this.POOL_2_ADDRESS : this.POOL_3_ADDRESS;

        return new TransactionInstruction({
            keys: [
                { pubkey: new PublicKey(poolAddress), isSigner: false, isWritable: true },
                { pubkey: this.wallet.publicKey, isSigner: true, isWritable: true }
            ],
            programId: this.METEORA_DLMM_PROGRAM,
            data: instructionData
        });
    }

    /**
     * 🔧 REPAY INSTRUCTION
     */
    createRepayInstruction(bank, repayAll) {
        const discriminator = [234, 103, 67, 82, 208, 234, 219, 166]; // lendingAccountRepay
        const instructionData = Buffer.concat([
            Buffer.from(discriminator),
            Buffer.from([0, 0, 0, 0, 0, 0, 0, 0]), // amount (ignored if repayAll)
            Buffer.from([repayAll ? 1 : 0]) // repayAll
        ]);

        return new TransactionInstruction({
            keys: [
                { pubkey: this.marginfiAccountAddress, isSigner: false, isWritable: true },
                { pubkey: bank, isSigner: false, isWritable: true },
                { pubkey: this.VAULTS.SOL.userTokenAccount, isSigner: false, isWritable: true },
                { pubkey: this.VAULTS.SOL.liquidityVault, isSigner: false, isWritable: true },
                { pubkey: this.TOKEN_PROGRAM, isSigner: false, isWritable: false },
                { pubkey: this.wallet.publicKey, isSigner: true, isWritable: false }
            ],
            programId: this.MARGINFI_PROGRAM,
            data: instructionData
        });
    }

    /**
     * 🔧 END FLASH LOAN (ПРАВИЛЬНЫЙ DISCRIMINATOR!)
     */
    createEndFlashLoanInstruction() {
        const discriminator = [105, 124, 201, 106, 153, 2, 8, 156]; // ПРАВИЛЬНЫЙ endFlashloan!
        const instructionData = Buffer.from(discriminator);

        return new TransactionInstruction({
            keys: [
                { pubkey: this.marginfiAccountAddress, isSigner: false, isWritable: true },
                { pubkey: this.wallet.publicKey, isSigner: true, isWritable: false }
            ],
            programId: this.MARGINFI_PROGRAM,
            data: instructionData
        });
    }

    /**
     * 🚀 ВЫПОЛНЕНИЕ ИНИЦИАЛИЗАЦИИ POOL B + C
     */
    async executePoolBCInit() {
        try {
            console.log('\n🚀 ЗАПУСК ИНИЦИАЛИЗАЦИИ POOL B + C...');
            
            const instructions = await this.createPoolBCInitTransaction();
            const transaction = new Transaction();
            instructions.forEach(ix => transaction.add(ix));

            console.log('📤 Отправка транзакции...');
            const signature = await sendAndConfirmTransaction(this.connection, transaction, [this.wallet]);
            
            console.log(`✅ POOL B + C ИНИЦИАЛИЗИРОВАНЫ! Signature: ${signature}`);
            return signature;
        } catch (error) {
            console.error('❌ Ошибка инициализации Pool B + C:', error);
            throw error;
        }
    }
}

module.exports = PoolBCInitializer;
