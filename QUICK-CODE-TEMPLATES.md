# 🚀 QUICK CODE TEMPLATES
## Готовые шаблоны кода для быстрого копирования

---

## 🎯 1. JUPITER API TEMPLATES

### 📊 Quote Request Template:

```javascript
const getJupiterQuote = async (inputMint, outputMint, amount) => {
  const url = `https://lite-api.jup.ag/swap/v1/quote?` +
    `inputMint=${inputMint}&` +
    `outputMint=${outputMint}&` +
    `amount=${amount}&` +
    `slippageBps=50&` +
    `restrictIntermediateTokens=true&` +
    `maxAccounts=84&` +
    `onlyDirectRoutes=false&` +
    `asLegacyTransaction=false`;

  const response = await fetch(url);
  if (!response.ok) {
    throw new Error(`Jupiter Quote API error: ${response.status}`);
  }

  return response.json();
};
```

### 🔧 Swap Instructions Template:

```javascript
const getJupiterSwapInstructions = async (quote, userPublicKey) => {
  const response = await fetch('https://lite-api.jup.ag/swap/v1/swap-instructions', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      quoteResponse: quote,
      userPublicKey: userPublicKey.toString(),
      wrapAndUnwrapSol: true,
      skipUserAccountsRpcCalls: true,
      dynamicComputeUnitLimit: true,
      dynamicSlippage: true,
      prioritizationFeeLamports: {
        priorityLevelWithMaxLamports: {
          maxLamports: 1000000,
          priorityLevel: "veryHigh"
        }
      }
    })
  });

  if (!response.ok) {
    throw new Error(`Jupiter Swap Instructions API error: ${response.status}`);
  }

  return response.json();
};
```

### 📋 Response Processing Template:

```javascript
const processJupiterResponse = (result) => {
  // ПРАВИЛЬНО - отдельные поля, НЕ массив instructions
  const {
    computeBudgetInstructions = [],
    setupInstructions = [],
    swapInstruction,
    cleanupInstruction,
    addressLookupTableAddresses = []
  } = result;

  const allInstructions = [
    ...computeBudgetInstructions,
    ...setupInstructions,
    ...(swapInstruction ? [swapInstruction] : []),
    ...(cleanupInstruction ? [cleanupInstruction] : [])
  ];

  console.log(`🪐 Jupiter инструкций: ${allInstructions.length}`);
  console.log(`🗜️ ALT таблиц: ${addressLookupTableAddresses.length}`);

  return { allInstructions, addressLookupTableAddresses };
};
```

---

## 🏦 2. MARGINFI TEMPLATES

### 📊 MarginFi Initialization Template:

```javascript
const initializeMarginFi = async (wallet, connection) => {
  const config = {
    environment: "mainnet-beta",
    cluster: "mainnet-beta",
    programId: "MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZPxwGdHdC",
    confirmTransactionInitialTimeout: 20000,
    commitment: "confirmed"
  };

  try {
    const client = await MarginfiClient.fetch(config, wallet, connection);
    const accounts = await client.getMarginfiAccountsForAuthority();

    if (accounts.length === 0) {
      throw new Error("No MarginFi account found");
    }

    console.log(`🏦 MarginFi инициализирован: ${accounts.length} аккаунтов`);
    return { client, account: accounts[0] };
  } catch (error) {
    console.error('❌ MarginFi initialization error:', error);
    throw error;
  }
};
```

### ⚡ Flash Loan Template:

```javascript
const createFlashLoan = async (amount, tokenSymbol, marginfiClient, marginfiAccount) => {
  const bank = marginfiClient.getBankByTokenSymbol(tokenSymbol);

  if (!bank) {
    throw new Error(`Bank for ${tokenSymbol} not found`);
  }

  // ✅ ПРАВИЛЬНО! ИСПОЛЬЗУЙТЕ ТОЛЬКО buildFlashLoanTx!
  // makeBorrowIx и makeRepayIx УДАЛЕНЫ НАВСЕГДА!

  console.log(`🏦 Flash Loan создан: ${amount} ${tokenSymbol}`);
  console.log(`📊 Используйте ТОЛЬКО buildFlashLoanTx!`);

  return {
    bank
  };
};
```

---

## ⚡ 3. ATOMIC TRANSACTION TEMPLATES

### 🔧 Complete Atomic Transaction Template:

```javascript
const buildAtomicTransaction = async (flashLoanAmount, inputMint, outputMint, wallet, connection) => {
  try {
    // 1. Initialize MarginFi
    const { client: marginfiClient, account: marginfiAccount } = await initializeMarginFi(wallet, connection);

    // 2. Get Jupiter quote
    const quote = await getJupiterQuote(inputMint, outputMint, flashLoanAmount);

    // 3. Get Jupiter swap instructions
    const jupiterResult = await getJupiterSwapInstructions(quote, wallet.publicKey);
    const { allInstructions: jupiterInstructions, addressLookupTableAddresses } = processJupiterResponse(jupiterResult);

    // 4. Create MarginFi flash loan
    const marginfiResult = await createFlashLoan(flashLoanAmount, "USDC", marginfiClient, marginfiAccount);

    // 5. Build atomic instruction sequence
    const atomicInstructions = [
      // Compute Budget (Jupiter)
      ...jupiterResult.computeBudgetInstructions,

      // Setup ATA (Jupiter)
      ...jupiterResult.setupInstructions,

      // Flash Loan Borrow (MarginFi - 44 аккаунта)
      ...marginfiResult.borrowInstructions,

      // Swap (Jupiter - 40 аккаунтов)
      jupiterResult.swapInstruction,

      // Flash Loan Repay (MarginFi - 44 аккаунта)
      ...marginfiResult.repayInstructions,

      // Cleanup (Jupiter)
      ...(jupiterResult.cleanupInstruction ? [jupiterResult.cleanupInstruction] : [])
    ];

    console.log(`⚡ Атомарная транзакция: ${atomicInstructions.length} инструкций`);
    console.log(`📊 Общий размер: ~${44 + 40 + 44} аккаунтов`);

    return {
      instructions: atomicInstructions,
      addressLookupTableAddresses,
      marginfiAccount
    };

  } catch (error) {
    console.error('❌ Atomic transaction build error:', error);
    throw error;
  }
};
```

---

## 🗜️ 4. ADDRESS LOOKUP TABLES TEMPLATES

### 📊 ALT Loading Template:

```javascript
const loadAddressLookupTables = async (addresses, connection) => {
  if (!addresses || addresses.length === 0) {
    return [];
  }

  try {
    const addressLookupTableAccountInfos = await connection.getMultipleAccountsInfo(
      addresses.map(addr => new PublicKey(addr))
    );

    const lookupTableAccounts = addressLookupTableAccountInfos.reduce((acc, accountInfo, index) => {
      if (accountInfo) {
        const addressLookupTableAccount = new AddressLookupTableAccount({
          key: new PublicKey(addresses[index]),
          state: AddressLookupTableAccount.deserialize(accountInfo.data)
        });
        acc.push(addressLookupTableAccount);
      }
      return acc;
    }, []);

    console.log(`🗜️ Загружено ALT: ${lookupTableAccounts.length}/${addresses.length}`);
    return lookupTableAccounts;

  } catch (error) {
    console.error('❌ ALT loading error:', error);
    return [];
  }
};
```

### 🔧 Versioned Transaction Template:

```javascript
const createVersionedTransaction = async (instructions, altAddresses, wallet, connection) => {
  try {
    // Get latest blockhash
    const { blockhash } = await connection.getLatestBlockhash();

    // Load Address Lookup Tables
    const addressLookupTableAccounts = await loadAddressLookupTables(altAddresses, connection);

    // Convert instructions to proper format
    const formattedInstructions = instructions.map(ix => ({
      programId: new PublicKey(ix.programId),
      accounts: ix.accounts.map(acc => ({
        pubkey: new PublicKey(acc.pubkey),
        isSigner: acc.isSigner,
        isWritable: acc.isWritable
      })),
      data: Buffer.from(ix.data, 'base64')
    }));

    // Create message
    const messageV0 = new TransactionMessage({
      payerKey: wallet.publicKey,
      recentBlockhash: blockhash,
      instructions: formattedInstructions
    }).compileToV0Message(addressLookupTableAccounts);

    // Create and sign transaction
    const transaction = new VersionedTransaction(messageV0);
    transaction.sign([wallet]);

    // Validate size
    const size = transaction.serialize().length;
    console.log(`📊 Размер транзакции: ${size}/1232 байт`);

    if (size > 1232) {
      throw new Error(`Transaction too large: ${size} > 1232 bytes`);
    }

    return transaction;

  } catch (error) {
    console.error('❌ Versioned transaction creation error:', error);
    throw error;
  }
};
```

---

## 🚨 5. ERROR HANDLING TEMPLATES

### 📊 Validation Template:

```javascript
const validateAtomicTransaction = (instructions, altAddresses) => {
  // Check instructions
  if (!instructions || instructions.length === 0) {
    throw new Error('No instructions provided');
  }

  // Count accounts
  let totalAccounts = 0;
  instructions.forEach(ix => {
    totalAccounts += ix.accounts?.length || 0;
  });

  console.log(`📊 Всего аккаунтов: ${totalAccounts}`);

  // Check if ALT compression needed
  if (totalAccounts > 64 && (!altAddresses || altAddresses.length === 0)) {
    console.warn('⚠️ Много аккаунтов, но нет ALT для сжатия!');
  }

  // Check for System Program Transfer issues
  instructions.forEach((ix, index) => {
    if (ix.programId === '11111111111111111111111111111112' && ix.data) {
      const dataBuffer = Buffer.from(ix.data, 'base64');
      if (dataBuffer.length === 8) {
        console.warn(`⚠️ Инструкция ${index}: System Program Transfer с 8 байт (должно быть 12)`);
      }
    }
  });

  return true;
};
```

### 🔍 Debug Template:

```javascript
const debugAtomicTransaction = (instructions, altAddresses) => {
  console.log('\n🔍 ОТЛАДКА АТОМАРНОЙ ТРАНЗАКЦИИ:');

  instructions.forEach((ix, index) => {
    const accountCount = ix.accounts?.length || 0;
    const dataSize = ix.data ? Buffer.from(ix.data, 'base64').length : 0;
    const programId = ix.programId?.toString().slice(0, 8) + '...';

    console.log(`📍 Инструкция ${index}: ${programId} (${accountCount} аккаунтов, ${dataSize} байт)`);
  });

  console.log(`🗜️ ALT адресов: ${altAddresses?.length || 0}`);
  console.log(`📊 Общий размер: ${instructions.length} инструкций\n`);
};
```
