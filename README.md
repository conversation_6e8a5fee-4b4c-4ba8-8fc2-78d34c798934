# 🚀 SOLANA DEX ARBITRAGE & IDE SETUP

🔴 **DEXSWAP0** - Полноценная система для торговли на Solana с аналогом REMIX IDE для быстрой разработки и отладки контрактов.

## 🎯 SOLANA IDE SETUP (аналог REMIX)

### 1. **Solana Playground (Онлайн IDE)**
🌐 **URL**: https://beta.solpg.io/
- ✅ Браузерная IDE для Solana контрактов
- ✅ Поддержка Rust и Anchor
- ✅ Встроенный компилятор и деплой
- ✅ Тестирование на devnet/mainnet

### 2. **Локальная среда разработки**
```bash
# Установка Solana CLI
sh -c "$(curl -sSfL https://release.solana.com/v1.18.4/install)"

# Установка Anchor Framework
npm install -g @coral-xyz/anchor-cli

# Установка Rust
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
```

### 3. **VS Code Extensions для Solana**
```bash
# Установите эти расширения:
- rust-analyzer
- Solana Snippets
- Anchor Language Support
```

## 🚀 Быстрый старт торговли

```bash
# 1. Установка зависимостей
npm install

# 2. Настройка кошелька (DEVNET для тестов)
cp .env.devnet .env

# 3. Запуск мониторинга цен
npm start

# 4. Запуск арбитража
npm run enhanced
```

## 📊 Поддерживаемые DEX (8 активных)

### ✅ Активные Solana DEX:
- **Jupiter** (агрегатор) - Program ID: `JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4K`
- **Orca** (AMM) - Program ID: `whirLbMiicVdio4qvUfM5KAg6Ct8VwpYzGff3uctyCc`
- **Raydium** (AMM) - Program ID: `675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8`
- **Meteora** (DLMM) - Program ID: `LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo`
- **OpenBook V2** (Orderbook) - Program ID: `opnb2LAfJYbRMAHHvqjCwQxanZn7ReEHp1k81EohpZb`
- **Saber** (Stable Swap) - Program ID: `SSwpkEEcbUqx4vtoEByFjSkhKdCT862DNVb52nZg1UZ`
- **Lifinity** (Proactive MM) - Program ID: `EewxydAPCCVuNEyrVN68PuSYdQ7wKn27V9Gjeoi8dy3S`
- **Aldrin** (AMM) - Program ID: `AMM55ShdkoGRB5jVYPjWziwk8m5MpwyDgsMWHaMSQWH6`

### 🔄 Поддерживаемые токены (15 активных):
- SOL, USDC, WBTC, WETH, USDT
- UNI, AAVE, LINK, MATIC, ADA
- SUSHI, CRV, 1INCH, COMP

## 🔧 Конфигурация для торговли

### Безопасное тестирование (DEVNET):
```bash
cp .env.devnet .env
# Тестовый кошелек с 2 SOL уже настроен
```

### Продакшн (MAINNET):
```bash
cp .env.solana.clean .env
# Настройте свой кошелек и RPC
```

## 📈 Функции системы

### ✅ Готово к использованию:
- 🔄 Мониторинг цен в реальном времени (8 DEX)
- 🎯 Выявление арбитражных возможностей
- 📊 WebSocket подключения к DEX
- 🔗 REST API интеграции
- 🦀 Rust движок для высокой производительности
- 💰 Flash loans через MarginFi
- 🎮 Bundle система для MEV защиты

### 🔧 В разработке:
- ⏳ Автоматическое исполнение сделок
- ⏳ Расширенная MEV защита
- ⏳ Больше DEX интеграций

## 🛠 Технологии

### Core:
- **Node.js** - Основная платформа
- **Rust** - Высокопроизводительный движок
- **Solana Web3.js** - Blockchain интеграция
- **Anchor Framework** - Smart contracts

### DEX SDKs:
- **Jupiter API** - Агрегатор свопов
- **Orca SDK** - AMM протокол  
- **Raydium SDK** - AMM + фермы
- **Meteora SDK** - Dynamic Liquidity
- **OpenBook SDK** - Orderbook DEX

### Инфраструктура:
- **MarginFi** - Flash loans
- **Helius RPC** - Быстрые RPC запросы
- **QuickNode** - Резервный RPC

## 🧪 Тестирование и отладка

### Запуск тестов:
```bash
# Тест всех DEX подключений
npm run test

# Тест конкретного DEX
node solana-flash-loans/test-additional-dex.js

# Тест Rust движка
cd solana-flash-loans/rust-test && cargo run
```

### Отладка ошибок:
```bash
# Проверка безопасности
npm audit

# Исправление уязвимостей
npm audit fix

# Полная переустановка
rm -rf node_modules package-lock.json && npm install
```

## 🔐 Безопасность

⚠️ **ВАЖНО**: Никогда не делитесь приватными ключами!
- Используйте `.env.devnet` для тестов
- Добавьте `.env` в `.gitignore`
- Используйте аппаратные кошельки для больших сумм

## 📞 Поддержка

- 📖 Документация: См. файлы в папке `solana-flash-loans/`
- 🐛 Баги: Создайте issue в репозитории
- 💬 Вопросы: Telegram/Discord сообщество

---

**Статус проекта**: 🟢 Активная разработка
**Последнее обновление**: 2025-06-28
