# 🔐 БЕЗОПАСНАЯ НАСТРОЙКА СИСТЕМЫ

## ⚠️ КРИТИЧЕСКИ ВАЖНО

**НИКОГДА НЕ ДЕЛИТЕСЬ ПРИВАТНЫМИ КЛЮЧАМИ И API КЛЮЧАМИ!**

## 🚀 БЫСТРАЯ НАСТРОЙКА

### 1. Скопируйте файл конфигурации
```bash
cp .env.example .env.solana
```

### 2. Получите RPC endpoints

#### Helius (рекомендуется)
1. Зарегистрируйтесь на https://helius.xyz
2. Создайте новый проект
3. Скопируйте RPC URL с API ключом
4. Вставьте в `HELIUS_RPC_URL`

#### QuickNode (рекомендуется)
1. Зарегистрируйтесь на https://quicknode.com
2. Создайте Solana Mainnet endpoint
3. Скопируйте HTTP URL
4. Вставьте в `QUICKNODE_RPC_URL`

### 3. Настройте кошелек

#### Экспорт приватного ключа из Phantom
1. Откройте Phantom Wallet
2. Настройки → Показать приватный ключ
3. Скопируйте ключ в формате Base58
4. Вставьте в `WALLET_PRIVATE_KEY`

#### Получите адрес кошелька
1. Скопируйте публичный адрес из Phantom
2. Вставьте в `WALLET_ADDRESS`

### 4. Проверьте конфигурацию
```bash
node -e "
const fs = require('fs');
const env = fs.readFileSync('.env.solana', 'utf8');
console.log('✅ Конфигурация загружена');
console.log('🔍 Проверьте, что все ключи заполнены');
"
```

## 🛡️ БЕЗОПАСНОСТЬ

### ✅ ОБЯЗАТЕЛЬНО
- Используйте отдельный кошелек для торговли
- Храните основные средства в холодном кошельке
- Регулярно меняйте API ключи
- Используйте VPN при торговле

### ❌ НИКОГДА НЕ ДЕЛАЙТЕ
- Не коммитьте .env.solana в Git
- Не делитесь приватными ключами
- Не используйте основной кошелек
- Не торгуйте на незнакомых DEX

## 🔧 ПРОВЕРКА ГОТОВНОСТИ

Запустите проверку системы:
```bash
node real-trading-executor.js --check-config
```

Система должна показать:
- ✅ RPC endpoints доступны
- ✅ Кошелек загружен
- ✅ Баланс SOL > 0.1
- ✅ MarginFi готов

## 🚨 В СЛУЧАЕ ПРОБЛЕМ

### RPC ошибки
- Проверьте API ключи
- Убедитесь в наличии средств на аккаунте RPC провайдера
- Попробуйте другой endpoint

### Ошибки кошелька
- Проверьте формат приватного ключа (Base58)
- Убедитесь в наличии SOL для комиссий (минимум 0.1 SOL)
- Проверьте соответствие адреса и ключа

### Ошибки торговли
- Проверьте баланс USDC
- Убедитесь в доступности MarginFi
- Проверьте настройки минимальной прибыли

## 📞 ПОДДЕРЖКА

При возникновении проблем:
1. Проверьте логи в консоли
2. Убедитесь в правильности конфигурации
3. Проверьте баланс кошелька
4. Перезапустите систему
