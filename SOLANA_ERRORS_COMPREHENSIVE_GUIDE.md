# Полное руководство по ошибкам Solana

## HTTP Ошибки

### Общие HTTP коды ошибок

| Код | Сообщение | Объяснение |
|-----|-----------|------------|
| **400** | Bad Request | Неправильный тип HTTP запроса (например, использование GET вместо POST) или недопустимые символы |
| **401** | Unauthorized | Нарушение требований безопасности: неправильный Token Auth, IP не в белом списке, недействительный JWT |
| **403** | Forbidden | Endpoint отключен (возможно из-за просроченного платежа) |
| **403** | Forbidden - custom trace not found | Код custom trace не в белом списке |
| **404** | Not Found | Неправильный URL или неправильный метод |
| **413** | Content Too Large | Тело запроса слишком большое |
| **429** | Too Many Requests | RPS запросов превышает лимиты плана |
| **500** | Internal Server Error | Внутренняя ошибка сервера |
| **503** | Service Unavailable | Сервис недоступен |

## Solana RPC Ошибки

### Основные RPC коды ошибок

| Код | Сообщение | Объяснение |
|-----|-----------|------------|
| **-32002** | Transaction simulation failed: Error processing Instruction X: custom program error: 0xXX | Ошибка preflight проверки - транзакция содержит ошибки |
| **-32002** | Transaction simulation failed: Blockhash not found | Blockhash в транзакции неизвестен валидатору |
| **-32003** | Transaction signature verification failure | Неправильный приватный или публичный ключ |
| **-32004** | Block not available for slot x | Блок недоступен для указанного слота |
| **-32005** | Node is unhealthy Node is behind by xxxx slots | Нода отстает от сети |
| **-32007** | Slot xxxxxx was skipped, or missing due to ledger jump | Запрашиваемый блок не существует |
| **-32009** | Slot xxxxx was skipped, or missing in long-term storage | Слот пропущен или отсутствует в долгосрочном хранилище |
| **-32010** | xxxxxx excluded from account secondary indexes | Неправильные данные в запросе |
| **-32013** | Mismatch in the length of the transaction signature | Неправильная длина подписи |
| **-32014** | Block status for the specified slot is not yet available | Блок еще не готов или нода не синхронизирована |
| **-32015** | Transaction version (0) is not supported | Нужно добавить "maxSupportedTransactionVersion": 0 |
| **-32016** | Minimum context slot has not been reached | Контекстный слот еще не достигнут |
| **-32602** | Invalid params: xxxxx | Неправильные параметры запроса |

## Anchor Framework Ошибки

### Внутренние ошибки Anchor (ErrorCode)

| Диапазон | Описание |
|----------|----------|
| >= 100 | Ошибки инструкций |
| >= 1000 | IDL ошибки |
| >= 2000 | Ошибки ограничений |
| >= 3000 | Ошибки аккаунтов |
| >= 4100 | Разные ошибки |
| = 5000 | Устаревший код ошибки |
| >= 6000 | Пользовательские ошибки |

### Макросы для обработки ошибок

| Макрос | Описание |
|--------|----------|
| `require!` | Проверяет условие, иначе возвращает ошибку |
| `require_eq!` | Проверяет равенство НЕ-PUBKEY значений |
| `require_neq!` | Проверяет неравенство НЕ-PUBKEY значений |
| `require_keys_eq!` | Проверяет равенство pubkey значений |
| `require_keys_neq!` | Проверяет неравенство pubkey значений |
| `require_gt!` | Проверяет что первое значение больше второго |
| `require_gte!` | Проверяет что первое значение больше или равно второму |

## MarginFi Специфические Ошибки

### Критические ошибки MarginFi

| Код | Название | Описание | Решение |
|-----|----------|----------|---------|
| **6027** | BankLiabilityCapacityExceeded | Превышен лимит заимствования банка | Уменьшить сумму займа или сбросить флаг |
| **6037** | AccountInFlashloan | Аккаунт в состоянии flash loan | Дождаться завершения или проверить состояние |
| **6038** | IllegalFlashloan | Недопустимый flash loan | Проверить параметры и разрешения |

### Полный список ошибок MarginFi

| Код | Название | Описание |
|-----|----------|----------|
| 6000 | InternalLogicError | Внутренняя ошибка логики |
| 6001 | BankNotFound | Неверный индекс банка |
| 6002 | LendingAccountBalanceNotFound | Баланс lending аккаунта не найден |
| 6003 | BankAssetCapacityExceeded | Превышена емкость депозитов банка |
| 6004 | InvalidTransfer | Недопустимый перевод |
| 6005 | MissingPythOrBankAccount | Отсутствует Oracle, Bank, LST mint или Sol Pool |
| 6006 | MissingPythAccount | Отсутствует Pyth аккаунт |
| 6007 | MissingBankAccount | Отсутствует Bank аккаунт |
| 6008 | InvalidBankAccount | Недопустимый Bank аккаунт |
| 6009 | RiskEngineInitRejected | Отклонено из-за плохого здоровья или устаревших оракулов |
| 6010 | LendingAccountBalanceSlotsFull | Слоты баланса lending заполнены |
| 6011 | BankAlreadyExists | Банк уже существует |
| 6012 | ZeroLiquidationAmount | Сумма ликвидации должна быть положительной |
| 6013 | AccountNotBankrupt | Аккаунт не банкрот |
| 6014 | BalanceNotBadDebt | Баланс не является плохим долгом |
| 6015 | InvalidConfig | Недопустимая конфигурация группы |
| 6016 | BankPaused | Банк приостановлен |
| 6017 | BankReduceOnly | Банк в режиме только уменьшения |
| 6018 | BankAccountNotFound | Банк отсутствует |
| 6019 | OperationDepositOnly | Операция только депозита |
| 6020 | OperationWithdrawOnly | Операция только вывода |
| 6021 | OperationBorrowOnly | Операция только займа |
| 6022 | OperationRepayOnly | Операция только погашения |
| 6023 | NoAssetFound | Актив не найден |
| 6024 | NoLiabilityFound | Обязательство не найдено |
| 6025 | InvalidOracleSetup | Недопустимая настройка оракула |
| 6026 | IllegalUtilizationRatio | Недопустимый коэффициент использования банка |
| 6027 | BankLiabilityCapacityExceeded | Превышен лимит заимствования банка |
| 6028 | InvalidPrice | Недопустимая цена |
| 6029 | IsolatedAccountIllegalState | Недопустимое состояние изолированного аккаунта |
| 6030 | EmissionsAlreadySetup | Эмиссии уже настроены |
| 6031 | OracleNotSetup | Оракул не настроен |
| 6032 | InvalidSwitchboardDecimalConversion | Недопустимое преобразование Switchboard |
| 6033 | CannotCloseOutstandingEmissions | Нельзя закрыть с невыплаченными эмиссиями |
| 6034 | EmissionsUpdateError | Ошибка обновления эмиссий |
| 6035 | AccountDisabled | Аккаунт отключен |
| 6036 | AccountTempActiveBalanceLimitExceeded | Превышен временный лимит активного баланса |
| 6037 | AccountInFlashloan | Аккаунт в flashloan |
| 6038 | IllegalFlashloan | Недопустимый flashloan |
| 6039 | IllegalFlag | Недопустимый флаг |
| 6040 | IllegalBalanceState | Недопустимое состояние баланса |
| 6041 | IllegalAccountAuthorityTransfer | Недопустимая передача полномочий аккаунта |
| 6042 | Unauthorized | Неавторизован |
| 6043 | IllegalAction | Недопустимое действие |
| 6044 | T22MintRequired | Требуется mint аккаунт для Token22 банка |
| 6045 | InvalidFeeAta | Недопустимый ATA для глобального fee аккаунта |
| 6046 | AddedStakedPoolManually | Ручное добавление пула не разрешено |
| 6047 | AssetTagMismatch | Несоответствие тега актива |
| 6048 | StakePoolValidationFailed | Ошибка валидации stake pool |
| 6049 | SwitchboardStalePrice | Устаревшая цена от Switchboard оракула |
| 6050 | PythPushStalePrice | Устаревшая цена от Pyth Push оракула |
| 6051 | WrongNumberOfOracleAccounts | Неправильное количество оракул аккаунтов |
| 6052 | WrongOracleAccountKeys | Неправильные ключи оракул аккаунтов |
| 6053 | PythPushWrongAccountOwner | Неправильный владелец аккаунта Pyth Push |
| 6054 | StakedPythPushWrongAccountOwner | Неправильный владелец Staked Pyth Push |
| 6055 | PythPushMismatchedFeedId | Несоответствие feed ID в Pyth Push |
| 6056 | PythPushInsufficientVerificationLevel | Недостаточный уровень верификации Pyth Push |
| 6057 | PythPushFeedIdMustBe32Bytes | Feed ID должен быть 32 байта для Pyth Push |
| 6058 | PythPushFeedIdNonHexCharacter | Feed ID содержит не-hex символы |
| 6059 | SwitchboardWrongAccountOwner | Неправильный владелец Switchboard аккаунта |
| 6060 | PythPushInvalidAccount | Недопустимый аккаунт Pyth Push |
| 6061 | SwitchboardInvalidAccount | Недопустимый аккаунт Switchboard |
| 6062 | MathError | Математическая ошибка |
| 6063 | InvalidEmissionsDestinationAccount | Недопустимый аккаунт назначения эмиссий |
| 6064 | SameAssetAndLiabilityBanks | Банки активов и обязательств должны отличаться |
| 6065 | OverliquidationAttempt | Попытка сверхликвидации |
| 6066 | NoLiabilitiesInLiabilityBank | Нет обязательств в банке обязательств |
| 6067 | AssetsInLiabilityBank | Активы в банке обязательств |
| 6068 | HealthyAccount | Здоровый аккаунт |
| 6069 | ExhaustedLiability | Исчерпанное обязательство |
| 6070 | TooSeverePayoff | Слишком серьезное погашение |
| 6071 | TooSevereLiquidation | Слишком серьезная ликвидация |
| 6072 | WorseHealthPostLiquidation | Ухудшение здоровья после ликвидации |
| 6073 | ArenaBankLimit | Группы Arena поддерживают максимум два банка |
| 6074 | ArenaSettingCannotChange | Настройки Arena групп нельзя изменить |

## Системные ошибки Solana

### Ошибки инструкций (Instruction Errors)

| Тип | Описание |
|-----|----------|
| GenericError | Общая ошибка |
| InvalidArgument | Недопустимый аргумент |
| InvalidInstructionData | Недопустимые данные инструкции |
| InvalidAccountData | Недопустимые данные аккаунта |
| AccountDataTooSmall | Данные аккаунта слишком малы |
| InsufficientFunds | Недостаточно средств |
| IncorrectProgramId | Неправильный ID программы |
| MissingRequiredSignature | Отсутствует обязательная подпись |
| AccountAlreadyInitialized | Аккаунт уже инициализирован |
| UninitializedAccount | Неинициализированный аккаунт |
| UnbalancedInstruction | Несбалансированная инструкция |
| ModifiedProgramId | Изменен ID программы |
| ExternalAccountLamportSpend | Внешний расход лампортов аккаунта |
| ExternalAccountDataModified | Изменены внешние данные аккаунта |
| ReadonlyLamportChange | Изменение лампортов в readonly аккаунте |
| ReadonlyDataModified | Изменение данных в readonly аккаунте |
| DuplicateAccountIndex | Дублированный индекс аккаунта |
| ExecutableModified | Изменен исполняемый файл |
| RentEpochModified | Изменена эпоха аренды |
| NotEnoughAccountKeys | Недостаточно ключей аккаунтов |
| AccountDataSizeChanged | Изменен размер данных аккаунта |
| AccountNotExecutable | Аккаунт не исполняемый |
| AccountBorrowFailed | Ошибка заимствования аккаунта |
| AccountBorrowOutstanding | Невыполненное заимствование аккаунта |
| DuplicateAccountOutOfSync | Дублированный аккаунт не синхронизирован |
| Custom(u32) | Пользовательская ошибка с кодом |
| InvalidError | Недопустимая ошибка |
| ExecutableDataModified | Изменены исполняемые данные |
| ExecutableLamportChange | Изменение лампортов исполняемого файла |
| ExecutableAccountNotRentExempt | Исполняемый аккаунт не освобожден от аренды |
| UnsupportedProgramId | Неподдерживаемый ID программы |
| CallDepth | Глубина вызова |
| MissingAccount | Отсутствующий аккаунт |
| ReentrancyNotAllowed | Реентерабельность не разрешена |
| MaxSeedLengthExceeded | Превышена максимальная длина seed |
| InvalidSeeds | Недопустимые seeds |
| InvalidRealloc | Недопустимое перераспределение |
| ComputationalBudgetExceeded | Превышен вычислительный бюджет |
| PrivilegeEscalation | Эскалация привилегий |
| ProgramEnvironmentSetupFailure | Ошибка настройки среды программы |
| ProgramFailedToComplete | Программа не смогла завершиться |
| ProgramFailedToCompile | Программа не смогла скомпилироваться |
| Immutable | Неизменяемый |
| IncorrectAuthority | Неправильные полномочия |
| BorrowFailed | Ошибка заимствования |
| AccountNotRentExempt | Аккаунт не освобожден от аренды |
| InvalidAccountOwner | Недопустимый владелец аккаунта |
| ArithmeticOverflow | Арифметическое переполнение |
| UnsupportedSysvar | Неподдерживаемый sysvar |
| IllegalOwner | Недопустимый владелец |
| MaxAccountsDataAllocationsExceeded | Превышено максимальное выделение данных аккаунтов |
| MaxAccountsExceeded | Превышено максимальное количество аккаунтов |
| MaxInstructionTraceLengthExceeded | Превышена максимальная длина трассировки инструкций |
| BuiltinProgramsMustConsumeComputeUnits | Встроенные программы должны потреблять вычислительные единицы |

## Рекомендации по обработке ошибок

### Общие принципы

1. **Всегда проверяйте preflight** - используйте симуляцию транзакций
2. **Обрабатывайте retry логику** - для временных ошибок сети
3. **Логируйте детали ошибок** - сохраняйте полную информацию для отладки
4. **Используйте типизированные ошибки** - в Anchor программах
5. **Проверяйте состояние аккаунтов** - перед выполнением операций

### Специфичные для MarginFi

1. **Ошибка 6027** - проверить флаги аккаунта и сбросить при необходимости
2. **Flash loan ошибки** - убедиться в правильной последовательности инструкций
3. **Oracle ошибки** - проверить актуальность данных оракулов
4. **Capacity ошибки** - проверить лимиты банков перед операциями

### Инструменты для диагностики

1. **Solana Explorer** - для просмотра транзакций и ошибок
2. **RPC логи** - детальная информация об ошибках
3. **Anchor IDL** - для декодирования пользовательских ошибок
4. **solana-verify** - для верификации программ

## Jupiter API Ошибки

### Основные ошибки Jupiter

| Код | Описание | Решение |
|-----|----------|---------|
| **0x1771** | Превышена толерантность к проскальзыванию | Увеличить slippage или повторить запрос |
| **429** | Слишком много запросов (Rate Limit) | Реализовать retry логику с задержками |
| **Transaction timeout** | Транзакция не подтверждена за 60 секунд | Увеличить priority fee или использовать retry |
| **Blockhash invalid** | Недействительный blockhash | Использовать processed commitment |
| **No route found** | Маршрут для свапа не найден | Проверить ликвидность пары |

### Параметры для решения проблем Jupiter

```javascript
// Автоматическая установка priority fee
prioritizationFeeLamports: "auto"

// Увеличение multiplier для высокой конкуренции
prioritizationFeeLamports: {
  autoMultiplier: 2
}

// Динамический лимит compute units
dynamicComputeUnitLimit: true

// Настройки отправки транзакции
const txid = await connection.sendRawTransaction(rawTransaction, {
  skipPreflight: true,
  maxRetries: 2,
  preflightCommitment: 'processed'
});
```

## DEX Специфические Ошибки

### Raydium Ошибки

| Ошибка | Описание | Решение |
|--------|----------|---------|
| Pool not found | Пул не найден для токена | Проверить существование пула на Raydium |
| Undefined is not an object | Ошибка при вставке адреса токена | Проверить корректность адреса токена |
| Insufficient liquidity | Недостаточная ликвидность | Уменьшить размер свапа |

### Orca Ошибки

| Ошибка | Описание | Решение |
|--------|----------|---------|
| Whirlpool not found | Whirlpool не найден | Проверить существование пула |
| Price impact too high | Слишком высокое влияние на цену | Уменьшить размер свапа |
| Tick out of bounds | Тик вне границ | Проверить диапазон цен |

### Meteora Ошибки

| Ошибка | Описание | Решение |
|--------|----------|---------|
| Pool validation failed | Ошибка валидации пула | Проверить параметры пула |
| Dynamic pool error | Ошибка динамического пула | Обновить состояние пула |
| Fee calculation error | Ошибка расчета комиссии | Проверить настройки комиссий |

## Ошибки Flash Loans

### Общие ошибки Flash Loans

| Ошибка | Описание | Решение |
|--------|----------|---------|
| Flash loan not repaid | Flash loan не погашен | Убедиться в погашении в той же транзакции |
| Insufficient collateral | Недостаточное обеспечение | Добавить больше обеспечения |
| Health factor too low | Слишком низкий фактор здоровья | Улучшить позицию |
| Flash loan flag stuck | Застрявший флаг flash loan | Использовать специальный скрипт сброса |

### MarginFi Flash Loan Ошибки

| Код | Название | Описание | Решение |
|-----|----------|----------|---------|
| 6037 | AccountInFlashloan | Аккаунт в состоянии flash loan | Завершить flash loan правильно |
| 6038 | IllegalFlashloan | Недопустимый flash loan | Проверить последовательность инструкций |
| 6039 | IllegalFlag | Недопустимый флаг | Сбросить флаги аккаунта |

## Ошибки Address Lookup Tables (ALT)

### Ошибки ALT

| Ошибка | Описание | Решение |
|--------|----------|---------|
| ALT not found | Таблица адресов не найдена | Проверить существование ALT |
| ALT account not writable | ALT аккаунт не доступен для записи | Проверить права доступа |
| Invalid ALT index | Недопустимый индекс ALT | Проверить индексы в ALT |
| ALT deactivation period | Период деактивации ALT | Дождаться завершения периода |

## Compute Unit Ошибки

### Ошибки вычислительных единиц

| Ошибка | Описание | Решение |
|--------|----------|---------|
| ComputationalBudgetExceeded | Превышен вычислительный бюджет | Увеличить compute unit limit |
| Insufficient compute units | Недостаточно compute units | Оптимизировать инструкции |
| Priority fee too low | Слишком низкая priority fee | Увеличить priority fee |

## Сетевые ошибки

### Ошибки RPC соединения

| Ошибка | Описание | Решение |
|--------|----------|---------|
| Connection timeout | Таймаут соединения | Использовать другой RPC endpoint |
| RPC node behind | RPC нода отстает | Переключиться на другую ноду |
| Rate limit exceeded | Превышен лимит запросов | Реализовать rate limiting |
| Invalid RPC response | Недопустимый ответ RPC | Проверить формат запроса |

## Стратегии обработки ошибок

### Retry стратегии

```javascript
// Экспоненциальная задержка
const retryWithBackoff = async (fn, maxRetries = 3) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      await new Promise(resolve =>
        setTimeout(resolve, Math.pow(2, i) * 1000)
      );
    }
  }
};

// Специфичная обработка ошибок
const handleSolanaError = (error) => {
  if (error.code === -32002) {
    // Preflight error - проверить параметры
    return 'PREFLIGHT_ERROR';
  } else if (error.code === 429) {
    // Rate limit - подождать и повторить
    return 'RATE_LIMIT';
  } else if (error.message.includes('0x1771')) {
    // Jupiter slippage error
    return 'SLIPPAGE_ERROR';
  }
  return 'UNKNOWN_ERROR';
};
```

### Мониторинг и логирование

```javascript
// Структурированное логирование ошибок
const logError = (error, context) => {
  console.error({
    timestamp: new Date().toISOString(),
    error: {
      code: error.code,
      message: error.message,
      stack: error.stack
    },
    context: {
      transaction: context.txId,
      instruction: context.instruction,
      accounts: context.accounts
    }
  });
};
```

## Заключение

Данное руководство покрывает основные типы ошибок в экосистеме Solana, включая:

- HTTP и RPC ошибки
- Anchor framework ошибки
- MarginFi специфические ошибки
- Jupiter и другие DEX ошибки
- Flash loan ошибки
- ALT и compute unit ошибки
- Стратегии обработки ошибок

Для получения актуальной информации всегда обращайтесь к официальной документации и исходному коду программ. Регулярно обновляйте свои знания, так как экосистема Solana быстро развивается.
