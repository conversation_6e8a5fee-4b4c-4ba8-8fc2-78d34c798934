/**
 * 🔥 ADVANCED WALLET MONITOR - ПРОДВИНУТЫЙ МОНИТОРИНГ С АНАЛИЗОМ ТРАНЗАКЦИЙ
 * ═══════════════════════════════════════════════════════════════════════════════
 * 
 * 🎯 ВОЗМОЖНОСТИ:
 * - Мониторинг в реальном времени через WebSocket
 * - Анализ типов транзакций (DEX, Transfer, Program calls)
 * - Отслеживание изменений токенов
 * - Уведомления о крупных операциях
 * - Детальная статистика
 */

const { Connection, PublicKey } = require('@solana/web3.js');
const { getAssociatedTokenAddress, TOKEN_PROGRAM_ID } = require('@solana/spl-token');
const colors = require('colors');

class AdvancedWalletMonitor {
    constructor(walletAddress) {
        console.log('🔥 ADVANCED WALLET MONITOR ИНИЦИАЛИЗАЦИЯ...'.red.bold);
        
        this.walletAddress = walletAddress || 'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV';
        this.walletPublicKey = new PublicKey(this.walletAddress);
        
        // 🔗 МНОЖЕСТВЕННЫЕ RPC ДЛЯ НАДЕЖНОСТИ
        this.connections = this.initializeConnections();
        
        // 🪙 ТОКЕНЫ ДЛЯ МОНИТОРИНГА
        this.monitoredTokens = {
            'So11111111111111111111111111111111111111112': { symbol: 'SOL', decimals: 9, name: 'Solana' },
            'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v': { symbol: 'USDC', decimals: 6, name: 'USD Coin' },
            'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB': { symbol: 'USDT', decimals: 6, name: 'Tether USD' }
        };
        
        // 📊 РАСШИРЕННАЯ СТАТИСТИКА
        this.stats = {
            totalTransactions: 0,
            dexTransactions: 0,
            transferTransactions: 0,
            programTransactions: 0,
            tokenChanges: 0,
            largeTransactions: 0,
            startTime: Date.now(),
            lastActivity: null,
            balanceHistory: []
        };
        
        // 🔔 ПОДПИСКИ И СОСТОЯНИЕ
        this.subscriptions = new Map();
        this.currentBalances = new Map();
        this.isMonitoring = false;
        
        console.log(`🎯 Мониторинг кошелька: ${this.walletAddress}`.cyan);
        console.log(`🪙 Отслеживаемые токены: ${Object.keys(this.monitoredTokens).length}`.green);
    }

    /**
     * 🔗 ИНИЦИАЛИЗАЦИЯ ПОДКЛЮЧЕНИЙ
     */
    initializeConnections() {
        const connections = {};
        
        // Helius (если есть ключ)
        if (process.env.HELIUS_RPC_URL) {
            connections.helius = new Connection(process.env.HELIUS_RPC_URL, {
                wsEndpoint: process.env.HELIUS_WS_URL,
                commitment: 'confirmed'
            });
        }
        
        // QuickNode (если есть ключ)
        if (process.env.QUICKNODE_RPC_URL) {
            connections.quicknode = new Connection(process.env.QUICKNODE_RPC_URL, {
                wsEndpoint: process.env.QUICKNODE_WS_URL,
                commitment: 'confirmed'
            });
        }
        
        // Публичный Solana RPC (всегда доступен)
        connections.solana = new Connection('https://api.mainnet-beta.solana.com', {
            wsEndpoint: 'wss://api.mainnet-beta.solana.com',
            commitment: 'confirmed'
        });
        
        console.log(`🔗 Инициализировано ${Object.keys(connections).length} RPC подключений`.green);
        return connections;
    }

    /**
     * 🚀 ЗАПУСК МОНИТОРИНГА
     */
    async start() {
        if (this.isMonitoring) {
            console.log('⚠️ Мониторинг уже запущен!'.yellow);
            return;
        }
        
        console.log('\n🚀 ЗАПУСК ADVANCED WALLET MONITORING...'.yellow.bold);
        
        try {
            // 📊 ПОЛУЧЕНИЕ НАЧАЛЬНЫХ БАЛАНСОВ
            await this.getInitialBalances();
            
            // 📡 ПОДПИСКА НА ОСНОВНОЙ АККАУНТ
            await this.subscribeToMainAccount();
            
            // 🪙 ПОДПИСКА НА ТОКЕН АККАУНТЫ
            await this.subscribeToTokenAccounts();
            
            // 📝 ПОДПИСКА НА ЛОГИ ПРОГРАММ
            await this.subscribeToProgramLogs();
            
            // 📊 ЗАПУСК ОТЧЕТНОСТИ
            this.startReporting();
            
            this.isMonitoring = true;
            console.log('✅ МОНИТОРИНГ АКТИВЕН! Ожидаем активность...'.green.bold);
            
        } catch (error) {
            console.error('❌ Ошибка запуска мониторинга:', error);
        }
    }

    /**
     * 📊 ПОЛУЧЕНИЕ НАЧАЛЬНЫХ БАЛАНСОВ
     */
    async getInitialBalances() {
        console.log('📊 Получение начальных балансов...'.cyan);
        
        try {
            const connection = Object.values(this.connections)[0];
            
            // SOL баланс
            const solBalance = await connection.getBalance(this.walletPublicKey);
            this.currentBalances.set('SOL', solBalance / 1e9);
            
            // Токен балансы
            for (const [mint, tokenInfo] of Object.entries(this.monitoredTokens)) {
                if (mint === 'So11111111111111111111111111111111111111112') continue; // SOL уже получен
                
                try {
                    const tokenAccount = await getAssociatedTokenAddress(
                        new PublicKey(mint),
                        this.walletPublicKey
                    );
                    
                    const tokenBalance = await connection.getTokenAccountBalance(tokenAccount);
                    const balance = tokenBalance.value.uiAmount || 0;
                    this.currentBalances.set(tokenInfo.symbol, balance);
                    
                } catch (error) {
                    // Токен аккаунт может не существовать
                    this.currentBalances.set(tokenInfo.symbol, 0);
                }
            }
            
            console.log('💰 Начальные балансы:'.green);
            for (const [token, balance] of this.currentBalances) {
                console.log(`   ${token}: ${balance}`.cyan);
            }
            
        } catch (error) {
            console.error('❌ Ошибка получения балансов:', error.message);
        }
    }

    /**
     * 📡 ПОДПИСКА НА ОСНОВНОЙ АККАУНТ
     */
    async subscribeToMainAccount() {
        console.log('📡 Подписка на основной аккаунт...'.cyan);
        
        for (const [name, connection] of Object.entries(this.connections)) {
            try {
                const subscriptionId = connection.onAccountChange(
                    this.walletPublicKey,
                    (accountInfo, context) => {
                        this.handleMainAccountChange(name, accountInfo, context);
                    },
                    'confirmed'
                );
                
                this.subscriptions.set(`main_${name}`, subscriptionId);
                console.log(`✅ Подписка на основной аккаунт через ${name}`.green);
                
            } catch (error) {
                console.error(`❌ Ошибка подписки через ${name}:`, error.message);
            }
        }
    }

    /**
     * 🪙 ПОДПИСКА НА ТОКЕН АККАУНТЫ
     */
    async subscribeToTokenAccounts() {
        console.log('🪙 Подписка на токен аккаунты...'.cyan);
        
        for (const [mint, tokenInfo] of Object.entries(this.monitoredTokens)) {
            if (mint === 'So11111111111111111111111111111111111111112') continue; // SOL не токен аккаунт
            
            try {
                const tokenAccount = await getAssociatedTokenAddress(
                    new PublicKey(mint),
                    this.walletPublicKey
                );
                
                for (const [name, connection] of Object.entries(this.connections)) {
                    try {
                        const subscriptionId = connection.onAccountChange(
                            tokenAccount,
                            (accountInfo, context) => {
                                this.handleTokenAccountChange(name, tokenInfo, accountInfo, context);
                            },
                            'confirmed'
                        );
                        
                        this.subscriptions.set(`token_${tokenInfo.symbol}_${name}`, subscriptionId);
                        
                    } catch (error) {
                        console.error(`❌ Ошибка подписки на ${tokenInfo.symbol} через ${name}:`, error.message);
                    }
                }
                
                console.log(`✅ Подписка на ${tokenInfo.symbol} токен аккаунт`.green);
                
            } catch (error) {
                console.error(`❌ Ошибка создания подписки на ${tokenInfo.symbol}:`, error.message);
            }
        }
    }

    /**
     * 📝 ПОДПИСКА НА ЛОГИ ПРОГРАММ
     */
    async subscribeToProgramLogs() {
        console.log('📝 Подписка на логи программ...'.cyan);
        
        // Основные программы для мониторинга
        const programs = [
            'JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4', // Jupiter
            'whirLbMiicVdio4qvUfM5KAg6Ct8VwpYzGff3uctyCc', // Orca Whirlpools
            '675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8', // Raydium
            'MERLuDFBMmsHnsBPZw2sDQZHvXFMwp8EdjudcU2HKky'  // Meteora
        ];
        
        for (const programId of programs) {
            for (const [name, connection] of Object.entries(this.connections)) {
                try {
                    const subscriptionId = connection.onLogs(
                        new PublicKey(programId),
                        (logs, context) => {
                            this.handleProgramLogs(name, programId, logs, context);
                        },
                        'confirmed'
                    );
                    
                    this.subscriptions.set(`logs_${programId}_${name}`, subscriptionId);
                    
                } catch (error) {
                    console.error(`❌ Ошибка подписки на логи ${programId} через ${name}:`, error.message);
                }
            }
        }
        
        console.log(`✅ Подписка на логи ${programs.length} программ`.green);
    }

    /**
     * 🔄 ОБРАБОТКА ИЗМЕНЕНИЙ ОСНОВНОГО АККАУНТА
     */
    handleMainAccountChange(rpcName, accountInfo, context) {
        const timestamp = new Date().toISOString();
        const newBalance = accountInfo.lamports / 1e9;
        const oldBalance = this.currentBalances.get('SOL') || 0;
        const change = newBalance - oldBalance;
        
        console.log('\n🔄 ИЗМЕНЕНИЕ SOL БАЛАНСА!'.yellow.bold);
        console.log(`📡 RPC: ${rpcName}`.cyan);
        console.log(`⏰ ${timestamp}`.gray);
        console.log(`💰 Старый баланс: ${oldBalance.toFixed(6)} SOL`.red);
        console.log(`💰 Новый баланс: ${newBalance.toFixed(6)} SOL`.green);
        console.log(`📈 Изменение: ${change > 0 ? '+' : ''}${change.toFixed(6)} SOL`.magenta.bold);
        
        // Обновляем баланс
        this.currentBalances.set('SOL', newBalance);
        this.stats.totalTransactions++;
        this.stats.lastActivity = timestamp;
        
        // Проверка на крупную транзакцию
        if (Math.abs(change) > 1) { // Больше 1 SOL
            console.log('🚨 КРУПНАЯ ТРАНЗАКЦИЯ ОБНАРУЖЕНА!'.red.bold);
            this.stats.largeTransactions++;
        }
        
        // Сохраняем в историю
        this.stats.balanceHistory.push({
            timestamp,
            token: 'SOL',
            oldBalance,
            newBalance,
            change,
            rpc: rpcName
        });
    }

    /**
     * 🪙 ОБРАБОТКА ИЗМЕНЕНИЙ ТОКЕН АККАУНТОВ
     */
    handleTokenAccountChange(rpcName, tokenInfo, accountInfo, context) {
        const timestamp = new Date().toISOString();
        
        console.log(`\n🪙 ИЗМЕНЕНИЕ ${tokenInfo.symbol} БАЛАНСА!`.blue.bold);
        console.log(`📡 RPC: ${rpcName}`.cyan);
        console.log(`⏰ ${timestamp}`.gray);
        
        // Здесь нужно парсить данные токен аккаунта
        // Для простоты показываем что изменение произошло
        console.log(`🔄 Токен аккаунт ${tokenInfo.symbol} изменился`.yellow);
        
        this.stats.tokenChanges++;
        this.stats.lastActivity = timestamp;
    }

    /**
     * 📝 ОБРАБОТКА ЛОГОВ ПРОГРАММ
     */
    handleProgramLogs(rpcName, programId, logs, context) {
        const timestamp = new Date().toISOString();
        
        // Определяем тип программы
        const programNames = {
            'JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4': 'Jupiter',
            'whirLbMiicVdio4qvUfM5KAg6Ct8VwpYzGff3uctyCc': 'Orca',
            '675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8': 'Raydium',
            'MERLuDFBMmsHnsBPZw2sDQZHvXFMwp8EdjudcU2HKky': 'Meteora'
        };
        
        const programName = programNames[programId] || 'Unknown';
        
        console.log(`\n📝 АКТИВНОСТЬ ${programName}!`.magenta.bold);
        console.log(`📡 RPC: ${rpcName}`.cyan);
        console.log(`⏰ ${timestamp}`.gray);
        console.log(`🎰 Слот: ${context.slot}`.blue);
        
        if (logs.err) {
            console.log(`❌ Ошибка транзакции:`.red, logs.err);
        } else {
            console.log(`✅ Успешная транзакция в ${programName}`.green);
            this.stats.dexTransactions++;
        }
        
        this.stats.programTransactions++;
        this.stats.lastActivity = timestamp;
    }

    /**
     * 📊 ЗАПУСК ОТЧЕТНОСТИ
     */
    startReporting() {
        // Краткий отчет каждые 30 секунд
        setInterval(() => {
            if (this.stats.lastActivity) {
                const timeSinceActivity = Date.now() - new Date(this.stats.lastActivity).getTime();
                const minutesSinceActivity = Math.floor(timeSinceActivity / 60000);
                
                console.log(`\n📊 Последняя активность: ${minutesSinceActivity} минут назад`.blue);
            }
        }, 30000);
        
        // Полный отчет каждые 5 минут
        setInterval(() => {
            this.printFullStats();
        }, 300000);
    }

    /**
     * 📊 ПОЛНАЯ СТАТИСТИКА
     */
    printFullStats() {
        const uptime = Math.floor((Date.now() - this.stats.startTime) / 1000);
        const hours = Math.floor(uptime / 3600);
        const minutes = Math.floor((uptime % 3600) / 60);
        
        console.log('\n📊 ПОЛНАЯ СТАТИСТИКА МОНИТОРИНГА'.blue.bold);
        console.log('═'.repeat(50).blue);
        console.log(`⏰ Время работы: ${hours}ч ${minutes}м`.gray);
        console.log(`📝 Всего событий: ${this.stats.totalTransactions}`.cyan);
        console.log(`🔄 DEX транзакции: ${this.stats.dexTransactions}`.green);
        console.log(`🪙 Изменения токенов: ${this.stats.tokenChanges}`.yellow);
        console.log(`📋 Программные вызовы: ${this.stats.programTransactions}`.magenta);
        console.log(`🚨 Крупные транзакции: ${this.stats.largeTransactions}`.red);
        console.log(`🔔 Активных подписок: ${this.subscriptions.size}`.blue);
        
        console.log('\n💰 ТЕКУЩИЕ БАЛАНСЫ:'.green.bold);
        for (const [token, balance] of this.currentBalances) {
            console.log(`   ${token}: ${balance}`.cyan);
        }
        console.log('═'.repeat(50).blue);
    }

    /**
     * 🛑 ОСТАНОВКА МОНИТОРИНГА
     */
    async stop() {
        console.log('\n🛑 ОСТАНОВКА МОНИТОРИНГА...'.red.bold);
        
        for (const [name, subscriptionId] of this.subscriptions) {
            try {
                const [type, ...parts] = name.split('_');
                const rpcName = parts[parts.length - 1];
                const connection = this.connections[rpcName];
                
                if (type === 'main' || type === 'token') {
                    await connection.removeAccountChangeListener(subscriptionId);
                } else if (type === 'logs') {
                    await connection.removeOnLogsListener(subscriptionId);
                }
                
            } catch (error) {
                console.error(`❌ Ошибка отмены подписки ${name}:`, error.message);
            }
        }
        
        this.subscriptions.clear();
        this.isMonitoring = false;
        
        // Финальная статистика
        this.printFullStats();
        console.log('🛑 Мониторинг остановлен'.red);
    }
}

// 🚀 ЗАПУСК
async function main() {
    const walletAddress = process.argv[2] || 'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV';
    const monitor = new AdvancedWalletMonitor(walletAddress);
    
    await monitor.start();
    
    // Обработка выхода
    process.on('SIGINT', async () => {
        await monitor.stop();
        process.exit(0);
    });
    
    console.log('\n💡 Нажмите Ctrl+C для остановки'.yellow);
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = AdvancedWalletMonitor;
