/**
 * 🔍 ПРАВИЛЬНЫЙ АНАЛИЗ СТРУКТУРЫ METEORA add_liquidity_by_strategy
 */

// ОРИГИНАЛЬНЫЕ ДАННЫЕ ИЗ РАБОЧИХ ТРАНЗАКЦИЙ
const data1 = '03dd95da6f8d76d50000000000000000e803000000000000f1eeffff00000000f0eeffff00000000060000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000100';

const data2 = '03dd95da6f8d76d50000000000000000286411420f000000f7eeffff03000000f6eefffff6eeffff06000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000200000000000100';

console.log('🔍 ПРАВИЛЬНЫЙ АНАЛИЗ СТРУКТУРЫ METEORA add_liquidity_by_strategy');
console.log('=' .repeat(80));

function analyzeMeteoraStructure(hexData, name) {
    const buffer = Buffer.from(hexData, 'hex');
    console.log(`\n📋 ${name}:`);
    console.log(`   Общий размер: ${buffer.length} bytes`);
    console.log(`   Hex: ${hexData.slice(0, 64)}...`);
    console.log('');
    
    let offset = 0;
    
    // 1. Discriminator (8 bytes)
    const discriminator = buffer.slice(offset, offset + 8);
    console.log(`   [${offset.toString().padStart(2)}-${(offset+7).toString().padStart(2)}] Discriminator: ${discriminator.toString('hex')} (add_liquidity_by_strategy2)`);
    offset += 8;
    
    // 2. Amount X (8 bytes)
    const amountX = buffer.readBigUInt64LE(offset);
    console.log(`   [${offset.toString().padStart(2)}-${(offset+7).toString().padStart(2)}] Amount X: ${amountX} (${amountX === 0n ? 'ZERO' : 'NON-ZERO'})`);
    offset += 8;
    
    // 3. Amount Y (8 bytes)
    const amountY = buffer.readBigUInt64LE(offset);
    console.log(`   [${offset.toString().padStart(2)}-${(offset+7).toString().padStart(2)}] Amount Y: ${amountY} (${amountY === 0n ? 'ZERO' : 'NON-ZERO'})`);
    offset += 8;
    
    // 4. Active ID (4 bytes)
    const activeId = buffer.readInt32LE(offset);
    console.log(`   [${offset.toString().padStart(2)}-${(offset+3).toString().padStart(2)}] Active ID: ${activeId} (bin ID)`);
    offset += 4;
    
    // 5. Max Slippage (4 bytes)
    const maxSlippage = buffer.readUInt32LE(offset);
    console.log(`   [${offset.toString().padStart(2)}-${(offset+3).toString().padStart(2)}] Max Slippage: ${maxSlippage}`);
    offset += 4;
    
    console.log(`\n   🔍 АНАЛИЗ ДОПОЛНИТЕЛЬНЫХ ПОЛЕЙ (${buffer.length - offset} bytes):`);
    
    // Анализируем остальные поля
    const fields = [];
    while (offset < buffer.length) {
        if (offset + 4 <= buffer.length) {
            const value = buffer.readUInt32LE(offset);
            const isImportant = value !== 0 && value !== 0xffffffff;
            fields.push({
                offset,
                value,
                hex: value.toString(16).padStart(8, '0'),
                important: isImportant,
                description: getFieldDescription(offset - 32, value)
            });
            offset += 4;
        } else {
            // Остаток меньше 4 bytes
            const remaining = buffer.slice(offset);
            fields.push({
                offset,
                value: remaining.toString('hex'),
                hex: remaining.toString('hex'),
                important: remaining.some(b => b !== 0),
                description: 'Остаток данных'
            });
            break;
        }
    }
    
    // Выводим поля
    fields.forEach(field => {
        const importance = field.important ? '🔥 ВАЖНО' : '⚪ ZERO/PADDING';
        console.log(`   [${field.offset.toString().padStart(2)}] 0x${field.hex}: ${field.value} - ${field.description} ${importance}`);
    });
    
    // Статистика
    const importantFields = fields.filter(f => f.important);
    console.log(`\n   📊 Статистика: ${importantFields.length} важных полей из ${fields.length}`);
    
    return { fields, importantFields };
}

function getFieldDescription(relativeOffset, value) {
    // Описания полей на основе анализа Meteora DLMM
    const descriptions = {
        0: 'Bin ID Low',
        4: 'Bin ID High', 
        8: 'Bin Count',
        12: 'Reserved 1',
        16: 'Reserved 2',
        20: 'Reserved 3',
        24: 'Reserved 4',
        28: 'Reserved 5',
        32: 'Reserved 6',
        36: 'Reserved 7',
        40: 'Reserved 8',
        44: 'Reserved 9',
        48: 'Reserved 10',
        52: 'Reserved 11',
        56: 'Reserved 12',
        60: 'Reserved 13',
        64: 'Reserved 14',
        68: 'Reserved 15',
        72: 'Strategy Flags',
        76: 'Strategy Config'
    };
    
    const desc = descriptions[relativeOffset] || `Unknown offset ${relativeOffset}`;
    
    // Специальные значения
    if (value === 6) return `${desc} (BIN_COUNT=6)`;
    if (value === 0xffffeef0 || value === 0xffffeef6 || value === 0xffffeef7) return `${desc} (BIN_ID)`;
    if (value === 0x00010000) return `${desc} (FLAG)`;
    if (value === 0x00000200) return `${desc} (FLAG)`;
    if (value === 0x01000000) return `${desc} (FLAG)`;
    
    return desc;
}

// Анализируем обе структуры
const analysis1 = analyzeMeteoraStructure(data1, 'POOL 1 STRUCTURE');
const analysis2 = analyzeMeteoraStructure(data2, 'POOL 2 STRUCTURE');

console.log('\n🔍 СРАВНИТЕЛЬНЫЙ АНАЛИЗ:');
console.log('=' .repeat(80));

// Находим общие важные поля
const commonImportantOffsets = analysis1.importantFields
    .filter(f1 => analysis2.importantFields.some(f2 => f2.offset === f1.offset))
    .map(f => f.offset);

console.log('🔥 ОБЩИЕ ВАЖНЫЕ ПОЛЯ (присутствуют в обеих структурах):');
commonImportantOffsets.forEach(offset => {
    const field1 = analysis1.fields.find(f => f.offset === offset);
    const field2 = analysis2.fields.find(f => f.offset === offset);
    console.log(`   Offset ${offset}: Pool1=0x${field1.hex}, Pool2=0x${field2.hex} - ${field1.description}`);
});

console.log('\n💡 ВЫВОДЫ ДЛЯ ОПТИМИЗАЦИИ:');
console.log('=' .repeat(80));

// Анализируем возможности оптимизации
const totalImportantFields = new Set([
    ...analysis1.importantFields.map(f => f.offset),
    ...analysis2.importantFields.map(f => f.offset)
]).size;

const totalZeroFields = analysis1.fields.filter(f => !f.important).length;

console.log(`📊 Важных полей: ${totalImportantFields}`);
console.log(`📊 Нулевых полей: ${totalZeroFields}`);
console.log(`📊 Потенциальная экономия: ${totalZeroFields * 4} bytes (если убрать только нули)`);

// Рекомендации
console.log('\n🎯 РЕКОМЕНДАЦИИ:');

if (commonImportantOffsets.includes(40)) { // offset 40 = bin count
    console.log('⚠️  НЕ УДАЛЯТЬ: Bin Count (offset 40) - критично для ликвидности');
}

if (commonImportantOffsets.includes(32) || commonImportantOffsets.includes(36)) {
    console.log('⚠️  НЕ УДАЛЯТЬ: Bin IDs (offset 32,36) - критично для позиционирования');
}

const safeToRemoveCount = analysis1.fields.filter(f => 
    !f.important && 
    f.offset > 44 && // После важных полей
    f.value === 0
).length;

console.log(`✅ БЕЗОПАСНО УДАЛИТЬ: ${safeToRemoveCount} нулевых полей в конце (${safeToRemoveCount * 4} bytes)`);

// Минимальная безопасная структура
const minSafeSize = 32 + (commonImportantOffsets.length * 4);
console.log(`📏 МИНИМАЛЬНЫЙ БЕЗОПАСНЫЙ РАЗМЕР: ${minSafeSize} bytes`);
console.log(`📏 ТЕКУЩИЙ РАЗМЕР: ${analysis1.fields.length * 4 + 32} bytes`);
console.log(`💰 МАКСИМАЛЬНАЯ БЕЗОПАСНАЯ ЭКОНОМИЯ: ${(analysis1.fields.length * 4 + 32) - minSafeSize} bytes`);

console.log('\n🚨 ВНИМАНИЕ:');
console.log('   Meteora DLMM может требовать точную структуру!');
console.log('   Рекомендуется тестирование перед применением оптимизации!');
console.log('   Лучше оптимизировать другие инструкции!');
