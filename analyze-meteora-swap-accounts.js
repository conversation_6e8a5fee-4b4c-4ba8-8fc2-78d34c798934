/**
 * 🔍 ДЕТАЛЬНЫЙ АНАЛИЗ АККАУНТОВ В METEORA SWAP ИНСТРУКЦИЯХ
 * Проверяем какие именно аккаунты используются в наших Meteora swaps
 */

const { Connection, PublicKey, Keypair } = require('@solana/web3.js');
const { getAssociatedTokenAddress } = require('@solana/spl-token');
require('dotenv').config({ path: '.env.solana' });

// Импортируем наш РАБОТАЮЩИЙ Meteora SDK (ТОЧНО ТОТ ЖЕ ЧТО В BMeteora.js!)
const MeteoraHybridImplementation = require('./meteora-hybrid-implementation.js');

async function analyzeMeteorSwapAccounts() {
    console.log('🔍 ДЕТАЛЬНЫЙ АНАЛИЗ АККАУНТОВ В METEORA SWAP ИНСТРУКЦИЯХ');
    console.log('=' .repeat(80));

    try {
        // 1. Подключение к RPC
        const connection = new Connection(process.env.QUICKNODE_RPC_URL);
        const wallet = Keypair.generate(); // Dummy wallet
        console.log('✅ Подключение к RPC установлено');

        // 2. Инициализация Meteora SDK (ТОЧНО ТАК ЖЕ КАК В BMeteora.js!)
        const meteoraSDK = new MeteoraHybridImplementation(connection, wallet);
        console.log('✅ Meteora SDK инициализирован');

        // Инициализируем DLMM инстансы (как в BMeteora.js)
        await meteoraSDK.initializeDLMMInstances();
        console.log('✅ DLMM инстансы инициализированы');

        // 3. Наши тестовые пулы
        const pools = [
            {
                name: 'Pool 1',
                address: 'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y'
            },
            {
                name: 'Pool 2', 
                address: '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6'
            },
            {
                name: 'Pool 3',
                address: 'HTvjzsfX3yU6BUodCjZ5vZkUrAxMDTrBs3CJaq43ashR'
            }
        ];

        // 4. Загружаем все ALT таблицы для сравнения
        console.log('\n🔍 ЗАГРУЗКА ALT ТАБЛИЦ ДЛЯ СРАВНЕНИЯ...');
        const altAddresses = [
            'HGmknUTUmeovMc9ryERNWG6UFZDFDVr9xrum3ZhyL4fC', // MarginFi 1
            '5FuKF7C1tJji2mXZuJ14U9oDb37is5mmvYLf4KwojoF1', // MarginFi 2
            'FEFhAFKz48P3w82Ds5VhvyEDwhRqu2FejmnuxEPZ8wNR', // MarginFi 3
            'FAeyUf4AdG7vTQ2Q89r9hWBkPdYkykJKG4CxgqArBGXe', // Custom
            '9A2DNR6RPNLYcYS8g5M4V12ytLWmGg8g4tMoeL1xJUW8', // Jupiter 1
            '5cFsmTCEfmvpBUBHqsWZnf9n5vTWLYH2LT8X7HdShwxP', // Jupiter 2
            '3eqTbgbMhbAQXEYLaw5BSgpJmNYJ7gjtyiukpTmeSAaX'  // Jupiter 3
        ];

        const allALTAddresses = new Set();
        for (const altAddress of altAddresses) {
            try {
                const altAccount = await connection.getAddressLookupTable(new PublicKey(altAddress));
                if (altAccount && altAccount.value) {
                    altAccount.value.state.addresses.forEach(addr => 
                        allALTAddresses.add(addr.toString())
                    );
                }
            } catch (error) {
                console.log(`⚠️ Ошибка загрузки ALT ${altAddress}: ${error.message}`);
            }
        }

        console.log(`📊 Загружено ${allALTAddresses.size} уникальных адресов из ALT таблиц`);

        // 5. Анализируем каждый пул
        for (const pool of pools) {
            console.log(`\n${'='.repeat(60)}`);
            console.log(`🎯 АНАЛИЗ ${pool.name}: ${pool.address}`);
            console.log(`${'='.repeat(60)}`);

            try {
                // Создаем тестовую swap инструкцию
                const testAmount = 100000; // 0.1 SOL
                const minOut = 1;

                console.log(`📋 Создание swap инструкции для ${pool.name}...`);
                const swapResult = await meteoraSDK.createStableSwapInstruction(
                    pool.address, 
                    testAmount, 
                    minOut
                );

                if (!swapResult || !swapResult.instruction) {
                    console.log(`❌ Не удалось создать swap инструкцию для ${pool.name}`);
                    continue;
                }

                const instruction = swapResult.instruction;
                console.log(`✅ Swap инструкция создана: ${instruction.keys.length} аккаунтов`);

                // Анализируем каждый аккаунт в инструкции
                console.log(`\n📋 ДЕТАЛЬНЫЙ АНАЛИЗ АККАУНТОВ:`);
                let foundInALT = 0;
                let notFoundInALT = 0;

                for (let i = 0; i < instruction.keys.length; i++) {
                    const account = instruction.keys[i];
                    const address = account.pubkey.toString();
                    const inALT = allALTAddresses.has(address);
                    
                    console.log(`   ${(i + 1).toString().padStart(2)}. ${inALT ? '✅' : '❌'} ${address}`);
                    console.log(`       Signer: ${account.isSigner}, Writable: ${account.isWritable}`);
                    
                    if (inALT) {
                        foundInALT++;
                    } else {
                        notFoundInALT++;
                        
                        // Пытаемся определить тип аккаунта
                        let accountType = 'Unknown';
                        if (address === pool.address) {
                            accountType = 'Pool Address';
                        } else if (address === 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo') {
                            accountType = 'Meteora DLMM Program';
                        } else if (address === 'So11111111111111111111111111111111111111112') {
                            accountType = 'SOL Token';
                        } else if (address === 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v') {
                            accountType = 'USDC Token';
                        } else if (address === 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA') {
                            accountType = 'Token Program';
                        } else if (address === '11111111111111111111111111111111') {
                            accountType = 'System Program';
                        } else if (address.length === 44) {
                            // Пытаемся определить по длине и структуре
                            if (account.isWritable) {
                                accountType = 'Bin Array / Reserve (предположительно)';
                            } else {
                                accountType = 'Oracle / Config (предположительно)';
                            }
                        }
                        
                        console.log(`       ❌ НЕ В ALT: ${accountType}`);
                    }
                }

                console.log(`\n📊 СТАТИСТИКА ${pool.name}:`);
                console.log(`   ✅ Найдено в ALT: ${foundInALT}/${instruction.keys.length}`);
                console.log(`   ❌ НЕ найдено в ALT: ${notFoundInALT}/${instruction.keys.length}`);
                console.log(`   📊 Процент сжатия: ${(foundInALT / instruction.keys.length * 100).toFixed(1)}%`);

                // Если много аккаунтов не найдено, показываем их
                if (notFoundInALT > 5) {
                    console.log(`\n🚨 КРИТИЧНО: ${notFoundInALT} аккаунтов НЕ СЖИМАЮТСЯ!`);
                    console.log(`💡 Эти аккаунты нужно добавить в ALT таблицы для сжатия`);
                }

            } catch (error) {
                console.log(`❌ Ошибка анализа ${pool.name}: ${error.message}`);
            }
        }

        // 6. Общие выводы
        console.log(`\n${'='.repeat(80)}`);
        console.log('🎯 ОБЩИЕ ВЫВОДЫ:');
        console.log(`📊 Всего адресов в ALT таблицах: ${allALTAddresses.size}`);
        console.log('🔍 Каждый Meteora пул использует свои уникальные bin arrays');
        console.log('💡 Для максимального сжатия нужно добавить pool-специфичные адреса в ALT');
        console.log('🚨 Jupiter ALT таблицы содержат общие адреса, но НЕ наши конкретные пулы');

    } catch (error) {
        console.error('❌ Ошибка анализа:', error.message);
        console.error(error.stack);
    }
}

// Запуск анализа
if (require.main === module) {
    analyzeMeteorSwapAccounts();
}

module.exports = { analyzeMeteorSwapAccounts };
