/**
 * 🔍 АНАЛИЗ ТРАНЗАКЦИИ 242UzTkewL7ShmGzVgTuJynKfPoh64LYNMS4F1mp5dBMzYzwxM6VeEPK5KenMpXK6VNfnnh4o2SbpCT2Nvk68oaE
 * Ошибка: Instruction #9 Failed - Provided owner is not allowed
 */

console.log('🔍 АНАЛИЗ ПРОБЛЕМНОЙ ТРАНЗАКЦИИ');
console.log('═══════════════════════════════════════════════════════');

// Данные из Solscan для транзакции 242UzTkewL7ShmGzVgTuJynKfPoh64LYNMS4F1mp5dBMzYzwxM6VeEPK5KenMpXK6VNfnnh4o2SbpCT2Nvk68oaE
const instructionAnalysis = [
  { index: 1, type: 'lending_account_start_flashloan', program: 'MarginFi V2', status: 'SUCCESS' },
  { index: 2, type: 'createIdempotent', program: 'Associated Token Account Program', status: 'SUCCESS' },
  { index: 3, type: 'lending_account_borrow', program: 'MarginFi V2', status: 'SUCCESS' },
  { index: 4, type: 'SetComputeUnitLimit', program: 'Compute Budget', status: 'SUCCESS' },
  { index: 5, type: 'SetComputeUnitPrice', program: 'Compute Budget', status: 'SUCCESS' },
  { index: 6, type: 'createIdempotent', program: 'Associated Token Account Program', status: 'SUCCESS' },
  { index: 7, type: 'route', program: 'Jupiter Aggregator v6', status: 'SUCCESS' },
  { index: 8, type: 'closeAccount', program: 'Token Program', status: 'SUCCESS', account: '68rtTtSuEPR84Wo1xWGs6ytBttn7JN33Ux8WsDp38FHk' },
  { index: 9, type: 'set_token_ledger', program: 'Jupiter Aggregator v6', status: 'FAILED', account: '68rtTtSuEPR84Wo1xWGs6ytBttn7JN33Ux8WsDp38FHk', error: 'Provided owner is not allowed' },
  { index: 10, type: 'createIdempotent', program: 'Associated Token Account Program', status: 'NOT_EXECUTED' },
  { index: 11, type: 'transfer', program: 'System Program', status: 'NOT_EXECUTED' },
  { index: 12, type: 'syncNative', program: 'Token Program', status: 'NOT_EXECUTED' },
  { index: 13, type: 'route_with_token_ledger', program: 'Jupiter Aggregator v6', status: 'NOT_EXECUTED' },
  { index: 14, type: 'closeAccount', program: 'Token Program', status: 'NOT_EXECUTED' },
  { index: 15, type: 'lending_account_repay', program: 'MarginFi V2', status: 'NOT_EXECUTED' },
  { index: 16, type: 'lending_account_end_flashloan', program: 'MarginFi V2', status: 'NOT_EXECUTED' }
];

console.log('\n📋 АНАЛИЗ ПОСЛЕДОВАТЕЛЬНОСТИ ИНСТРУКЦИЙ:');
console.log('═══════════════════════════════════════════════════════');

let closeAccountIndex = -1;
// 🔥 УДАЛЕНО: setTokenLedgerIndex больше не нужен
const problemTokenAccount = '68rtTtSuEPR84Wo1xWGs6ytBttn7JN33Ux8WsDp38FHk';

instructionAnalysis.forEach((instruction) => {
  const statusIcon = instruction.status === 'SUCCESS' ? '✅' : 
                    instruction.status === 'FAILED' ? '❌' : '⏸️';
  
  console.log(`#${instruction.index} - ${instruction.type} (${instruction.program}) ${statusIcon}`);
  
  if (instruction.type === 'closeAccount' && instruction.status === 'SUCCESS') {
    closeAccountIndex = instruction.index;
    console.log(`   🔴 НАЙДЕНА closeAccount инструкция! Аккаунт: ${instruction.account}`);
  }
  
  // 🔥 УДАЛЕНО: set_token_ledger больше не используется
});

console.log('\n🎯 РЕЗУЛЬТАТ АНАЛИЗА:');
console.log('═══════════════════════════════════════════════════════');

if (closeAccountIndex !== -1) {
  console.log(`🔴 closeAccount найдена на позиции: #${closeAccountIndex}`);
  console.log(`🎯 Проблемный токен аккаунт: ${problemTokenAccount}`);

  // 🔥 УДАЛЕНО: set_token_ledger анализ больше не нужен
    
    console.log('\n🚨 ДЕТАЛЬНЫЙ АНАЛИЗ ПРОБЛЕМЫ:');
    console.log('═══════════════════════════════════════════════════════');
    console.log('1. Instruction #7: Jupiter route - создает WSOL токен аккаунт 68rtTt...');
    console.log('2. Instruction #8: closeAccount - ЗАКРЫВАЕТ токен аккаунт 68rtTt...');
    console.log('3. Instruction #9: set_token_ledger - пытается использовать ЗАКРЫТЫЙ аккаунт 68rtTt...');
    console.log('4. РЕЗУЛЬТАТ: "Provided owner is not allowed" - аккаунт уже не существует!');
    
    console.log('\n🔧 ИСПРАВЛЕНИЕ В КОДЕ:');
    console.log('═══════════════════════════════════════════════════════');
    console.log('Файл: instruction-processor.js');
    console.log('Метод: reorderJupiterInstructions()');
    console.log('Проблема: set_token_ledger классифицируется неправильно');
    console.log('Решение: Улучшить логику определения set_token_ledger инструкций');
    
  } else {
    console.log(`✅ Порядок правильный: set_token_ledger выполняется перед closeAccount`);
  }
} else {
  console.log(`⚠️ Не найдены ожидаемые инструкции:`);
  console.log(`   closeAccount: ${closeAccountIndex !== -1 ? 'НАЙДЕНА' : 'НЕ НАЙДЕНА'}`);
  // 🔥 УДАЛЕНО: set_token_ledger статистика
}

console.log('\n📚 ОФИЦИАЛЬНАЯ ДОКУМЕНТАЦИЯ JUPITER:');
console.log('═══════════════════════════════════════════════════════');
console.log('Источник: https://dev.jup.ag/docs/old/apis/swap-api#using-token-ledger-instruction');
console.log('Правильный порядок:');
console.log('1. computeBudget инструкции');
console.log('2. setup инструкции (создание ATA)');
// 🔥 УДАЛЕНО: 3. set_token_ledger инструкции
console.log('4. swap инструкции');
console.log('5. cleanup инструкции');
console.log('6. closeAccount инструкции (ПОСЛЕДНИЕ!)');

console.log('\n🎯 ЗАКЛЮЧЕНИЕ:');
console.log('═══════════════════════════════════════════════════════');
console.log('❌ НАШ КОД НАРУШАЕТ ОФИЦИАЛЬНЫЙ ПОРЯДОК JUPITER ИНСТРУКЦИЙ!');
console.log('🔧 ТРЕБУЕТСЯ ИСПРАВЛЕНИЕ В instruction-processor.js');
console.log('✅ ПОСЛЕ ИСПРАВЛЕНИЯ ОШИБКА "Provided owner is not allowed" БУДЕТ УСТРАНЕНА');
