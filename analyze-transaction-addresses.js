/**
 * 🔍 АНАЛИЗ АДРЕСОВ В ТРАНЗАКЦИЯХ - ЧТО ДОБАВИТЬ В ALT ТАБЛИЦЫ
 */

const { PublicKey } = require('@solana/web3.js');
const fs = require('fs');

// 🔥 АДРЕСА КОТОРЫЕ ЧАСТО ИСПОЛЬЗУЮТСЯ В НАШИХ ТРАНЗАКЦИЯХ
const FREQUENT_ADDRESSES = {
    // 🏦 MARGINFI АДРЕСА (уже в ALT)
    MARGINFI_PROGRAM: 'MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA',
    MARGINFI_GROUP: '4qp6Fx6tnZkY5Wropq9wUYgtFxXKwE6viZxFHg3rdAG8',
    
    // 🌪️ METEORA DLMM АДРЕСА (КРИТИЧЕСКИ ВАЖНЫЕ!)
    METEORA_DLMM_PROGRAM: 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo',
    
    // 🎯 НАШИ ОСНОВНЫЕ ПУЛЫ
    POOL_1_WSOL_USDC: '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6',
    POOL_2_WSOL_USDC: 'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y',
    
    // 🎯 НАШИ ПОЗИЦИИ
    POSITION_1: '5XYfyyDBpNt8aTuvxe6RAQ7f11WwNWyD3ZUJkfJzDFKU',
    POSITION_2: 'A7tpj5uz4pCHiCaBSuTFt8EWmSBFp8qrzoVVCsuVfXgC',
    
    // 🏊 POOL RESERVES (ИСПОЛЬЗУЮТСЯ В КАЖДОЙ ТРАНЗАКЦИИ!)
    POOL_1_RESERVE_X: 'EYj9xKw6NqrfUWKhq8h5Gqhj8NqrfUWKhq8h5Gqhj8N',  // WSOL reserve
    POOL_1_RESERVE_Y: 'CoaxzEh8NqrfUWKhq8h5Gqhj8NqrfUWKhq8h5Gqhj8N',  // USDC reserve
    POOL_2_RESERVE_X: 'DwZz4S1ZNqrfUWKhq8h5Gqhj8NqrfUWKhq8h5Gqhj8N',  // WSOL reserve
    POOL_2_RESERVE_Y: '4N22J4vWNqrfUWKhq8h5Gqhj8NqrfUWKhq8h5Gqhj8N',  // USDC reserve
    
    // 📊 BIN ARRAYS (ИСПОЛЬЗУЮТСЯ В SWAP И REMOVE LIQUIDITY!)
    POOL_1_BIN_ARRAYS: [
        'Hd6qVSiPQELZq3FAXPCumWmnKPx4BbZXd9782TEeRY2x', // Bin Array -61
        'B7XxwUwnUhtmXLnBvcGxuA1byHaAhrE7oiz53nxT82tw', // Bin Array Lower
        '5kY2AvUZoSVABoNXoaPfDVtCAJLAFRz8RsX78BECz8NC'  // Bin Array Upper
    ],
    POOL_2_BIN_ARRAYS: [
        'Dbw8mACQmyrvbvLZs9bzAA6Tg2GVf5cmLzVbkfRzahRS', // Bin Array -25
        'DjyDh54Q6LqPkARYmgjgLUKmwo1BZGDcY5Z5MFwvPc4N', // Bin Array Lower
        'FcYLBnripHnpmGFMwrwMkT1LBRhBWjn5ftwBmCaJLdDb'  // Bin Array Upper
    ],
    
    // 🔮 ORACLES (ИСПОЛЬЗУЮТСЯ В SWAP!)
    POOL_1_ORACLE: '59YuGWPunbchD2mbi9U7qvjWQKQReGeepn4ZSr9zz9Li',
    POOL_2_ORACLE: 'ETc6tqgLrr7wXsH8u2QBK1CyXHX3kvV6WQjBz4cf3sCj',
    
    // 🎪 EVENT AUTHORITIES
    METEORA_EVENT_AUTHORITY: 'D1ZN9Wj1fRSUQfCjhvnu1hqDMT7hzjzBBpi12nVniYD6',
    
    // 🏦 MARGINFI BANKS
    USDC_BANK: '2s37akK2eyBbp8DQxsaPpyMkqsM4hQTd8pCFRHsa4VYf',
    WSOL_BANK: 'CCKtUs6Cgwo4aaQUmBPmyoApH2gUDErxNZCAntD6LYGh',
    
    // 🏦 MARGINFI VAULTS
    USDC_VAULT: '7jaiZR5Sk8hdYN9MxTpczTcwbWpb5WEoxSANuUwveuat',
    WSOL_VAULT: '2eicbpitfJXDwqCuFAmPgDP7t2oUotnAzbGzRKLMgSLe',
    
    // 👤 USER TOKEN ACCOUNTS (СОЗДАЮТСЯ ДИНАМИЧЕСКИ, НО МОЖНО ПРЕДСКАЗАТЬ)
    USER_USDC_ATA: '3AWxcMzxsTeBQ6YC1khJ9NkPtmLw2XbCGxvZHZpbAQFo',
    USER_WSOL_ATA: '68rtTtSuEPR84Wo1xWGs6ytBttn7JN33Ux8WsDp38FHk',
    
    // 🎯 СИСТЕМНЫЕ ПРОГРАММЫ (уже в ALT, но важно проверить)
    SYSTEM_PROGRAM: '11111111111111111111111111111111',
    TOKEN_PROGRAM: 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA',
    ATA_PROGRAM: 'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL',
    SYSVAR_INSTRUCTIONS: 'Sysvar1nstructions1111111111111111111111111',
    SYSVAR_RENT: 'SysvarRent111111111111111111111111111111111',
    
    // 🎪 ДОПОЛНИТЕЛЬНЫЕ METEORA АДРЕСА
    METEORA_BIN_ARRAY_BITMAP_EXTENSION_1: '59YuGWPunbchD2mbi9U7qvjWQKQReGeepn4ZSr9zz9Li',
    METEORA_BIN_ARRAY_BITMAP_EXTENSION_2: 'ETc6tqgLrr7wXsH8u2QBK1CyXHX3kvV6WQjBz4cf3sCj',
    
    // 💰 TOKEN MINTS
    USDC_MINT: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
    WSOL_MINT: 'So11111111111111111111111111111111111111112',
    
    // 🎯 MEMO PROGRAM (используется в swap)
    MEMO_PROGRAM: 'MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr',
    MEMO_PROGRAM_V2: 'Memo1UhkJRfHyvLMcVucJwxXeuD728EqVDDwQDxFMNo'
};

async function analyzeTransactionAddresses() {
    console.log('🔍 АНАЛИЗ АДРЕСОВ ДЛЯ ДОБАВЛЕНИЯ В ALT ТАБЛИЦЫ');
    console.log('=' .repeat(60));
    
    // Читаем текущие ALT таблицы
    let currentALT = {};
    try {
        const altData = JSON.parse(fs.readFileSync('custom-alt-data.json', 'utf8'));
        currentALT = altData.tables;
        console.log(`📊 Текущие ALT таблицы: ${Object.keys(currentALT).length}`);
        
        Object.entries(currentALT).forEach(([name, data]) => {
            console.log(`   ${name}: ${data.addresses.length} адресов`);
        });
    } catch (error) {
        console.log(`⚠️ Не удалось прочитать текущие ALT: ${error.message}`);
    }
    
    // Собираем все адреса которые нужно добавить
    const addressesToAdd = [];
    const addressCategories = {};
    
    Object.entries(FREQUENT_ADDRESSES).forEach(([key, value]) => {
        if (Array.isArray(value)) {
            // Массив адресов
            value.forEach(addr => {
                addressesToAdd.push(addr);
                addressCategories[addr] = key;
            });
        } else {
            // Одиночный адрес
            addressesToAdd.push(value);
            addressCategories[value] = key;
        }
    });
    
    console.log(`\n🎯 АДРЕСА ДЛЯ ДОБАВЛЕНИЯ: ${addressesToAdd.length}`);
    
    // Проверяем какие адреса уже есть в ALT
    const existingAddresses = new Set();
    Object.values(currentALT).forEach(table => {
        if (table.addresses) {
            table.addresses.forEach(addr => existingAddresses.add(addr));
        }
    });
    
    const newAddresses = addressesToAdd.filter(addr => !existingAddresses.has(addr));
    const alreadyInALT = addressesToAdd.filter(addr => existingAddresses.has(addr));
    
    console.log(`\n📊 СТАТИСТИКА:`);
    console.log(`   ✅ Уже в ALT: ${alreadyInALT.length}`);
    console.log(`   🆕 Новые адреса: ${newAddresses.length}`);
    
    if (alreadyInALT.length > 0) {
        console.log(`\n✅ УЖЕ В ALT ТАБЛИЦАХ:`);
        alreadyInALT.forEach(addr => {
            console.log(`   ${addr} (${addressCategories[addr]})`);
        });
    }
    
    if (newAddresses.length > 0) {
        console.log(`\n🆕 НОВЫЕ АДРЕСА ДЛЯ ДОБАВЛЕНИЯ:`);
        newAddresses.forEach(addr => {
            console.log(`   ${addr} (${addressCategories[addr]})`);
        });
        
        // Группируем по категориям
        const categorizedNew = {};
        newAddresses.forEach(addr => {
            const category = addressCategories[addr];
            if (!categorizedNew[category]) {
                categorizedNew[category] = [];
            }
            categorizedNew[category].push(addr);
        });
        
        console.log(`\n📋 ПО КАТЕГОРИЯМ:`);
        Object.entries(categorizedNew).forEach(([category, addresses]) => {
            console.log(`   ${category}: ${addresses.length} адресов`);
            addresses.forEach(addr => {
                console.log(`     - ${addr}`);
            });
        });
    }
    
    // Рекомендации по оптимизации
    console.log(`\n💡 РЕКОМЕНДАЦИИ:`);
    console.log(`   🎯 Добавить ${newAddresses.length} новых адресов в Custom ALT`);
    console.log(`   📊 Это сэкономит ~${newAddresses.length * 32} байт на транзакцию`);
    console.log(`   🚀 Транзакции станут меньше на ~${Math.round(newAddresses.length * 32 / 1232 * 100)}%`);
    
    return {
        newAddresses,
        alreadyInALT,
        addressCategories,
        totalSavings: newAddresses.length * 32
    };
}

analyzeTransactionAddresses().catch(console.error);
