/**
 * 🔍 АНАЛИЗАТОР КОМИССИЙ DEX ИЗ РЕАЛЬНЫХ ТРАНЗАКЦИЙ
 * 
 * Этот скрипт анализирует реальные транзакции Solana и извлекает
 * точные комиссии различных DEX для обновления наших конфигураций.
 */

const { Connection, PublicKey } = require('@solana/web3.js');

class TransactionFeeAnalyzer {
  constructor() {
    this.connection = new Connection('https://api.mainnet-beta.solana.com', 'confirmed');
    
    // 🏦 ИЗВЕСТНЫЕ ПРОГРАММЫ DEX
    this.dexPrograms = {
      'JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4': 'Jupiter',
      'opnb2LAfJYbRMAHHvqjCwQxanZn7ReEHp1k81EohpZb': 'OpenBook V2',
      'PhoeNiXZ8ByJGLkxNfZRnkUfjvmuYqLR89jjFHGqdXY': 'Phoenix',
      '675kPX9MHTjS2zt1qfr1NYHuzeLXfQM9H24wFSUt1Mp8': 'Raydium',
      'whirLbMiicVdio4qvUfM5KAg6Ct8VwpYzGff3uctyCc': 'Orca',
      '24Uqj9JCLxUeoC3hGfh5W3s9FM9uCHDS2SG3LYwBpyTi': 'Meteora'
    };

    // 💰 ИЗВЕСТНЫЕ ТОКЕНЫ
    this.tokens = {
      'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v': { symbol: 'USDC', decimals: 6 },
      'So11111111111111111111111111111111111111112': { symbol: 'SOL', decimals: 9 },
      'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB': { symbol: 'USDT', decimals: 6 }
    };

    // 📊 РЕЗУЛЬТАТЫ АНАЛИЗА
    this.feeAnalysis = {
      OpenBook: [],
      Phoenix: [],
      Raydium: [],
      Orca: [],
      Meteora: []
    };
  }

  /**
   * 🔍 АНАЛИЗ КОНКРЕТНОЙ ТРАНЗАКЦИИ
   */
  async analyzeTransaction(signature) {
    try {
      console.log(`\n🔍 АНАЛИЗ ТРАНЗАКЦИИ: ${signature}`);
      console.log('═'.repeat(80));

      const transaction = await this.connection.getParsedTransaction(signature, {
        maxSupportedTransactionVersion: 0
      });

      if (!transaction) {
        console.log('❌ Транзакция не найдена');
        return null;
      }

      if (transaction.meta?.err) {
        console.log(`⚠️ Транзакция провалилась: ${JSON.stringify(transaction.meta.err)}`);
      }

      // 📋 АНАЛИЗИРУЕМ ИНСТРУКЦИИ
      const instructions = transaction.transaction.message.instructions;
      const innerInstructions = transaction.meta?.innerInstructions || [];

      console.log(`📋 Инструкций: ${instructions.length}`);
      console.log(`📋 Внутренних инструкций: ${innerInstructions.length}`);

      // 🔍 ИЩЕМ DEX ОПЕРАЦИИ
      await this.analyzeDEXOperations(instructions, innerInstructions, transaction);

      return this.feeAnalysis;

    } catch (error) {
      console.error(`❌ Ошибка анализа транзакции: ${error.message}`);
      return null;
    }
  }

  /**
   * 🏦 АНАЛИЗ DEX ОПЕРАЦИЙ
   */
  async analyzeDEXOperations(instructions, innerInstructions, transaction) {
    const accountKeys = transaction.transaction.message.accountKeys;
    
    // 🔍 АНАЛИЗИРУЕМ ОСНОВНЫЕ ИНСТРУКЦИИ
    for (let i = 0; i < instructions.length; i++) {
      const instruction = instructions[i];
      const programId = accountKeys[instruction.programIdIndex]?.pubkey?.toString();
      const dexName = this.dexPrograms[programId];

      if (dexName) {
        console.log(`\n🏦 НАЙДЕН ${dexName} (инструкция #${i + 1})`);
        await this.analyzeSwapInstruction(instruction, dexName, accountKeys, i, transaction);
      }
    }

    // 🔍 АНАЛИЗИРУЕМ ВНУТРЕННИЕ ИНСТРУКЦИИ
    for (const innerGroup of innerInstructions) {
      for (const innerIx of innerGroup.instructions) {
        if (innerIx.parsed && innerIx.parsed.type === 'transfer') {
          await this.analyzeTokenTransfer(innerIx.parsed.info, innerGroup.index);
        }
      }
    }
  }

  /**
   * 💱 АНАЛИЗ SWAP ИНСТРУКЦИИ
   */
  async analyzeSwapInstruction(instruction, dexName, accountKeys, index, transaction) {
    console.log(`   📊 Программа: ${dexName}`);
    console.log(`   📊 Аккаунтов: ${instruction.accounts?.length || 0}`);

    // 🔍 ИЩЕМ СВЯЗАННЫЕ ТРАНСФЕРЫ В INNER INSTRUCTIONS
    const relatedTransfers = this.findRelatedTransfers(transaction.meta?.innerInstructions, index);
    
    if (relatedTransfers.length > 0) {
      console.log(`   💸 Связанных трансферов: ${relatedTransfers.length}`);
      
      // 📊 РАССЧИТЫВАЕМ КОМИССИЮ
      const feeAnalysis = this.calculateDEXFee(relatedTransfers, dexName);
      if (feeAnalysis) {
        this.feeAnalysis[dexName].push(feeAnalysis);
        console.log(`   💰 Комиссия ${dexName}: ${feeAnalysis.feePercent.toFixed(4)}% ($${feeAnalysis.feeUSD.toFixed(2)})`);
      }
    }
  }

  /**
   * 🔍 ПОИСК СВЯЗАННЫХ ТРАНСФЕРОВ
   */
  findRelatedTransfers(innerInstructions, instructionIndex) {
    if (!innerInstructions) return [];

    const relatedGroup = innerInstructions.find(group => group.index === instructionIndex);
    if (!relatedGroup) return [];

    return relatedGroup.instructions.filter(ix => 
      ix.parsed && ix.parsed.type === 'transfer'
    );
  }

  /**
   * 💰 РАСЧЕТ КОМИССИИ DEX
   */
  calculateDEXFee(transfers, dexName) {
    if (transfers.length < 2) return null;

    try {
      // 🔍 ИЩЕМ INPUT И OUTPUT ТРАНСФЕРЫ
      let inputTransfer = null;
      let outputTransfer = null;

      for (const transfer of transfers) {
        const info = transfer.parsed.info;
        const amount = parseFloat(info.amount);
        
        if (amount > 1000000) { // Больше 1 USDC или 0.001 SOL
          if (!inputTransfer || amount > parseFloat(inputTransfer.parsed.info.amount)) {
            outputTransfer = inputTransfer;
            inputTransfer = transfer;
          } else if (!outputTransfer || amount > parseFloat(outputTransfer.parsed.info.amount)) {
            outputTransfer = transfer;
          }
        }
      }

      if (!inputTransfer || !outputTransfer) return null;

      const inputAmount = parseFloat(inputTransfer.parsed.info.amount);
      const outputAmount = parseFloat(outputTransfer.parsed.info.amount);

      // 🔍 ОПРЕДЕЛЯЕМ ТОКЕНЫ
      const inputMint = inputTransfer.parsed.info.mint || 'SOL';
      const outputMint = outputTransfer.parsed.info.mint || 'SOL';

      const inputToken = this.tokens[inputMint] || { symbol: 'UNKNOWN', decimals: 9 };
      const outputToken = this.tokens[outputMint] || { symbol: 'UNKNOWN', decimals: 9 };

      // 💰 НОРМАЛИЗУЕМ СУММЫ
      const normalizedInput = inputAmount / Math.pow(10, inputToken.decimals);
      const normalizedOutput = outputAmount / Math.pow(10, outputToken.decimals);

      // 📊 РАССЧИТЫВАЕМ КОМИССИЮ (приблизительно)
      let feePercent = 0;
      let feeUSD = 0;

      if (inputToken.symbol === 'USDC' && outputToken.symbol === 'SOL') {
        // USDC → SOL
        const expectedSOL = normalizedInput / 147; // Примерная цена SOL
        const actualSOL = normalizedOutput;
        const lostValue = (expectedSOL - actualSOL) * 147;
        feePercent = (lostValue / normalizedInput) * 100;
        feeUSD = lostValue;
      } else if (inputToken.symbol === 'SOL' && outputToken.symbol === 'USDC') {
        // SOL → USDC
        const expectedUSDC = normalizedInput * 147;
        const actualUSDC = normalizedOutput;
        const lostValue = expectedUSDC - actualUSDC;
        feePercent = (lostValue / expectedUSDC) * 100;
        feeUSD = lostValue;
      }

      return {
        dex: dexName,
        inputToken: inputToken.symbol,
        outputToken: outputToken.symbol,
        inputAmount: normalizedInput,
        outputAmount: normalizedOutput,
        feePercent: Math.abs(feePercent),
        feeUSD: Math.abs(feeUSD),
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error(`❌ Ошибка расчета комиссии: ${error.message}`);
      return null;
    }
  }

  /**
   * 💸 АНАЛИЗ TOKEN TRANSFER
   */
  async analyzeTokenTransfer(transferInfo, instructionIndex) {
    const amount = parseFloat(transferInfo.amount);
    const mint = transferInfo.mint;
    const token = this.tokens[mint] || { symbol: 'UNKNOWN', decimals: 9 };
    const normalizedAmount = amount / Math.pow(10, token.decimals);

    if (normalizedAmount > 1) { // Только значимые трансферы
      console.log(`     💸 Transfer: ${normalizedAmount.toFixed(6)} ${token.symbol}`);
    }
  }

  /**
   * 📊 ОТЧЕТ ПО КОМИССИЯМ
   */
  generateFeeReport() {
    console.log('\n📊 ОТЧЕТ ПО КОМИССИЯМ DEX');
    console.log('═'.repeat(80));

    for (const [dexName, fees] of Object.entries(this.feeAnalysis)) {
      if (fees.length > 0) {
        console.log(`\n🏦 ${dexName.toUpperCase()}:`);
        
        const avgFee = fees.reduce((sum, fee) => sum + fee.feePercent, 0) / fees.length;
        const avgFeeUSD = fees.reduce((sum, fee) => sum + fee.feeUSD, 0) / fees.length;
        
        console.log(`   📊 Операций: ${fees.length}`);
        console.log(`   💰 Средняя комиссия: ${avgFee.toFixed(4)}%`);
        console.log(`   💵 Средняя комиссия: $${avgFeeUSD.toFixed(2)}`);
        
        fees.forEach((fee, index) => {
          console.log(`   ${index + 1}. ${fee.inputToken}→${fee.outputToken}: ${fee.feePercent.toFixed(4)}% ($${fee.feeUSD.toFixed(2)})`);
        });
      }
    }
  }
}

/**
 * 🚀 АНАЛИЗ ВАШЕЙ ПРОВАЛИВШЕЙСЯ ТРАНЗАКЦИИ
 */
async function analyzeFailedTransaction() {
  const analyzer = new TransactionFeeAnalyzer();
  
  // 🔍 ВАША ПРОВАЛИВШАЯСЯ ТРАНЗАКЦИЯ
  const signature = '4U5rjM29YhPp74wABGcVvA7KgDnc93ArAeHnwnxuF3EBE9WW5PB5ijBrRL5Dwqqjwtax2dzBZWkhbRT7qFiYxgbu';
  
  console.log('🔍 АНАЛИЗ КОМИССИЙ DEX ИЗ РЕАЛЬНОЙ ТРАНЗАКЦИИ');
  console.log('═'.repeat(80));
  
  await analyzer.analyzeTransaction(signature);
  analyzer.generateFeeReport();
}

// 🚀 ЗАПУСК АНАЛИЗА
if (require.main === module) {
  analyzeFailedTransaction().catch(console.error);
}

module.exports = { TransactionFeeAnalyzer };
