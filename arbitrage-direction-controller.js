/**
 * 🧠 УМНЫЙ КОНТРОЛЛЕР НАПРАВЛЕНИЯ АРБИТРАЖА
 * 
 * Получает направление от анализатора и правильно передает его в Jupiter API
 * с контролем конкретных DEX для арбитража
 * 
 * ОСНОВНАЯ ПРОБЛЕМА:
 * - Анализатор определяет: "SOL дешевле на Raydium, дороже на Orca"
 * - Но Jupiter API не знает что нужно покупать именно на Raydium и продавать на Orca
 * - Jupiter может выбрать любые DEX по своему алгоритму
 * 
 * РЕШЕНИЕ:
 * - Делаем ДВА ОТДЕЛЬНЫХ Jupiter запроса с параметром `dexes`
 * - Запрос 1: USDC → SOL с dexes="Raydium" (покупка дешево)
 * - Запрос 2: SOL → USDC с dexes="Orca" (продажа дорого)
 */

const { getJupiterRequestParams } = require('./trading-config');

class ArbitrageDirectionController {
  constructor(jupiterSwapInstructions) {
    this.jupiterSwapInstructions = jupiterSwapInstructions;
    
    // 🎯 МАППИНГ DEX НАЗВАНИЙ (Анализатор → Jupiter API)
    // 🚨 ТОЛЬКО УКАЗАННЫЕ DEX! НЕ БОЛЬШЕ!
    this.dexMapping = {
      // 🪐 JUPITER - АГРЕГАТОР (возвращает null = используем ограниченный список DEX)
      'Jupiter': null,             // null = используем ограниченный список DEX

      // ✅ ТОЛЬКО УКАЗАННЫЕ DEX:
      'Raydium': 'Raydium',
      'Raydium CLMM': 'Raydium CLMM',
      'Orca': 'Orca V2',
      'Orca V1': 'Orca V1',
      'Orca V2': 'Orca V2',
      'Whirlpool': 'Whirlpool',
      'Meteora': 'Meteora',
      'Meteora DLMM': 'Meteora DLMM',
      'Phoenix': 'Phoenix',
      'OpenBook V2': 'OpenBook V2'
      // 🚨 ВСЕ ОСТАЛЬНЫЕ DEX УДАЛЕНЫ!
    };

    // 🔥 АЛЬТЕРНАТИВНЫЕ НАЗВАНИЯ DEX ДЛЯ FALLBACK (ТОЛЬКО ПРЯМЫЕ МАРШРУТЫ!)
    this.dexAlternatives = {
      'Orca': ['Whirlpool', 'Orca V2', 'Orca', 'Raydium'], // 🔄 Добавляем Raydium как fallback для Orca
      'Raydium': ['Raydium', 'Raydium CLMM', 'Raydium CPMM', 'Whirlpool'], // 🔄 Добавляем Whirlpool как fallback
      'Meteora': ['Meteora', 'Meteora DLMM', 'Raydium'],
      'Phoenix': ['Phoenix', 'Phoenix V1', 'Raydium'],
      'Openbook': ['Openbook', 'OpenBook', 'Raydium'],
      'Whirlpool': ['Whirlpool', 'Raydium', 'Orca V2'] // Прямые альтернативы для Whirlpool
    };
    
    // 🔥 ПРИОРИТЕТНЫЕ DEX ДЛЯ АРБИТРАЖА (ПРОВЕРЕННЫЕ)
    this.preferredDexes = {
      'buy': ['Raydium', 'Orca', 'Whirlpool', 'Meteora'],  // Для покупки (дешево)
      'sell': ['Orca', 'Raydium', 'Whirlpool', 'Phoenix']  // Для продажи (дорого)
    };
    
    console.log('🧠 ArbitrageDirectionController инициализирован');
  }

  /**
   * 🎯 ГЛАВНАЯ ФУНКЦИЯ: СОЗДАНИЕ КОНТРОЛИРУЕМОГО АРБИТРАЖА
   * 
   * @param {Object} arbitrageDirection - Направление от анализатора
   * @param {string} arbitrageDirection.buyDex - DEX для покупки (дешево)
   * @param {string} arbitrageDirection.sellDex - DEX для продажи (дорого)
   * @param {string} arbitrageDirection.baseMint - Базовый токен (USDC)
   * @param {string} arbitrageDirection.intermediateMint - Промежуточный токен (SOL)
   * @param {number} arbitrageDirection.amount - Сумма в lamports
   * @param {number} arbitrageDirection.slippageBps - Slippage в bps
   */
  async createControlledArbitrageInstructions(arbitrageDirection) {
    try {
      console.log('🧠 СОЗДАНИЕ КОНТРОЛИРУЕМОГО АРБИТРАЖА...');
      console.log(`   Направление: ${arbitrageDirection.buyDex} → ${arbitrageDirection.sellDex}`);
      console.log(`   Маршрут: ${arbitrageDirection.baseMint.slice(0,8)}... → ${arbitrageDirection.intermediateMint.slice(0,8)}... → ${arbitrageDirection.baseMint.slice(0,8)}...`);
      console.log(`   Сумма: ${arbitrageDirection.amount}, Slippage: ${arbitrageDirection.slippageBps} bps`);

      // 🔥 ШАГ 1: ПОКУПКА ДЕШЕВО (baseMint → intermediateMint)
      console.log(`🛒 ШАГ 1: Покупка ${arbitrageDirection.intermediateMint.slice(0,8)}... дешево на ${arbitrageDirection.buyDex}`);
      
      const buyQuote = await this.getControlledJupiterQuote({
        inputMint: arbitrageDirection.baseMint,
        outputMint: arbitrageDirection.intermediateMint,
        amount: arbitrageDirection.amount,
        slippageBps: arbitrageDirection.slippageBps,
        dexes: arbitrageDirection.buyDex,
        operation: 'BUY'
      });

      if (!buyQuote || !buyQuote.outAmount) {
        console.log(`⚠️ Покупка на ${arbitrageDirection.buyDex} недоступна - пропускаем арбитраж`);
        return { success: false, error: `${arbitrageDirection.buyDex} недоступен для покупки` };
      }

      console.log(`✅ Покупка: ${arbitrageDirection.amount} → ${buyQuote.outAmount} (${arbitrageDirection.buyDex})`);

      // 🔥 ШАГ 2: ПРОДАЖА ДОРОГО (intermediateMint → baseMint)
      console.log(`💰 ШАГ 2: Продажа ${arbitrageDirection.intermediateMint.slice(0,8)}... дорого на ${arbitrageDirection.sellDex}`);
      
      const sellQuote = await this.getControlledJupiterQuote({
        inputMint: arbitrageDirection.intermediateMint,
        outputMint: arbitrageDirection.baseMint,
        amount: buyQuote.outAmount, // Используем РЕАЛЬНЫЙ выход от первого свопа
        slippageBps: arbitrageDirection.slippageBps,
        dexes: arbitrageDirection.sellDex,
        operation: 'SELL'
      });

      if (!sellQuote || !sellQuote.outAmount) {
        console.log(`⚠️ Продажа на ${arbitrageDirection.sellDex} недоступна - пропускаем арбитраж`);
        return { success: false, error: `${arbitrageDirection.sellDex} недоступен для продажи` };
      }

      console.log(`✅ Продажа: ${buyQuote.outAmount} → ${sellQuote.outAmount} (${arbitrageDirection.sellDex})`);

      // 🔍 ШАГ 3: ВАЛИДАЦИЯ АРБИТРАЖНОГО МАРШРУТА
      console.log(`🔍 ШАГ 3: Валидация арбитражного маршрута...`);

      const routeValidation = this.validateArbitrageRoute(buyQuote, sellQuote, arbitrageDirection);

      if (!routeValidation.isValid) {
        console.log(`❌ Валидация маршрута не пройдена:`);
        routeValidation.errors.forEach(error => console.log(`   ❌ ${error}`));
        return {
          success: false,
          error: 'Арбитражный маршрут не прошел валидацию',
          validationErrors: routeValidation.errors
        };
      }

      // 🔥 ШАГ 4: АНАЛИЗ ПРИБЫЛЬНОСТИ (ИСПРАВЛЕННЫЙ)
      const profitAnalysis = this.analyzeProfitability(
        arbitrageDirection.amount,
        sellQuote.outAmount,
        arbitrageDirection.buyDex,
        arbitrageDirection.sellDex,
        arbitrageDirection.baseMint,      // 🔥 ДОБАВЛЕНО: input mint
        arbitrageDirection.intermediateMint // 🔥 ДОБАВЛЕНО: output mint
      );

      // 🚨 КРИТИЧЕСКАЯ ПРОВЕРКА: ЕСЛИ УБЫТОК, МЕНЯЕМ НАПРАВЛЕНИЕ!
      if (!profitAnalysis.isProfitable) {
        console.log(`🚨 ОБНАРУЖЕН УБЫТОК: ${profitAnalysis.netProfitUsd} USD`);
        console.log(`🔄 ПРОБУЕМ ОБРАТНОЕ НАПРАВЛЕНИЕ: ${arbitrageDirection.sellDex} → ${arbitrageDirection.buyDex}`);

        // Меняем направление местами
        const reversedDirection = {
          ...arbitrageDirection,
          buyDex: arbitrageDirection.sellDex,
          sellDex: arbitrageDirection.buyDex
        };

        // Пробуем обратное направление
        return await this.createControlledArbitrageInstructions(reversedDirection);
      }

      // 🛡️ ШАГ 5: ПРОВЕРКА БЕЗОПАСНОСТИ
      const safetyCheck = this.validateArbitrageSafety(profitAnalysis, arbitrageDirection);

      if (!safetyCheck.isSafe) {
        console.log(`⚠️ Обнаружены риски безопасности:`);
        safetyCheck.risks.forEach(risk => console.log(`   ⚠️ ${risk}`));
        // Не блокируем выполнение, но предупреждаем
      }

      if (!profitAnalysis.isProfitable) {
        console.log(`❌ Арбитраж НЕ прибыльный: ${profitAnalysis.netProfitUsd} USD`);
        return {
          success: false,
          error: 'Арбитраж не прибыльный после учета комиссий',
          profitAnalysis
        };
      }

      console.log(`🎯 Арбитраж ПРИБЫЛЬНЫЙ: +${profitAnalysis.netProfitUsd} USD (${profitAnalysis.profitPercent}%)`);

      // 🔥 ШАГ 6: СОЗДАНИЕ ИНСТРУКЦИЙ
      const buyInstructions = await this.jupiterSwapInstructions.getJupiterSwapInstructions(buyQuote);
      const sellInstructions = await this.jupiterSwapInstructions.getJupiterSwapInstructions(sellQuote);

      if (!buyInstructions || !sellInstructions) {
        throw new Error('Не удалось создать Jupiter инструкции');
      }

      // 🔥 ШАГ 7: ОБЪЕДИНЕНИЕ ИНСТРУКЦИЙ
      const allInstructions = [
        ...buyInstructions.instructions,
        ...sellInstructions.instructions
      ];

      console.log(`✅ Создано ${allInstructions.length} инструкций для контролируемого арбитража`);

      return {
        success: true,
        instructions: allInstructions,
        buyQuote,
        sellQuote,
        profitAnalysis,
        direction: {
          buyDex: arbitrageDirection.buyDex,
          sellDex: arbitrageDirection.sellDex,
          route: `${arbitrageDirection.baseMint.slice(0,8)}... → ${arbitrageDirection.intermediateMint.slice(0,8)}... → ${arbitrageDirection.baseMint.slice(0,8)}...`
        },
        summary: `Контролируемый арбитраж: ${arbitrageDirection.buyDex} → ${arbitrageDirection.sellDex} (+${profitAnalysis.netProfitUsd} USD)`
      };

    } catch (error) {
      console.error('❌ Ошибка создания контролируемого арбитража:', error.message);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 🪐 ПОЛУЧЕНИЕ КОНТРОЛИРУЕМОГО JUPITER QUOTE С КОНКРЕТНЫМ DEX
   * 
   * Использует параметр `dexes` для принудительного выбора DEX
   * Согласно документации: "If a DEX is indicated, the route will only use that DEX"
   */
  async getControlledJupiterQuote({ inputMint, outputMint, amount, slippageBps, dexes, operation }) {
    try {
      console.log(`🪐 Контролируемый Jupiter Quote (${operation}): ${dexes}`);
      console.log(`   ${inputMint.slice(0,8)}... → ${outputMint.slice(0,8)}... (${amount})`);

      // Получаем базовые параметры
      const baseParams = getJupiterRequestParams(inputMint, outputMint, amount);
      
      // 🔥 ПРЕОБРАЗУЕМ НАЗВАНИЕ DEX В ПРАВИЛЬНЫЙ ФОРМАТ ДЛЯ JUPITER API
      const jupiterDexName = this.convertDexNameForJupiter(dexes);
      console.log(`🔄 Преобразование DEX: ${dexes} → ${jupiterDexName}`);

      // 🚨 КРИТИЧЕСКИ ВАЖНО: МАКСИМАЛЬНО СТРОГИЙ КОНТРОЛЬ БЕЗ ПРОМЕЖУТОЧНЫХ ТОКЕНОВ
      const controlledParams = {
        inputMint: inputMint,
        outputMint: outputMint,
        amount: amount,
        slippageBps: slippageBps || 50,  // 🔥 МИНИМАЛЬНЫЙ slippage для точности
        dexes: jupiterDexName,           // 🎯 ПРИНУДИТЕЛЬНЫЙ ВЫБОР DEX!
        onlyDirectRoutes: true           // 🚨 КРИТИЧЕСКИ ВАЖНО: ТОЛЬКО ПРЯМЫЕ МАРШРУТЫ!
      };

      // Убираем excludeDexes так как мы указываем конкретный DEX
      delete controlledParams.excludeDexes;

      console.log(`🎯 Параметры контролируемого запроса:`, {
        dexes: controlledParams.dexes,
        onlyDirectRoutes: controlledParams.onlyDirectRoutes,
        maxAccounts: controlledParams.maxAccounts
      });

      // 🔥 ДЕЛАЕМ ПРЯМОЙ HTTP ЗАПРОС К JUPITER API С КОНТРОЛЕМ DEX
      let quote;

      // 🚨 СТРОГИЙ РЕЖИМ: ТОЛЬКО УКАЗАННЫЙ DEX, НИКАКИХ FALLBACK!
      try {
        quote = await this.makeControlledJupiterRequest(controlledParams);

        if (!quote) {
          const dexDisplayName = jupiterDexName || dexes || 'неизвестный DEX';
          throw new Error(`Jupiter не вернул quote для ${dexDisplayName} - DEX недоступен или нет ликвидности`);
        }

        // 🚨 КРИТИЧЕСКАЯ ПРОВЕРКА: УБЕЖДАЕМСЯ ЧТО ЭТО ПРЯМОЙ МАРШРУТ
        const directRouteCheck = this.verifyDirectRoute(quote, inputMint, outputMint);
        if (!directRouteCheck.isDirect) {
          console.log(`🚨 КРИТИЧЕСКАЯ ОШИБКА: Jupiter вернул НЕ прямой маршрут!`);
          directRouteCheck.issues.forEach(issue => console.log(`   ❌ ${issue}`));
          throw new Error(`Jupiter нарушил требование прямого маршрута: ${directRouteCheck.issues.join(', ')}`);
        }

      } catch (error) {
        const dexDisplayName = jupiterDexName || dexes || 'неизвестный DEX';

        // 🔥 ОБРАБОТКА RATE LIMIT 429 - НЕ БЛОКИРУЕМ АРБИТРАЖ!
        if (error.message.includes('429') || error.message.includes('Too Many Requests')) {
          console.log(`⚠️ ${dexDisplayName} rate limit - пропускаем этот DEX`);
          return null; // Возвращаем null вместо ошибки
        }

        console.log(`⚠️ ${dexDisplayName} временно недоступен: ${error.message}`);
        throw new Error(`DEX ${dexDisplayName} недоступен для арбитража: ${error.message}`);
      }

      if (!quote) {
        console.log(`⚠️ Jupiter не вернул quote для ${dexes} - пропускаем`);
        return null;
      }

      // 🚨 КРИТИЧЕСКАЯ ВАЛИДАЦИЯ: ПРОВЕРЯЕМ ЧТО МАРШРУТ ПРОСТЕЙШИЙ
      const routeValidation = this.validateSimpleRoute(quote, inputMint, outputMint, operation);

      if (!routeValidation.isValid) {
        console.log(`🚨 КРИТИЧЕСКАЯ ОШИБКА: Jupiter вернул сложный маршрут!`);
        routeValidation.errors.forEach(error => console.log(`   ❌ ${error}`));
        throw new Error(`Jupiter вернул сложный маршрут вместо простого: ${routeValidation.errors.join(', ')}`);
      }

      // 🔥 ВАЛИДАЦИЯ: ПРОВЕРЯЕМ ЧТО JUPITER ИСПОЛЬЗОВАЛ НУЖНЫЙ DEX
      const usedDexes = this.extractUsedDexes(quote);
      const expectedDexes = [jupiterDexName, dexes]; // Проверяем и преобразованное, и оригинальное название
      const foundExpectedDex = expectedDexes.some(expectedDex => usedDexes.includes(expectedDex));

      if (!foundExpectedDex) {
        console.log(`⚠️ ВНИМАНИЕ: Jupiter использовал ${usedDexes.join(', ')} вместо ожидаемых ${expectedDexes.join(' или ')}`);
        // Не бросаем ошибку, так как Jupiter может использовать альтернативные названия
      }

      console.log(`✅ Контролируемый quote получен: ${quote.inAmount} → ${quote.outAmount}`);
      console.log(`   Использованные DEX: ${usedDexes.join(', ')}`);

      return quote;

    } catch (error) {
      console.error(`❌ Ошибка контролируемого Jupiter quote (${operation}):`, error.message);
      throw error;
    }
  }

  /**
   * 🪐 ПОЛУЧЕНИЕ ЦЕНЫ SOL ИЗ JUPITER КЭША (ИСПРАВЛЕНО)
   */
  getJupiterPrice(pair) {
    try {
      console.log(`   🔍 Ищем Jupiter цену для ${pair}...`);

      // 🔥 ПРАВИЛЬНЫЙ СПОСОБ: Получаем из глобального кэша Jupiter
      if (typeof global !== 'undefined' && global.jupiterPricesCache) {
        console.log(`   📊 Проверяем global.jupiterPricesCache...`);
        console.log(`   📊 Размер кэша: ${global.jupiterPricesCache.size}`);
        console.log(`   📊 Ключи в кэше:`, Array.from(global.jupiterPricesCache.keys()));

        const price = global.jupiterPricesCache.get(pair);
        console.log(`   📊 Цена из кэша для ${pair}: ${price}`);
        if (price && typeof price === 'number') {
          console.log(`   ✅ Jupiter цена найдена в кэше: $${price}`);
          return price;
        }

        // Пробуем разные варианты названий
        const variants = [pair, `SOL/${pair.split('/')[1]}`, `${pair.split('/')[0]}/USDC`, `${pair.split('/')[0]}/USDT`];
        for (const variant of variants) {
          const variantPrice = global.jupiterPricesCache.get(variant);
          console.log(`   📊 Проверяем вариант ${variant}: ${variantPrice}`);
          if (variantPrice && typeof variantPrice === 'number') {
            console.log(`   ✅ Jupiter цена найдена по варианту ${variant}: $${variantPrice}`);
            return variantPrice;
          }
        }
      } else {
        console.log(`   ❌ global.jupiterPricesCache недоступен!`);
      }

      console.log(`   ❌ Jupiter цена для ${pair} не найдена в кэше`);
      return null; // Цена не найдена
    } catch (error) {
      console.log(`   ⚠️ Ошибка получения Jupiter цены для ${pair}: ${error.message}`);
      return null;
    }
  }

  /**
   * 📊 АНАЛИЗ ПРИБЫЛЬНОСТИ АРБИТРАЖА
   * 🔥 ИСПРАВЛЕНО: ПРАВИЛЬНАЯ КОНВЕРТАЦИЯ LAMPORTS СОГЛАСНО ОФИЦИАЛЬНОЙ ДОКУМЕНТАЦИИ JUPITER
   */
  analyzeProfitability(inputAmount, outputAmount, buyDex, sellDex, inputMint, outputMint) {
    console.log(`📊 АНАЛИЗ ПРИБЫЛЬНОСТИ (ИСПРАВЛЕННЫЙ):`);
    console.log(`   Input: ${inputAmount} lamports (${inputMint})`);
    console.log(`   Output: ${outputAmount} lamports (${outputMint})`);

    // 🔥 ПРАВИЛЬНАЯ КОНВЕРТАЦИЯ СОГЛАСНО ОФИЦИАЛЬНОЙ ДОКУМЕНТАЦИИ JUPITER:

    // 1. ВХОДНАЯ СУММА (USDC/USDT имеют 6 decimals)
    let inputAmountUsd;
    if (inputMint.includes('EPjFWdd5') || inputMint.includes('Es9vMFrz')) {
      // USDC/USDT: 6 decimals
      inputAmountUsd = inputAmount / 1000000; // 10^6
      console.log(`   Input USD: ${inputAmount} / 10^6 = $${inputAmountUsd}`);
    } else {
      throw new Error(`Неподдерживаемый input mint: ${inputMint}`);
    }

    // 2. ВЫХОДНАЯ СУММА (SOL имеет 9 decimals)
    let outputAmountUsd;
    if (outputMint.includes('So11111111')) {
      // SOL: 9 decimals
      const outputAmountSol = outputAmount / 1000000000; // 10^9
      console.log(`   Output SOL: ${outputAmount} / 10^9 = ${outputAmountSol} SOL`);

      // 🔥 ПОЛУЧАЕМ РЕАЛЬНУЮ ЦЕНУ SOL ИЗ JUPITER КЭША (ДИНАМИЧЕСКИ!)
      let solPriceUsd;
      try {
        // 🔥 ТОЛЬКО РЕАЛЬНЫЕ ЦЕНЫ ИЗ JUPITER КЭША! НИКАКИХ FALLBACK!
        if (inputMint.includes('EPjFWdd5')) {
          // USDC → SOL, используем цену SOL/USDC из Jupiter
          const jupiterPrice = this.getJupiterPrice('SOL/USDC');
          if (!jupiterPrice) {
            throw new Error('Jupiter SOL/USDC цена недоступна! Fallback запрещен!');
          }
          solPriceUsd = jupiterPrice;
          console.log(`   🪐 Jupiter SOL/USDC цена: $${solPriceUsd} (реальная из кэша)`);
        } else if (inputMint.includes('Es9vMFrz')) {
          // USDT → SOL, используем цену SOL/USDT из Jupiter
          const jupiterPrice = this.getJupiterPrice('SOL/USDT');
          if (!jupiterPrice) {
            throw new Error('Jupiter SOL/USDT цена недоступна! Fallback запрещен!');
          }
          solPriceUsd = jupiterPrice;
          console.log(`   🪐 Jupiter SOL/USDT цена: $${solPriceUsd} (реальная из кэша)`);
        } else {
          throw new Error(`Неподдерживаемый input mint для определения цены SOL: ${inputMint}`);
        }
      } catch (error) {
        console.error(`   ❌ КРИТИЧЕСКАЯ ОШИБКА: ${error.message}`);
        throw new Error(`Fallback цены запрещены! Только реальные цены из Jupiter кэша!`);
      }

      outputAmountUsd = outputAmountSol * solPriceUsd;
      console.log(`   Output USD: ${outputAmountSol} SOL × $${solPriceUsd} = $${outputAmountUsd}`);
    } else {
      throw new Error(`Неподдерживаемый output mint: ${outputMint}`);
    }

    const grossProfitUsd = outputAmountUsd - inputAmountUsd;
    const grossProfitPercent = (grossProfitUsd / inputAmountUsd) * 100;

    console.log(`   Валовая прибыль: $${outputAmountUsd} - $${inputAmountUsd} = $${grossProfitUsd}`);

    // 🔥 РЕАЛИСТИЧНЫЕ КОМИССИИ (из trading-config.js)
    const estimatedFeesPercent = 0.05; // 0.05% общие комиссии
    const estimatedFeesUsd = inputAmountUsd * (estimatedFeesPercent / 100);

    const netProfitUsd = grossProfitUsd - estimatedFeesUsd;
    const netProfitPercent = (netProfitUsd / inputAmountUsd) * 100;

    const isProfitable = netProfitUsd > 1; // Минимум $1 прибыли

    console.log(`   Чистая прибыль: $${grossProfitUsd} - $${estimatedFeesUsd} = $${netProfitUsd}`);
    console.log(`   Прибыльность: ${isProfitable ? '✅ ДА' : '❌ НЕТ'}`);

    return {
      isProfitable,
      grossProfitUsd: parseFloat(grossProfitUsd.toFixed(2)),
      netProfitUsd: parseFloat(netProfitUsd.toFixed(2)),
      profitPercent: parseFloat(netProfitPercent.toFixed(3)),
      estimatedFeesUsd: parseFloat(estimatedFeesUsd.toFixed(2)),
      inputAmountUsd,
      outputAmountUsd,
      buyDex,
      sellDex
    };
  }

  /**
   * 🔥 ПРЯМОЙ HTTP ЗАПРОС К JUPITER API С КОНТРОЛЕМ DEX
   */
  async makeControlledJupiterRequest(params) {
    const axios = require('axios');

    try {
      const endpoint = 'https://quote-api.jup.ag/v6/quote';

      console.log(`🌐 Прямой запрос к Jupiter API: ${endpoint}`);
      console.log(`🎯 Параметры:`, {
        inputMint: params.inputMint.slice(0, 8) + '...',
        outputMint: params.outputMint.slice(0, 8) + '...',
        amount: params.amount,
        dexes: params.dexes,
        onlyDirectRoutes: params.onlyDirectRoutes,
        slippageBps: params.slippageBps
      });

      // 🔧 ПОДГОТАВЛИВАЕМ ПАРАМЕТРЫ ЗАПРОСА (ТОЛЬКО ПОДДЕРЖИВАЕМЫЕ)
      const requestParams = {
        inputMint: params.inputMint,
        outputMint: params.outputMint,
        amount: params.amount.toString(),
        onlyDirectRoutes: params.onlyDirectRoutes.toString()
      };

      // 🔧 ДОБАВЛЯЕМ DEXES ТОЛЬКО ЕСЛИ ЭТО НЕ JUPITER АГРЕГАТОР
      if (params.dexes !== null && params.dexes !== undefined && params.dexes !== '') {
        requestParams.dexes = params.dexes;     // 🎯 ОГРАНИЧИВАЕМ КОНКРЕТНЫМ DEX
        console.log(`🎯 Ограничиваем поиск DEX: ${params.dexes}`);
      } else {
        // 🚨 JUPITER АГРЕГАТОР: ИСПОЛЬЗУЕМ ВСЕ УКАЗАННЫЕ DEX!
        requestParams.dexes = 'Raydium,Raydium CLMM,Orca V1,Orca V2,Whirlpool,Meteora,Meteora DLMM,Phoenix,OpenBook V2';
        console.log(`🪐 Jupiter агрегатор: используем ВСЕ указанные DEX: ${requestParams.dexes}`);
      }

      // 🔧 ДОБАВЛЯЕМ ОПЦИОНАЛЬНЫЕ ПАРАМЕТРЫ ТОЛЬКО ЕСЛИ ОНИ ОПРЕДЕЛЕНЫ
      if (params.slippageBps !== undefined) {
        requestParams.slippageBps = params.slippageBps.toString();
      }

      const response = await axios.get(endpoint, {
        params: requestParams,
        timeout: 15000
      });

      if (!response.data) {
        throw new Error('Jupiter API вернул пустой ответ');
      }

      console.log(`✅ Jupiter API ответ получен: ${response.data.inAmount} → ${response.data.outAmount}`);

      return response.data;

    } catch (error) {
      console.error('❌ Ошибка прямого запроса к Jupiter API:', error.message);

      if (error.response) {
        console.error('📊 Детали ошибки:', {
          status: error.response.status,
          data: error.response.data
        });
      }

      throw error;
    }
  }

  /**
   * 🔍 ИЗВЛЕЧЕНИЕ ИСПОЛЬЗОВАННЫХ DEX ИЗ JUPITER QUOTE
   */
  extractUsedDexes(quote) {
    const usedDexes = [];

    if (quote.routePlan && Array.isArray(quote.routePlan)) {
      for (const step of quote.routePlan) {
        if (step.swapInfo && step.swapInfo.label) {
          usedDexes.push(step.swapInfo.label);
        }
      }
    }

    return [...new Set(usedDexes)]; // Убираем дубликаты
  }

  /**
   * 🎯 СОЗДАНИЕ НАПРАВЛЕНИЯ АРБИТРАЖА ИЗ АНАЛИЗА ЦЕН
   *
   * Преобразует результат анализатора в формат для контролируемого арбитража
   */
  createArbitrageDirectionFromAnalysis(priceAnalysis, baseMint, intermediateMint, amount, slippageBps = 150) {
    if (!priceAnalysis || !priceAnalysis.buyDex || !priceAnalysis.sellDex) {
      throw new Error('Некорректный анализ цен для создания направления арбитража');
    }

    return {
      buyDex: priceAnalysis.buyDex,
      sellDex: priceAnalysis.sellDex,
      baseMint: baseMint,
      intermediateMint: intermediateMint,
      amount: amount,
      slippageBps: slippageBps,
      expectedProfit: priceAnalysis.expectedProfit || 0,
      confidence: priceAnalysis.confidence || 0
    };
  }

  /**
   * 🔧 ВАЛИДАЦИЯ НАПРАВЛЕНИЯ АРБИТРАЖА
   */
  validateArbitrageDirection(direction) {
    const required = ['buyDex', 'sellDex', 'baseMint', 'intermediateMint', 'amount'];

    for (const field of required) {
      if (!direction[field]) {
        throw new Error(`Отсутствует обязательное поле: ${field}`);
      }
    }

    if (direction.buyDex === direction.sellDex) {
      throw new Error('DEX для покупки и продажи не могут быть одинаковыми');
    }

    if (direction.amount <= 0) {
      throw new Error('Сумма должна быть больше нуля');
    }

    return true;
  }

  /**
   * 🔍 ВАЛИДАЦИЯ АРБИТРАЖНОГО МАРШРУТА JUPITER
   *
   * Проверяет что Jupiter вернул именно арбитражный маршрут:
   * 1. Первый свап: baseMint → intermediateMint (покупка дешево)
   * 2. Второй свап: intermediateMint → baseMint (продажа дорого)
   * 3. Используются правильные DEX
   */
  validateArbitrageRoute(buyQuote, sellQuote, expectedDirection) {
    console.log('🔍 ВАЛИДАЦИЯ АРБИТРАЖНОГО МАРШРУТА...');

    const validationResults = {
      isValid: true,
      errors: [],
      warnings: []
    };

    // 🔍 ПРОВЕРКА 1: ПРАВИЛЬНОСТЬ НАПРАВЛЕНИЯ СВАПОВ
    if (buyQuote.inputMint !== expectedDirection.baseMint) {
      validationResults.errors.push(`Неправильный input для покупки: ожидался ${expectedDirection.baseMint}, получен ${buyQuote.inputMint}`);
    }

    if (buyQuote.outputMint !== expectedDirection.intermediateMint) {
      validationResults.errors.push(`Неправильный output для покупки: ожидался ${expectedDirection.intermediateMint}, получен ${buyQuote.outputMint}`);
    }

    if (sellQuote.inputMint !== expectedDirection.intermediateMint) {
      validationResults.errors.push(`Неправильный input для продажи: ожидался ${expectedDirection.intermediateMint}, получен ${sellQuote.inputMint}`);
    }

    if (sellQuote.outputMint !== expectedDirection.baseMint) {
      validationResults.errors.push(`Неправильный output для продажи: ожидался ${expectedDirection.baseMint}, получен ${sellQuote.outputMint}`);
    }

    // 🔍 ПРОВЕРКА 2: ИСПОЛЬЗОВАНИЕ ПРАВИЛЬНЫХ DEX
    const buyDexes = this.extractUsedDexes(buyQuote);
    const sellDexes = this.extractUsedDexes(sellQuote);

    if (!buyDexes.includes(expectedDirection.buyDex)) {
      validationResults.warnings.push(`DEX для покупки: ожидался ${expectedDirection.buyDex}, использованы ${buyDexes.join(', ')}`);
    }

    if (!sellDexes.includes(expectedDirection.sellDex)) {
      validationResults.warnings.push(`DEX для продажи: ожидался ${expectedDirection.sellDex}, использованы ${sellDexes.join(', ')}`);
    }

    // 🔍 ПРОВЕРКА 3: ЦИКЛИЧЕСКИЙ ХАРАКТЕР (ВОЗВРАТ К БАЗОВОМУ ТОКЕНУ)
    if (buyQuote.inputMint !== sellQuote.outputMint) {
      validationResults.errors.push(`Нарушен циклический характер: начинаем с ${buyQuote.inputMint}, заканчиваем ${sellQuote.outputMint}`);
    }

    // 🔍 ПРОВЕРКА 4: РАЗУМНОСТЬ СУММ
    const inputAmount = parseInt(buyQuote.inAmount);
    const intermediateAmount = parseInt(buyQuote.outAmount);
    const outputAmount = parseInt(sellQuote.outAmount);

    if (intermediateAmount <= 0) {
      validationResults.errors.push(`Промежуточная сумма должна быть больше нуля: ${intermediateAmount}`);
    }

    if (outputAmount <= inputAmount * 0.5) {
      validationResults.warnings.push(`Подозрительно низкая выходная сумма: ${outputAmount} vs ${inputAmount} (потеря >50%)`);
    }

    // 🚨 ПРОВЕРКА 5: СТРОГАЯ ПРОВЕРКА НА ПРОСТЕЙШИЙ МАРШРУТ (КРИТИЧЕСКИ ВАЖНО!)
    const buyRouteComplexity = this.analyzeRouteComplexity(buyQuote, 'покупки');
    const sellRouteComplexity = this.analyzeRouteComplexity(sellQuote, 'продажи');

    if (buyRouteComplexity.hasIntermediateTokens) {
      validationResults.errors.push(`КРИТИЧЕСКАЯ ОШИБКА: Маршрут покупки содержит промежуточные токены: ${buyRouteComplexity.intermediateTokens.join(' → ')}`);
    }

    if (sellRouteComplexity.hasIntermediateTokens) {
      validationResults.errors.push(`КРИТИЧЕСКАЯ ОШИБКА: Маршрут продажи содержит промежуточные токены: ${sellRouteComplexity.intermediateTokens.join(' → ')}`);
    }

    if (buyQuote.routePlan && buyQuote.routePlan.length > 1) {
      validationResults.errors.push(`КРИТИЧЕСКАЯ ОШИБКА: Сложный маршрут покупки: ${buyQuote.routePlan.length} шагов (должен быть 1)`);
    }

    if (sellQuote.routePlan && sellQuote.routePlan.length > 1) {
      validationResults.errors.push(`КРИТИЧЕСКАЯ ОШИБКА: Сложный маршрут продажи: ${sellQuote.routePlan.length} шагов (должен быть 1)`);
    }

    // 🔍 ФИНАЛЬНАЯ ОЦЕНКА
    validationResults.isValid = validationResults.errors.length === 0;

    // 🔍 ЛОГИРОВАНИЕ РЕЗУЛЬТАТОВ
    if (validationResults.isValid) {
      console.log('✅ ВАЛИДАЦИЯ ПРОЙДЕНА: Арбитражный маршрут корректен');
      if (validationResults.warnings.length > 0) {
        console.log('⚠️ Предупреждения:');
        validationResults.warnings.forEach(warning => console.log(`   - ${warning}`));
      }
    } else {
      console.log('❌ ВАЛИДАЦИЯ НЕ ПРОЙДЕНА: Обнаружены ошибки в маршруте');
      validationResults.errors.forEach(error => console.log(`   ❌ ${error}`));
    }

    console.log(`🔍 Использованные DEX: покупка [${buyDexes.join(', ')}], продажа [${sellDexes.join(', ')}]`);
    console.log(`📊 Суммы: ${inputAmount} → ${intermediateAmount} → ${outputAmount}`);

    return validationResults;
  }

  /**
   * 🛡️ ПРОВЕРКА БЕЗОПАСНОСТИ АРБИТРАЖА
   *
   * Дополнительные проверки безопасности перед выполнением
   */
  validateArbitrageSafety(profitAnalysis, direction) {
    console.log('🛡️ ПРОВЕРКА БЕЗОПАСНОСТИ АРБИТРАЖА...');

    const safetyChecks = {
      isSafe: true,
      risks: [],
      recommendations: []
    };

    // 🛡️ ПРОВЕРКА 1: МИНИМАЛЬНАЯ ПРИБЫЛЬ
    if (profitAnalysis.netProfitUsd < 1) {
      safetyChecks.risks.push(`Низкая прибыль: $${profitAnalysis.netProfitUsd} < $1`);
    }

    // 🛡️ ПРОВЕРКА 2: РАЗУМНЫЙ ПРОЦЕНТ ПРИБЫЛИ
    if (profitAnalysis.profitPercent > 10) {
      safetyChecks.risks.push(`Подозрительно высокая прибыль: ${profitAnalysis.profitPercent}% (возможна ошибка в данных)`);
    }

    if (profitAnalysis.profitPercent < 0.1) {
      safetyChecks.risks.push(`Очень низкая прибыль: ${profitAnalysis.profitPercent}% (высокий риск убытка)`);
    }

    // 🛡️ ПРОВЕРКА 3: ИЗВЕСТНЫЕ DEX
    const knownDexes = Object.keys(this.dexMapping);
    if (!knownDexes.includes(direction.buyDex)) {
      safetyChecks.risks.push(`Неизвестный DEX для покупки: ${direction.buyDex}`);
    }

    if (!knownDexes.includes(direction.sellDex)) {
      safetyChecks.risks.push(`Неизвестный DEX для продажи: ${direction.sellDex}`);
    }

    // 🛡️ ПРОВЕРКА 4: РАЗМЕР ПОЗИЦИИ
    const positionSizeUsd = direction.amount / 1000000; // Предполагаем USDC
    if (positionSizeUsd > 50000) {
      safetyChecks.risks.push(`Большая позиция: $${positionSizeUsd} (высокий price impact)`);
    }

    // 🛡️ ФИНАЛЬНАЯ ОЦЕНКА
    safetyChecks.isSafe = safetyChecks.risks.length === 0;

    // 🛡️ ЛОГИРОВАНИЕ
    if (safetyChecks.isSafe) {
      console.log('✅ ПРОВЕРКА БЕЗОПАСНОСТИ ПРОЙДЕНА');
    } else {
      console.log('⚠️ ОБНАРУЖЕНЫ РИСКИ:');
      safetyChecks.risks.forEach(risk => console.log(`   ⚠️ ${risk}`));
    }

    return safetyChecks;
  }

  /**
   * 🚨 АНАЛИЗ СЛОЖНОСТИ МАРШРУТА (КРИТИЧЕСКИ ВАЖНО ДЛЯ АРБИТРАЖА!)
   *
   * Проверяет что Jupiter не добавил промежуточные токены которые съедят прибыль
   */
  analyzeRouteComplexity(quote, operationType) {
    console.log(`🚨 АНАЛИЗ СЛОЖНОСТИ МАРШРУТА ${operationType.toUpperCase()}...`);

    const analysis = {
      hasIntermediateTokens: false,
      intermediateTokens: [],
      totalSteps: 0,
      usedDexes: [],
      isSimpleRoute: true
    };

    if (!quote.routePlan || !Array.isArray(quote.routePlan)) {
      console.log(`⚠️ Нет routePlan в quote для ${operationType}`);
      return analysis;
    }

    analysis.totalSteps = quote.routePlan.length;
    console.log(`📊 Общее количество шагов в маршруте ${operationType}: ${analysis.totalSteps}`);

    // Анализируем каждый шаг маршрута
    for (let i = 0; i < quote.routePlan.length; i++) {
      const step = quote.routePlan[i];

      if (step.swapInfo) {
        const stepInfo = {
          step: i + 1,
          dex: step.swapInfo.label || 'Unknown',
          inputMint: step.swapInfo.inputMint || 'Unknown',
          outputMint: step.swapInfo.outputMint || 'Unknown',
          inAmount: step.swapInfo.inAmount || 0,
          outAmount: step.swapInfo.outAmount || 0
        };

        analysis.usedDexes.push(stepInfo.dex);

        console.log(`🔍 Шаг ${stepInfo.step}: ${stepInfo.inputMint.slice(0,8)}... → ${stepInfo.outputMint.slice(0,8)}... на ${stepInfo.dex}`);

        // Проверяем на промежуточные токены
        if (i > 0) {
          // Это не первый шаг, значит есть промежуточные токены
          analysis.hasIntermediateTokens = true;
          analysis.intermediateTokens.push(`${stepInfo.inputMint.slice(0,8)}...`);
        }
      }
    }

    // Определяем простоту маршрута
    analysis.isSimpleRoute = analysis.totalSteps === 1 && !analysis.hasIntermediateTokens;

    // Логирование результатов
    if (analysis.isSimpleRoute) {
      console.log(`✅ ПРОСТОЙ МАРШРУТ ${operationType.toUpperCase()}: 1 шаг, без промежуточных токенов`);
    } else {
      console.log(`🚨 СЛОЖНЫЙ МАРШРУТ ${operationType.toUpperCase()}:`);
      console.log(`   📊 Шагов: ${analysis.totalSteps}`);
      console.log(`   🔗 Промежуточные токены: ${analysis.hasIntermediateTokens ? analysis.intermediateTokens.join(' → ') : 'Нет'}`);
      console.log(`   🏦 Использованные DEX: ${analysis.usedDexes.join(', ')}`);
    }

    return analysis;
  }

  /**
   * 🚨 ВАЛИДАЦИЯ ПРОСТЕЙШЕГО МАРШРУТА (КРИТИЧЕСКИ ВАЖНО!)
   *
   * Проверяет что Jupiter вернул именно простой маршрут без промежуточных токенов
   */
  validateSimpleRoute(quote, expectedInputMint, expectedOutputMint, operation) {
    console.log(`🚨 ВАЛИДАЦИЯ ПРОСТЕЙШЕГО МАРШРУТА ДЛЯ ${operation.toUpperCase()}...`);

    const validation = {
      isValid: true,
      errors: [],
      warnings: []
    };

    // 🚨 ПРОВЕРКА 1: ПРАВИЛЬНЫЕ INPUT/OUTPUT ТОКЕНЫ
    if (quote.inputMint !== expectedInputMint) {
      validation.errors.push(`Неправильный input токен: ожидался ${expectedInputMint}, получен ${quote.inputMint}`);
    }

    if (quote.outputMint !== expectedOutputMint) {
      validation.errors.push(`Неправильный output токен: ожидался ${expectedOutputMint}, получен ${quote.outputMint}`);
    }

    // 🚨 ПРОВЕРКА 2: ТОЛЬКО ОДИН ШАГ В МАРШРУТЕ
    if (quote.routePlan && quote.routePlan.length > 1) {
      validation.errors.push(`Сложный маршрут: ${quote.routePlan.length} шагов вместо 1`);

      // Детальный анализ каждого шага
      quote.routePlan.forEach((step, index) => {
        if (step.swapInfo) {
          const stepInfo = `Шаг ${index + 1}: ${step.swapInfo.inputMint?.slice(0,8)}... → ${step.swapInfo.outputMint?.slice(0,8)}... на ${step.swapInfo.label}`;
          validation.errors.push(`Промежуточный шаг: ${stepInfo}`);
        }
      });
    }

    // 🚨 ПРОВЕРКА 3: НЕТ ПРОМЕЖУТОЧНЫХ ТОКЕНОВ
    if (quote.routePlan && quote.routePlan.length === 1) {
      const step = quote.routePlan[0];
      if (step.swapInfo) {
        if (step.swapInfo.inputMint !== expectedInputMint) {
          validation.errors.push(`Промежуточный input токен в шаге: ${step.swapInfo.inputMint} вместо ${expectedInputMint}`);
        }
        if (step.swapInfo.outputMint !== expectedOutputMint) {
          validation.errors.push(`Промежуточный output токен в шаге: ${step.swapInfo.outputMint} вместо ${expectedOutputMint}`);
        }
      }
    }

    // 🚨 ПРОВЕРКА 4: РАЗУМНЫЕ СУММЫ (НЕТ ПОТЕРЬ НА КОНВЕРТАЦИИ)
    const inputAmount = parseInt(quote.inAmount);
    const outputAmount = parseInt(quote.outAmount);

    if (outputAmount === 0) {
      validation.errors.push(`Нулевая выходная сумма - возможна ошибка в маршруте`);
    }

    // 🚨 ПРОВЕРКА 5: КРИТИЧЕСКАЯ - НЕТ СКРЫТЫХ ПРОМЕЖУТОЧНЫХ ТОКЕНОВ
    if (quote.swapInfo && quote.swapInfo.length > 1) {
      validation.errors.push(`КРИТИЧЕСКАЯ ОШИБКА: Обнаружены скрытые промежуточные свапы: ${quote.swapInfo.length} операций`);
    }

    // 🚨 ПРОВЕРКА 6: ПРОВЕРЯЕМ ЧТО НЕТ ДРУГИХ ТОКЕНОВ В МАРШРУТЕ
    if (quote.routePlan && quote.routePlan.length === 1) {
      const step = quote.routePlan[0];
      if (step.swapInfo && step.swapInfo.ammKey) {
        // Проверяем что это действительно прямой свап без промежуточных пулов
        console.log(`🔍 Проверяем AMM ключ: ${step.swapInfo.ammKey?.slice(0,8)}...`);
      }
    }

    // 🚨 ПРОВЕРКА 7: МАКСИМАЛЬНАЯ ПОТЕРЯ НЕ БОЛЕЕ 1% (защита от скрытых потерь)
    if (operation === 'BUY') {
      // Для покупки: должны получить разумное количество токенов
      const expectedMinOutput = inputAmount * 0.99; // Минимум 99% эффективности
      if (outputAmount < expectedMinOutput / 1000000) { // Учитываем разные decimals
        validation.warnings.push(`Подозрительно низкая эффективность покупки: возможны скрытые потери`);
      }
    }

    // Финальная оценка
    validation.isValid = validation.errors.length === 0;

    // Логирование
    if (validation.isValid) {
      console.log(`✅ ПРОСТЕЙШИЙ МАРШРУТ ПОДТВЕРЖДЕН для ${operation.toUpperCase()}`);
      console.log(`   📊 ${expectedInputMint.slice(0,8)}... → ${expectedOutputMint.slice(0,8)}...`);
      console.log(`   💰 ${inputAmount} → ${outputAmount}`);
    } else {
      console.log(`🚨 МАРШРУТ НЕ ПРОШЕЛ ВАЛИДАЦИЮ для ${operation.toUpperCase()}:`);
      validation.errors.forEach(error => console.log(`   ❌ ${error}`));
    }

    return validation;
  }

  /**
   * 🚨 ПРОВЕРКА ЧТО МАРШРУТ ДЕЙСТВИТЕЛЬНО ПРЯМОЙ (КРИТИЧЕСКИ ВАЖНО!)
   *
   * Финальная проверка что Jupiter не добавил скрытые промежуточные токены
   */
  verifyDirectRoute(quote, expectedInputMint, expectedOutputMint) {
    console.log(`🚨 ФИНАЛЬНАЯ ПРОВЕРКА ПРЯМОГО МАРШРУТА...`);

    const verification = {
      isDirect: true,
      issues: []
    };

    // 🚨 ПРОВЕРКА 1: ТОЧНОЕ СООТВЕТСТВИЕ INPUT/OUTPUT
    if (quote.inputMint !== expectedInputMint) {
      verification.issues.push(`Input токен не соответствует: ${quote.inputMint} ≠ ${expectedInputMint}`);
    }

    if (quote.outputMint !== expectedOutputMint) {
      verification.issues.push(`Output токен не соответствует: ${quote.outputMint} ≠ ${expectedOutputMint}`);
    }

    // 🚨 ПРОВЕРКА 2: ТОЛЬКО ОДИН ШАГ В ROUTE PLAN
    if (!quote.routePlan || quote.routePlan.length === 0) {
      verification.issues.push(`Отсутствует routePlan - подозрительно`);
    } else if (quote.routePlan.length > 1) {
      verification.issues.push(`Множественные шаги в routePlan: ${quote.routePlan.length} (должен быть 1)`);
    }

    // 🚨 ПРОВЕРКА 3: АНАЛИЗ ПЕРВОГО (И ЕДИНСТВЕННОГО) ШАГА
    if (quote.routePlan && quote.routePlan.length === 1) {
      const step = quote.routePlan[0];

      if (!step.swapInfo) {
        verification.issues.push(`Отсутствует swapInfo в единственном шаге`);
      } else {
        // Проверяем что шаг соответствует ожиданиям
        if (step.swapInfo.inputMint !== expectedInputMint) {
          verification.issues.push(`Шаг input не соответствует: ${step.swapInfo.inputMint} ≠ ${expectedInputMint}`);
        }

        if (step.swapInfo.outputMint !== expectedOutputMint) {
          verification.issues.push(`Шаг output не соответствует: ${step.swapInfo.outputMint} ≠ ${expectedOutputMint}`);
        }

        // Проверяем что нет вложенных операций
        if (step.swapInfo.feeAmount && parseInt(step.swapInfo.feeAmount) > parseInt(quote.inAmount) * 0.1) {
          verification.issues.push(`Подозрительно высокая комиссия: ${step.swapInfo.feeAmount} (возможны скрытые операции)`);
        }
      }
    }

    // 🚨 ПРОВЕРКА 4: НЕТ ДОПОЛНИТЕЛЬНЫХ SWAP INFO
    if (quote.swapInfo && Array.isArray(quote.swapInfo) && quote.swapInfo.length > 1) {
      verification.issues.push(`Множественные swapInfo: ${quote.swapInfo.length} (должен быть 1)`);
    }

    // 🚨 ПРОВЕРКА 5: РАЗУМНОЕ СООТНОШЕНИЕ INPUT/OUTPUT
    const inputAmount = parseInt(quote.inAmount);
    const outputAmount = parseInt(quote.outAmount);

    if (outputAmount === 0) {
      verification.issues.push(`Нулевая выходная сумма - критическая ошибка`);
    }

    // Финальная оценка
    verification.isDirect = verification.issues.length === 0;

    // Логирование
    if (verification.isDirect) {
      console.log(`✅ ПРЯМОЙ МАРШРУТ ПОДТВЕРЖДЕН`);
      console.log(`   📊 ${expectedInputMint.slice(0,8)}... → ${expectedOutputMint.slice(0,8)}...`);
      console.log(`   💰 ${inputAmount} → ${outputAmount}`);
      console.log(`   🏦 1 шаг, без промежуточных токенов`);
    } else {
      console.log(`🚨 МАРШРУТ НЕ ЯВЛЯЕТСЯ ПРЯМЫМ:`);
      verification.issues.forEach(issue => console.log(`   ❌ ${issue}`));
    }

    return verification;
  }

  /**
   * 🔄 ПРЕОБРАЗОВАНИЕ НАЗВАНИЯ DEX ДЛЯ JUPITER API
   *
   * Преобразует названия DEX из анализатора в правильные названия для Jupiter API
   */
  convertDexNameForJupiter(dexName) {
    // Проверяем прямое соответствие
    if (this.dexMapping.hasOwnProperty(dexName)) {
      const mapped = this.dexMapping[dexName];
      if (mapped === null) {
        console.log(`🪐 ${dexName} - агрегатор, используем все DEX`);
      } else {
        console.log(`🎯 ${dexName} → ${mapped}`);
      }
      return mapped;
    }

    // Если нет прямого соответствия, возвращаем как есть
    console.log(`⚠️ DEX ${dexName} не найден в маппинге, используем как есть`);
    return dexName;
  }

  /**
   * 🚨 ПОЛУЧЕНИЕ СПИСКА ИСКЛЮЧЕННЫХ DEX ДЛЯ СТРОГОГО КОНТРОЛЯ МАРШРУТА
   *
   * Исключаем ВСЕ DEX кроме нужного, чтобы Jupiter не мог использовать промежуточные DEX
   */
  getExcludedDexesForStrictRoute(allowedDex) {
    // 🚨 ТОЛЬКО УКАЗАННЫЕ DEX! НЕ БОЛЬШЕ!
    const allKnownDexes = [
      'Raydium', 'Raydium CLMM', 'Orca V1', 'Orca V2', 'Whirlpool',
      'Meteora', 'Meteora DLMM', 'Phoenix', 'OpenBook V2'
      // 🚨 ВСЕ ОСТАЛЬНЫЕ DEX УДАЛЕНЫ!
    ];

    // Исключаем все кроме разрешенного DEX
    const excludedDexes = allKnownDexes.filter(dex => dex !== allowedDex);

    console.log(`🚨 СТРОГИЙ КОНТРОЛЬ: Разрешен только ${allowedDex}, исключено ${excludedDexes.length} DEX`);

    return excludedDexes;
  }


}

module.exports = ArbitrageDirectionController;
