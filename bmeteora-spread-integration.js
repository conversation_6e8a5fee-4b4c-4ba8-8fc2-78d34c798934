// 🔗 ИНТЕГРАЦИЯ АНАЛИЗАТОРА СПРЕДОВ В BMETEORA.JS
// Модуль для подключения AtomicFlashSpreadAnalyzer к основному боту

const AtomicFlashSpreadAnalyzer = require('./atomic-flash-spread-analyzer.js');
const colors = require('colors');

/**
 * 🔗 ИНТЕГРАЦИОННЫЙ МОДУЛЬ ДЛЯ BMETEORA.JS
 * 
 * Добавляет возможности анализа спредов в существующий бот
 * без изменения основной архитектуры
 */
class BMeteoraSpreadsIntegration {
    constructor(meteoraBot) {
        this.meteoraBot = meteoraBot;
        this.spreadAnalyzer = null;
        this.isEnabled = false;
        
        // 🎯 КОНФИГУРАЦИЯ ИНТЕГРАЦИИ
        this.config = {
            enableSpreadAnalysis: true,
            spreadAnalysisInterval: 1000, // 1 секунда
            minSpreadForAlert: 0.1,       // 0.1% для алерта
            minSpreadForExecution: 0.2,   // 0.2% для исполнения
            maxConcurrentAnalysis: 3,     // Максимум 3 анализа одновременно
            enableAutoExecution: false,   // Автоисполнение отключено по умолчанию
        };
        
        // 📊 СТАТИСТИКА ИНТЕГРАЦИИ
        this.integrationStats = {
            totalSpreadsAnalyzed: 0,
            profitableOpportunitiesFound: 0,
            autoExecutedTrades: 0,
            totalProfitFromSpreads: 0,
            lastAnalysisTime: 0
        };
        
        // 🔄 АКТИВНЫЕ АНАЛИЗЫ
        this.activeAnalyses = new Set();
        this.analysisQueue = [];
        
        console.log('🔗 BMeteora Spreads Integration инициализирован');
    }

    /**
     * 🚀 ИНИЦИАЛИЗАЦИЯ ИНТЕГРАЦИИ
     */
    async initialize() {
        try {
            console.log('🚀 Инициализация интеграции анализатора спредов...');
            
            // 🎯 СОЗДАЕМ АНАЛИЗАТОР СПРЕДОВ
            this.spreadAnalyzer = new AtomicFlashSpreadAnalyzer(
                this.meteoraBot.connection,
                this.meteoraBot.wallet,
                {
                    minSpreadPercent: this.config.minSpreadForExecution,
                    maxFlashLoanUSD: this.meteoraBot.config?.maxFlashLoan || 50000,
                    emergencySpreadPercent: this.config.minSpreadForAlert
                }
            );
            
            // 🔗 ИНТЕГРИРУЕМ В ОСНОВНОЙ БОТ
            this.integrateWithMainBot();
            
            // 🔄 ЗАПУСКАЕМ ПЕРИОДИЧЕСКИЙ АНАЛИЗ
            if (this.config.enableSpreadAnalysis) {
                this.startPeriodicSpreadAnalysis();
            }
            
            this.isEnabled = true;
            console.log('✅ Интеграция анализатора спредов успешно инициализирована');
            
            return true;
            
        } catch (error) {
            console.error('❌ Ошибка инициализации интеграции:', error.message);
            return false;
        }
    }

    /**
     * 🔗 ИНТЕГРАЦИЯ С ОСНОВНЫМ БОТОМ
     */
    integrateWithMainBot() {
        console.log('🔗 Интеграция с основным BMeteora ботом...');
        
        // 🎯 ДОБАВЛЯЕМ МЕТОД АНАЛИЗА СПРЕДОВ В ОСНОВНОЙ БОТ
        this.meteoraBot.analyzeSpreadOpportunities = async () => {
            return await this.analyzeCurrentSpreads();
        };
        
        // 🎯 ДОБАВЛЯЕМ МЕТОД ПОЛУЧЕНИЯ СТАТИСТИКИ СПРЕДОВ
        this.meteoraBot.getSpreadStats = () => {
            return this.getIntegrationStats();
        };
        
        // 🎯 ДОБАВЛЯЕМ МЕТОД ВКЛЮЧЕНИЯ/ВЫКЛЮЧЕНИЯ АНАЛИЗА СПРЕДОВ
        this.meteoraBot.toggleSpreadAnalysis = (enabled) => {
            this.toggleSpreadAnalysis(enabled);
        };
        
        // 🔄 ПЕРЕХВАТЫВАЕМ ОСНОВНОЙ ЦИКЛ АНАЛИЗА
        const originalAnalyzeMethod = this.meteoraBot.analyzeAndExecuteArbitrage;
        if (originalAnalyzeMethod) {
            this.meteoraBot.analyzeAndExecuteArbitrage = async () => {
                // 🔥 СНАЧАЛА ВЫПОЛНЯЕМ АНАЛИЗ СПРЕДОВ
                await this.enhanceArbitrageAnalysis();
                
                // 🔄 ЗАТЕМ ВЫПОЛНЯЕМ ОРИГИНАЛЬНЫЙ АНАЛИЗ
                return await originalAnalyzeMethod.call(this.meteoraBot);
            };
        }
        
        console.log('✅ Интеграция с основным ботом завершена');
    }

    /**
     * 🔄 ЗАПУСК ПЕРИОДИЧЕСКОГО АНАЛИЗА СПРЕДОВ
     */
    startPeriodicSpreadAnalysis() {
        console.log(`🔄 Запуск периодического анализа спредов каждые ${this.config.spreadAnalysisInterval}ms`);
        
        this.spreadAnalysisInterval = setInterval(async () => {
            if (this.activeAnalyses.size < this.config.maxConcurrentAnalysis) {
                await this.performBackgroundSpreadAnalysis();
            }
        }, this.config.spreadAnalysisInterval);
    }

    /**
     * 🔍 АНАЛИЗ ТЕКУЩИХ СПРЕДОВ
     */
    async analyzeCurrentSpreads() {
        if (!this.isEnabled || !this.spreadAnalyzer) {
            return [];
        }
        
        const analysisId = `analysis_${Date.now()}`;
        this.activeAnalyses.add(analysisId);
        
        try {
            console.log('🔍 Анализ текущих спредов...');
            
            // 🎯 ПОЛУЧАЕМ ПУЛЫ ИЗ ОСНОВНОГО БОТА
            const pools = this.getMeteoraPools();
            
            if (pools.length < 2) {
                console.log('⚠️ Недостаточно пулов для анализа спредов');
                return [];
            }
            
            // 🚀 ВЫПОЛНЯЕМ АНАЛИЗ
            const opportunities = await this.spreadAnalyzer.analyzeAtomicArbitrageOpportunities(pools);
            
            // 📊 ОБНОВЛЯЕМ СТАТИСТИКУ
            this.integrationStats.totalSpreadsAnalyzed++;
            this.integrationStats.profitableOpportunitiesFound += opportunities.length;
            this.integrationStats.lastAnalysisTime = Date.now();
            
            // 🎯 ФИЛЬТРУЕМ ПО МИНИМАЛЬНОМУ СПРЕДУ
            const significantOpportunities = opportunities.filter(opp => 
                opp.spread.percent >= this.config.minSpreadForAlert
            );
            
            if (significantOpportunities.length > 0) {
                console.log(`🎯 Найдено ${significantOpportunities.length} значимых спред-возможностей:`);
                significantOpportunities.forEach((opp, i) => {
                    console.log(`   ${i + 1}. ${opp.direction}: ${opp.spread.percent.toFixed(4)}% спред, $${opp.expectedProfit.net.toFixed(2)} прибыль`);
                });
                
                // 🔥 АВТОИСПОЛНЕНИЕ ЕСЛИ ВКЛЮЧЕНО
                if (this.config.enableAutoExecution) {
                    await this.autoExecuteBestOpportunity(significantOpportunities);
                }
            }
            
            return significantOpportunities;
            
        } catch (error) {
            console.error('❌ Ошибка анализа спредов:', error.message);
            return [];
        } finally {
            this.activeAnalyses.delete(analysisId);
        }
    }

    /**
     * 🔄 ФОНОВЫЙ АНАЛИЗ СПРЕДОВ
     */
    async performBackgroundSpreadAnalysis() {
        try {
            const opportunities = await this.analyzeCurrentSpreads();
            
            // 🚨 АЛЕРТЫ ДЛЯ БОЛЬШИХ СПРЕДОВ
            const bigOpportunities = opportunities.filter(opp => 
                opp.spread.percent >= 1.0 // 1%+ спред
            );
            
            if (bigOpportunities.length > 0) {
                console.log(`🚨 БОЛЬШИЕ СПРЕДЫ ОБНАРУЖЕНЫ!`.red.bold);
                bigOpportunities.forEach(opp => {
                    console.log(`   🎯 ${opp.direction}: ${opp.spread.percent.toFixed(2)}% спред!`.yellow.bold);
                    console.log(`   💰 Потенциальная прибыль: $${opp.expectedProfit.net.toFixed(2)}`);
                });
            }
            
        } catch (error) {
            console.error('❌ Ошибка фонового анализа:', error.message);
        }
    }

    /**
     * 🔥 УЛУЧШЕНИЕ АНАЛИЗА АРБИТРАЖА ОСНОВНОГО БОТА
     */
    async enhanceArbitrageAnalysis() {
        if (!this.isEnabled) return;
        
        try {
            // 🎯 ПОЛУЧАЕМ СПРЕД-ВОЗМОЖНОСТИ
            const spreadOpportunities = await this.analyzeCurrentSpreads();
            
            // 🔗 ДОБАВЛЯЕМ ИХ В КОНТЕКСТ ОСНОВНОГО БОТА
            if (this.meteoraBot.currentOpportunities) {
                this.meteoraBot.currentOpportunities.spreads = spreadOpportunities;
            } else {
                this.meteoraBot.currentOpportunities = { spreads: spreadOpportunities };
            }
            
            // 🎯 ПРИОРИТИЗИРУЕМ ЛУЧШИЕ ВОЗМОЖНОСТИ
            const bestSpreadOpportunity = spreadOpportunities[0];
            if (bestSpreadOpportunity && bestSpreadOpportunity.spread.percent >= this.config.minSpreadForExecution) {
                console.log(`🎯 Лучшая спред-возможность: ${bestSpreadOpportunity.spread.percent.toFixed(4)}% спред`);
                
                // 🔗 ПЕРЕДАЕМ В ОСНОВНОЙ БОТ ДЛЯ ПРИОРИТЕТНОГО ИСПОЛНЕНИЯ
                this.meteoraBot.priorityOpportunity = bestSpreadOpportunity;
            }
            
        } catch (error) {
            console.error('❌ Ошибка улучшения анализа:', error.message);
        }
    }

    /**
     * 🔥 АВТОИСПОЛНЕНИЕ ЛУЧШЕЙ ВОЗМОЖНОСТИ
     */
    async autoExecuteBestOpportunity(opportunities) {
        if (!this.config.enableAutoExecution || opportunities.length === 0) {
            return;
        }
        
        const bestOpportunity = opportunities[0];
        
        // 🎯 ПРОВЕРЯЕМ КРИТЕРИИ ДЛЯ АВТОИСПОЛНЕНИЯ
        if (bestOpportunity.spread.percent < this.config.minSpreadForExecution) {
            return;
        }
        
        try {
            console.log(`🔥 Автоисполнение лучшей возможности: ${bestOpportunity.spread.percent.toFixed(4)}% спред`);
            
            // 🚀 ПЕРЕДАЕМ В ОСНОВНОЙ БОТ ДЛЯ ИСПОЛНЕНИЯ
            if (this.meteoraBot.executeFlashLoanArbitrage) {
                const result = await this.meteoraBot.executeFlashLoanArbitrage(bestOpportunity);
                
                if (result && result.signature) {
                    this.integrationStats.autoExecutedTrades++;
                    this.integrationStats.totalProfitFromSpreads += bestOpportunity.expectedProfit.net;
                    
                    console.log(`✅ Автоисполнение успешно: ${result.signature.slice(0, 8)}...`);
                    console.log(`💰 Прибыль: $${bestOpportunity.expectedProfit.net.toFixed(2)}`);
                }
            }
            
        } catch (error) {
            console.error('❌ Ошибка автоисполнения:', error.message);
        }
    }

    /**
     * 🎯 ПОЛУЧЕНИЕ ПУЛОВ METEORA ИЗ ОСНОВНОГО БОТА
     */
    getMeteoraPools() {
        // 🔗 ПОЛУЧАЕМ ПУЛЫ ИЗ ОСНОВНОГО БОТА
        if (this.meteoraBot.meteoraPools) {
            return this.meteoraBot.meteoraPools;
        }
        
        // 🎯 FALLBACK: СТАНДАРТНЫЕ ПУЛЫ
        return [
            { address: '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6', name: 'Pool 1' },
            { address: 'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y', name: 'Pool 2' },
            { address: 'HTvjzsfX3yU6BUodCjZ5vZkUrAxMDTrBs3CJaq43ashR', name: 'Pool 3' }
        ];
    }

    /**
     * 🔄 ПЕРЕКЛЮЧЕНИЕ АНАЛИЗА СПРЕДОВ
     */
    toggleSpreadAnalysis(enabled) {
        this.config.enableSpreadAnalysis = enabled;
        
        if (enabled && !this.spreadAnalysisInterval) {
            this.startPeriodicSpreadAnalysis();
            console.log('✅ Анализ спредов включен');
        } else if (!enabled && this.spreadAnalysisInterval) {
            clearInterval(this.spreadAnalysisInterval);
            this.spreadAnalysisInterval = null;
            console.log('⏸️ Анализ спредов отключен');
        }
    }

    /**
     * 📊 ПОЛУЧЕНИЕ СТАТИСТИКИ ИНТЕГРАЦИИ
     */
    getIntegrationStats() {
        const spreadStats = this.spreadAnalyzer ? this.spreadAnalyzer.getStats() : {};
        
        return {
            integration: this.integrationStats,
            spreadAnalyzer: spreadStats,
            isEnabled: this.isEnabled,
            activeAnalyses: this.activeAnalyses.size,
            config: this.config
        };
    }

    /**
     * 📊 ПОКАЗАТЬ СТАТИСТИКУ
     */
    showStats() {
        const stats = this.getIntegrationStats();
        
        console.log('\n📊 СТАТИСТИКА АНАЛИЗА СПРЕДОВ:'.yellow.bold);
        console.log('═'.repeat(50));
        console.log(`🔍 Всего анализов: ${stats.integration.totalSpreadsAnalyzed}`);
        console.log(`🎯 Найдено возможностей: ${stats.integration.profitableOpportunitiesFound}`);
        console.log(`🔥 Автоисполнено сделок: ${stats.integration.autoExecutedTrades}`);
        console.log(`💰 Общая прибыль от спредов: $${stats.integration.totalProfitFromSpreads.toFixed(2)}`);
        console.log(`⚡ Активных анализов: ${stats.activeAnalyses}`);
        console.log(`🔄 Статус: ${stats.isEnabled ? 'Включен' : 'Отключен'}`);
        
        if (stats.spreadAnalyzer.averageAnalysisTime) {
            console.log(`⏱️ Среднее время анализа: ${stats.spreadAnalyzer.averageAnalysisTime.toFixed(2)}ms`);
        }
    }

    /**
     * 🛑 ОСТАНОВКА ИНТЕГРАЦИИ
     */
    stop() {
        console.log('🛑 Остановка интеграции анализатора спредов...');
        
        this.isEnabled = false;
        
        if (this.spreadAnalysisInterval) {
            clearInterval(this.spreadAnalysisInterval);
            this.spreadAnalysisInterval = null;
        }
        
        // 🧹 ОЧИСТКА КЭШЕЙ
        if (this.spreadAnalyzer) {
            this.spreadAnalyzer.clearCaches();
        }
        
        this.activeAnalyses.clear();
        this.analysisQueue = [];
        
        console.log('✅ Интеграция остановлена');
    }
}

module.exports = BMeteoraSpreadsIntegration;
