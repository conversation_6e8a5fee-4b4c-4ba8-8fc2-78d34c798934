const crypto = require('crypto');

/**
 * 🔧 УТИЛИТА ДЛЯ ВЫЧИСЛЕНИЯ ANCHOR DISCRIMINATORS
 * Anchor discriminators = SHA256("global:method_name").slice(0, 8)
 */

function calculateAnchorDiscriminator(methodName) {
    const input = `global:${methodName}`;
    const hash = crypto.createHash('sha256').update(input).digest();
    const discriminator = hash.slice(0, 8);
    
    console.log(`📋 Метод: ${methodName}`);
    console.log(`   Input: "${input}"`);
    console.log(`   SHA256: ${hash.toString('hex')}`);
    console.log(`   Discriminator: [${Array.from(discriminator).join(', ')}]`);
    console.log(`   Hex: ${discriminator.toString('hex')}`);
    console.log('');
    
    return discriminator;
}

console.log('🔧 ВЫЧИСЛЕНИЕ MARGINFI ANCHOR DISCRIMINATORS\n');

// 🎯 ОСНОВНЫЕ MARGINFI МЕТОДЫ
const marginfiMethods = [
    'lending_account_borrow',
    'lending_account_repay', 
    'lending_account_deposit',
    'lending_account_withdraw',
    'lending_account_start_flashloan',
    'lending_account_end_flashloan',
    'marginfi_account_initialize',
    'lending_pool_borrow',
    'lending_pool_repay'
];

console.log('📊 РЕЗУЛЬТАТЫ ВЫЧИСЛЕНИЙ:');
console.log('='.repeat(80));

const discriminators = {};

marginfiMethods.forEach(method => {
    discriminators[method] = calculateAnchorDiscriminator(method);
});

console.log('🎯 ИТОГОВЫЕ DISCRIMINATORS ДЛЯ КОДА:');
console.log('='.repeat(80));

console.log('this.DISCRIMINATORS = {');
Object.entries(discriminators).forEach(([method, discriminator]) => {
    const constName = method.toUpperCase();
    const bytes = Array.from(discriminator).map(b => `0x${b.toString(16).padStart(2, '0')}`).join(', ');
    console.log(`    ${constName}: Buffer.from([${bytes}]), // ${method}`);
});
console.log('};');

console.log('\n✅ DISCRIMINATORS ГОТОВЫ ДЛЯ ИСПОЛЬЗОВАНИЯ!');
