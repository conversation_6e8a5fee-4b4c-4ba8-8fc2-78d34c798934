/**
 * 🔧 ВЫЧИСЛЕНИЕ ПРАВИЛЬНЫХ METEORA DLMM DISCRIMINATORS
 * 
 * Использует тот же алгоритм, что и для MarginFi
 */

const crypto = require('crypto');

/**
 * 🔧 ВЫЧИСЛЕНИЕ ANCHOR DISCRIMINATOR
 */
function calculateAnchorDiscriminator(methodName) {
    const input = `global:${methodName}`;
    const hash = crypto.createHash('sha256').update(input).digest();
    return hash.slice(0, 8); // Первые 8 байт
}

console.log('🔧 ВЫЧИСЛЕНИЕ METEORA DLMM ANCHOR DISCRIMINATORS\n');

// 🎯 ОСНОВНЫЕ METEORA DLMM МЕТОДЫ
const meteoraMethods = [
    'swap',
    'swap_exact_out',
    'add_liquidity',
    'remove_liquidity',
    'add_liquidity_by_weight',
    'remove_liquidity_by_range',
    'initialize_lb_pair',
    'initialize_bin_array',
    'increase_oracle_length',
    'set_activation_slot'
];

console.log('📊 РЕЗУЛЬТАТЫ ВЫЧИСЛЕНИЙ:');
console.log('='.repeat(80));

meteoraMethods.forEach(method => {
    const discriminator = calculateAnchorDiscriminator(method);
    const bytes = Array.from(discriminator);
    const hex = discriminator.toString('hex');
    
    console.log(`📋 Метод: ${method}`);
    console.log(`   Input: "global:${method}"`);
    console.log(`   SHA256: ${crypto.createHash('sha256').update(`global:${method}`).digest('hex')}`);
    console.log(`   Discriminator: [${bytes.join(', ')}]`);
    console.log(`   Hex: ${hex}`);
    console.log('');
});

const discriminators = {};

meteoraMethods.forEach(method => {
    discriminators[method] = calculateAnchorDiscriminator(method);
});

console.log('🎯 ИТОГОВЫЕ DISCRIMINATORS ДЛЯ КОДА:');
console.log('='.repeat(80));

console.log('this.METEORA_DISCRIMINATORS = {');
Object.entries(discriminators).forEach(([method, discriminator]) => {
    const constName = method.toUpperCase();
    const bytes = Array.from(discriminator).map(b => `0x${b.toString(16).padStart(2, '0')}`).join(', ');
    console.log(`    ${constName}: Buffer.from([${bytes}]), // ${method}`);
});
console.log('};');

console.log('\n✅ METEORA DLMM DISCRIMINATORS ГОТОВЫ ДЛЯ ИСПОЛЬЗОВАНИЯ!');
