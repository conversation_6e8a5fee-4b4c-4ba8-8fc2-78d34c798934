/**
 * 🔍 ПРОВЕРКА ИСТОРИИ MARGINFI АККАУНТА
 * Ищем как был создан первый lending balance
 */

const { Connection, PublicKey } = require('@solana/web3.js');

async function checkAccountHistory() {
    const connection = new Connection('https://api.mainnet-beta.solana.com');
    const account = new PublicKey('3dPECtRBPcYtcJG6wWDCr7VCyzxttDTjgGgo9gWC9vmU');
    
    console.log('🔍 ПРОВЕРКА ИСТОРИИ MARGINFI АККАУНТА...');
    console.log('📋 Аккаунт:', account.toString());
    
    try {
        const signatures = await connection.getSignaturesForAddress(account, { limit: 100 });
        console.log('📊 Найдено транзакций:', signatures.length);
        
        // Ищем самые старые транзакции (в конце массива)
        const oldestTransactions = signatures.slice(-20).reverse();
        
        console.log('\n🔍 АНАЛИЗ САМЫХ СТАРЫХ ТРАНЗАКЦИЙ:');
        
        for (let i = 0; i < oldestTransactions.length; i++) {
            const sig = oldestTransactions[i];
            console.log(`\n📋 Транзакция ${i + 1} (старая -> новая):`);
            console.log(`   Signature: ${sig.signature}`);
            console.log(`   Slot: ${sig.slot}`);
            console.log(`   Status: ${sig.err ? 'FAILED' : 'SUCCESS'}`);
            
            if (sig.err) {
                console.log(`   Error: ${JSON.stringify(sig.err)}`);
                continue;
            }
            
            // Получаем детали успешных транзакций
            try {
                const tx = await connection.getTransaction(sig.signature, { 
                    maxSupportedTransactionVersion: 0 
                });
                
                if (tx && tx.meta && tx.meta.logMessages) {
                    const logs = tx.meta.logMessages;
                    
                    // Ищем MarginFi логи
                    const marginfiLogs = logs.filter(log => 
                        log.includes('marginfi') || 
                        log.includes('deposit') || 
                        log.includes('lending') ||
                        log.includes('MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA')
                    );
                    
                    if (marginfiLogs.length > 0) {
                        console.log('   📋 MarginFi логи:');
                        marginfiLogs.forEach(log => console.log(`     ${log}`));
                    }
                    
                    // Ищем инструкции
                    if (tx.transaction && tx.transaction.message && tx.transaction.message.instructions) {
                        console.log(`   📊 Инструкций: ${tx.transaction.message.instructions.length}`);
                    }
                    
                    // Проверяем изменения балансов
                    if (tx.meta.preBalances && tx.meta.postBalances) {
                        const balanceChanges = tx.meta.postBalances.map((post, idx) => 
                            post - tx.meta.preBalances[idx]
                        ).filter(change => change !== 0);
                        
                        if (balanceChanges.length > 0) {
                            console.log('   💰 Изменения балансов:', balanceChanges);
                        }
                    }
                }
            } catch (e) {
                console.log('   ❌ Не удалось получить детали транзакции:', e.message);
            }
        }
        
    } catch (error) {
        console.error('❌ Ошибка:', error.message);
    }
}

checkAccountHistory().catch(console.error);
