/**
 * 🔍 ПРОВЕРКА ВСЕХ ИНСТРУКЦИЙ НА ЛИШНИЕ ДАННЫЕ
 */

const { Connection, PublicKey } = require('@solana/web3.js');
const CompleteFlashLoanStructure = require('./complete-flash-loan-structure');
require('dotenv').config({ path: '.env.solana' });

async function checkAllInstructionData() {
    console.log('🔍 ПРОВЕРКА ВСЕХ ИНСТРУКЦИЙ НА ЛИШНИЕ ДАННЫЕ');
    console.log('=' .repeat(80));

    try {
        // 1. Создаем структуру
        const connection = new Connection(process.env.QUICKNODE_RPC_URL);
        const walletData = require('./wallet.json');
        const wallet = require('@solana/web3.js').Keypair.fromSecretKey(new Uint8Array(walletData));
        const marginfiAccountAddress = '********************************************';
        
        const flashLoanStructure = new CompleteFlashLoanStructure(wallet, marginfiAccountAddress, connection);
        
        // 2. Создаем транзакцию
        console.log('🔧 Создание транзакции для анализа...');
        const result = await flashLoanStructure.createCompleteFlashLoanTransactionWithALT();
        
        if (!result || !result.instructions) {
            throw new Error('Не удалось создать транзакцию');
        }

        console.log(`📊 Анализируем ${result.instructions.length} инструкций...\n`);

        // 3. Анализируем каждую инструкцию
        const suspiciousInstructions = [];
        
        result.instructions.forEach((instruction, index) => {
            const programName = getProgramName(instruction.programId);
            const keysCount = instruction.keys ? instruction.keys.length : 0;
            const dataSize = instruction.data ? instruction.data.length : 0;
            
            console.log(`📋 ИНСТРУКЦИЯ ${index.toString().padStart(2, '0')}: ${programName}`);
            console.log(`   Ключей: ${keysCount}`);
            console.log(`   Данных: ${dataSize} bytes`);
            
            if (dataSize > 0) {
                const dataHex = instruction.data.toString('hex');
                console.log(`   Hex: ${dataHex.slice(0, 32)}${dataHex.length > 32 ? '...' : ''}`);
                
                // Анализируем подозрительные паттерны
                const analysis = analyzeInstructionData(instruction.data, programName, index);
                
                if (analysis.suspicious) {
                    suspiciousInstructions.push({
                        index,
                        programName,
                        dataSize,
                        issues: analysis.issues,
                        potentialSavings: analysis.potentialSavings
                    });
                    console.log(`   🚨 ПОДОЗРИТЕЛЬНО: ${analysis.issues.join(', ')}`);
                    console.log(`   💰 Потенциальная экономия: ${analysis.potentialSavings} bytes`);
                }
                
                console.log(`   ✅ Анализ: ${analysis.summary}`);
            } else {
                console.log(`   ✅ Нет данных`);
            }
            
            console.log('');
        });

        // 4. Суммарный анализ
        console.log('=' .repeat(80));
        console.log('📊 СУММАРНЫЙ АНАЛИЗ ПОДОЗРИТЕЛЬНЫХ ИНСТРУКЦИЙ:');
        console.log('=' .repeat(80));

        if (suspiciousInstructions.length === 0) {
            console.log('✅ НЕ НАЙДЕНО ПОДОЗРИТЕЛЬНЫХ ИНСТРУКЦИЙ!');
            console.log('   Все инструкции имеют оптимальный размер данных.');
        } else {
            let totalPotentialSavings = 0;
            
            suspiciousInstructions.forEach((inst, rank) => {
                console.log(`${(rank + 1).toString().padStart(1)}: Инструкция ${inst.index} (${inst.programName})`);
                console.log(`   Размер данных: ${inst.dataSize} bytes`);
                console.log(`   Проблемы: ${inst.issues.join(', ')}`);
                console.log(`   Экономия: ${inst.potentialSavings} bytes`);
                console.log('');
                
                totalPotentialSavings += inst.potentialSavings;
            });
            
            console.log(`🔥 ОБЩАЯ ПОТЕНЦИАЛЬНАЯ ЭКОНОМИЯ: ${totalPotentialSavings} bytes`);
            console.log(`📊 Подозрительных инструкций: ${suspiciousInstructions.length} из ${result.instructions.length}`);
        }

        // 5. Рекомендации по оптимизации
        console.log('\n💡 РЕКОМЕНДАЦИИ ПО ОПТИМИЗАЦИИ:');
        console.log('=' .repeat(80));

        const recommendations = generateOptimizationRecommendations(suspiciousInstructions);
        recommendations.forEach((rec, index) => {
            console.log(`${index + 1}. ${rec}`);
        });

        return {
            totalInstructions: result.instructions.length,
            suspiciousInstructions,
            totalPotentialSavings: suspiciousInstructions.reduce((sum, inst) => sum + inst.potentialSavings, 0),
            recommendations
        };

    } catch (error) {
        console.error('❌ Ошибка анализа инструкций:', error.message);
        console.error(error.stack);
        return null;
    }
}

/**
 * 🔍 АНАЛИЗ ДАННЫХ ИНСТРУКЦИИ НА ПОДОЗРИТЕЛЬНЫЕ ПАТТЕРНЫ
 */
function analyzeInstructionData(data, programName, index) {
    const issues = [];
    let potentialSavings = 0;
    let suspicious = false;

    // 1. Проверяем на избыточные нули в конце
    const trailingZeros = countTrailingZeros(data);
    if (trailingZeros > 8) {
        issues.push(`${trailingZeros} нулей в конце`);
        potentialSavings += Math.max(0, trailingZeros - 4); // Оставляем 4 байта padding
        suspicious = true;
    }

    // 2. Проверяем на повторяющиеся паттерны
    const repeatingPattern = findRepeatingPattern(data);
    if (repeatingPattern.length > 0) {
        issues.push(`Повторяющийся паттерн: ${repeatingPattern.pattern}`);
        potentialSavings += repeatingPattern.wastedBytes;
        suspicious = true;
    }

    // 3. Проверяем размер относительно типичного для программы
    const expectedSize = getExpectedDataSize(programName);
    if (data.length > expectedSize * 2) {
        issues.push(`Размер ${data.length} > ожидаемого ${expectedSize}`);
        potentialSavings += data.length - expectedSize;
        suspicious = true;
    }

    // 4. Проверяем на избыточные структуры
    if (programName.includes('Meteora') || programName.includes('LBUZKhRx')) {
        const meteoraAnalysis = analyzeMeteorData(data);
        if (meteoraAnalysis.suspicious) {
            issues.push(...meteoraAnalysis.issues);
            potentialSavings += meteoraAnalysis.savings;
            suspicious = true;
        }
    }

    return {
        suspicious,
        issues,
        potentialSavings,
        summary: suspicious ? `${issues.length} проблем, экономия ${potentialSavings} bytes` : 'Оптимально'
    };
}

/**
 * 🔢 ПОДСЧЕТ НУЛЕЙ В КОНЦЕ
 */
function countTrailingZeros(data) {
    let count = 0;
    for (let i = data.length - 1; i >= 0; i--) {
        if (data[i] === 0) {
            count++;
        } else {
            break;
        }
    }
    return count;
}

/**
 * 🔄 ПОИСК ПОВТОРЯЮЩИХСЯ ПАТТЕРНОВ
 */
function findRepeatingPattern(data) {
    // Ищем паттерны длиной 4, 8, 16 байт
    for (const patternLength of [4, 8, 16]) {
        if (data.length < patternLength * 2) continue;
        
        const pattern = data.slice(0, patternLength);
        let repeats = 1;
        
        for (let i = patternLength; i < data.length; i += patternLength) {
            const chunk = data.slice(i, i + patternLength);
            if (chunk.equals(pattern)) {
                repeats++;
            } else {
                break;
            }
        }
        
        if (repeats > 2) {
            return {
                pattern: pattern.toString('hex').slice(0, 16) + '...',
                length: patternLength,
                repeats,
                wastedBytes: (repeats - 1) * patternLength
            };
        }
    }
    
    return { length: 0 };
}

/**
 * 📏 ОЖИДАЕМЫЙ РАЗМЕР ДАННЫХ ДЛЯ ПРОГРАММЫ
 */
function getExpectedDataSize(programName) {
    const expectedSizes = {
        'ComputeBudget': 10,
        'MarginFi': 20,
        'SPL Token': 5,
        'Associated Token': 1,
        'System Program': 15,
        'Meteora': 40,
        'LBUZKhRx': 40
    };
    
    for (const [key, size] of Object.entries(expectedSizes)) {
        if (programName.includes(key)) {
            return size;
        }
    }
    
    return 32; // По умолчанию
}

/**
 * 🌟 АНАЛИЗ METEORA ДАННЫХ
 */
function analyzeMeteorData(data) {
    const issues = [];
    let savings = 0;
    let suspicious = false;

    // Проверяем на bin arrays (обычно в конце)
    if (data.length > 50) {
        const lastHalf = data.slice(Math.floor(data.length / 2));
        const zeros = countTrailingZeros(lastHalf);
        
        if (zeros > 20) {
            issues.push(`Возможно лишние bin arrays (${zeros} нулей)`);
            savings += zeros;
            suspicious = true;
        }
    }

    // Проверяем на дублированные discriminator
    if (data.length > 16) {
        const firstDiscriminator = data.slice(0, 8);
        const secondDiscriminator = data.slice(8, 16);
        
        if (firstDiscriminator.equals(secondDiscriminator)) {
            issues.push('Дублированный discriminator');
            savings += 8;
            suspicious = true;
        }
    }

    return { suspicious, issues, savings };
}

/**
 * 💡 ГЕНЕРАЦИЯ РЕКОМЕНДАЦИЙ ПО ОПТИМИЗАЦИИ
 */
function generateOptimizationRecommendations(suspiciousInstructions) {
    const recommendations = [];
    
    if (suspiciousInstructions.length === 0) {
        recommendations.push('Все инструкции уже оптимизированы!');
        return recommendations;
    }

    // Группируем по типам проблем
    const trailingZeroInstructions = suspiciousInstructions.filter(inst => 
        inst.issues.some(issue => issue.includes('нулей в конце'))
    );
    
    const oversizedInstructions = suspiciousInstructions.filter(inst => 
        inst.issues.some(issue => issue.includes('ожидаемого'))
    );
    
    const meteoraInstructions = suspiciousInstructions.filter(inst => 
        inst.programName.includes('Meteora') || inst.programName.includes('LBUZKhRx')
    );

    if (trailingZeroInstructions.length > 0) {
        recommendations.push(`Убрать лишние нули в конце у ${trailingZeroInstructions.length} инструкций`);
    }
    
    if (oversizedInstructions.length > 0) {
        recommendations.push(`Оптимизировать размер данных у ${oversizedInstructions.length} инструкций`);
    }
    
    if (meteoraInstructions.length > 0) {
        recommendations.push(`Минимизировать Meteora структуры у ${meteoraInstructions.length} инструкций`);
    }

    const totalSavings = suspiciousInstructions.reduce((sum, inst) => sum + inst.potentialSavings, 0);
    recommendations.push(`Общая экономия: ${totalSavings} bytes`);

    return recommendations;
}

/**
 * 🔧 ПОЛУЧЕНИЕ ИМЕНИ ПРОГРАММЫ ПО ID
 */
function getProgramName(programId) {
    const programIdStr = programId.toString();
    
    const knownPrograms = {
        'ComputeBudget111111111111111111111111111111': 'ComputeBudget',
        'MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA': 'MarginFi',
        'LBUZKhRxUjRKandJyNdnQEHPFdNRNisFbMzp2GxqF46': 'Meteora DLMM',
        'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA': 'SPL Token',
        'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL': 'Associated Token',
        '11111111111111111111111111111111': 'System Program'
    };
    
    return knownPrograms[programIdStr] || `Unknown (${programIdStr.slice(0,8)}...)`;
}

// Запуск анализа
if (require.main === module) {
    checkAllInstructionData()
        .then(result => {
            if (result) {
                console.log('\n✅ Анализ завершен успешно');
                console.log(`📊 Потенциальная экономия: ${result.totalPotentialSavings} bytes`);
            } else {
                console.log('❌ Анализ провален');
            }
        })
        .catch(error => {
            console.error('❌ Критическая ошибка:', error);
            process.exit(1);
        });
}

module.exports = { checkAllInstructionData };
