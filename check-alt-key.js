const { Connection, PublicKey } = require('@solana/web3.js');

/**
 * 🔍 ПРОВЕРЯЕМ ЕСТЬ ЛИ КЛЮЧ В ALT ТАБЛИЦЕ
 */

const RPC_URL = 'https://api.mainnet-beta.solana.com';
const connection = new Connection(RPC_URL, 'confirmed');

const customALTAddress = new PublicKey('FAeyUf4AdG7vTQ2Q89r9hWBkPdYkykJKG4CxgqArBGXe');
const missingKey = '3rkLayH2xo2Ru9fKVxaohe61tJ4FLnMTC3LyheiaakGH';

async function checkKeyInALT() {
    try {
        console.log('🔍 ПРОВЕРКА КЛЮЧА В ALT ТАБЛИЦЕ...');
        console.log(`🔑 Ключ: ${missingKey}`);
        console.log(`📋 ALT: ${customALTAddress.toString()}`);
        
        const altAccount = await connection.getAddressLookupTable(customALTAddress);
        
        if (!altAccount || !altAccount.value) {
            console.log('❌ ALT таблица не найдена!');
            return;
        }
        
        const addresses = altAccount.value.state.addresses.map(addr => addr.toString());
        console.log(`📊 Всего адресов в ALT: ${addresses.length}`);
        
        const keyExists = addresses.includes(missingKey);
        
        if (keyExists) {
            console.log('✅ КЛЮЧ УЖЕ ЕСТЬ В ALT ТАБЛИЦЕ!');
            const index = addresses.indexOf(missingKey);
            console.log(`📍 Индекс в таблице: ${index}`);
        } else {
            console.log('❌ КЛЮЧ НЕ НАЙДЕН В ALT ТАБЛИЦЕ!');
            console.log('💡 Нужно добавить этот ключ');
        }
        
        // Показываем последние 10 ключей для контекста
        console.log('\n📋 ПОСЛЕДНИЕ 10 КЛЮЧЕЙ В ALT:');
        const lastKeys = addresses.slice(-10);
        lastKeys.forEach((key, index) => {
            const globalIndex = addresses.length - 10 + index;
            console.log(`   ${globalIndex}: ${key}`);
        });
        
    } catch (error) {
        console.error('❌ ОШИБКА:', error.message);
    }
}

checkKeyInALT();
