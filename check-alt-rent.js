const { Connection, PublicKey, AddressLookupTableProgram } = require('@solana/web3.js');
const fs = require('fs');

/**
 * 🔍 ПРОВЕРЯЕМ СКОЛЬКО НУЖНО RENT ДЛЯ РАСШИРЕНИЯ ALT
 */

const RPC_URL = 'https://api.mainnet-beta.solana.com';
const connection = new Connection(RPC_URL, 'confirmed');

const customALTAddress = new PublicKey('FAeyUf4AdG7vTQ2Q89r9hWBkPdYkykJKG4CxgqArBGXe');

// Загружаем кошелек
const walletData = JSON.parse(fs.readFileSync('wallet.json', 'utf8'));
const { Keypair } = require('@solana/web3.js');
const wallet = Keypair.fromSecretKey(new Uint8Array(walletData));

async function checkALTRent() {
    try {
        console.log('🔍 ПРОВЕРКА RENT ДЛЯ РАСШИРЕНИЯ ALT...');
        
        // Проверяем баланс кошелька
        const balance = await connection.getBalance(wallet.publicKey);
        const solBalance = balance / 1e9;
        console.log(`💰 Баланс кошелька: ${solBalance.toFixed(6)} SOL`);
        
        // Получаем информацию о ALT
        const altAccount = await connection.getAddressLookupTable(customALTAddress);
        if (!altAccount || !altAccount.value) {
            console.log('❌ ALT таблица не найдена!');
            return;
        }
        
        console.log(`📊 Текущих адресов в ALT: ${altAccount.value.state.addresses.length}`);
        
        // Проверяем rent для ALT аккаунта
        const altAccountInfo = await connection.getAccountInfo(customALTAddress);
        if (!altAccountInfo) {
            console.log('❌ Информация об ALT аккаунте не найдена!');
            return;
        }
        
        console.log(`💾 Размер ALT аккаунта: ${altAccountInfo.data.length} байт`);
        console.log(`💰 Lamports в ALT аккаунте: ${altAccountInfo.lamports}`);
        
        // Рассчитываем rent для текущего размера
        const currentRent = await connection.getMinimumBalanceForRentExemption(altAccountInfo.data.length);
        console.log(`🏠 Текущий rent: ${currentRent / 1e9} SOL`);
        
        // Рассчитываем rent для расширенного размера (добавляем 1 адрес = 32 байта)
        const newSize = altAccountInfo.data.length + 32;
        const newRent = await connection.getMinimumBalanceForRentExemption(newSize);
        console.log(`🏠 Новый rent (после добавления): ${newRent / 1e9} SOL`);
        
        const rentDifference = newRent - currentRent;
        console.log(`💸 Дополнительный rent: ${rentDifference / 1e9} SOL`);
        
        // Проверяем хватает ли баланса
        const totalNeeded = rentDifference + 5000; // + комиссия транзакции
        console.log(`🎯 Всего нужно: ${totalNeeded / 1e9} SOL`);
        
        if (balance >= totalNeeded) {
            console.log('✅ БАЛАНСА ХВАТАЕТ ДЛЯ РАСШИРЕНИЯ ALT!');
        } else {
            console.log('❌ НЕ ХВАТАЕТ БАЛАНСА ДЛЯ РАСШИРЕНИЯ ALT!');
            console.log(`💡 Нужно пополнить: ${(totalNeeded - balance) / 1e9} SOL`);
        }
        
        // Проверяем есть ли уже достаточно rent в ALT аккаунте
        if (altAccountInfo.lamports >= newRent) {
            console.log('✅ В ALT аккаунте достаточно rent для расширения!');
        } else {
            console.log('❌ В ALT аккаунте недостаточно rent для расширения!');
            console.log(`💡 Нужно добавить в ALT: ${(newRent - altAccountInfo.lamports) / 1e9} SOL`);
        }
        
    } catch (error) {
        console.error('❌ ОШИБКА:', error.message);
    }
}

checkALTRent();
