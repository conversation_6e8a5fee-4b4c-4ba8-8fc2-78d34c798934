/**
 * 🔍 ПРОВЕРКА ALT ТАБЛИЦ
 */

const { Connection, PublicKey } = require('@solana/web3.js');

async function checkALTTables() {
  console.log('🔍 ПРОВЕРКА ALT ТАБЛИЦ');

  const connection = new Connection('https://api.mainnet-beta.solana.com', 'confirmed');
  
  const altAddresses = [
    '2immgwYNHBbyVQKVGCEkgWpi53bLwWNRMB5G2nbgYV17', // Jupiter
    'D8cy77BBepLMngZx6ZukaTff5hCt1HrWyKk3Hnd9oitf', // Jupiter 2
    'HGmknUTUmeovMc9ryERNWG6UFZDFDVr9xrum3ZhyL4fC', // MarginFi 1
    '5FuKF7C1tJji2mXZuJ14U9oDb37is5mmvYLf4KwojoF1', // MarginFi 2
    'FEFhAFKz48P3w82Ds5VhvyEDwhRqu2FejmnuxEPZ8wNR', // MarginFi 3
    'FAeyUf4AdG7vTQ2Q89r9hWBkPdYkykJKG4CxgqArBGXe'  // ТВОЯ КАСТОМНАЯ ALT
  ];

  console.log(`📊 Проверяем ${altAddresses.length} ALT таблиц:`);

  for (let i = 0; i < altAddresses.length; i++) {
    const address = altAddresses[i];
    console.log(`\n${i + 1}. ${address}`);
    
    try {
      // Проверяем аккаунт
      const accountInfo = await connection.getAccountInfo(new PublicKey(address));
      
      if (!accountInfo) {
        console.log(`   ❌ Аккаунт не найден`);
        continue;
      }
      
      console.log(`   ✅ Аккаунт найден`);
      console.log(`   👤 Владелец: ${accountInfo.owner.toString()}`);
      console.log(`   💰 Lamports: ${accountInfo.lamports}`);
      console.log(`   📊 Размер данных: ${accountInfo.data.length} байт`);
      
      // Проверяем правильность владельца ALT
      const expectedOwner = 'AddressLookupTab1e1111111111111111111111111';
      const isCorrectOwner = accountInfo.owner.toString() === expectedOwner;
      console.log(`   🔍 Правильный владелец: ${isCorrectOwner ? '✅' : '❌'}`);
      
      if (!isCorrectOwner) {
        console.log(`   ⚠️ Ожидался: ${expectedOwner}`);
        console.log(`   ⚠️ Получен: ${accountInfo.owner.toString()}`);
      }
      
      // Пытаемся загрузить как ALT
      try {
        const altAccount = await connection.getAddressLookupTable(new PublicKey(address));
        if (altAccount.value) {
          console.log(`   ✅ ALT загружена: ${altAccount.value.addresses.length} адресов`);
          
          // Показываем первые несколько адресов
          const firstAddresses = altAccount.value.addresses.slice(0, 3);
          firstAddresses.forEach((addr, idx) => {
            console.log(`      ${idx + 1}. ${addr.toString()}`);
          });
          if (altAccount.value.addresses.length > 3) {
            console.log(`      ... и еще ${altAccount.value.addresses.length - 3} адресов`);
          }
        } else {
          console.log(`   ❌ ALT не загружена (null)`);
        }
      } catch (altError) {
        console.log(`   ❌ Ошибка загрузки ALT: ${altError.message}`);
      }
      
    } catch (error) {
      console.log(`   ❌ Ошибка: ${error.message}`);
    }
  }

  // Проверяем конкретно твою кастомную ALT
  console.log(`\n🔥 СПЕЦИАЛЬНАЯ ПРОВЕРКА ТВОЕЙ КАСТОМНОЙ ALT:`);
  const customALT = 'FAeyUf4AdG7vTQ2Q89r9hWBkPdYkykJKG4CxgqArBGXe';
  
  try {
    const accountInfo = await connection.getAccountInfo(new PublicKey(customALT));
    
    if (accountInfo) {
      console.log(`✅ Твоя кастомная ALT существует!`);
      console.log(`   👤 Владелец: ${accountInfo.owner.toString()}`);
      console.log(`   💰 Lamports: ${accountInfo.lamports}`);
      console.log(`   📊 Размер: ${accountInfo.data.length} байт`);
      
      const isALTOwner = accountInfo.owner.toString() === 'AddressLookupTab1e1111111111111111111111111';
      console.log(`   🔍 Это ALT таблица: ${isALTOwner ? '✅ ДА' : '❌ НЕТ'}`);
      
      if (isALTOwner) {
        const altAccount = await connection.getAddressLookupTable(new PublicKey(customALT));
        if (altAccount.value) {
          console.log(`   📋 Адресов в таблице: ${altAccount.value.addresses.length}`);
          console.log(`   🎯 ТВОЯ КАСТОМНАЯ ALT РАБОТАЕТ!`);
        }
      } else {
        console.log(`   ⚠️ Это не ALT таблица, а обычный аккаунт`);
        console.log(`   💡 Возможно, нужно создать ALT таблицу по этому адресу`);
      }
    } else {
      console.log(`❌ Твоя кастомная ALT не найдена!`);
      console.log(`💡 Нужно создать ALT таблицу по адресу: ${customALT}`);
    }
  } catch (error) {
    console.log(`❌ Ошибка проверки кастомной ALT: ${error.message}`);
  }

  console.log(`\n🎯 ИТОГ ПРОВЕРКИ ALT ТАБЛИЦ:`);
  console.log(`   📊 Всего проверено: ${altAddresses.length} адресов`);
  console.log(`   🔍 Результаты смотри выше`);
  console.log(`   💡 Если ALT не загружается - проверь владельца`);
}

if (require.main === module) {
  checkALTTables().catch(console.error);
}

module.exports = { checkALTTables };
