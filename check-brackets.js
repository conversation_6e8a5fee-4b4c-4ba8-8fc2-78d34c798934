/**
 * 🔍 ПРОВЕРКА БАЛАНСА СКОБОК
 */

const fs = require('fs');

function checkBrackets() {
  const content = fs.readFileSync('./src/atomic-transaction-builder-fixed.js', 'utf8');
  const lines = content.split('\n');
  
  let openBraces = 0;
  let openParens = 0;
  let openBrackets = 0;
  
  console.log('🔍 ПРОВЕРКА БАЛАНСА СКОБОК:');
  console.log('');
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const lineNum = i + 1;
    
    // Подсчитываем скобки
    for (const char of line) {
      switch (char) {
        case '{': openBraces++; break;
        case '}': openBraces--; break;
        case '(': openParens++; break;
        case ')': openParens--; break;
        case '[': openBrackets++; break;
        case ']': openBrackets--; break;
      }
    }
    
    // Проверяем критические точки
    if (lineNum === 612) {
      console.log(`📍 Строка ${lineNum} (начало createFlashLoanTransaction):`);
      console.log(`   Фигурные скобки: ${openBraces}`);
      console.log(`   Круглые скобки: ${openParens}`);
      console.log(`   Квадратные скобки: ${openBrackets}`);
    }
    
    if (lineNum === 1461) {
      console.log(`📍 Строка ${lineNum} (конец createFlashLoanTransaction):`);
      console.log(`   Фигурные скобки: ${openBraces}`);
      console.log(`   Круглые скобки: ${openParens}`);
      console.log(`   Квадратные скобки: ${openBrackets}`);
    }
    
    if (lineNum === 1466) {
      console.log(`📍 Строка ${lineNum} (setAltManager - проблемная):`);
      console.log(`   Фигурные скобки: ${openBraces}`);
      console.log(`   Круглые скобки: ${openParens}`);
      console.log(`   Квадратные скобки: ${openBrackets}`);
      console.log(`   Содержимое: "${line.trim()}"`);
    }
    
    // Проверяем отрицательные значения
    if (openBraces < 0 || openParens < 0 || openBrackets < 0) {
      console.log(`❌ ОШИБКА на строке ${lineNum}: Лишняя закрывающая скобка!`);
      console.log(`   Строка: "${line.trim()}"`);
      console.log(`   Фигурные: ${openBraces}, Круглые: ${openParens}, Квадратные: ${openBrackets}`);
      break;
    }
  }
  
  console.log('');
  console.log('🎯 ФИНАЛЬНЫЙ БАЛАНС:');
  console.log(`   Фигурные скобки: ${openBraces} (должно быть 0)`);
  console.log(`   Круглые скобки: ${openParens} (должно быть 0)`);
  console.log(`   Квадратные скобки: ${openBrackets} (должно быть 0)`);
  
  if (openBraces === 0 && openParens === 0 && openBrackets === 0) {
    console.log('✅ Баланс скобок корректен!');
  } else {
    console.log('❌ Баланс скобок нарушен!');
  }
}

checkBrackets();
