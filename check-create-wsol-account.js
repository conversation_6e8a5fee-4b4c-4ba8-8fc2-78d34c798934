/**
 * 🔍 ПРОВЕРКА И СОЗДАНИЕ WSOL АККАУНТА
 * 
 * Проверяет существование WSOL аккаунта пользователя и создает его при необходимости
 */

const { Connection, PublicKey, Transaction, Keypair } = require('@solana/web3.js');
const { 
  getAssociatedTokenAddress, 
  createAssociatedTokenAccountInstruction,
  TOKEN_PROGRAM_ID 
} = require('@solana/spl-token');
const fs = require('fs');

class WSolAccountChecker {
  constructor() {
    // Используем QuickNode RPC как предпочитает пользователь
    this.connection = new Connection('https://billowing-empty-patron.solana-mainnet.quiknode.pro/c79212a7508ec6a37353793d9be93c917898e858/');
    
    // WSOL mint address
    this.WSOL_MINT = new PublicKey('So11111111111111111111111111111111111111112');
    
    // Загружаем кошелек пользователя
    this.loadWallet();
  }

  /**
   * 🔑 ЗАГРУЗКА КОШЕЛЬКА ПОЛЬЗОВАТЕЛЯ
   */
  loadWallet() {
    try {
      // Пытаемся найти файл кошелька
      const walletPaths = [
        './wallet.json',
        './keypair.json', 
        './id.json',
        './solana-keypair.json'
      ];

      let walletData = null;
      let usedPath = null;

      for (const path of walletPaths) {
        if (fs.existsSync(path)) {
          console.log(`🔑 Найден кошелек: ${path}`);
          walletData = JSON.parse(fs.readFileSync(path, 'utf8'));
          usedPath = path;
          break;
        }
      }

      if (!walletData) {
        throw new Error('Файл кошелька не найден. Ожидаемые файлы: wallet.json, keypair.json, id.json, solana-keypair.json');
      }

      // Создаем Keypair из данных
      this.wallet = Keypair.fromSecretKey(new Uint8Array(walletData));
      console.log(`✅ Кошелек загружен из: ${usedPath}`);
      console.log(`📍 Публичный ключ: ${this.wallet.publicKey.toString()}`);

    } catch (error) {
      console.error(`❌ Ошибка загрузки кошелька: ${error.message}`);
      throw error;
    }
  }

  /**
   * 🔍 ПРОВЕРКА СУЩЕСТВОВАНИЯ WSOL АККАУНТА
   */
  async checkWSolAccount() {
    try {
      console.log('\n🔍 ПРОВЕРКА WSOL АККАУНТА');
      console.log('═══════════════════════════════════════════════════════════════');

      // Вычисляем Associated Token Address для WSOL
      const wsolATA = await getAssociatedTokenAddress(
        this.WSOL_MINT,
        this.wallet.publicKey
      );

      console.log(`📊 WSOL mint: ${this.WSOL_MINT.toString()}`);
      console.log(`📊 Ваш кошелек: ${this.wallet.publicKey.toString()}`);
      console.log(`📊 WSOL ATA: ${wsolATA.toString()}`);

      // Проверяем существование аккаунта
      const accountInfo = await this.connection.getAccountInfo(wsolATA);

      if (accountInfo) {
        console.log(`✅ WSOL аккаунт существует!`);
        console.log(`📊 Размер аккаунта: ${accountInfo.data.length} байт`);
        console.log(`💰 Lamports в аккаунте: ${accountInfo.lamports}`);
        
        // Для WSOL аккаунта lamports = wrapped SOL balance
        const wsolBalance = accountInfo.lamports / 1e9;
        console.log(`💰 WSOL баланс: ${wsolBalance.toFixed(9)} SOL`);

        return {
          exists: true,
          address: wsolATA.toString(),
          lamports: accountInfo.lamports,
          wsolBalance: wsolBalance
        };
      } else {
        console.log(`❌ WSOL аккаунт НЕ существует!`);
        console.log(`🔧 Нужно создать ATA: ${wsolATA.toString()}`);

        return {
          exists: false,
          address: wsolATA.toString(),
          needsCreation: true
        };
      }

    } catch (error) {
      console.error(`❌ Ошибка проверки WSOL аккаунта: ${error.message}`);
      throw error;
    }
  }

  /**
   * 🚀 СОЗДАНИЕ WSOL АККАУНТА
   */
  async createWSolAccount() {
    try {
      console.log('\n🚀 СОЗДАНИЕ WSOL АККАУНТА');
      console.log('═══════════════════════════════════════════════════════════════');

      // Вычисляем Associated Token Address для WSOL
      const wsolATA = await getAssociatedTokenAddress(
        this.WSOL_MINT,
        this.wallet.publicKey
      );

      console.log(`📍 Создаем WSOL ATA: ${wsolATA.toString()}`);

      // Проверяем баланс SOL для оплаты комиссии
      const solBalance = await this.connection.getBalance(this.wallet.publicKey);
      console.log(`💰 Ваш SOL баланс: ${solBalance / 1e9} SOL`);

      if (solBalance < 5000000) { // 0.005 SOL минимум для комиссии
        throw new Error(`Недостаточно SOL для создания аккаунта. Нужно минимум 0.005 SOL, у вас: ${solBalance / 1e9} SOL`);
      }

      // Создаем инструкцию для создания Associated Token Account
      const createATAInstruction = createAssociatedTokenAccountInstruction(
        this.wallet.publicKey, // payer (кто платит комиссию)
        wsolATA,               // ata (адрес создаваемого аккаунта)
        this.wallet.publicKey, // owner (владелец аккаунта)
        this.WSOL_MINT,        // mint (WSOL mint)
        TOKEN_PROGRAM_ID       // token program
      );

      // Создаем транзакцию
      const transaction = new Transaction().add(createATAInstruction);

      // Получаем последний blockhash
      const { blockhash } = await this.connection.getLatestBlockhash();
      transaction.recentBlockhash = blockhash;
      transaction.feePayer = this.wallet.publicKey;

      console.log(`📋 Транзакция подготовлена`);
      console.log(`📊 Инструкций: ${transaction.instructions.length}`);

      // Подписываем транзакцию
      transaction.sign(this.wallet);

      console.log(`✍️ Транзакция подписана`);

      // Отправляем транзакцию
      console.log(`🚀 Отправляем транзакцию...`);
      
      const signature = await this.connection.sendRawTransaction(
        transaction.serialize(),
        {
          skipPreflight: false,
          preflightCommitment: 'processed',
          maxRetries: 3
        }
      );

      console.log(`✅ Транзакция отправлена: ${signature}`);

      // Ждем подтверждения
      console.log('⏳ Ждем подтверждения...');
      const confirmation = await this.connection.confirmTransaction(signature, 'confirmed');

      if (confirmation.value.err) {
        throw new Error(`Транзакция не подтверждена: ${JSON.stringify(confirmation.value.err)}`);
      }

      console.log(`✅ WSOL аккаунт создан успешно!`);

      // Проверяем результат
      const newAccountInfo = await this.connection.getAccountInfo(wsolATA);
      if (newAccountInfo) {
        console.log(`📊 Размер аккаунта: ${newAccountInfo.data.length} байт`);
        console.log(`💰 Lamports в аккаунте: ${newAccountInfo.lamports}`);
        console.log(`💰 WSOL баланс: ${newAccountInfo.lamports / 1e9} SOL`);
      }

      return {
        success: true,
        signature,
        address: wsolATA.toString(),
        lamports: newAccountInfo?.lamports || 0
      };

    } catch (error) {
      console.error(`❌ Ошибка создания WSOL аккаунта: ${error.message}`);
      throw error;
    }
  }

  /**
   * 🔄 ОСНОВНАЯ ФУНКЦИЯ - ПРОВЕРКА И СОЗДАНИЕ
   */
  async checkAndCreateWSol() {
    try {
      console.log('🪙 ПРОВЕРКА И СОЗДАНИЕ WSOL АККАУНТА');
      console.log('═'.repeat(60));

      // 1. Проверяем существование
      const checkResult = await this.checkWSolAccount();

      if (checkResult.exists) {
        console.log('\n✅ WSOL аккаунт уже существует - создание не требуется!');
        return checkResult;
      }

      // 2. Создаем аккаунт
      console.log('\n🔧 WSOL аккаунт не найден - создаем...');
      const createResult = await this.createWSolAccount();

      return createResult;

    } catch (error) {
      console.error(`❌ Критическая ошибка: ${error.message}`);
      throw error;
    }
  }
}

// Запускаем проверку, если файл выполняется напрямую
async function main() {
  try {
    const checker = new WSolAccountChecker();
    const result = await checker.checkAndCreateWSol();
    
    console.log('\n🏁 РЕЗУЛЬТАТ:');
    console.log(JSON.stringify(result, null, 2));
    
    process.exit(0);
  } catch (error) {
    console.error(`💥 Фатальная ошибка: ${error.message}`);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = WSolAccountChecker;
