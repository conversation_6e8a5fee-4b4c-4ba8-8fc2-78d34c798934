/**
 * 🔥 ПРОВЕРКА КАСТОМНОЙ ALT ТАБЛИЦЫ VS 26 НЕПОКРЫТЫХ КЛЮЧЕЙ
 * 
 * Сверяем что есть в ALT и что нужно добавить
 */

const { Connection, PublicKey } = require('@solana/web3.js');
const fs = require('fs');
require('dotenv').config({ path: '.env.solana' });

console.log('🔥 ПРОВЕРКА КАСТОМНОЙ ALT ТАБЛИЦЫ VS 26 НЕПОКРЫТЫХ КЛЮЧЕЙ');
console.log('=' .repeat(80));

async function checkCustomALTvsUncoveredKeys() {
    try {
        // 1. Подключение к RPC
        const connection = new Connection(process.env.QUICKNODE_RPC_URL);
        console.log('✅ Подключение к QUICKNODE RPC установлено');

        // 2. Кастомная ALT таблица
        const customALTAddress = new PublicKey('FAeyUf4AdG7vTQ2Q89r9hWBkPdYkykJKG4CxgqArBGXe');
        console.log(`📋 Кастомная ALT таблица: ${customALTAddress.toString()}`);

        // 3. Загружаем 26 непокрытых ключей
        console.log('\n📋 Загрузка 26 непокрытых ключей...');
        const uncoveredKeysData = JSON.parse(fs.readFileSync('all-24-instructions-uncovered-keys.json', 'utf8'));
        const uncoveredKeys = uncoveredKeysData.keysToAdd.map(key => key.address);
        console.log(`📊 Загружено непокрытых ключей: ${uncoveredKeys.length}`);

        // 4. Загружаем текущее состояние ALT таблицы
        console.log('\n🔍 ЗАГРУЗКА ТЕКУЩЕГО СОСТОЯНИЯ ALT ТАБЛИЦЫ...');
        const altAccount = await connection.getAddressLookupTable(customALTAddress);
        
        if (!altAccount || !altAccount.value) {
            throw new Error('Кастомная ALT таблица не найдена в блокчейне!');
        }

        const currentAddresses = altAccount.value.state.addresses.map(addr => addr.toString());
        console.log(`📊 Текущих адресов в ALT: ${currentAddresses.length}`);

        // 5. ДЕТАЛЬНОЕ СРАВНЕНИЕ
        console.log('\n🔍 ДЕТАЛЬНОЕ СРАВНЕНИЕ:');
        console.log('=' .repeat(60));

        let alreadyInALT = 0;
        let notInALT = 0;
        let invalidKeys = 0;

        const keysToAdd = [];
        const keysAlreadyInALT = [];
        const invalidKeysList = [];

        for (let i = 0; i < uncoveredKeys.length; i++) {
            const key = uncoveredKeys[i];
            const keyData = uncoveredKeysData.keysToAdd[i];
            
            // Проверяем валидность ключа
            try {
                new PublicKey(key);
            } catch (error) {
                console.log(`❌ ${i + 1}. НЕВАЛИДНЫЙ: ${key.slice(0,8)}...${key.slice(-8)} (${keyData.category})`);
                invalidKeys++;
                invalidKeysList.push(key);
                continue;
            }

            // Проверяем есть ли в ALT
            if (currentAddresses.includes(key)) {
                console.log(`✅ ${i + 1}. УЖЕ В ALT: ${key.slice(0,8)}...${key.slice(-8)} (${keyData.category})`);
                alreadyInALT++;
                keysAlreadyInALT.push(key);
            } else {
                console.log(`🔥 ${i + 1}. НУЖНО ДОБАВИТЬ: ${key.slice(0,8)}...${key.slice(-8)} (${keyData.category})`);
                notInALT++;
                keysToAdd.push({
                    address: key,
                    category: keyData.category,
                    name: keyData.name
                });
            }
        }

        // 6. ИТОГОВАЯ СТАТИСТИКА
        console.log('\n📊 ИТОГОВАЯ СТАТИСТИКА:');
        console.log('=' .repeat(60));
        console.log(`📋 Всего непокрытых ключей: ${uncoveredKeys.length}`);
        console.log(`✅ Уже в ALT таблице: ${alreadyInALT}`);
        console.log(`🔥 Нужно добавить: ${notInALT}`);
        console.log(`❌ Невалидных ключей: ${invalidKeys}`);
        console.log(`📊 Текущий размер ALT: ${currentAddresses.length}`);
        console.log(`📊 Размер после добавления: ${currentAddresses.length + notInALT}`);
        console.log(`📊 Лимит ALT: 256`);

        // 7. ЭКОНОМИЯ БАЙТ
        const byteSavings = notInALT * 31;
        console.log(`💰 Экономия байт: ${byteSavings} байт`);

        // 8. ПОКАЗЫВАЕМ ЧТО НУЖНО ДОБАВИТЬ
        if (keysToAdd.length > 0) {
            console.log('\n🔥 КЛЮЧИ ДЛЯ ДОБАВЛЕНИЯ:');
            console.log('=' .repeat(60));
            keysToAdd.forEach((key, index) => {
                console.log(`   ${index + 1}. ${key.address.slice(0,8)}...${key.address.slice(-8)} (${key.category})`);
            });
        }

        // 9. ПОКАЗЫВАЕМ НЕВАЛИДНЫЕ КЛЮЧИ
        if (invalidKeysList.length > 0) {
            console.log('\n❌ НЕВАЛИДНЫЕ КЛЮЧИ:');
            console.log('=' .repeat(60));
            invalidKeysList.forEach((key, index) => {
                console.log(`   ${index + 1}. ${key}`);
            });
        }

        // 10. СОХРАНЯЕМ РЕЗУЛЬТАТ
        const result = {
            timestamp: new Date().toISOString(),
            altAddress: customALTAddress.toString(),
            currentALTSize: currentAddresses.length,
            analysis: {
                totalUncoveredKeys: uncoveredKeys.length,
                alreadyInALT: alreadyInALT,
                needToAdd: notInALT,
                invalidKeys: invalidKeys
            },
            keysToAdd: keysToAdd,
            keysAlreadyInALT: keysAlreadyInALT,
            invalidKeys: invalidKeysList,
            byteSavings: byteSavings,
            finalALTSize: currentAddresses.length + notInALT
        };

        const resultFile = 'custom-alt-vs-uncovered-keys-analysis.json';
        fs.writeFileSync(resultFile, JSON.stringify(result, null, 2));
        console.log(`\n✅ Результат сохранен в: ${resultFile}`);

        console.log(`\n${'='.repeat(80)}`);
        console.log('🎯 ВЫВОД:');
        if (notInALT > 0) {
            console.log(`🔥 НУЖНО ДОБАВИТЬ ${notInALT} КЛЮЧЕЙ В ALT ТАБЛИЦУ!`);
            console.log(`💰 ЭТО СЭКОНОМИТ ${byteSavings} БАЙТ В ТРАНЗАКЦИЯХ!`);
        } else {
            console.log('✅ ВСЕ ВАЛИДНЫЕ КЛЮЧИ УЖЕ В ALT ТАБЛИЦЕ!');
        }
        console.log(`${'='.repeat(80)}`);

    } catch (error) {
        console.error('❌ Ошибка проверки:', error.message);
        console.error(error.stack);
    }
}

// Запуск проверки
if (require.main === module) {
    checkCustomALTvsUncoveredKeys();
}

module.exports = { checkCustomALTvsUncoveredKeys };
