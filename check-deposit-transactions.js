/**
 * 🔍 ПРОВЕРКА ТРАНЗАКЦИЙ ДЕПОЗИТА
 * 
 * Проверяем что произошло с транзакциями депозита в MarginFi
 */

const { Connection } = require('@solana/web3.js');

class DepositTransactionChecker {
    constructor() {
        this.connection = new Connection('https://billowing-empty-patron.solana-mainnet.quiknode.pro/c79212a7508ec6a37353793d9be93c917898e858/', 'confirmed');
    }

    /**
     * 🔍 ПРОВЕРКА ТРАНЗАКЦИИ
     */
    async checkTransaction(signature) {
        try {
            console.log(`\n🔍 ПРОВЕРКА ТРАНЗАКЦИИ: ${signature}`);
            console.log('═══════════════════════════════════════════════════════════════');
            
            const tx = await this.connection.getTransaction(signature, {
                maxSupportedTransactionVersion: 0
            });
            
            if (!tx) {
                console.log('❌ Транзакция не найдена');
                return false;
            }
            
            console.log(`✅ Транзакция найдена`);
            console.log(`📊 Slot: ${tx.slot}`);
            console.log(`⏱️ Block Time: ${new Date(tx.blockTime * 1000).toISOString()}`);
            console.log(`💰 Fee: ${tx.meta.fee} lamports`);
            
            if (tx.meta.err) {
                console.log(`❌ Статус: FAILED`);
                console.log(`🚨 Ошибка: ${JSON.stringify(tx.meta.err)}`);
                return false;
            } else {
                console.log(`✅ Статус: SUCCESS`);
            }
            
            // Анализируем инструкции
            console.log(`\n📋 ИНСТРУКЦИИ (${tx.transaction.message.instructions.length}):`);
            
            for (let i = 0; i < tx.transaction.message.instructions.length; i++) {
                const ix = tx.transaction.message.instructions[i];
                const programId = tx.transaction.message.accountKeys[ix.programIdIndex];
                
                console.log(`   ${i + 1}. Program: ${programId.toString()}`);
                console.log(`      Data: ${Buffer.from(ix.data, 'base64').toString('hex')}`);
                console.log(`      Accounts: ${ix.accounts.length}`);
                
                // Проверяем если это MarginFi
                if (programId.toString() === 'MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA') {
                    console.log(`      🎯 MARGINFI ИНСТРУКЦИЯ!`);
                    
                    const data = Buffer.from(ix.data, 'base64');
                    const discriminator = data.slice(0, 8);
                    console.log(`      🔍 Discriminator: ${discriminator.toString('hex')}`);
                    
                    // Известные discriminators
                    const discriminators = {
                        'ab5e6b67524454c8': 'lending_account_deposit',
                        '047e74353005d41f': 'lending_account_borrow',
                        '8c3c1e8b1e6e9f2d': 'lending_account_repay',
                        '7c3e8b1a5c6e9f2d': 'lending_account_withdraw'
                    };
                    
                    const discriminatorHex = discriminator.toString('hex');
                    const instructionName = discriminators[discriminatorHex] || 'UNKNOWN';
                    console.log(`      📝 Инструкция: ${instructionName}`);
                }
            }
            
            // Анализируем изменения аккаунтов
            console.log(`\n💰 ИЗМЕНЕНИЯ БАЛАНСОВ:`);
            
            if (tx.meta.preBalances && tx.meta.postBalances) {
                for (let i = 0; i < tx.meta.preBalances.length; i++) {
                    const pre = tx.meta.preBalances[i];
                    const post = tx.meta.postBalances[i];
                    const diff = post - pre;
                    
                    if (diff !== 0) {
                        const account = tx.transaction.message.accountKeys[i];
                        console.log(`   ${account.toString()}: ${diff > 0 ? '+' : ''}${diff} lamports`);
                    }
                }
            }
            
            // Анализируем логи
            if (tx.meta.logMessages) {
                console.log(`\n📝 ЛОГИ:`);
                for (const log of tx.meta.logMessages) {
                    if (log.includes('Error') || log.includes('error')) {
                        console.log(`   ❌ ${log}`);
                    } else if (log.includes('MarginFi') || log.includes('MFv2')) {
                        console.log(`   🎯 ${log}`);
                    }
                }
            }
            
            return true;
            
        } catch (error) {
            console.error(`❌ Ошибка проверки транзакции: ${error.message}`);
            return false;
        }
    }

    /**
     * 🔍 АНАЛИЗ ВСЕХ ТРАНЗАКЦИЙ ДЕПОЗИТА
     */
    async analyzeDepositTransactions() {
        try {
            console.log('🔍 АНАЛИЗ ТРАНЗАКЦИЙ ДЕПОЗИТА В MARGINFI');
            console.log('═══════════════════════════════════════════════════════════════');
            
            const transactions = [
                '3maMov4pNHQxyYLRnip9y8vuTnWkE4QjrBkYkR3eAtXz',
                'e1aMZgABFBkhWwswWS26vzCdLap7fFZ1uh3VF2zFucNd1poYWFV1X6Mokef2C8TaXt5Htzv2hKWJL8fYSCnisfn'
            ];
            
            let successCount = 0;
            
            for (const signature of transactions) {
                const success = await this.checkTransaction(signature);
                if (success) successCount++;
            }
            
            console.log(`\n📊 ИТОГ АНАЛИЗА:`);
            console.log(`   Всего транзакций: ${transactions.length}`);
            console.log(`   Успешных: ${successCount}`);
            console.log(`   Провалившихся: ${transactions.length - successCount}`);
            
            if (successCount === 0) {
                console.log('\n🚨 ВСЕ ТРАНЗАКЦИИ ДЕПОЗИТА ПРОВАЛИЛИСЬ!');
                console.log('💡 Это объясняет почему lending balance не создан');
                console.log('🔧 Нужно повторить депозит в MarginFi UI');
            } else if (successCount < transactions.length) {
                console.log('\n⚠️ НЕКОТОРЫЕ ТРАНЗАКЦИИ ПРОВАЛИЛИСЬ');
                console.log('🔍 Проверь детали выше');
            } else {
                console.log('\n✅ ВСЕ ТРАНЗАКЦИИ УСПЕШНЫ');
                console.log('🤔 Но lending balance все еще не создан...');
                console.log('💡 Возможно нужно время для обновления');
            }
            
        } catch (error) {
            console.error(`❌ Ошибка анализа: ${error.message}`);
        }
    }
}

async function main() {
    console.log('🔍 ПРОВЕРКА ТРАНЗАКЦИЙ ДЕПОЗИТА В MARGINFI');
    console.log('═══════════════════════════════════════════════════════════════');
    console.log('🎯 Цель: Выяснить почему lending balance не создан');
    console.log('💡 Анализируем транзакции депозита из MarginFi UI');
    console.log('═══════════════════════════════════════════════════════════════');

    const checker = new DepositTransactionChecker();
    await checker.analyzeDepositTransactions();
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = DepositTransactionChecker;
