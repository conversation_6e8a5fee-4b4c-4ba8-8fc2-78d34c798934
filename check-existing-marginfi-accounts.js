#!/usr/bin/env node

/**
 * 🔍 ПРОВЕРКА ВСЕХ СУЩЕСТВУЮЩИХ MARGINFI АККАУНТОВ
 * 
 * Проверяем все аккаунты связанные с кошельком
 */

const { Connection, PublicKey, Keypair } = require('@solana/web3.js');
const { MarginfiClient, getConfig } = require('@mrgnlabs/marginfi-client-v2');
const { NodeWallet } = require('@mrgnlabs/mrgn-common');
const fs = require('fs');
const bs58 = require('bs58');

class MarginFiAccountChecker {
  constructor() {
    console.log('🔍 ПРОВЕРКА ВСЕХ СУЩЕСТВУЮЩИХ MARGINFI АККАУНТОВ');
    console.log('═══════════════════════════════════════════════════════════════════');
  }

  async initialize() {
    try {
      // Инициализация подключения
      console.log('🔗 Инициализация подключения...');
      this.connection = new Connection('https://sparkling-patient-market.solana-mainnet.quiknode.pro/146de9e433ca3b8f3473c0bd41742b24429dfc43/', 'confirmed');

      // Загрузка кошелька из .env.solana
      console.log('👛 Загрузка кошелька из .env.solana...');
      
      const envPath = 'H:/Mempool/DEXSWAP/.env.solana';
      const envContent = fs.readFileSync(envPath, 'utf8');
      const lines = envContent.split('\n');

      let privateKey = null;
      let walletAddress = null;
      
      for (const line of lines) {
        if (line.startsWith('WALLET_PRIVATE_KEY=')) {
          privateKey = line.split('=')[1].trim();
        }
        if (line.startsWith('WALLET_ADDRESS=')) {
          walletAddress = line.split('=')[1].trim();
        }
      }

      if (!privateKey) {
        throw new Error('WALLET_PRIVATE_KEY не найден в .env.solana');
      }

      const keypair = Keypair.fromSecretKey(bs58.default ? bs58.default.decode(privateKey) : bs58.decode(privateKey));
      this.wallet = new NodeWallet(keypair);
      
      console.log(`✅ Кошелек загружен:`);
      console.log(`   📍 Адрес: ${this.wallet.publicKey.toString()}`);
      console.log(`   📍 Конфиг: ${walletAddress || 'не указан'}`);

      // Проверяем баланс SOL
      const balance = await this.connection.getBalance(this.wallet.publicKey);
      console.log(`💰 Баланс SOL: ${balance / 1e9} SOL`);

      // Инициализация MarginFi клиента
      console.log('🏦 Инициализация MarginFi клиента...');
      const config = getConfig('production');
      this.client = await MarginfiClient.fetch(config, this.wallet, this.connection);
      console.log(`✅ MarginFi клиент инициализирован`);

    } catch (error) {
      console.error('❌ Ошибка инициализации:', error.message);
      throw error;
    }
  }

  async checkAllAccounts() {
    console.log('\n🔍 ПОИСК ВСЕХ MARGINFI АККАУНТОВ');
    console.log('═══════════════════════════════════════════════════════════════════');

    try {
      const accounts = await this.client.getMarginfiAccountsForAuthority();
      console.log(`📊 Найдено аккаунтов: ${accounts.length}`);

      if (accounts.length === 0) {
        console.log('❌ НЕТ СУЩЕСТВУЮЩИХ АККАУНТОВ');
        console.log('💡 Нужно создать новый аккаунт или пополнить кошелек SOL');
        return [];
      }

      console.log('\n📋 ДЕТАЛЬНАЯ ИНФОРМАЦИЯ ОБ АККАУНТАХ:');
      console.log('═══════════════════════════════════════════════════════════════════');

      const accountsInfo = [];

      for (let i = 0; i < accounts.length; i++) {
        const account = accounts[i];
        console.log(`\n${i + 1}. АККАУНТ: ${account.address.toString()}`);
        console.log('───────────────────────────────────────────────────────────────────');

        // Проверяем балансы
        const balances = account.activeBalances;
        console.log(`💰 Активных позиций: ${balances.length}`);

        let totalAssets = 0;
        let totalLiabilities = 0;
        const positions = [];

        if (balances.length === 0) {
          console.log('✅ ЧИСТЫЙ АККАУНТ - НЕТ ПОЗИЦИЙ');
        } else {
          balances.forEach((balance, j) => {
            try {
              const bank = this.client.getBankByPk(balance.bankPk);
              const oraclePrice = this.client.getOraclePriceByBank(bank.address);
              const { assets, liabilities } = balance.computeUsdValue(bank, oraclePrice);
              
              console.log(`   ${j + 1}. ${bank.tokenSymbol}:`);
              console.log(`      💰 Активы: $${assets.toFixed(2)}`);
              console.log(`      💸 Долги: $${liabilities.toFixed(2)}`);
              
              totalAssets += assets;
              totalLiabilities += liabilities;

              positions.push({
                token: bank.tokenSymbol,
                assets,
                liabilities
              });
            } catch (balanceError) {
              console.log(`   ${j + 1}. Ошибка анализа позиции: ${balanceError.message}`);
            }
          });
        }

        const netWorth = totalAssets - totalLiabilities;
        const isHealthy = netWorth >= 0;

        console.log(`\n📊 ИТОГО ПО АККАУНТУ:`);
        console.log(`   💰 Общие активы: $${totalAssets.toFixed(2)}`);
        console.log(`   💸 Общие долги: $${totalLiabilities.toFixed(2)}`);
        console.log(`   💵 Чистая стоимость: $${netWorth.toFixed(2)}`);
        console.log(`   🏥 Здоровье: ${isHealthy ? '✅ ХОРОШЕЕ' : '❌ ПЛОХОЕ'}`);

        // Определяем статус аккаунта
        let status = 'НЕИЗВЕСТНО';
        let canUseForFlashLoans = false;

        if (balances.length === 0) {
          status = 'ЧИСТЫЙ';
          canUseForFlashLoans = true;
        } else if (isHealthy && totalLiabilities === 0) {
          status = 'ТОЛЬКО АКТИВЫ';
          canUseForFlashLoans = true;
        } else if (isHealthy && totalLiabilities > 0) {
          status = 'ЕСТЬ ДОЛГИ НО ЗДОРОВЫЙ';
          canUseForFlashLoans = false;
        } else {
          status = 'НЕЗДОРОВЫЙ';
          canUseForFlashLoans = false;
        }

        console.log(`   📋 Статус: ${status}`);
        console.log(`   🚀 Можно для Flash Loans: ${canUseForFlashLoans ? '✅ ДА' : '❌ НЕТ'}`);

        accountsInfo.push({
          address: account.address.toString(),
          totalAssets,
          totalLiabilities,
          netWorth,
          isHealthy,
          status,
          canUseForFlashLoans,
          positions,
          account
        });
      }

      return accountsInfo;

    } catch (error) {
      console.error('❌ Ошибка проверки аккаунтов:', error.message);
      return [];
    }
  }

  async findBestAccount(accountsInfo) {
    console.log('\n🎯 ПОИСК ЛУЧШЕГО АККАУНТА ДЛЯ FLASH LOANS');
    console.log('═══════════════════════════════════════════════════════════════════');

    if (accountsInfo.length === 0) {
      console.log('❌ НЕТ АККАУНТОВ ДЛЯ АНАЛИЗА');
      return null;
    }

    // Ищем чистые аккаунты
    const cleanAccounts = accountsInfo.filter(acc => acc.canUseForFlashLoans);

    if (cleanAccounts.length === 0) {
      console.log('❌ НЕТ ПОДХОДЯЩИХ АККАУНТОВ ДЛЯ FLASH LOANS');
      console.log('💡 ВСЕ АККАУНТЫ ИМЕЮТ ДОЛГИ ИЛИ ПРОБЛЕМЫ');
      
      console.log('\n📋 РЕКОМЕНДАЦИИ:');
      console.log('1. Создать новый аккаунт (нужно пополнить SOL)');
      console.log('2. Очистить долги существующих аккаунтов');
      console.log('3. Использовать другой кошелек');
      
      return null;
    }

    console.log(`✅ НАЙДЕНО ${cleanAccounts.length} ПОДХОДЯЩИХ АККАУНТОВ:`);

    // Выбираем лучший аккаунт
    let bestAccount = cleanAccounts[0];
    
    cleanAccounts.forEach((acc, i) => {
      console.log(`\n${i + 1}. ${acc.address}`);
      console.log(`   📋 Статус: ${acc.status}`);
      console.log(`   💰 Активы: $${acc.totalAssets.toFixed(2)}`);
      console.log(`   💸 Долги: $${acc.totalLiabilities.toFixed(2)}`);
      console.log(`   🚀 Готов для Flash Loans: ✅ ДА`);
      
      // Предпочитаем полностью чистые аккаунты
      if (acc.status === 'ЧИСТЫЙ' && bestAccount.status !== 'ЧИСТЫЙ') {
        bestAccount = acc;
      }
    });

    console.log(`\n🏆 ЛУЧШИЙ АККАУНТ: ${bestAccount.address}`);
    console.log(`   📋 Статус: ${bestAccount.status}`);
    console.log(`   🚀 Готов для безопасных Flash Loans!`);

    return bestAccount;
  }

  async testFlashLoanCapability(accountInfo) {
    console.log('\n🧪 ТЕСТИРОВАНИЕ FLASH LOAN ВОЗМОЖНОСТЕЙ');
    console.log('═══════════════════════════════════════════════════════════════════');

    try {
      const account = accountInfo.account;
      
      console.log('🔧 Тестируем buildFlashLoanTx...');
      
      // Создаем БЕЗОПАСНЫЙ тест buildFlashLoanTx БЕЗ прямых вызовов
      const testFlashLoanTx = await account.buildFlashLoanTx({
        ixs: [], // Пустые инструкции для безопасного тестирования
        signers: []
      });
      
      console.log(`✅ buildFlashLoanTx работает без ошибок!`);
      console.log(`📊 Транзакция создана: ${testFlashLoanTx ? 'ДА' : 'НЕТ'}`);
      
      if (testFlashLoanTx) {
        console.log(`📏 Размер транзакции: ${testFlashLoanTx.serialize().length} байт`);
      }

      console.log('\n🎉 ТЕСТ ПРОЙДЕН УСПЕШНО!');
      console.log('✅ buildFlashLoanTx функционирует корректно');
      console.log('✅ Аккаунт готов для безопасных flash loans');

      return true;

    } catch (testError) {
      console.error(`❌ Ошибка тестирования: ${testError.message}`);
      return false;
    }
  }

  async run() {
    try {
      await this.initialize();
      
      const accountsInfo = await this.checkAllAccounts();
      const bestAccount = await this.findBestAccount(accountsInfo);

      if (bestAccount) {
        const testResult = await this.testFlashLoanCapability(bestAccount);
        
        if (testResult) {
          console.log('\n🎉 УСПЕХ! НАЙДЕН ГОТОВЫЙ АККАУНТ ДЛЯ FLASH LOANS!');
          console.log('═══════════════════════════════════════════════════════════════════');
          console.log(`✅ Адрес аккаунта: ${bestAccount.address}`);
          console.log(`✅ Статус: ${bestAccount.status}`);
          console.log(`✅ buildFlashLoanTx работает корректно`);
          console.log(`✅ Готов для безопасных flash loans`);
          console.log(`🚀 МОЖНО ЗАПУСКАТЬ АРБИТРАЖ!`);
          
          // Сохраняем информацию об аккаунте
          const accountData = {
            address: bestAccount.address,
            wallet: this.wallet.publicKey.toString(),
            status: bestAccount.status,
            totalAssets: bestAccount.totalAssets,
            totalLiabilities: bestAccount.totalLiabilities,
            canUseForFlashLoans: bestAccount.canUseForFlashLoans,
            tested: true,
            found: new Date().toISOString()
          };
          
          fs.writeFileSync('ready-marginfi-account.json', JSON.stringify(accountData, null, 2));
          console.log('✅ Информация сохранена в ready-marginfi-account.json');
        }
      } else {
        console.log('\n❌ НЕ НАЙДЕНО ПОДХОДЯЩИХ АККАУНТОВ');
        console.log('💡 РЕКОМЕНДАЦИИ:');
        console.log('1. Пополнить кошелек на 0.005 SOL и создать новый аккаунт');
        console.log('2. Очистить долги существующих аккаунтов');
        console.log('3. Использовать другой кошелек');
      }

    } catch (error) {
      console.error('❌ Критическая ошибка:', error.message);
    }
  }
}

// Запуск проверки
if (require.main === module) {
  const checker = new MarginFiAccountChecker();
  checker.run().catch(console.error);
}

module.exports = MarginFiAccountChecker;
