const { Connection } = require('@solana/web3.js');

async function checkFailedTransaction() {
    const connection = new Connection('https://api.mainnet-beta.solana.com');
    
    // Последняя отправленная транзакция
    const signature = '2pDjv4JjL44CHw64QX6iyPRZj42EPMUdayVyiP3DL9Bt7RcgCoC9b1s93c16CKqSfXMjPvEQWaZFNdFAMgUTrbQe';
    
    try {
        console.log(`🔍 Проверяем провалившуюся транзакцию: ${signature}`);
        
        // Получаем детали транзакции
        const transaction = await connection.getTransaction(signature, {
            maxSupportedTransactionVersion: 0
        });
        
        if (transaction) {
            console.log('📋 ТРАНЗАКЦИЯ НАЙДЕНА В БЛОКЧЕЙНЕ');
            console.log(`   Slot: ${transaction.slot}`);
            console.log(`   Block time: ${new Date(transaction.blockTime * 1000)}`);
            console.log(`   Fee: ${transaction.meta.fee} lamports`);
            
            if (transaction.meta.err) {
                console.log('❌ ТРАНЗАКЦИЯ ПРОВАЛИЛАСЬ!');
                console.log(`   Ошибка: ${JSON.stringify(transaction.meta.err, null, 2)}`);
                
                // Анализируем тип ошибки
                if (transaction.meta.err.InstructionError) {
                    const [instructionIndex, error] = transaction.meta.err.InstructionError;
                    console.log(`   Инструкция #${instructionIndex} провалилась: ${JSON.stringify(error)}`);
                }
                
                // Показываем логи программ
                if (transaction.meta.logMessages) {
                    console.log('\n📝 ЛОГИ ПРОГРАММ:');
                    transaction.meta.logMessages.forEach((log, index) => {
                        console.log(`   ${index}: ${log}`);
                    });
                }
                
            } else {
                console.log('✅ Транзакция успешна');
            }
            
        } else {
            console.log('❌ Транзакция НЕ найдена в блокчейне');
        }
        
    } catch (error) {
        console.error('❌ Ошибка проверки:', error.message);
    }
}

// Также проверим последние транзакции кошелька
async function checkWalletTransactions() {
    const connection = new Connection('https://api.mainnet-beta.solana.com');
    const walletAddress = 'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV';
    
    try {
        console.log(`\n🔍 Проверяем последние транзакции кошелька: ${walletAddress}`);
        
        const signatures = await connection.getSignaturesForAddress(
            new (require('@solana/web3.js')).PublicKey(walletAddress),
            { limit: 5 }
        );
        
        console.log(`📊 Найдено ${signatures.length} последних транзакций:`);
        
        for (let i = 0; i < signatures.length; i++) {
            const sig = signatures[i];
            console.log(`\n${i + 1}. ${sig.signature}`);
            console.log(`   Время: ${new Date(sig.blockTime * 1000)}`);
            console.log(`   Статус: ${sig.err ? '❌ ПРОВАЛИЛАСЬ' : '✅ УСПЕШНА'}`);
            
            if (sig.err) {
                console.log(`   Ошибка: ${JSON.stringify(sig.err)}`);
            }
        }

        return signatures;

    } catch (error) {
        console.error('❌ Ошибка проверки кошелька:', error.message);
        return [];
    }
}

async function main() {
    const signatures = await checkWalletTransactions();

    // Проверяем САМУЮ ПОСЛЕДНЮЮ транзакцию из логов (строка 2832)
    const lastSignature = '4UGhPbYNmi9iMNHsHVuV5AJpT6yMzwihEKPde39FSKGFL6Q8hx8f1gtkexR9LVgEmeqBCQFxikmEGFkfuuwr4Eyw';
    console.log(`\n🔍 ДЕТАЛЬНЫЙ АНАЛИЗ ПОСЛЕДНЕЙ ОТПРАВЛЕННОЙ ТРАНЗАКЦИИ: ${lastSignature}`);
    await checkSpecificTransaction(lastSignature);
}

async function checkSpecificTransaction(signature) {
    const connection = new Connection('https://api.mainnet-beta.solana.com');

    try {
        const transaction = await connection.getTransaction(signature, {
            maxSupportedTransactionVersion: 0
        });

        if (transaction && transaction.meta.err) {
            console.log('📝 ДЕТАЛЬНЫЕ ЛОГИ ПРОГРАММ:');
            transaction.meta.logMessages.forEach((log, index) => {
                console.log(`   ${index}: ${log}`);
            });

            console.log('\n📊 АНАЛИЗ ИНСТРУКЦИЙ:');
            transaction.transaction.message.instructions.forEach((ix, index) => {
                console.log(`   Инструкция #${index}: Program ${ix.programId || 'Unknown'}`);
            });
        }

    } catch (error) {
        console.error('❌ Ошибка детального анализа:', error.message);
    }
}

main();
