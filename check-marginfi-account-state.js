/**
 * 🔍 ПРОВЕРКА РЕАЛЬНОГО СОСТОЯНИЯ MARGINFI АККАУНТА
 * 
 * Проверяем что произошло после депозита в MarginFi UI
 */

const { Connection, PublicKey } = require('@solana/web3.js');

class MarginFiAccountChecker {
    constructor() {
        this.connection = new Connection('https://billowing-empty-patron.solana-mainnet.quiknode.pro/c79212a7508ec6a37353793d9be93c917898e858/', 'confirmed');
        this.MARGINFI_ACCOUNT = new PublicKey('3dPECtRBPcYtcJG6wWDCr7VCyzxttDTjgGgo9gWC9vmU');
        this.SOL_BANK = new PublicKey('CCKtUs6Cgwo4aaQUmBPmyoApH2gUDErxNZCAntD6LYGh');
    }

    /**
     * 🔍 ПРОВЕРКА СОСТОЯНИЯ MARGINFI АККАУНТА
     */
    async checkAccountState() {
        try {
            console.log('🔍 ПРОВЕРКА СОСТОЯНИЯ MARGINFI АККАУНТА ПОСЛЕ ДЕПОЗИТА');
            console.log('═══════════════════════════════════════════════════════════════');
            console.log(`📍 Аккаунт: ${this.MARGINFI_ACCOUNT.toString()}`);
            
            const accountInfo = await this.connection.getAccountInfo(this.MARGINFI_ACCOUNT);
            if (!accountInfo) {
                console.log('❌ MarginFi аккаунт не найден!');
                return false;
            }
            
            console.log('✅ MarginFi аккаунт найден');
            console.log(`📏 Размер данных: ${accountInfo.data.length} bytes`);
            console.log(`👤 Владелец: ${accountInfo.owner.toString()}`);
            
            // Анализируем balances
            const balances = await this.analyzeBalances(accountInfo.data);
            
            return balances.length > 0;
            
        } catch (error) {
            console.error(`❌ Ошибка проверки аккаунта: ${error.message}`);
            return false;
        }
    }

    /**
     * 🔍 АНАЛИЗ BALANCES В АККАУНТЕ
     */
    async analyzeBalances(data) {
        try {
            console.log('\n📊 АНАЛИЗ LENDING BALANCES:');
            console.log('═══════════════════════════════════════════════════════════════');
            
            // MarginFi аккаунт структура:
            // 0-8: discriminator
            // 8-40: group
            // 40-72: authority
            // 72-74: account_flags
            // 74-...: balances (каждый 184 байта)
            
            const balancesStart = 74;
            const balancesData = data.slice(balancesStart);
            const BALANCE_SIZE = 184;
            const maxBalances = Math.floor(balancesData.length / BALANCE_SIZE);
            
            console.log(`📊 Максимум balances: ${maxBalances}`);
            console.log(`📏 Данные balances: ${balancesData.length} bytes`);
            
            const balances = [];
            
            for (let i = 0; i < maxBalances; i++) {
                const balanceOffset = i * BALANCE_SIZE;
                const isActive = balancesData[balanceOffset] === 1;
                
                console.log(`\n🔍 Balance ${i + 1}:`);
                console.log(`   Offset: ${balanceOffset}`);
                console.log(`   Active: ${isActive ? 'ДА' : 'НЕТ'}`);
                
                if (isActive) {
                    const bankOffset = balanceOffset + 8;
                    const bankBytes = balancesData.slice(bankOffset, bankOffset + 32);
                    const bankPubkey = new PublicKey(bankBytes);
                    
                    const assetSharesOffset = bankOffset + 32;
                    const liabilitySharesOffset = assetSharesOffset + 16;
                    
                    const assetShares = balancesData.readBigUInt64LE(assetSharesOffset);
                    const liabilityShares = balancesData.readBigUInt64LE(liabilitySharesOffset);
                    
                    console.log(`   🏦 Банк: ${bankPubkey.toString()}`);
                    console.log(`   💰 Asset Shares: ${assetShares.toString()}`);
                    console.log(`   💸 Liability Shares: ${liabilityShares.toString()}`);
                    
                    // Проверяем если это SOL банк
                    if (bankPubkey.equals(this.SOL_BANK)) {
                        console.log('   🎯 ЭТО SOL БАНК!');
                        console.log(`   ✅ SOL lending balance найден!`);
                        console.log(`   💰 SOL Assets: ${assetShares.toString()}`);
                        console.log(`   💸 SOL Liabilities: ${liabilityShares.toString()}`);
                    }
                    
                    balances.push({
                        index: i,
                        bank: bankPubkey,
                        assetShares: assetShares,
                        liabilityShares: liabilityShares,
                        isSOL: bankPubkey.equals(this.SOL_BANK)
                    });
                }
            }
            
            console.log(`\n📊 ИТОГ АНАЛИЗА:`);
            console.log(`   Всего активных balances: ${balances.length}`);
            
            const solBalance = balances.find(b => b.isSOL);
            if (solBalance) {
                console.log(`   ✅ SOL lending balance: НАЙДЕН!`);
                console.log(`   💰 SOL Assets: ${solBalance.assetShares.toString()}`);
                console.log(`   💸 SOL Liabilities: ${solBalance.liabilityShares.toString()}`);
                console.log(`   🚀 Flash Loan возможен: ${solBalance.assetShares > 0n ? 'ДА' : 'НЕТ'}`);
            } else {
                console.log(`   ❌ SOL lending balance: НЕ НАЙДЕН!`);
            }
            
            return balances;
            
        } catch (error) {
            console.error(`❌ Ошибка анализа balances: ${error.message}`);
            return [];
        }
    }

    /**
     * 🔍 ПРОВЕРКА ПОСЛЕДНИХ ТРАНЗАКЦИЙ
     */
    async checkRecentTransactions() {
        try {
            console.log('\n🔍 ПРОВЕРКА ПОСЛЕДНИХ ТРАНЗАКЦИЙ:');
            console.log('═══════════════════════════════════════════════════════════════');
            
            const signatures = await this.connection.getSignaturesForAddress(
                this.MARGINFI_ACCOUNT,
                { limit: 10 }
            );
            
            console.log(`📊 Найдено транзакций: ${signatures.length}`);
            
            for (let i = 0; i < Math.min(5, signatures.length); i++) {
                const sig = signatures[i];
                console.log(`\n📝 Транзакция ${i + 1}:`);
                console.log(`   Signature: ${sig.signature}`);
                console.log(`   Slot: ${sig.slot}`);
                console.log(`   Status: ${sig.err ? 'FAILED' : 'SUCCESS'}`);
                
                if (sig.err) {
                    console.log(`   ❌ Ошибка: ${JSON.stringify(sig.err)}`);
                }
            }
            
        } catch (error) {
            console.error(`❌ Ошибка проверки транзакций: ${error.message}`);
        }
    }

    /**
     * 🎯 ДИАГНОСТИКА ОШИБКИ 3007
     */
    async diagnoseError3007() {
        try {
            console.log('\n🎯 ДИАГНОСТИКА ОШИБКИ 3007:');
            console.log('═══════════════════════════════════════════════════════════════');
            
            const hasBalances = await this.checkAccountState();
            await this.checkRecentTransactions();
            
            console.log('\n🔍 ВОЗМОЖНЫЕ ПРИЧИНЫ ОШИБКИ 3007:');
            
            if (!hasBalances) {
                console.log('❌ 1. Lending balance действительно не создан');
                console.log('💡 Решение: Повторить депозит в MarginFi UI');
            } else {
                console.log('✅ 1. Lending balance существует');
                console.log('🔍 2. Проверяем другие причины...');
                
                console.log('\n🚨 ВОЗМОЖНЫЕ ПРИЧИНЫ:');
                console.log('   ❌ Неправильный bank address в коде');
                console.log('   ❌ Неправильная структура borrow инструкции');
                console.log('   ❌ Неправильные аккаунты в borrow');
                console.log('   ❌ Проблема с authority или permissions');
                console.log('   ❌ Кэширование старого состояния аккаунта');
            }
            
            console.log('\n💡 РЕКОМЕНДАЦИИ:');
            console.log('   1. Проверить правильность bank address');
            console.log('   2. Обновить структуру borrow инструкции');
            console.log('   3. Использовать свежие данные аккаунта');
            console.log('   4. Проверить permissions и authority');
            
        } catch (error) {
            console.error(`❌ Ошибка диагностики: ${error.message}`);
        }
    }
}

async function main() {
    console.log('🔍 ДИАГНОСТИКА MARGINFI АККАУНТА ПОСЛЕ ДЕПОЗИТА');
    console.log('═══════════════════════════════════════════════════════════════');
    console.log('🎯 Цель: Выяснить почему ошибка 3007 продолжается');
    console.log('💡 Проверяем реальное состояние после депозита в UI');
    console.log('═══════════════════════════════════════════════════════════════');

    const checker = new MarginFiAccountChecker();
    await checker.diagnoseError3007();

    console.log('\n🎯 ИТОГ ДИАГНОСТИКИ:');
    console.log('Проверь результаты выше для понимания проблемы');
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = MarginFiAccountChecker;
