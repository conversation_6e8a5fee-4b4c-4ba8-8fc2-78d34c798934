#!/usr/bin/env node

/**
 * 🔍 БЫСТРАЯ ПРОВЕРКА ФЛАГА MARGINFI FLASH LOAN
 * ═══════════════════════════════════════════════════════════════
 * 🎯 САМЫЙ НАДЕЖНЫЙ СПОСОБ ПРОВЕРКИ ЧЕРЕЗ ПОПЫТКУ СОЗДАНИЯ FLASH LOAN
 */

const { Connection, Keypair } = require('@solana/web3.js');
const { MarginfiClient, getConfig } = require('@mrgnlabs/marginfi-client-v2');
const { NodeWallet } = require('@mrgnlabs/mrgn-common');
const fs = require('fs');

class MarginFiFlagChecker {
  constructor() {
    this.connection = new Connection('https://billowing-empty-patron.solana-mainnet.quiknode.pro/c79212a7508ec6a37353793d9be93c917898e858/', 'confirmed');
    this.wallet = null;
    this.marginfiClient = null;
    this.marginfiAccount = null;
  }

  async initialize() {
    try {
      // Загружаем wallet
      const walletData = JSON.parse(fs.readFileSync('wallet.json', 'utf8'));
      const keypair = Keypair.fromSecretKey(new Uint8Array(walletData));
      this.wallet = new NodeWallet(keypair);

      // Создаем MarginFi client
      const config = getConfig('production');
      this.marginfiClient = await MarginfiClient.fetch(config, this.wallet, this.connection);

      // Получаем MarginFi аккаунт
      const accounts = await this.marginfiClient.getMarginfiAccountsForAuthority();
      if (accounts.length > 0) {
        this.marginfiAccount = accounts[0];
        return true;
      } else {
        throw new Error('MarginFi аккаунты не найдены');
      }
    } catch (error) {
      console.error(`❌ Ошибка инициализации: ${error.message}`);
      return false;
    }
  }

  // МЕТОД 1: Проверка флагов аккаунта (может быть неточным из-за кэша)
  async checkAccountFlags() {
    try {
      const accountInfo = await this.connection.getAccountInfo(this.marginfiAccount.address, 'finalized');
      if (!accountInfo) {
        throw new Error('Не удалось получить информацию об аккаунте');
      }

      const flags = accountInfo.data.readUInt8(8);
      const inFlashLoan = (flags & 64) !== 0; // Бит 6 = флаг ACCOUNT_IN_FLASHLOAN
      
      return {
        method: 'Account Flags',
        flags: flags,
        flagsHex: `0x${flags.toString(16)}`,
        flagsBinary: flags.toString(2).padStart(8, '0'),
        inFlashLoan: inFlashLoan,
        reliable: false // Может быть кэшировано
      };
    } catch (error) {
      return {
        method: 'Account Flags',
        error: error.message,
        reliable: false
      };
    }
  }

  // МЕТОД 2: Проверка через попытку создания flash loan (САМЫЙ НАДЕЖНЫЙ!)
  async checkFlashLoanCapability() {
    try {
      const flashLoanTx = await this.marginfiAccount.buildFlashLoanTx({
        ixs: [], // Пустые инструкции для теста
        signers: []
      });

      return {
        method: 'Flash Loan Test',
        canCreateFlashLoan: true,
        transactionSize: flashLoanTx.serialize().length,
        reliable: true, // САМЫЙ НАДЕЖНЫЙ МЕТОД!
        status: 'READY',
        message: '✅ Flash loan можно создать - флаг сброшен!'
      };
    } catch (error) {
      let inFlashLoan = false;
      let errorType = 'Unknown';
      let status = 'ERROR';
      
      if (error.message.includes('6037') || error.message.includes('AccountInFlashloan')) {
        inFlashLoan = true;
        errorType = 'AccountInFlashloan';
        status = 'STUCK';
      } else if (error.message.includes('6027') || error.message.includes('BankLiabilityCapacityExceeded')) {
        errorType = 'BankCapacityExceeded';
        status = 'BANK_FULL';
      }
      
      return {
        method: 'Flash Loan Test',
        canCreateFlashLoan: false,
        inFlashLoan: inFlashLoan,
        errorType: errorType,
        status: status,
        error: error.message,
        reliable: true,
        message: inFlashLoan ? '❌ Аккаунт заблокирован - флаг НЕ сброшен!' : '❓ Другая ошибка'
      };
    }
  }

  // БЫСТРАЯ ПРОВЕРКА
  async quickCheck() {
    console.log('⚡ БЫСТРАЯ ПРОВЕРКА MARGINFI FLASH LOAN ФЛАГА');
    console.log('═══════════════════════════════════════════════════════════════');
    console.log(`💼 Wallet: ${this.wallet.publicKey.toString()}`);
    console.log(`🏦 MarginFi аккаунт: ${this.marginfiAccount.address.toString()}`);
    console.log('');

    // Проверяем через попытку создания flash loan (самый надежный метод)
    const flashLoanResult = await this.checkFlashLoanCapability();

    console.log('🧪 ТЕСТ FLASH LOAN (САМЫЙ НАДЕЖНЫЙ):');
    console.log('─────────────────────────────────────────────────────────────');
    console.log(`📊 Статус: ${flashLoanResult.status}`);
    console.log(`💬 ${flashLoanResult.message}`);
    
    if (flashLoanResult.canCreateFlashLoan) {
      console.log(`📏 Размер тестовой транзакции: ${flashLoanResult.transactionSize} байт`);
    } else {
      console.log(`🔍 Тип ошибки: ${flashLoanResult.errorType}`);
      console.log(`📝 Детали: ${flashLoanResult.error}`);
    }

    console.log('');

    // Дополнительно проверяем флаги аккаунта
    const flagsResult = await this.checkAccountFlags();
    
    console.log('🏁 ФЛАГИ АККАУНТА (ДОПОЛНИТЕЛЬНО):');
    console.log('─────────────────────────────────────────────────────────────');
    
    if (flagsResult.error) {
      console.log(`❌ Ошибка чтения флагов: ${flagsResult.error}`);
    } else {
      console.log(`📊 Флаги: ${flagsResult.flags} (${flagsResult.flagsHex})`);
      console.log(`🔢 Бинарно: ${flagsResult.flagsBinary}`);
      console.log(`🔍 Flash loan флаг (бит 6): ${flagsResult.inFlashLoan ? '❌ УСТАНОВЛЕН' : '✅ НЕ УСТАНОВЛЕН'}`);
      console.log(`⚠️ Надежность: ${flagsResult.reliable ? 'ВЫСОКАЯ' : 'НИЗКАЯ (может быть кэш)'}`);
    }

    console.log('');
    console.log('🎯 ЗАКЛЮЧЕНИЕ:');
    console.log('═══════════════════════════════════════════════════════════════');
    
    switch (flashLoanResult.status) {
      case 'READY':
        console.log('🎉 ✅ FLASH LOAN РАБОТАЕТ!');
        console.log('💰 328.591 SOL flash loan доступен для арбитража!');
        console.log('🚀 Можно запускать торгового бота!');
        break;
        
      case 'STUCK':
        console.log('🚨 ❌ ФЛАГ ЗАБЛОКИРОВАН!');
        console.log('🛠️ Нужно выполнить: node correct-marginfi-flag-reset.js');
        console.log('⏰ Или подождать автоматического сброса');
        break;
        
      case 'BANK_FULL':
        console.log('🏦 ⚠️ БАНК ПЕРЕПОЛНЕН!');
        console.log('💡 Банк USDC достиг лимита займов');
        console.log('🔄 Попробуйте позже или используйте другой банк');
        break;
        
      default:
        console.log('❓ НЕИЗВЕСТНАЯ ПРОБЛЕМА');
        console.log('🔍 Проверьте детали ошибки выше');
        console.log('📚 Обратитесь к документации MarginFi');
        break;
    }

    return {
      status: flashLoanResult.status,
      canUseFlashLoan: flashLoanResult.canCreateFlashLoan,
      flagsCheck: flagsResult,
      flashLoanCheck: flashLoanResult
    };
  }

  // ДЕТАЛЬНАЯ ПРОВЕРКА
  async detailedCheck() {
    console.log('🔍 ДЕТАЛЬНАЯ ПРОВЕРКА MARGINFI FLASH LOAN ФЛАГА');
    console.log('═══════════════════════════════════════════════════════════════');
    console.log(`💼 Wallet: ${this.wallet.publicKey.toString()}`);
    console.log(`🏦 MarginFi аккаунт: ${this.marginfiAccount.address.toString()}`);
    console.log('');

    // 1. Проверка флагов аккаунта
    const flagsResult = await this.checkAccountFlags();
    
    console.log('🏁 ПРОВЕРКА ФЛАГОВ АККАУНТА:');
    console.log('─────────────────────────────────────────────────────────────');
    
    if (flagsResult.error) {
      console.log(`❌ Ошибка: ${flagsResult.error}`);
    } else {
      console.log(`📊 Флаги аккаунта: ${flagsResult.flags} (${flagsResult.flagsHex})`);
      console.log(`🔢 Бинарное представление: ${flagsResult.flagsBinary}`);
      console.log(`🔍 Flash loan флаг (бит 6): ${flagsResult.inFlashLoan ? '❌ УСТАНОВЛЕН' : '✅ НЕ УСТАНОВЛЕН'}`);
      console.log(`⚠️ Надежность метода: ${flagsResult.reliable ? 'ВЫСОКАЯ' : 'НИЗКАЯ (может быть кэш)'}`);
    }

    console.log('');

    // 2. Проверка через попытку создания flash loan
    const flashLoanResult = await this.checkFlashLoanCapability();
    
    console.log('🧪 ТЕСТ СОЗДАНИЯ FLASH LOAN (САМЫЙ НАДЕЖНЫЙ):');
    console.log('─────────────────────────────────────────────────────────────');
    console.log(`📊 Статус: ${flashLoanResult.status}`);
    console.log(`💬 Результат: ${flashLoanResult.message}`);
    console.log(`✅ Надежность: ${flashLoanResult.reliable ? 'МАКСИМАЛЬНАЯ' : 'НИЗКАЯ'}`);
    
    if (flashLoanResult.canCreateFlashLoan) {
      console.log(`📏 Размер тестовой транзакции: ${flashLoanResult.transactionSize} байт`);
    } else {
      console.log(`🔍 Тип ошибки: ${flashLoanResult.errorType}`);
      console.log(`📝 Полная ошибка: ${flashLoanResult.error}`);
    }

    console.log('');

    // 3. Сравнение результатов
    console.log('🔄 СРАВНЕНИЕ МЕТОДОВ:');
    console.log('─────────────────────────────────────────────────────────────');
    
    if (!flagsResult.error && flashLoanResult.reliable) {
      if (flagsResult.inFlashLoan && !flashLoanResult.canCreateFlashLoan) {
        console.log('✅ Оба метода согласны: флаг установлен');
      } else if (!flagsResult.inFlashLoan && flashLoanResult.canCreateFlashLoan) {
        console.log('✅ Оба метода согласны: флаг сброшен');
      } else {
        console.log('⚠️ Методы дают разные результаты - доверяем тесту flash loan');
      }
    }

    console.log('');
    console.log('🎯 ИТОГОВОЕ ЗАКЛЮЧЕНИЕ:');
    console.log('═══════════════════════════════════════════════════════════════');
    
    if (flashLoanResult.canCreateFlashLoan) {
      console.log('🎉 FLASH LOAN ГОТОВ К ИСПОЛЬЗОВАНИЮ!');
      console.log('💰 Можно выполнять арбитраж на 328.591 SOL!');
      console.log('🚀 Запускайте торгового бота!');
    } else {
      console.log('🚨 FLASH LOAN НЕ ДОСТУПЕН!');
      
      if (flashLoanResult.status === 'STUCK') {
        console.log('🛠️ РЕШЕНИЕ: Выполните node correct-marginfi-flag-reset.js');
      } else if (flashLoanResult.status === 'BANK_FULL') {
        console.log('🛠️ РЕШЕНИЕ: Подождите или используйте другой банк');
      } else {
        console.log('🛠️ РЕШЕНИЕ: Проверьте детали ошибки и документацию');
      }
    }

    return {
      status: flashLoanResult.status,
      canUseFlashLoan: flashLoanResult.canCreateFlashLoan,
      flagsCheck: flagsResult,
      flashLoanCheck: flashLoanResult
    };
  }
}

async function main() {
  const args = process.argv.slice(2);
  const detailed = args.includes('--detailed') || args.includes('-d');

  const checker = new MarginFiFlagChecker();
  
  const initialized = await checker.initialize();
  if (!initialized) {
    console.log('❌ Не удалось инициализировать проверку');
    return;
  }

  if (detailed) {
    await checker.detailedCheck();
  } else {
    await checker.quickCheck();
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = MarginFiFlagChecker;
