/**
 * 🧪 ПРОВЕРКА СТАТУСА MARGINFI СИСТЕМЫ
 * Простая проверка без запуска основной системы
 */

// Загружаем .env
require('dotenv').config();

const fs = require('fs');

async function checkMarginFiStatus() {
  console.log(`🧪 ===== ПРОВЕРКА СТАТУСА MARGINFI СИСТЕМЫ =====`);
  console.log(`⏰ Время: ${new Date().toLocaleString()}`);
  
  // 1. Проверяем .env переменные
  console.log(`\n1️⃣ ПРОВЕРКА .ENV ПЕРЕМЕННЫХ:`);
  const envVars = {
    'QUICKNODE_RPC_URL': process.env.QUICKNODE_RPC_URL,
    'HELIUS_RPC_URL': process.env.HELIUS_RPC_URL,
    'SOLANA_RPC_URL': process.env.SOLANA_RPC_URL,
    'MARGINFI_ENABLED': process.env.MARGINFI_ENABLED,
    'FLASH_LOAN_ENABLED': process.env.FLASH_LOAN_ENABLED,
    'REAL_TRADING_ENABLED': process.env.REAL_TRADING_ENABLED
  };
  
  for (const [key, value] of Object.entries(envVars)) {
    if (value) {
      console.log(`   ✅ ${key}: ${key.includes('RPC') ? value.substring(0, 50) + '...' : value}`);
    } else {
      console.log(`   ❌ ${key}: НЕ УСТАНОВЛЕН`);
    }
  }
  
  // 2. Проверяем wallet файл
  console.log(`\n2️⃣ ПРОВЕРКА WALLET ФАЙЛА:`);
  const walletPath = 'H:/Mempool/DEXSWAP/wallet.json';
  if (fs.existsSync(walletPath)) {
    try {
      const walletData = JSON.parse(fs.readFileSync(walletPath, 'utf8'));
      if (Array.isArray(walletData) && walletData.length === 64) {
        console.log(`   ✅ Wallet файл корректен (64 байта)`);
      } else {
        console.log(`   ⚠️ Wallet файл некорректен (${walletData.length} элементов)`);
      }
    } catch (e) {
      console.log(`   ❌ Ошибка чтения wallet: ${e.message}`);
    }
  } else {
    console.log(`   ❌ Wallet файл не найден: ${walletPath}`);
  }
  
  // 3. Проверяем MarginFi аккаунт файл
  console.log(`\n3️⃣ ПРОВЕРКА MARGINFI АККАУНТА:`);
  const marginfiAccountPath = 'ready-marginfi-account.json';
  if (fs.existsSync(marginfiAccountPath)) {
    try {
      const accountData = JSON.parse(fs.readFileSync(marginfiAccountPath, 'utf8'));
      console.log(`   ✅ MarginFi аккаунт файл найден`);
      console.log(`   📋 Адрес: ${accountData.address || 'НЕ УКАЗАН'}`);
      console.log(`   📋 Создан: ${accountData.createdAt || 'НЕ УКАЗАНО'}`);
    } catch (e) {
      console.log(`   ⚠️ Ошибка чтения MarginFi аккаунта: ${e.message}`);
    }
  } else {
    console.log(`   ⚠️ MarginFi аккаунт файл не найден`);
    console.log(`   💡 Система создаст его при первом запуске`);
  }
  
  // 4. Проверяем активность системы по ALT файлам
  console.log(`\n4️⃣ ПРОВЕРКА АКТИВНОСТИ СИСТЕМЫ:`);
  const altDir = './ALT';
  if (fs.existsSync(altDir)) {
    const altFiles = fs.readdirSync(altDir);
    console.log(`   📊 ALT файлов: ${altFiles.length}`);
    
    if (altFiles.length > 0) {
      // Находим самый новый файл
      const newestFile = altFiles
        .map(file => ({
          name: file,
          time: fs.statSync(`${altDir}/${file}`).mtime
        }))
        .sort((a, b) => b.time - a.time)[0];
      
      const timeDiff = Date.now() - newestFile.time.getTime();
      const minutesAgo = Math.floor(timeDiff / 60000);
      
      console.log(`   📅 Последний файл: ${minutesAgo} минут назад`);
      
      if (minutesAgo < 5) {
        console.log(`   ✅ Система АКТИВНА (файлы создаются)`);
      } else if (minutesAgo < 30) {
        console.log(`   ⚠️ Система возможно НЕАКТИВНА (${minutesAgo} минут без файлов)`);
      } else {
        console.log(`   ❌ Система НЕАКТИВНА (${minutesAgo} минут без файлов)`);
      }
    } else {
      console.log(`   ❌ ALT файлов нет - система не работает`);
    }
  } else {
    console.log(`   ❌ ALT папка не найдена`);
  }
  
  // 5. Проверяем доступность модулей
  console.log(`\n5️⃣ ПРОВЕРКА МОДУЛЕЙ:`);
  const modules = [
    '@solana/web3.js',
    '@mrgnlabs/marginfi-client-v2',
    '@mrgnlabs/mrgn-common',
    'dotenv'
  ];
  
  for (const module of modules) {
    try {
      require(module);
      console.log(`   ✅ ${module}: доступен`);
    } catch (e) {
      console.log(`   ❌ ${module}: НЕ НАЙДЕН`);
    }
  }
  
  // 6. Проверяем основные файлы системы
  console.log(`\n6️⃣ ПРОВЕРКА ФАЙЛОВ СИСТЕМЫ:`);
  const systemFiles = [
    'real-solana-rpc-websocket.js',
    'src/atomic-transaction-builder-fixed.js',
    'src/utils/strict-rpc-manager.js',
    'jupiter-swap-instructions.js'
  ];
  
  for (const file of systemFiles) {
    if (fs.existsSync(file)) {
      console.log(`   ✅ ${file}: найден`);
    } else {
      console.log(`   ❌ ${file}: НЕ НАЙДЕН`);
    }
  }
  
  // 7. Итоговая оценка
  console.log(`\n7️⃣ ИТОГОВАЯ ОЦЕНКА:`);
  
  let score = 0;
  let maxScore = 0;
  
  // Оценка .env переменных
  maxScore += 3;
  if (process.env.QUICKNODE_RPC_URL) score += 1;
  if (process.env.MARGINFI_ENABLED === 'true') score += 1;
  if (process.env.FLASH_LOAN_ENABLED === 'true') score += 1;
  
  // Оценка файлов
  maxScore += 2;
  if (fs.existsSync(walletPath)) score += 1;
  if (fs.existsSync('real-solana-rpc-websocket.js')) score += 1;
  
  // Оценка активности
  maxScore += 1;
  if (fs.existsSync(altDir) && fs.readdirSync(altDir).length > 0) score += 1;
  
  const percentage = Math.round((score / maxScore) * 100);
  
  console.log(`   📊 Готовность системы: ${score}/${maxScore} (${percentage}%)`);
  
  if (percentage >= 80) {
    console.log(`   ✅ СИСТЕМА ГОТОВА К РАБОТЕ`);
    console.log(`   🎉 MarginFi Flash Loans должны работать!`);
  } else if (percentage >= 60) {
    console.log(`   ⚠️ СИСТЕМА ЧАСТИЧНО ГОТОВА`);
    console.log(`   🔧 Требуются небольшие исправления`);
  } else {
    console.log(`   ❌ СИСТЕМА НЕ ГОТОВА`);
    console.log(`   🚨 Требуется настройка и исправления`);
  }
  
  // 8. Рекомендации
  console.log(`\n8️⃣ РЕКОМЕНДАЦИИ:`);
  
  if (!process.env.QUICKNODE_RPC_URL) {
    console.log(`   🔧 Установите QUICKNODE_RPC_URL в .env файле`);
  }
  
  if (!fs.existsSync(walletPath)) {
    console.log(`   🔧 Создайте wallet.json файл`);
  }
  
  if (process.env.MARGINFI_ENABLED !== 'true') {
    console.log(`   🔧 Установите MARGINFI_ENABLED=true в .env файле`);
  }
  
  if (!fs.existsSync(altDir) || fs.readdirSync(altDir).length === 0) {
    console.log(`   🔧 Запустите основную систему: node real-solana-rpc-websocket.js`);
  }
  
  console.log(`\n✅ ПРОВЕРКА ЗАВЕРШЕНА!`);
  
  return {
    ready: percentage >= 80,
    score: score,
    maxScore: maxScore,
    percentage: percentage
  };
}

// Запуск проверки
checkMarginFiStatus().then(result => {
  if (result.ready) {
    console.log(`\n🎯 РЕЗУЛЬТАТ: СИСТЕМА ГОТОВА (${result.percentage}%)`);
    process.exit(0);
  } else {
    console.log(`\n🎯 РЕЗУЛЬТАТ: СИСТЕМА НЕ ГОТОВА (${result.percentage}%)`);
    process.exit(1);
  }
}).catch(error => {
  console.log(`\n💥 ОШИБКА ПРОВЕРКИ: ${error.message}`);
  process.exit(1);
});
