/**
 * 🔥 ПРОВЕРКА ORACLE ДАННЫХ И СОСТОЯНИЯ MARGINFI
 * 
 * Проверяет:
 * 1. Oracle данные и их свежесть
 * 2. Health Factor аккаунта
 * 3. Реальное состояние долгов
 * 4. Готовность к flash loan
 */

const { MarginfiClient, getConfig } = require('@mrgnlabs/marginfi-client-v2');
const { Connection, PublicKey, Keypair } = require('@solana/web3.js');
const fs = require('fs');

class OracleAndDebtChecker {
    constructor() {
        this.connection = new Connection('https://billowing-empty-patron.solana-mainnet.quiknode.pro/c79212a7508ec6a37353793d9be93c917898e858/', 'confirmed');
        this.wallet = null;
        this.marginfiClient = null;
        
        // 🔥 ТВОЙ РЕАЛЬНЫЙ MARGINFI АККАУНТ
        this.MARGINFI_ACCOUNT_PK = new PublicKey('********************************************');
        
        // 🔥 ОСНОВНЫЕ БАНКИ ДЛЯ ПРОВЕРКИ
        this.BANKS = {
            SOL: new PublicKey('CCKtUs6Cgwo4aaQUmBPmyoApH2gUDErxNZCAntD6LYGh'),
            USDC: new PublicKey('2s37akK2eyBbp8DZgCm7RtsaEz8eJP3Nxd4urLHQv7yB'),
            USDT: new PublicKey('HmpMfL8942u22htC4EMiWgLX931g3sacXFR6KjuLgKLV')
        };
    }

    async initialize() {
        try {
            console.log('🔧 ИНИЦИАЛИЗАЦИЯ ПРОВЕРКИ ORACLE И ДОЛГОВ');
            console.log('═══════════════════════════════════════════════════════════════');
            
            // Загружаем wallet
            const walletData = JSON.parse(fs.readFileSync('wallet.json', 'utf8'));
            const keypair = Keypair.fromSecretKey(new Uint8Array(walletData));
            this.wallet = { payer: keypair, publicKey: keypair.publicKey };
            
            console.log(`💼 Wallet: ${this.wallet.publicKey.toString()}`);
            
            // Создаем MarginFi client
            const config = getConfig('production');
            this.marginfiClient = await MarginfiClient.fetch(config, this.wallet, this.connection);
            
            console.log('✅ MarginFi client инициализирован');
            console.log(`🏦 MarginFi аккаунт: ${this.MARGINFI_ACCOUNT_PK.toString()}`);
            
            return true;
        } catch (error) {
            console.error(`❌ Ошибка инициализации: ${error.message}`);
            return false;
        }
    }

    /**
     * 🔍 ПРОВЕРКА ORACLE ДАННЫХ
     */
    async checkOracleData() {
        try {
            console.log('\n🔍 ПРОВЕРКА ORACLE ДАННЫХ');
            console.log('═══════════════════════════════════════════════════════════════');
            
            const currentTime = Date.now() / 1000; // Текущее время в секундах
            const MAX_PRICE_AGE_SEC = 60; // Максимальный возраст oracle данных
            
            for (const [symbol, bankPk] of Object.entries(this.BANKS)) {
                try {
                    const bank = this.marginfiClient.getBankByPk(bankPk);
                    if (!bank) {
                        console.log(`❌ Банк ${symbol} не найден`);
                        continue;
                    }
                    
                    console.log(`\n📊 БАНК ${symbol}:`);
                    console.log(`   🏦 Адрес: ${bankPk.toString()}`);
                    
                    // Проверяем oracle данные
                    const oracleData = bank.config.oracleKeys[0]; // Основной oracle
                    if (oracleData) {
                        console.log(`   🔮 Oracle: ${oracleData.toString()}`);
                        
                        // Получаем данные oracle аккаунта
                        const oracleAccountInfo = await this.connection.getAccountInfo(oracleData);
                        if (oracleAccountInfo) {
                            console.log(`   📏 Oracle размер данных: ${oracleAccountInfo.data.length} bytes`);
                            console.log(`   👤 Oracle владелец: ${oracleAccountInfo.owner.toString()}`);
                        }
                    }
                    
                    // Проверяем цену и время последнего обновления
                    const price = bank.getPrice();
                    console.log(`   💰 Цена: $${price.toFixed(6)}`);
                    
                    // Проверяем свежесть данных
                    const priceAge = currentTime - bank.config.lastUpdate.toNumber();
                    const isStale = priceAge > MAX_PRICE_AGE_SEC;
                    
                    console.log(`   ⏰ Последнее обновление: ${bank.config.lastUpdate.toNumber()}`);
                    console.log(`   🕐 Возраст данных: ${priceAge.toFixed(1)} секунд`);
                    console.log(`   ${isStale ? '🚨 УСТАРЕЛИ!' : '✅ СВЕЖИЕ'} (лимит: ${MAX_PRICE_AGE_SEC}с)`);
                    
                    if (isStale) {
                        console.log(`   ⚠️ Oracle данные устарели! Это может вызывать ошибку 6009`);
                    }
                    
                } catch (bankError) {
                    console.log(`❌ Ошибка проверки банка ${symbol}: ${bankError.message}`);
                }
            }
            
            return true;
        } catch (error) {
            console.error(`❌ Ошибка проверки oracle: ${error.message}`);
            return false;
        }
    }

    /**
     * 💰 ДЕТАЛЬНАЯ ПРОВЕРКА ДОЛГОВ И HEALTH FACTOR
     */
    async checkDebtAndHealth() {
        try {
            console.log('\n💰 ДЕТАЛЬНАЯ ПРОВЕРКА ДОЛГОВ И HEALTH FACTOR');
            console.log('═══════════════════════════════════════════════════════════════');
            
            // Получаем MarginFi аккаунт
            const marginfiAccounts = await this.marginfiClient.getMarginfiAccountsForAuthority();
            const marginfiAccount = marginfiAccounts.find(acc => acc.address.equals(this.MARGINFI_ACCOUNT_PK));
            
            if (!marginfiAccount) {
                console.log('❌ MarginFi аккаунт не найден');
                return false;
            }
            
            console.log('✅ MarginFi аккаунт найден');
            
            // Проверяем все балансы
            const balances = marginfiAccount.balances;
            console.log(`📊 Всего балансов: ${balances.length}`);
            
            let totalAssets = 0;
            let totalLiabilities = 0;
            let hasActiveBalances = false;
            
            for (let i = 0; i < balances.length; i++) {
                const balance = balances[i];
                
                if (balance.active) {
                    hasActiveBalances = true;
                    const bank = this.marginfiClient.getBankByPk(balance.bankPk);
                    const symbol = bank ? bank.tokenSymbol : 'UNKNOWN';
                    
                    console.log(`\n   💼 БАЛАНС ${i + 1} (${symbol}):`);
                    console.log(`      🏦 Банк: ${balance.bankPk.toString()}`);
                    console.log(`      ✅ Активен: ${balance.active}`);
                    
                    // Получаем количества
                    const quantities = balance.computeQuantity(bank);
                    const assets = quantities.assets.toNumber() / Math.pow(10, bank.mintDecimals);
                    const liabilities = quantities.liabilities.toNumber() / Math.pow(10, bank.mintDecimals);
                    
                    console.log(`      💰 Активы: ${assets.toFixed(6)} ${symbol}`);
                    console.log(`      💸 Долги: ${liabilities.toFixed(6)} ${symbol}`);
                    
                    // Конвертируем в USD
                    const price = bank.getPrice();
                    const assetsUSD = assets * price;
                    const liabilitiesUSD = liabilities * price;
                    
                    console.log(`      💵 Активы USD: $${assetsUSD.toFixed(2)}`);
                    console.log(`      💸 Долги USD: $${liabilitiesUSD.toFixed(2)}`);
                    
                    totalAssets += assetsUSD;
                    totalLiabilities += liabilitiesUSD;
                    
                    if (liabilities > 0) {
                        console.log(`      🚨 НАЙДЕН ДОЛГ: ${liabilities.toFixed(6)} ${symbol} ($${liabilitiesUSD.toFixed(2)})`);
                    }
                }
            }
            
            if (!hasActiveBalances) {
                console.log('✅ Активных балансов нет - аккаунт пустой');
            }
            
            // Вычисляем Health Factor
            console.log(`\n📊 ИТОГОВЫЕ ПОКАЗАТЕЛИ:`);
            console.log(`   💰 Общие активы: $${totalAssets.toFixed(2)}`);
            console.log(`   💸 Общие долги: $${totalLiabilities.toFixed(2)}`);
            console.log(`   💵 Чистая стоимость: $${(totalAssets - totalLiabilities).toFixed(2)}`);
            
            const healthFactor = totalLiabilities > 0 ? totalAssets / totalLiabilities : Infinity;
            const minHealthFactor = 1.1; // 110%
            
            console.log(`   ❤️ Health Factor: ${healthFactor === Infinity ? '∞' : healthFactor.toFixed(4)}`);
            console.log(`   📏 Минимальный HF: ${minHealthFactor}`);
            
            if (healthFactor >= minHealthFactor || healthFactor === Infinity) {
                console.log(`   ✅ Health Factor в норме!`);
            } else {
                console.log(`   🚨 Health Factor слишком низкий! Это вызывает ошибку 6009`);
            }
            
            return {
                totalAssets,
                totalLiabilities,
                healthFactor,
                isHealthy: healthFactor >= minHealthFactor || healthFactor === Infinity
            };
            
        } catch (error) {
            console.error(`❌ Ошибка проверки долгов: ${error.message}`);
            return false;
        }
    }

    /**
     * 🧪 ТЕСТ ГОТОВНОСТИ К FLASH LOAN
     */
    async testFlashLoanReadiness() {
        try {
            console.log('\n🧪 ТЕСТ ГОТОВНОСТИ К FLASH LOAN');
            console.log('═══════════════════════════════════════════════════════════════');
            
            const marginfiAccounts = await this.marginfiClient.getMarginfiAccountsForAuthority();
            const marginfiAccount = marginfiAccounts.find(acc => acc.address.equals(this.MARGINFI_ACCOUNT_PK));
            
            if (!marginfiAccount) {
                console.log('❌ MarginFi аккаунт не найден');
                return false;
            }
            
            try {
                // Пытаемся создать тестовый flash loan
                const flashLoanTx = await marginfiAccount.buildFlashLoanTx({
                    ixs: [], // Пустые инструкции для теста
                    signers: []
                });

                console.log('✅ Flash loan можно создать!');
                console.log(`📏 Размер транзакции: ${flashLoanTx.serialize().length} байт`);
                console.log('🎉 Аккаунт готов к flash loan операциям!');
                
                return true;
                
            } catch (flashLoanError) {
                console.log('❌ Flash loan создать нельзя!');
                console.log(`🚨 Ошибка: ${flashLoanError.message}`);
                
                if (flashLoanError.message.includes('6009')) {
                    console.log('💡 Ошибка 6009 = RiskEngineInitRejected');
                    console.log('🔍 Причины: плохой Health Factor или устаревшие oracle данные');
                } else if (flashLoanError.message.includes('6037')) {
                    console.log('💡 Ошибка 6037 = AccountInFlashloan');
                    console.log('🔍 Аккаунт застрял в состоянии flash loan');
                }
                
                return false;
            }
            
        } catch (error) {
            console.error(`❌ Ошибка теста flash loan: ${error.message}`);
            return false;
        }
    }

    /**
     * 🔥 ПОЛНАЯ ДИАГНОСТИКА
     */
    async performFullDiagnostic() {
        try {
            console.log('🔥 ПОЛНАЯ ДИАГНОСТИКА MARGINFI И ORACLE');
            console.log('═══════════════════════════════════════════════════════════════');
            
            // 1. Проверяем oracle данные
            const oracleOk = await this.checkOracleData();
            
            // 2. Проверяем долги и health factor
            const debtResult = await this.checkDebtAndHealth();
            
            // 3. Тестируем готовность к flash loan
            const flashLoanReady = await this.testFlashLoanReadiness();
            
            // 4. Итоговый отчет
            console.log('\n🎯 ИТОГОВЫЙ ОТЧЕТ');
            console.log('═══════════════════════════════════════════════════════════════');
            
            console.log(`🔮 Oracle данные: ${oracleOk ? '✅ Проверены' : '❌ Ошибка'}`);
            console.log(`💰 Долги и Health Factor: ${debtResult ? '✅ Проверены' : '❌ Ошибка'}`);
            console.log(`🧪 Flash Loan готовность: ${flashLoanReady ? '✅ Готов' : '❌ Не готов'}`);
            
            if (debtResult && debtResult.isHealthy && flashLoanReady) {
                console.log('\n🎉 ВСЕ В ПОРЯДКЕ! МОЖНО ЗАПУСКАТЬ АРБИТРАЖ!');
                console.log('🚀 Команда: node BMeteora.js');
            } else {
                console.log('\n⚠️ ЕСТЬ ПРОБЛЕМЫ, ТРЕБУЮЩИЕ РЕШЕНИЯ:');
                
                if (debtResult && !debtResult.isHealthy) {
                    console.log('   💸 Плохой Health Factor - нужно погасить долги или добавить активы');
                }
                
                if (!flashLoanReady) {
                    console.log('   🧪 Flash Loan не готов - проверьте oracle данные и состояние аккаунта');
                }
            }
            
            return flashLoanReady;
            
        } catch (error) {
            console.error(`❌ Ошибка полной диагностики: ${error.message}`);
            return false;
        }
    }
}

async function main() {
    console.log('🔥 ПРОВЕРКА ORACLE ДАННЫХ И СОСТОЯНИЯ MARGINFI');
    console.log('═══════════════════════════════════════════════════════════════');
    console.log('🎯 Проверяем oracle данные, долги, health factor и готовность к flash loan');
    console.log('💡 Цель: найти причину ошибки 6009 (RiskEngineInitRejected)');
    console.log('═══════════════════════════════════════════════════════════════');

    const checker = new OracleAndDebtChecker();

    const initialized = await checker.initialize();
    if (!initialized) {
        console.log('❌ Не удалось инициализировать проверку');
        return;
    }

    const ready = await checker.performFullDiagnostic();

    if (ready) {
        console.log('\n✅ ДИАГНОСТИКА ЗАВЕРШЕНА - ВСЕ ГОТОВО!');
    } else {
        console.log('\n❌ ДИАГНОСТИКА ВЫЯВИЛА ПРОБЛЕМЫ');
        console.log('💡 Используйте результаты для устранения проблем');
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = OracleAndDebtChecker;
