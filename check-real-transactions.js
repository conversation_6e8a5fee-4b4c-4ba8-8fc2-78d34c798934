/**
 * 🔍 ПРОВЕРКА РЕАЛЬНЫХ ТРАНЗАКЦИЙ СИСТЕМЫ
 * ═══════════════════════════════════════════════════════════════
 * Проверяем, отправляет ли наша система реальные транзакции
 */

const { Connection, PublicKey } = require('@solana/web3.js');
require('dotenv').config({ path: '.env.solana' });

async function checkRealTransactions() {
    console.log('🔍 ПРОВЕРКА РЕАЛЬНЫХ ТРАНЗАКЦИЙ СИСТЕМЫ');
    console.log('═'.repeat(60));
    
    const walletAddress = 'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV';
    const walletPublicKey = new PublicKey(walletAddress);
    
    // Подключение к RPC
    const connection = new Connection(
        process.env.HELIUS_RPC_URL || 'https://api.mainnet-beta.solana.com',
        'confirmed'
    );
    
    try {
        console.log(`🎯 Анализируем кошелек: ${walletAddress}`);
        console.log(`📡 RPC: ${connection.rpcEndpoint}`);
        
        // Получаем последние транзакции
        console.log('\n📊 Получаем последние 20 транзакций...');
        const signatures = await connection.getSignaturesForAddress(
            walletPublicKey,
            { limit: 20 }
        );
        
        console.log(`✅ Найдено ${signatures.length} транзакций`);
        
        // Анализируем каждую транзакцию
        for (let i = 0; i < Math.min(signatures.length, 10); i++) {
            const sig = signatures[i];
            console.log(`\n🔍 ТРАНЗАКЦИЯ #${i + 1}:`);
            console.log(`   📝 Signature: ${sig.signature}`);
            console.log(`   ⏰ Время: ${new Date(sig.blockTime * 1000).toLocaleString()}`);
            console.log(`   ✅ Статус: ${sig.err ? '❌ FAILED' : '✅ SUCCESS'}`);
            
            try {
                // Получаем детали транзакции
                const txDetails = await connection.getTransaction(sig.signature, {
                    maxSupportedTransactionVersion: 0
                });
                
                if (txDetails) {
                    // Получаем инструкции в зависимости от версии транзакции
                    let instructions, accountKeys;

                    if (txDetails.version === 0) {
                        // Versioned transaction
                        instructions = txDetails.transaction.message.compiledInstructions || [];
                        accountKeys = txDetails.transaction.message.staticAccountKeys || [];
                    } else {
                        // Legacy transaction
                        instructions = txDetails.transaction.message.instructions || [];
                        accountKeys = txDetails.transaction.message.accountKeys || [];
                    }

                    console.log(`   📋 Инструкций: ${instructions.length}`);

                    // Анализируем инструкции
                    let hasJupiter = false;
                    let hasMarginFi = false;
                    let hasSystemTransfer = false;
                    let transferAmount = 0;
                    let systemTransferCount = 0;

                    for (const ix of instructions) {
                        if (!accountKeys || !accountKeys[ix.programIdIndex]) continue;

                        const programId = accountKeys[ix.programIdIndex];

                        if (programId.toString() === 'JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4') {
                            hasJupiter = true;
                        }
                        if (programId.toString() === 'MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZPxwGdHdC') {
                            hasMarginFi = true;
                        }
                        if (programId.toString() === '11111111111111111111111111111111') {
                            hasSystemTransfer = true;
                            systemTransferCount++;

                            // Пытаемся извлечь сумму из System Program transfer
                            if (ix.data && ix.data.length >= 12) {
                                try {
                                    const data = Buffer.from(ix.data);
                                    if (data.readUInt32LE(0) === 2) { // Transfer instruction
                                        const amount = data.readBigUInt64LE(4);
                                        transferAmount = Number(amount) / 1e9;
                                    }
                                } catch (e) {
                                    // Игнорируем ошибки парсинга
                                }
                            }
                        }
                    }
                    
                    // Определяем тип транзакции
                    let txType = '🔄 Неизвестная';
                    if (hasJupiter && hasMarginFi) {
                        txType = '🚀 АРБИТРАЖ (Jupiter + MarginFi)';
                    } else if (hasJupiter) {
                        txType = '🪐 Jupiter Swap';
                    } else if (hasMarginFi) {
                        txType = '🏦 MarginFi операция';
                    } else if (hasSystemTransfer) {
                        if (systemTransferCount >= 10 && transferAmount < 0.001) {
                            txType = '💸 СПАМ (множественные микротрансферы)';
                        } else if (transferAmount < 0.001) {
                            txType = '💸 СПАМ (микротрансфер)';
                        } else {
                            txType = '💰 System Transfer';
                        }
                    }

                    console.log(`   🏷️ Тип: ${txType}`);
                    if (transferAmount > 0) {
                        console.log(`   💰 Сумма: ${transferAmount.toFixed(9)} SOL`);
                    }
                    if (systemTransferCount > 1) {
                        console.log(`   🔢 System transfers: ${systemTransferCount}`);
                    }

                    // Проверяем fee payer (кто платил за транзакцию)
                    const feePayer = accountKeys && accountKeys[0] ? accountKeys[0] : null;
                    if (feePayer) {
                        const isOurTransaction = feePayer.toString() === walletAddress;
                        console.log(`   💳 Fee Payer: ${feePayer.toString()}`);
                        console.log(`   🔍 Наша транзакция: ${isOurTransaction ? '✅ ДА' : '❌ НЕТ'}`);
                    } else {
                        console.log(`   ❌ Не удалось определить Fee Payer`);
                    }
                    
                } else {
                    console.log(`   ❌ Не удалось получить детали транзакции`);
                }
                
            } catch (error) {
                console.log(`   ❌ Ошибка анализа: ${error.message}`);
            }
            
            // Небольшая пауза между запросами
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        console.log('\n🎯 ВЫВОДЫ:');
        console.log('═'.repeat(40));
        
        // Подсчитываем типы транзакций
        const ourTransactions = signatures.filter(sig => !sig.err).length;
        console.log(`📊 Всего успешных транзакций: ${ourTransactions}`);
        console.log(`🔍 Проанализировано: ${Math.min(signatures.length, 10)}`);
        
        console.log('\n💡 РЕКОМЕНДАЦИИ:');
        console.log('1. Микротранзакции (1 lamport) - это СПАМ, игнорируйте их');
        console.log('2. Ищите транзакции где Fee Payer = ваш адрес');
        console.log('3. Реальные арбитражные сделки содержат Jupiter + MarginFi инструкции');
        console.log('4. Проверьте логи вашей системы на предмет реальных отправок');
        
    } catch (error) {
        console.error('❌ Ошибка:', error.message);
    }
}

// Запуск
checkRealTransactions().catch(console.error);
