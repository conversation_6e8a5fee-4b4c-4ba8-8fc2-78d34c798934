#!/usr/bin/env node

/**
 * 🔍 ПРОВЕРКА СТАТУСА ТРАНЗАКЦИИ
 * 
 * 🎯 ЦЕЛЬ: Проверить что произошло с нашей flash arbitrage транзакцией
 */

require('dotenv').config({ path: '.env.solana' });

const { Connection } = require('@solana/web3.js');

async function checkTransactionStatus() {
    try {
        console.log('🔍 ПРОВЕРКА СТАТУСА ТРАНЗАКЦИИ...\n');
        
        const connection = new Connection(process.env.QUICKNODE2_RPC_URL);
        const signature = '2Me69xRCSjtgDvQfhHoeJm8oZAF2Ybrpmryd1qBvztcp9LHSfxDS2ZUTv979Ac9pF8ihNvkKMBdPJKXeveGNuWny';
        
        console.log(`📋 Signature: ${signature}`);
        
        // 1. Проверяем статус транзакции
        console.log('\n📊 ПРОВЕРКА СТАТУСА...');
        const status = await connection.getSignatureStatus(signature);
        
        if (status.value) {
            console.log('✅ Транзакция найдена в блокчейне');
            console.log(`📊 Slot: ${status.value.slot}`);
            console.log(`📊 Confirmations: ${status.value.confirmations}`);
            console.log(`📊 Err: ${status.value.err ? JSON.stringify(status.value.err) : 'null'}`);
            console.log(`📊 Confirmation Status: ${status.value.confirmationStatus}`);
        } else {
            console.log('❌ Транзакция НЕ найдена в блокчейне');
        }
        
        // 2. Пробуем получить детали транзакции
        console.log('\n📋 ПОЛУЧЕНИЕ ДЕТАЛЕЙ ТРАНЗАКЦИИ...');
        try {
            const transaction = await connection.getTransaction(signature, {
                maxSupportedTransactionVersion: 0
            });
            
            if (transaction) {
                console.log('✅ Детали транзакции получены');
                console.log(`📊 Slot: ${transaction.slot}`);
                console.log(`📊 Block Time: ${new Date(transaction.blockTime * 1000)}`);
                console.log(`📊 Fee: ${transaction.meta.fee} lamports`);
                console.log(`📊 Compute Units: ${transaction.meta.computeUnitsConsumed}`);
                
                if (transaction.meta.err) {
                    console.log('❌ ТРАНЗАКЦИЯ ПРОВАЛЕНА!');
                    console.log(`❌ Ошибка: ${JSON.stringify(transaction.meta.err)}`);
                    
                    // Анализируем ошибку
                    if (transaction.meta.err.InstructionError) {
                        const [instructionIndex, error] = transaction.meta.err.InstructionError;
                        console.log(`❌ Ошибка в инструкции #${instructionIndex}: ${JSON.stringify(error)}`);
                        
                        // Определяем какая инструкция провалилась
                        const instructionNames = [
                            'START FLASH LOAN',
                            'BORROW',
                            'BUY SWAP',
                            'SELL SWAP', 
                            'REPAY',
                            'END FLASH LOAN'
                        ];
                        
                        if (instructionIndex < instructionNames.length) {
                            console.log(`💥 ПРОВАЛИЛАСЬ: ${instructionNames[instructionIndex]}`);
                        }
                    }
                    
                    // Показываем логи
                    if (transaction.meta.logMessages) {
                        console.log('\n📋 ЛОГИ ТРАНЗАКЦИИ:');
                        transaction.meta.logMessages.forEach((log, index) => {
                            console.log(`   ${index}: ${log}`);
                        });
                    }
                } else {
                    console.log('✅ ТРАНЗАКЦИЯ УСПЕШНА!');
                }
            } else {
                console.log('❌ Детали транзакции не найдены');
            }
        } catch (error) {
            console.log(`❌ Ошибка получения деталей: ${error.message}`);
        }
        
        // 3. Проверяем последние транзакции аккаунта
        console.log('\n📋 ПОСЛЕДНИЕ ТРАНЗАКЦИИ АККАУНТА...');
        try {
            const signatures = await connection.getSignaturesForAddress(
                'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV',
                { limit: 5 }
            );
            
            console.log('📊 Последние 5 транзакций:');
            signatures.forEach((sig, index) => {
                console.log(`   ${index + 1}: ${sig.signature} (${sig.err ? 'FAILED' : 'SUCCESS'})`);
            });
        } catch (error) {
            console.log(`❌ Ошибка получения истории: ${error.message}`);
        }
        
    } catch (error) {
        console.error('❌ Ошибка проверки:', error.message);
    }
}

checkTransactionStatus();
