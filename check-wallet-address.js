/**
 * 🔍 ПРОВЕРКА АДРЕСА КОШЕЛЬКА И БАЛАНСА
 * 
 * Этот скрипт проверяет какой адрес генерируется из wallet.json
 * и сравнивает с ожидаемым адресом из Solscan
 */

const { Connection, Keypair, PublicKey } = require('@solana/web3.js');
const fs = require('fs');

async function checkWalletAddress() {
  console.log(`\n🔍 ДИАГНОСТИКА АДРЕСА КОШЕЛЬКА`);
  console.log(`${'='.repeat(60)}`);

  try {
    // 1. Загружаем wallet.json
    console.log(`📂 Загружаем wallet.json...`);
    const walletData = JSON.parse(fs.readFileSync('wallet.json', 'utf8'));
    console.log(`✅ Wallet.json загружен, размер: ${walletData.length} байт`);

    // 2. Создаем Keypair
    const keypair = Keypair.fromSecretKey(new Uint8Array(walletData));
    const publicKey = keypair.publicKey;
    const address = publicKey.toString();

    console.log(`\n🔑 ИНФОРМАЦИЯ О КОШЕЛЬКЕ:`);
    console.log(`   📍 Адрес из wallet.json: ${address}`);
    console.log(`   📍 Ожидаемый адрес:      bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV`);

    // 3. Сравниваем адреса
    const expectedAddress = 'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV';
    const addressMatch = address === expectedAddress;

    console.log(`\n🔍 СРАВНЕНИЕ АДРЕСОВ:`);
    console.log(`   Совпадают: ${addressMatch ? '✅ ДА' : '❌ НЕТ'}`);

    if (!addressMatch) {
      console.log(`\n🚨 КРИТИЧЕСКАЯ ПРОБЛЕМА!`);
      console.log(`   Wallet.json содержит НЕПРАВИЛЬНЫЙ приватный ключ!`);
      console.log(`   Фактический адрес:  ${address}`);
      console.log(`   Ожидаемый адрес:    ${expectedAddress}`);
      console.log(`\n💡 РЕШЕНИЕ:`);
      console.log(`   1. Найдите правильный приватный ключ для адреса ${expectedAddress}`);
      console.log(`   2. Обновите wallet.json правильным ключом`);
      console.log(`   3. Или используйте кошелек с адресом ${address}`);
    }

    // 4. Проверяем баланс ФАКТИЧЕСКОГО адреса
    console.log(`\n💰 ПРОВЕРКА БАЛАНСА ФАКТИЧЕСКОГО АДРЕСА:`);
    const connection = new Connection('https://api.mainnet-beta.solana.com', 'confirmed');
    
    try {
      const balance = await connection.getBalance(publicKey);
      const solBalance = balance / 1e9;
      
      console.log(`   📊 Баланс: ${balance} lamports`);
      console.log(`   📊 Баланс: ${solBalance.toFixed(6)} SOL`);
      console.log(`   📊 Баланс: ~$${(solBalance * 153).toFixed(2)} USD`);

      if (balance === 0) {
        console.log(`\n🚨 БАЛАНС РАВЕН НУЛЮ!`);
        console.log(`   Причины:`);
        console.log(`   1. Кошелек пустой`);
        console.log(`   2. Неправильный адрес кошелька`);
        console.log(`   3. Проблемы с RPC`);
      }

    } catch (balanceError) {
      console.log(`❌ Ошибка получения баланса: ${balanceError.message}`);
    }

    // 5. Проверяем баланс ОЖИДАЕМОГО адреса (если адреса не совпадают)
    if (!addressMatch) {
      console.log(`\n💰 ПРОВЕРКА БАЛАНСА ОЖИДАЕМОГО АДРЕСА:`);
      try {
        const expectedPublicKey = new PublicKey(expectedAddress);
        const expectedBalance = await connection.getBalance(expectedPublicKey);
        const expectedSolBalance = expectedBalance / 1e9;
        
        console.log(`   📊 Баланс ожидаемого адреса: ${expectedBalance} lamports`);
        console.log(`   📊 Баланс ожидаемого адреса: ${expectedSolBalance.toFixed(6)} SOL`);
        console.log(`   📊 Баланс ожидаемого адреса: ~$${(expectedSolBalance * 153).toFixed(2)} USD`);

        if (expectedBalance > 0) {
          console.log(`\n✅ НАЙДЕН БАЛАНС НА ОЖИДАЕМОМ АДРЕСЕ!`);
          console.log(`   Нужно использовать правильный приватный ключ для этого адреса`);
        }

      } catch (expectedBalanceError) {
        console.log(`❌ Ошибка получения баланса ожидаемого адреса: ${expectedBalanceError.message}`);
      }
    }

    // 6. Проверяем переменные окружения
    console.log(`\n🔧 ПРОВЕРКА ПЕРЕМЕННЫХ ОКРУЖЕНИЯ:`);
    const envAddress = process.env.WALLET_ADDRESS;
    console.log(`   WALLET_ADDRESS: ${envAddress || 'не установлен'}`);

    if (envAddress && envAddress !== address) {
      console.log(`   ⚠️ НЕСООТВЕТСТВИЕ: .env содержит ${envAddress}, но wallet.json генерирует ${address}`);
    }

    // 7. Итоговый отчет
    console.log(`\n🎯 ИТОГОВЫЙ ОТЧЕТ:`);
    console.log(`${'='.repeat(60)}`);
    
    if (addressMatch && balance > 0) {
      console.log(`✅ ВСЕ В ПОРЯДКЕ: Правильный адрес и есть баланс`);
    } else if (addressMatch && balance === 0) {
      console.log(`⚠️ ПРАВИЛЬНЫЙ АДРЕС, НО НЕТ БАЛАНСА`);
    } else if (!addressMatch) {
      console.log(`❌ НЕПРАВИЛЬНЫЙ АДРЕС В WALLET.JSON`);
    }

    return {
      actualAddress: address,
      expectedAddress: expectedAddress,
      addressMatch: addressMatch,
      balance: balance || 0,
      solBalance: (balance || 0) / 1e9
    };

  } catch (error) {
    console.error(`❌ Критическая ошибка: ${error.message}`);
    return null;
  }
}

// Запуск проверки
if (require.main === module) {
  checkWalletAddress()
    .then(result => {
      if (result) {
        console.log(`\n🏁 ПРОВЕРКА ЗАВЕРШЕНА`);
        process.exit(result.addressMatch && result.balance > 0 ? 0 : 1);
      } else {
        process.exit(1);
      }
    })
    .catch(error => {
      console.error(`❌ Ошибка: ${error.message}`);
      process.exit(1);
    });
}

module.exports = checkWalletAddress;
