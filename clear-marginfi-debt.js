#!/usr/bin/env node

/**
 * 🔧 СКРИПТ ДЛЯ ОБНУЛЕНИЯ ДОЛГА MARGINFI
 * 
 * Использует официальную документацию MarginFi для полного погашения долга
 * Источник: https://docs.marginfi.com/ts-sdk
 */

const { Connection } = require('@solana/web3.js');
const { MarginfiClient, getConfig } = require('@mrgnlabs/marginfi-client-v2');
const { NodeWallet } = require('@mrgnlabs/mrgn-common');
require('dotenv').config({ path: '.env.solana' });

class MarginFiDebtClearer {
  constructor() {
    this.connection = null;
    this.client = null;
    this.wallet = null;
    this.marginfiAccount = null;
  }

  async initialize() {
    console.log('🔧 ИНИЦИАЛИЗАЦИЯ MARGINFI DEBT CLEARER...');
    
    // 🔗 Создаем подключение
    const rpcUrl = 'https://sparkling-patient-market.solana-mainnet.quiknode.pro/146de9e433ca3b8f3473c0bd41742b24429dfc43/';
    this.connection = new Connection(rpcUrl, 'confirmed');
    console.log('✅ RPC подключение создано');

    // 🔑 Простая загрузка wallet
    const privateKeyBase58 = process.env.WALLET_PRIVATE_KEY;
    if (!privateKeyBase58) {
      throw new Error('❌ WALLET_PRIVATE_KEY не найден в .env.solana');
    }

    console.log(`🔑 Длина приватного ключа: ${privateKeyBase58.length} символов`);

    // Используем простое декодирование
    const { Keypair } = require('@solana/web3.js');

    // Создаем wallet напрямую из base58 строки
    let keypair;
    try {
      // Пробуем создать keypair из секретного ключа
      const secretKey = Uint8Array.from(Buffer.from(privateKeyBase58, 'base64'));
      keypair = Keypair.fromSecretKey(secretKey);
    } catch (error1) {
      try {
        // Пробуем как JSON массив
        const secretKey = Uint8Array.from(JSON.parse(privateKeyBase58));
        keypair = Keypair.fromSecretKey(secretKey);
      } catch (error2) {
        throw new Error(`❌ Не удалось декодировать приватный ключ. Попробуйте base64 или JSON формат.`);
      }
    }

    this.wallet = new NodeWallet(keypair);
    console.log(`✅ Wallet загружен: ${this.wallet.publicKey.toString()}`);

    // 🏦 Инициализируем MarginFi client
    const config = getConfig('production');
    this.client = await MarginfiClient.fetch(config, this.wallet, this.connection);
    console.log('✅ MarginFi client инициализирован');

    // 📋 Получаем существующие аккаунты
    const accounts = await this.client.getMarginfiAccountsForAuthority();
    if (accounts.length === 0) {
      throw new Error('❌ MarginFi аккаунты не найдены');
    }

    this.marginfiAccount = accounts[0];
    console.log(`✅ MarginFi аккаунт найден: ${this.marginfiAccount.address.toString()}`);
  }

  async checkDebt() {
    console.log('\n💰 ПРОВЕРКА ТЕКУЩЕГО ДОЛГА...');
    
    await this.marginfiAccount.reload();
    
    const activeBalances = this.marginfiAccount.activeBalances;
    console.log(`📊 Активных балансов: ${activeBalances.length}`);

    let totalDebtUSD = 0;
    const debts = [];

    for (const balance of activeBalances) {
      const bank = this.client.getBankByPk(balance.bankPk);
      if (!bank) continue;

      const oraclePrice = this.client.getOraclePriceByBank(bank.address);
      if (!oraclePrice) continue;

      const { assets, liabilities } = balance.computeUsdValue(bank, oraclePrice);
      
      if (liabilities.gt(0)) {
        const debtAmount = liabilities.toNumber();
        totalDebtUSD += debtAmount;
        
        debts.push({
          bank: bank,
          symbol: bank.tokenSymbol || 'UNKNOWN',
          debtUSD: debtAmount,
          balance: balance
        });

        console.log(`💸 Долг ${bank.tokenSymbol}: $${debtAmount.toFixed(2)}`);
      }
    }

    console.log(`💸 ОБЩИЙ ДОЛГ: $${totalDebtUSD.toFixed(2)}`);
    
    return { debts, totalDebtUSD };
  }

  async clearDebt(debt) {
    console.log(`\n🔧 ПОГАШЕНИЕ ДОЛГА ${debt.symbol}...`);
    
    try {
      // 🔥 ИСПОЛЬЗУЕМ ОФИЦИАЛЬНЫЙ МЕТОД ИЗ ДОКУМЕНТАЦИИ
      console.log('📋 Используем официальный метод repay с repayAll=true...');
      
      // Получаем количество долга в нативных единицах
      const debtQuantity = debt.balance.computeQuantity(debt.bank).liabilities;
      console.log(`💰 Количество долга: ${debtQuantity.toString()}`);

      // 🔥 ОФИЦИАЛЬНЫЙ СПОСОБ: repay с repayAll=true
      const signature = await this.marginfiAccount.repay(
        debtQuantity.toNumber(), // amount
        debt.bank.address,       // bankAddress  
        true                     // repayAll = true (КЛЮЧЕВОЙ ПАРАМЕТР!)
      );

      console.log(`✅ Долг ${debt.symbol} погашен!`);
      console.log(`🔗 Транзакция: ${signature}`);
      console.log(`🌐 Explorer: https://solscan.io/tx/${signature}`);

      return { success: true, signature };

    } catch (error) {
      console.error(`❌ Ошибка погашения долга ${debt.symbol}:`, error.message);
      return { success: false, error: error.message };
    }
  }

  async clearAllDebts() {
    console.log('\n🚀 НАЧИНАЕМ ПОГАШЕНИЕ ВСЕХ ДОЛГОВ...');
    
    const { debts, totalDebtUSD } = await this.checkDebt();
    
    if (debts.length === 0) {
      console.log('✅ ДОЛГОВ НЕТ! Аккаунт чистый.');
      return;
    }

    console.log(`🎯 Найдено долгов: ${debts.length}`);
    console.log(`💸 Общая сумма: $${totalDebtUSD.toFixed(2)}`);

    const results = [];

    for (const debt of debts) {
      const result = await this.clearDebt(debt);
      results.push({ debt, result });
      
      // Пауза между транзакциями
      if (debts.length > 1) {
        console.log('⏳ Пауза 3 секунды...');
        await new Promise(resolve => setTimeout(resolve, 3000));
      }
    }

    // 📊 Итоговый отчет
    console.log('\n📊 ИТОГОВЫЙ ОТЧЕТ:');
    console.log('═'.repeat(50));
    
    let successCount = 0;
    let failCount = 0;

    for (const { debt, result } of results) {
      if (result.success) {
        console.log(`✅ ${debt.symbol}: ПОГАШЕН ($${debt.debtUSD.toFixed(2)})`);
        successCount++;
      } else {
        console.log(`❌ ${debt.symbol}: ОШИБКА ($${debt.debtUSD.toFixed(2)})`);
        failCount++;
      }
    }

    console.log('═'.repeat(50));
    console.log(`🎉 Успешно погашено: ${successCount}/${debts.length}`);
    console.log(`❌ Ошибок: ${failCount}/${debts.length}`);

    if (successCount === debts.length) {
      console.log('🎉 ВСЕ ДОЛГИ ПОГАШЕНЫ! АККАУНТ ЧИСТЫЙ!');
    }
  }
}

async function main() {
  console.log('🔥 MARGINFI DEBT CLEARER - ОФИЦИАЛЬНЫЙ СПОСОБ');
  console.log('📖 Источник: https://docs.marginfi.com/ts-sdk');
  console.log('═'.repeat(60));

  const clearer = new MarginFiDebtClearer();
  
  try {
    await clearer.initialize();
    await clearer.clearAllDebts();
    
  } catch (error) {
    console.error('❌ КРИТИЧЕСКАЯ ОШИБКА:', error.message);
    process.exit(1);
  }
}

// Запуск скрипта
if (require.main === module) {
  main().catch(console.error);
}

module.exports = MarginFiDebtClearer;
