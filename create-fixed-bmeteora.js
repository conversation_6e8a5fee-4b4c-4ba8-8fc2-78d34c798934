const fs = require('fs');

console.log('🔥 СОЗДАНИЕ ИСПРАВЛЕННОГО BMeteora.js...');

// Читаем исходный файл из бэкапа
const sourceContent = fs.readFileSync('DEXSWAP1 — копия (10)/BMeteora.js', 'utf8');

// Исправляем проблемную строку
const fixedContent = sourceContent.replace(
    'const priceData = exactPrices[poolAddress];',
    '// 🔥 ИСПРАВЛЕНО: exactPrices это Map, используем get()\n                const priceData = exactPrices.get(poolAddress);'
);

// Также заменяем импорт на чистую версию
const finalContent = fixedContent.replace(
    "const MeteoraBinCacheManager = require('./meteora-bin-cache-manager.js');",
    "const MeteoraBinCacheManager = require('./meteora-bin-cache-manager-clean.js');"
);

// Записываем исправленный файл
fs.writeFileSync('BMeteora.js', finalContent);

console.log('✅ ИСПРАВЛЕННЫЙ BMeteora.js СОЗДАН!');

// Проверяем результат
const newContent = fs.readFileSync('BMeteora.js', 'utf8');
if (newContent.includes('exactPrices.get(poolAddress)') && newContent.includes('meteora-bin-cache-manager-clean.js')) {
    console.log('🎉 ВСЕ ИСПРАВЛЕНИЯ ПРИМЕНЕНЫ УСПЕШНО!');
    console.log('   ✅ exactPrices.get(poolAddress) - ИСПРАВЛЕНО');
    console.log('   ✅ meteora-bin-cache-manager-clean.js - ПОДКЛЮЧЕН');
} else {
    console.log('❌ Что-то пошло не так');
}

console.log('🚀 ГОТОВ К ТЕСТИРОВАНИЮ!');
