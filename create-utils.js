#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🚨 СОЗДАЕМ UTILITY КЛАССЫ ДЛЯ ИСПРАВЛЕНИЯ КРИТИЧЕСКИХ ОШИБОК...\n');

// Создаем папку
const utilsDir = 'src/utils/memory-management';
if (!fs.existsSync(utilsDir)) {
  fs.mkdirSync(utilsDir, { recursive: true });
  console.log(`✅ Создана папка: ${utilsDir}`);
}

// 1. BoundedMap.js
const boundedMapCode = `class BoundedMap extends Map {
  constructor(maxSize = 1000, ttl = 300000) {
    super();
    this.maxSize = maxSize;
    this.ttl = ttl;
    this.timestamps = new Map();
    this.cleanupInterval = setInterval(() => this.cleanup(), 30000);
  }
  
  set(key, value) {
    this.cleanup();
    if (this.size >= this.maxSize) {
      const firstKey = this.keys().next().value;
      this.delete(firstKey);
      this.timestamps.delete(firstKey);
    }
    this.timestamps.set(key, Date.now());
    return super.set(key, value);
  }
  
  get(key) {
    const value = super.get(key);
    if (value !== undefined) {
      this.timestamps.set(key, Date.now());
    }
    return value;
  }
  
  delete(key) {
    this.timestamps.delete(key);
    return super.delete(key);
  }
  
  cleanup() {
    const now = Date.now();
    const expiredKeys = [];
    for (const [key, timestamp] of this.timestamps) {
      if (now - timestamp > this.ttl) {
        expiredKeys.push(key);
      }
    }
    for (const key of expiredKeys) {
      this.delete(key);
    }
    if (expiredKeys.length > 0) {
      console.log(\`🧹 BoundedMap: очищено \${expiredKeys.length} expired entries\`);
    }
  }
  
  clear() {
    this.timestamps.clear();
    return super.clear();
  }
  
  destroy() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    this.clear();
  }
}

module.exports = BoundedMap;`;

fs.writeFileSync(path.join(utilsDir, 'BoundedMap.js'), boundedMapCode);
console.log('✅ Создан BoundedMap.js');

// 2. TimeoutManager.js
const timeoutManagerCode = `class TimeoutManager {
  constructor() {
    this.timeouts = new Set();
    this.intervals = new Set();
  }
  
  setTimeout(callback, delay) {
    const timeoutId = setTimeout(() => {
      this.timeouts.delete(timeoutId);
      try {
        callback();
      } catch (error) {
        console.error('Timeout callback error:', error);
      }
    }, delay);
    this.timeouts.add(timeoutId);
    return timeoutId;
  }
  
  setInterval(callback, delay) {
    const intervalId = setInterval(() => {
      try {
        callback();
      } catch (error) {
        console.error('Interval callback error:', error);
      }
    }, delay);
    this.intervals.add(intervalId);
    return intervalId;
  }
  
  clearTimeout(timeoutId) {
    clearTimeout(timeoutId);
    this.timeouts.delete(timeoutId);
  }
  
  clearInterval(intervalId) {
    clearInterval(intervalId);
    this.intervals.delete(intervalId);
  }
  
  clearAll() {
    console.log(\`🧹 TimeoutManager: очищаем \${this.timeouts.size} timeouts и \${this.intervals.size} intervals\`);
    for (const timeoutId of this.timeouts) {
      clearTimeout(timeoutId);
    }
    for (const intervalId of this.intervals) {
      clearInterval(intervalId);
    }
    this.timeouts.clear();
    this.intervals.clear();
  }
  
  getStats() {
    return {
      activeTimeouts: this.timeouts.size,
      activeIntervals: this.intervals.size
    };
  }
}

module.exports = TimeoutManager;`;

fs.writeFileSync(path.join(utilsDir, 'TimeoutManager.js'), timeoutManagerCode);
console.log('✅ Создан TimeoutManager.js');

// 3. AsyncMutex.js
const asyncMutexCode = `class AsyncMutex {
  constructor() {
    this.locked = false;
    this.queue = [];
  }
  
  async acquire() {
    return new Promise(resolve => {
      if (!this.locked) {
        this.locked = true;
        resolve();
      } else {
        this.queue.push(resolve);
      }
    });
  }
  
  release() {
    if (this.queue.length > 0) {
      const next = this.queue.shift();
      next();
    } else {
      this.locked = false;
    }
  }
  
  async withLock(fn) {
    await this.acquire();
    try {
      return await fn();
    } finally {
      this.release();
    }
  }
  
  isLocked() {
    return this.locked;
  }
  
  getQueueLength() {
    return this.queue.length;
  }
}

module.exports = AsyncMutex;`;

fs.writeFileSync(path.join(utilsDir, 'AsyncMutex.js'), asyncMutexCode);
console.log('✅ Создан AsyncMutex.js');

// 4. ResourceManager.js
const resourceManagerCode = `class ResourceManager {
  constructor() {
    this.resources = new Set();
    this.cleanupCallbacks = new Set();
    this.isCleaningUp = false;
  }
  
  register(resource, cleanupFn) {
    this.resources.add(resource);
    if (cleanupFn && typeof cleanupFn === 'function') {
      this.cleanupCallbacks.add(cleanupFn);
    }
  }
  
  unregister(resource) {
    this.resources.delete(resource);
  }
  
  addCleanupCallback(cleanupFn) {
    if (typeof cleanupFn === 'function') {
      this.cleanupCallbacks.add(cleanupFn);
    }
  }
  
  async cleanup() {
    if (this.isCleaningUp) {
      console.log('🔄 ResourceManager: cleanup уже выполняется...');
      return;
    }
    
    this.isCleaningUp = true;
    console.log(\`🧹 ResourceManager: начинаем cleanup \${this.cleanupCallbacks.size} callbacks и \${this.resources.size} ресурсов\`);
    
    for (const cleanupFn of this.cleanupCallbacks) {
      try {
        await cleanupFn();
      } catch (error) {
        console.error('Cleanup callback error:', error);
      }
    }
    
    for (const resource of this.resources) {
      if (resource && typeof resource.cleanup === 'function') {
        try {
          await resource.cleanup();
        } catch (error) {
          console.error('Resource cleanup error:', error);
        }
      }
      
      if (resource && typeof resource.destroy === 'function') {
        try {
          await resource.destroy();
        } catch (error) {
          console.error('Resource destroy error:', error);
        }
      }
      
      if (resource && typeof resource.close === 'function') {
        try {
          await resource.close();
        } catch (error) {
          console.error('Resource close error:', error);
        }
      }
    }
    
    this.resources.clear();
    this.cleanupCallbacks.clear();
    this.isCleaningUp = false;
    
    console.log('✅ ResourceManager: cleanup завершен');
  }
  
  getStats() {
    return {
      resources: this.resources.size,
      cleanupCallbacks: this.cleanupCallbacks.size,
      isCleaningUp: this.isCleaningUp
    };
  }
}

module.exports = ResourceManager;`;

fs.writeFileSync(path.join(utilsDir, 'ResourceManager.js'), resourceManagerCode);
console.log('✅ Создан ResourceManager.js');

// 5. index.js
const indexCode = `module.exports = {
  BoundedMap: require('./BoundedMap'),
  TimeoutManager: require('./TimeoutManager'),
  AsyncMutex: require('./AsyncMutex'),
  ResourceManager: require('./ResourceManager')
};`;

fs.writeFileSync(path.join(utilsDir, 'index.js'), indexCode);
console.log('✅ Создан index.js');

console.log('\n🎉 ВСЕ UTILITY КЛАССЫ СОЗДАНЫ УСПЕШНО!');
console.log('\n📋 СЛЕДУЮЩИЕ ШАГИ:');
console.log('1. Добавить импорты в главные файлы');
console.log('2. Заменить Map на BoundedMap');
console.log('3. Использовать TimeoutManager для timeout/interval');
console.log('4. Добавить AsyncMutex для критических секций');
console.log('5. Использовать ResourceManager для cleanup');
console.log('\n🚨 ЭТИ ИЗМЕНЕНИЯ КРИТИЧЕСКИ ВАЖНЫ ДЛЯ СТАБИЛЬНОСТИ!');
