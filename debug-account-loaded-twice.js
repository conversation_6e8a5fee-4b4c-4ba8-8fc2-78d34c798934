#!/usr/bin/env node

/**
 * 🔍 ДИАГНОСТИКА "AccountLoadedTwice" - ПОИСК ДУБЛИРУЮЩИХСЯ АККАУНТОВ
 * Находим и исправляем дублирующиеся аккаунты в ALT таблицах
 */

const fs = require('fs');

async function debugAccountLoadedTwice() {
    console.log('🔍 ДИАГНОСТИКА "AccountLoadedTwice" - ПОИСК ДУБЛИРУЮЩИХСЯ АККАУНТОВ');
    console.log('=' .repeat(80));

    try {
        // 1. Загружаем все ALT файлы
        console.log('📁 ЗАГРУЗКА ALT ФАЙЛОВ...');
        
        const meteoraFile = './meteora-alt-cache.json';
        const marginfiFile = './marginfi-alt-cache.json';
        
        if (!fs.existsSync(meteoraFile)) {
            throw new Error(`❌ Meteora кэш не найден: ${meteoraFile}`);
        }
        
        if (!fs.existsSync(marginfiFile)) {
            throw new Error(`❌ MarginFi кэш не найден: ${marginfiFile}`);
        }
        
        const meteoraCache = JSON.parse(fs.readFileSync(meteoraFile, 'utf8'));
        const marginfiCache = JSON.parse(fs.readFileSync(marginfiFile, 'utf8'));
        
        console.log(`✅ Meteora кэш: ${meteoraCache.totalTables} таблиц`);
        console.log(`✅ MarginFi кэш: ${marginfiCache.totalTables} таблиц`);

        // 2. Собираем все аккаунты из всех ALT таблиц
        console.log('\n🔍 СБОР ВСЕХ АККАУНТОВ ИЗ ALT ТАБЛИЦ...');
        
        const allAccounts = [];
        const accountSources = new Map(); // аккаунт -> источники
        
        // Meteora аккаунты
        meteoraCache.validationResults.forEach((validation, index) => {
            if (validation.valid && validation.accounts) {
                console.log(`📊 Meteora ALT ${index + 1}: ${validation.accounts.length} аккаунтов`);
                
                validation.accounts.forEach(account => {
                    allAccounts.push(account);
                    
                    if (!accountSources.has(account)) {
                        accountSources.set(account, []);
                    }
                    accountSources.get(account).push(`Meteora ALT ${index + 1} (${validation.address.slice(0, 8)}...)`);
                });
            }
        });
        
        // MarginFi аккаунты
        marginfiCache.validationResults.forEach((validation, index) => {
            if (validation.valid && validation.accounts) {
                console.log(`📊 MarginFi ALT ${index + 1}: ${validation.accounts.length} аккаунтов`);
                
                validation.accounts.forEach(account => {
                    allAccounts.push(account);
                    
                    if (!accountSources.has(account)) {
                        accountSources.set(account, []);
                    }
                    accountSources.get(account).push(`MarginFi ALT ${index + 1} (${validation.address.slice(0, 8)}...)`);
                });
            }
        });
        
        console.log(`\n📊 ОБЩАЯ СТАТИСТИКА:`);
        console.log(`   Всего аккаунтов: ${allAccounts.length}`);
        console.log(`   Уникальных аккаунтов: ${accountSources.size}`);
        console.log(`   Дублирований: ${allAccounts.length - accountSources.size}`);

        // 3. Находим дублирующиеся аккаунты
        console.log('\n🔍 ПОИСК ДУБЛИРУЮЩИХСЯ АККАУНТОВ...');
        
        const duplicates = [];
        const duplicateDetails = new Map();
        
        accountSources.forEach((sources, account) => {
            if (sources.length > 1) {
                duplicates.push(account);
                duplicateDetails.set(account, sources);
            }
        });
        
        console.log(`❌ Найдено дублирующихся аккаунтов: ${duplicates.length}`);

        // 4. Показываем детали дублирований
        if (duplicates.length > 0) {
            console.log('\n❌ ДЕТАЛИ ДУБЛИРУЮЩИХСЯ АККАУНТОВ:');
            
            duplicates.slice(0, 20).forEach((account, index) => {
                const sources = duplicateDetails.get(account);
                console.log(`\n${index + 1}. ${account.slice(0, 8)}...${account.slice(-8)}`);
                console.log(`   Полный адрес: ${account}`);
                console.log(`   Встречается в ${sources.length} ALT таблицах:`);
                sources.forEach((source, idx) => {
                    console.log(`      ${idx + 1}. ${source}`);
                });
            });
            
            if (duplicates.length > 20) {
                console.log(`\n... и еще ${duplicates.length - 20} дублирующихся аккаунтов`);
            }
        }

        // 5. Анализируем пересечения между Meteora и MarginFi
        console.log('\n🔍 АНАЛИЗ ПЕРЕСЕЧЕНИЙ METEORA ↔ MARGINFI...');
        
        const meteoraAccounts = new Set();
        const marginfiAccounts = new Set();
        
        meteoraCache.validationResults.forEach(validation => {
            if (validation.valid && validation.accounts) {
                validation.accounts.forEach(account => meteoraAccounts.add(account));
            }
        });
        
        marginfiCache.validationResults.forEach(validation => {
            if (validation.valid && validation.accounts) {
                validation.accounts.forEach(account => marginfiAccounts.add(account));
            }
        });
        
        const intersection = [];
        meteoraAccounts.forEach(account => {
            if (marginfiAccounts.has(account)) {
                intersection.push(account);
            }
        });
        
        console.log(`📊 ПЕРЕСЕЧЕНИЯ METEORA ↔ MARGINFI:`);
        console.log(`   Meteora аккаунтов: ${meteoraAccounts.size}`);
        console.log(`   MarginFi аккаунтов: ${marginfiAccounts.size}`);
        console.log(`   Общих аккаунтов: ${intersection.length}`);
        
        if (intersection.length > 0) {
            console.log(`\n🔍 ОБЩИЕ АККАУНТЫ METEORA ↔ MARGINFI:`);
            intersection.slice(0, 10).forEach((account, index) => {
                console.log(`   ${index + 1}. ${account.slice(0, 8)}...${account.slice(-8)} (${account})`);
            });
            if (intersection.length > 10) {
                console.log(`   ... и еще ${intersection.length - 10} общих аккаунтов`);
            }
        }

        // 6. Проверяем дублирования внутри каждой ALT таблицы
        console.log('\n🔍 ПРОВЕРКА ДУБЛИРОВАНИЙ ВНУТРИ КАЖДОЙ ALT ТАБЛИЦЫ...');
        
        let internalDuplicatesFound = false;
        
        // Meteora ALT
        meteoraCache.validationResults.forEach((validation, index) => {
            if (validation.valid && validation.accounts) {
                const accountSet = new Set();
                const internalDuplicates = [];
                
                validation.accounts.forEach(account => {
                    if (accountSet.has(account)) {
                        internalDuplicates.push(account);
                    } else {
                        accountSet.add(account);
                    }
                });
                
                if (internalDuplicates.length > 0) {
                    console.log(`❌ Meteora ALT ${index + 1}: ${internalDuplicates.length} внутренних дублирований`);
                    internalDuplicates.slice(0, 5).forEach(account => {
                        console.log(`      ${account.slice(0, 8)}...${account.slice(-8)}`);
                    });
                    internalDuplicatesFound = true;
                } else {
                    console.log(`✅ Meteora ALT ${index + 1}: нет внутренних дублирований`);
                }
            }
        });
        
        // MarginFi ALT
        marginfiCache.validationResults.forEach((validation, index) => {
            if (validation.valid && validation.accounts) {
                const accountSet = new Set();
                const internalDuplicates = [];
                
                validation.accounts.forEach(account => {
                    if (accountSet.has(account)) {
                        internalDuplicates.push(account);
                    } else {
                        accountSet.add(account);
                    }
                });
                
                if (internalDuplicates.length > 0) {
                    console.log(`❌ MarginFi ALT ${index + 1}: ${internalDuplicates.length} внутренних дублирований`);
                    internalDuplicates.slice(0, 5).forEach(account => {
                        console.log(`      ${account.slice(0, 8)}...${account.slice(-8)}`);
                    });
                    internalDuplicatesFound = true;
                } else {
                    console.log(`✅ MarginFi ALT ${index + 1}: нет внутренних дублирований`);
                }
            }
        });

        // 7. Диагноз и рекомендации
        console.log('\n🎯 ДИАГНОЗ И РЕКОМЕНДАЦИИ:');
        console.log('-' .repeat(50));
        
        if (duplicates.length === 0 && !internalDuplicatesFound) {
            console.log('✅ ДУБЛИРУЮЩИХСЯ АККАУНТОВ НЕ НАЙДЕНО В ALT ТАБЛИЦАХ');
            console.log('💡 Проблема "AccountLoadedTwice" может быть в:');
            console.log('   1. Дублировании аккаунтов между ALT и обычными аккаунтами транзакции');
            console.log('   2. Дублировании в самих инструкциях MarginFi/Meteora');
            console.log('   3. Неправильном использовании ALT таблиц в compileToV0Message()');
        } else {
            console.log(`❌ НАЙДЕНЫ ДУБЛИРУЮЩИЕСЯ АККАУНТЫ: ${duplicates.length}`);
            console.log('💡 РЕШЕНИЯ:');
            
            if (intersection.length > 0) {
                console.log(`   1. КРИТИЧНО: Удалить ${intersection.length} общих аккаунтов из одной из систем`);
                console.log('   2. Рекомендация: Оставить общие аккаунты только в MarginFi ALT');
                console.log('   3. Удалить общие аккаунты из Meteora ALT таблиц');
            }
            
            if (internalDuplicatesFound) {
                console.log('   4. Удалить внутренние дублирования в каждой ALT таблице');
            }
            
            console.log('   5. Пересоздать ALT таблицы без дублирований');
            console.log('   6. Использовать только уникальные аккаунты в каждой ALT таблице');
        }

        // 8. Создаем исправленные ALT таблицы
        if (duplicates.length > 0) {
            console.log('\n🔧 СОЗДАНИЕ ИСПРАВЛЕННЫХ ALT ТАБЛИЦ...');
            
            // Стратегия: оставляем общие аккаунты только в MarginFi ALT
            const cleanedMeteora = JSON.parse(JSON.stringify(meteoraCache));
            
            cleanedMeteora.validationResults.forEach((validation, index) => {
                if (validation.valid && validation.accounts) {
                    // Удаляем аккаунты, которые есть в MarginFi
                    const cleanedAccounts = validation.accounts.filter(account => 
                        !marginfiAccounts.has(account)
                    );
                    
                    // Удаляем внутренние дублирования
                    const uniqueAccounts = [...new Set(cleanedAccounts)];
                    
                    validation.accounts = uniqueAccounts;
                    validation.accountCount = uniqueAccounts.length;
                    validation.cleaned = true;
                    validation.cleanedTimestamp = new Date().toISOString();
                    validation.removedDuplicates = validation.accounts.length - uniqueAccounts.length;
                    
                    console.log(`✅ Meteora ALT ${index + 1}: ${validation.accounts.length} → ${uniqueAccounts.length} аккаунтов`);
                }
            });
            
            // Обновляем общую статистику
            cleanedMeteora.totalAccounts = cleanedMeteora.validationResults.reduce((sum, result) => {
                return sum + (result.valid ? result.accounts.length : 0);
            }, 0);
            
            // Сохраняем исправленный Meteora кэш
            const backupFile = './meteora-alt-cache-before-dedup.json';
            fs.writeFileSync(backupFile, JSON.stringify(meteoraCache, null, 2));
            console.log(`💾 Бэкап Meteora сохранен: ${backupFile}`);
            
            fs.writeFileSync(meteoraFile, JSON.stringify(cleanedMeteora, null, 2));
            console.log(`✅ Исправленный Meteora кэш сохранен: ${meteoraFile}`);
            
            // Очищаем MarginFi от внутренних дублирований
            const cleanedMarginFi = JSON.parse(JSON.stringify(marginfiCache));
            
            cleanedMarginFi.validationResults.forEach((validation, index) => {
                if (validation.valid && validation.accounts) {
                    // Удаляем внутренние дублирования
                    const uniqueAccounts = [...new Set(validation.accounts)];
                    
                    if (uniqueAccounts.length !== validation.accounts.length) {
                        validation.accounts = uniqueAccounts;
                        validation.accountCount = uniqueAccounts.length;
                        validation.cleaned = true;
                        validation.cleanedTimestamp = new Date().toISOString();
                        validation.removedDuplicates = validation.accounts.length - uniqueAccounts.length;
                        
                        console.log(`✅ MarginFi ALT ${index + 1}: удалены внутренние дублирования`);
                    }
                }
            });
            
            // Сохраняем исправленный MarginFi кэш
            const marginfiBackupFile = './marginfi-alt-cache-before-dedup.json';
            fs.writeFileSync(marginfiBackupFile, JSON.stringify(marginfiCache, null, 2));
            console.log(`💾 Бэкап MarginFi сохранен: ${marginfiBackupFile}`);
            
            fs.writeFileSync(marginfiFile, JSON.stringify(cleanedMarginFi, null, 2));
            console.log(`✅ Исправленный MarginFi кэш сохранен: ${marginfiFile}`);
        }

        // 9. Итоговый результат
        console.log('\n🎯 ИТОГОВЫЙ РЕЗУЛЬТАТ:');
        console.log('=' .repeat(50));
        
        if (duplicates.length === 0) {
            console.log('✅ ALT ТАБЛИЦЫ ЧИСТЫЕ - НЕТ ДУБЛИРОВАНИЙ');
            console.log('💡 Проблема "AccountLoadedTwice" в другом месте');
        } else {
            console.log(`🔧 ИСПРАВЛЕНЫ ДУБЛИРОВАНИЯ: ${duplicates.length} аккаунтов`);
            console.log('✅ ALT таблицы очищены от дублирований');
            console.log('🚀 Попробуйте запустить транзакцию снова');
        }
        
        console.log(`📊 Общих аккаунтов Meteora ↔ MarginFi: ${intersection.length}`);
        console.log(`📊 Внутренних дублирований: ${internalDuplicatesFound ? 'НАЙДЕНЫ И ИСПРАВЛЕНЫ' : 'НЕ НАЙДЕНО'}`);

    } catch (error) {
        console.error('❌ Ошибка диагностики:', error.message);
        console.error(error.stack);
    }
}

// Запуск диагностики
if (require.main === module) {
    debugAccountLoadedTwice().catch(console.error);
}

module.exports = { debugAccountLoadedTwice };
