/**
 * 🔍 ОТЛАДКА ADD LIQUIDITY ИНСТРУКЦИИ
 * Проверяем почему инструкция 5 (ADD Liquidity Pool 1) дает ошибку 0x66
 */

const { PublicKey } = require('@solana/web3.js');

function debugAddLiquidityError() {
    console.log('🔍 ОТЛАДКА ОШИБКИ 0x66 В ADD LIQUIDITY POOL 1...');
    
    console.log('\n📊 АНАЛИЗ ОШИБКИ:');
    console.log('   Ошибка: 0x66 (102 в decimal)');
    console.log('   Инструкция: 5 (ADD Liquidity Pool 1)');
    console.log('   Программа: Meteora DLMM');
    
    console.log('\n🔍 ВОЗМОЖНЫЕ ПРИЧИНЫ ОШИБКИ 0x66:');
    console.log('   1. ❌ Недостаточный баланс токенов в ATA');
    console.log('   2. ❌ Неправильные аккаунты резервов пула');
    console.log('   3. ❌ Устаревшие bin arrays в кэше');
    console.log('   4. ❌ Неправильная позиция или bin ID');
    console.log('   5. ❌ Проблемы с разрешениями токенов');
    
    console.log('\n📋 ПОРЯДОК ИНСТРУКЦИЙ (ПРАВИЛЬНЫЙ):');
    console.log('   0: START Flash Loan');
    console.log('   1: CREATE USDC ATA ✅');
    console.log('   2: CREATE SOL ATA ✅');
    console.log('   3: BORROW 2,500,000 USDC ✅ (токены появляются здесь)');
    console.log('   4: BORROW 8,000 WSOL ✅ (токены появляются здесь)');
    console.log('   5: ADD Liquidity Pool 1 ❌ (ОШИБКА 0x66 ЗДЕСЬ!)');
    
    console.log('\n🔍 ПРОВЕРКА ЛОГИКИ ADD LIQUIDITY:');
    console.log('   - Порядок правильный: BORROW → ADD Liquidity ✅');
    console.log('   - Токены должны быть доступны в ATA ✅');
    console.log('   - Суммы исправлены на минимальные ✅');
    
    console.log('\n🎯 НАИБОЛЕЕ ВЕРОЯТНЫЕ ПРИЧИНЫ:');
    console.log('   1. 🔥 УСТАРЕВШИЕ BIN ARRAYS в кэше');
    console.log('   2. 🔥 НЕПРАВИЛЬНЫЕ РЕЗЕРВЫ Pool 1');
    console.log('   3. 🔥 НЕПРАВИЛЬНАЯ ПОЗИЦИЯ для Pool 1');
    
    console.log('\n📝 РЕКОМЕНДАЦИИ ДЛЯ ИСПРАВЛЕНИЯ:');
    console.log('   1. Обновить кэш bin arrays для Pool 1');
    console.log('   2. Проверить правильность резервов Pool 1');
    console.log('   3. Проверить создание позиции для Pool 1');
    console.log('   4. Добавить детальное логирование аккаунтов');
    
    return {
        errorCode: '0x66',
        instruction: 5,
        name: 'ADD Liquidity Pool 1',
        likelyCauses: [
            'Устаревшие bin arrays',
            'Неправильные резервы пула',
            'Неправильная позиция'
        ]
    };
}

function suggestFixes() {
    console.log('\n🔧 ПРЕДЛАГАЕМЫЕ ИСПРАВЛЕНИЯ:');
    
    console.log('\n1. 🔄 ОБНОВЛЕНИЕ КЭША BIN ARRAYS:');
    console.log('   - Принудительно обновить кэш перед ADD Liquidity');
    console.log('   - Проверить актуальность bin arrays в блокчейне');
    console.log('   - Использовать свежие данные из RPC');
    
    console.log('\n2. 🏦 ПРОВЕРКА РЕЗЕРВОВ POOL 1:');
    console.log('   - Убедиться что резервы Pool 1 правильные');
    console.log('   - Проверить что Pool 1 существует и активен');
    console.log('   - Сравнить с рабочими транзакциями');
    
    console.log('\n3. 📍 ПРОВЕРКА ПОЗИЦИИ:');
    console.log('   - Убедиться что позиция создается правильно');
    console.log('   - Проверить bin ID диапазоны');
    console.log('   - Использовать правильные стратегии');
    
    console.log('\n4. 🔍 ДЕТАЛЬНОЕ ЛОГИРОВАНИЕ:');
    console.log('   - Добавить логи всех аккаунтов в инструкции');
    console.log('   - Проверить балансы ATA перед ADD Liquidity');
    console.log('   - Логировать все параметры инструкции');
    
    console.log('\n🎯 ПРИОРИТЕТНОЕ ИСПРАВЛЕНИЕ:');
    console.log('   Начать с обновления кэша bin arrays - это наиболее');
    console.log('   частая причина ошибок в Meteora DLMM операциях.');
}

function analyzePool1Specifics() {
    console.log('\n🏊 АНАЛИЗ СПЕЦИФИКИ POOL 1:');
    
    console.log('\n📊 Pool 1 Параметры:');
    console.log('   - Тип: USDC/SOL пара');
    console.log('   - Ликвидность: 8,000 WSOL (односторонняя)');
    console.log('   - Стратегия: SpotOneSide');
    console.log('   - Сумма: 1 lamport (минимальная)');
    
    console.log('\n🔍 Возможные проблемы Pool 1:');
    console.log('   1. Pool 1 может быть неактивным');
    console.log('   2. Bin arrays могли измениться');
    console.log('   3. Резервы могли быть обновлены');
    console.log('   4. Позиция может быть неправильной');
    
    console.log('\n💡 Решение для Pool 1:');
    console.log('   - Получить свежие данные Pool 1 из блокчейна');
    console.log('   - Обновить все кэшированные данные');
    console.log('   - Проверить что Pool 1 поддерживает нашу стратегию');
}

// Запускаем отладку
if (require.main === module) {
    const result = debugAddLiquidityError();
    suggestFixes();
    analyzePool1Specifics();
    
    console.log('\n🏁 ИТОГ ОТЛАДКИ:');
    console.log(`   Ошибка: ${result.errorCode} в инструкции ${result.instruction}`);
    console.log(`   Операция: ${result.name}`);
    console.log(`   Вероятные причины: ${result.likelyCauses.join(', ')}`);
    console.log('\n🎯 СЛЕДУЮЩИЙ ШАГ: Обновить кэш bin arrays для Pool 1');
}

module.exports = { debugAddLiquidityError, suggestFixes, analyzePool1Specifics };
