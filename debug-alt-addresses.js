/**
 * 🔍 ДЕТАЛЬНЫЙ АНАЛИЗ ALT ТАБЛИЦ VS ИНСТРУКЦИЙ
 * Сравниваем каждый адрес в ALT таблицах с адресами в инструкциях
 */

const { Connection, PublicKey } = require('@solana/web3.js');
require('dotenv').config({ path: '.env.solana' });

async function analyzeALTvsInstructions() {
    console.log('🔍 ДЕТАЛЬНЫЙ АНАЛИЗ ALT ТАБЛИЦ VS ИНСТРУКЦИЙ');
    console.log('=' .repeat(80));

    try {
        // 1. Подключение к RPC
        const connection = new Connection(process.env.QUICKNODE_RPC_URL);
        console.log('✅ Подключение к RPC установлено');

        // 2. Все ALT адреса (7 таблиц)
        const altAddresses = [
            'HGmknUTUmeovMc9ryERNWG6UFZDFDVr9xrum3ZhyL4fC', // MarginFi 1
            '5FuKF7C1tJji2mXZuJ14U9oDb37is5mmvYLf4KwojoF1', // MarginFi 2
            'FEFhAFKz48P3w82Ds5VhvyEDwhRqu2FejmnuxEPZ8wNR', // MarginFi 3
            'FAeyUf4AdG7vTQ2Q89r9hWBkPdYkykJKG4CxgqArBGXe', // Custom
            '9A2DNR6RPNLYcYS8g5M4V12ytLWmGg8g4tMoeL1xJUW8', // Jupiter 1
            '5cFsmTCEfmvpBUBHqsWZnf9n5vTWLYH2LT8X7HdShwxP', // Jupiter 2
            '3eqTbgbMhbAQXEYLaw5BSgpJmNYJ7gjtyiukpTmeSAaX'  // Jupiter 3
        ];

        // 3. Загружаем все ALT таблицы и собираем все адреса
        console.log('\n🔍 ЗАГРУЗКА ВСЕХ ALT ТАБЛИЦ...');
        const allALTAddresses = new Set();
        const altDetails = {};

        for (let i = 0; i < altAddresses.length; i++) {
            const altAddress = altAddresses[i];
            const altName = ['MarginFi1', 'MarginFi2', 'MarginFi3', 'Custom', 'Jupiter1', 'Jupiter2', 'Jupiter3'][i];
            
            try {
                console.log(`\n📋 Загрузка ${altName}: ${altAddress}`);
                const altAccount = await connection.getAddressLookupTable(new PublicKey(altAddress));
                
                if (altAccount && altAccount.value) {
                    const addresses = altAccount.value.state.addresses;
                    console.log(`✅ ${altName}: ${addresses.length} адресов`);
                    
                    // Добавляем все адреса в общий набор
                    addresses.forEach(addr => allALTAddresses.add(addr.toString()));
                    
                    // Сохраняем детали
                    altDetails[altName] = {
                        address: altAddress,
                        count: addresses.length,
                        addresses: addresses.map(addr => addr.toString())
                    };
                    
                    // Показываем первые 10 адресов
                    console.log(`   Первые 10 адресов:`);
                    for (let j = 0; j < Math.min(10, addresses.length); j++) {
                        console.log(`      ${j + 1}: ${addresses[j].toString()}`);
                    }
                } else {
                    console.log(`❌ ${altName}: не найдена`);
                }
            } catch (error) {
                console.log(`❌ Ошибка загрузки ${altName}: ${error.message}`);
            }
        }

        console.log(`\n📊 ИТОГО УНИКАЛЬНЫХ АДРЕСОВ В ALT ТАБЛИЦАХ: ${allALTAddresses.size}`);

        // 4. Анализируем типичные адреса из Meteora DLMM swap
        console.log('\n🎯 АНАЛИЗ ТИПИЧНЫХ METEORA DLMM АДРЕСОВ...');
        
        const meteoraAddresses = [
            // Meteora DLMM Program
            'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo',
            
            // Пулы
            'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y', // Pool 1
            '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6', // Pool 2
            
            // Токены
            'So11111111111111111111111111111111111111112', // SOL
            'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // USDC
            
            // Системные программы
            'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA', // Token Program
            '11111111111111111111111111111111', // System Program
            'ComputeBudget111111111111111111111111111111', // Compute Budget
            
            // MarginFi
            'MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA', // MarginFi Program
        ];

        console.log('\n🔍 ПРОВЕРКА METEORA АДРЕСОВ В ALT ТАБЛИЦАХ:');
        let foundCount = 0;
        
        for (const addr of meteoraAddresses) {
            const found = allALTAddresses.has(addr);
            console.log(`   ${found ? '✅' : '❌'} ${addr} - ${found ? 'НАЙДЕН' : 'НЕ НАЙДЕН'}`);
            if (found) foundCount++;
        }
        
        console.log(`\n📊 НАЙДЕНО В ALT: ${foundCount}/${meteoraAddresses.length} адресов`);

        // 5. Поиск специфичных Meteora адресов в Jupiter ALT
        console.log('\n🪐 АНАЛИЗ JUPITER ALT ТАБЛИЦ НА METEORA АДРЕСА...');
        
        const jupiterALTs = ['Jupiter1', 'Jupiter2', 'Jupiter3'];
        for (const jupiterName of jupiterALTs) {
            if (altDetails[jupiterName]) {
                console.log(`\n📋 ${jupiterName} (${altDetails[jupiterName].count} адресов):`);
                
                // Ищем Meteora-специфичные адреса
                const meteoraFound = [];
                for (const addr of altDetails[jupiterName].addresses) {
                    if (meteoraAddresses.includes(addr)) {
                        meteoraFound.push(addr);
                    }
                }
                
                if (meteoraFound.length > 0) {
                    console.log(`   🎯 Найдено Meteora адресов: ${meteoraFound.length}`);
                    meteoraFound.forEach(addr => console.log(`      ✅ ${addr}`));
                } else {
                    console.log(`   ⚠️ Meteora адресов не найдено`);
                }
                
                // Показываем случайные адреса для анализа
                console.log(`   📋 Случайные адреса из таблицы:`);
                const randomAddresses = altDetails[jupiterName].addresses.slice(0, 5);
                randomAddresses.forEach((addr, i) => console.log(`      ${i + 1}: ${addr}`));
            }
        }

        // 6. Выводы
        console.log('\n' + '=' .repeat(80));
        console.log('🎯 ВЫВОДЫ:');
        console.log(`📊 Всего адресов в ALT таблицах: ${allALTAddresses.size}`);
        console.log(`🎯 Meteora адресов найдено: ${foundCount}/${meteoraAddresses.length}`);
        
        if (foundCount < meteoraAddresses.length) {
            console.log('🚨 ПРОБЛЕМА: Не все Meteora адреса есть в ALT таблицах!');
            console.log('💡 РЕШЕНИЕ: Нужно добавить недостающие адреса в ALT таблицы');
        } else {
            console.log('✅ Все основные Meteora адреса найдены в ALT таблицах');
            console.log('🔍 Проблема может быть в других адресах (bin arrays, reserves, etc.)');
        }

    } catch (error) {
        console.error('❌ Ошибка анализа:', error.message);
        console.error(error.stack);
    }
}

// Запуск анализа
if (require.main === module) {
    analyzeALTvsInstructions();
}

module.exports = { analyzeALTvsInstructions };
