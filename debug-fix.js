const fs = require('fs');

console.log('🔍 ОТЛАДКА ИСПРАВЛЕНИЯ...');

// Читаем файл построчно
const lines = fs.readFileSync('BMeteora.js', 'utf8').split('\n');

// Ищем проблемную строку
for (let i = 525; i < 535; i++) {
    console.log(`Строка ${i + 1}: "${lines[i]}"`);
    if (lines[i] && lines[i].includes('exactPrices[')) {
        console.log(`🎯 НАЙДЕНА ПРОБЛЕМНАЯ СТРОКА ${i + 1}!`);
        console.log(`Содержимое: "${lines[i]}"`);
        
        // Исправляем
        lines[i] = lines[i].replace('exactPrices[poolAddress]', 'exactPrices.get(poolAddress)');
        console.log(`Исправлено на: "${lines[i]}"`);
        
        // Записываем обратно
        fs.writeFileSync('BMeteora.js', lines.join('\n'));
        
        console.log('✅ ИСПРАВЛЕНИЕ ПРИМЕНЕНО!');
        break;
    }
}

// Финальная проверка
const content = fs.readFileSync('BMeteora.js', 'utf8');
if (content.includes('exactPrices.get(poolAddress)')) {
    console.log('🎉 УСПЕХ! Исправление работает');
} else {
    console.log('❌ Исправление не сработало');
    
    // Показываем что есть в файле
    const lines2 = content.split('\n');
    console.log('Строки 525-535:');
    for (let i = 525; i < 535; i++) {
        if (lines2[i]) {
            console.log(`${i + 1}: ${lines2[i]}`);
        }
    }
}
