#!/usr/bin/env node

/**
 * 🔍 ДЕТЕКТИВНОЕ РАССЛЕДОВАНИЕ ПОТОКА ДАННЫХ MARGINFI
 * ═══════════════════════════════════════════════════════════════════════════════
 * 🎯 ЦЕЛЬ: Найти ОТКУДА берутся ошибочные данные о долгах 37580.68
 * 🔍 ПРОБЛЕМА: Система читает неверные данные из блокчейна
 * ═══════════════════════════════════════════════════════════════════════════════
 */

const { Connection, PublicKey, Keypair } = require('@solana/web3.js');
const { MarginfiClient, getConfig, MarginfiAccountWrapper } = require('@mrgnlabs/marginfi-client-v2');
const { NodeWallet } = require('@mrgnlabs/mrgn-common');
const bs58 = require('bs58');
require('dotenv').config({ path: '.env.solana' });

class MarginFiDataFlowDetective {
  constructor() {
    this.connection = null;
    this.wallet = null;
    this.marginfiClient = null;
    this.suspiciousAccount = '********************************************';
  }

  async initialize() {
    console.log('🔍 ДЕТЕКТИВНОЕ РАССЛЕДОВАНИЕ ПОТОКА ДАННЫХ MARGINFI');
    console.log('═══════════════════════════════════════════════════════════════');
    console.log(`🎯 Подозрительный аккаунт: ${this.suspiciousAccount}`);
    console.log('🔍 Ищем источник ошибочных данных о долгах 37580.68');
    console.log('═══════════════════════════════════════════════════════════════');

    // Подключение к RPC
    this.connection = new Connection(
      'https://sparkling-patient-market.solana-mainnet.quiknode.pro/146de9e433ca3b8f3473c0bd41742b24429dfc43/',
      'confirmed'
    );

    // Загрузка кошелька
    const privateKeyBase58 = process.env.WALLET_PRIVATE_KEY;
    if (!privateKeyBase58) {
      throw new Error('WALLET_PRIVATE_KEY не найден в .env.solana');
    }

    const privateKeyBytes = bs58.default ? bs58.default.decode(privateKeyBase58) : bs58.decode(privateKeyBase58);
    this.wallet = Keypair.fromSecretKey(privateKeyBytes);
    console.log(`👛 Кошелек: ${this.wallet.publicKey.toString()}`);

    // Инициализация MarginFi
    const nodeWallet = new NodeWallet(this.wallet);
    const config = getConfig('production');
    this.marginfiClient = await MarginfiClient.fetch(config, nodeWallet, this.connection);
    console.log('✅ MarginFi клиент инициализирован');
  }

  async investigateDataFlow() {
    console.log('\n🔍 ИССЛЕДОВАНИЕ ПОТОКА ДАННЫХ');
    console.log('═══════════════════════════════════════════════════════════════');

    const accountAddress = new PublicKey(this.suspiciousAccount);
    
    try {
      // Шаг 1: Загружаем аккаунт через MarginfiAccountWrapper
      console.log('📊 ШАГ 1: Загрузка через MarginfiAccountWrapper');
      const account = await MarginfiAccountWrapper.fetch(accountAddress, this.marginfiClient);
      
      if (!account) {
        console.log('❌ Аккаунт не найден');
        return;
      }

      console.log(`✅ Аккаунт загружен: ${account.address.toString()}`);

      // Шаг 2: Исследуем computeHealthComponents
      console.log('\n📊 ШАГ 2: Анализ computeHealthComponents');
      
      try {
        // Вызываем тот же метод что и в SDK
        const healthComponents = account.computeHealthComponents('Maintenance');
        
        console.log('🔍 РЕЗУЛЬТАТ computeHealthComponents:');
        console.log(`   Assets: ${healthComponents.assets.toString()}`);
        console.log(`   Liabilities: ${healthComponents.liabilities.toString()}`);
        console.log(`   Assets (число): ${healthComponents.assets.toNumber()}`);
        console.log(`   Liabilities (число): ${healthComponents.liabilities.toNumber()}`);
        
        // Проверяем откуда берутся эти данные
        console.log('\n🔍 АНАЛИЗ ИСТОЧНИКА ДАННЫХ:');
        console.log(`   Banks в клиенте: ${this.marginfiClient.banks ? this.marginfiClient.banks.size : 'НЕТ'}`);
        console.log(`   Oracle prices: ${this.marginfiClient.oraclePrices ? this.marginfiClient.oraclePrices.size : 'НЕТ'}`);

      } catch (error) {
        console.log(`❌ Ошибка computeHealthComponents: ${error.message}`);
      }

      // Шаг 3: Исследуем балансы напрямую
      console.log('\n📊 ШАГ 3: Анализ балансов напрямую');
      
      if (account.balances) {
        console.log(`🔍 Найдено ${account.balances.length} балансов:`);
        
        for (let i = 0; i < account.balances.length; i++) {
          const balance = account.balances[i];
          console.log(`\n   Баланс ${i + 1}:`);
          console.log(`     Bank PK: ${balance.bankPk.toString()}`);
          console.log(`     Asset Shares: ${balance.assetShares.toString()}`);
          console.log(`     Liability Shares: ${balance.liabilityShares.toString()}`);
          console.log(`     Active: ${balance.active}`);
          
          // Получаем банк для этого баланса
          try {
            const bank = this.marginfiClient.getBankByPk(balance.bankPk);
            if (bank) {
              console.log(`     Token: ${bank.tokenSymbol || 'UNKNOWN'}`);
              console.log(`     Mint: ${bank.mint.toString()}`);
              
              // Получаем oracle цену
              try {
                const oraclePrice = this.marginfiClient.getOraclePriceByBank(bank.address);
                if (oraclePrice) {
                  console.log(`     Oracle Price: ${oraclePrice.price.toString()}`);
                  console.log(`     Oracle Confidence: ${oraclePrice.confidence.toString()}`);
                  console.log(`     Oracle Slot: ${oraclePrice.slot}`);
                }
              } catch (oracleError) {
                console.log(`     Oracle Error: ${oracleError.message}`);
              }
              
              // Вычисляем USD значения
              try {
                const usdValue = balance.computeUsdValue(bank, this.marginfiClient.getOraclePriceByBank(bank.address));
                console.log(`     USD Assets: ${usdValue.assets.toString()}`);
                console.log(`     USD Liabilities: ${usdValue.liabilities.toString()}`);
              } catch (usdError) {
                console.log(`     USD Calculation Error: ${usdError.message}`);
              }
            }
          } catch (bankError) {
            console.log(`     Bank Error: ${bankError.message}`);
          }
        }
      }

      // Шаг 4: Проверяем активные балансы
      console.log('\n📊 ШАГ 4: Анализ активных балансов');
      
      const activeBalances = account.activeBalances || [];
      console.log(`🔍 Активных балансов: ${activeBalances.length}`);
      
      if (activeBalances.length > 0) {
        activeBalances.forEach((balance, index) => {
          console.log(`   Активный баланс ${index + 1}:`);
          console.log(`     ${JSON.stringify(balance, null, 4)}`);
        });
      }

      // Шаг 5: Проверяем canBeLiquidated
      console.log('\n📊 ШАГ 5: Проверка canBeLiquidated');
      
      try {
        const canLiquidate = account.canBeLiquidated();
        console.log(`🔍 Может быть ликвидирован: ${canLiquidate}`);
      } catch (liquidateError) {
        console.log(`❌ Ошибка canBeLiquidated: ${liquidateError.message}`);
      }

    } catch (error) {
      console.log(`❌ Ошибка исследования: ${error.message}`);
      console.log(`🔍 Stack trace: ${error.stack}`);
    }
  }

  async investigateRawAccountData() {
    console.log('\n🔍 ИССЛЕДОВАНИЕ RAW ДАННЫХ АККАУНТА');
    console.log('═══════════════════════════════════════════════════════════════');

    const accountAddress = new PublicKey(this.suspiciousAccount);
    
    try {
      // Получаем raw данные аккаунта
      const accountInfo = await this.connection.getAccountInfo(accountAddress);
      
      if (accountInfo) {
        console.log(`✅ Raw аккаунт найден`);
        console.log(`📊 Owner: ${accountInfo.owner.toString()}`);
        console.log(`📊 Data length: ${accountInfo.data.length} bytes`);
        console.log(`📊 Lamports: ${accountInfo.lamports}`);
        
        // Анализируем структуру данных
        console.log('\n🔍 АНАЛИЗ СТРУКТУРЫ ДАННЫХ:');
        
        // Первые 8 байт - discriminator
        const discriminator = accountInfo.data.slice(0, 8);
        console.log(`📊 Discriminator: ${discriminator.toString('hex')}`);
        
        // Показываем данные по частям
        console.log('\n🔍 ДАННЫЕ ПО ЧАСТЯМ (первые 200 байт):');
        for (let i = 0; i < Math.min(200, accountInfo.data.length); i += 32) {
          const chunk = accountInfo.data.slice(i, i + 32);
          console.log(`   ${i.toString().padStart(3, '0')}-${(i+31).toString().padStart(3, '0')}: ${chunk.toString('hex')}`);
        }
        
      } else {
        console.log('❌ Raw аккаунт не найден');
      }

    } catch (error) {
      console.log(`❌ Ошибка получения raw данных: ${error.message}`);
    }
  }

  async investigateTransactionHistory() {
    console.log('\n🔍 ИССЛЕДОВАНИЕ ИСТОРИИ ТРАНЗАКЦИЙ');
    console.log('═══════════════════════════════════════════════════════════════');

    const accountAddress = new PublicKey(this.suspiciousAccount);
    
    try {
      // Получаем последние транзакции
      const signatures = await this.connection.getSignaturesForAddress(accountAddress, { limit: 10 });
      
      console.log(`🔍 Найдено ${signatures.length} последних транзакций:`);
      
      for (let i = 0; i < Math.min(5, signatures.length); i++) {
        const sig = signatures[i];
        console.log(`\n   Транзакция ${i + 1}:`);
        console.log(`     Signature: ${sig.signature}`);
        console.log(`     Slot: ${sig.slot}`);
        console.log(`     Block Time: ${new Date(sig.blockTime * 1000).toISOString()}`);
        console.log(`     Status: ${sig.err ? 'ERROR' : 'SUCCESS'}`);
        
        if (sig.err) {
          console.log(`     Error: ${JSON.stringify(sig.err)}`);
        }
      }

    } catch (error) {
      console.log(`❌ Ошибка получения истории транзакций: ${error.message}`);
    }
  }

  async run() {
    try {
      await this.initialize();
      await this.investigateDataFlow();
      await this.investigateRawAccountData();
      await this.investigateTransactionHistory();
      
      console.log('\n🏁 РАССЛЕДОВАНИЕ ЗАВЕРШЕНО');
      console.log('═══════════════════════════════════════════════════════════════');
      
    } catch (error) {
      console.error(`❌ Критическая ошибка: ${error.message}`);
      console.log(`🔍 Stack trace: ${error.stack}`);
    }
  }
}

// Запуск расследования
const detective = new MarginFiDataFlowDetective();
detective.run();
