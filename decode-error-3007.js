/**
 * 🔥 ДЕКОДЕР ОШИБКИ 3007 - MARGINFI ERROR ANALYZER
 * 
 * Анализирует ошибку Custom: 3007 и находит решение
 */

const { Connection, PublicKey } = require('@solana/web3.js');
const { MarginfiClient, getConfig } = require('@mrgnlabs/marginfi-client-v2');
const fs = require('fs');

class Error3007Decoder {
    constructor() {
        this.connection = new Connection('https://billowing-empty-patron.solana-mainnet.quiknode.pro/c79212a7508ec6a37353793d9be93c917898e858/', 'confirmed');
        
        // 🔥 ИЗВЕСТНЫЕ MARGINFI ОШИБКИ
        this.MARGINFI_ERRORS = {
            3000: "InternalLogicError",
            3001: "BankNotFound", 
            3002: "LendingAccountBalanceNotFound",
            3003: "BankAssetCapacityExceeded",
            3004: "InvalidTransfer",
            3005: "MissingPythOrBankAccount",
            3006: "MissingPythAccount",
            3007: "MissingBankAccount", // ← ВОТ НАША ОШИБКА!
            3008: "InvalidBankAccount",
            6009: "RiskEngineInitRejected",
            6027: "BankLiabilityCapacityExceeded"
        };
        
        // 🔥 БАНКИ MARGINFI
        this.MARGINFI_BANKS = {
            SOL: new PublicKey('CCKtUs6Cgwo4aaQUmBPmyoApH2gUDErxNZCAntD6LYGh'),
            USDC: new PublicKey('2s37akK2eyBbp8DZgCm7RtsaEz8eJP3Nxd4urLHQv7yB'),
            USDT: new PublicKey('HmpMfL8942u22htC4EMiWgLX931g3sacXFR6KjuLgKLV')
        };
    }

    /**
     * 🔍 АНАЛИЗ ОШИБКИ 3007
     */
    analyzeError3007() {
        console.log('🔥 АНАЛИЗ ОШИБКИ 3007 - MARGINFI ERROR DECODER');
        console.log('═══════════════════════════════════════════════════════════════');
        
        const errorCode = 3007;
        const errorName = this.MARGINFI_ERRORS[errorCode];
        
        console.log(`🚨 ОШИБКА: Custom: ${errorCode}`);
        console.log(`📋 НАЗВАНИЕ: ${errorName}`);
        console.log(`💡 ОПИСАНИЕ: Missing Bank Account - Отсутствует банковский аккаунт`);
        
        console.log('\n🔍 ВОЗМОЖНЫЕ ПРИЧИНЫ:');
        console.log('   1. ❌ Неправильный адрес банка в инструкции');
        console.log('   2. ❌ Банк не передан в аккаунты транзакции');
        console.log('   3. ❌ Банк не существует или недоступен');
        console.log('   4. ❌ Неправильный порядок аккаунтов');
        console.log('   5. ❌ Банк не принадлежит MarginFi группе');
        
        console.log('\n🎯 РЕШЕНИЯ:');
        console.log('   ✅ Проверить адреса банков');
        console.log('   ✅ Убедиться что банк передается в транзакцию');
        console.log('   ✅ Проверить порядок аккаунтов');
        console.log('   ✅ Использовать правильные банки MarginFi');
        
        return {
            code: errorCode,
            name: errorName,
            description: 'Missing Bank Account - Отсутствует банковский аккаунт'
        };
    }

    /**
     * 🏦 ПРОВЕРКА БАНКОВ MARGINFI
     */
    async checkMarginFiBanks() {
        try {
            console.log('\n🏦 ПРОВЕРКА БАНКОВ MARGINFI');
            console.log('═══════════════════════════════════════════════════════════════');
            
            // Загружаем wallet
            const walletData = JSON.parse(fs.readFileSync('wallet.json', 'utf8'));
            const keypair = require('@solana/web3.js').Keypair.fromSecretKey(new Uint8Array(walletData));
            const wallet = { payer: keypair, publicKey: keypair.publicKey };
            
            // Создаем MarginFi client
            const config = getConfig('production');
            const marginfiClient = await MarginfiClient.fetch(config, wallet, this.connection);
            
            console.log('✅ MarginFi client инициализирован');
            
            for (const [symbol, bankPk] of Object.entries(this.MARGINFI_BANKS)) {
                try {
                    console.log(`\n📊 ПРОВЕРКА БАНКА ${symbol}:`);
                    console.log(`   🏦 Адрес: ${bankPk.toString()}`);
                    
                    // Проверяем существование банка
                    const bankAccountInfo = await this.connection.getAccountInfo(bankPk);
                    if (!bankAccountInfo) {
                        console.log(`   ❌ Банк не существует в блокчейне!`);
                        continue;
                    }
                    
                    console.log(`   ✅ Банк существует в блокчейне`);
                    console.log(`   📏 Размер данных: ${bankAccountInfo.data.length} bytes`);
                    console.log(`   👤 Владелец: ${bankAccountInfo.owner.toString()}`);
                    
                    // Проверяем через MarginFi client
                    const bank = marginfiClient.getBankByPk(bankPk);
                    if (!bank) {
                        console.log(`   ❌ Банк не найден в MarginFi client!`);
                        continue;
                    }
                    
                    console.log(`   ✅ Банк найден в MarginFi client`);
                    console.log(`   🏷️ Символ: ${bank.tokenSymbol}`);
                    console.log(`   🪙 Mint: ${bank.mint.toString()}`);
                    console.log(`   💰 Цена: $${bank.getPrice().toFixed(6)}`);
                    
                    // Проверяем принадлежность к группе
                    const expectedGroup = new PublicKey('4qp6Fx6tnZkY5Wropq9wUYgtFxXKwE6viZxFHg3rdAG8');
                    if (bank.group.equals(expectedGroup)) {
                        console.log(`   ✅ Принадлежит правильной MarginFi группе`);
                    } else {
                        console.log(`   ❌ НЕ принадлежит правильной MarginFi группе!`);
                        console.log(`   📋 Ожидаемая группа: ${expectedGroup.toString()}`);
                        console.log(`   📋 Фактическая группа: ${bank.group.toString()}`);
                    }
                    
                } catch (bankError) {
                    console.log(`   ❌ Ошибка проверки банка ${symbol}: ${bankError.message}`);
                }
            }
            
            return true;
        } catch (error) {
            console.error(`❌ Ошибка проверки банков: ${error.message}`);
            return false;
        }
    }

    /**
     * 🔧 ДИАГНОСТИКА ТРАНЗАКЦИИ
     */
    async diagnoseTransaction() {
        try {
            console.log('\n🔧 ДИАГНОСТИКА ПРОБЛЕМНОЙ ТРАНЗАКЦИИ');
            console.log('═══════════════════════════════════════════════════════════════');
            
            console.log('🔍 АНАЛИЗ СТРУКТУРЫ ТРАНЗАКЦИИ:');
            console.log('   📊 Всего инструкций: 6');
            console.log('   📏 Размер: 862 bytes');
            console.log('   🏗️ Структура: borrow + арбитраж + repay');
            
            console.log('\n🚨 ПРОБЛЕМА В ИНСТРУКЦИИ 0 (первая инструкция):');
            console.log('   ❌ InstructionError: [0, { Custom: 3007 }]');
            console.log('   💡 Ошибка в первой инструкции (borrow)');
            console.log('   🏦 Проблема: Missing Bank Account');
            
            console.log('\n🎯 ВОЗМОЖНЫЕ ПРИЧИНЫ:');
            console.log('   1. ❌ Неправильный банк в borrow инструкции');
            console.log('   2. ❌ Банк не передан в аккаунты');
            console.log('   3. ❌ Неправильный порядок аккаунтов');
            console.log('   4. ❌ Банк не принадлежит MarginFi группе');
            
            console.log('\n✅ РЕШЕНИЯ:');
            console.log('   🔧 Проверить адрес банка USDC: 2s37akK2eyBbp8DZgCm7RtsaEz8eJP3Nxd4urLHQv7yB');
            console.log('   🔧 Убедиться что банк передается в аккаунты borrow инструкции');
            console.log('   🔧 Проверить порядок аккаунтов в borrow инструкции');
            console.log('   🔧 Использовать правильную MarginFi группу');
            
            return true;
        } catch (error) {
            console.error(`❌ Ошибка диагностики: ${error.message}`);
            return false;
        }
    }

    /**
     * 🔥 ПОЛНАЯ ДИАГНОСТИКА ОШИБКИ 3007
     */
    async performFullDiagnosis() {
        try {
            console.log('🔥 ПОЛНАЯ ДИАГНОСТИКА ОШИБКИ 3007');
            console.log('═══════════════════════════════════════════════════════════════');
            
            // 1. Анализируем ошибку
            const errorInfo = this.analyzeError3007();
            
            // 2. Проверяем банки
            const banksOk = await this.checkMarginFiBanks();
            
            // 3. Диагностируем транзакцию
            const diagnosisOk = await this.diagnoseTransaction();
            
            // 4. Итоговый отчет
            console.log('\n🎯 ИТОГОВЫЙ ОТЧЕТ');
            console.log('═══════════════════════════════════════════════════════════════');
            
            console.log(`🚨 Ошибка: ${errorInfo.name} (${errorInfo.code})`);
            console.log(`📋 Описание: ${errorInfo.description}`);
            console.log(`🏦 Банки проверены: ${banksOk ? '✅ Да' : '❌ Нет'}`);
            console.log(`🔧 Диагностика: ${diagnosisOk ? '✅ Завершена' : '❌ Ошибка'}`);
            
            console.log('\n🔥 ГЛАВНАЯ ПРОБЛЕМА:');
            console.log('   ❌ В borrow инструкции отсутствует или неправильный банк');
            console.log('   💡 Нужно исправить структуру аккаунтов в borrow инструкции');
            
            console.log('\n✅ СЛЕДУЮЩИЕ ШАГИ:');
            console.log('   1. 🔧 Проверить код создания borrow инструкции');
            console.log('   2. 🏦 Убедиться что правильный банк передается');
            console.log('   3. 📋 Проверить порядок аккаунтов');
            console.log('   4. 🧪 Протестировать исправленную инструкцию');
            
            return true;
            
        } catch (error) {
            console.error(`❌ Ошибка полной диагностики: ${error.message}`);
            return false;
        }
    }
}

async function main() {
    console.log('🔥 ДЕКОДЕР ОШИБКИ 3007 - MARGINFI ERROR ANALYZER');
    console.log('═══════════════════════════════════════════════════════════════');
    console.log('🎯 Анализируем ошибку Custom: 3007 и находим решение');
    console.log('💡 Цель: исправить проблему с отсутствующим банком');
    console.log('═══════════════════════════════════════════════════════════════');

    const decoder = new Error3007Decoder();
    const success = await decoder.performFullDiagnosis();

    if (success) {
        console.log('\n✅ ДИАГНОСТИКА ЗАВЕРШЕНА - ПРОБЛЕМА НАЙДЕНА!');
        console.log('🔧 Используйте результаты для исправления borrow инструкции');
    } else {
        console.log('\n❌ ДИАГНОСТИКА ВЫЯВИЛА ДОПОЛНИТЕЛЬНЫЕ ПРОБЛЕМЫ');
        console.log('💡 Проверьте подключение и конфигурацию');
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = Error3007Decoder;
