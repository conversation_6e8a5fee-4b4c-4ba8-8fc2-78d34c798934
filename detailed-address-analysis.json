{"timestamp": "2025-07-20T18:48:40.072Z", "summary": {"totalAddresses": 12, "coveredCount": 9, "uncoveredCount": 3, "uncoveredAddresses": ["izePositionAndAddLiquidityByStrategy", "AH5RFXEfxoKCGdKxezZb7ASK26mzDCEgtjzD9rtmHpmA", "9NRt4FZP5mES5X4TmyVdt5JkjE4cDAJY3Bf7cS6Errto"], "instructionBreakdown": [{"type": "BORROW", "addressCount": 0, "addresses": []}, {"type": "BORROW", "addressCount": 0, "addresses": []}, {"type": "BORROW", "addressCount": 0, "addresses": []}, {"type": "BORROW", "addressCount": 0, "addresses": []}, {"type": "WSOL_CONVERSION", "addressCount": 0, "addresses": []}, {"type": "UNKNOWN", "addressCount": 1, "addresses": ["izePositionAndAddLiquidityByStrategy"]}, {"type": "UNKNOWN", "addressCount": 2, "addresses": ["izePositionAndAddLiquidityByStrategy", "5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6"]}, {"type": "UNKNOWN", "addressCount": 2, "addresses": ["AH5RFXEfxoKCGdKxezZb7ASK26mzDCEgtjzD9rtmHpmA", "BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y"]}, {"type": "UNKNOWN", "addressCount": 1, "addresses": ["9NRt4FZP5mES5X4TmyVdt5JkjE4cDAJY3Bf7cS6Errto"]}, {"type": "UNKNOWN", "addressCount": 1, "addresses": ["izePositionAndAddLiquidityByStrategy"]}, {"type": "CREATE_ATA", "addressCount": 0, "addresses": []}, {"type": "UNKNOWN", "addressCount": 0, "addresses": []}, {"type": "SWAP", "addressCount": 8, "addresses": ["BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y", "DwZz4S1Z1LBXomzmncQRVKCYhjCqSAMQ6RPKbUAadr7H", "4N22J4vW2juHocTntJNmXywSonYjkndCwahjZ2cYLDgb", "ETc6tqgLrr7wXsH8u2QBK1CyXHX3kvV6WQjBz4cf3sCj", "5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6", "EYj9xKw6ZszwpyNibHY7JD5o3QgTVrSdcBp1fMJhrR9o", "CoaxzEh8p5YyGLcj36Eo3cUThVJxeKCs7qvLAGDYwBcz", "59YuGWPunbchD2mbi9U7qvjWQKQReGeepn4ZSr9zz9Li"]}, {"type": "COLLECT_FEE", "addressCount": 0, "addresses": []}, {"type": "REPAY", "addressCount": 0, "addresses": []}, {"type": "REPAY", "addressCount": 0, "addresses": []}, {"type": "REPAY", "addressCount": 0, "addresses": []}, {"type": "REPAY", "addressCount": 0, "addresses": []}, {"type": "BORROW", "addressCount": 0, "addresses": []}, {"type": "REPAY", "addressCount": 2, "addresses": ["AH5RFXEfxoKCGdKxezZb7ASK26mzDCEgtjzD9rtmHpmA", "9NRt4FZP5mES5X4TmyVdt5JkjE4cDAJY3Bf7cS6Errto"]}, {"type": "UNKNOWN", "addressCount": 3, "addresses": ["AH5RFXEfxoKCGdKxezZb7ASK26mzDCEgtjzD9rtmHpmA", "9NRt4FZP5mES5X4TmyVdt5JkjE4cDAJY3Bf7cS6Errto", "bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV"]}], "compressionPotential": {"addressCount": 3, "byteSavings": 96, "kbSavings": "0.1"}}, "detailedBreakdown": [{"type": "BORROW", "addressCount": 0, "addresses": []}, {"type": "BORROW", "addressCount": 0, "addresses": []}, {"type": "BORROW", "addressCount": 0, "addresses": []}, {"type": "BORROW", "addressCount": 0, "addresses": []}, {"type": "WSOL_CONVERSION", "addressCount": 0, "addresses": []}, {"type": "UNKNOWN", "addressCount": 1, "addresses": ["izePositionAndAddLiquidityByStrategy"]}, {"type": "UNKNOWN", "addressCount": 2, "addresses": ["izePositionAndAddLiquidityByStrategy", "5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6"]}, {"type": "UNKNOWN", "addressCount": 2, "addresses": ["AH5RFXEfxoKCGdKxezZb7ASK26mzDCEgtjzD9rtmHpmA", "BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y"]}, {"type": "UNKNOWN", "addressCount": 1, "addresses": ["9NRt4FZP5mES5X4TmyVdt5JkjE4cDAJY3Bf7cS6Errto"]}, {"type": "UNKNOWN", "addressCount": 1, "addresses": ["izePositionAndAddLiquidityByStrategy"]}, {"type": "CREATE_ATA", "addressCount": 0, "addresses": []}, {"type": "UNKNOWN", "addressCount": 0, "addresses": []}, {"type": "SWAP", "addressCount": 8, "addresses": ["BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y", "DwZz4S1Z1LBXomzmncQRVKCYhjCqSAMQ6RPKbUAadr7H", "4N22J4vW2juHocTntJNmXywSonYjkndCwahjZ2cYLDgb", "ETc6tqgLrr7wXsH8u2QBK1CyXHX3kvV6WQjBz4cf3sCj", "5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6", "EYj9xKw6ZszwpyNibHY7JD5o3QgTVrSdcBp1fMJhrR9o", "CoaxzEh8p5YyGLcj36Eo3cUThVJxeKCs7qvLAGDYwBcz", "59YuGWPunbchD2mbi9U7qvjWQKQReGeepn4ZSr9zz9Li"]}, {"type": "COLLECT_FEE", "addressCount": 0, "addresses": []}, {"type": "REPAY", "addressCount": 0, "addresses": []}, {"type": "REPAY", "addressCount": 0, "addresses": []}, {"type": "REPAY", "addressCount": 0, "addresses": []}, {"type": "REPAY", "addressCount": 0, "addresses": []}, {"type": "BORROW", "addressCount": 0, "addresses": []}, {"type": "REPAY", "addressCount": 2, "addresses": ["AH5RFXEfxoKCGdKxezZb7ASK26mzDCEgtjzD9rtmHpmA", "9NRt4FZP5mES5X4TmyVdt5JkjE4cDAJY3Bf7cS6Errto"]}, {"type": "UNKNOWN", "addressCount": 3, "addresses": ["AH5RFXEfxoKCGdKxezZb7ASK26mzDCEgtjzD9rtmHpmA", "9NRt4FZP5mES5X4TmyVdt5JkjE4cDAJY3Bf7cS6Errto", "bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV"]}]}