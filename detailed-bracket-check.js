/**
 * 🔍 ДЕТАЛЬНАЯ ПРОВЕРКА СКОБОК В ФУНКЦИИ createFlashLoanTransaction
 */

const fs = require('fs');

function detailedBracketCheck() {
  const content = fs.readFileSync('./src/atomic-transaction-builder-fixed.js', 'utf8');
  const lines = content.split('\n');
  
  console.log('🔍 ДЕТАЛЬНАЯ ПРОВЕРКА ФУНКЦИИ createFlashLoanTransaction:');
  console.log('');
  
  let braceCount = 0;
  let inFunction = false;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const lineNum = i + 1;
    
    // Начало функции createFlashLoanTransaction
    if (lineNum === 612) {
      inFunction = true;
      console.log(`📍 НАЧАЛО ФУНКЦИИ (строка ${lineNum}):`);
    }
    
    if (inFunction) {
      // Подсчитываем фигурные скобки в строке
      let openBraces = 0;
      let closeBraces = 0;
      
      for (const char of line) {
        if (char === '{') openBraces++;
        if (char === '}') closeBraces++;
      }
      
      braceCount += openBraces - closeBraces;
      
      // Показываем важные строки
      if (line.includes('try') || line.includes('catch') || line.includes('}') || openBraces > 0 || closeBraces > 0) {
        console.log(`   ${lineNum}: [${braceCount}] ${line.trim()}`);
        if (openBraces > 0) console.log(`      +${openBraces} открывающих`);
        if (closeBraces > 0) console.log(`      -${closeBraces} закрывающих`);
      }
      
      // Конец функции createFlashLoanTransaction
      if (lineNum === 1461) {
        console.log(`📍 КОНЕЦ ФУНКЦИИ (строка ${lineNum}): баланс = ${braceCount}`);
        break;
      }
      
      // Проверяем критические точки
      if (braceCount < 0) {
        console.log(`❌ ОШИБКА на строке ${lineNum}: Отрицательный баланс скобок!`);
        console.log(`   Строка: "${line.trim()}"`);
        break;
      }
    }
  }
  
  console.log('');
  if (braceCount === 0) {
    console.log('✅ Баланс скобок в функции createFlashLoanTransaction корректен!');
  } else {
    console.log(`❌ Баланс скобок нарушен: ${braceCount > 0 ? 'не хватает' : 'лишние'} ${Math.abs(braceCount)} скобок`);
  }
}

detailedBracketCheck();
