# 🏊 ПОЛНЫЙ СПРАВОЧНИК SDK ДЛЯ ПРЯМОГО ПОДКЛЮЧЕНИЯ К ПУЛАМ

## 🎯 **НАЙДЕННЫЕ ОФИЦИАЛЬНЫЕ SDK И РЕПОЗИТОРИИ**

---

## 🔥 **1. PHOENIX DEX**

### 📦 **Официальный SDK:**
- **GitHub**: https://github.com/Ellipsis-Labs/phoenix-sdk
- **NPM**: `@ellipsis-labs/phoenix-sdk`
- **Языки**: TypeScript, Rust, Python

### 🚀 **Установка:**
```bash
npm install @ellipsis-labs/phoenix-sdk
```

### 📊 **Пример использования:**
```typescript
import * as Phoenix from "@ellipsis-labs/phoenix-sdk";
import { Connection, Keypair } from "@solana/web3.js";

// Подключение к Phoenix
const connection = new Connection("https://api.mainnet-beta.solana.com");
const phoenix = await Phoenix.Client.create(connection);

// Поиск рынка SOL/USDC
const marketConfig = phoenix.marketConfigs.find(
  (market) => market.name === "SOL/USDC"
);

// Получение состояния рынка
const marketState = phoenix.marketStates.get(marketConfig.marketId);

// Получение цены
const price = marketState.getBestBid(); // Лучшая цена покупки
const ask = marketState.getBestAsk();   // Лучшая цена продажи
```

### 🔗 **Дополнительные ресурсы:**
- **Mainnet Markets**: https://github.com/Ellipsis-Labs/phoenix-sdk/blob/master/mainnet_markets.json
- **Devnet Markets**: https://github.com/Ellipsis-Labs/phoenix-sdk/blob/master/devnet_markets.json
- **Python SDK**: https://github.com/Ellipsis-Labs/phoenixpy

---

## 🌊 **2. LIFINITY V2**

### 📦 **Официальный репозиторий:**
- **GitHub**: https://github.com/Lifinity-Labs
- **Основной репо**: https://github.com/Lifinity-Labs/flare-staking
- **Статус**: Ограниченные публичные SDK

### ⚠️ **Проблема:**
Lifinity Labs имеет **ограниченные публичные SDK**. Большинство репозиториев - форки других проектов.

### 🔧 **Альтернативные способы:**
1. **Через Jupiter SDK** (рекомендуется):
```typescript
// Jupiter автоматически подключается к Lifinity V2 пулам
const quote = await jupiter.getQuote({
  inputMint: "So11111111111111111111111111111111111111112",
  outputMint: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
  amount: 1000000000,
  dexes: ["Lifinity V2"] // Ограничиваем только Lifinity V2
});
```

2. **Прямое взаимодействие с программой**:
```typescript
// Program ID Lifinity V2
const LIFINITY_V2_PROGRAM_ID = "2wT8Yq49kHgDzXuPxZSaeLaH1qbmGXtEyPy64bL7aD3c";
```

---

## 📚 **3. OPENBOOK DEX**

### 📦 **Официальные SDK:**
- **GitHub**: https://github.com/openbook-dex
- **OpenBook V2**: https://github.com/openbook-dex/openbook-v2
- **NPM**: `@openbook-dex/openbook-v2`
- **Scripts**: https://github.com/openbook-dex/scripts-v2

### 🚀 **Установка:**
```bash
npm install @openbook-dex/openbook-v2
```

### 📊 **Пример использования:**
```typescript
import { OpenBookV2Client } from '@openbook-dex/openbook-v2';
import { Connection, PublicKey } from '@solana/web3.js';

// Подключение к OpenBook V2
const connection = new Connection("https://api.mainnet-beta.solana.com");
const client = new OpenBookV2Client(connection);

// Получение информации о рынке
const marketPubkey = new PublicKey("MARKET_ADDRESS_HERE");
const market = await client.getMarket(marketPubkey);

// Получение книги ордеров
const bids = await market.loadBids(connection);
const asks = await market.loadAsks(connection);

// Лучшие цены
const bestBid = bids.getBest();
const bestAsk = asks.getBest();
```

### 🔗 **Дополнительные ресурсы:**
- **Документация**: https://github.com/openbook-dex/openbook-docs
- **Примеры**: https://github.com/openbook-dex/scripts-v2
- **UI**: https://github.com/openbook-dex/openbook-v2-ui

---

## 🎯 **4. ДОПОЛНИТЕЛЬНЫЕ DEX SDK**

### 🌊 **Orca Whirlpools:**
- **GitHub**: https://github.com/orca-so/whirlpools
- **NPM**: `@orca-so/whirlpools-sdk`

### ⚡ **Raydium:**
- **GitHub**: https://github.com/raydium-io/raydium-sdk-V2
- **NPM**: `@raydium-io/raydium-sdk-v2`

### 🌪️ **Meteora:**
- **GitHub**: https://github.com/MeteoraAg/dlmm-sdk
- **NPM**: `@meteora-ag/dlmm`

---

## 🛠️ **ПРАКТИЧЕСКАЯ РЕАЛИЗАЦИЯ**

### 📊 **Создание мультипулового мониторинга:**

```typescript
import * as Phoenix from "@ellipsis-labs/phoenix-sdk";
import { OpenBookV2Client } from '@openbook-dex/openbook-v2';
import { Connection } from '@solana/web3.js';

class MultiPoolMonitor {
  constructor() {
    this.connection = new Connection("https://api.mainnet-beta.solana.com");
    this.pools = new Map();
  }

  async initializePhoenix() {
    this.phoenix = await Phoenix.Client.create(this.connection);
    console.log("✅ Phoenix подключен");
  }

  async initializeOpenBook() {
    this.openbook = new OpenBookV2Client(this.connection);
    console.log("✅ OpenBook подключен");
  }

  async getPhoenixPrice(marketName) {
    const marketConfig = this.phoenix.marketConfigs.find(
      (market) => market.name === marketName
    );
    
    if (!marketConfig) return null;
    
    const marketState = this.phoenix.marketStates.get(marketConfig.marketId);
    
    return {
      bid: marketState.getBestBid(),
      ask: marketState.getBestAsk(),
      source: 'Phoenix'
    };
  }

  async getOpenBookPrice(marketAddress) {
    const market = await this.openbook.getMarket(marketAddress);
    const bids = await market.loadBids(this.connection);
    const asks = await market.loadAsks(this.connection);
    
    return {
      bid: bids.getBest(),
      ask: asks.getBest(),
      source: 'OpenBook'
    };
  }

  async monitorPrices() {
    const phoenixPrice = await this.getPhoenixPrice("SOL/USDC");
    const openbookPrice = await this.getOpenBookPrice("MARKET_ADDRESS");
    
    // Сравнение цен и поиск арбитража
    if (phoenixPrice && openbookPrice) {
      const spread = phoenixPrice.bid - openbookPrice.ask;
      if (spread > 0) {
        console.log(`🎯 Арбитраж найден: ${spread} USDC`);
      }
    }
  }
}
```

---

## 🎯 **РЕКОМЕНДАЦИИ ПО ИСПОЛЬЗОВАНИЮ**

### ✅ **Лучшие практики:**

1. **Phoenix DEX** - ✅ **Отличный SDK**, полная документация
2. **OpenBook V2** - ✅ **Хороший SDK**, активная разработка  
3. **Lifinity V2** - ⚠️ **Ограниченный SDK**, используйте Jupiter
4. **Orca/Raydium** - ✅ **Отличные SDK**, много примеров

### 🚀 **Стратегия реализации:**

1. **Начните с Phoenix и OpenBook** - у них лучшие SDK
2. **Используйте Jupiter для Lifinity V2** - проще и надежнее
3. **Добавьте Orca и Raydium** для полного покрытия
4. **Мониторьте конкретные пулы**, а не DEX в целом

### 💡 **Следующие шаги:**

1. Установите SDK: `npm install @ellipsis-labs/phoenix-sdk @openbook-dex/openbook-v2`
2. Изучите примеры в репозиториях
3. Создайте тестовое подключение к каждому пулу
4. Реализуйте мониторинг цен в реальном времени
5. Добавьте логику поиска арбитража между пулами

---

## 🔗 **ПОЛЕЗНЫЕ ССЫЛКИ**

- **Phoenix SDK**: https://github.com/Ellipsis-Labs/phoenix-sdk
- **OpenBook V2**: https://github.com/openbook-dex/openbook-v2  
- **Lifinity Labs**: https://github.com/Lifinity-Labs
- **Jupiter Core**: https://github.com/jup-ag/jupiter-core
- **Solana Web3.js**: https://github.com/solana-labs/solana-web3.js

**Теперь у вас есть все необходимые инструменты для прямого подключения к пулам!** 🚀
