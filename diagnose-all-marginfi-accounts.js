#!/usr/bin/env node

/**
 * 🔍 ДИАГНОСТИКА ВСЕХ MARGINFI АККАУНТОВ
 *
 * Находим все созданные аккаунты и выбираем правильный для flash loans
 */

const { Connection, PublicKey, Keypair } = require('@solana/web3.js');
const { MarginfiClient, getConfig } = require('@mrgnlabs/marginfi-client-v2');
const { NodeWallet } = require('@mrgnlabs/mrgn-common');
const fs = require('fs');
const bs58 = require('bs58');

class MarginFiAccountDiagnostic {
  constructor() {
    console.log('🔍 ДИАГНОСТИКА ВСЕХ MARGINFI АККАУНТОВ');
    console.log('═══════════════════════════════════════════════════════');
  }

  async initialize() {
    try {
      // Инициализация подключения
      console.log('🔗 Инициализация подключения...');
      this.connection = new Connection('https://sparkling-patient-market.solana-mainnet.quiknode.pro/146de9e433ca3b8f3473c0bd41742b24429dfc43/', 'confirmed');

      // Загрузка кошелька из .env.solana (как в рабочем коде)
      console.log('👛 Загрузка кошелька из .env.solana...');

      // Читаем .env.solana файл
      const envPath = 'H:/Mempool/DEXSWAP/.env.solana';
      const envContent = fs.readFileSync(envPath, 'utf8');
      const lines = envContent.split('\n');

      let privateKey = null;
      for (const line of lines) {
        if (line.startsWith('WALLET_PRIVATE_KEY=')) {
          privateKey = line.split('=')[1].trim();
          break;
        }
      }

      if (!privateKey) {
        throw new Error('WALLET_PRIVATE_KEY не найден в .env.solana');
      }

      console.log(`✅ НАЙДЕН WALLET_PRIVATE_KEY, длина: ${privateKey.length}`);

      // Декодируем Base58 приватный ключ
      const keypair = Keypair.fromSecretKey(bs58.default ? bs58.default.decode(privateKey) : bs58.decode(privateKey));
      this.wallet = new NodeWallet(keypair);
      console.log(`✅ Wallet: ${this.wallet.publicKey.toString()}`);

      // Инициализация MarginFi клиента
      console.log('🏦 Инициализация MarginFi клиента...');
      const config = getConfig('production');
      this.client = await MarginfiClient.fetch(config, this.wallet, this.connection);
      console.log(`✅ MarginFi клиент инициализирован`);
      console.log(`📊 Group: ${this.client.groupAddress.toString()}`);

    } catch (error) {
      console.error('❌ Ошибка инициализации:', error.message);
      throw error;
    }
  }

  async getAllAccounts() {
    console.log('\n🔍 ПОЛУЧЕНИЕ ВСЕХ MARGINFI АККАУНТОВ');
    console.log('═══════════════════════════════════════════════════════');

    try {
      // Метод 1: getMarginfiAccountsForAuthority
      console.log('📊 Метод 1: getMarginfiAccountsForAuthority...');
      const accounts = await this.client.getMarginfiAccountsForAuthority();
      console.log(`✅ Найдено аккаунтов: ${accounts.length}`);

      if (accounts.length === 0) {
        console.log('⚠️ Аккаунты не найдены через getMarginfiAccountsForAuthority');
        return [];
      }

      return accounts;

    } catch (error) {
      console.error('❌ Ошибка получения аккаунтов:', error.message);
      return [];
    }
  }

  async analyzeAccount(account, index) {
    console.log(`\n📋 АККАУНТ ${index + 1}:`);
    console.log('─────────────────────────────────────────────');

    try {
      console.log(`📍 Адрес: ${account.address.toString()}`);
      console.log(`👤 Authority: ${account.authority.toString()}`);
      console.log(`🏦 Group: ${account.group.toString()}`);

      // Получаем балансы
      const balances = account.getHealthCheckAccounts();
      console.log(`💰 Активных позиций: ${balances.length}`);

      if (balances.length > 0) {
        console.log(`📊 ПОЗИЦИИ:`);
        balances.forEach((balance, i) => {
          console.log(`   ${i + 1}. ${balance.bank.tokenSymbol || 'Unknown'}: ${balance.balance.toString()}`);
        });
      } else {
        console.log(`📊 ПОЗИЦИИ: Нет активных позиций (пустой аккаунт)`);
      }

      // Проверяем health
      try {
        const health = account.getHealthCheckAccounts();
        console.log(`🏥 Health check: ${health.length} позиций`);
      } catch (healthError) {
        console.log(`🏥 Health check: Ошибка - ${healthError.message}`);
      }

      // Проверяем можно ли использовать для flash loans
      const canFlashLoan = this.canUseForFlashLoan(account, balances);
      console.log(`🔥 Flash Loan готовность: ${canFlashLoan ? '✅ ГОТОВ' : '❌ НЕ ГОТОВ'}`);

      return {
        address: account.address.toString(),
        authority: account.authority.toString(),
        group: account.group.toString(),
        positions: balances.length,
        canFlashLoan: canFlashLoan,
        account: account
      };

    } catch (error) {
      console.error(`❌ Ошибка анализа аккаунта ${index + 1}:`, error.message);
      return {
        address: account.address.toString(),
        error: error.message,
        canFlashLoan: false
      };
    }
  }

  canUseForFlashLoan(account, balances) {
    // Для flash loans лучше использовать пустой аккаунт
    // Flash loans не требуют депозитов, но требуют чистого состояния

    if (balances.length === 0) {
      console.log(`   ✅ Пустой аккаунт - идеален для flash loans`);
      return true;
    }

    // Если есть позиции, проверяем что они не создают проблем
    let hasDebt = false;
    let hasAssets = false;

    balances.forEach(balance => {
      if (balance.balance.gt(0)) {
        hasAssets = true;
      } else if (balance.balance.lt(0)) {
        hasDebt = true;
      }
    });

    if (hasDebt && !hasAssets) {
      console.log(`   ❌ Есть долги без активов - плохо для flash loans`);
      return false;
    }

    if (hasAssets && !hasDebt) {
      console.log(`   ⚠️ Есть активы без долгов - можно использовать, но не идеально`);
      return true;
    }

    if (hasAssets && hasDebt) {
      console.log(`   ⚠️ Есть и активы и долги - рискованно для flash loans`);
      return false;
    }

    console.log(`   ✅ Чистый аккаунт - хорошо для flash loans`);
    return true;
  }

  async findBestAccountForFlashLoan(accounts) {
    console.log('\n🎯 ВЫБОР ЛУЧШЕГО АККАУНТА ДЛЯ FLASH LOANS');
    console.log('═══════════════════════════════════════════════════════');

    if (accounts.length === 0) {
      console.log('❌ Нет доступных аккаунтов');
      return null;
    }

    // Анализируем все аккаунты
    const analyzedAccounts = [];
    for (let i = 0; i < accounts.length; i++) {
      const analysis = await this.analyzeAccount(accounts[i], i);
      analyzedAccounts.push(analysis);
    }

    // Находим лучший аккаунт
    const flashLoanReadyAccounts = analyzedAccounts.filter(acc => acc.canFlashLoan);

    console.log(`\n📊 РЕЗУЛЬТАТЫ АНАЛИЗА:`);
    console.log(`   Всего аккаунтов: ${analyzedAccounts.length}`);
    console.log(`   Готовых для flash loans: ${flashLoanReadyAccounts.length}`);

    if (flashLoanReadyAccounts.length === 0) {
      console.log('❌ НИ ОДИН АККАУНТ НЕ ГОТОВ ДЛЯ FLASH LOANS!');
      console.log('💡 Рекомендация: Создать новый чистый аккаунт');
      return null;
    }

    // Выбираем лучший аккаунт (предпочитаем пустые)
    const emptyAccounts = flashLoanReadyAccounts.filter(acc => acc.positions === 0);

    let bestAccount;
    if (emptyAccounts.length > 0) {
      bestAccount = emptyAccounts[0];
      console.log(`✅ ВЫБРАН ПУСТОЙ АККАУНТ: ${bestAccount.address}`);
    } else {
      bestAccount = flashLoanReadyAccounts[0];
      console.log(`✅ ВЫБРАН АККАУНТ С ПОЗИЦИЯМИ: ${bestAccount.address}`);
    }

    return bestAccount;
  }

  async testFlashLoanWithAccount(accountAnalysis) {
    console.log('\n🧪 ТЕСТ FLASH LOAN С ВЫБРАННЫМ АККАУНТОМ');
    console.log('═══════════════════════════════════════════════════════');

    if (!accountAnalysis || !accountAnalysis.account) {
      console.log('❌ Нет аккаунта для тестирования');
      return false;
    }

    try {
      const account = accountAnalysis.account;
      console.log(`🎯 Тестируем аккаунт: ${account.address.toString()}`);

      // Получаем USDC банк
      const usdcBank = this.client.getBankByTokenSymbol('USDC');
      if (!usdcBank) {
        console.log('❌ USDC банк не найден');
        return false;
      }

      console.log(`🏦 USDC банк: ${usdcBank.address.toString()}`);

      // Тестовая сумма: $1 USDC
      const testAmount = 1; // $1 USDC (UI amount)
      console.log(`💰 Тестовая сумма: ${testAmount} USDC`);

      // Создаем borrow инструкции
      console.log('🔧 Создание borrow инструкций...');
      // 🚨 КРИТИЧЕСКИЙ БАГ ИСПРАВЛЕН: УДАЛЕНЫ ПРЯМЫЕ ВЫЗОВЫ makeBorrowIx/makeRepayIx!
      // ЭТО СОЗДАВАЛО РЕАЛЬНЫЕ ЗАЙМЫ ВМЕСТО FLASH LOANS!
      console.log(`🚨 КРИТИЧЕСКИЙ БАГ ИСПРАВЛЕН: Удалены прямые вызовы makeBorrowIx/makeRepayIx!`);
      console.log(`💡 ПРИЧИНА ДОЛГОВ: Эти вызовы создавали РЕАЛЬНЫЕ займы вместо flash loans!`);
      console.log(`✅ ИСПРАВЛЕНИЕ: Используем только buildFlashLoanTx для flash loans!`);

      throw new Error('КРИТИЧЕСКИЙ БАГ ИСПРАВЛЕН: Прямые вызовы makeBorrowIx создают реальные займы!');

      console.log('🎉 ТЕСТ ПРОЙДЕН! Аккаунт готов для flash loans');
      return true;

    } catch (error) {
      console.error('❌ ТЕСТ ПРОВАЛЕН:', error.message);

      if (error.message.includes('6009') || error.message.includes('RiskEngine')) {
        console.log('🔍 ДИАГНОЗ: Проблема с health check - аккаунт имеет плохое здоровье');
        console.log('💡 РЕШЕНИЕ: Использовать другой аккаунт или создать новый');
      }

      return false;
    }
  }

  async run() {
    try {
      await this.initialize();

      const accounts = await this.getAllAccounts();

      if (accounts.length === 0) {
        console.log('\n❌ НЕТ MARGINFI АККАУНТОВ!');
        console.log('💡 Рекомендация: Создать новый аккаунт через createMarginfiAccount()');
        return;
      }

      const bestAccount = await this.findBestAccountForFlashLoan(accounts);

      if (!bestAccount) {
        console.log('\n❌ НЕТ ПОДХОДЯЩИХ АККАУНТОВ ДЛЯ FLASH LOANS!');
        return;
      }

      const testResult = await this.testFlashLoanWithAccount(bestAccount);

      console.log('\n🎯 ФИНАЛЬНЫЕ РЕКОМЕНДАЦИИ:');
      console.log('═══════════════════════════════════════════════════════');

      if (testResult) {
        console.log(`✅ ИСПОЛЬЗУЙТЕ АККАУНТ: ${bestAccount.address}`);
        console.log(`📝 Обновите код чтобы использовать этот аккаунт для flash loans`);
      } else {
        console.log(`❌ АККАУНТ ${bestAccount.address} НЕ РАБОТАЕТ`);
        console.log(`💡 Создайте новый чистый аккаунт для flash loans`);
      }

    } catch (error) {
      console.error('❌ Критическая ошибка:', error.message);
    }
  }
}

// Запуск диагностики
if (require.main === module) {
  const diagnostic = new MarginFiAccountDiagnostic();
  diagnostic.run().catch(console.error);
}

module.exports = MarginFiAccountDiagnostic;
