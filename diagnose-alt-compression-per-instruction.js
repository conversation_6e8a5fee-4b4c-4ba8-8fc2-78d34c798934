/**
 * 🔍 ДИАГНОСТИКА ALT СЖАТИЯ ПО КАЖДОЙ ИНСТРУКЦИИ
 * Проверяем какие инструкции НЕ сжимаются ALT таблицами
 */

const { Connection, PublicKey } = require('@solana/web3.js');
const CompleteFlashLoanStructure = require('./complete-flash-loan-structure');
require('dotenv').config({ path: '.env.solana' });

async function diagnoseALTCompressionPerInstruction() {
    console.log('🔍 ДИАГНОСТИКА ALT СЖАТИЯ ПО КАЖДОЙ ИНСТРУКЦИИ');
    console.log('=' .repeat(80));

    try {
        // 1. Создаем структуру транзакции
        const connection = new Connection(process.env.QUICKNODE_RPC_URL);
        const walletData = require('./wallet.json');
        const wallet = require('@solana/web3.js').Keypair.fromSecretKey(new Uint8Array(walletData));

        // MarginFi аккаунт из файла
        const marginfiAccountAddress = '********************************************';

        const flashLoanStructure = new CompleteFlashLoanStructure(wallet, marginfiAccountAddress, connection);
        
        console.log('✅ Создаем полную структуру транзакции...');
        const result = await flashLoanStructure.createCompleteFlashLoanTransactionWithALT();
        
        if (!result || !result.instructions) {
            throw new Error('Не удалось создать структуру транзакции');
        }

        console.log(`📊 Всего инструкций: ${result.instructions.length}`);
        console.log(`📊 ALT таблиц: ${result.altTables ? result.altTables.length : 0}`);

        // 2. Загружаем ALT таблицы
        console.log('\n🔍 ЗАГРУЗКА ALT ТАБЛИЦ...');
        const altAddresses = [
            'HGmknUTUmeovMc9ryERNWG6UFZDFDVr9xrum3ZhyL4fC', // ALT 1 (marginfi1)
            '5FuKF7C1tJji2mXZuJ14U9oDb37is5mmvYLf4KwojoF1', // ALT 2 (marginfi2)
            'FEFhAFKz48P3w82Ds5VhvyEDwhRqu2FejmnuxEPZ8wNR', // ALT 3 (marginfi3)
            'FAeyUf4AdG7vTQ2Q89r9hWBkPdYkykJKG4CxgqArBGXe'  // ALT 4 (кастомная)
        ];

        // Создаем карту всех адресов из ALT таблиц
        const altAddressMap = new Map();
        let totalAltAddresses = 0;

        for (let i = 0; i < altAddresses.length; i++) {
            try {
                const altAccount = await connection.getAddressLookupTable(new PublicKey(altAddresses[i]));
                if (altAccount && altAccount.value) {
                    const addresses = altAccount.value.state.addresses;
                    addresses.forEach((addr, index) => {
                        const addrStr = addr.toString();
                        if (!altAddressMap.has(addrStr)) {
                            altAddressMap.set(addrStr, { table: i, index });
                            totalAltAddresses++;
                        }
                    });
                    console.log(`✅ ALT ${i + 1}: ${addresses.length} адресов`);
                }
            } catch (error) {
                console.log(`❌ ALT ${i + 1}: Ошибка загрузки - ${error.message}`);
            }
        }

        console.log(`📊 Всего уникальных адресов в ALT: ${totalAltAddresses}`);

        // 3. Анализируем каждую инструкцию
        console.log('\n🔍 АНАЛИЗ СЖАТИЯ ПО ИНСТРУКЦИЯМ:');
        console.log('=' .repeat(80));

        let totalKeys = 0;
        let totalCompressed = 0;
        let totalUncompressed = 0;
        let totalInstructionWeight = 0;

        // Функция для расчета веса инструкции
        function calculateInstructionWeight(instruction) {
            let weight = 0;
            weight += 1; // program_id_index
            weight += 1; // accounts length
            weight += instruction.keys.length; // account indexes (1 byte each if ALT compressed, 32 bytes if not)
            weight += 1; // data length
            weight += instruction.data ? instruction.data.length : 0; // data
            return weight;
        }

        for (let i = 0; i < result.instructions.length; i++) {
            const instruction = result.instructions[i];
            const programId = instruction.programId.toString();
            const keys = instruction.keys || [];

            // Расчет веса инструкции
            const baseWeight = calculateInstructionWeight(instruction);
            let compressedWeight = baseWeight;
            let uncompressedWeight = baseWeight;

            console.log(`\n📋 ИНСТРУКЦИЯ ${i.toString().padStart(2, '0')}: ${programId.slice(0, 8)}... (${keys.length} keys)`);

            if (keys.length === 0) {
                console.log('   ✅ Нет ключей для сжатия');
                console.log(`   ⚖️ Вес: ${baseWeight} bytes`);
                totalInstructionWeight += baseWeight;
                continue;
            }

            let compressed = 0;
            let uncompressed = 0;
            const uncompressedKeys = [];

            keys.forEach((key, keyIndex) => {
                const keyStr = key.pubkey.toString();
                const isDynamic = key.isSigner; // Signers обычно динамические

                if (altAddressMap.has(keyStr)) {
                    compressed++;
                    compressedWeight += 1; // ALT сжатый ключ = 1 байт
                    const altInfo = altAddressMap.get(keyStr);
                    console.log(`   ✅ Key ${keyIndex}: ALT[${altInfo.table}][${altInfo.index}] ${keyStr.slice(0,8)}... (1 byte)`);
                } else {
                    uncompressed++;
                    uncompressedWeight += 32; // Несжатый ключ = 32 байта
                    uncompressedKeys.push({
                        index: keyIndex,
                        address: keyStr,
                        isDynamic,
                        isSigner: key.isSigner,
                        isWritable: key.isWritable
                    });
                    const type = isDynamic ? 'DYNAMIC' : 'STATIC';
                    console.log(`   ❌ Key ${keyIndex}: ${type} ${keyStr.slice(0,8)}... ${key.isSigner ? '(SIGNER)' : ''} (32 bytes)`);
                }
            });

            const compressionRate = keys.length > 0 ? (compressed / keys.length * 100).toFixed(1) : 0;
            const weightSavings = uncompressedWeight - compressedWeight;

            console.log(`   📊 Сжатие: ${compressed}/${keys.length} (${compressionRate}%)`);
            console.log(`   ⚖️ Вес БЕЗ ALT: ${uncompressedWeight} bytes`);
            console.log(`   ⚖️ Вес С ALT: ${compressedWeight} bytes`);
            console.log(`   💾 Экономия: ${weightSavings} bytes`);

            if (uncompressed > 0) {
                console.log(`   🚨 НЕ СЖАТО: ${uncompressed} ключей (${uncompressed * 32} bytes)`);
                uncompressedKeys.forEach(key => {
                    if (!key.isDynamic) {
                        console.log(`      🔑 СТАТИЧЕСКИЙ: ${key.address} (можно добавить в ALT)`);
                    }
                });
            }

            totalKeys += keys.length;
            totalCompressed += compressed;
            totalUncompressed += uncompressed;
            totalInstructionWeight += compressedWeight;
        }

        // 4. Общая статистика
        console.log('\n' + '=' .repeat(80));
        console.log('📊 ОБЩАЯ СТАТИСТИКА ALT СЖАТИЯ:');
        console.log('=' .repeat(80));

        const overallCompressionRate = totalKeys > 0 ? (totalCompressed / totalKeys * 100).toFixed(1) : 0;
        const totalUncompressedWeight = totalKeys * 32 + (result.instructions.length * 3); // 32 байта на ключ + базовый вес инструкций
        const totalSavings = totalUncompressedWeight - totalInstructionWeight;

        console.log(`📋 Всего ключей в транзакции: ${totalKeys}`);
        console.log(`✅ Сжато ALT: ${totalCompressed}`);
        console.log(`❌ НЕ сжато: ${totalUncompressed}`);
        console.log(`📈 Общее сжатие: ${overallCompressionRate}%`);
        console.log(`⚖️ Общий вес БЕЗ ALT: ${totalUncompressedWeight} bytes`);
        console.log(`⚖️ Общий вес С ALT: ${totalInstructionWeight} bytes`);
        console.log(`💾 Общая экономия: ${totalSavings} bytes`);

        // Расчет экономии байт
        const byteSavings = totalCompressed * 31; // 32 байта -> 1 байт = экономия 31 байт
        console.log(`💾 Экономия от ALT сжатия: ${byteSavings} bytes`);

        // 5. Рекомендации
        console.log('\n🎯 РЕКОМЕНДАЦИИ:');
        if (totalUncompressed > 0) {
            console.log(`🔧 Добавить ${totalUncompressed} статических ключей в ALT таблицы`);
            console.log(`💰 Потенциальная экономия: ${totalUncompressed * 31} bytes`);
        } else {
            console.log('✅ Все статические ключи уже в ALT таблицах');
        }

        // 6. Топ самых тяжелых инструкций
        console.log('\n🏆 САМЫЕ ТЯЖЕЛЫЕ ИНСТРУКЦИИ:');
        const instructionWeights = [];
        for (let i = 0; i < result.instructions.length; i++) {
            const instruction = result.instructions[i];
            const keys = instruction.keys || [];
            let weight = calculateInstructionWeight(instruction);

            // Добавляем вес ключей с учетом ALT сжатия
            keys.forEach(key => {
                const keyStr = key.pubkey.toString();
                if (altAddressMap.has(keyStr)) {
                    weight += 1; // ALT сжатый
                } else {
                    weight += 32; // Несжатый
                }
            });

            instructionWeights.push({
                index: i,
                programId: instruction.programId.toString().slice(0, 8),
                keyCount: keys.length,
                weight: weight
            });
        }

        // Сортируем по весу и показываем топ-5
        instructionWeights.sort((a, b) => b.weight - a.weight);
        instructionWeights.slice(0, 5).forEach((inst, rank) => {
            console.log(`   ${rank + 1}. Инструкция ${inst.index.toString().padStart(2, '0')}: ${inst.programId}... (${inst.keyCount} keys, ${inst.weight} bytes)`);
        });

        console.log('\n' + '=' .repeat(80));
        console.log('🎉 ДИАГНОСТИКА ЗАВЕРШЕНА!');
        console.log('=' .repeat(80));

    } catch (error) {
        console.error('❌ Ошибка диагностики:', error.message);
        console.error(error.stack);
    }
}

// Запуск диагностики
if (require.main === module) {
    diagnoseALTCompressionPerInstruction();
}

module.exports = { diagnoseALTCompressionPerInstruction };
