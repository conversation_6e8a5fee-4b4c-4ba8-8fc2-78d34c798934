#!/usr/bin/env node

/**
 * 🔧 DLMM ERROR FIXER
 * 
 * АВТОМАТИЧЕСКОЕ ИСПРАВЛЕНИЕ ОШИБОК НАЙДЕННЫХ СИМУЛЯТОРОМ
 */

const { 
    Connection, 
    PublicKey, 
    Keypair, 
    Transaction,
    TransactionInstruction,
    SystemProgram,
    SYSVAR_RENT_PUBKEY
} = require('@solana/web3.js');
const { 
    TOKEN_PROGRAM_ID,
    createInitializeAccountInstruction,
    getMinimumBalanceForRentExemptAccount
} = require('@solana/spl-token');
const bs58 = require('bs58');
const BN = require('bn.js');

const DLMMTransactionSimulator = require('./dlmm-transaction-simulator.js');

require('dotenv').config();

class DLMMErrorFixer {
    constructor() {
        // 🧪 СИМУЛЯТОР
        this.simulator = new DLMMTransactionSimulator();
        
        // 🌐 CONNECTION
        this.connection = new Connection(
            process.env.QUICKNODE_RPC_URL || process.env.SOLANA_RPC_URL,
            'confirmed'
        );
        
        // 🔑 WALLET
        this.wallet = null;
        
        // 📊 ПРОГРАММЫ
        this.PROGRAMS = {
            METEORA_DLMM: new PublicKey('Eo7WjKq67rjJQSZxS6z3YkapzY3eMj6Xy8X5EQVn5UaB'),
            MARGINFI: new PublicKey('MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZPxwGdHdC')
        };
        
        console.log('🔧 DLMM ERROR FIXER ИНИЦИАЛИЗИРОВАН');
    }

    /**
     * 🔑 ИНИЦИАЛИЗАЦИЯ
     */
    async initialize() {
        console.log('\n🔑 ИНИЦИАЛИЗАЦИЯ ERROR FIXER...');
        
        const privateKeyBytes = bs58.default.decode(process.env.WALLET_PRIVATE_KEY);
        this.wallet = Keypair.fromSecretKey(privateKeyBytes);
        
        console.log(`   Кошелек: ${this.wallet.publicKey.toString()}`);
        console.log('   ✅ Error Fixer готов');
    }

    /**
     * 🔍 ПОИСК ПРАВИЛЬНЫХ DLMM ПУЛОВ
     */
    async findCorrectDLMMPools() {
        console.log('\n🔍 ПОИСК ПРАВИЛЬНЫХ DLMM ПУЛОВ...');
        
        try {
            // Известные DLMM пулы SOL/USDC
            const knownPools = [
                'ARwi1S4DaiTG5DX7S4M4ZsrXqpMD1MrTmbu9ue2tpmEq', // SOL/USDC DLMM
                '58oQChx4yWmvKdwLLZzBi4ChoCc2fqCUWBkwMihLYQo2',  // SOL/USDC DLMM
                'BbZjQanvSaE9me4adAitmTTaSgASVDiVAKkLTyTdEGZu',  // SOL/USDC DLMM
                'FwEHs3kJEdMa2qZHv7SgzCiFXUQPEycEXksfBkwmS8gj'   // SOL/USDC DLMM
            ];
            
            const validPools = [];
            
            for (const poolAddress of knownPools) {
                try {
                    const poolPubkey = new PublicKey(poolAddress);
                    const poolInfo = await this.connection.getAccountInfo(poolPubkey);
                    
                    if (poolInfo && poolInfo.owner.toString() === this.PROGRAMS.METEORA_DLMM.toString()) {
                        console.log(`   ✅ Найден валидный DLMM пул: ${poolAddress}`);
                        
                        validPools.push({
                            address: poolAddress,
                            pubkey: poolPubkey,
                            dataLength: poolInfo.data.length,
                            lamports: poolInfo.lamports
                        });
                    }
                } catch (error) {
                    console.log(`   ❌ Ошибка проверки ${poolAddress}: ${error.message}`);
                }
            }
            
            console.log(`   📊 Найдено валидных DLMM пулов: ${validPools.length}`);
            
            return validPools;
            
        } catch (error) {
            console.error('❌ ОШИБКА ПОИСКА ПУЛОВ:', error.message);
            return [];
        }
    }

    /**
     * 🏗️ СОЗДАНИЕ ИНСТРУКЦИИ ДЛЯ BIN ARRAY
     */
    createBinArrayInstruction(poolAddress, binArrayIndex) {
        console.log(`   🏗️ Создание Bin Array ${binArrayIndex}...`);
        
        try {
            // Рассчитываем PDA для Bin Array
            const [binArrayPDA] = PublicKey.findProgramAddressSync(
                [
                    Buffer.from('bin_array'),
                    new PublicKey(poolAddress).toBuffer(),
                    new BN(binArrayIndex).toArrayLike(Buffer, 'le', 8)
                ],
                this.PROGRAMS.METEORA_DLMM
            );
            
            // Создаем инструкцию инициализации Bin Array
            const initBinArrayData = Buffer.alloc(16);
            initBinArrayData.writeUInt8(10, 0); // Initialize Bin Array discriminator
            initBinArrayData.writeBigInt64LE(BigInt(binArrayIndex), 8);
            
            const instruction = new TransactionInstruction({
                programId: this.PROGRAMS.METEORA_DLMM,
                keys: [
                    { pubkey: this.wallet.publicKey, isSigner: true, isWritable: true },
                    { pubkey: binArrayPDA, isSigner: false, isWritable: true },
                    { pubkey: new PublicKey(poolAddress), isSigner: false, isWritable: false },
                    { pubkey: SystemProgram.programId, isSigner: false, isWritable: false },
                    { pubkey: SYSVAR_RENT_PUBKEY, isSigner: false, isWritable: false }
                ],
                data: initBinArrayData
            });
            
            console.log(`      ✅ Bin Array ${binArrayIndex} инструкция создана`);
            console.log(`      📍 PDA: ${binArrayPDA.toString()}`);
            
            return {
                instruction: instruction,
                pda: binArrayPDA,
                index: binArrayIndex
            };
            
        } catch (error) {
            console.error(`      ❌ Ошибка создания Bin Array ${binArrayIndex}:`, error.message);
            return null;
        }
    }

    /**
     * 🏗️ СОЗДАНИЕ ИНСТРУКЦИИ ДЛЯ POSITION
     */
    createPositionInstruction(poolAddress) {
        console.log(`   🏗️ Создание Position...`);
        
        try {
            // Рассчитываем PDA для Position
            const [positionPDA] = PublicKey.findProgramAddressSync(
                [
                    Buffer.from('position'),
                    new PublicKey(poolAddress).toBuffer(),
                    this.wallet.publicKey.toBuffer()
                ],
                this.PROGRAMS.METEORA_DLMM
            );
            
            // Создаем инструкцию инициализации Position
            const initPositionData = Buffer.alloc(8);
            initPositionData.writeUInt8(11, 0); // Initialize Position discriminator
            
            const instruction = new TransactionInstruction({
                programId: this.PROGRAMS.METEORA_DLMM,
                keys: [
                    { pubkey: this.wallet.publicKey, isSigner: true, isWritable: true },
                    { pubkey: positionPDA, isSigner: false, isWritable: true },
                    { pubkey: new PublicKey(poolAddress), isSigner: false, isWritable: false },
                    { pubkey: SystemProgram.programId, isSigner: false, isWritable: false },
                    { pubkey: SYSVAR_RENT_PUBKEY, isSigner: false, isWritable: false }
                ],
                data: initPositionData
            });
            
            console.log(`      ✅ Position инструкция создана`);
            console.log(`      📍 PDA: ${positionPDA.toString()}`);
            
            return {
                instruction: instruction,
                pda: positionPDA
            };
            
        } catch (error) {
            console.error(`      ❌ Ошибка создания Position:`, error.message);
            return null;
        }
    }

    /**
     * 🏗️ СОЗДАНИЕ ИНСТРУКЦИИ ДЛЯ MARGINFI ACCOUNT
     */
    createMarginFiAccountInstruction() {
        console.log(`   🏗️ Создание MarginFi Account...`);
        
        try {
            // Рассчитываем PDA для MarginFi Account
            const [marginfiAccountPDA] = PublicKey.findProgramAddressSync(
                [
                    Buffer.from('marginfi_account'),
                    this.wallet.publicKey.toBuffer(),
                    new BN(0).toArrayLike(Buffer, 'le', 8)
                ],
                this.PROGRAMS.MARGINFI
            );
            
            // MarginFi Group (известный адрес)
            const marginfiGroup = new PublicKey('4qp6Fx6tnZkY5Wropq9wUYgtFxXKwE6viZxFHg3rdAG8');
            
            // Создаем инструкцию инициализации MarginFi Account
            const initAccountData = Buffer.alloc(8);
            initAccountData.writeUInt8(0, 0); // Initialize Account discriminator
            
            const instruction = new TransactionInstruction({
                programId: this.PROGRAMS.MARGINFI,
                keys: [
                    { pubkey: this.wallet.publicKey, isSigner: true, isWritable: true },
                    { pubkey: marginfiAccountPDA, isSigner: false, isWritable: true },
                    { pubkey: marginfiGroup, isSigner: false, isWritable: false },
                    { pubkey: SystemProgram.programId, isSigner: false, isWritable: false },
                    { pubkey: SYSVAR_RENT_PUBKEY, isSigner: false, isWritable: false }
                ],
                data: initAccountData
            });
            
            console.log(`      ✅ MarginFi Account инструкция создана`);
            console.log(`      📍 PDA: ${marginfiAccountPDA.toString()}`);
            
            return {
                instruction: instruction,
                pda: marginfiAccountPDA
            };
            
        } catch (error) {
            console.error(`      ❌ Ошибка создания MarginFi Account:`, error.message);
            return null;
        }
    }

    /**
     * 🔧 АВТОМАТИЧЕСКОЕ ИСПРАВЛЕНИЕ ОШИБОК
     */
    async autoFixErrors() {
        console.log('\n🔧 АВТОМАТИЧЕСКОЕ ИСПРАВЛЕНИЕ ОШИБОК');
        console.log('=' .repeat(80));
        
        try {
            // 1. Инициализация
            await this.initialize();
            
            // 2. Запуск симуляции для получения ошибок
            console.log('\n🧪 ЗАПУСК СИМУЛЯЦИИ ДЛЯ АНАЛИЗА ОШИБОК...');
            const simulationResult = await this.simulator.runFullSimulation();
            
            if (!simulationResult.success) {
                throw new Error('Симуляция провалена');
            }
            
            const { fixes, results } = simulationResult;
            
            // 3. Поиск правильных пулов
            const validPools = await this.findCorrectDLMMPools();
            if (validPools.length === 0) {
                throw new Error('Не найдены валидные DLMM пулы');
            }
            
            const selectedPool = validPools[0]; // Используем первый найденный пул
            console.log(`\n🎯 Используем пул: ${selectedPool.address}`);
            
            // 4. Создание исправляющих инструкций
            console.log('\n🏗️ СОЗДАНИЕ ИСПРАВЛЯЮЩИХ ИНСТРУКЦИЙ...');
            
            const fixInstructions = [];
            
            // MarginFi Account (должен быть первым)
            if (fixes.some(f => f.type === 'CREATE_MARGINFI_ACCOUNT')) {
                const marginfiInstruction = this.createMarginFiAccountInstruction();
                if (marginfiInstruction) {
                    fixInstructions.push(marginfiInstruction);
                }
            }
            
            // Bin Arrays
            const binArrayFixes = fixes.filter(f => f.type === 'CREATE_BIN_ARRAY');
            for (let i = -1; i <= 1; i++) {
                const binArrayInstruction = this.createBinArrayInstruction(selectedPool.address, i);
                if (binArrayInstruction) {
                    fixInstructions.push(binArrayInstruction);
                }
            }
            
            // Position
            if (fixes.some(f => f.type === 'CREATE_POSITION')) {
                const positionInstruction = this.createPositionInstruction(selectedPool.address);
                if (positionInstruction) {
                    fixInstructions.push(positionInstruction);
                }
            }
            
            // 5. Создание транзакции исправлений
            console.log('\n🎯 СОЗДАНИЕ ТРАНЗАКЦИИ ИСПРАВЛЕНИЙ...');
            
            const fixTransaction = new Transaction();
            fixInstructions.forEach(fix => {
                fixTransaction.add(fix.instruction);
            });
            
            console.log(`   ✅ Транзакция исправлений создана`);
            console.log(`   📊 Инструкций: ${fixTransaction.instructions.length}`);
            
            // 6. Симуляция транзакции исправлений
            console.log('\n🧪 СИМУЛЯЦИЯ ТРАНЗАКЦИИ ИСПРАВЛЕНИЙ...');
            
            const { blockhash } = await this.connection.getLatestBlockhash();
            fixTransaction.recentBlockhash = blockhash;
            fixTransaction.feePayer = this.wallet.publicKey;
            fixTransaction.sign(this.wallet);
            
            const fixSimulation = await this.connection.simulateTransaction(fixTransaction, {
                sigVerify: false,
                commitment: 'processed'
            });
            
            if (fixSimulation.value.err) {
                console.log('   ❌ Симуляция исправлений провалена');
                console.log(`      Ошибка: ${JSON.stringify(fixSimulation.value.err)}`);
            } else {
                console.log('   ✅ Симуляция исправлений успешна');
                console.log(`      Compute Units: ${fixSimulation.value.unitsConsumed || 'N/A'}`);
            }
            
            return {
                success: true,
                fixTransaction: fixTransaction,
                fixInstructions: fixInstructions,
                selectedPool: selectedPool,
                simulationResult: fixSimulation.value
            };
            
        } catch (error) {
            console.error('💥 ОШИБКА АВТОИСПРАВЛЕНИЯ:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }
}

// 🧪 ЗАПУСК
if (require.main === module) {
    async function main() {
        const fixer = new DLMMErrorFixer();
        const result = await fixer.autoFixErrors();
        
        if (result.success) {
            console.log('\n🎉 АВТОИСПРАВЛЕНИЕ УСПЕШНО!');
            console.log(`🏗️ Создано инструкций: ${result.fixInstructions.length}`);
            console.log(`🎯 Выбранный пул: ${result.selectedPool.address}`);
            
            if (!result.simulationResult.err) {
                console.log('✅ ТРАНЗАКЦИЯ ИСПРАВЛЕНИЙ ГОТОВА К ВЫПОЛНЕНИЮ!');
            } else {
                console.log('⚠️ ТРЕБУЕТСЯ ДОПОЛНИТЕЛЬНАЯ НАСТРОЙКА');
            }
        } else {
            console.log('\n❌ АВТОИСПРАВЛЕНИЕ ПРОВАЛЕНО!');
            console.log(`❌ Ошибка: ${result.error}`);
        }
    }
    
    main().catch(console.error);
}

module.exports = DLMMErrorFixer;
