#!/usr/bin/env node

/**
 * 🔥 DLMM TRANSACTION ASSEMBLER
 * 
 * ПОШАГОВЫЙ СБОРЩИК ВСЕЙ DLMM ТРАНЗАКЦИИ
 * Показывает КАК и ЧТО загружается на каждом этапе
 */

const { 
    Connection, 
    PublicKey, 
    Keypair, 
    Transaction, 
    TransactionInstruction,
    SystemProgram,
    SYSVAR_RENT_PUBKEY,
    SYSVAR_CLOCK_PUBKEY
} = require('@solana/web3.js');
const { 
    TOKEN_PROGRAM_ID, 
    ASSOCIATED_TOKEN_PROGRAM_ID,
    getAssociatedTokenAddress,
    createAssociatedTokenAccountInstruction,
    createTransferInstruction
} = require('@solana/spl-token');
const bs58 = require('bs58');
const BN = require('bn.js');

require('dotenv').config();

class DLMMTransactionAssembler {
    constructor() {
        // 🌐 CONNECTION
        this.connection = new Connection(
            process.env.QUICKNODE_RPC_URL || process.env.SOLANA_RPC_URL,
            'confirmed'
        );
        
        // 🔑 WALLET
        this.wallet = null;
        
        // 📊 ПРОГРАММЫ И АДРЕСА
        this.PROGRAMS = {
            SYSTEM: SystemProgram.programId,
            TOKEN: TOKEN_PROGRAM_ID,
            ASSOCIATED_TOKEN: ASSOCIATED_TOKEN_PROGRAM_ID,
            METEORA_DLMM: new PublicKey('Eo7WjKq67rjJQSZxS6z3YkapzY3eMj6Xy8X5EQVn5UaB'),
            MARGINFI: new PublicKey('MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZPxwGdHdC'),
            JUPITER: new PublicKey('JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4')
        };
        
        // 🪙 ТОКЕНЫ
        this.TOKENS = {
            USDC: new PublicKey('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'),
            SOL: new PublicKey('So11111111111111111111111111111111111111112')
        };
        
        // 🏊 ПУЛЫ
        this.POOLS = {
            LARGE_POOL: new PublicKey('AVs9TA4nWDzfPJE9gGVNJMVhcQy3V9PGazuz33BfG2RA'),
            MEDIUM_POOL: new PublicKey('BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y')
        };
        
        // 💰 ПАРАМЕТРЫ СТРАТЕГИИ
        this.STRATEGY = {
            flash_loan: 1820000 * 1e6,      // 1.82M USDC (в микро-единицах)
            liquidity_add: 1400000 * 1e6,   // 1.4M USDC
            trading_amount: 420000 * 1e6,   // 420K USDC
            sol_amount: 2456 * 1e9          // 2456 SOL (в lamports)
        };
        
        // 📋 ИНСТРУКЦИИ
        this.instructions = [];
        this.accounts = new Map();
        
        console.log('🔥 DLMM TRANSACTION ASSEMBLER ИНИЦИАЛИЗИРОВАН');
    }

    /**
     * 🔑 ИНИЦИАЛИЗАЦИЯ КОШЕЛЬКА
     */
    async initializeWallet() {
        console.log('\n🔑 ИНИЦИАЛИЗАЦИЯ КОШЕЛЬКА...');
        
        const privateKeyBytes = bs58.default.decode(process.env.WALLET_PRIVATE_KEY);
        this.wallet = Keypair.fromSecretKey(privateKeyBytes);
        
        console.log(`   Адрес: ${this.wallet.publicKey.toString()}`);
        console.log('   ✅ Кошелек готов');
    }

    /**
     * 📊 ЗАГРУЗКА АККАУНТОВ
     */
    async loadAccounts() {
        console.log('\n📊 ЗАГРУЗКА ВСЕХ НЕОБХОДИМЫХ АККАУНТОВ...');
        
        try {
            // 1. ОСНОВНЫЕ АККАУНТЫ ПОЛЬЗОВАТЕЛЯ
            console.log('   1️⃣ Загрузка аккаунтов пользователя...');
            
            // USDC аккаунт пользователя
            const userUSDC = await getAssociatedTokenAddress(
                this.TOKENS.USDC,
                this.wallet.publicKey
            );
            this.accounts.set('USER_USDC', userUSDC);
            console.log(`      USER_USDC: ${userUSDC.toString()}`);
            
            // SOL аккаунт пользователя (wrapped SOL)
            const userSOL = await getAssociatedTokenAddress(
                this.TOKENS.SOL,
                this.wallet.publicKey
            );
            this.accounts.set('USER_SOL', userSOL);
            console.log(`      USER_SOL: ${userSOL.toString()}`);
            
            // 2. АККАУНТЫ ПУЛОВ
            console.log('   2️⃣ Загрузка аккаунтов пулов...');
            
            // Большой пул (для покупки SOL)
            this.accounts.set('LARGE_POOL', this.POOLS.LARGE_POOL);
            console.log(`      LARGE_POOL: ${this.POOLS.LARGE_POOL.toString()}`);
            
            // Средний пул (наша ликвидность)
            this.accounts.set('MEDIUM_POOL', this.POOLS.MEDIUM_POOL);
            console.log(`      MEDIUM_POOL: ${this.POOLS.MEDIUM_POOL.toString()}`);
            
            // 3. PDA АККАУНТЫ ДЛЯ DLMM
            console.log('   3️⃣ Расчет PDA аккаунтов для DLMM...');
            
            // Bin Array аккаунты
            const binArrays = await this.calculateBinArrayPDAs();
            binArrays.forEach((binArray, index) => {
                this.accounts.set(`BIN_ARRAY_${index}`, binArray);
                console.log(`      BIN_ARRAY_${index}: ${binArray.toString()}`);
            });
            
            // Position аккаунт
            const [positionPDA] = PublicKey.findProgramAddressSync(
                [
                    Buffer.from('position'),
                    this.POOLS.MEDIUM_POOL.toBuffer(),
                    this.wallet.publicKey.toBuffer()
                ],
                this.PROGRAMS.METEORA_DLMM
            );
            this.accounts.set('POSITION', positionPDA);
            console.log(`      POSITION: ${positionPDA.toString()}`);
            
            // 4. MARGINFI АККАУНТЫ
            console.log('   4️⃣ Расчет MarginFi аккаунтов...');
            
            // MarginFi Account
            const [marginfiAccount] = PublicKey.findProgramAddressSync(
                [
                    Buffer.from('marginfi_account'),
                    this.wallet.publicKey.toBuffer(),
                    new BN(0).toArrayLike(Buffer, 'le', 8)
                ],
                this.PROGRAMS.MARGINFI
            );
            this.accounts.set('MARGINFI_ACCOUNT', marginfiAccount);
            console.log(`      MARGINFI_ACCOUNT: ${marginfiAccount.toString()}`);
            
            // MarginFi Group
            const marginfiGroup = new PublicKey('4qp6Fx6tnZkY5Wropq9wUYgtFxXKwE6viZxFHg3rdAG8');
            this.accounts.set('MARGINFI_GROUP', marginfiGroup);
            console.log(`      MARGINFI_GROUP: ${marginfiGroup.toString()}`);
            
            // 5. JUPITER АККАУНТЫ
            console.log('   5️⃣ Jupiter аккаунты...');
            
            // Jupiter Program
            this.accounts.set('JUPITER_PROGRAM', this.PROGRAMS.JUPITER);
            console.log(`      JUPITER_PROGRAM: ${this.PROGRAMS.JUPITER.toString()}`);
            
            console.log('   ✅ Все аккаунты загружены');
            console.log(`   📊 Всего аккаунтов: ${this.accounts.size}`);
            
        } catch (error) {
            console.error('❌ ОШИБКА ЗАГРУЗКИ АККАУНТОВ:', error.message);
            throw error;
        }
    }

    /**
     * 🗂️ РАСЧЕТ BIN ARRAY PDA
     */
    async calculateBinArrayPDAs() {
        console.log('      📊 Расчет Bin Array PDA...');
        
        const binArrays = [];
        
        // Для нашей стратегии нужны bin arrays для диапазона -10 до +10
        for (let binArrayIndex = -1; binArrayIndex <= 1; binArrayIndex++) {
            const [binArrayPDA] = PublicKey.findProgramAddressSync(
                [
                    Buffer.from('bin_array'),
                    this.POOLS.MEDIUM_POOL.toBuffer(),
                    new BN(binArrayIndex).toArrayLike(Buffer, 'le', 8)
                ],
                this.PROGRAMS.METEORA_DLMM
            );
            
            binArrays.push(binArrayPDA);
            console.log(`         Bin Array ${binArrayIndex}: ${binArrayPDA.toString()}`);
        }
        
        return binArrays;
    }

    /**
     * 🏗️ СБОРКА ИНСТРУКЦИИ 1: FLASH LOAN
     */
    buildFlashLoanInstruction() {
        console.log('\n🏗️ СБОРКА ИНСТРУКЦИИ 1: FLASH LOAN...');
        
        try {
            console.log('   📊 Параметры Flash Loan:');
            console.log(`      Сумма: ${this.STRATEGY.flash_loan / 1e6} USDC`);
            console.log(`      Программа: ${this.PROGRAMS.MARGINFI.toString()}`);
            
            // MarginFi Flash Loan инструкция
            const flashLoanData = Buffer.alloc(16);
            flashLoanData.writeUInt8(0, 0); // Flash Loan instruction discriminator
            flashLoanData.writeBigUInt64LE(BigInt(this.STRATEGY.flash_loan), 8);
            
            const flashLoanInstruction = new TransactionInstruction({
                programId: this.PROGRAMS.MARGINFI,
                keys: [
                    { pubkey: this.wallet.publicKey, isSigner: true, isWritable: false },
                    { pubkey: this.accounts.get('MARGINFI_ACCOUNT'), isSigner: false, isWritable: true },
                    { pubkey: this.accounts.get('MARGINFI_GROUP'), isSigner: false, isWritable: false },
                    { pubkey: this.accounts.get('USER_USDC'), isSigner: false, isWritable: true },
                    { pubkey: this.TOKENS.USDC, isSigner: false, isWritable: false },
                    { pubkey: this.PROGRAMS.TOKEN, isSigner: false, isWritable: false },
                    { pubkey: SYSVAR_CLOCK_PUBKEY, isSigner: false, isWritable: false }
                ],
                data: flashLoanData
            });
            
            this.instructions.push(flashLoanInstruction);
            
            console.log('   ✅ Flash Loan инструкция собрана');
            console.log(`      Аккаунтов: ${flashLoanInstruction.keys.length}`);
            console.log(`      Данных: ${flashLoanInstruction.data.length} байт`);
            
            return flashLoanInstruction;
            
        } catch (error) {
            console.error('❌ ОШИБКА СБОРКИ FLASH LOAN:', error.message);
            throw error;
        }
    }

    /**
     * 🏗️ СБОРКА ИНСТРУКЦИИ 2: ADD LIQUIDITY
     */
    buildAddLiquidityInstruction() {
        console.log('\n🏗️ СБОРКА ИНСТРУКЦИИ 2: ADD LIQUIDITY...');
        
        try {
            console.log('   📊 Параметры Add Liquidity:');
            console.log(`      Сумма: ${this.STRATEGY.liquidity_add / 1e6} USDC`);
            console.log(`      Пул: ${this.accounts.get('MEDIUM_POOL').toString()}`);
            
            // Meteora DLMM Add Liquidity инструкция
            const addLiquidityData = Buffer.alloc(24);
            addLiquidityData.writeUInt8(1, 0); // Add Liquidity instruction discriminator
            addLiquidityData.writeBigUInt64LE(BigInt(this.STRATEGY.liquidity_add), 8);
            addLiquidityData.writeBigUInt64LE(BigInt(0), 16); // SOL amount (0 for single-sided)
            
            const addLiquidityInstruction = new TransactionInstruction({
                programId: this.PROGRAMS.METEORA_DLMM,
                keys: [
                    { pubkey: this.wallet.publicKey, isSigner: true, isWritable: false },
                    { pubkey: this.accounts.get('MEDIUM_POOL'), isSigner: false, isWritable: true },
                    { pubkey: this.accounts.get('POSITION'), isSigner: false, isWritable: true },
                    { pubkey: this.accounts.get('USER_USDC'), isSigner: false, isWritable: true },
                    { pubkey: this.accounts.get('USER_SOL'), isSigner: false, isWritable: true },
                    { pubkey: this.accounts.get('BIN_ARRAY_0'), isSigner: false, isWritable: true },
                    { pubkey: this.accounts.get('BIN_ARRAY_1'), isSigner: false, isWritable: true },
                    { pubkey: this.accounts.get('BIN_ARRAY_2'), isSigner: false, isWritable: true },
                    { pubkey: this.TOKENS.USDC, isSigner: false, isWritable: false },
                    { pubkey: this.TOKENS.SOL, isSigner: false, isWritable: false },
                    { pubkey: this.PROGRAMS.TOKEN, isSigner: false, isWritable: false },
                    { pubkey: SYSVAR_RENT_PUBKEY, isSigner: false, isWritable: false }
                ],
                data: addLiquidityData
            });
            
            this.instructions.push(addLiquidityInstruction);
            
            console.log('   ✅ Add Liquidity инструкция собрана');
            console.log(`      Аккаунтов: ${addLiquidityInstruction.keys.length}`);
            console.log(`      Данных: ${addLiquidityInstruction.data.length} байт`);
            
            return addLiquidityInstruction;
            
        } catch (error) {
            console.error('❌ ОШИБКА СБОРКИ ADD LIQUIDITY:', error.message);
            throw error;
        }
    }

    /**
     * 🏗️ СБОРКА ИНСТРУКЦИИ 3: BUY SOL
     */
    buildBuySOLInstruction() {
        console.log('\n🏗️ СБОРКА ИНСТРУКЦИИ 3: BUY SOL...');
        
        try {
            console.log('   📊 Параметры Buy SOL:');
            console.log(`      Сумма: ${this.STRATEGY.trading_amount / 1e6} USDC`);
            console.log(`      Получим: ${this.STRATEGY.sol_amount / 1e9} SOL`);
            console.log(`      Пул: ${this.accounts.get('LARGE_POOL').toString()}`);
            
            // Jupiter Swap инструкция
            const swapData = Buffer.alloc(32);
            swapData.writeUInt8(2, 0); // Swap instruction discriminator
            swapData.writeBigUInt64LE(BigInt(this.STRATEGY.trading_amount), 8);
            swapData.writeBigUInt64LE(BigInt(this.STRATEGY.sol_amount), 16);
            swapData.writeBigUInt64LE(BigInt(Math.floor(this.STRATEGY.sol_amount * 0.99)), 24); // Min out with 1% slippage
            
            const buySOLInstruction = new TransactionInstruction({
                programId: this.PROGRAMS.JUPITER,
                keys: [
                    { pubkey: this.wallet.publicKey, isSigner: true, isWritable: false },
                    { pubkey: this.accounts.get('USER_USDC'), isSigner: false, isWritable: true },
                    { pubkey: this.accounts.get('USER_SOL'), isSigner: false, isWritable: true },
                    { pubkey: this.accounts.get('LARGE_POOL'), isSigner: false, isWritable: true },
                    { pubkey: this.TOKENS.USDC, isSigner: false, isWritable: false },
                    { pubkey: this.TOKENS.SOL, isSigner: false, isWritable: false },
                    { pubkey: this.PROGRAMS.TOKEN, isSigner: false, isWritable: false }
                ],
                data: swapData
            });
            
            this.instructions.push(buySOLInstruction);
            
            console.log('   ✅ Buy SOL инструкция собрана');
            console.log(`      Аккаунтов: ${buySOLInstruction.keys.length}`);
            console.log(`      Данных: ${buySOLInstruction.data.length} байт`);
            
            return buySOLInstruction;
            
        } catch (error) {
            console.error('❌ ОШИБКА СБОРКИ BUY SOL:', error.message);
            throw error;
        }
    }

    /**
     * 🏗️ СБОРКА ИНСТРУКЦИИ 4: SELL SOL
     */
    buildSellSOLInstruction() {
        console.log('\n🏗️ СБОРКА ИНСТРУКЦИИ 4: SELL SOL...');
        
        try {
            console.log('   📊 Параметры Sell SOL:');
            console.log(`      Продаем: ${this.STRATEGY.sol_amount / 1e9} SOL`);
            console.log(`      Получим: ~${(this.STRATEGY.trading_amount * 1.1) / 1e6} USDC`);
            console.log(`      Пул: ${this.accounts.get('MEDIUM_POOL').toString()}`);
            
            // Meteora DLMM Swap инструкция
            const sellData = Buffer.alloc(24);
            sellData.writeUInt8(3, 0); // Swap instruction discriminator
            sellData.writeBigUInt64LE(BigInt(this.STRATEGY.sol_amount), 8);
            sellData.writeBigUInt64LE(BigInt(Math.floor(this.STRATEGY.trading_amount * 1.05)), 16); // Min out with profit
            
            const sellSOLInstruction = new TransactionInstruction({
                programId: this.PROGRAMS.METEORA_DLMM,
                keys: [
                    { pubkey: this.wallet.publicKey, isSigner: true, isWritable: false },
                    { pubkey: this.accounts.get('MEDIUM_POOL'), isSigner: false, isWritable: true },
                    { pubkey: this.accounts.get('USER_SOL'), isSigner: false, isWritable: true },
                    { pubkey: this.accounts.get('USER_USDC'), isSigner: false, isWritable: true },
                    { pubkey: this.accounts.get('BIN_ARRAY_0'), isSigner: false, isWritable: true },
                    { pubkey: this.accounts.get('BIN_ARRAY_1'), isSigner: false, isWritable: true },
                    { pubkey: this.accounts.get('BIN_ARRAY_2'), isSigner: false, isWritable: true },
                    { pubkey: this.TOKENS.SOL, isSigner: false, isWritable: false },
                    { pubkey: this.TOKENS.USDC, isSigner: false, isWritable: false },
                    { pubkey: this.PROGRAMS.TOKEN, isSigner: false, isWritable: false }
                ],
                data: sellData
            });
            
            this.instructions.push(sellSOLInstruction);
            
            console.log('   ✅ Sell SOL инструкция собрана');
            console.log(`      Аккаунтов: ${sellSOLInstruction.keys.length}`);
            console.log(`      Данных: ${sellSOLInstruction.data.length} байт`);
            
            return sellSOLInstruction;
            
        } catch (error) {
            console.error('❌ ОШИБКА СБОРКИ SELL SOL:', error.message);
            throw error;
        }
    }

    /**
     * 🏗️ СБОРКА ИНСТРУКЦИИ 5: REMOVE LIQUIDITY
     */
    buildRemoveLiquidityInstruction() {
        console.log('\n🏗️ СБОРКА ИНСТРУКЦИИ 5: REMOVE LIQUIDITY...');
        
        try {
            console.log('   📊 Параметры Remove Liquidity:');
            console.log(`      Убираем всю ликвидность из позиции`);
            console.log(`      Пул: ${this.accounts.get('MEDIUM_POOL').toString()}`);
            
            // Meteora DLMM Remove Liquidity инструкция
            const removeLiquidityData = Buffer.alloc(16);
            removeLiquidityData.writeUInt8(4, 0); // Remove Liquidity instruction discriminator
            removeLiquidityData.writeBigUInt64LE(BigInt(100), 8); // Remove 100% of liquidity
            
            const removeLiquidityInstruction = new TransactionInstruction({
                programId: this.PROGRAMS.METEORA_DLMM,
                keys: [
                    { pubkey: this.wallet.publicKey, isSigner: true, isWritable: false },
                    { pubkey: this.accounts.get('MEDIUM_POOL'), isSigner: false, isWritable: true },
                    { pubkey: this.accounts.get('POSITION'), isSigner: false, isWritable: true },
                    { pubkey: this.accounts.get('USER_USDC'), isSigner: false, isWritable: true },
                    { pubkey: this.accounts.get('USER_SOL'), isSigner: false, isWritable: true },
                    { pubkey: this.accounts.get('BIN_ARRAY_0'), isSigner: false, isWritable: true },
                    { pubkey: this.accounts.get('BIN_ARRAY_1'), isSigner: false, isWritable: true },
                    { pubkey: this.accounts.get('BIN_ARRAY_2'), isSigner: false, isWritable: true },
                    { pubkey: this.TOKENS.USDC, isSigner: false, isWritable: false },
                    { pubkey: this.TOKENS.SOL, isSigner: false, isWritable: false },
                    { pubkey: this.PROGRAMS.TOKEN, isSigner: false, isWritable: false }
                ],
                data: removeLiquidityData
            });
            
            this.instructions.push(removeLiquidityInstruction);
            
            console.log('   ✅ Remove Liquidity инструкция собрана');
            console.log(`      Аккаунтов: ${removeLiquidityInstruction.keys.length}`);
            console.log(`      Данных: ${removeLiquidityInstruction.data.length} байт`);
            
            return removeLiquidityInstruction;
            
        } catch (error) {
            console.error('❌ ОШИБКА СБОРКИ REMOVE LIQUIDITY:', error.message);
            throw error;
        }
    }

    /**
     * 🏗️ СБОРКА ИНСТРУКЦИИ 6: REPAY FLASH LOAN
     */
    buildRepayFlashLoanInstruction() {
        console.log('\n🏗️ СБОРКА ИНСТРУКЦИИ 6: REPAY FLASH LOAN...');
        
        try {
            const repayAmount = this.STRATEGY.flash_loan + (22239 * 1e6); // Loan + profit
            
            console.log('   📊 Параметры Repay Flash Loan:');
            console.log(`      Возвращаем: ${repayAmount / 1e6} USDC`);
            console.log(`      Прибыль: ${22239} USDC`);
            
            // MarginFi Repay Flash Loan инструкция
            const repayData = Buffer.alloc(16);
            repayData.writeUInt8(5, 0); // Repay Flash Loan instruction discriminator
            repayData.writeBigUInt64LE(BigInt(repayAmount), 8);
            
            const repayInstruction = new TransactionInstruction({
                programId: this.PROGRAMS.MARGINFI,
                keys: [
                    { pubkey: this.wallet.publicKey, isSigner: true, isWritable: false },
                    { pubkey: this.accounts.get('MARGINFI_ACCOUNT'), isSigner: false, isWritable: true },
                    { pubkey: this.accounts.get('MARGINFI_GROUP'), isSigner: false, isWritable: false },
                    { pubkey: this.accounts.get('USER_USDC'), isSigner: false, isWritable: true },
                    { pubkey: this.TOKENS.USDC, isSigner: false, isWritable: false },
                    { pubkey: this.PROGRAMS.TOKEN, isSigner: false, isWritable: false }
                ],
                data: repayData
            });
            
            this.instructions.push(repayInstruction);
            
            console.log('   ✅ Repay Flash Loan инструкция собрана');
            console.log(`      Аккаунтов: ${repayInstruction.keys.length}`);
            console.log(`      Данных: ${repayInstruction.data.length} байт`);
            
            return repayInstruction;
            
        } catch (error) {
            console.error('❌ ОШИБКА СБОРКИ REPAY FLASH LOAN:', error.message);
            throw error;
        }
    }

    /**
     * 🎯 ПОЛНАЯ СБОРКА ТРАНЗАКЦИИ
     */
    async assembleCompleteTransaction() {
        console.log('\n🎯 ПОЛНАЯ СБОРКА DLMM ТРАНЗАКЦИИ');
        console.log('=' .repeat(80));
        
        try {
            // 1. Инициализация
            await this.initializeWallet();
            
            // 2. Загрузка аккаунтов
            await this.loadAccounts();
            
            // 3. Сборка всех инструкций
            console.log('\n🏗️ СБОРКА ВСЕХ ИНСТРУКЦИЙ...');
            
            this.buildFlashLoanInstruction();
            this.buildAddLiquidityInstruction();
            this.buildBuySOLInstruction();
            this.buildSellSOLInstruction();
            this.buildRemoveLiquidityInstruction();
            this.buildRepayFlashLoanInstruction();
            
            // 4. Создание финальной транзакции
            console.log('\n🎯 СОЗДАНИЕ ФИНАЛЬНОЙ ТРАНЗАКЦИИ...');
            
            const transaction = new Transaction();
            this.instructions.forEach(instruction => {
                transaction.add(instruction);
            });
            
            console.log('   ✅ Транзакция собрана');
            console.log(`      Инструкций: ${transaction.instructions.length}`);
            console.log(`      Подписант: ${this.wallet.publicKey.toString()}`);
            
            // 5. Анализ транзакции
            this.analyzeTransaction(transaction);
            
            return {
                success: true,
                transaction: transaction,
                instructionsCount: transaction.instructions.length,
                accountsCount: this.accounts.size
            };
            
        } catch (error) {
            console.error('💥 ОШИБКА СБОРКИ ТРАНЗАКЦИИ:', error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 📊 АНАЛИЗ СОБРАННОЙ ТРАНЗАКЦИИ
     */
    analyzeTransaction(transaction) {
        console.log('\n📊 АНАЛИЗ СОБРАННОЙ ТРАНЗАКЦИИ...');
        
        console.log('   🔍 Детали транзакции:');
        console.log(`      Инструкций: ${transaction.instructions.length}`);
        
        transaction.instructions.forEach((instruction, index) => {
            console.log(`      ${index + 1}. Программа: ${instruction.programId.toString()}`);
            console.log(`         Аккаунтов: ${instruction.keys.length}`);
            console.log(`         Данных: ${instruction.data.length} байт`);
        });
        
        console.log('\n   📋 Используемые аккаунты:');
        this.accounts.forEach((address, name) => {
            console.log(`      ${name}: ${address.toString()}`);
        });
        
        console.log('\n   💰 Финансовые операции:');
        console.log(`      Flash Loan: ${this.STRATEGY.flash_loan / 1e6} USDC`);
        console.log(`      Add Liquidity: ${this.STRATEGY.liquidity_add / 1e6} USDC`);
        console.log(`      Trading: ${this.STRATEGY.trading_amount / 1e6} USDC`);
        console.log(`      Expected Profit: ~22,239 USDC`);
        
        console.log('\n   ✅ ТРАНЗАКЦИЯ ГОТОВА К ВЫПОЛНЕНИЮ');
    }
}

// 🧪 ЗАПУСК
if (require.main === module) {
    async function main() {
        const assembler = new DLMMTransactionAssembler();
        const result = await assembler.assembleCompleteTransaction();
        
        if (result.success) {
            console.log('\n🎉 DLMM ТРАНЗАКЦИЯ УСПЕШНО СОБРАНА!');
            console.log(`📊 Инструкций: ${result.instructionsCount}`);
            console.log(`🔑 Аккаунтов: ${result.accountsCount}`);
        } else {
            console.log('\n❌ ОШИБКА СБОРКИ ТРАНЗАКЦИИ!');
            console.log(`❌ Ошибка: ${result.error}`);
        }
    }
    
    main().catch(console.error);
}

module.exports = DLMMTransactionAssembler;
