#!/usr/bin/env node

/**
 * 🧪 DLMM TRANSACTION SIMULATOR
 * 
 * СИМУЛЯЦИЯ ТРАНЗАКЦИЙ ПЕРЕД ОТПРАВКОЙ
 * - Проверка всех аккаунтов
 * - Симуляция выполнения
 * - Анализ ошибок
 * - Предложения по исправлению
 */

const { 
    Connection, 
    PublicKey, 
    Keypair, 
    Transaction,
    sendAndConfirmTransaction
} = require('@solana/web3.js');
const bs58 = require('bs58');
const DLMMTransactionAssembler = require('./dlmm-transaction-assembler.js');

require('dotenv').config();

class DLMMTransactionSimulator {
    constructor() {
        // 🌐 CONNECTION
        this.connection = new Connection(
            process.env.QUICKNODE_RPC_URL || process.env.SOLANA_RPC_URL,
            'confirmed'
        );
        
        // 🏗️ ASSEMBLER
        this.assembler = new DLMMTransactionAssembler();
        
        // 🔑 WALLET
        this.wallet = null;
        
        // 📊 РЕЗУЛЬТАТЫ СИМУЛЯЦИИ
        this.simulationResults = {
            accountChecks: [],
            instructionChecks: [],
            transactionSimulation: null,
            errors: [],
            warnings: [],
            recommendations: []
        };
        
        console.log('🧪 DLMM TRANSACTION SIMULATOR ИНИЦИАЛИЗИРОВАН');
    }

    /**
     * 🔑 ИНИЦИАЛИЗАЦИЯ
     */
    async initialize() {
        console.log('\n🔑 ИНИЦИАЛИЗАЦИЯ СИМУЛЯТОРА...');
        
        const privateKeyBytes = bs58.default.decode(process.env.WALLET_PRIVATE_KEY);
        this.wallet = Keypair.fromSecretKey(privateKeyBytes);
        
        console.log(`   Кошелек: ${this.wallet.publicKey.toString()}`);
        console.log('   ✅ Симулятор готов');
    }

    /**
     * 📊 ПРОВЕРКА ВСЕХ АККАУНТОВ
     */
    async checkAllAccounts(accounts) {
        console.log('\n📊 ПРОВЕРКА ВСЕХ АККАУНТОВ...');
        
        const accountChecks = [];
        
        for (const [name, address] of accounts.entries()) {
            console.log(`   🔍 Проверка ${name}: ${address.toString()}`);
            
            try {
                const accountInfo = await this.connection.getAccountInfo(address);
                
                const check = {
                    name: name,
                    address: address.toString(),
                    exists: accountInfo !== null,
                    owner: accountInfo?.owner?.toString() || null,
                    lamports: accountInfo?.lamports || 0,
                    dataLength: accountInfo?.data?.length || 0,
                    executable: accountInfo?.executable || false,
                    rentEpoch: accountInfo?.rentEpoch || null
                };
                
                if (check.exists) {
                    console.log(`      ✅ Существует | Owner: ${check.owner} | Lamports: ${check.lamports}`);
                } else {
                    console.log(`      ❌ НЕ СУЩЕСТВУЕТ`);
                    this.simulationResults.errors.push(`Аккаунт ${name} не существует: ${address.toString()}`);
                }
                
                accountChecks.push(check);
                
            } catch (error) {
                console.log(`      💥 ОШИБКА: ${error.message}`);
                
                const check = {
                    name: name,
                    address: address.toString(),
                    exists: false,
                    error: error.message
                };
                
                accountChecks.push(check);
                this.simulationResults.errors.push(`Ошибка проверки ${name}: ${error.message}`);
            }
        }
        
        this.simulationResults.accountChecks = accountChecks;
        
        console.log(`   📊 Проверено аккаунтов: ${accountChecks.length}`);
        console.log(`   ✅ Существуют: ${accountChecks.filter(a => a.exists).length}`);
        console.log(`   ❌ Не существуют: ${accountChecks.filter(a => !a.exists).length}`);
        
        return accountChecks;
    }

    /**
     * 🔍 ПРОВЕРКА БАЛАНСОВ ТОКЕНОВ
     */
    async checkTokenBalances() {
        console.log('\n🔍 ПРОВЕРКА БАЛАНСОВ ТОКЕНОВ...');
        
        try {
            // SOL баланс
            const solBalance = await this.connection.getBalance(this.wallet.publicKey);
            console.log(`   💰 SOL баланс: ${(solBalance / 1e9).toFixed(6)} SOL`);
            
            if (solBalance < 10000) { // 0.00001 SOL
                this.simulationResults.warnings.push('Низкий баланс SOL для gas fees');
            }
            
            // Проверяем USDC аккаунт
            const usdcMint = new PublicKey('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v');
            const { getAssociatedTokenAddress } = require('@solana/spl-token');
            
            const userUSDC = await getAssociatedTokenAddress(usdcMint, this.wallet.publicKey);
            const usdcAccountInfo = await this.connection.getAccountInfo(userUSDC);
            
            if (usdcAccountInfo) {
                // Парсим баланс USDC (упрощенно)
                console.log(`   💵 USDC аккаунт существует: ${userUSDC.toString()}`);
            } else {
                console.log(`   ⚠️ USDC аккаунт не существует: ${userUSDC.toString()}`);
                this.simulationResults.warnings.push('USDC аккаунт не существует - будет создан');
            }
            
        } catch (error) {
            console.error(`   ❌ Ошибка проверки балансов: ${error.message}`);
            this.simulationResults.errors.push(`Ошибка проверки балансов: ${error.message}`);
        }
    }

    /**
     * 🏊 ПРОВЕРКА СОСТОЯНИЯ ПУЛОВ
     */
    async checkPoolStates() {
        console.log('\n🏊 ПРОВЕРКА СОСТОЯНИЯ ПУЛОВ...');
        
        const pools = [
            { name: 'LARGE_POOL', address: 'AVs9TA4nWDzfPJE9gGVNJMVhcQy3V9PGazuz33BfG2RA' },
            { name: 'MEDIUM_POOL', address: 'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y' }
        ];
        
        for (const pool of pools) {
            console.log(`   🔍 Проверка ${pool.name}...`);
            
            try {
                const poolPubkey = new PublicKey(pool.address);
                const poolInfo = await this.connection.getAccountInfo(poolPubkey);
                
                if (poolInfo) {
                    console.log(`      ✅ Пул активен | Owner: ${poolInfo.owner.toString()}`);
                    console.log(`      📊 Данных: ${poolInfo.data.length} байт`);
                    
                    // Проверяем что это действительно DLMM пул
                    const expectedOwner = 'Eo7WjKq67rjJQSZxS6z3YkapzY3eMj6Xy8X5EQVn5UaB';
                    if (poolInfo.owner.toString() === expectedOwner) {
                        console.log(`      ✅ Корректный DLMM пул`);
                    } else {
                        console.log(`      ⚠️ Неожиданный owner пула`);
                        this.simulationResults.warnings.push(`${pool.name} имеет неожиданный owner`);
                    }
                } else {
                    console.log(`      ❌ Пул не найден`);
                    this.simulationResults.errors.push(`Пул ${pool.name} не существует`);
                }
                
            } catch (error) {
                console.error(`      💥 Ошибка: ${error.message}`);
                this.simulationResults.errors.push(`Ошибка проверки ${pool.name}: ${error.message}`);
            }
        }
    }

    /**
     * 🧪 СИМУЛЯЦИЯ ТРАНЗАКЦИИ
     */
    async simulateTransaction(transaction) {
        console.log('\n🧪 СИМУЛЯЦИЯ ТРАНЗАКЦИИ...');
        
        try {
            console.log('   📤 Отправка симуляции в RPC...');
            
            // Получаем recent blockhash
            const { blockhash } = await this.connection.getLatestBlockhash();
            transaction.recentBlockhash = blockhash;
            transaction.feePayer = this.wallet.publicKey;
            
            // Подписываем транзакцию для симуляции
            transaction.sign(this.wallet);
            
            // Симулируем транзакцию
            const simulation = await this.connection.simulateTransaction(transaction, {
                sigVerify: false,
                commitment: 'processed'
            });
            
            console.log('   📊 Результаты симуляции:');
            
            if (simulation.value.err) {
                console.log('   ❌ СИМУЛЯЦИЯ ПРОВАЛЕНА');
                console.log(`      Ошибка: ${JSON.stringify(simulation.value.err)}`);
                
                this.simulationResults.errors.push(`Симуляция провалена: ${JSON.stringify(simulation.value.err)}`);
                
                // Анализируем тип ошибки
                this.analyzeSimulationError(simulation.value.err);
                
            } else {
                console.log('   ✅ СИМУЛЯЦИЯ УСПЕШНА');
                console.log(`      Compute Units: ${simulation.value.unitsConsumed || 'N/A'}`);
                
                if (simulation.value.logs) {
                    console.log('   📋 Логи симуляции:');
                    simulation.value.logs.forEach((log, index) => {
                        console.log(`      ${index + 1}. ${log}`);
                    });
                }
            }
            
            this.simulationResults.transactionSimulation = simulation.value;
            
            return simulation.value;
            
        } catch (error) {
            console.error('   💥 ОШИБКА СИМУЛЯЦИИ:', error.message);
            this.simulationResults.errors.push(`Ошибка симуляции: ${error.message}`);
            return null;
        }
    }

    /**
     * 🔍 АНАЛИЗ ОШИБОК СИМУЛЯЦИИ
     */
    analyzeSimulationError(error) {
        console.log('\n🔍 АНАЛИЗ ОШИБКИ СИМУЛЯЦИИ...');
        
        const errorStr = JSON.stringify(error);
        
        // Распространенные ошибки и их решения
        const errorPatterns = [
            {
                pattern: /InsufficientFunds/i,
                message: 'Недостаточно средств',
                solution: 'Пополните баланс SOL или USDC'
            },
            {
                pattern: /AccountNotFound/i,
                message: 'Аккаунт не найден',
                solution: 'Создайте необходимые token аккаунты'
            },
            {
                pattern: /InvalidAccountData/i,
                message: 'Неверные данные аккаунта',
                solution: 'Проверьте корректность PDA адресов'
            },
            {
                pattern: /ProgramError/i,
                message: 'Ошибка программы',
                solution: 'Проверьте параметры инструкций'
            },
            {
                pattern: /InvalidInstruction/i,
                message: 'Неверная инструкция',
                solution: 'Проверьте формат данных инструкции'
            }
        ];
        
        for (const pattern of errorPatterns) {
            if (pattern.pattern.test(errorStr)) {
                console.log(`   🎯 Тип ошибки: ${pattern.message}`);
                console.log(`   💡 Решение: ${pattern.solution}`);
                
                this.simulationResults.recommendations.push({
                    error: pattern.message,
                    solution: pattern.solution
                });
                break;
            }
        }
        
        // Дополнительный анализ
        if (errorStr.includes('0x1')) {
            console.log('   🎯 Ошибка: Insufficient funds для rent');
            console.log('   💡 Решение: Добавьте больше SOL для создания аккаунтов');
        }
        
        if (errorStr.includes('0x0')) {
            console.log('   🎯 Ошибка: Custom program error');
            console.log('   💡 Решение: Проверьте логику программы и параметры');
        }
    }

    /**
     * 📋 ГЕНЕРАЦИЯ ОТЧЕТА
     */
    generateReport() {
        console.log('\n📋 ОТЧЕТ СИМУЛЯЦИИ');
        console.log('=' .repeat(80));
        
        // Статистика аккаунтов
        const existingAccounts = this.simulationResults.accountChecks.filter(a => a.exists).length;
        const totalAccounts = this.simulationResults.accountChecks.length;
        
        console.log('📊 СТАТИСТИКА АККАУНТОВ:');
        console.log(`   Всего проверено: ${totalAccounts}`);
        console.log(`   Существуют: ${existingAccounts}`);
        console.log(`   Не существуют: ${totalAccounts - existingAccounts}`);
        
        // Ошибки
        if (this.simulationResults.errors.length > 0) {
            console.log('\n❌ ОШИБКИ:');
            this.simulationResults.errors.forEach((error, index) => {
                console.log(`   ${index + 1}. ${error}`);
            });
        }
        
        // Предупреждения
        if (this.simulationResults.warnings.length > 0) {
            console.log('\n⚠️ ПРЕДУПРЕЖДЕНИЯ:');
            this.simulationResults.warnings.forEach((warning, index) => {
                console.log(`   ${index + 1}. ${warning}`);
            });
        }
        
        // Рекомендации
        if (this.simulationResults.recommendations.length > 0) {
            console.log('\n💡 РЕКОМЕНДАЦИИ:');
            this.simulationResults.recommendations.forEach((rec, index) => {
                console.log(`   ${index + 1}. ${rec.error}: ${rec.solution}`);
            });
        }
        
        // Итоговый статус
        const hasErrors = this.simulationResults.errors.length > 0;
        const hasWarnings = this.simulationResults.warnings.length > 0;
        
        console.log('\n🎯 ИТОГОВЫЙ СТАТУС:');
        if (!hasErrors && !hasWarnings) {
            console.log('   ✅ ВСЕ ПРОВЕРКИ ПРОЙДЕНЫ - ГОТОВО К ВЫПОЛНЕНИЮ');
        } else if (!hasErrors && hasWarnings) {
            console.log('   ⚠️ ЕСТЬ ПРЕДУПРЕЖДЕНИЯ - МОЖНО ВЫПОЛНЯТЬ С ОСТОРОЖНОСТЬЮ');
        } else {
            console.log('   ❌ ЕСТЬ ОШИБКИ - ТРЕБУЕТСЯ ИСПРАВЛЕНИЕ');
        }
        
        return {
            status: hasErrors ? 'ERROR' : hasWarnings ? 'WARNING' : 'SUCCESS',
            errors: this.simulationResults.errors.length,
            warnings: this.simulationResults.warnings.length,
            recommendations: this.simulationResults.recommendations.length
        };
    }

    /**
     * 🔧 ГЕНЕРАЦИЯ ИНСТРУКЦИЙ ИСПРАВЛЕНИЯ
     */
    generateFixInstructions() {
        console.log('\n🔧 ГЕНЕРАЦИЯ ИНСТРУКЦИЙ ИСПРАВЛЕНИЯ...');

        const fixes = [];

        // Исправления для несуществующих аккаунтов
        const missingAccounts = this.simulationResults.accountChecks.filter(a => !a.exists);

        missingAccounts.forEach(account => {
            if (account.name.includes('BIN_ARRAY')) {
                fixes.push({
                    type: 'CREATE_BIN_ARRAY',
                    account: account.name,
                    address: account.address,
                    instruction: 'Добавить инструкцию создания Bin Array перед Add Liquidity'
                });
            } else if (account.name === 'POSITION') {
                fixes.push({
                    type: 'CREATE_POSITION',
                    account: account.name,
                    address: account.address,
                    instruction: 'Добавить инструкцию создания Position перед Add Liquidity'
                });
            } else if (account.name === 'MARGINFI_ACCOUNT') {
                fixes.push({
                    type: 'CREATE_MARGINFI_ACCOUNT',
                    account: account.name,
                    address: account.address,
                    instruction: 'Добавить инструкцию создания MarginFi Account перед Flash Loan'
                });
            }
        });

        // Исправления для неправильных пулов
        if (this.simulationResults.warnings.some(w => w.includes('owner'))) {
            fixes.push({
                type: 'FIX_POOL_ADDRESSES',
                instruction: 'Найти правильные DLMM пулы с owner Eo7WjKq67rjJQSZxS6z3YkapzY3eMj6Xy8X5EQVn5UaB'
            });
        }

        console.log('   💡 Рекомендуемые исправления:');
        fixes.forEach((fix, index) => {
            console.log(`      ${index + 1}. ${fix.type}: ${fix.instruction}`);
        });

        return fixes;
    }

    /**
     * 🎯 ПОЛНАЯ СИМУЛЯЦИЯ
     */
    async runFullSimulation() {
        console.log('🎯 ЗАПУСК ПОЛНОЙ СИМУЛЯЦИИ DLMM ТРАНЗАКЦИИ');
        console.log('=' .repeat(80));
        
        try {
            // 1. Инициализация
            await this.initialize();
            
            // 2. Сборка транзакции
            console.log('\n🏗️ СБОРКА ТРАНЗАКЦИИ...');
            const assemblyResult = await this.assembler.assembleCompleteTransaction();
            
            if (!assemblyResult.success) {
                throw new Error(`Ошибка сборки: ${assemblyResult.error}`);
            }
            
            const { transaction } = assemblyResult;
            
            // 3. Проверка аккаунтов
            await this.checkAllAccounts(this.assembler.accounts);
            
            // 4. Проверка балансов
            await this.checkTokenBalances();
            
            // 5. Проверка пулов
            await this.checkPoolStates();
            
            // 6. Симуляция транзакции
            await this.simulateTransaction(transaction);
            
            // 7. Генерация отчета
            const report = this.generateReport();

            // 8. Генерация инструкций исправления
            const fixes = this.generateFixInstructions();

            console.log('\n🎉 СИМУЛЯЦИЯ ЗАВЕРШЕНА!');
            
            return {
                success: true,
                report: report,
                fixes: fixes,
                transaction: transaction,
                results: this.simulationResults
            };
            
        } catch (error) {
            console.error('💥 КРИТИЧЕСКАЯ ОШИБКА СИМУЛЯЦИИ:', error.message);
            
            return {
                success: false,
                error: error.message,
                results: this.simulationResults
            };
        }
    }
}

// 🧪 ЗАПУСК
if (require.main === module) {
    async function main() {
        const simulator = new DLMMTransactionSimulator();
        const result = await simulator.runFullSimulation();
        
        if (result.success) {
            console.log('\n🎉 СИМУЛЯЦИЯ УСПЕШНА!');
            console.log(`📊 Статус: ${result.report.status}`);
            
            if (result.report.status === 'SUCCESS') {
                console.log('✅ ТРАНЗАКЦИЯ ГОТОВА К ВЫПОЛНЕНИЮ!');
            } else if (result.report.status === 'WARNING') {
                console.log('⚠️ ЕСТЬ ПРЕДУПРЕЖДЕНИЯ - ПРОВЕРЬТЕ ПЕРЕД ВЫПОЛНЕНИЕМ');
            } else {
                console.log('❌ ЕСТЬ ОШИБКИ - ИСПРАВЬТЕ ПЕРЕД ВЫПОЛНЕНИЕМ');
            }
        } else {
            console.log('\n❌ СИМУЛЯЦИЯ ПРОВАЛЕНА!');
            console.log(`❌ Ошибка: ${result.error}`);
        }
    }
    
    main().catch(console.error);
}

module.exports = DLMMTransactionSimulator;
