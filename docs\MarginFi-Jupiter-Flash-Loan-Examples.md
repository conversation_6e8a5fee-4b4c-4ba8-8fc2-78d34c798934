# MarginFi + Jupiter Flash Loan Атомарные Транзакции - Полные Примеры

## 🔥 ОФИЦИАЛЬНЫЕ ИСТОЧНИКИ

### 1. Jupiter Flash Fill (Официальный пример)
- **Репозиторий**: https://github.com/jup-ag/sol-swap-flash-fill
- **Документация**: https://dev.jup.ag/docs/old/apis/flash-fill
- **Транзакция**: https://solscan.io/tx/4psWiUFGdRhKqi1UXSWrpoM3RCJWAXpz6CTpsd5fZwjr8nEpLiZVuiyaERj95hUNnm6dhfxircLgAqCbHV3wCVpT

### 2. MarginFi Flash Loan (Официальная документация)
- **Документация**: https://docs.marginfi.com/ts-sdk#conduct-a-flash-loan
- **SDK**: @mrgnlabs/marginfi-client-v2

## 🚀 JUPITER FLASH FILL - ПОЛНЫЙ ПРИМЕР

### TypeScript Client (flash-fill.ts)

```typescript
import {
  programAuthority,
  provider,
  wallet,
  program,
  connection,
  getAdressLookupTableAccounts,
  instructionDataToTransactionInstruction,
} from "./helper";
import { NATIVE_MINT } from "@solana/spl-token";
import {
  SystemProgram,
  TransactionMessage,
  PublicKey,
  VersionedTransaction,
  SYSVAR_INSTRUCTIONS_PUBKEY,
} from "@solana/web3.js";
import fetch from "node-fetch";

const API_ENDPOINT = "https://quote-api.jup.ag/v6";

const getQuote = async (
  fromMint: PublicKey,
  toMint: PublicKey,
  amount: number
) => {
  return fetch(
    `${API_ENDPOINT}/quote?outputMint=${toMint.toBase58()}&inputMint=${fromMint.toBase58()}&amount=${amount}&slippage=0.5`
  ).then((response) => response.json());
};

const getSwapIx = async (user: PublicKey, quote: any) => {
  const data = {
    quoteResponse: quote,
    userPublicKey: user.toBase58(),
  };

  return fetch(`${API_ENDPOINT}/swap-instructions`, {
    method: "POST",
    headers: {
      Accept: "application/json",
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  }).then((response) => response.json());
};

const swapToSol = async (
  computeBudgetPayloads: any[],
  setupPayloads: any[],
  swapPayload: any,
  cleanupPayload: any | null,
  addressLookupTableAddresses: string[]
) => {
  const instructions = [
    ...computeBudgetPayloads.map(instructionDataToTransactionInstruction),
    await program.methods
      .borrow()
      .accountsStrict({
        borrower: wallet.publicKey,
        programAuthority,
        instructions: SYSVAR_INSTRUCTIONS_PUBKEY,
        systemProgram: SystemProgram.programId,
      })
      .instruction(),
    ...setupPayloads.map(instructionDataToTransactionInstruction),
    instructionDataToTransactionInstruction(swapPayload),
    instructionDataToTransactionInstruction(cleanupPayload), // can be null
    await program.methods
      .repay()
      .accountsStrict({
        borrower: wallet.publicKey,
        programAuthority,
        instructions: SYSVAR_INSTRUCTIONS_PUBKEY,
        systemProgram: SystemProgram.programId,
      })
      .instruction(),
  ].filter((instruction) => {
    return instruction !== null;
  });

  const blockhash = (await connection.getLatestBlockhash()).blockhash;

  // If you want, you can add more lookup table accounts here
  const addressLookupTableAccounts = await getAdressLookupTableAccounts(
    addressLookupTableAddresses
  );

  const messageV0 = new TransactionMessage({
    payerKey: wallet.publicKey,
    recentBlockhash: blockhash,
    instructions,
  }).compileToV0Message(addressLookupTableAccounts);

  const transaction = new VersionedTransaction(messageV0);

  try {
    await provider.simulate(transaction, [wallet.payer]);
    const txID = await provider.sendAndConfirm(transaction, [wallet.payer]);
    console.log({ txID });
  } catch (e) {
    console.log({ simulationResponse: e.simulationResponse });
  }
};

// Main
(async () => {
  const USDC = new PublicKey("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v");

  // Find the best Quote from the Jupiter API
  const quote = await getQuote(USDC, NATIVE_MINT, 1000000);
  console.log({ quote });

  // Convert the Quote into a Swap instruction
  const result = await getSwapIx(wallet.publicKey, quote);
  if ("error" in result) {
    console.log({ result });
    return result;
  }

  // We have now both the instruction and the lookup table addresses.
  const {
    computeBudgetInstructions, // The necessary instructions to setup the compute budget.
    setupInstructions, // Setup missing ATA for the users.
    swapInstruction, // The actual swap instruction.
    cleanupInstruction, // Unwrap the SOL if `wrapUnwrapSOL = true`.
    addressLookupTableAddresses, // The lookup table addresses that you can use if you are using versioned transaction.
  } = result;

  await swapToSol(
    computeBudgetInstructions,
    setupInstructions,
    swapInstruction,
    cleanupInstruction,
    addressLookupTableAddresses
  );
})();
```

## 🏦 MARGINFI FLASH LOAN - ОФИЦИАЛЬНЫЙ ПРИМЕР

### MarginFi Flash Loan (Из официальной документации)

```typescript
import { Connection } from "@solana/web3.js";
import { MarginfiClient, MarginfiAccountWrapper } from '@mrgnlabs/marginfi-client-v2';
import { NodeWallet } from "@mrgnlabs/mrgn-common";

async function main() {
  const connection = new Connection("<rpc-url>", "confirmed");
  const wallet = NodeWallet.local()
  const config = getConfig("<environment>");
  const client = await MarginfiClient.fetch(config, wallet, connection);

  const marginfiAccounts = await client.getMarginfiAccountsForAuthority();
  if (marginfiAccounts.length === 0) throw Error("No marginfi account found");

  const marginfiAccount = marginfiAccounts[0];

  const solBank = client.getBankByTokenSymbol("SOL");
  if (!solBank) throw Error("SOL bank not found");

  const amount = 10; // SOL

  // ⚡ АТОМАРНЫЙ FLASH LOAN: repayAll = true для полного возврата займа
  const borrowIx = await marginfiAccount.makeBorrowIx(amount, solBank.address);
  const repayIx = await marginfiAccount.makeRepayIx(amount, solBank.address, true); // repayAll = true!

  const flashLoanTx = await marginfiAccount.buildFlashLoanTx({
    ixs: [...borrowIx.instructions, ...repayIx.instructions],
    signers: [],
  });

  await client.processTransaction(flashLoanTx);
}

main().catch((e) => console.log(e));
```

## ⚡ КОМБИНИРОВАННЫЙ ПРИМЕР: MARGINFI + JUPITER

### Атомарная транзакция с MarginFi Flash Loan + Jupiter Swap

```typescript
import { Connection } from "@solana/web3.js";
import { MarginfiClient, MarginfiAccountWrapper } from '@mrgnlabs/marginfi-client-v2';
import { NodeWallet } from "@mrgnlabs/mrgn-common";

async function marginfiJupiterFlashLoan() {
  const connection = new Connection("<rpc-url>", "confirmed");
  const wallet = NodeWallet.local();
  const config = getConfig("<environment>");
  const client = await MarginfiClient.fetch(config, wallet, connection);

  const marginfiAccount = await client.getMarginfiAccountsForAuthority()[0];
  const usdcBank = client.getBankByTokenSymbol("USDC");

  const flashLoanAmount = 1000; // $1000 USDC

  // 1. Создаем borrow инструкции
  const borrowIx = await marginfiAccount.makeBorrowIx(flashLoanAmount, usdcBank.address);

  // 2. Получаем Jupiter swap инструкции
  const jupiterQuote = await getJupiterQuote(USDC_MINT, SOL_MINT, flashLoanAmount);
  const jupiterSwap = await getJupiterSwapInstructions(wallet.publicKey, jupiterQuote);

  // 3. Создаем repay инструкции
  const repayIx = await marginfiAccount.makeRepayIx(flashLoanAmount, usdcBank.address, true);

  // 4. Создаем атомарную flash loan транзакцию С ОБЯЗАТЕЛЬНЫМИ ALT
  if (!jupiterSwap.addressLookupTableAccounts || jupiterSwap.addressLookupTableAccounts.length === 0) {
    throw new Error('❌ ALT таблицы обязательны для buildFlashLoanTx!');
  }

  const flashLoanTx = await marginfiAccount.buildFlashLoanTx({
    ixs: [
      ...borrowIx.instructions,        // Займ USDC
      ...jupiterSwap.instructions,     // Swap USDC -> SOL -> USDC
      ...repayIx.instructions          // Возврат USDC
    ],
    signers: [],
  }, jupiterSwap.addressLookupTableAccounts); // ✅ ОБЯЗАТЕЛЬНЫЕ ALT ТАБЛИЦЫ!

  // 5. Выполняем транзакцию
  const txId = await client.processTransaction(flashLoanTx);
  console.log("Flash loan + Jupiter swap успешно:", txId);
}
```

## 🔧 HELPER ФУНКЦИИ

### Jupiter API Helper

```typescript
const getJupiterQuote = async (inputMint: PublicKey, outputMint: PublicKey, amount: number) => {
  const response = await fetch(
    `https://quote-api.jup.ag/v6/quote?inputMint=${inputMint.toBase58()}&outputMint=${outputMint.toBase58()}&amount=${amount}&slippage=0.5`
  );
  return response.json();
};

const getJupiterSwapInstructions = async (userPublicKey: PublicKey, quote: any) => {
  const response = await fetch('https://quote-api.jup.ag/v6/swap-instructions', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      quoteResponse: quote,
      userPublicKey: userPublicKey.toBase58(),
    }),
  });
  return response.json();
};
```

### Helper.ts (Jupiter Flash Fill)

```typescript
import * as anchor from "@coral-xyz/anchor";
import { Program, Wallet, AnchorProvider } from "@coral-xyz/anchor";
import { IDL, FlashFill } from "../target/types/flash_fill";
import {
  PublicKey,
  Keypair,
  Connection,
  AddressLookupTableAccount,
  TransactionInstruction,
} from "@solana/web3.js";
import { bs58 } from "@coral-xyz/anchor/dist/cjs/utils/bytes";
import {
  ASSOCIATED_TOKEN_PROGRAM_ID,
  NATIVE_MINT,
  TOKEN_PROGRAM_ID,
} from "@solana/spl-token";

export const programId = new PublicKey(
  "JUPLdTqUdKztWJ1isGMV92W2QvmEmzs9WTJjhZe4QdJ"
);

export const wallet = new Wallet(
  Keypair.fromSecretKey(bs58.decode(process.env.KEYPAIR))
);

export const connection = new Connection(process.env.RPC_URL);

export const provider = new AnchorProvider(connection, wallet, {
  commitment: "processed",
});

anchor.setProvider(provider);

export const program = new Program(IDL, programId, provider);

const findProgramAuthority = (): PublicKey => {
  return PublicKey.findProgramAddressSync(
    [Buffer.from("authority")],
    programId
  )[0];
};

export const programAuthority = findProgramAuthority();

export const getAdressLookupTableAccounts = async (
  keys: string[]
): Promise<AddressLookupTableAccount[]> => {
  const addressLookupTableAccountInfos = await connection.getMultipleAccountsInfo(
    keys.map((key) => new PublicKey(key))
  );

  return addressLookupTableAccountInfos.reduce((acc, accountInfo, index) => {
    const addressLookupTableAddress = keys[index];
    if (accountInfo) {
      const addressLookupTableAccount = new AddressLookupTableAccount({
        key: new PublicKey(addressLookupTableAddress),
        state: AddressLookupTableAccount.deserialize(accountInfo.data),
      });
      acc.push(addressLookupTableAccount);
    }
    return acc;
  }, new Array<AddressLookupTableAccount>());
};

export const instructionDataToTransactionInstruction = (
  instructionPayload: any
) => {
  if (instructionPayload === null) {
    return null;
  }
  return new TransactionInstruction({
    programId: new PublicKey(instructionPayload.programId),
    keys: instructionPayload.accounts.map((key) => ({
      pubkey: new PublicKey(key.pubkey),
      isSigner: key.isSigner,
      isWritable: key.isWritable,
    })),
    data: Buffer.from(instructionPayload.data, "base64"),
  });
};
```

## 📋 КЛЮЧЕВЫЕ ПРИНЦИПЫ

### 1. Атомарность
- Вся транзакция выполняется или откатывается полностью
- Нет промежуточного состояния

### 2. Безрисковость
- Flash loan возвращается в той же транзакции
- Невозможно потерять средства

### 3. repayAll = true
- Для атомарных flash loans ВСЕГДА используйте repayAll = true
- Займ ДОЛЖЕН быть полностью возвращен

### 4. Address Lookup Tables
- Используйте ALT для сжатия транзакций
- Jupiter предоставляет ALT в ответе API

## 🦀 RUST ПРОГРАММА FLASH FILL (Jupiter)

### Flash Fill Program (lib.rs)

```rust
use anchor_lang::prelude::*;
use anchor_lang::solana_program::sysvar;
use anchor_lang::solana_program::sysvar::instructions::{
    load_current_index_checked, load_instruction_at_checked,
};
use anchor_lang::Discriminator;
use anchor_spl::token::TokenAccount;
use anchor_lang::system_program;

pub const AUTHORITY_SEED: &[u8] = b"authority";

declare_id!("JUPLdTqUdKztWJ1isGMV92W2QvmEmzs9WTJjhZe4QdJ");

#[program]
pub mod flash_fill {
    use super::*;

    pub fn borrow(ctx: Context<Borrow>) -> Result<()> {
        let ixs = ctx.accounts.instructions.to_account_info();

        // make sure this isnt a cpi call
        let current_index = load_current_index_checked(&ixs)? as usize;
        let current_ix = load_instruction_at_checked(current_index, &ixs)?;
        if current_ix.program_id != *ctx.program_id {
            return Err(FlashFillError::ProgramMismatch.into());
        }

        // loop through instructions, looking for an equivalent repay to this borrow
        let mut index = current_index + 1; // jupiter swap

        loop {
            // get the next instruction, die if theres no more
            if let Ok(ix) = load_instruction_at_checked(index, &ixs) {
                if ix.program_id == crate::id() {
                    let ix_discriminator: [u8; 8] = ix.data[0..8]
                        .try_into()
                        .map_err(|_| FlashFillError::UnknownInstruction)?;

                    // check if we have a toplevel repay toward the program authority
                    if ix_discriminator == self::instruction::Repay::discriminator() {
                        require_keys_eq!(
                            ix.accounts[1].pubkey,
                            ctx.accounts.program_authority.key(),
                            FlashFillError::IncorrectProgramAuthority
                        );
                        break;
                    } else if ix_discriminator == self::instruction::Borrow::discriminator() {
                        return Err(FlashFillError::CannotBorrowBeforeRepay.into());
                    } else {
                        return Err(FlashFillError::UnknownInstruction.into());
                    }
                }
            } else {
                // no more instructions, so we're missing a repay
                return Err(FlashFillError::MissingRepay.into());
            }
            index += 1
        }

        let authority_bump = ctx.bumps.get("program_authority").unwrap().to_le_bytes();
        let rent = Rent::get()?;
        let space = TokenAccount::LEN;
        let token_lamports = rent.minimum_balance(space);

        // transfer enough SOL to the borrower to open wSOL account.
        let signer_seeds: &[&[&[u8]]] = &[&[AUTHORITY_SEED, authority_bump.as_ref()]];
        system_program::transfer(
            CpiContext::new_with_signer(
                ctx.accounts.system_program.to_account_info(),
                system_program::Transfer {
                    from: ctx.accounts.program_authority.to_account_info(),
                    to: ctx.accounts.borrower.to_account_info(),
                },
                signer_seeds,
            ),
            token_lamports,
        )?;

        Ok(())
    }

    pub fn repay(ctx: Context<Repay>) -> Result<()> {
        let ixs = ctx.accounts.instructions.to_account_info();

        // make sure this isnt a cpi call
        let current_index = load_current_index_checked(&ixs)? as usize;
        let current_ix = load_instruction_at_checked(current_index, &ixs)?;
        if current_ix.program_id != *ctx.program_id {
            return Err(FlashFillError::ProgramMismatch.into());
        }

        let rent = Rent::get()?;
        let space = TokenAccount::LEN;
        let token_lamports = rent.minimum_balance(space);

        // transfer borrowed SOL back to the program authority
        system_program::transfer(
            CpiContext::new(
                ctx.accounts.system_program.to_account_info(),
                system_program::Transfer {
                    from: ctx.accounts.borrower.to_account_info(),
                    to: ctx.accounts.program_authority.to_account_info(),
                },
            ),
            token_lamports,
        )?;

        Ok(())
    }
}

#[derive(Accounts)]
pub struct Borrow<'info> {
    pub borrower: Signer<'info>,
    #[account(mut, seeds = [AUTHORITY_SEED], bump)]
    pub program_authority: SystemAccount<'info>,
    /// CHECK: check instructions account
    #[account(address = sysvar::instructions::ID @FlashFillError::AddressMismatch)]
    pub instructions: UncheckedAccount<'info>,
    pub system_program: Program<'info, System>,
}

#[derive(Accounts)]
pub struct Repay<'info> {
    pub borrower: Signer<'info>,
    #[account(mut, seeds = [AUTHORITY_SEED], bump)]
    pub program_authority: SystemAccount<'info>,
    /// CHECK: check instructions account
    #[account(address = sysvar::instructions::ID @FlashFillError::AddressMismatch)]
    pub instructions: UncheckedAccount<'info>,
    pub system_program: Program<'info, System>,
}

/// Errors for this program
#[error_code]
pub enum FlashFillError {
    #[msg("Address Mismatch")]
    AddressMismatch,
    #[msg("Program Mismatch")]
    ProgramMismatch,
    #[msg("Missing Repay")]
    MissingRepay,
    #[msg("Incorrect Owner")]
    IncorrectOwner,
    #[msg("Incorrect Program Authority")]
    IncorrectProgramAuthority,
    #[msg("Cannot Borrow Before Repay")]
    CannotBorrowBeforeRepay,
    #[msg("Unknown Instruction")]
    UnknownInstruction,
}
```

## 🚨 ВАЖНЫЕ ЗАМЕЧАНИЯ

1. **Размер транзакции**: Лимит 1232 байта
2. **Compute Units**: Установите достаточный лимит
3. **Slippage**: Учитывайте проскальзывание цены
4. **Fees**: Рассчитывайте все комиссии заранее
5. **repayAll = true**: Обязательно для атомарных flash loans
6. **Address Lookup Tables**: Используйте для сжатия транзакций
