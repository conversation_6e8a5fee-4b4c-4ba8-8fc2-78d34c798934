#!/usr/bin/env node

/**
 * 🧠 ДИНАМИЧЕСКИЙ ОПТИМИЗАТОР РАЗМЕРА ПОЗИЦИИ
 * ═══════════════════════════════════════════════════════════════════════════════
 * 🎯 ОПРЕДЕЛЯЕТ: Оптимальный размер займа для каждой арбитражной операции
 * 🚀 УЧИТЫВАЕТ: Ликвидность, спред, риски, доступный капитал
 * 💰 МАКСИМИЗИРУЕТ: Прибыль при минимизации рисков
 * ═══════════════════════════════════════════════════════════════════════════════
 */

const colors = require('colors');

class DynamicPositionOptimizer {
    constructor() {
        console.log('🧠 ДИНАМИЧЕСКИЙ ОПТИМИЗАТОР РАЗМЕРА ПОЗИЦИИ АКТИВИРОВАН!'.cyan.bold);

        // 💰 КОНФИГУРАЦИЯ РАЗМЕРОВ ПОЗИЦИЙ - ИСПОЛЬЗУЕМ ЦЕНТРАЛИЗОВАННЫЕ НАСТРОЙКИ!
        const { TRADING_CONFIG } = require('./trading-config.js');
        this.positionConfig = {
            // Базовые лимиты - УВЕЛИЧЕНЫ ДЛЯ МАКСИМАЛЬНОЙ ПРИБЫЛИ!
            MIN_POSITION_USD: 1000,      // $1K минимум
            MAX_POSITION_USD: 200000,    // 🔥 $200K максимум для максимальной прибыли!
            DEFAULT_POSITION_USD: 20000, // 🔥 $20K по умолчанию для агрессивной торговли!

            // Множители риска
            CONSERVATIVE_MULTIPLIER: 0.5,  // 50% от максимума для консервативных сделок
            AGGRESSIVE_MULTIPLIER: 1.0,    // 100% от максимума для агрессивных сделок

            // Пороги спреда для размера позиции
            SPREAD_THRESHOLDS: {
                ULTRA_HIGH: 2.0,    // 2%+ спред = максимальная позиция
                HIGH: 1.0,          // 1%+ спред = большая позиция
                MEDIUM: 0.5,        // 0.5%+ спред = средняя позиция
                LOW: 0.08,          // 🔥 ИСПРАВЛЕНО: 0.08%+ спред = средняя позиция (было 0.1%)
                MINIMAL: 0.05       // 0.05%+ спред = минимальная позиция
            },

            // Коэффициенты ликвидности
            LIQUIDITY_FACTORS: {
                ULTRA_HIGH: 1.0,    // $50M+ ликвидность = полный размер
                HIGH: 0.8,          // $20M+ ликвидность = 80% размера
                MEDIUM: 0.6,        // $10M+ ликвидность = 60% размера
                LOW: 0.4,           // $5M+ ликвидность = 40% размера
                MINIMAL: 0.2        // <$5M ликвидность = 20% размера
            }
        };

        console.log('📊 Конфигурация оптимизатора:');
        console.log(`   💰 Диапазон позиций: $${this.positionConfig.MIN_POSITION_USD.toLocaleString()} - $${this.positionConfig.MAX_POSITION_USD.toLocaleString()}`);
        console.log(`   🎯 Позиция по умолчанию: $${this.positionConfig.DEFAULT_POSITION_USD.toLocaleString()}`);
    }

    /**
     * 🎯 ОСНОВНОЙ МЕТОД: ОПРЕДЕЛЕНИЕ ОПТИМАЛЬНОГО РАЗМЕРА ПОЗИЦИИ
     */
    calculateOptimalPositionSize(opportunity, smartOptimizerResult = null) {
        try {
            console.log('\n🧠 РАСЧЕТ ОПТИМАЛЬНОГО РАЗМЕРА ПОЗИЦИИ'.yellow.bold);
            console.log('═══════════════════════════════════════════════════════════════');

            // 1. АНАЛИЗ ВХОДНЫХ ДАННЫХ
            const analysis = this.analyzeOpportunity(opportunity);
            console.log(`📊 Анализ возможности: ${analysis.riskLevel} риск, ${analysis.liquidityLevel} ликвидность`);

            // 2. РАСЧЕТ БАЗОВОГО РАЗМЕРА НА ОСНОВЕ СПРЕДА
            const spreadBasedSize = this.calculateSpreadBasedSize(opportunity.spread);
            console.log(`📈 Размер на основе спреда (${opportunity.spread.toFixed(4)}%): $${spreadBasedSize.toLocaleString()}`);

            // 3. КОРРЕКТИРОВКА НА ОСНОВЕ ЛИКВИДНОСТИ
            const liquidityAdjustedSize = this.adjustForLiquidity(spreadBasedSize, analysis.totalLiquidity);
            console.log(`🏊 Корректировка ликвидности ($${analysis.totalLiquidity.toLocaleString()}M): $${liquidityAdjustedSize.toLocaleString()}`);

            // 4. БЕЗ КОРРЕКТИРОВКИ РИСКОВ - FLASH LOANS ИМЕЮТ СТАБИЛЬНЫЕ РИСКИ!
            // В арбитраже с flash loans риски стабильны (только комиссии)
            // Используем максимально допустимый расчетный баланс для займа
            console.log(`💪 БЕЗ корректировки рисков - flash loans стабильны: $${liquidityAdjustedSize.toLocaleString()}`);

            // 5. 🧠 УМНАЯ ОПТИМИЗАЦИЯ ДЛЯ МАКСИМАЛЬНОЙ ПРИБЫЛИ
            const smartOptimizedSize = this.findOptimalSizeForMaxProfit(opportunity, analysis);
            console.log(`🧠 Умная оптимизация для максимальной прибыли: $${smartOptimizedSize.toLocaleString()}`);

            let finalSize = smartOptimizedSize;
            if (smartOptimizerResult && smartOptimizerResult.bestRoute) {
                finalSize = Math.max(smartOptimizedSize, this.integrateSmartOptimizerResult(liquidityAdjustedSize, smartOptimizerResult));
                console.log(`🧠 Интеграция с умным оптимизатором: $${finalSize.toLocaleString()}`);
            }

            // 6. ПРИМЕНЕНИЕ ЛИМИТОВ
            const boundedSize = this.applyPositionLimits(finalSize);
            console.log(`🎯 Финальный размер (с лимитами): $${boundedSize.toLocaleString()}`);

            // 7. РАСЧЕТ ОЖИДАЕМОЙ ПРИБЫЛИ
            const expectedProfit = this.calculateExpectedProfit(boundedSize, opportunity.spread, analysis);

            const result = {
                optimalPositionSize: boundedSize,
                expectedProfit: expectedProfit,
                analysis: analysis,
                breakdown: {
                    spreadBased: spreadBasedSize,
                    liquidityAdjusted: liquidityAdjustedSize,
                    noRiskAdjustment: 'Flash loans имеют стабильные риски',
                    final: boundedSize
                },
                recommendation: this.getPositionRecommendation(boundedSize, expectedProfit, analysis)
            };

            this.displayOptimizationResult(result);
            return result;

        } catch (error) {
            console.error(`❌ Ошибка расчета оптимального размера позиции: ${error.message}`.red);
            return {
                optimalPositionSize: this.positionConfig.DEFAULT_POSITION_USD,
                expectedProfit: 0,
                error: error.message
            };
        }
    }

    /**
     * 📊 АНАЛИЗ ВОЗМОЖНОСТИ АРБИТРАЖА
     */
    analyzeOpportunity(opportunity) {
        // Анализ ликвидности (примерные значения на основе DEX)
        const liquidityEstimates = {
            'Orca': 85,      // $85M
            'Raydium': 45,   // $45M
            'Meteora': 25,   // $25M
            'Jupiter': 30    // $30M
        };

        const buyLiquidity = liquidityEstimates[opportunity.buyDex] || 20;
        const sellLiquidity = liquidityEstimates[opportunity.sellDex] || 20;
        const totalLiquidity = Math.min(buyLiquidity, sellLiquidity); // Лимитирующий фактор

        // Определение уровня ликвидности
        let liquidityLevel = 'MINIMAL';
        if (totalLiquidity >= 50) liquidityLevel = 'ULTRA_HIGH';
        else if (totalLiquidity >= 20) liquidityLevel = 'HIGH';
        else if (totalLiquidity >= 10) liquidityLevel = 'MEDIUM';
        else if (totalLiquidity >= 5) liquidityLevel = 'LOW';

        // Анализ рисков (ТОЛЬКО ДЛЯ ИНФОРМАЦИИ - НЕ ВЛИЯЕТ НА РАЗМЕР ПОЗИЦИИ!)
        let riskScore = 3; // Flash loans имеют стабильно низкие риски

        // Flash loans риски стабильны:
        // - Атомарность: либо все успешно, либо откат
        // - Комиссии: фиксированные и предсказуемые
        // - Ликвидность: гарантирована протоколом
        console.log(`💪 Flash loans риски стабильны - размер позиции НЕ корректируется`);

        let riskLevel = 'LOW'; // Flash loans всегда низкий риск

        return {
            totalLiquidity,
            liquidityLevel,
            riskScore,
            riskLevel,
            buyLiquidity,
            sellLiquidity
        };
    }

    /**
     * 📈 РАСЧЕТ РАЗМЕРА НА ОСНОВЕ СПРЕДА
     */
    calculateSpreadBasedSize(spreadPercent) {
        const thresholds = this.positionConfig.SPREAD_THRESHOLDS;
        const maxSize = this.positionConfig.MAX_POSITION_USD;

        // 🔥 АГРЕССИВНАЯ ФОРМУЛА ДЛЯ МАКСИМАЛЬНОЙ ПРИБЫЛИ!
        if (spreadPercent >= thresholds.ULTRA_HIGH) {
            return maxSize; // 100% максимального размера ($200K)
        } else if (spreadPercent >= thresholds.HIGH) {
            return maxSize * 0.9; // 90% = $180K для высокого спреда
        } else if (spreadPercent >= thresholds.MEDIUM) {
            return maxSize * 0.7; // 70% = $140K для среднего спреда
        } else if (spreadPercent >= thresholds.LOW) {
            return maxSize * 0.5; // 50% = $100K для низкого спреда
        } else if (spreadPercent >= thresholds.MINIMAL) {
            return maxSize * 0.25; // 25% = $50K для минимального спреда
        } else {
            return Math.max(20000, this.positionConfig.MIN_POSITION_USD); // Минимум $20K
        }
    }

    /**
     * 🏊 КОРРЕКТИРОВКА НА ОСНОВЕ ЛИКВИДНОСТИ
     */
    adjustForLiquidity(baseSize, liquidityMillion) {
        // 🔥 АГРЕССИВНАЯ КОРРЕКТИРОВКА НА ОСНОВЕ ЛИКВИДНОСТИ!
        // Чем больше ликвидность, тем больше можем взять без влияния на цену

        let factor = 0.5; // Минимальный фактор для низкой ликвидности

        if (liquidityMillion >= 100) {
            factor = 1.2; // 120% для ультра-высокой ликвидности ($100M+)
        } else if (liquidityMillion >= 50) {
            factor = 1.0; // 100% для высокой ликвидности ($50M+)
        } else if (liquidityMillion >= 25) {
            factor = 0.9; // 90% для хорошей ликвидности ($25M+)
        } else if (liquidityMillion >= 10) {
            factor = 0.8; // 80% для средней ликвидности ($10M+)
        } else if (liquidityMillion >= 5) {
            factor = 0.7; // 70% для низкой ликвидности ($5M+)
        }

        const adjustedSize = Math.floor(baseSize * factor);

        // Обеспечиваем минимум $10K для любой ликвидности
        return Math.max(adjustedSize, 10000);
    }

    /**
     * 💪 FLASH LOANS НЕ ТРЕБУЮТ КОРРЕКТИРОВКИ РИСКОВ
     * Flash loans имеют стабильные риски - только комиссии транзакций
     * Используем максимально допустимый расчетный баланс для займа
     */
    // adjustForRisk() УДАЛЕН - flash loans имеют стабильные риски!

    /**
     * 🧠 ПОИСК ОПТИМАЛЬНОГО РАЗМЕРА ДЛЯ МАКСИМАЛЬНОЙ ПРИБЫЛИ
     */
    findOptimalSizeForMaxProfit(opportunity, analysis) {
        // Тестируем разные размеры позиций и находим тот, который дает максимальную прибыль
        const testSizes = [
            10000, 20000, 30000, 50000, 75000, 100000,
            125000, 150000, 175000, 200000
        ];

        let bestSize = 20000; // По умолчанию $20K
        let maxProfit = -Infinity;

        for (const testSize of testSizes) {
            if (testSize > this.positionConfig.MAX_POSITION_USD) break;

            // Рассчитываем прибыль для этого размера
            const profit = this.calculateExpectedProfit(testSize, opportunity.spread, analysis);

            // Если чистая прибыль больше предыдущей лучшей, обновляем
            if (profit.net > maxProfit) {
                maxProfit = profit.net;
                bestSize = testSize;
            }

            // Если прибыль начала падать, останавливаемся (нашли пик)
            if (profit.net < maxProfit * 0.95) {
                break;
            }
        }

        return bestSize;
    }

    /**
     * 🧠 ИНТЕГРАЦИЯ С УМНЫМ ОПТИМИЗАТОРОМ
     */
    integrateSmartOptimizerResult(baseSize, smartResult) {
        if (!smartResult.bestRoute) return baseSize;

        const roi = smartResult.bestRoute.roi;
        const netProfit = smartResult.bestRoute.netProfit;

        // Если умный оптимизатор показывает высокую прибыльность, увеличиваем позицию
        let multiplier = 1.0;

        if (roi >= 3.0) multiplier = 1.5;      // ROI 3%+ = увеличиваем на 50%
        else if (roi >= 1.5) multiplier = 1.3; // ROI 1.5%+ = увеличиваем на 30%
        else if (roi >= 0.8) multiplier = 1.1; // ROI 0.8%+ = увеличиваем на 10%
        else if (roi < 0.3) multiplier = 0.7;  // ROI <0.3% = уменьшаем на 30%

        return Math.floor(baseSize * multiplier);
    }

    /**
     * 🧠 УМНЫЙ РАСЧЕТ PRICE IMPACT НА ОСНОВЕ ЛИКВИДНОСТИ
     */
    calculateSmartPriceImpact(positionSize, liquidityMillion) {
        // Формула: impact = (positionSize / liquidity)^1.5 * baseImpactRate
        // Чем больше позиция относительно ликвидности, тем больше impact

        const liquidityUSD = liquidityMillion * 1000000; // Конвертируем в USD
        const positionRatio = positionSize / liquidityUSD; // Отношение позиции к ликвидности

        // Базовая ставка impact (0.01% для идеальных условий)
        const baseImpactRate = 0.0001;

        // Экспоненциальный рост impact при увеличении отношения позиции к ликвидности
        const impactMultiplier = Math.pow(positionRatio * 100, 1.2); // Степень 1.2 для плавного роста

        // Минимальный impact 0.001%, максимальный 1%
        const impactPercent = Math.max(0.00001, Math.min(0.01, baseImpactRate * impactMultiplier));

        return positionSize * impactPercent;
    }

    /**
     * 🎯 ПРИМЕНЕНИЕ ЛИМИТОВ ПОЗИЦИИ
     */
    applyPositionLimits(size) {
        return Math.max(
            this.positionConfig.MIN_POSITION_USD,
            Math.min(this.positionConfig.MAX_POSITION_USD, size)
        );
    }

    /**
     * 💰 РАСЧЕТ ОЖИДАЕМОЙ ПРИБЫЛИ (ИСПРАВЛЕНО ДЛЯ РЕАЛЬНЫХ УСЛОВИЙ)
     */
    calculateExpectedProfit(positionSize, spreadPercent, analysis) {
        // Базовая прибыль от спреда
        const grossProfit = positionSize * (spreadPercent / 100);

        // 🔧 ПРАВИЛЬНЫЕ КОМИССИИ ПО ДАННЫМ ПОЛЬЗОВАТЕЛЯ:
        // - Pool fees: 0.04% + 0.01% = 0.05% максимум
        // - MarginFi flash loan: 0% комиссий!
        // - Network fee: ~$0.005 фиксированная сумма
        // - Проскальзывание: НЕ УЧИТЫВАЕМ (компенсируем количеством сделок)
        const poolFees = positionSize * 0.0005; // 0.05% максимальные комиссии пулов
        const flashLoanFee = 0; // MarginFi flash loan БЕЗ комиссий!
        const networkFee = 0.005; // Фиксированная сетевая комиссия

        const totalFees = poolFees + flashLoanFee + networkFee;

        // 🧠 УМНЫЙ РАСЧЕТ PRICE IMPACT НА ОСНОВЕ ЛИКВИДНОСТИ
        const priceImpact = this.calculateSmartPriceImpact(positionSize, analysis.totalLiquidity);

        const netProfit = grossProfit - totalFees - priceImpact;

        return {
            gross: grossProfit,
            fees: totalFees,
            impact: priceImpact,
            net: netProfit,
            roi: (netProfit / positionSize) * 100,
            breakdown: {
                poolFees,
                networkFee,
                totalFees
            }
        };
    }

    /**
     * 💡 РЕКОМЕНДАЦИЯ ПО ПОЗИЦИИ
     */
    getPositionRecommendation(positionSize, expectedProfit, analysis) {
        const roi = expectedProfit.roi;

        if (roi >= 1.0) {
            return {
                action: '🚀 МАКСИМАЛЬНАЯ ПОЗИЦИЯ',
                reason: `ROI ${roi.toFixed(3)}% - отличная возможность!`,
                confidence: 'HIGH'
            };
        } else if (roi >= 0.5) {
            return {
                action: '💎 БОЛЬШАЯ ПОЗИЦИЯ',
                reason: `ROI ${roi.toFixed(3)}% - хорошая прибыльность`,
                confidence: 'MEDIUM'
            };
        } else if (roi >= 0.2) {
            return {
                action: '📊 СРЕДНЯЯ ПОЗИЦИЯ',
                reason: `ROI ${roi.toFixed(3)}% - умеренная прибыль`,
                confidence: 'MEDIUM'
            };
        } else if (roi >= 0.01) {
            return {
                action: '✅ ВЫПОЛНЯТЬ',
                reason: `ROI ${roi.toFixed(3)}% - минимальная прибыль`,
                confidence: 'LOW'
            };
        } else {
            return {
                action: '✅ ВЫПОЛНЯТЬ',
                reason: `ROI ${roi.toFixed(3)}% - ЛЮБАЯ ПРИБЫЛЬ ЛУЧШЕ ЧЕМ НИЧЕГО!`,
                confidence: 'MINIMAL'
            };
        }
    }

    /**
     * 📊 ОТОБРАЖЕНИЕ РЕЗУЛЬТАТА ОПТИМИЗАЦИИ
     */
    displayOptimizationResult(result) {
        console.log('\n📊 РЕЗУЛЬТАТ ОПТИМИЗАЦИИ ПОЗИЦИИ'.green.bold);
        console.log('═══════════════════════════════════════════════════════════════');

        console.log(`🎯 ОПТИМАЛЬНЫЙ РАЗМЕР ПОЗИЦИИ: $${result.optimalPositionSize.toLocaleString()}`.cyan.bold);
        console.log(`💰 ОЖИДАЕМАЯ ЧИСТАЯ ПРИБЫЛЬ: $${result.expectedProfit.net.toFixed(2)}`.green.bold);
        console.log(`📈 ОЖИДАЕМЫЙ ROI: ${result.expectedProfit.roi.toFixed(3)}%`.blue.bold);

        console.log('\n📋 ДЕТАЛЬНАЯ РАЗБИВКА:');
        console.log(`   📈 На основе спреда: $${result.breakdown.spreadBased.toLocaleString()}`);
        console.log(`   🏊 Корректировка ликвидности: $${result.breakdown.liquidityAdjusted.toLocaleString()}`);
        console.log(`   💪 БЕЗ корректировки рисков: ${result.breakdown.noRiskAdjustment}`);
        console.log(`   🎯 Финальный размер: $${result.breakdown.final.toLocaleString()}`);

        console.log('\n💰 ПРОГНОЗ ПРИБЫЛИ:');
        console.log(`   📈 Валовая прибыль: $${result.expectedProfit.gross.toFixed(2)}`);
        console.log(`   💸 Комиссии: $${result.expectedProfit.fees.toFixed(2)}`);
        console.log(`   📉 Price Impact: $${result.expectedProfit.impact.toFixed(2)}`);
        console.log(`   💎 Чистая прибыль: $${result.expectedProfit.net.toFixed(2)}`);

        console.log(`\n🎯 РЕКОМЕНДАЦИЯ: ${result.recommendation.action}`.yellow.bold);
        console.log(`   ${result.recommendation.reason}`);
        console.log(`   Уверенность: ${result.recommendation.confidence}`);
    }
}

module.exports = { DynamicPositionOptimizer };

// Тест при прямом запуске
if (require.main === module) {
    console.log('🧪 ТЕСТ ДИНАМИЧЕСКОГО ОПТИМИЗАТОРА ПОЗИЦИИ'.cyan.bold);

    const optimizer = new DynamicPositionOptimizer();

    // Тестовая возможность арбитража
    const testOpportunity = {
        token: 'SOL',
        spread: 0.75,           // 0.75% спред
        buyDex: 'Orca',
        sellDex: 'Raydium',
        buyPrice: 140.50,
        sellPrice: 141.55
    };

    const result = optimizer.calculateOptimalPositionSize(testOpportunity);

    console.log('\n🎯 ТЕСТ ЗАВЕРШЕН!'.green.bold);
    console.log(`Оптимальный размер позиции: $${result.optimalPositionSize.toLocaleString()}`);
}
