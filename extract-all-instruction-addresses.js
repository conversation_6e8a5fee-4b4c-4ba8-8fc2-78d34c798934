/**
 * 🔍 ИЗВЛЕЧЕНИЕ ВСЕХ АДРЕСОВ ИЗ КАЖДОЙ ИНСТРУКЦИИ
 * Детальный анализ всех 22 инструкций
 */

const { PublicKey } = require('@solana/web3.js');
const fs = require('fs');

class InstructionAddressExtractor {
    constructor() {
        this.allAddresses = new Set();
        this.instructionAddresses = [];
    }

    /**
     * 🔍 ИЗВЛЕЧЕНИЕ АДРЕСОВ ИЗ СУЩЕСТВУЮЩЕГО ФАЙЛА
     */
    async extractAllAddresses() {
        console.log('🔍 ИЗВЛЕЧЕНИЕ ВСЕХ АДРЕСОВ ИЗ 22 ИНСТРУКЦИЙ\n');

        try {
            // Читаем существующий файл вывода
            const output = fs.readFileSync('instruction-output.txt', 'utf8');
            this.parseInstructionOutput(output);
            return this.analyzeAddresses();
        } catch (error) {
            console.error('❌ Ошибка чтения файла:', error.message);
            console.log('🔄 Попробуем запустить программу заново...');

            // Запускаем программу заново
            const { spawn } = require('child_process');

            return new Promise((resolve, reject) => {
                const childProcess = spawn('node', ['complete-flash-loan-structure.js'], {
                    stdio: ['pipe', 'pipe', 'pipe'],
                    cwd: __dirname
                });

                let output = '';
                let errorOutput = '';

                childProcess.stdout.on('data', (data) => {
                    output += data.toString();
                });

                childProcess.stderr.on('data', (data) => {
                    errorOutput += data.toString();
                });

                childProcess.on('close', (code) => {
                    if (code === 0) {
                        this.parseInstructionOutput(output);
                        resolve(this.analyzeAddresses());
                    } else {
                        console.error('❌ Ошибка выполнения программы:', errorOutput);
                        reject(new Error(`Process exited with code ${code}`));
                    }
                });
            });
        }
    }

    /**
     * 📋 ПАРСИНГ ВЫВОДА ИНСТРУКЦИЙ
     */
    parseInstructionOutput(output) {
        console.log('📋 ПАРСИНГ ВЫВОДА ИНСТРУКЦИЙ...\n');
        
        const lines = output.split('\n');
        let currentInstruction = null;
        let instructionIndex = 0;
        
        for (const line of lines) {
            // Ищем начало новой инструкции
            if (line.includes('Инструкция') || line.includes('instruction') || 
                line.includes('BORROW') || line.includes('SWAP') || 
                line.includes('REPAY') || line.includes('Position')) {
                
                if (currentInstruction) {
                    this.instructionAddresses.push(currentInstruction);
                }
                
                currentInstruction = {
                    index: instructionIndex++,
                    type: this.extractInstructionType(line),
                    addresses: new Set(),
                    description: line.trim()
                };
            }
            
            // Извлекаем адреса из строки
            const addresses = this.extractAddressesFromLine(line);
            if (addresses.length > 0 && currentInstruction) {
                addresses.forEach(addr => {
                    currentInstruction.addresses.add(addr);
                    this.allAddresses.add(addr);
                });
            }
        }
        
        // Добавляем последнюю инструкцию
        if (currentInstruction) {
            this.instructionAddresses.push(currentInstruction);
        }
        
        console.log(`📊 Обработано ${this.instructionAddresses.length} инструкций`);
        console.log(`📋 Найдено ${this.allAddresses.size} уникальных адресов\n`);
    }

    /**
     * 🔍 ИЗВЛЕЧЕНИЕ АДРЕСОВ ИЗ СТРОКИ
     */
    extractAddressesFromLine(line) {
        // Регулярное выражение для Solana адресов
        const addressRegex = /[1-9A-HJ-NP-Za-km-z]{32,44}/g;
        const matches = line.match(addressRegex) || [];
        
        return matches.filter(addr => {
            // Фильтруем только валидные Solana адреса
            return addr.length >= 32 && addr.length <= 44 && 
                   !addr.includes('...') && // Исключаем сокращенные адреса
                   !line.includes('Ошибка') && // Исключаем ошибки
                   !line.includes('undefined'); // Исключаем undefined
        });
    }

    /**
     * 🏷️ ОПРЕДЕЛЕНИЕ ТИПА ИНСТРУКЦИИ
     */
    extractInstructionType(line) {
        if (line.includes('ComputeBudget')) return 'COMPUTE_BUDGET';
        if (line.includes('START') && line.includes('Flash')) return 'START_FLASH_LOAN';
        if (line.includes('BORROW')) return 'BORROW';
        if (line.includes('WSOL') && line.includes('SOL')) return 'WSOL_CONVERSION';
        if (line.includes('ATA')) return 'CREATE_ATA';
        if (line.includes('initialize_position')) return 'INITIALIZE_POSITION';
        if (line.includes('add_liquidity')) return 'ADD_LIQUIDITY';
        if (line.includes('SWAP') || line.includes('swap')) return 'SWAP';
        if (line.includes('REMOVE') || line.includes('remove')) return 'REMOVE_LIQUIDITY';
        if (line.includes('claimFee') || line.includes('Collect Fee')) return 'COLLECT_FEE';
        if (line.includes('REPAY')) return 'REPAY';
        if (line.includes('END') && line.includes('Flash')) return 'END_FLASH_LOAN';
        return 'UNKNOWN';
    }

    /**
     * 📊 АНАЛИЗ АДРЕСОВ
     */
    analyzeAddresses() {
        console.log('📊 АНАЛИЗ ВСЕХ ИЗВЛЕЧЕННЫХ АДРЕСОВ:\n');
        
        // Загружаем ALT таблицы
        const altAddresses = this.loadALTTables();
        
        // Анализируем покрытие
        const covered = [];
        const uncovered = [];
        
        this.allAddresses.forEach(address => {
            if (altAddresses.has(address)) {
                covered.push(address);
            } else {
                uncovered.push(address);
            }
        });
        
        console.log(`📋 ОБЩАЯ СТАТИСТИКА:`);
        console.log(`   📊 Всего адресов: ${this.allAddresses.size}`);
        console.log(`   ✅ Покрыто ALT: ${covered.length} (${(covered.length/this.allAddresses.size*100).toFixed(1)}%)`);
        console.log(`   ❌ НЕ покрыто ALT: ${uncovered.length} (${(uncovered.length/this.allAddresses.size*100).toFixed(1)}%)\n`);
        
        // Детальный анализ по инструкциям
        console.log('📋 ДЕТАЛЬНЫЙ АНАЛИЗ ПО ИНСТРУКЦИЯМ:');
        this.instructionAddresses.forEach((instruction, index) => {
            const addressArray = Array.from(instruction.addresses);
            const coveredCount = addressArray.filter(addr => altAddresses.has(addr)).length;
            const uncoveredCount = addressArray.length - coveredCount;
            
            console.log(`   ${index + 1}. ${instruction.type} (${addressArray.length} адресов)`);
            console.log(`      ✅ Покрыто: ${coveredCount}, ❌ НЕ покрыто: ${uncoveredCount}`);
            
            if (uncoveredCount > 0) {
                const uncoveredInInstruction = addressArray.filter(addr => !altAddresses.has(addr));
                uncoveredInInstruction.forEach(addr => {
                    console.log(`         ❌ ${addr.slice(0, 8)}...`);
                });
            }
        });
        
        console.log('\n🚨 ВСЕ НЕПОКРЫТЫЕ АДРЕСА:');
        uncovered.forEach((address, index) => {
            console.log(`   ${index + 1}. ${address}`);
        });
        
        return {
            totalAddresses: this.allAddresses.size,
            coveredCount: covered.length,
            uncoveredCount: uncovered.length,
            uncoveredAddresses: uncovered,
            instructionBreakdown: this.instructionAddresses.map(inst => ({
                type: inst.type,
                addressCount: inst.addresses.size,
                addresses: Array.from(inst.addresses)
            })),
            compressionPotential: {
                addressCount: uncovered.length,
                byteSavings: uncovered.length * 32,
                kbSavings: (uncovered.length * 32 / 1024).toFixed(1)
            }
        };
    }

    /**
     * 📋 ЗАГРУЗКА ALT ТАБЛИЦ
     */
    loadALTTables() {
        try {
            const fileData = JSON.parse(fs.readFileSync('correct-alt-tables-cache.json', 'utf8'));
            const altAddresses = new Set();
            
            for (const [tableName, tableData] of Object.entries(fileData.tables)) {
                if (tableData.addresses) {
                    tableData.addresses.forEach(addr => altAddresses.add(addr));
                }
            }
            
            return altAddresses;
            
        } catch (error) {
            console.error('❌ Ошибка загрузки ALT таблиц:', error.message);
            return new Set();
        }
    }
}

// 🚀 ЗАПУСК АНАЛИЗА
async function runDetailedAnalysis() {
    try {
        console.log('🔍 ДЕТАЛЬНЫЙ АНАЛИЗ ВСЕХ АДРЕСОВ ИЗ ИНСТРУКЦИЙ\n');
        
        const extractor = new InstructionAddressExtractor();
        const results = await extractor.extractAllAddresses();
        
        console.log('\n🎯 ПОТЕНЦИАЛ СЖАТИЯ:');
        console.log(`   📉 Можно сжать: ${results.compressionPotential.addressCount} адресов`);
        console.log(`   💾 Экономия: ~${results.compressionPotential.byteSavings} байт (${results.compressionPotential.kbSavings} KB)`);
        
        // Сохраняем результат
        const analysisResult = {
            timestamp: new Date().toISOString(),
            summary: results,
            detailedBreakdown: results.instructionBreakdown
        };
        
        fs.writeFileSync('detailed-address-analysis.json', JSON.stringify(analysisResult, null, 2));
        console.log('\n💾 Результат сохранен в detailed-address-analysis.json');
        
        return results;
        
    } catch (error) {
        console.error('❌ Ошибка анализа:', error.message);
        return null;
    }
}

// Запускаем анализ
if (require.main === module) {
    runDetailedAnalysis();
}

module.exports = { InstructionAddressExtractor, runDetailedAnalysis };
