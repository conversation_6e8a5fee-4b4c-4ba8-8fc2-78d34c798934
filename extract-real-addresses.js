/**
 * 🔥 ИЗВЛЕЧЕНИЕ РЕАЛЬНЫХ АДРЕСОВ ИЗ НАШИХ ТРАНЗАКЦИЙ
 */

const { Connection, PublicKey } = require('@solana/web3.js');
const fs = require('fs');
require('dotenv').config({ path: '.env.solana' });

// 🎯 РЕАЛЬНЫЕ АДРЕСА ИЗ НАШИХ ТРАНЗАКЦИЙ (ИЗ ЛОГОВ И SOLSCAN)
const REAL_ADDRESSES = {
    // 🌪️ METEORA DLMM ПРОГРАММА
    METEORA_DLMM_PROGRAM: 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo',
    
    // 🎯 НАШИ ОСНОВНЫЕ ПУЛЫ
    POOL_1_WSOL_USDC: '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6',
    POOL_2_WSOL_USDC: 'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y',
    
    // 🎯 НАШИ ПОЗИЦИИ (ИЗ ТРАНЗАКЦИИ)
    POSITION_1: '5XYfyyDBpNt8aTuvxe6RAQ7f11WwNWyD3ZUJkfJzDFKU',
    POSITION_2: 'A7tpj5uz4pCHiCaBSuTFt8EWmSBFp8qrzoVVCsuVfXgC',
    
    // 🏊 POOL RESERVES (ИЗ SOLSCAN ТРАНЗАКЦИИ)
    POOL_1_RESERVE_X: '8BnEgHoWFysVcuFFX7QztDmzuH8r5ZFvyP3sYwn1XTh6', // Meteora (WSOL-USDC) Pool 1
    POOL_1_RESERVE_Y: '5YMMM5JdkqC4kHKKmJxVEQk1NHdaVnpmX9fW9YTSJft1', // Meteora (WSOL-USDC) Pool 2
    POOL_2_RESERVE_X: '8BnEgHoWFysVcuFFX7QztDmzuH8r5ZFvyP3sYwn1XTh6', // Meteora (WSOL-USDC) Pool 1 (тот же)
    POOL_2_RESERVE_Y: '5YMMM5JdkqC4kHKKmJxVEQk1NHdaVnpmX9fW9YTSJft1', // Meteora (WSOL-USDC) Pool 2 (тот же)
    
    // 📊 BIN ARRAYS (ИЗ ТРАНЗАКЦИИ)
    POOL_1_BIN_ARRAYS: [
        'Hd6qVSiPQELZq3FAXPCumWmnKPx4BbZXd9782TEeRY2x', // Bin Array (из add_liquidity_by_strategy2 #7)
        'B7XxwUwnUhtmXLnBvcGxuA1byHaAhrE7oiz53nxT82tw', // Bin Array Lower (из remove_liquidity #13)
        '5kY2AvUZoSVABoNXoaPfDVtCAJLAFRz8RsX78BECz8NC'  // Bin Array Upper (из remove_liquidity #13)
    ],
    POOL_2_BIN_ARRAYS: [
        'Dbw8mACQmyrvbvLZs9bzAA6Tg2GVf5cmLzVbkfRzahRS', // Bin Array (из add_liquidity_by_strategy2 #8)
        'DjyDh54Q6LqPkARYmgjgLUKmwo1BZGDcY5Z5MFwvPc4N', // Bin Array Lower (из remove_liquidity #14)
        'FcYLBnripHnpmGFMwrwMkT1LBRhBWjn5ftwBmCaJLdDb'  // Bin Array Upper (из remove_liquidity #14)
    ],
    
    // 🔮 ORACLES (ИЗ SWAP ИНСТРУКЦИЙ)
    POOL_1_ORACLE: '59YuGWPunbchD2mbi9U7qvjWQKQReGeepn4ZSr9zz9Li', // Oracle (из swap2 #9)
    POOL_2_ORACLE: 'ETc6tqgLrr7wXsH8u2QBK1CyXHX3kvV6WQjBz4cf3sCj', // Oracle (из swap2 #12)
    
    // 🎪 EVENT AUTHORITY (ИЗ ВСЕХ METEORA ИНСТРУКЦИЙ)
    METEORA_EVENT_AUTHORITY: 'D1ZN9Wj1fRSUQfCjhvnu1hqDMT7hzjzBBpi12nVniYD6',
    
    // 👤 USER TOKEN ACCOUNTS (ИЗ ТРАНЗАКЦИИ)
    USER_USDC_ATA: '3AWxcMzxsTeBQ6YC1khJ9NkPtmLw2XbCGxvZHZpbAQFo',
    USER_WSOL_ATA: '68rtTtSuEPR84Wo1xWGs6ytBttn7JN33Ux8WsDp38FHk',
    
    // 🎯 BITMAP EXTENSIONS (ИЗ ТРАНЗАКЦИИ)
    BITMAP_EXTENSION_1: 'DArpuqJxNLRGQ8xq5ebZbobyjxSWWsPq8MqSZ2fUZLE', // Bin Array Bitmap Extension (из swap2 #9)
    BITMAP_EXTENSION_2: 'DjyDh54Q6LqPkARYmgjgLUKmwo1BZGDcY5Z5MFwvPc4N', // Bin Array Bitmap Extension (из swap2 #12)
    
    // 🎪 MEMO PROGRAM V2 (ИЗ SWAP)
    MEMO_PROGRAM_V2: 'Memo1UhkJRfHyvLMcVucJwxXeuD728EqVDDwQDxFMNo',
    
    // 💰 TOKEN MINTS (СТАНДАРТНЫЕ)
    USDC_MINT: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
    WSOL_MINT: 'So11111111111111111111111111111111111111112',
    
    // 🏦 MARGINFI ACCOUNT (НАШ)
    MARGINFI_ACCOUNT: '3dPECtRBPcYtcJG6wWDCr7VCyzxttDTjgGgo9gWC9vmU'
};

async function addRealAddressesToALT() {
    console.log('🔥 ДОБАВЛЕНИЕ РЕАЛЬНЫХ АДРЕСОВ В ALT ТАБЛИЦЫ');
    console.log('=' .repeat(60));
    
    // Читаем текущие ALT таблицы
    let altData;
    try {
        altData = JSON.parse(fs.readFileSync('custom-alt-data.json', 'utf8'));
        console.log(`📊 Текущие ALT таблицы загружены`);
    } catch (error) {
        console.log(`❌ Ошибка чтения ALT файла: ${error.message}`);
        return;
    }
    
    // Собираем все новые адреса
    const newAddresses = [];
    const addressCategories = {};
    
    Object.entries(REAL_ADDRESSES).forEach(([key, value]) => {
        if (Array.isArray(value)) {
            value.forEach(addr => {
                newAddresses.push(addr);
                addressCategories[addr] = key;
            });
        } else {
            newAddresses.push(value);
            addressCategories[value] = key;
        }
    });
    
    // Проверяем какие адреса уже есть
    const existingAddresses = new Set();
    Object.values(altData.tables).forEach(table => {
        if (table.addresses) {
            table.addresses.forEach(addr => existingAddresses.add(addr));
        }
    });
    
    const reallyNewAddresses = newAddresses.filter(addr => !existingAddresses.has(addr));
    
    console.log(`📊 СТАТИСТИКА:`);
    console.log(`   🎯 Всего адресов для добавления: ${newAddresses.length}`);
    console.log(`   ✅ Уже в ALT: ${newAddresses.length - reallyNewAddresses.length}`);
    console.log(`   🆕 Действительно новых: ${reallyNewAddresses.length}`);
    
    if (reallyNewAddresses.length === 0) {
        console.log(`✅ Все адреса уже в ALT таблицах!`);
        return;
    }
    
    // Добавляем новые адреса в Custom таблицу
    if (!altData.tables.custom) {
        console.log(`❌ Custom таблица не найдена!`);
        return;
    }
    
    const customTable = altData.tables.custom;
    const oldCount = customTable.addresses.length;
    
    // Добавляем новые адреса
    reallyNewAddresses.forEach(addr => {
        customTable.addresses.push(addr);
    });
    
    // Обновляем метаданные
    customTable.accountCount = customTable.addresses.length;
    altData.totalAccounts = Object.values(altData.tables).reduce((sum, table) => sum + table.accountCount, 0);
    altData.timestamp = new Date().toISOString();
    altData.source = "🔥 ОБНОВЛЕНО - ДОБАВЛЕНЫ РЕАЛЬНЫЕ АДРЕСА ИЗ ТРАНЗАКЦИЙ";
    
    // Сохраняем обновленный файл
    fs.writeFileSync('custom-alt-data.json', JSON.stringify(altData, null, 4));
    
    console.log(`\n🎯 РЕЗУЛЬТАТ:`);
    console.log(`   📊 Custom таблица: ${oldCount} → ${customTable.accountCount} адресов (+${reallyNewAddresses.length})`);
    console.log(`   📊 Всего адресов: ${altData.totalAccounts}`);
    console.log(`   💾 Файл обновлен: custom-alt-data.json`);
    
    console.log(`\n🆕 ДОБАВЛЕННЫЕ АДРЕСА:`);
    reallyNewAddresses.forEach(addr => {
        console.log(`   ${addr} (${addressCategories[addr]})`);
    });
    
    console.log(`\n💡 ЭКОНОМИЯ:`);
    console.log(`   📊 Сэкономим ~${reallyNewAddresses.length * 32} байт на транзакцию`);
    console.log(`   🚀 Транзакции станут меньше на ~${Math.round(reallyNewAddresses.length * 32 / 1232 * 100)}%`);
    
    return {
        added: reallyNewAddresses.length,
        totalAddresses: altData.totalAccounts,
        savings: reallyNewAddresses.length * 32
    };
}

addRealAddressesToALT().catch(console.error);
