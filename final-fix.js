const fs = require('fs');

console.log('🔥 ФИНАЛЬНОЕ ИСПРАВЛЕНИЕ...');

// Читаем файл построчно
const lines = fs.readFileSync('BMeteora.js', 'utf8').split('\n');

// Находим и исправляем строку 528
for (let i = 0; i < lines.length; i++) {
    if (lines[i].includes('const priceData = exactPrices[poolAddress];')) {
        console.log(`Найдена строка ${i + 1}: ${lines[i]}`);
        lines[i] = '                const priceData = exactPrices.get(poolAddress);';
        console.log(`Исправлена на: ${lines[i]}`);
        break;
    }
}

// Записываем обратно
fs.writeFileSync('BMeteora.js', lines.join('\n'));

console.log('✅ ИСПРАВЛЕНИЕ ЗАВЕРШЕНО!');

// Проверяем
const content = fs.readFileSync('BMeteora.js', 'utf8');
if (content.includes('exactPrices.get(poolAddress)')) {
    console.log('🎉 УСПЕШНО! Теперь система будет работать правильно');
} else {
    console.log('❌ Что-то пошло не так');
}
