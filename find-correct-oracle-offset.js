const { Connection, PublicKey } = require('@solana/web3.js');

async function findCorrectOracleOffset() {
    const connection = new Connection('https://api.mainnet-beta.solana.com');
    
    console.log('🔮 ПОИСК ПРАВИЛЬНОГО ORACLE OFFSET ДЛЯ USDC БАНКА...\n');
    
    const usdcBank = new PublicKey('2s37akK2eyBbp8DZgCm7RtsaEz8eJP3Nxd4urLHQv7yB');
    
    // Получаем bank data
    const bankInfo = await connection.getAccountInfo(usdcBank);
    if (!bankInfo) {
        console.log('❌ Банк не найден');
        return;
    }
    
    console.log('🔍 ТЕСТИРУЕМ РАЗНЫЕ OFFSETS ДЛЯ ORACLE:');
    
    // Известные oracle программы
    const oraclePrograms = {
        'SW1TCH7qEPTdLsDHRgPuMQjbQxKdH2aBStViMFnt64f': 'Switchboard',
        'FsJ3A3u2vn5cTVofAjvy6y5kwABJAqYWpe4975bi2epH': 'Pyth',
        'HEvSKofvBgfaexv23kMabbYqxasxU3mQ4ibBMEmJWHny': 'Chainlink'
    };
    
    // Тестируем разные offsets
    const testOffsets = [
        200, 232, 264, 296, 328, 360, 392, 424, 456, 488,
        520, 552, 584, 616, 648, 680, 712, 744, 776, 808,
        840, 872, 904, 936, 968, 1000, 1032, 1064, 1096, 1128,
        1160, 1192, 1224, 1256, 1288, 1320, 1352, 1384, 1416, 1448,
        1480, 1512, 1544, 1576, 1608, 1640, 1672, 1704, 1736, 1768,
        1800, 1832
    ];
    
    for (const offset of testOffsets) {
        if (offset + 32 > bankInfo.data.length) continue;
        
        try {
            const oracleBytes = bankInfo.data.slice(offset, offset + 32);
            const oracle = new PublicKey(oracleBytes);
            const oracleStr = oracle.toString();
            
            // Пропускаем нулевые ключи и System Program
            if (oracleStr.startsWith('11111111111111111111111111111111')) continue;
            
            console.log(`\n   Offset ${offset}: ${oracleStr}`);
            
            // Проверяем существование
            const oracleInfo = await connection.getAccountInfo(oracle);
            if (oracleInfo) {
                const owner = oracleInfo.owner.toString();
                console.log(`     ✅ Существует! Owner: ${owner}`);
                console.log(`     📊 Data length: ${oracleInfo.data.length}`);
                
                // Проверяем это oracle программа?
                if (oraclePrograms[owner]) {
                    console.log(`     🔮 ЭТО ${oraclePrograms[owner]} ORACLE!`);
                    console.log(`     🎯 ПРАВИЛЬНЫЙ OFFSET: ${offset}`);
                } else if (owner === 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA') {
                    console.log(`     🪙 Token Program Account (не oracle)`);
                } else if (owner === 'MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA') {
                    console.log(`     🏦 MarginFi Program Account`);
                } else {
                    console.log(`     ❓ Неизвестная программа: ${owner}`);
                }
            } else {
                console.log(`     ❌ Не существует`);
            }
        } catch (e) {
            // Игнорируем невалидные ключи
        }
    }
    
    console.log('\n🎉 ПОИСК ORACLE OFFSET ЗАВЕРШЕН!');
}

findCorrectOracleOffset().catch(console.error);
