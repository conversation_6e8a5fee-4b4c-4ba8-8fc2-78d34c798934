const { Connection, PublicKey } = require('@solana/web3.js');

/**
 * 🔧 ПОИСК ПРАВИЛЬНОГО MARGINFI GROUP ДЛЯ АККАУНТА
 * Цель: найти Group, к которому принадлежит ваш MarginFi аккаунт
 */

async function findMarginFiGroup() {
    try {
        console.log('🔧 ПОИСК ПРАВИЛЬНОГО MARGINFI GROUP...');
        
        // Подключение к Solana
        const connection = new Connection('https://api.mainnet-beta.solana.com', 'confirmed');
        
        // 🎯 ВАШ РЕАЛЬНЫЙ MARGINFI АККАУНТ
        const MARGINFI_ACCOUNT = new PublicKey('3dPECtRBPcYtcJG6wWDCr7VCyzxttDTjgGgo9gWC9vmU');
        
        console.log(`✅ Анализируем ваш MarginFi аккаунт: ${MARGINFI_ACCOUNT.toString()}`);
        
        // Получаем данные аккаунта
        const accountInfo = await connection.getAccountInfo(MARGINFI_ACCOUNT);
        
        if (!accountInfo) {
            throw new Error('MarginFi аккаунт не найден');
        }
        
        console.log(`✅ Аккаунт найден:`);
        console.log(`   Owner: ${accountInfo.owner.toString()}`);
        console.log(`   Data length: ${accountInfo.data.length}`);
        console.log(`   Lamports: ${accountInfo.lamports}`);
        
        // Анализируем данные аккаунта
        const data = accountInfo.data;
        console.log(`\n🔧 АНАЛИЗ ДАННЫХ АККАУНТА:`);
        console.log(`   Первые 32 байта (hex): ${data.slice(0, 32).toString('hex')}`);
        console.log(`   Первые 8 байт (discriminator): [${Array.from(data.slice(0, 8)).join(', ')}]`);
        
        // MarginFi аккаунт обычно содержит ссылку на Group в начале данных
        // Попробуем найти PublicKey в данных (32 байта)
        console.log(`\n🔧 ПОИСК GROUP PUBKEY В ДАННЫХ:`);
        
        const possibleGroups = [];
        
        // Проверяем разные позиции в данных, где может быть Group PublicKey
        for (let offset = 8; offset <= data.length - 32; offset += 8) {
            try {
                const possibleGroupBytes = data.slice(offset, offset + 32);
                const possibleGroup = new PublicKey(possibleGroupBytes);
                
                // Проверяем, является ли это валидным PublicKey (не все нули)
                const isValid = !possibleGroupBytes.every(byte => byte === 0);
                
                if (isValid) {
                    possibleGroups.push({
                        offset,
                        pubkey: possibleGroup.toString(),
                        bytes: possibleGroupBytes
                    });
                }
            } catch (error) {
                // Игнорируем невалидные PublicKey
            }
        }
        
        console.log(`   Найдено ${possibleGroups.length} возможных Group PublicKey:`);
        
        for (const group of possibleGroups.slice(0, 10)) { // Показываем первые 10
            console.log(`   Offset ${group.offset}: ${group.pubkey}`);
        }
        
        console.log(`\n🔧 ПРОВЕРКА ВОЗМОЖНЫХ GROUPS:`);
        
        // Проверяем каждый возможный Group
        for (const group of possibleGroups.slice(0, 5)) { // Проверяем первые 5
            try {
                const groupPubkey = new PublicKey(group.pubkey);
                const groupInfo = await connection.getAccountInfo(groupPubkey);
                
                if (groupInfo && groupInfo.owner.toString() === 'MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA') {
                    console.log(`   ✅ НАЙДЕН ВАЛИДНЫЙ GROUP: ${group.pubkey}`);
                    console.log(`      Offset: ${group.offset}`);
                    console.log(`      Owner: ${groupInfo.owner.toString()}`);
                    console.log(`      Data length: ${groupInfo.data.length}`);
                    
                    // Это может быть правильный Group!
                    return group.pubkey;
                } else {
                    console.log(`   ❌ Не MarginFi Group: ${group.pubkey}`);
                }
                
                // Небольшая задержка между запросами
                await new Promise(resolve => setTimeout(resolve, 100));
                
            } catch (error) {
                console.log(`   ❌ Ошибка проверки ${group.pubkey}: ${error.message}`);
            }
        }
        
        console.log(`\n🔧 ПРОВЕРКА ИЗВЕСТНЫХ GROUPS:`);
        
        // Проверяем известные MarginFi Groups
        const knownGroups = [
            '4qp6Fx6tnZkY5Wropq9wUYgtFxXKwE6viZxFHg3rdAG8', // Оригинальный
            'JBu1AL4obBcCMqKBBxhpWCNUt136ijcuMZLFvTP7iWdB', // Альтернативный
            '3Ux1jBrhCfVPJoXrmKdBZTBYdwHqvJJp8bYmFchGZBNG', // Еще один
        ];
        
        for (const groupAddress of knownGroups) {
            try {
                const groupPubkey = new PublicKey(groupAddress);
                const groupInfo = await connection.getAccountInfo(groupPubkey);
                
                if (groupInfo) {
                    console.log(`   ✅ Group ${groupAddress}:`);
                    console.log(`      Owner: ${groupInfo.owner.toString()}`);
                    console.log(`      Data length: ${groupInfo.data.length}`);
                    
                    // Проверяем discriminator Group аккаунта
                    const groupDiscriminator = groupInfo.data.slice(0, 8);
                    console.log(`      Discriminator: [${Array.from(groupDiscriminator).join(', ')}]`);
                    console.log(`      Hex: ${groupDiscriminator.toString('hex')}`);
                } else {
                    console.log(`   ❌ Group ${groupAddress}: не найден`);
                }
                
                await new Promise(resolve => setTimeout(resolve, 100));
                
            } catch (error) {
                console.log(`   ❌ Ошибка ${groupAddress}: ${error.message}`);
            }
        }
        
        console.log(`\n🔧 АНАЛИЗ СТРУКТУРЫ MARGINFI АККАУНТА:`);
        
        // Попробуем понять структуру MarginFi аккаунта
        console.log(`   Discriminator (0-8): [${Array.from(data.slice(0, 8)).join(', ')}]`);
        console.log(`   Возможный Group (8-40): ${new PublicKey(data.slice(8, 40)).toString()}`);
        console.log(`   Возможный Authority (40-72): ${new PublicKey(data.slice(40, 72)).toString()}`);
        
        // Проверяем, является ли байты 8-40 валидным Group
        try {
            const possibleGroup = new PublicKey(data.slice(8, 40));
            const groupInfo = await connection.getAccountInfo(possibleGroup);
            
            if (groupInfo && groupInfo.owner.toString() === 'MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA') {
                console.log(`\n🎯 НАЙДЕН ПРАВИЛЬНЫЙ GROUP: ${possibleGroup.toString()}`);
                return possibleGroup.toString();
            }
        } catch (error) {
            console.log(`   ❌ Байты 8-40 не являются валидным Group`);
        }
        
        console.log(`\n❌ ПРАВИЛЬНЫЙ GROUP НЕ НАЙДЕН`);
        console.log(`🔧 Возможно, нужно использовать MarginFi SDK для получения правильного Group`);
        
        return null;
        
    } catch (error) {
        console.error('❌ Ошибка поиска Group:', error.message);
        console.error('Stack:', error.stack);
        return null;
    }
}

// Запускаем поиск
findMarginFiGroup().then(result => {
    if (result) {
        console.log(`\n🎯 РЕЗУЛЬТАТ: ${result}`);
    } else {
        console.log(`\n❌ Group не найден`);
    }
});
