const { Connection, PublicKey } = require('@solana/web3.js');

async function findUsdcPythOracle() {
    const connection = new Connection('https://api.mainnet-beta.solana.com');
    
    console.log('🔮 ПОИСК ПРАВИЛЬНОГО USDC PYTH ORACLE...\n');
    
    // Известные USDC oracle адреса
    const knownUsdcOracles = [
        // Pyth USDC/USD
        'Gnt27xtC473ZT2Mw5u8wZ68Z3gULkSTb5DuxJy7eJotD',
        'En8hkHLkRe9d9DraYmBTrus518BvmVH448YcvmrFM6Ce',
        '5uQHSona1Moncw1NctZGXTPoWdaeTvbF19yCVvc8EqBb',
        // Switchboard USDC/USD  
        'CZx29wKMUxaJDq6aLVQTdViPL754tTR64NAgQBUGxxHb',
        'BjUgj6YCnFBZ49wF54ddBVA9qu8TeqkFtkbqmZcee8uW',
        // Chainlink USDC/USD
        'HEvSKofvBgfaexv23kMabbYqxasxU3mQ4ibBMEmJWHny'
    ];
    
    console.log('🔍 ПРОВЕРЯЕМ ИЗВЕСТНЫЕ USDC ORACLE АДРЕСА:');
    
    for (const oracleAddr of knownUsdcOracles) {
        try {
            const oracle = new PublicKey(oracleAddr);
            console.log(`\n   Oracle: ${oracleAddr}`);
            
            const oracleInfo = await connection.getAccountInfo(oracle);
            if (oracleInfo) {
                const owner = oracleInfo.owner.toString();
                console.log(`     ✅ Существует! Owner: ${owner}`);
                console.log(`     📊 Data length: ${oracleInfo.data.length}`);
                
                // Определяем тип oracle
                if (owner === 'FsJ3A3u2vn5cTVofAjvy6y5kwABJAqYWpe4975bi2epH') {
                    console.log(`     🔮 PYTH ORACLE!`);
                } else if (owner === 'SW1TCH7qEPTdLsDHRgPuMQjbQxKdH2aBStViMFnt64f') {
                    console.log(`     🔮 SWITCHBOARD ORACLE!`);
                } else if (owner === 'HEvSKofvBgfaexv23kMabbYqxasxU3mQ4ibBMEmJWHny') {
                    console.log(`     🔮 CHAINLINK ORACLE!`);
                } else {
                    console.log(`     ❓ Неизвестная oracle программа: ${owner}`);
                }
                
                // Проверяем данные oracle (если это Pyth)
                if (owner === 'FsJ3A3u2vn5cTVofAjvy6y5kwABJAqYWpe4975bi2epH' && oracleInfo.data.length > 0) {
                    console.log(`     📊 Pyth oracle data найдена`);
                }
            } else {
                console.log(`     ❌ Не существует`);
            }
        } catch (e) {
            console.log(`     ❌ Ошибка: ${e.message}`);
        }
    }
    
    // Также проверим стандартный Pyth USDC/USD oracle
    console.log('\n🔍 ПРОВЕРЯЕМ СТАНДАРТНЫЙ PYTH USDC/USD ORACLE:');
    
    try {
        // Это стандартный Pyth USDC/USD price feed
        const pythUsdcOracle = new PublicKey('Gnt27xtC473ZT2Mw5u8wZ68Z3gULkSTb5DuxJy7eJotD');
        console.log(`   Standard Pyth USDC/USD: ${pythUsdcOracle.toString()}`);
        
        const oracleInfo = await connection.getAccountInfo(pythUsdcOracle);
        if (oracleInfo) {
            console.log(`     ✅ Существует! Owner: ${oracleInfo.owner.toString()}`);
            console.log(`     📊 Data length: ${oracleInfo.data.length}`);
            
            if (oracleInfo.owner.toString() === 'FsJ3A3u2vn5cTVofAjvy6y5kwABJAqYWpe4975bi2epH') {
                console.log(`     🎯 ЭТО ПРАВИЛЬНЫЙ PYTH USDC/USD ORACLE!`);
            }
        }
    } catch (e) {
        console.log(`     ❌ Ошибка: ${e.message}`);
    }
    
    console.log('\n🎉 ПОИСК USDC ORACLE ЗАВЕРШЕН!');
}

findUsdcPythOracle().catch(console.error);
