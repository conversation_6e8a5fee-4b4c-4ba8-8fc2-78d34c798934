/**
 * 🔍 ПОИСК РАБОЧЕГО MARGINFI АККАУНТА С BALANCES
 * 
 * Ищем MarginFi аккаунт с активными balances для flash loan
 */

const { Connection, PublicKey } = require('@solana/web3.js');

class MarginFiAccountFinder {
    constructor() {
        this.connection = new Connection('https://billowing-empty-patron.solana-mainnet.quiknode.pro/c79212a7508ec6a37353793d9be93c917898e858/', 'confirmed');
        
        // 🔥 MARGINFI КОНСТАНТЫ
        this.MARGINFI_PROGRAM_ID = new PublicKey('MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA');
        this.MARGINFI_GROUP = new PublicKey('4qp6Fx6tnZkY5Wropq9wUYgtFxXKwE6viZxFHg3rdAG8');
        this.WALLET = new PublicKey('bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV');
        
        // 🏦 ИЗВЕСТНЫЕ БАНКИ
        this.BANKS = {
            USDC: new PublicKey('2s37akK2eyBbp8DZgCm7RtsaEz8eJP3Nxd4urLHQv7yB'),
            SOL: new PublicKey('CCKtUs6Cgwo4aaQUmBPmyoApH2gUDErxNZCAntD6LYGh'),
            USDT: new PublicKey('BFBVrMmJJLdVKdqyFQzjQFcRXEqhTBNHXaFBtYeURAhz'),
        };
    }

    /**
     * 🔍 ПОИСК ВСЕХ MARGINFI АККАУНТОВ ПОЛЬЗОВАТЕЛЯ
     */
    async findAllMarginFiAccounts() {
        try {
            console.log('🔍 ПОИСК ВСЕХ MARGINFI АККАУНТОВ ПОЛЬЗОВАТЕЛЯ');
            console.log('═══════════════════════════════════════════════════════════════');
            
            console.log(`👤 Кошелек: ${this.WALLET.toString()}`);
            console.log(`🏦 MarginFi Group: ${this.MARGINFI_GROUP.toString()}`);
            
            // Ищем все возможные MarginFi аккаунты через деривацию
            const accounts = [];
            
            for (let seed = 0; seed < 10; seed++) {
                try {
                    const [marginfiAccount] = await PublicKey.findProgramAddress(
                        [
                            Buffer.from('marginfi_account'),
                            this.WALLET.toBuffer(),
                            this.MARGINFI_GROUP.toBuffer(),
                            Buffer.from([seed])
                        ],
                        this.MARGINFI_PROGRAM_ID
                    );
                    
                    console.log(`\n🔧 Проверяем seed ${seed}: ${marginfiAccount.toString()}`);
                    
                    const accountInfo = await this.connection.getAccountInfo(marginfiAccount);
                    if (accountInfo) {
                        console.log(`   ✅ Аккаунт существует! Размер: ${accountInfo.data.length} bytes`);
                        
                        // Анализируем balances
                        const balanceAnalysis = await this.analyzeAccountBalances(marginfiAccount, accountInfo.data);
                        
                        accounts.push({
                            seed,
                            address: marginfiAccount,
                            data: accountInfo.data,
                            balances: balanceAnalysis
                        });
                    } else {
                        console.log(`   ❌ Аккаунт не существует`);
                    }
                } catch (error) {
                    console.log(`   ❌ Ошибка seed ${seed}: ${error.message}`);
                }
            }
            
            console.log(`\n📊 Найдено MarginFi аккаунтов: ${accounts.length}`);
            return accounts;
            
        } catch (error) {
            console.error(`❌ Ошибка поиска аккаунтов: ${error.message}`);
            return [];
        }
    }

    /**
     * 🔍 АНАЛИЗ BALANCES В АККАУНТЕ
     */
    async analyzeAccountBalances(accountAddress, data) {
        try {
            const balancesStart = 74;
            const balancesData = data.slice(balancesStart);
            const BALANCE_SIZE = 184;
            const maxBalances = Math.floor(balancesData.length / BALANCE_SIZE);
            
            const balances = [];
            
            for (let i = 0; i < maxBalances; i++) {
                const balanceOffset = i * BALANCE_SIZE;
                const isActive = balancesData[balanceOffset] === 1;
                
                if (isActive) {
                    const bankOffset = balanceOffset + 8;
                    const bankBytes = balancesData.slice(bankOffset, bankOffset + 32);
                    const bankPubkey = new PublicKey(bankBytes);
                    
                    const assetSharesOffset = bankOffset + 32;
                    const liabilitySharesOffset = assetSharesOffset + 16;
                    
                    const assetShares = balancesData.readBigUInt64LE(assetSharesOffset);
                    const liabilityShares = balancesData.readBigUInt64LE(liabilitySharesOffset);
                    
                    // Определяем токен
                    let tokenName = 'UNKNOWN';
                    for (const [name, bankAddr] of Object.entries(this.BANKS)) {
                        if (bankAddr.equals(bankPubkey)) {
                            tokenName = name;
                            break;
                        }
                    }
                    
                    balances.push({
                        index: i,
                        bank: bankPubkey,
                        token: tokenName,
                        assetShares: assetShares,
                        liabilityShares: liabilityShares,
                        hasAssets: assetShares > 0n,
                        hasLiabilities: liabilityShares > 0n,
                        canFlashLoan: assetShares > 0n || liabilityShares === 0n
                    });
                    
                    console.log(`      ✅ Balance ${i + 1}: ${tokenName}`);
                    console.log(`         💰 Assets: ${assetShares.toString()}`);
                    console.log(`         💸 Liabilities: ${liabilityShares.toString()}`);
                    console.log(`         🚀 Flash Loan: ${assetShares > 0n || liabilityShares === 0n ? 'ДА' : 'НЕТ'}`);
                }
            }
            
            return balances;
            
        } catch (error) {
            console.error(`❌ Ошибка анализа balances: ${error.message}`);
            return [];
        }
    }

    /**
     * 🎯 ПОИСК ЛУЧШЕГО АККАУНТА ДЛЯ FLASH LOAN
     */
    async findBestAccountForFlashLoan() {
        try {
            console.log('\n🎯 ПОИСК ЛУЧШЕГО АККАУНТА ДЛЯ FLASH LOAN');
            console.log('═══════════════════════════════════════════════════════════════');
            
            const accounts = await this.findAllMarginFiAccounts();
            
            if (accounts.length === 0) {
                console.log('❌ НЕ НАЙДЕНО НИ ОДНОГО MARGINFI АККАУНТА');
                return null;
            }
            
            // Ищем аккаунт с доступными balances для flash loan
            let bestAccount = null;
            let bestScore = -1;
            
            for (const account of accounts) {
                const availableBalances = account.balances.filter(b => b.canFlashLoan);
                const score = availableBalances.length;
                
                console.log(`\n📊 Аккаунт ${account.address.toString()}:`);
                console.log(`   🌱 Seed: ${account.seed}`);
                console.log(`   📊 Всего balances: ${account.balances.length}`);
                console.log(`   🚀 Доступно для flash loan: ${availableBalances.length}`);
                
                if (availableBalances.length > 0) {
                    console.log(`   ✅ ДОСТУПНЫЕ ТОКЕНЫ:`);
                    availableBalances.forEach(balance => {
                        console.log(`      ${balance.token}: ${balance.bank.toString()}`);
                    });
                }
                
                if (score > bestScore) {
                    bestScore = score;
                    bestAccount = account;
                }
            }
            
            if (bestAccount) {
                console.log(`\n🏆 ЛУЧШИЙ АККАУНТ ДЛЯ FLASH LOAN:`);
                console.log(`   📍 Адрес: ${bestAccount.address.toString()}`);
                console.log(`   🌱 Seed: ${bestAccount.seed}`);
                console.log(`   🚀 Доступных токенов: ${bestAccount.balances.filter(b => b.canFlashLoan).length}`);
                
                const recommendedToken = bestAccount.balances.find(b => b.canFlashLoan);
                if (recommendedToken) {
                    console.log(`   💡 РЕКОМЕНДУЕМЫЙ ТОКЕН: ${recommendedToken.token}`);
                    console.log(`   🏦 БАНК: ${recommendedToken.bank.toString()}`);
                }
                
                return bestAccount;
            } else {
                console.log('\n❌ НЕ НАЙДЕНО ПОДХОДЯЩИХ АККАУНТОВ ДЛЯ FLASH LOAN');
                return null;
            }
            
        } catch (error) {
            console.error(`❌ Ошибка поиска лучшего аккаунта: ${error.message}`);
            return null;
        }
    }

    /**
     * 🔧 ГЕНЕРАЦИЯ КОДА ДЛЯ ОБНОВЛЕНИЯ БОТА
     */
    generateBotUpdateCode(bestAccount) {
        if (!bestAccount) {
            console.log('\n❌ НЕТ АККАУНТА ДЛЯ ГЕНЕРАЦИИ КОДА');
            return;
        }
        
        console.log('\n🔧 КОД ДЛЯ ОБНОВЛЕНИЯ БОТА:');
        console.log('═══════════════════════════════════════════════════════════════');
        
        const recommendedToken = bestAccount.balances.find(b => b.canFlashLoan);
        
        console.log('// 🔥 ОБНОВЛЕНИЕ MARGINFI КОНФИГУРАЦИИ');
        console.log(`this.marginfiAccount = new PublicKey('${bestAccount.address.toString()}');`);
        
        if (recommendedToken) {
            console.log(`this.DEFAULT_FLASH_LOAN_BANK = new PublicKey('${recommendedToken.bank.toString()}');`);
            console.log(`// Токен: ${recommendedToken.token}`);
            
            if (recommendedToken.token === 'SOL') {
                console.log(`this.DEFAULT_FLASH_LOAN_MINT = new PublicKey('So11111111111111111111111111111111111111112');`);
            } else if (recommendedToken.token === 'USDC') {
                console.log(`this.DEFAULT_FLASH_LOAN_MINT = new PublicKey('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v');`);
            } else if (recommendedToken.token === 'USDT') {
                console.log(`this.DEFAULT_FLASH_LOAN_MINT = new PublicKey('Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB');`);
            }
        }
        
        console.log('\n💡 СКОПИРУЙ ЭТОТ КОД В low-level-marginfi-integration.js');
        console.log('🎯 ЗАМЕНИ СУЩЕСТВУЮЩИЕ ЗНАЧЕНИЯ НА ЭТИ');
    }
}

async function main() {
    console.log('🔍 ПОИСК РАБОЧЕГО MARGINFI АККАУНТА');
    console.log('═══════════════════════════════════════════════════════════════');
    console.log('🎯 Ищем MarginFi аккаунт с активными balances для flash loan');
    console.log('💡 Цель: найти аккаунт без ошибки 3002');
    console.log('═══════════════════════════════════════════════════════════════');

    const finder = new MarginFiAccountFinder();
    const bestAccount = await finder.findBestAccountForFlashLoan();
    
    if (bestAccount) {
        finder.generateBotUpdateCode(bestAccount);
        console.log('\n✅ НАЙДЕН РАБОЧИЙ АККАУНТ!');
        console.log('🚀 Обнови бот с новой конфигурацией');
    } else {
        console.log('\n❌ НЕ НАЙДЕНО РАБОЧИХ АККАУНТОВ');
        console.log('💡 Нужно создать lending balance в существующем аккаунте');
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = MarginFiAccountFinder;
