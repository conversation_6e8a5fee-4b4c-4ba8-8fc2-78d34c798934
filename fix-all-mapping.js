/**
 * 🔥 ПОЛНОЕ ИСПРАВЛЕНИЕ ВСЕХ ПРОБЛЕМ МАППИНГА
 */

const fs = require('fs');

console.log('🔥 ПОЛНОЕ ИСПРАВЛЕНИЕ МАППИНГА...');

// Читаем файл
let content = fs.readFileSync('BMeteora.js', 'utf8');

// Заменяем все проблемные места
content = content.replace(
    'const priceData = exactPrices[poolAddress];',
    '// 🔥 ИСПРАВЛЕНО: exactPrices это Map, используем get()\n                const priceData = exactPrices.get(poolAddress);'
);

// Записываем исправленный файл
fs.writeFileSync('BMeteora.js', content);

console.log('✅ Основная проблема исправлена!');

// Проверяем результат
const updatedContent = fs.readFileSync('BMeteora.js', 'utf8');
if (updatedContent.includes('exactPrices.get(poolAddress)')) {
    console.log('🎉 ИСПРАВЛЕНИЕ УСПЕШНО ПРИМЕНЕНО!');
} else {
    console.log('❌ Исправление не применилось');
}

console.log('✅ Файл готов к тестированию');
