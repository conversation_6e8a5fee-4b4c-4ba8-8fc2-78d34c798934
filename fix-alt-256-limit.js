#!/usr/bin/env node

/**
 * 🔧 ИСПРАВЛЕНИЕ ALT ТАБЛИЦ - ЛИМИТ 256 АККАУНТОВ
 * Убираем лишние аккаунты из первой Meteora ALT таблицы до лимита 256
 */

const fs = require('fs');

async function fixALT256Limit() {
    console.log('🔧 ИСПРАВЛЕНИЕ ALT ТАБЛИЦ - ЛИМИТ 256 АККАУНТОВ');
    console.log('=' .repeat(80));

    try {
        // 1. Загружаем Meteora кэш
        console.log('📁 Загрузка Meteora кэша...');
        
        const meteoraFile = './meteora-alt-cache.json';
        if (!fs.existsSync(meteoraFile)) {
            throw new Error(`❌ Meteora кэш не найден: ${meteoraFile}`);
        }
        
        const meteoraCache = JSON.parse(fs.readFileSync(meteoraFile, 'utf8'));
        console.log(`✅ Meteora кэш загружен: ${meteoraCache.totalTables} таблиц`);

        // 2. Проверяем первую ALT таблицу
        const firstALT = meteoraCache.validationResults[0];
        if (!firstALT || !firstALT.valid) {
            throw new Error('❌ Первая Meteora ALT таблица не найдена или невалидна');
        }
        
        console.log(`\n🔍 АНАЛИЗ ПЕРВОЙ ALT ТАБЛИЦЫ:`);
        console.log(`   Адрес: ${firstALT.address}`);
        console.log(`   Текущих аккаунтов: ${firstALT.accounts.length}`);
        console.log(`   Лимит Solana: 256 аккаунтов`);
        console.log(`   Превышение: ${firstALT.accounts.length - 256} аккаунтов`);

        if (firstALT.accounts.length <= 256) {
            console.log(`✅ ALT таблица уже в пределах лимита!`);
            return;
        }

        // 3. Загружаем специализированные аккаунты
        console.log('\n📁 Загрузка специализированных аккаунтов...');
        
        const specializedFile = './specialized-arbitrage-alt.json';
        if (!fs.existsSync(specializedFile)) {
            throw new Error(`❌ Специализированный файл не найден: ${specializedFile}`);
        }
        
        const specializedCache = JSON.parse(fs.readFileSync(specializedFile, 'utf8'));
        console.log(`✅ Специализированных аккаунтов: ${specializedCache.totalAccounts}`);

        // 4. Приоритизируем аккаунты
        console.log('\n🎯 ПРИОРИТИЗАЦИЯ АККАУНТОВ...');
        
        const specializedSet = new Set(specializedCache.accounts);
        const priorityAccounts = [];
        const regularAccounts = [];
        
        firstALT.accounts.forEach(account => {
            if (specializedSet.has(account)) {
                priorityAccounts.push(account);
            } else {
                regularAccounts.push(account);
            }
        });
        
        console.log(`✅ Приоритетных аккаунтов: ${priorityAccounts.length}`);
        console.log(`✅ Обычных аккаунтов: ${regularAccounts.length}`);

        // 5. Создаем оптимизированный список
        console.log('\n🔧 СОЗДАНИЕ ОПТИМИЗИРОВАННОГО СПИСКА...');
        
        const optimizedAccounts = [];
        
        // Сначала добавляем все приоритетные аккаунты
        optimizedAccounts.push(...priorityAccounts);
        
        // Затем добавляем обычные аккаунты до лимита 256
        const remainingSlots = 256 - priorityAccounts.length;
        optimizedAccounts.push(...regularAccounts.slice(0, remainingSlots));
        
        console.log(`✅ Оптимизированных аккаунтов: ${optimizedAccounts.length}`);
        console.log(`   Приоритетных: ${priorityAccounts.length}`);
        console.log(`   Обычных: ${Math.min(remainingSlots, regularAccounts.length)}`);
        console.log(`   Удалено: ${firstALT.accounts.length - optimizedAccounts.length}`);

        // 6. Обновляем первую ALT таблицу
        console.log('\n🔄 ОБНОВЛЕНИЕ ПЕРВОЙ ALT ТАБЛИЦЫ...');
        
        const originalCount = firstALT.accounts.length;
        
        meteoraCache.validationResults[0] = {
            ...firstALT,
            accountCount: optimizedAccounts.length,
            accounts: optimizedAccounts,
            optimized: true,
            optimizedTimestamp: new Date().toISOString(),
            originalCount: originalCount,
            removedCount: originalCount - optimizedAccounts.length,
            priorityAccountsCount: priorityAccounts.length
        };
        
        // Обновляем общую статистику
        meteoraCache.totalAccounts = meteoraCache.validationResults.reduce((sum, result) => {
            return sum + (result.valid ? result.accounts.length : 0);
        }, 0);
        
        console.log(`✅ ПЕРВАЯ ALT ТАБЛИЦА ОПТИМИЗИРОВАНА:`);
        console.log(`   Было аккаунтов: ${originalCount}`);
        console.log(`   Стало аккаунтов: ${optimizedAccounts.length}`);
        console.log(`   Удалено: ${originalCount - optimizedAccounts.length}`);
        console.log(`   Приоритетных сохранено: ${priorityAccounts.length}`);

        // 7. Создаем бэкап и сохраняем
        console.log('\n💾 СОХРАНЕНИЕ ИЗМЕНЕНИЙ...');
        
        const backupFile = './meteora-alt-cache-before-256-fix.json';
        const originalCache = JSON.parse(fs.readFileSync(meteoraFile, 'utf8'));
        fs.writeFileSync(backupFile, JSON.stringify(originalCache, null, 2));
        console.log(`✅ Бэкап сохранен: ${backupFile}`);
        
        fs.writeFileSync(meteoraFile, JSON.stringify(meteoraCache, null, 2));
        console.log(`✅ Оптимизированный кэш сохранен: ${meteoraFile}`);

        // 8. Проверяем покрытие специализированных аккаунтов
        console.log('\n🔍 ПРОВЕРКА ПОКРЫТИЯ СПЕЦИАЛИЗИРОВАННЫХ АККАУНТОВ...');
        
        const optimizedSet = new Set(optimizedAccounts);
        const coveredSpecialized = specializedCache.accounts.filter(account => 
            optimizedSet.has(account)
        );
        
        const coverage = (coveredSpecialized.length / specializedCache.totalAccounts * 100).toFixed(1);
        
        console.log(`📊 ПОКРЫТИЕ СПЕЦИАЛИЗИРОВАННЫХ АККАУНТОВ:`);
        console.log(`   Всего специализированных: ${specializedCache.totalAccounts}`);
        console.log(`   Покрыто в оптимизированной ALT: ${coveredSpecialized.length}`);
        console.log(`   Покрытие: ${coverage}%`);

        // 9. Проверяем все ALT таблицы на лимиты
        console.log('\n🔍 ПРОВЕРКА ВСЕХ ALT ТАБЛИЦ НА ЛИМИТЫ...');
        
        let allWithinLimits = true;
        meteoraCache.validationResults.forEach((result, index) => {
            if (result.valid && result.accounts) {
                const withinLimit = result.accounts.length <= 256;
                console.log(`   ALT ${index + 1}: ${result.accounts.length} аккаунтов ${withinLimit ? '✅' : '❌'}`);
                if (!withinLimit) {
                    allWithinLimits = false;
                }
            }
        });
        
        console.log(`\n📊 СТАТУС ВСЕХ ALT ТАБЛИЦ: ${allWithinLimits ? '✅ ВСЕ В ПРЕДЕЛАХ ЛИМИТА' : '❌ ЕСТЬ ПРЕВЫШЕНИЯ'}`);

        // 10. Итоговый результат
        console.log('\n🎯 ИТОГОВЫЙ РЕЗУЛЬТАТ:');
        console.log('=' .repeat(50));
        
        console.log(`🔧 ОПТИМИЗАЦИЯ ЗАВЕРШЕНА:`);
        console.log(`   Первая ALT таблица: ${originalCount} → ${optimizedAccounts.length} аккаунтов`);
        console.log(`   Удалено аккаунтов: ${originalCount - optimizedAccounts.length}`);
        console.log(`   Покрытие специализированных: ${coverage}%`);
        console.log(`   Все ALT в пределах лимита: ${allWithinLimits ? 'ДА' : 'НЕТ'}`);
        
        if (allWithinLimits && parseFloat(coverage) >= 90) {
            console.log(`\n🔥 ИДЕАЛЬНЫЙ РЕЗУЛЬТАТ! ALT ТАБЛИЦЫ ГОТОВЫ ДЛЯ РАБОТЫ!`);
        } else if (allWithinLimits) {
            console.log(`\n✅ ХОРОШИЙ РЕЗУЛЬТАТ! ALT таблицы в пределах лимита`);
        } else {
            console.log(`\n⚠️ ТРЕБУЕТСЯ ДОПОЛНИТЕЛЬНАЯ ОПТИМИЗАЦИЯ`);
        }
        
        console.log(`📊 Покрытие: ${coverage}%`);
        console.log(`🚀 ALT таблицы оптимизированы для максимальной производительности!`);

        // 11. Создаем тестовый скрипт
        const testScript = `#!/usr/bin/env node

/**
 * 🧪 ТЕСТ ОПТИМИЗИРОВАННЫХ ALT ТАБЛИЦ (256 ЛИМИТ)
 */

const { Connection, PublicKey, TransactionMessage, VersionedTransaction, SystemProgram, Keypair } = require('@solana/web3.js');
const fs = require('fs');

async function testOptimizedALT() {
    console.log('🧪 ТЕСТ ОПТИМИЗИРОВАННЫХ ALT ТАБЛИЦ (256 ЛИМИТ)');
    console.log('=' .repeat(60));
    
    try {
        const connection = new Connection('https://api.mainnet-beta.solana.com');
        const wallet = Keypair.generate();
        const { blockhash } = await connection.getLatestBlockhash();
        
        // Загружаем оптимизированные ALT таблицы
        const meteoraCache = JSON.parse(fs.readFileSync('./meteora-alt-cache.json', 'utf8'));
        const marginfiCache = JSON.parse(fs.readFileSync('./marginfi-alt-cache.json', 'utf8'));
        
        const altTables = [];
        
        // Meteora ALT
        meteoraCache.validationResults.forEach(result => {
            if (result.valid && result.accounts) {
                console.log(\`📊 Meteora ALT: \${result.accounts.length} аккаунтов (\${result.accounts.length <= 256 ? '✅' : '❌'})\`);
                altTables.push({
                    key: new PublicKey(result.address),
                    state: { addresses: result.accounts.map(addr => new PublicKey(addr)) }
                });
            }
        });
        
        // MarginFi ALT
        marginfiCache.validationResults.forEach(result => {
            if (result.valid && result.accounts) {
                console.log(\`📊 MarginFi ALT: \${result.accounts.length} аккаунтов (\${result.accounts.length <= 256 ? '✅' : '❌'})\`);
                altTables.push({
                    key: new PublicKey(result.address),
                    state: { addresses: result.accounts.map(addr => new PublicKey(addr)) }
                });
            }
        });
        
        console.log(\`\\n✅ ALT таблиц загружено: \${altTables.length}\`);
        
        // Создаем простую инструкцию
        const instruction = SystemProgram.transfer({
            fromPubkey: wallet.publicKey,
            toPubkey: wallet.publicKey,
            lamports: 1000
        });
        
        // Тестируем сжатие
        const message = new TransactionMessage({
            payerKey: wallet.publicKey,
            recentBlockhash: blockhash,
            instructions: [instruction]
        }).compileToV0Message(altTables);
        
        const tx = new VersionedTransaction(message);
        const size = tx.serialize().length;
        
        console.log(\`\\n📊 РЕЗУЛЬТАТЫ ТЕСТА:\`);
        console.log(\`   Размер транзакции: \${size} байт\`);
        console.log(\`   ALT lookups: \${message.addressTableLookups.length}\`);
        console.log(\`   Лимит Solana: 1232 байт\`);
        
        if (size <= 1232) {
            console.log(\`✅ ТРАНЗАКЦИЯ ПОМЕЩАЕТСЯ В ЛИМИТ!\`);
        } else {
            console.log(\`❌ Транзакция превышает лимит на \${size - 1232} байт\`);
        }
        
        console.log(\`\\n🎯 ТЕСТ ЗАВЕРШЕН: \${size <= 1232 ? 'УСПЕШНО' : 'ТРЕБУЕТСЯ ДОРАБОТКА'}\`);
        
    } catch (error) {
        console.error('❌ Ошибка теста:', error.message);
        if (error.message.includes('Max lookup table index exceeded')) {
            console.error('🚨 КРИТИЧЕСКАЯ ОШИБКА: Превышен лимит ALT индексов!');
            console.error('💡 РЕШЕНИЕ: Уменьшить количество аккаунтов в ALT таблицах до 256');
        }
    }
}

testOptimizedALT().catch(console.error);`;
        
        const testFile = './test-optimized-alt-256.js';
        fs.writeFileSync(testFile, testScript);
        console.log(`\n🧪 Тестовый скрипт создан: ${testFile}`);
        console.log(`   Запустите: node ${testFile}`);

    } catch (error) {
        console.error('❌ Ошибка исправления ALT:', error.message);
        console.error(error.stack);
    }
}

// Запуск исправления
if (require.main === module) {
    fixALT256Limit().catch(console.error);
}

module.exports = { fixALT256Limit };
