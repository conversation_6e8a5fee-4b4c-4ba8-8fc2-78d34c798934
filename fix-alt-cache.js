/**
 * 🔥 ИСПРАВЛЕНИЕ ALT КЭША - ПОЛУЧЕНИЕ РЕАЛЬНЫХ ДАННЫХ ИЗ БЛОКЧЕЙНА
 */

const { Connection, PublicKey } = require('@solana/web3.js');
const fs = require('fs');
require('dotenv').config({ path: '.env.solana' });

async function fixALTCache() {
    console.log('🔥 ИСПРАВЛЕНИЕ ALT КЭША - ПОЛУЧЕНИЕ РЕАЛЬНЫХ ДАННЫХ');
    console.log('=' .repeat(60));
    
    const connection = new Connection(process.env.SOLANA_RPC_URL || 'https://api.mainnet-beta.solana.com');
    
    const altAddresses = [
        'HGmknUTUmeovMc9ryERNWG6UFZDFDVr9xrum3ZhyL4fC', // MarginFi ALT
        'FAeyUf4AdG7vTQ2Q89r9hWBkPdYkykJKG4CxgqArBGXe'  // Custom ALT
    ];
    
    const tables = {};
    let totalAccounts = 0;
    
    for (let i = 0; i < altAddresses.length; i++) {
        const address = altAddresses[i];
        const tableName = i === 0 ? 'marginfi1' : 'custom';
        
        console.log(`🔍 Получаем данные ALT таблицы: ${address}`);
        
        try {
            const altAccount = await connection.getAddressLookupTable(new PublicKey(address));
            
            if (altAccount && altAccount.value) {
                const addresses = altAccount.value.state.addresses.map(addr => addr.toString());
                
                tables[tableName] = {
                    address: address,
                    accountCount: addresses.length,
                    addresses: addresses,
                    valid: true
                };
                
                totalAccounts += addresses.length;
                
                console.log(`✅ ${tableName}: ${addresses.length} адресов`);
                console.log(`   Первые 5: ${addresses.slice(0, 5).join(', ')}`);
                
            } else {
                console.log(`❌ ALT таблица не найдена: ${address}`);
                tables[tableName] = {
                    address: address,
                    accountCount: 0,
                    addresses: [],
                    valid: false
                };
            }
        } catch (error) {
            console.log(`❌ Ошибка получения ALT таблицы ${address}: ${error.message}`);
            tables[tableName] = {
                address: address,
                accountCount: 0,
                addresses: [],
                valid: false
            };
        }
    }
    
    // Создаем правильную структуру файла
    const altData = {
        timestamp: new Date().toISOString(),
        source: "🔥 ИСПРАВЛЕНО - РЕАЛЬНЫЕ ДАННЫЕ ИЗ БЛОКЧЕЙНА",
        totalTables: Object.keys(tables).length,
        totalAccounts: totalAccounts,
        tables: tables
    };
    
    // Сохраняем в файл
    const filename = 'custom-alt-data.json';
    fs.writeFileSync(filename, JSON.stringify(altData, null, 4));
    
    console.log(`\n🎯 РЕЗУЛЬТАТ:`);
    console.log(`📊 Всего таблиц: ${altData.totalTables}`);
    console.log(`📊 Всего адресов: ${altData.totalAccounts}`);
    console.log(`💾 Сохранено в: ${filename}`);
    
    Object.entries(tables).forEach(([name, data]) => {
        console.log(`   ${name}: ${data.accountCount} адресов (${data.valid ? 'ВАЛИДНО' : 'ОШИБКА'})`);
    });
    
    console.log(`\n✅ ALT КЭША ИСПРАВЛЕН!`);
}

fixALTCache().catch(console.error);
