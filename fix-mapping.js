/**
 * 🔥 ИСПРАВЛЕНИЕ МАППИНГА КЛЮЧЕЙ В BMETEORA.JS
 * 
 * ПРОБЛЕМА: система ищет exactPrices[poolAddress], но getAllExactPrices() возвращает Map
 * РЕШЕНИЕ: заменить на exactPrices.get(poolAddress)
 */

const fs = require('fs');

console.log('🔥 ИСПРАВЛЕНИЕ МАППИНГА КЛЮЧЕЙ...');

// Читаем файл
let content = fs.readFileSync('BMeteora.js', 'utf8');

// Исправляем все вхождения exactPrices[poolAddress] на exactPrices.get(poolAddress)
const fixes = [
    {
        old: 'const priceData = exactPrices[poolAddress];',
        new: '// 🔥 ИСПРАВЛЕНО: exactPrices это Map, используем get()\n                const priceData = exactPrices.get(poolAddress);'
    },
    {
        old: 'binId: priceData.binId,',
        new: 'binId: priceData.binId || \'unknown\','
    },
    {
        old: 'age: priceData.age,',
        new: 'age: Date.now() - priceData.timestamp,'
    },
    {
        old: 'console.log(`   ✅ ${poolKey}: $${priceData.price.toFixed(4)} (бин ID: ${priceData.binId}, возраст: ${priceData.age}ms)`);',
        new: 'console.log(`   ✅ ${poolKey}: $${priceData.price.toFixed(4)} (свежие данные)`);'
    }
];

let fixedCount = 0;

fixes.forEach(fix => {
    if (content.includes(fix.old)) {
        content = content.replace(fix.old, fix.new);
        fixedCount++;
        console.log(`✅ Исправлено: ${fix.old.slice(0, 50)}...`);
    }
});

// Записываем исправленный файл
fs.writeFileSync('BMeteora.js', content);

console.log(`🎉 ИСПРАВЛЕНО ${fixedCount} проблем маппинга!`);
console.log('✅ Файл BMeteora.js обновлен');
