#!/usr/bin/env node

/**
 * 🎯 ИСПРАВЛЕННЫЙ СИСТЕМНЫЙ КОНТРОЛЛЕР
 * ═══════════════════════════════════════════════════════════════
 * 🎯 ЦЕЛЬ: Единая точка входа для всей исправленной системы
 * 📋 ФУНКЦИИ: Полная интеграция всех компонентов
 * 🔧 АРХИТЕКТУРА: Все исправленные модули в единой системе
 */

const { Connection, Keypair, VersionedTransaction, TransactionMessage } = require('@solana/web3.js');
const { NodeWallet } = require('@mrgnlabs/mrgn-common');
const fs = require('fs');

// Импорт всех исправленных компонентов
const IntegratedInstructionProcessor = require('./integrated-instruction-processor.js');
const MasterTransactionController = require('./master-transaction-controller.js');

class FixedSystemController {
  constructor() {
    this.connection = null;
    this.wallet = null;
    this.instructionProcessor = null;
    this.transactionController = null;
    
    // Статистика системы
    this.stats = {
      initialized: false,
      totalProcessed: 0,
      successfulTransactions: 0,
      failedTransactions: 0,
      averageProcessingTime: 0,
      systemUptime: Date.now()
    };
    
    console.log('🎯 ИСПРАВЛЕННЫЙ СИСТЕМНЫЙ КОНТРОЛЛЕР СОЗДАН');
  }

  /**
   * 🚀 ИНИЦИАЛИЗАЦИЯ ВСЕЙ СИСТЕМЫ
   */
  async initialize() {
    console.log('\n🚀 ИНИЦИАЛИЗАЦИЯ ИСПРАВЛЕННОЙ СИСТЕМЫ');
    console.log('═'.repeat(60));

    try {
      // 1. Подключение к Solana
      console.log('1️⃣ Подключение к Solana RPC...');
      this.connection = new Connection(
        'https://billowing-empty-patron.solana-mainnet.quiknode.pro/c79212a7508ec6a37353793d9be93c917898e858/',
        'confirmed'
      );
      console.log(`✅ Подключение установлено: ${this.connection.rpcEndpoint}`);

      // 2. Загрузка кошелька
      console.log('\n2️⃣ Загрузка кошелька...');
      const walletData = JSON.parse(fs.readFileSync('wallet.json', 'utf8'));
      const keypair = Keypair.fromSecretKey(new Uint8Array(walletData));
      this.wallet = new NodeWallet(keypair);
      console.log(`✅ Кошелек загружен: ${this.wallet.publicKey.toString()}`);

      // 3. Инициализация процессора инструкций
      console.log('\n3️⃣ Инициализация процессора инструкций...');
      this.instructionProcessor = new IntegratedInstructionProcessor(this.connection, this.wallet);
      console.log('✅ Процессор инструкций готов');

      // 4. Инициализация контроллера транзакций
      console.log('\n4️⃣ Инициализация контроллера транзакций...');
      this.transactionController = new MasterTransactionController(this.connection, this.wallet);
      console.log('✅ Контроллер транзакций готов');

      // 5. Проверка системы
      console.log('\n5️⃣ Проверка системы...');
      const systemCheck = await this.performSystemCheck();
      
      if (!systemCheck.passed) {
        throw new Error(`Проверка системы провалена: ${systemCheck.errors.join(', ')}`);
      }

      this.stats.initialized = true;
      console.log('\n🎉 СИСТЕМА ПОЛНОСТЬЮ ИНИЦИАЛИЗИРОВАНА!');
      console.log('✅ Все компоненты работают корректно');
      console.log('🚀 Система готова к обработке транзакций');

      return true;

    } catch (error) {
      console.error(`❌ Ошибка инициализации системы: ${error.message}`);
      return false;
    }
  }

  /**
   * 🎯 ГЛАВНАЯ ФУНКЦИЯ: ОБРАБОТКА JUPITER АРБИТРАЖА
   * @param {Object} jupiterResponse - Ответ от Jupiter API
   * @returns {Object} - Результат обработки
   */
  async processJupiterArbitrage(jupiterResponse) {
    console.log('\n🎯 ОБРАБОТКА JUPITER АРБИТРАЖА');
    console.log('═'.repeat(60));

    if (!this.stats.initialized) {
      throw new Error('Система не инициализирована! Вызовите initialize() сначала');
    }

    const startTime = Date.now();

    try {
      this.stats.totalProcessed++;

      // ШАГ 1: Полная обработка Jupiter ответа
      console.log('1️⃣ Полная обработка Jupiter ответа...');
      const processedResult = await this.instructionProcessor.processJupiterResponse(jupiterResponse);
      
      console.log(`✅ Обработка завершена:`);
      console.log(`   📋 Инструкций: ${processedResult.instructions.length}`);
      console.log(`   🗜️ ALT таблиц: ${processedResult.addressLookupTableAccounts.length}`);
      console.log(`   ✅ Валидация: ${processedResult.validation.isValid ? 'ПРОЙДЕНА' : 'ПРОВАЛЕНА'}`);

      if (!processedResult.validation.isValid) {
        throw new Error(`Валидация провалена: ${processedResult.validation.errors.join(', ')}`);
      }

      // ШАГ 2: Оптимизация транзакции
      console.log('\n2️⃣ Оптимизация транзакции...');
      const optimizedTransaction = await this.transactionController.optimizeTransaction(
        processedResult.instructions,
        processedResult.addressLookupTableAccounts,
        'JUPITER_ARBITRAGE'
      );

      console.log(`✅ Оптимизация завершена:`);
      console.log(`   📊 Размер: ${optimizedTransaction.finalSize} байт`);
      console.log(`   🎭 Обфускация: уровень ${optimizedTransaction.obfuscationLevel || 0}`);
      console.log(`   🗜️ ALT оптимизация: ${optimizedTransaction.altOptimization?.optimizedALTs?.length || 0} таблиц`);

      // ШАГ 3: Создание финальной транзакции
      console.log('\n3️⃣ Создание финальной транзакции...');
      const finalTransaction = await this.createFinalTransaction(
        optimizedTransaction.instructions,
        optimizedTransaction.addressLookupTableAccounts
      );

      console.log(`✅ Финальная транзакция создана:`);
      console.log(`   📏 Реальный размер: ${finalTransaction.serialize().length} байт`);

      const processingTime = Date.now() - startTime;
      this.updateStats(processingTime, true);

      const result = {
        success: true,
        transaction: finalTransaction,
        instructions: optimizedTransaction.instructions,
        addressLookupTableAccounts: optimizedTransaction.addressLookupTableAccounts,
        stats: {
          processingTime,
          instructionCount: optimizedTransaction.instructions.length,
          altCount: optimizedTransaction.addressLookupTableAccounts.length,
          transactionSize: finalTransaction.serialize().length,
          validation: processedResult.validation
        },
        optimization: {
          finalSize: optimizedTransaction.finalSize,
          obfuscationLevel: optimizedTransaction.obfuscationLevel,
          altOptimization: optimizedTransaction.altOptimization
        }
      };

      console.log('\n🎉 JUPITER АРБИТРАЖ УСПЕШНО ОБРАБОТАН!');
      console.log(`⏱️ Время обработки: ${processingTime}ms`);
      console.log(`📊 Финальный размер: ${result.stats.transactionSize} байт`);

      return result;

    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.updateStats(processingTime, false);
      
      console.error(`❌ Ошибка обработки Jupiter арбитража: ${error.message}`);
      
      return {
        success: false,
        error: error.message,
        stats: {
          processingTime,
          failed: true
        }
      };
    }
  }

  /**
   * 🔧 СОЗДАНИЕ ФИНАЛЬНОЙ ТРАНЗАКЦИИ
   */
  async createFinalTransaction(instructions, addressLookupTableAccounts) {
    try {
      // Получаем свежий blockhash
      const { blockhash } = await this.connection.getLatestBlockhash('confirmed');

      // Создаем TransactionMessage с ALT поддержкой
      const messageV0 = new TransactionMessage({
        payerKey: this.wallet.publicKey,
        recentBlockhash: blockhash,
        instructions: instructions
      }).compileToV0Message(addressLookupTableAccounts);

      // Создаем VersionedTransaction
      const transaction = new VersionedTransaction(messageV0);

      // Подписываем транзакцию
      transaction.sign([this.wallet.payer]);

      return transaction;

    } catch (error) {
      throw new Error(`Ошибка создания финальной транзакции: ${error.message}`);
    }
  }

  /**
   * 🔍 ПРОВЕРКА СИСТЕМЫ
   */
  async performSystemCheck() {
    console.log('🔍 Выполнение проверки системы...');

    const check = {
      passed: true,
      errors: [],
      warnings: []
    };

    try {
      // Проверка подключения
      const slot = await this.connection.getSlot();
      console.log(`   ✅ RPC подключение: слот ${slot}`);

      // Проверка баланса кошелька
      const balance = await this.connection.getBalance(this.wallet.publicKey);
      console.log(`   ✅ Баланс кошелька: ${balance / 1e9} SOL`);
      
      if (balance < 1e6) { // Меньше 0.001 SOL
        check.warnings.push('Низкий баланс кошелька');
      }

      // Проверка компонентов
      if (!this.instructionProcessor) {
        check.errors.push('Процессор инструкций не инициализирован');
        check.passed = false;
      }

      if (!this.transactionController) {
        check.errors.push('Контроллер транзакций не инициализирован');
        check.passed = false;
      }

      console.log(`   ✅ Проверка завершена: ${check.passed ? 'ПРОЙДЕНА' : 'ПРОВАЛЕНА'}`);
      
      if (check.errors.length > 0) {
        console.log(`   ❌ Ошибки: ${check.errors.join(', ')}`);
      }
      
      if (check.warnings.length > 0) {
        console.log(`   ⚠️ Предупреждения: ${check.warnings.join(', ')}`);
      }

      return check;

    } catch (error) {
      check.passed = false;
      check.errors.push(`Ошибка проверки системы: ${error.message}`);
      return check;
    }
  }

  /**
   * 📊 ОБНОВЛЕНИЕ СТАТИСТИКИ
   */
  updateStats(processingTime, success) {
    if (success) {
      this.stats.successfulTransactions++;
    } else {
      this.stats.failedTransactions++;
    }

    // Обновляем среднее время обработки
    const totalTransactions = this.stats.successfulTransactions + this.stats.failedTransactions;
    this.stats.averageProcessingTime = (
      (this.stats.averageProcessingTime * (totalTransactions - 1) + processingTime) / totalTransactions
    );
  }

  /**
   * 📊 ПОЛУЧЕНИЕ СТАТИСТИКИ СИСТЕМЫ
   */
  getSystemStats() {
    const uptime = Date.now() - this.stats.systemUptime;
    
    return {
      ...this.stats,
      uptime: uptime,
      uptimeFormatted: this.formatUptime(uptime),
      successRate: this.stats.totalProcessed > 0 ? 
        ((this.stats.successfulTransactions / this.stats.totalProcessed) * 100).toFixed(1) + '%' : 
        '0%',
      componentStats: {
        instructionProcessor: this.instructionProcessor?.getStats(),
        transactionController: this.transactionController?.config
      }
    };
  }

  /**
   * 🕐 ФОРМАТИРОВАНИЕ ВРЕМЕНИ РАБОТЫ
   */
  formatUptime(ms) {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}ч ${minutes % 60}м ${seconds % 60}с`;
    } else if (minutes > 0) {
      return `${minutes}м ${seconds % 60}с`;
    } else {
      return `${seconds}с`;
    }
  }
}

module.exports = FixedSystemController;
