{"filesChecked": 217, "dangerousFiles": [{"file": "clear-marginfi-account-debts.js", "dangerous": [{"pattern": "/await\\s+.*\\.makeRepayIx\\s*\\(/g", "matches": ["await account.makeRepayIx("], "type": "DANGEROUS"}], "safe": [{"pattern": "/const\\s+repayIx\\s*=\\s*await.*\\.makeRepayIx/g", "matches": ["const repayIx = await account.makeRepayIx"], "type": "SAFE"}]}, {"file": "create-proper-marginfi-flash-loan-account.js", "dangerous": [{"pattern": "/await\\s+.*\\.makeBorrowIx\\s*\\(/g", "matches": ["await account.makeBorrowIx(", "await account.makeBorrowIx("], "type": "DANGEROUS"}, {"pattern": "/await\\s+.*\\.makeRepayIx\\s*\\(/g", "matches": ["await account.makeRepayIx(", "await account.makeRepayIx("], "type": "DANGEROUS"}], "safe": [{"pattern": "/const\\s+borrowIx\\s*=\\s*await.*\\.makeBorrowIx/g", "matches": ["const borrowIx = await account.makeBorrowIx"], "type": "SAFE"}, {"pattern": "/const\\s+repayIx\\s*=\\s*await.*\\.makeRepayIx/g", "matches": ["const repayIx = await account.makeRepayIx"], "type": "SAFE"}, {"pattern": "/buildFlashLoanTx\\s*\\(/g", "matches": ["buildFlashLoanTx("], "type": "SAFE"}]}, {"file": "src\\bundle\\jupiter-bundle-integration.js", "dangerous": [{"pattern": "/await\\s+.*\\.makeBorrowIx\\s*\\(/g", "matches": ["await marginfiAccount.makeBorrowIx(", "await marginfiAccount.makeBorrowIx("], "type": "DANGEROUS"}, {"pattern": "/await\\s+.*\\.makeRepayIx\\s*\\(/g", "matches": ["await marginfiAccount.makeRepayIx(", "await marginfiAccount.makeRepayIx("], "type": "DANGEROUS"}], "safe": [{"pattern": "/const\\s+borrowIx\\s*=\\s*await.*\\.makeBorrowIx/g", "matches": ["const borrowIx = await marginfiAccount.makeBorrowIx", "const borrowIx = await marginfiAccount.makeBorrowIx"], "type": "SAFE"}, {"pattern": "/const\\s+repayIx\\s*=\\s*await.*\\.makeRepayIx/g", "matches": ["const repayIx = await marginfiAccount.makeRepayIx", "const repayIx = await marginfiAccount.makeRepayIx"], "type": "SAFE"}, {"pattern": "/buildFlashLoanTx\\s*\\(/g", "matches": ["buildFlashLoanTx(", "buildFlashLoanTx(", "buildFlashLoanTx(", "buildFlashLoanTx (", "buildFlashLoanTx(", "buildFlashLoanTx(", "buildFlashLoanTx(", "buildFlashLoanTx (", "buildFlashLoanTx(", "buildFlashLoanTx("], "type": "SAFE"}]}, {"file": "test-new-marginfi-account.js", "dangerous": [{"pattern": "/await\\s+.*\\.makeBorrowIx\\s*\\(/g", "matches": ["await this.marginfiAccount.makeBorrowIx("], "type": "DANGEROUS"}, {"pattern": "/await\\s+.*\\.makeRepayIx\\s*\\(/g", "matches": ["await this.marginfiAccount.makeRepayIx("], "type": "DANGEROUS"}], "safe": [{"pattern": "/const\\s+borrowIx\\s*=\\s*await.*\\.makeBorrowIx/g", "matches": ["const borrowIx = await this.marginfiAccount.makeBorrowIx"], "type": "SAFE"}, {"pattern": "/const\\s+repayIx\\s*=\\s*await.*\\.makeRepayIx/g", "matches": ["const repayIx = await this.marginfiAccount.makeRepayIx"], "type": "SAFE"}, {"pattern": "/buildFlashLoanTx\\s*\\(/g", "matches": ["buildFlashLoanTx("], "type": "SAFE"}]}, {"file": "verify-flash-loan-safety.js", "dangerous": [{"pattern": "/processTransaction\\s*\\(\\s*borrowIx/g", "matches": ["processTransaction(borrowIx"], "type": "DANGEROUS"}, {"pattern": "/makeBorrowIx.*processTransaction/g", "matches": ["makeBorrowIx.*processTransaction"], "type": "DANGEROUS"}, {"pattern": "/makeRepayIx.*processTransaction/g", "matches": ["makeRepayIx.*processTransaction"], "type": "DANGEROUS"}], "safe": []}, {"file": "src\\bundle\\jupiter-bundle-integration.js", "dangerous": [{"pattern": "/await\\s+.*\\.makeBorrowIx\\s*\\(/g", "matches": ["await marginfiAccount.makeBorrowIx(", "await marginfiAccount.makeBorrowIx("], "type": "DANGEROUS"}, {"pattern": "/await\\s+.*\\.makeRepayIx\\s*\\(/g", "matches": ["await marginfiAccount.makeRepayIx(", "await marginfiAccount.makeRepayIx("], "type": "DANGEROUS"}], "safe": [{"pattern": "/const\\s+borrowIx\\s*=\\s*await.*\\.makeBorrowIx/g", "matches": ["const borrowIx = await marginfiAccount.makeBorrowIx", "const borrowIx = await marginfiAccount.makeBorrowIx"], "type": "SAFE"}, {"pattern": "/const\\s+repayIx\\s*=\\s*await.*\\.makeRepayIx/g", "matches": ["const repayIx = await marginfiAccount.makeRepayIx", "const repayIx = await marginfiAccount.makeRepayIx"], "type": "SAFE"}, {"pattern": "/buildFlashLoanTx\\s*\\(/g", "matches": ["buildFlashLoanTx(", "buildFlashLoanTx(", "buildFlashLoanTx(", "buildFlashLoanTx (", "buildFlashLoanTx(", "buildFlashLoanTx(", "buildFlashLoanTx(", "buildFlashLoanTx (", "buildFlashLoanTx(", "buildFlashLoanTx("], "type": "SAFE"}]}], "safeFiles": [{"file": "check-existing-marginfi-accounts.js", "safe": [{"pattern": "/buildFlashLoanTx\\s*\\(/g", "matches": ["buildFlashLoanTx("], "type": "SAFE"}]}, {"file": "create-new-safe-marginfi-account.js", "safe": [{"pattern": "/buildFlashLoanTx\\s*\\(/g", "matches": ["buildFlashLoanTx("], "type": "SAFE"}]}, {"file": "real-trading-executor.js", "safe": [{"pattern": "/buildFlashLoanTx\\s*\\(/g", "matches": ["buildFlashLoanTx ("], "type": "SAFE"}]}, {"file": "solana-flash-loans\\marginfi-flash-loan.js", "safe": [{"pattern": "/buildFlashLoanTx\\s*\\(/g", "matches": ["buildFlashLoanTx(", "buildFlashLoanTx("], "type": "SAFE"}]}, {"file": "solana-flash-loans\\solana-flash-loan-manager.js", "safe": [{"pattern": "/buildFlashLoanTx\\s*\\(/g", "matches": ["buildFlashLoanTx("], "type": "SAFE"}]}, {"file": "src\\atomic-transaction-builder-fixed.js", "safe": [{"pattern": "/buildFlashLoanTx\\s*\\(/g", "matches": ["buildFlashLoanTx("], "type": "SAFE"}]}, {"file": "test-final-marginfi-fix.js", "safe": [{"pattern": "/buildFlashLoanTx\\s*\\(/g", "matches": ["buildFlashLoanTx("], "type": "SAFE"}]}, {"file": "solana-flash-loans\\marginfi-flash-loan.js", "safe": [{"pattern": "/buildFlashLoanTx\\s*\\(/g", "matches": ["buildFlashLoanTx(", "buildFlashLoanTx("], "type": "SAFE"}]}, {"file": "solana-flash-loans\\solana-flash-loan-manager.js", "safe": [{"pattern": "/buildFlashLoanTx\\s*\\(/g", "matches": ["buildFlashLoanTx("], "type": "SAFE"}]}, {"file": "src\\atomic-transaction-builder-fixed.js", "safe": [{"pattern": "/buildFlashLoanTx\\s*\\(/g", "matches": ["buildFlashLoanTx("], "type": "SAFE"}]}], "errors": []}