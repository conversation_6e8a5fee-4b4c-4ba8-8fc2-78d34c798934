#!/usr/bin/env node

/**
 * 🔧 БЕСПЛАТНОЕ ОБНОВЛЕНИЕ MARGINFI АККАУНТОВ
 * ═══════════════════════════════════════════════════════════════
 * 🎯 ЦЕЛЬ: Принудительно перезагрузить данные аккаунтов (БЕСПЛАТНО!)
 * 📚 ИСТОЧНИК: Daily Solana Tip 83 - Reload accounts after CPI
 */

const { Connection, PublicKey } = require('@solana/web3.js');
const { MarginfiClient, getConfig } = require('@mrgnlabs/marginfi-client-v2');
const { Wallet } = require('@coral-xyz/anchor');
const fs = require('fs');

class FreeAccountReloader {
  constructor() {
    this.connection = null;
    this.wallet = null;
    this.marginfiClient = null;
    this.marginfiAccount = null;
  }

  async initialize() {
    try {
      console.log('🔧 БЕСПЛАТНОЕ ОБНОВЛЕНИЕ MARGINFI АККАУНТОВ');
      console.log('═══════════════════════════════════════════════════════════════');
      console.log('💰 СТОИМОСТЬ: БЕСПЛАТНО! (только чтение данных)');
      console.log('📚 ИСТОЧНИК: Daily Solana Tip 83 - Reload accounts after CPI');

      // Подключение к RPC с принудительным обновлением
      this.connection = new Connection(
        'https://solana-mainnet.g.alchemy.com/v2/alch-demo',
        {
          commitment: 'confirmed',
          confirmTransactionInitialTimeout: 60000,
          disableRetryOnRateLimit: false,
          httpHeaders: {
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          }
        }
      );
      console.log('✅ RPC подключение создано (без кэша)');

      // Загрузка wallet
      const secretKey = JSON.parse(fs.readFileSync('wallet.json', 'utf8'));
      const { Keypair } = require('@solana/web3.js');
      const keypair = Keypair.fromSecretKey(new Uint8Array(secretKey));
      this.wallet = new Wallet(keypair);
      console.log(`✅ Wallet загружен: ${this.wallet.publicKey.toString()}`);

      // Инициализация MarginFi с принудительным обновлением
      const config = getConfig('production');
      this.marginfiClient = await MarginfiClient.fetch(config, this.wallet, this.connection);
      console.log('✅ MarginFi client инициализирован');

      return true;
    } catch (error) {
      console.error(`❌ Ошибка инициализации: ${error.message}`);
      return false;
    }
  }

  async forceReloadAllAccounts() {
    try {
      console.log('\n🔄 ПРИНУДИТЕЛЬНАЯ ПЕРЕЗАГРУЗКА ВСЕХ АККАУНТОВ');
      console.log('═══════════════════════════════════════════════════════════════');

      // 1. Принудительно обновляем MarginFi Client
      console.log('🔄 Перезагружаем MarginFi Client...');
      const config = getConfig('production');
      this.marginfiClient = await MarginfiClient.fetch(config, this.wallet, this.connection);
      console.log('✅ MarginFi Client перезагружен');

      // 2. Ищем аккаунты заново
      console.log('🔍 Ищем MarginFi аккаунты заново...');
      const accounts = await this.marginfiClient.getMarginfiAccountsForAuthority(this.wallet.publicKey);
      
      if (accounts.length === 0) {
        console.log('❌ MarginFi аккаунты не найдены');
        return false;
      }

      this.marginfiAccount = accounts[0];
      console.log(`✅ MarginFi аккаунт найден: ${this.marginfiAccount.address.toString()}`);

      // 3. Принудительно перезагружаем аккаунт
      console.log('🔄 Принудительно перезагружаем аккаунт...');
      await this.marginfiAccount.reload();
      console.log('✅ Аккаунт перезагружен');

      // 4. Принудительно обновляем все банки
      console.log('🏦 Принудительно обновляем все банки...');
      const banks = Array.from(this.marginfiClient.banks.values());
      
      for (const bank of banks) {
        try {
          // Принудительно получаем свежие данные банка
          const freshBankInfo = await this.connection.getAccountInfo(bank.address, 'confirmed');
          if (freshBankInfo) {
            console.log(`✅ Банк ${bank.config.assetSymbol} обновлен`);
          }
        } catch (bankError) {
          console.log(`⚠️ Ошибка обновления банка ${bank.config.assetSymbol}: ${bankError.message}`);
        }
      }

      return true;

    } catch (error) {
      console.error(`❌ Ошибка перезагрузки: ${error.message}`);
      return false;
    }
  }

  async checkAccountStateAfterReload() {
    try {
      console.log('\n📊 ПРОВЕРКА СОСТОЯНИЯ ПОСЛЕ ПЕРЕЗАГРУЗКИ');
      console.log('═══════════════════════════════════════════════════════════════');

      if (!this.marginfiAccount) {
        console.log('❌ MarginFi аккаунт не загружен');
        return false;
      }

      // Еще раз принудительно перезагружаем
      await this.marginfiAccount.reload();

      const balances = this.marginfiAccount.activeBalances;
      console.log(`💰 Активных балансов: ${balances.length}`);

      let totalAssets = 0;
      let totalLiabilities = 0;
      let hasAnyBalance = false;

      for (const balance of balances) {
        try {
          const bank = this.marginfiClient.getBankByPk(balance.bankPk);
          
          if (bank) {
            const symbol = bank.config.assetSymbol;
            const assetShares = balance.assetShares.toNumber();
            const liabilityShares = balance.liabilityShares.toNumber();
            
            if (assetShares > 0 || liabilityShares > 0) {
              hasAnyBalance = true;
              console.log(`\n🏦 ${symbol}:`);
              console.log(`   💰 Asset Shares: ${assetShares}`);
              console.log(`   💸 Liability Shares: ${liabilityShares}`);
              
              if (assetShares > 0) {
                try {
                  const assetValue = balance.computeUsdValue(bank, 'asset').toNumber();
                  totalAssets += assetValue;
                  console.log(`   💵 Asset Value: $${assetValue.toFixed(6)}`);
                } catch (assetError) {
                  console.log(`   ⚠️ Ошибка расчета asset value: ${assetError.message}`);
                }
              }
              
              if (liabilityShares > 0) {
                try {
                  const liabilityValue = balance.computeUsdValue(bank, 'liability').toNumber();
                  totalLiabilities += liabilityValue;
                  console.log(`   💸 Liability Value: $${liabilityValue.toFixed(6)}`);
                } catch (liabilityError) {
                  console.log(`   ⚠️ Ошибка расчета liability value: ${liabilityError.message}`);
                }
              }
            }
          }
        } catch (balanceError) {
          console.log(`   ❌ Ошибка обработки баланса: ${balanceError.message}`);
        }
      }

      console.log(`\n📊 ИТОГО ПОСЛЕ БЕСПЛАТНОЙ ПЕРЕЗАГРУЗКИ:`);
      console.log(`   💰 Общие активы: $${totalAssets.toFixed(6)}`);
      console.log(`   💸 Общие долги: $${totalLiabilities.toFixed(6)}`);
      console.log(`   📈 Чистая стоимость: $${(totalAssets - totalLiabilities).toFixed(6)}`);

      if (!hasAnyBalance) {
        console.log(`   ✅ БАЛАНСОВ НЕТ - АККАУНТ ЧИСТЫЙ!`);
        return true;
      }

      if (totalLiabilities > 0.000001) {
        console.log(`🚨 ДОЛГ ОСТАЕТСЯ: $${totalLiabilities.toFixed(6)}`);
        console.log(`💡 Возможно, это реальный долг, а не phantom debt`);
        
        if (totalAssets > 0) {
          const healthRatio = (totalAssets / totalLiabilities) * 100;
          console.log(`📊 Health Ratio: ${healthRatio.toFixed(2)}%`);
          
          if (healthRatio > 110) {
            console.log('✅ Health достаточный для транзакций');
            return true;
          } else {
            console.log('🚨 Health критический - нужно пополнение');
            return false;
          }
        } else {
          console.log('🚨 Нет активов - health критический');
          return false;
        }
      } else {
        console.log('✅ ДОЛГОВ НЕТ - PHANTOM DEBT УСТРАНЕН БЕСПЛАТНО!');
        return true;
      }

    } catch (error) {
      console.error(`❌ Ошибка проверки состояния: ${error.message}`);
      return false;
    }
  }

  async performFreeReload() {
    try {
      console.log('\n🎯 ВЫПОЛНЯЕМ БЕСПЛАТНУЮ ПЕРЕЗАГРУЗКУ АККАУНТОВ');
      console.log('💡 Принцип: Daily Solana Tip 83 - Reload accounts after CPI');
      console.log('🔧 Метод: Принудительное обновление данных без транзакций');

      // 1. Принудительно перезагружаем все аккаунты
      const reloadSuccess = await this.forceReloadAllAccounts();
      if (!reloadSuccess) {
        console.log('\n❌ Не удалось перезагрузить аккаунты');
        return false;
      }

      // 2. Проверяем состояние после перезагрузки
      const isFixed = await this.checkAccountStateAfterReload();

      if (isFixed) {
        console.log('\n🎉 УСПЕХ! PHANTOM DEBT УСТРАНЕН БЕСПЛАТНО!');
        console.log('✅ Данные аккаунтов синхронизированы с блокчейном');
        console.log('💰 Стоимость: $0.00 (только чтение данных)');
        console.log('🚀 Теперь можно выполнять арбитражные операции');
      } else {
        console.log('\n⚠️ Phantom debt остается или это реальный долг');
        console.log('💡 Возможно, потребуются дополнительные действия');
        console.log('🔧 Попробуйте запустить скрипт еще раз через несколько минут');
      }

      return isFixed;

    } catch (error) {
      console.error(`❌ Ошибка процесса: ${error.message}`);
      return false;
    }
  }
}

async function main() {
  console.log('🔧 БЕСПЛАТНОЕ РЕШЕНИЕ PHANTOM DEBT');
  console.log('💰 Стоимость: $0.00 (только чтение данных)');
  console.log('📚 Источник: Daily Solana Tip 83 - Reload accounts after CPI');
  console.log('🎯 Принцип: Принудительная перезагрузка данных аккаунтов');
  
  const reloader = new FreeAccountReloader();
  
  if (await reloader.initialize()) {
    await reloader.performFreeReload();
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = FreeAccountReloader;
