const fs = require('fs');

/**
 * 🔥 ПОЛНЫЙ АНАЛИЗ ВСЕХ 24 ИНСТРУКЦИЙ
 * ИЗВЛЕКАЕМ ВСЕ КЛЮЧИ ИЗ КАЖДОЙ ИНСТРУКЦИИ И ПРОВЕРЯЕМ ПОКРЫТИЕ
 */

console.log('🔥 ПОЛНЫЙ АНАЛИЗ ВСЕХ 24 ИНСТРУКЦИЙ');

// Загружаем ALT конфигурацию
let altConfig;
try {
    altConfig = JSON.parse(fs.readFileSync('final-custom-alt-config.json', 'utf8'));
    console.log('✅ ALT конфигурация загружена');
} catch (error) {
    console.log('❌ Не удалось загрузить ALT конфигурацию:', error.message);
    process.exit(1);
}

// Создаём Set всех адресов в ALT таблицах
const altAddresses = new Set();
altConfig.addresses.forEach(addr => {
    altAddresses.add(addr.address);
});

console.log(`📊 Всего адресов в ALT: ${altAddresses.size}`);

/**
 * 🔍 ВСЕ КЛЮЧИ ИЗ ВСЕХ 24 ИНСТРУКЦИЙ (ИЗ ЛОГА)
 */
const allInstructionKeys = [
    // Инструкция 0: ComputeBudget (0 keys)
    [],
    
    // Инструкция 1: ComputeBudget (0 keys)  
    [],
    
    // Инструкция 2: MFv2hWf3 (3 keys) - START Flash Loan
    [
        'MFv2hWf31CqbX2vTKRtLuGnvGFgkJd4KVvwGVgr5EQxf', // MarginFi Program
        '********************************************', // MarginFi Account
        'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV'  // Wallet (dynamic)
    ],
    
    // Инструкция 3: MFv2hWf3 (8 keys) - BORROW USDC
    [
        'MFv2hWf31CqbX2vTKRtLuGnvGFgkJd4KVvwGVgr5EQxf', // MarginFi Program
        '********************************************', // MarginFi Account
        'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV', // Wallet (dynamic)
        '2s37akK2eyBbp8DQxvMkjxqEFNzdEgLBLAq6QUpyHHxu', // USDC Bank
        '7jaiZR5SFsZFbmTwjUs8588SZs2aVGBm8uySeqPikcuF', // USDC Vault
        '********************************************', // USDC User Account
        'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // USDC Mint
        'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA'  // Token Program (dynamic)
    ],
    
    // Инструкция 4: MFv2hWf3 (8 keys) - BORROW SOL
    [
        'MFv2hWf31CqbX2vTKRtLuGnvGFgkJd4KVvwGVgr5EQxf', // MarginFi Program
        '********************************************', // MarginFi Account
        'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV', // Wallet (dynamic)
        'CCKtUs6Cdd1YfNVAVLsVSBpVnepnKjnfuTjSzjKcTPqy', // SOL Bank
        '2eicbpitfJXDwqCuFAmPgDP7t2oUotnAzbGzRKLMgSLe', // SOL Vault
        '68rtTtSuDnfVQzVPQJjsp38FHkuVSDp38FHkuVSDp38F', // SOL User Account
        'So****************************************2',    // WSOL Mint
        'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA'   // Token Program (dynamic)
    ],
    
    // Инструкция 5: Tokenkeg (3 keys) - WSOL → SOL
    [
        'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA', // Token Program (dynamic)
        '68rtTtSuDnfVQzVPQJjsp38FHkuVSDp38FHkuVSDp38F', // WSOL Account
        'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV'  // Wallet (dynamic)
    ],
    
    // Инструкция 6: LBUZKhRx (8 keys) - Meteora Position
    [
        'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo', // Meteora Program
        '6kL6XehuRvDY25byUKz6i5VFRVWdrbdDiDgbSUg2dfcW', // Position
        '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6', // Pool
        'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV', // Wallet (dynamic)
        '********************************',               // System Program (dynamic)
        'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA', // Token Program (dynamic)
        'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL', // Associated Token Program (dynamic)
        'SysvarRent********************************1'    // Sysvar Rent (dynamic)
    ],
    
    // Инструкция 7: ******** (2 keys) - System Transfer
    [
        '********************************',               // System Program (dynamic)
        'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV'  // Wallet (dynamic)
    ],
    
    // Инструкция 8: Tokenkeg (1 key) - Sync Native
    [
        'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA'  // Token Program (dynamic)
    ],
    
    // Инструкция 9: LBUZKhRx (15 keys) - Add Liquidity 1
    [
        'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo', // Meteora Program
        '6gUQUWWkjz6snSEeS7un3vt14zFDRCxx752ypWrjNG6A', // Position
        '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6', // Pool
        'EYj9xKw6ZszwpyNibHY7JD5o3QgTVrSdcBp1fMJhrR9o', // Reserve X
        'CoaxzEh8p5YyGLcj36Eo3cUThVJxeKCs7qvLAGDYwBcz', // Reserve Y
        '68rtTtSuDnfVQzVPQJjsp38FHkuVSDp38FHkuVSDp38F', // User Token X
        '********************************************', // User Token Y
        'So****************************************2',    // Token X Mint
        'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // Token Y Mint
        '59YuGWPunbchD2mbi9U7qvjWQKQReGeepn4ZSr9zz9Li', // Oracle
        'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV', // Wallet (dynamic)
        'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA', // Token Program (dynamic)
        'D1ZN9Wj1f4Q1nzjxhC6AuVcwjzch6ZQQ4x4KvKm8Qg7P', // Event Authority
        '2mGnsXcGNVVyPc8C4nzjxhC6AuVcwjzch6ZQQ4x4KvKm', // Bin Array 1
        'GBDuzqBgNVVyPc8C4nzjxhC6AuVcwjzch6ZQQ4x4KvKm'  // Bin Array 2
    ],

    // Инструкция 10: Tokenkeg (3 keys) - Create ATA
    [
        'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA', // Token Program (dynamic)
        '********************************************', // USDC ATA
        'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'  // USDC Mint
    ],

    // Инструкция 11: LBUZKhRx (15 keys) - Add Liquidity 2
    [
        'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo', // Meteora Program
        '6gUQUWWkjz6snSEeS7un3vt14zFDRCxx752ypWrjNG6A', // Position
        'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y', // Pool 2
        'DwZz4S1Z1LBXomzmncQRVKCYhjCqSAMQ6RPKbUAadr7H', // Reserve X
        '4N22J4vW2juHocTntJNmXywSonYjkndCwahjZ2cYLDgb', // Reserve Y
        '68rtTtSuDnfVQzVPQJjsp38FHkuVSDp38FHkuVSDp38F', // User Token X
        '********************************************', // User Token Y
        'So****************************************2',    // Token X Mint
        'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // Token Y Mint
        'ETc6tqgLrr7wXsH8u2QBK1CyXHX3kvV6WQjBz4cf3sCj', // Oracle
        'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV', // Wallet (dynamic)
        'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA', // Token Program (dynamic)
        'D1ZN9Wj1f4Q1nzjxhC6AuVcwjzch6ZQQ4x4KvKm8Qg7P', // Event Authority
        'Dbw8mACQNf6ywKKLBFBYyKQBms8VJfviGU5jkEEXg1Fy', // Bin Array 1
        '********************************************'  // Bin Array 2
    ],

    // Инструкция 12: LBUZKhRx (15 keys) - Add Liquidity 3
    [
        'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo', // Meteora Program
        '6gUQUWWkjz6snSEeS7un3vt14zFDRCxx752ypWrjNG6A', // Position
        'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y', // Pool 2
        'DwZz4S1Z1LBXomzmncQRVKCYhjCqSAMQ6RPKbUAadr7H', // Reserve X
        '4N22J4vW2juHocTntJNmXywSonYjkndCwahjZ2cYLDgb', // Reserve Y
        '68rtTtSuDnfVQzVPQJjsp38FHkuVSDp38FHkuVSDp38F', // User Token X
        '********************************************', // User Token Y
        'So****************************************2',    // Token X Mint
        'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // Token Y Mint
        'ETc6tqgLrr7wXsH8u2QBK1CyXHX3kvV6WQjBz4cf3sCj', // Oracle
        'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV', // Wallet (dynamic)
        'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA', // Token Program (dynamic)
        'D1ZN9Wj1f4Q1nzjxhC6AuVcwjzch6ZQQ4x4KvKm8Qg7P', // Event Authority
        'HZzNfgApNVVyPc8C4nzjxhC6AuVcwjzch6ZQQ4x4KvKm', // Bin Array 3
        '8A4Crui8NVVyPc8C4nzjxhC6AuVcwjzch6ZQQ4x4KvKm'  // Bin Array 4
    ],

    // Инструкция 13: ATokenGP (6 keys) - Create USDC ATA
    [
        'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL', // Associated Token Program (dynamic)
        'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV', // Wallet (dynamic)
        '********************************************', // USDC ATA
        'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // USDC Mint
        '********************************',               // System Program (dynamic)
        'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA'  // Token Program (dynamic)
    ],

    // Инструкция 14: ATokenGP (6 keys) - Create SOL ATA
    [
        'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL', // Associated Token Program (dynamic)
        'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV', // Wallet (dynamic)
        '68rtTtSuDnfVQzVPQJjsp38FHkuVSDp38FHkuVSDp38F', // SOL ATA
        'So****************************************2',    // WSOL Mint
        '********************************',               // System Program (dynamic)
        'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA'  // Token Program (dynamic)
    ],

    // Инструкция 15: LBUZKhRx (23 keys) - BUY SOL SWAP
    [
        'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y', // Pool
        'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo', // Meteora Program
        'DwZz4S1Z1LBXomzmncQRVKCYhjCqSAMQ6RPKbUAadr7H', // ReserveX
        '4N22J4vW2juHocTntJNmXywSonYjkndCwahjZ2cYLDgb', // ReserveY
        '********************************************', // User Token X
        '68rtTtSuDnfVQzVPQJjsp38FHkuVSDp38FHkuVSDp38F', // User Token Y
        'So****************************************2',    // Token X Mint
        'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // Token Y Mint
        'ETc6tqgLrr7wXsH8u2QBK1CyXHX3kvV6WQjBz4cf3sCj', // Oracle
        'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo', // Program (duplicate)
        'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV', // Wallet (dynamic)
        'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA', // Token Program (dynamic)
        'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA', // Token Program (duplicate, dynamic)
        'MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr', // Memo Program (dynamic)
        'D1ZN9Wj1f4Q1nzjxhC6AuVcwjzch6ZQQ4x4KvKm8Qg7P', // Event Authority
        'Dbw8mACQNf6ywKKLBFBYyKQBms8VJfviGU5jkEEXg1Fy', // Bin Array 1
        '********************************************', // Bin Array 2
        'HZzNfgApNVVyPc8C4nzjxhC6AuVcwjzch6ZQQ4x4KvKm', // Bin Array 3
        '8A4Crui8NVVyPc8C4nzjxhC6AuVcwjzch6ZQQ4x4KvKm', // Bin Array 4
        'Dbw8mACQNf6ywKKLBFBYyKQBms8VJfviGU5jkEEXg1Fy', // Bin Array 1 (duplicate)
        '8A4Crui8NVVyPc8C4nzjxhC6AuVcwjzch6ZQQ4x4KvKm', // Bin Array 4 (duplicate)
        'HZzNfgApNVVyPc8C4nzjxhC6AuVcwjzch6ZQQ4x4KvKm', // Bin Array 3 (duplicate)
        'Dbw8mACQNf6ywKKLBFBYyKQBms8VJfviGU5jkEEXg1Fy'  // Bin Array 1 (duplicate)
    ],

    // Инструкция 16: LBUZKhRx (23 keys) - SELL SOL SWAP
    [
        '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6', // Pool
        'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo', // Meteora Program
        'EYj9xKw6ZszwpyNibHY7JD5o3QgTVrSdcBp1fMJhrR9o', // ReserveX
        'CoaxzEh8p5YyGLcj36Eo3cUThVJxeKCs7qvLAGDYwBcz', // ReserveY
        '68rtTtSuDnfVQzVPQJjsp38FHkuVSDp38FHkuVSDp38F', // User Token X
        '********************************************', // User Token Y
        'So****************************************2',    // Token X Mint
        'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // Token Y Mint
        '59YuGWPunbchD2mbi9U7qvjWQKQReGeepn4ZSr9zz9Li', // Oracle
        'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo', // Program (duplicate)
        'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV', // Wallet (dynamic)
        'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA', // Token Program (dynamic)
        'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA', // Token Program (duplicate, dynamic)
        'MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr', // Memo Program (dynamic)
        'D1ZN9Wj1f4Q1nzjxhC6AuVcwjzch6ZQQ4x4KvKm8Qg7P', // Event Authority
        '2mGnsXcGNVVyPc8C4nzjxhC6AuVcwjzch6ZQQ4x4KvKm', // Bin Array 1
        'GBDuzqBgNVVyPc8C4nzjxhC6AuVcwjzch6ZQQ4x4KvKm', // Bin Array 2
        '7xmtz8hDNVVyPc8C4nzjxhC6AuVcwjzch6ZQQ4x4KvKm', // Bin Array 3
        '6dvANXbq79ETRLSEpN8jBGjPfAWsbarCnEKnA7GezSzF', // Bin Array 4
        '7xmtz8hDNVVyPc8C4nzjxhC6AuVcwjzch6ZQQ4x4KvKm', // Bin Array 3 (duplicate)
        '7xmtz8hDNVVyPc8C4nzjxhC6AuVcwjzch6ZQQ4x4KvKm', // Bin Array 3 (duplicate)
        '7xmtz8hDNVVyPc8C4nzjxhC6AuVcwjzch6ZQQ4x4KvKm', // Bin Array 3 (duplicate)
        'GBDuzqBgNVVyPc8C4nzjxhC6AuVcwjzch6ZQQ4x4KvKm'  // Bin Array 2 (duplicate)
    ],

    // Инструкция 17: LBUZKhRx (12 keys) - Remove Liquidity 1
    [
        'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo', // Meteora Program
        '6gUQUWWkjz6snSEeS7un3vt14zFDRCxx752ypWrjNG6A', // Position
        '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6', // Pool
        'EYj9xKw6ZszwpyNibHY7JD5o3QgTVrSdcBp1fMJhrR9o', // Reserve X
        'CoaxzEh8p5YyGLcj36Eo3cUThVJxeKCs7qvLAGDYwBcz', // Reserve Y
        '68rtTtSuDnfVQzVPQJjsp38FHkuVSDp38FHkuVSDp38F', // User Token X
        '********************************************', // User Token Y
        'So****************************************2',    // Token X Mint
        'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // Token Y Mint
        'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV', // Wallet (dynamic)
        'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA', // Token Program (dynamic)
        'D1ZN9Wj1f4Q1nzjxhC6AuVcwjzch6ZQQ4x4KvKm8Qg7P'  // Event Authority
    ],

    // Инструкция 18: LBUZKhRx (12 keys) - Remove Liquidity 2
    [
        'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo', // Meteora Program
        '6gUQUWWkjz6snSEeS7un3vt14zFDRCxx752ypWrjNG6A', // Position
        'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y', // Pool 2
        'DwZz4S1Z1LBXomzmncQRVKCYhjCqSAMQ6RPKbUAadr7H', // Reserve X
        '4N22J4vW2juHocTntJNmXywSonYjkndCwahjZ2cYLDgb', // Reserve Y
        '68rtTtSuDnfVQzVPQJjsp38FHkuVSDp38FHkuVSDp38F', // User Token X
        '********************************************', // User Token Y
        'So****************************************2',    // Token X Mint
        'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // Token Y Mint
        'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV', // Wallet (dynamic)
        'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA', // Token Program (dynamic)
        'D1ZN9Wj1f4Q1nzjxhC6AuVcwjzch6ZQQ4x4KvKm8Qg7P'  // Event Authority
    ],

    // Инструкция 19: ******** (2 keys) - SOL Transfer
    [
        '********************************',               // System Program (dynamic)
        'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV'  // Wallet (dynamic)
    ],

    // Инструкция 20: Tokenkeg (1 key) - Sync Native
    [
        'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA'  // Token Program (dynamic)
    ],

    // Инструкция 21: MFv2hWf3 (7 keys) - REPAY USDC
    [
        'MFv2hWf31CqbX2vTKRtLuGnvGFgkJd4KVvwGVgr5EQxf', // MarginFi Program
        '********************************************', // MarginFi Account
        'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV', // Wallet (dynamic)
        '2s37akK2eyBbp8DQxvMkjxqEFNzdEgLBLAq6QUpyHHxu', // USDC Bank
        '7jaiZR5SFsZFbmTwjUs8588SZs2aVGBm8uySeqPikcuF', // USDC Vault
        '********************************************', // USDC User Account
        'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA'  // Token Program (dynamic)
    ],

    // Инструкция 22: MFv2hWf3 (7 keys) - REPAY SOL
    [
        'MFv2hWf31CqbX2vTKRtLuGnvGFgkJd4KVvwGVgr5EQxf', // MarginFi Program
        '********************************************', // MarginFi Account
        'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV', // Wallet (dynamic)
        'CCKtUs6Cdd1YfNVAVLsVSBpVnepnKjnfuTjSzjKcTPqy', // SOL Bank
        '2eicbpitfJXDwqCuFAmPgDP7t2oUotnAzbGzRKLMgSLe', // SOL Vault
        '68rtTtSuDnfVQzVPQJjsp38FHkuVSDp38FHkuVSDp38F', // SOL User Account
        'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA'  // Token Program (dynamic)
    ],

    // Инструкция 23: MFv2hWf3 (2 keys) - END Flash Loan
    [
        'MFv2hWf31CqbX2vTKRtLuGnvGFgkJd4KVvwGVgr5EQxf', // MarginFi Program
        '********************************************'  // MarginFi Account
    ]
];

console.log(`🔍 Анализируем ${allInstructionKeys.length} инструкций`);

/**
 * 🔥 ПОЛНЫЙ АНАЛИЗ ВСЕХ ИНСТРУКЦИЙ
 */
function analyzeAllInstructions() {
    console.log('\n🔍 АНАЛИЗ ВСЕХ 24 ИНСТРУКЦИЙ...');
    
    const fullAnalysis = {
        totalInstructions: allInstructionKeys.length,
        instructionAnalysis: [],
        allUniqueKeys: new Set(),
        allUncoveredKeys: new Set(),
        summary: {
            totalKeys: 0,
            coveredKeys: 0,
            uncoveredKeys: 0,
            dynamicKeys: 0
        }
    };
    
    // Анализируем каждую инструкцию
    allInstructionKeys.forEach((keys, instructionIndex) => {
        console.log(`\n📊 ИНСТРУКЦИЯ ${instructionIndex}: ${keys.length} ключей`);
        
        const instructionAnalysis = {
            index: instructionIndex,
            totalKeys: keys.length,
            keys: [],
            coveredCount: 0,
            uncoveredCount: 0,
            dynamicCount: 0
        };
        
        keys.forEach((key, keyIndex) => {
            const isDynamic = isDynamicKey(key);
            const isCovered = altAddresses.has(key);
            
            const keyInfo = {
                index: keyIndex,
                address: key,
                shortAddress: key.slice(0,8) + '...' + key.slice(-8),
                isDynamic: isDynamic,
                isCovered: isCovered,
                category: getKeyCategory(key)
            };
            
            instructionAnalysis.keys.push(keyInfo);
            fullAnalysis.allUniqueKeys.add(key);
            
            if (isDynamic) {
                instructionAnalysis.dynamicCount++;
                fullAnalysis.summary.dynamicKeys++;
            } else if (isCovered) {
                instructionAnalysis.coveredCount++;
                fullAnalysis.summary.coveredKeys++;
            } else {
                instructionAnalysis.uncoveredCount++;
                fullAnalysis.summary.uncoveredKeys++;
                fullAnalysis.allUncoveredKeys.add(key);
            }
            
            const status = isCovered ? '✅' : (isDynamic ? '🔄' : '❌');
            console.log(`   ${status} ${keyIndex}: ${keyInfo.shortAddress} (${keyInfo.category})`);
        });
        
        fullAnalysis.summary.totalKeys += keys.length;
        fullAnalysis.instructionAnalysis.push(instructionAnalysis);
        
        const staticKeys = instructionAnalysis.totalKeys - instructionAnalysis.dynamicCount;
        const coveragePercent = staticKeys > 0 ? Math.round((instructionAnalysis.coveredCount / staticKeys) * 100) : 100;
        console.log(`   📈 Покрытие статических: ${coveragePercent}% (${instructionAnalysis.coveredCount}/${staticKeys})`);
    });
    
    return fullAnalysis;
}

function isDynamicKey(key) {
    return key.includes('********************************') ||
           key === 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA' ||
           key === 'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL' ||
           key === 'MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr' ||
           key.includes('Sysvar') ||
           key === 'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV';
}

function getKeyCategory(address) {
    if (address.includes('********************************')) return 'SYSTEM_PROGRAM';
    if (address === 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA') return 'TOKEN_PROGRAM';
    if (address === 'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL') return 'ASSOCIATED_TOKEN_PROGRAM';
    if (address === 'MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr') return 'MEMO_PROGRAM';
    if (address === 'So****************************************2') return 'WSOL_MINT';
    if (address === 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v') return 'USDC_MINT';
    if (address === 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo') return 'METEORA_PROGRAM';
    if (address === 'MFv2hWf31CqbX2vTKRtLuGnvGFgkJd4KVvwGVgr5EQxf') return 'MARGINFI_PROGRAM';
    if (address.startsWith('3dPECtRB')) return 'MARGINFI_ACCOUNT';
    if (address.startsWith('2s37akK2') || address.startsWith('CCKtUs6C')) return 'MARGINFI_BANK';
    if (address.startsWith('7jaiZR5S') || address.startsWith('2eicbpit')) return 'MARGINFI_VAULT';
    if (address.startsWith('6kL6Xehu') || address.startsWith('6gUQUWWk')) return 'METEORA_POSITION';
    if (address.startsWith('5rCf1DM8') || address.startsWith('BGm1tav5')) return 'METEORA_POOL';
    if (address.startsWith('EYj9xKw6') || address.startsWith('DwZz4S1Z')) return 'METEORA_RESERVE';
    if (address.startsWith('59YuGWPu') || address.startsWith('ETc6tqgL')) return 'METEORA_ORACLE';
    if (address.startsWith('2mGnsXcG') || address.startsWith('GBDuzqBg')) return 'METEORA_BIN_ARRAY';
    if (address.startsWith('D1ZN9Wj1')) return 'METEORA_EVENT_AUTHORITY';
    if (address.includes('Sysvar')) return 'SYSVAR';
    if (address === 'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV') return 'WALLET';
    return 'UNKNOWN';
}

// Запускаем полный анализ
const analysis = analyzeAllInstructions();

// Выводим итоговую статистику
console.log('\n🎯 ИТОГОВАЯ СТАТИСТИКА:');
console.log(`📋 Всего инструкций: ${analysis.totalInstructions}`);
console.log(`🔑 Всего ключей: ${analysis.summary.totalKeys}`);
console.log(`🔑 Уникальных ключей: ${analysis.allUniqueKeys.size}`);
console.log(`✅ Покрыто ALT: ${analysis.summary.coveredKeys}`);
console.log(`❌ НЕ покрыто: ${analysis.summary.uncoveredKeys}`);
console.log(`🔄 Динамические: ${analysis.summary.dynamicKeys}`);

const staticKeys = analysis.summary.totalKeys - analysis.summary.dynamicKeys;
const coveragePercent = staticKeys > 0 ? Math.round((analysis.summary.coveredKeys / staticKeys) * 100) : 100;
console.log(`📈 Общее покрытие статических: ${coveragePercent}%`);

// Список всех непокрытых ключей
if (analysis.allUncoveredKeys.size > 0) {
    console.log('\n🚨 ВСЕ НЕПОКРЫТЫЕ СТАТИЧЕСКИЕ КЛЮЧИ:');
    const uncoveredArray = Array.from(analysis.allUncoveredKeys);
    uncoveredArray.forEach((key, index) => {
        const category = getKeyCategory(key);
        console.log(`   ${index + 1}. ${key.slice(0,8)}...${key.slice(-8)} (${category})`);
        console.log(`      Полный: ${key}`);
    });
    
    // Создаём файл для добавления в ALT
    const keysToAdd = uncoveredArray.map((key, index) => ({
        address: key,
        name: `${getKeyCategory(key)} ${index + 1}`,
        category: getKeyCategory(key)
    }));
    
    const outputData = {
        timestamp: new Date().toISOString(),
        totalUncoveredKeys: keysToAdd.length,
        analysis: analysis,
        keysToAdd: keysToAdd
    };
    
    fs.writeFileSync('all-24-instructions-uncovered-keys.json', JSON.stringify(outputData, null, 2));
    
    console.log(`\n💾 Все непокрытые ключи сохранены в all-24-instructions-uncovered-keys.json`);
    console.log(`🔥 ГОТОВО К ДОБАВЛЕНИЮ: ${keysToAdd.length} КЛЮЧЕЙ`);
} else {
    console.log('\n🎉 ВСЕ СТАТИЧЕСКИЕ КЛЮЧИ ПОКРЫТЫ ALT!');
}

console.log('\n🎉 ПОЛНЫЙ АНАЛИЗ ВСЕХ 24 ИНСТРУКЦИЙ ЗАВЕРШЁН!');
process.exit(0);
