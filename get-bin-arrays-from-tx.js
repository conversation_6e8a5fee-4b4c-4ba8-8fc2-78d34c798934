/**
 * 🔥 ПОЛУЧЕНИЕ BIN ARRAY АДРЕСОВ ИЗ РЕАЛЬНОЙ ТРАНЗАКЦИИ
 * Берем адреса из успешной транзакции remove_liquidity
 */

console.log('🔥 BIN ARRAY АДРЕСА ИЗ РЕАЛЬНОЙ ТРАНЗАКЦИИ');
console.log('=' .repeat(60));

// 🎯 ИЗ РЕАЛЬНОЙ ТРАНЗАКЦИИ remove_liquidity (инструкция #13)
const binArraysFromTransaction = [
    // Из инструкции #13 (POOL_1 remove_liquidity):
    'B7XxwUwnUhtmXLnBvcGxuA1byHaAhrE7oiz53nxT82tw', // Bin Array Bitmap Extension
    
    // Из инструкции #14 (POOL_2 remove_liquidity):  
    'DjyDh54Q6LqPkARYmgjgLUKmwo1BZGDcY5Z5MFwvPc4N', // Bin Array Bitmap Extension
    
    // Дополнительные bin arrays из claim_fee2:
    'Hd6qVSiPQELZq3FAXPCumWmnKPx4BbZXd9782TEeRY2x', // POOL_1 Bin Array Extension (claim_fee2 #15)
    'Dbw8mACQmyrvbvLZs9bzAA6Tg2GVf5cmLzVbkfRzahRS', // POOL_2 Bin Array Extension (claim_fee2 #16)
    
    // Позиции (тоже нужны для remove_liquidity):
    '5XYfyyDBpNt8aTuvxe6RAQ7f11WwNWyD3ZUJkfJzDFKU', // POOL_1 Position
    'A7tpj5uz4pCHiCaBSuTFt8EWmSBFp8qrzoVVCsuVfXgC'  // POOL_2 Position
];

console.log(`🎯 НАЙДЕНО ${binArraysFromTransaction.length} АДРЕСОВ ИЗ РЕАЛЬНОЙ ТРАНЗАКЦИИ:`);
binArraysFromTransaction.forEach((addr, i) => {
    const names = [
        'POOL_1 Bin Array Bitmap Extension (remove_liquidity #13)',
        'POOL_2 Bin Array Bitmap Extension (remove_liquidity #14)', 
        'POOL_1 Bin Array Extension (claim_fee2 #15)',
        'POOL_2 Bin Array Extension (claim_fee2 #16)',
        'POOL_1 Position (remove_liquidity)',
        'POOL_2 Position (remove_liquidity)'
    ];
    console.log(`${i+1}. ${addr} // ${names[i]}`);
});

console.log('\n📋 МАССИВ ДЛЯ add-meteora-to-alt.js:');
console.log('[');
binArraysFromTransaction.forEach((addr, i) => {
    const comma = i < binArraysFromTransaction.length - 1 ? ',' : '';
    const names = [
        'POOL_1 Bin Array Bitmap Extension',
        'POOL_2 Bin Array Bitmap Extension', 
        'POOL_1 Bin Array Extension',
        'POOL_2 Bin Array Extension',
        'POOL_1 Position',
        'POOL_2 Position'
    ];
    console.log(`    '${addr}'${comma} // ${names[i]}`);
});
console.log('];');

console.log('\n🔥 ЭТИ АДРЕСА ВЗЯТЫ ИЗ РЕАЛЬНОЙ УСПЕШНОЙ ТРАНЗАКЦИИ!');
console.log('💾 ЭКОНОМИЯ: 6 × 31 bytes = 186 bytes');
