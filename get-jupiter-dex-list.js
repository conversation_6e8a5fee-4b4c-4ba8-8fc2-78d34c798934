/**
 * 🔍 ПОЛУЧЕНИЕ ОФИЦИАЛЬНОГО СПИСКА DEX ОТ JUPITER API
 */

async function getJupiterDEXList() {
  console.log('🔍 ПОЛУЧЕНИЕ ОФИЦИАЛЬНОГО СПИСКА DEX ОТ JUPITER API');
  console.log('=' .repeat(60));

  try {
    // 🔍 1. Попробуем получить список через program-id-to-label
    console.log('1️⃣ ПОЛУЧЕНИЕ СПИСКА ЧЕРЕЗ program-id-to-label:');
    const response1 = await fetch('https://quote-api.jup.ag/v6/program-id-to-label');
    
    if (response1.ok) {
      const programIdToLabel = await response1.json();
      console.log('✅ Получен список program-id-to-label:');
      
      const dexNames = Object.values(programIdToLabel);
      const uniqueDexNames = [...new Set(dexNames)].sort();
      
      console.log(`📊 Найдено ${uniqueDexNames.length} уникальных DEX:`);
      uniqueDexNames.forEach((dex, index) => {
        console.log(`   ${index + 1}. ${dex}`);
      });
      
      console.log('');
      console.log('🎯 РЕКОМЕНДУЕМЫЕ НАДЕЖНЫЕ DEX:');
      const reliableDexes = uniqueDexNames.filter(dex => 
        ['Raydium', 'Orca', 'Whirlpool', 'Meteora', 'Phoenix'].includes(dex)
      );
      reliableDexes.forEach(dex => console.log(`   ✅ ${dex}`));
      
      console.log('');
      console.log('🚫 ПРОБЛЕМНЫЕ DEX (из наших ошибок):');
      const problematicDexes = uniqueDexNames.filter(dex => 
        ['Lifinity', 'Obric', 'SolFi', 'TesseraV'].includes(dex)
      );
      problematicDexes.forEach(dex => console.log(`   ❌ ${dex}`));
      
    } else {
      console.log('❌ Не удалось получить program-id-to-label');
    }

    console.log('');

    // 🔍 2. Тестируем конкретные DEX названия
    console.log('2️⃣ ТЕСТИРОВАНИЕ КОНКРЕТНЫХ DEX НАЗВАНИЙ:');
    
    const testDexes = [
      'Raydium', 'Orca', 'Whirlpool', 'Meteora', 'Phoenix',
      'Lifinity', 'Obric', 'SolFi', 'TesseraV', 'Aldrin'
    ];
    
    const inputMint = 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'; // USDC
    const outputMint = 'So11111111111111111111111111111111111111112'; // SOL
    const amount = 1000000000; // 1,000 USDC
    
    for (const dex of testDexes) {
      try {
        const url = `https://lite-api.jup.ag/swap/v1/quote?inputMint=${inputMint}&outputMint=${outputMint}&amount=${amount}&slippageBps=150&includeDexes=${encodeURIComponent(dex)}`;
        const response = await fetch(url);
        const quote = await response.json();
        
        if (quote.routePlan && quote.routePlan.length > 0) {
          const usedDex = quote.routePlan[0].swapInfo?.label || 'Unknown';
          if (usedDex === dex) {
            console.log(`   ✅ ${dex} - РАБОТАЕТ (используется: ${usedDex})`);
          } else {
            console.log(`   ⚠️ ${dex} - НЕ РАБОТАЕТ (используется: ${usedDex})`);
          }
        } else {
          console.log(`   ❌ ${dex} - НЕТ МАРШРУТА`);
        }
        
        // Небольшая задержка между запросами
        await new Promise(resolve => setTimeout(resolve, 100));
        
      } catch (error) {
        console.log(`   ❌ ${dex} - ОШИБКА: ${error.message}`);
      }
    }

    console.log('');
    console.log('🔧 РЕКОМЕНДАЦИИ:');
    console.log('1. Используйте excludeDexes для исключения проблемных DEX');
    console.log('2. includeDexes может не работать как ожидается');
    console.log('3. Jupiter приоритизирует лучшую цену над нашими предпочтениями');
    console.log('4. Рассмотрите реализацию собственной фильтрации маршрутов');

  } catch (error) {
    console.error('❌ ОШИБКА:');
    console.error(`   Сообщение: ${error.message}`);
  }
}

// Запуск
if (require.main === module) {
  getJupiterDEXList().catch(console.error);
}

module.exports = { getJupiterDEXList };
