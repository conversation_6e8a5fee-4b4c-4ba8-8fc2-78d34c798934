/**
 * 🔥 ПОЛУЧЕНИЕ РЕАЛЬНЫХ BIN ARRAY АДРЕСОВ ИЗ ПОЗИЦИЙ
 */

const { Connection, PublicKey } = require('@solana/web3.js');
require('dotenv').config({ path: '.env.solana' });

async function getBinArrays() {
    console.log('🔥 ПОЛУЧЕНИЕ РЕАЛЬНЫХ BIN ARRAY АДРЕСОВ ИЗ ПОЗИЦИЙ');
    console.log('=' .repeat(60));
    
    const connection = new Connection(process.env.SOLANA_RPC_URL || 'https://api.mainnet-beta.solana.com');
    
    const positions = [
        '5XYfyyDBpNt8aTuvxe6RAQ7f11WwNWyD3ZUJkfJzDFKU', // POOL_1
        'A7tpj5uz4pCHiCaBSuTFt8EWmSBFp8qrzoVVCsuVfXgC'  // POOL_2
    ];
    
    const binArrays = [];
    
    for (const pos of positions) {
        console.log(`🔍 Анализ позиции: ${pos}`);
        const account = await connection.getAccountInfo(new PublicKey(pos));
        
        if (account) {
            // Парсим данные позиции для получения bin arrays
            const data = account.data;
            console.log(`📊 Размер данных: ${data.length} bytes`);
            
            // Ищем bin array адреса в данных позиции
            for (let i = 0; i < data.length - 32; i++) {
                const slice = data.slice(i, i + 32);
                try {
                    const pubkey = new PublicKey(slice);
                    const str = pubkey.toString();
                    // Проверяем что это похоже на bin array адрес
                    if (str.length === 44 && !str.includes('1111111') && !str.includes('So11111')) {
                        console.log(`   📍 Найден адрес: ${str}`);
                        if (!binArrays.includes(str)) {
                            binArrays.push(str);
                        }
                    }
                } catch (e) {}
            }
        }
    }
    
    console.log(`\n🎯 НАЙДЕНО ${binArrays.length} УНИКАЛЬНЫХ BIN ARRAY АДРЕСОВ:`);
    binArrays.forEach((addr, i) => {
        console.log(`${i+1}. ${addr}`);
    });
    
    console.log('\n📋 МАССИВ ДЛЯ КОПИРОВАНИЯ:');
    console.log('[');
    binArrays.forEach((addr, i) => {
        const comma = i < binArrays.length - 1 ? ',' : '';
        console.log(`    '${addr}'${comma} // Bin Array ${i+1}`);
    });
    console.log(']');
}

getBinArrays().catch(console.error);
