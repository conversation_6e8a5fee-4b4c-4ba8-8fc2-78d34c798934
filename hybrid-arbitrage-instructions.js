#!/usr/bin/env node

/**
 * 🔥 ГИБРИДНЫЙ АРБИТРАЖ: JUPITER + ПРЯМЫЕ DEX ИНСТРУКЦИИ
 * 
 * КОНЦЕПЦИЯ:
 * 1. Анализ цен: Все DEX через прямые SDK (точные цены)
 * 2. Покупка: Jupiter API (лучшая ликвидность)
 * 3. Продажа: Прямой DEX вызов (гарантированная цена)
 */

const { Connection, PublicKey, Transaction } = require('@solana/web3.js');
const colors = require('colors');

class HybridArbitrageInstructions {
    constructor() {
        this.connection = new Connection(process.env.SOLANA_RPC_URL || 'https://api.mainnet-beta.solana.com');
        this.wallet = null; // Будет загружен из основной системы
        
        // 🎯 КОНФИГУРАЦИЯ ГИБРИДНОГО АРБИТРАЖА
        this.hybridConfig = {
            // 💰 ПОКУПКА ЧЕРЕЗ JUPITER (ЛУЧШАЯ ЛИКВИДНОСТЬ)
            buyMethod: 'jupiter',
            
            // 🎯 ПРОДАЖА ЧЕРЕЗ ПРЯМОЙ DEX (ТОЧНАЯ ЦЕНА)
            sellMethod: 'direct_dex',
            
            // 🔥 ПОДДЕРЖИВАЕМЫЕ DEX ДЛЯ ПРЯМЫХ ВЫЗОВОВ
            supportedDEX: ['meteora', 'orca', 'raydium'],
            
            // ⚡ МИНИМАЛЬНЫЕ СПРЕДЫ ДЛЯ РАЗНЫХ ТИПОВ АРБИТРАЖА
            minSpreads: {
                internal_meteora: 0.05,  // 0.05% между Meteora пулами
                cross_dex: 0.07,         // 0.07% между разными DEX
                jupiter_hybrid: 0.10     // 0.10% для Jupiter гибрида
            }
        };
    }

    /**
     * 🔥 СОЗДАНИЕ ГИБРИДНЫХ АРБИТРАЖНЫХ ИНСТРУКЦИЙ
     */
    async createHybridArbitrageInstructions(opportunity) {
        try {
            console.log('\n🔥 СОЗДАНИЕ ГИБРИДНЫХ АРБИТРАЖНЫХ ИНСТРУКЦИЙ...'.yellow.bold);
            console.log(`💰 Тип арбитража: ${opportunity.type}`);
            console.log(`📊 Спред: ${opportunity.spread.toFixed(4)}%`);
            console.log(`💵 Прибыль: $${opportunity.profit.toFixed(4)}`);

            const instructions = [];
            
            // 🎯 ВЫБИРАЕМ СТРАТЕГИЮ ПО ТИПУ АРБИТРАЖА
            switch (opportunity.type) {
                case 'internal_meteora':
                    return await this.createInternalMeteoraInstructions(opportunity);
                    
                case 'cross_dex':
                    return await this.createCrossDEXInstructions(opportunity);
                    
                case 'jupiter_hybrid':
                    return await this.createJupiterHybridInstructions(opportunity);
                    
                default:
                    throw new Error(`Неподдерживаемый тип арбитража: ${opportunity.type}`);
            }

        } catch (error) {
            console.error('❌ Ошибка создания гибридных инструкций:', error.message);
            throw error;
        }
    }

    /**
     * 🌪️ ВНУТРЕННИЙ METEORA АРБИТРАЖ (МЕЖДУ 3 ПУЛАМИ)
     */
    async createInternalMeteoraInstructions(opportunity) {
        console.log('\n🌪️ СОЗДАНИЕ ИНСТРУКЦИЙ ВНУТРЕННЕГО METEORA АРБИТРАЖА...'.cyan);
        
        const instructions = [];
        
        // 1️⃣ ПОКУПКА В ДЕШЕВОМ METEORA ПУЛЕ
        const buyInstruction = await this.createMeteoraSwapInstruction({
            poolAddress: opportunity.buyPool.address,
            direction: 'buy',
            amount: opportunity.amount,
            expectedPrice: opportunity.buyPool.price
        });
        instructions.push(buyInstruction);
        
        // 2️⃣ ПРОДАЖА В ДОРОГОМ METEORA ПУЛЕ  
        const sellInstruction = await this.createMeteoraSwapInstruction({
            poolAddress: opportunity.sellPool.address,
            direction: 'sell',
            amount: opportunity.amount,
            expectedPrice: opportunity.sellPool.price
        });
        instructions.push(sellInstruction);
        
        console.log(`✅ Создано ${instructions.length} инструкций для внутреннего Meteora арбитража`.green);
        return instructions;
    }

    /**
     * 🔄 МЕЖБИРЖЕВОЙ АРБИТРАЖ (METEORA ↔ ORCA/RAYDIUM)
     */
    async createCrossDEXInstructions(opportunity) {
        console.log('\n🔄 СОЗДАНИЕ ИНСТРУКЦИЙ МЕЖБИРЖЕВОГО АРБИТРАЖА...'.cyan);
        
        const instructions = [];
        
        // 🎯 ОПРЕДЕЛЯЕМ КАКОЙ DEX ДЕШЕВЛЕ
        const cheapDEX = opportunity.buyPool.dex;
        const expensiveDEX = opportunity.sellPool.dex;
        
        console.log(`💰 ПОКУПКА: ${cheapDEX} ($${opportunity.buyPool.price.toFixed(4)})`);
        console.log(`💰 ПРОДАЖА: ${expensiveDEX} ($${opportunity.sellPool.price.toFixed(4)})`);
        
        // 1️⃣ ПОКУПКА В ДЕШЕВОМ DEX
        if (cheapDEX === 'meteora') {
            const buyInstruction = await this.createMeteoraSwapInstruction({
                poolAddress: opportunity.buyPool.address,
                direction: 'buy',
                amount: opportunity.amount,
                expectedPrice: opportunity.buyPool.price
            });
            instructions.push(buyInstruction);
        } else if (cheapDEX === 'orca') {
            const buyInstruction = await this.createOrcaSwapInstruction({
                poolAddress: opportunity.buyPool.address,
                direction: 'buy',
                amount: opportunity.amount,
                expectedPrice: opportunity.buyPool.price
            });
            instructions.push(buyInstruction);
        }
        
        // 2️⃣ ПРОДАЖА В ДОРОГОМ DEX
        if (expensiveDEX === 'meteora') {
            const sellInstruction = await this.createMeteoraSwapInstruction({
                poolAddress: opportunity.sellPool.address,
                direction: 'sell',
                amount: opportunity.amount,
                expectedPrice: opportunity.sellPool.price
            });
            instructions.push(sellInstruction);
        } else if (expensiveDEX === 'orca') {
            const sellInstruction = await this.createOrcaSwapInstruction({
                poolAddress: opportunity.sellPool.address,
                direction: 'sell',
                amount: opportunity.amount,
                expectedPrice: opportunity.sellPool.price
            });
            instructions.push(sellInstruction);
        }
        
        console.log(`✅ Создано ${instructions.length} инструкций для межбиржевого арбитража`.green);
        return instructions;
    }

    /**
     * 🚀 JUPITER ГИБРИДНЫЙ АРБИТРАЖ
     */
    async createJupiterHybridInstructions(opportunity) {
        console.log('\n🚀 СОЗДАНИЕ JUPITER ГИБРИДНЫХ ИНСТРУКЦИЙ...'.cyan);
        
        const instructions = [];
        
        // 🎯 СТРАТЕГИЯ: JUPITER ДЛЯ ПОКУПКИ + ПРЯМОЙ DEX ДЛЯ ПРОДАЖИ
        console.log(`💰 ПОКУПКА: Jupiter (лучшая ликвидность)`);
        console.log(`💰 ПРОДАЖА: ${opportunity.sellPool.dex} (точная цена $${opportunity.sellPool.price.toFixed(4)})`);
        
        // 1️⃣ ПОКУПКА ЧЕРЕЗ JUPITER
        const jupiterBuyInstruction = await this.createJupiterSwapInstruction({
            inputMint: 'USDC',
            outputMint: 'SOL',
            amount: opportunity.amount,
            slippageBps: 50, // 0.5% slippage
            onlyDirectRoutes: true
        });
        instructions.push(jupiterBuyInstruction);
        
        // 2️⃣ ПРОДАЖА ЧЕРЕЗ ПРЯМОЙ DEX
        if (opportunity.sellPool.dex === 'meteora') {
            const sellInstruction = await this.createMeteoraSwapInstruction({
                poolAddress: opportunity.sellPool.address,
                direction: 'sell',
                amount: opportunity.amount,
                expectedPrice: opportunity.sellPool.price
            });
            instructions.push(sellInstruction);
        } else if (opportunity.sellPool.dex === 'orca') {
            const sellInstruction = await this.createOrcaSwapInstruction({
                poolAddress: opportunity.sellPool.address,
                direction: 'sell',
                amount: opportunity.amount,
                expectedPrice: opportunity.sellPool.price
            });
            instructions.push(sellInstruction);
        }
        
        console.log(`✅ Создано ${instructions.length} инструкций для Jupiter гибридного арбитража`.green);
        return instructions;
    }

    /**
     * 🌪️ СОЗДАНИЕ METEORA SWAP ИНСТРУКЦИИ
     */
    async createMeteoraSwapInstruction(params) {
        console.log(`🌪️ Создание Meteora swap: ${params.direction} в пуле ${params.poolAddress.slice(0,8)}...`);
        
        // TODO: Интеграция с Meteora SDK
        return {
            type: 'meteora_swap',
            poolAddress: params.poolAddress,
            direction: params.direction,
            amount: params.amount,
            expectedPrice: params.expectedPrice,
            instruction: null // Будет заполнено Meteora SDK
        };
    }

    /**
     * 🏊‍♂️ СОЗДАНИЕ ORCA SWAP ИНСТРУКЦИИ
     */
    async createOrcaSwapInstruction(params) {
        console.log(`🏊‍♂️ Создание Orca swap: ${params.direction} в пуле ${params.poolAddress.slice(0,8)}...`);
        
        // TODO: Интеграция с Orca SDK
        return {
            type: 'orca_swap',
            poolAddress: params.poolAddress,
            direction: params.direction,
            amount: params.amount,
            expectedPrice: params.expectedPrice,
            instruction: null // Будет заполнено Orca SDK
        };
    }

    /**
     * 🚀 СОЗДАНИЕ JUPITER SWAP ИНСТРУКЦИИ
     */
    async createJupiterSwapInstruction(params) {
        console.log(`🚀 Создание Jupiter swap: ${params.inputMint} → ${params.outputMint}`);
        
        // TODO: Интеграция с Jupiter API
        return {
            type: 'jupiter_swap',
            inputMint: params.inputMint,
            outputMint: params.outputMint,
            amount: params.amount,
            slippageBps: params.slippageBps,
            onlyDirectRoutes: params.onlyDirectRoutes,
            instruction: null // Будет заполнено Jupiter API
        };
    }

    /**
     * 🔥 ОБЪЕДИНЕНИЕ ВСЕХ ИНСТРУКЦИЙ В АТОМНУЮ ТРАНЗАКЦИЮ
     */
    async combineInstructionsToTransaction(instructions) {
        console.log('\n🔥 ОБЪЕДИНЕНИЕ ИНСТРУКЦИЙ В АТОМНУЮ ТРАНЗАКЦИЮ...'.yellow);
        
        const transaction = new Transaction();
        
        for (const instruction of instructions) {
            if (instruction.instruction) {
                transaction.add(instruction.instruction);
                console.log(`✅ Добавлена инструкция: ${instruction.type}`.green);
            } else {
                console.log(`⚠️ Пропущена инструкция без данных: ${instruction.type}`.yellow);
            }
        }
        
        console.log(`🎯 Создана атомная транзакция с ${transaction.instructions.length} инструкциями`.green.bold);
        return transaction;
    }
}

module.exports = HybridArbitrageInstructions;
