#!/usr/bin/env python3
"""
Immunefi Mass Tester
Система массового параллельного тестирования всех программ Immunefi
"""

import asyncio
import aiohttp
import json
import time
import random
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from datetime import datetime
import logging
from pathlib import Path
import concurrent.futures
from immunefi_mass_scanner import ImmunefiBountyParser
from immunefi_vulnerability_tester import ImmunefiBountyTester, VulnerabilityResult
from immunefi_prioritizer import ImmunefiBountyPrioritizer

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('immunefi_mass_tester.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class TestingSession:
    """Сессия массового тестирования"""
    session_id: str
    start_time: datetime
    end_time: Optional[datetime] = None
    total_programs: int = 0
    tested_programs: int = 0
    vulnerabilities_found: int = 0
    high_severity_vulns: int = 0
    critical_vulns: int = 0
    status: str = "running"  # running, completed, failed, paused
    
class ImmunefiBountyMassTester:
    """Система массового тестирования программ Immunefi"""
    
    def __init__(self, max_concurrent: int = 10, rate_limit_delay: float = 1.0):
        self.max_concurrent = max_concurrent
        self.rate_limit_delay = rate_limit_delay
        self.session: Optional[TestingSession] = None
        
        # Компоненты системы
        self.parser = None
        self.tester = None
        self.prioritizer = ImmunefiBountyPrioritizer()
        
        # Результаты
        self.all_programs: List[Dict] = []
        self.prioritized_programs: List[Dict] = []
        self.all_vulnerabilities: List[VulnerabilityResult] = []
        self.tested_programs: set = set()
        
        # Контроль выполнения
        self.semaphore = asyncio.Semaphore(max_concurrent)
        self.is_running = False
        self.pause_event = asyncio.Event()
        self.pause_event.set()  # Изначально не на паузе
        
        # Статистика
        self.stats = {
            'programs_per_hour': 0,
            'vulnerabilities_per_hour': 0,
            'success_rate': 0,
            'average_test_time': 0,
        }
    
    async def __aenter__(self):
        """Асинхронный контекстный менеджер"""
        self.parser = ImmunefiBountyParser()
        await self.parser.__aenter__()
        
        self.tester = ImmunefiBountyTester()
        await self.tester.__aenter__()
        
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Закрытие ресурсов"""
        if self.parser:
            await self.parser.__aexit__(exc_type, exc_val, exc_tb)
        if self.tester:
            await self.tester.__aexit__(exc_type, exc_val, exc_tb)
    
    async def start_mass_testing(self, target_programs: int = 0, 
                                time_limit_hours: float = 0) -> TestingSession:
        """Запуск массового тестирования"""
        session_id = f"immunefi_mass_test_{int(time.time())}"
        self.session = TestingSession(
            session_id=session_id,
            start_time=datetime.now()
        )
        
        logger.info(f"Запуск массового тестирования. Сессия: {session_id}")
        
        try:
            # Этап 1: Получение списка всех программ
            logger.info("Этап 1: Получение списка программ...")
            await self._fetch_all_programs()
            
            # Этап 2: Приоритизация программ
            logger.info("Этап 2: Приоритизация программ...")
            await self._prioritize_programs()
            
            # Этап 3: Массовое тестирование
            logger.info("Этап 3: Начало массового тестирования...")
            await self._run_mass_testing(target_programs, time_limit_hours)
            
            # Завершение сессии
            self.session.end_time = datetime.now()
            self.session.status = "completed"
            
            logger.info(f"Массовое тестирование завершено. Найдено {self.session.vulnerabilities_found} уязвимостей")
            
        except Exception as e:
            logger.error(f"Ошибка массового тестирования: {e}")
            self.session.status = "failed"
            raise
        
        return self.session
    
    async def _fetch_all_programs(self):
        """Получение всех программ Immunefi"""
        try:
            programs_data = await self.parser.fetch_bounty_list()
            
            if not programs_data:
                logger.warning("Не удалось получить программы через API, используем веб-скрапинг")
                programs_data = await self._fallback_scraping()
            
            self.all_programs = programs_data
            self.session.total_programs = len(programs_data)
            
            logger.info(f"Получено {len(programs_data)} программ для тестирования")
            
            # Сохранение списка программ
            with open(f"programs_{self.session.session_id}.json", 'w', encoding='utf-8') as f:
                json.dump(programs_data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"Ошибка получения программ: {e}")
            raise
    
    async def _fallback_scraping(self) -> List[Dict]:
        """Резервный метод получения программ через скрапинг"""
        logger.info("Использование резервного метода скрапинга...")
        
        # Здесь можно реализовать дополнительные методы получения данных
        # Например, парсинг HTML страниц, использование Selenium и т.д.
        
        return []
    
    async def _prioritize_programs(self):
        """Приоритизация программ для тестирования"""
        try:
            self.prioritizer.load_programs(self.all_programs)
            scored_programs = self.prioritizer.calculate_priority_scores()
            
            # Преобразование в формат для тестирования
            self.prioritized_programs = []
            for scored_program in scored_programs:
                # Поиск исходных данных программы
                original_program = next(
                    (p for p in self.all_programs if p.get('name') == scored_program.program_name),
                    None
                )
                
                if original_program:
                    # Добавление оценок к исходным данным
                    program_with_score = original_program.copy()
                    program_with_score.update({
                        'priority_score': scored_program.total_score,
                        'success_probability': scored_program.success_probability,
                        'estimated_time_hours': scored_program.estimated_time_hours,
                        'priority_rank': scored_program.priority_rank,
                    })
                    self.prioritized_programs.append(program_with_score)
            
            logger.info(f"Приоритизировано {len(self.prioritized_programs)} программ")
            
            # Сохранение приоритизированного списка
            with open(f"prioritized_programs_{self.session.session_id}.json", 'w', encoding='utf-8') as f:
                json.dump(self.prioritized_programs, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"Ошибка приоритизации: {e}")
            # Если приоритизация не удалась, используем исходный список
            self.prioritized_programs = self.all_programs
    
    async def _run_mass_testing(self, target_programs: int, time_limit_hours: float):
        """Выполнение массового тестирования"""
        self.is_running = True
        start_time = time.time()
        
        # Определение количества программ для тестирования
        programs_to_test = self.prioritized_programs
        if target_programs > 0:
            programs_to_test = programs_to_test[:target_programs]
        
        logger.info(f"Начало тестирования {len(programs_to_test)} программ")
        
        # Создание задач для параллельного выполнения
        tasks = []
        for i, program in enumerate(programs_to_test):
            if time_limit_hours > 0:
                elapsed_hours = (time.time() - start_time) / 3600
                if elapsed_hours >= time_limit_hours:
                    logger.info(f"Достигнут лимит времени: {time_limit_hours} часов")
                    break
            
            task = asyncio.create_task(
                self._test_single_program_with_semaphore(program, i + 1, len(programs_to_test))
            )
            tasks.append(task)
            
            # Задержка между запуском задач для rate limiting
            await asyncio.sleep(self.rate_limit_delay / self.max_concurrent)
        
        # Ожидание завершения всех задач
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
        
        self.is_running = False
        
        # Обновление статистики сессии
        self._update_session_stats()
    
    async def _test_single_program_with_semaphore(self, program: Dict, 
                                                 current: int, total: int):
        """Тестирование одной программы с контролем семафора"""
        async with self.semaphore:
            await self.pause_event.wait()  # Ожидание если на паузе
            
            if not self.is_running:
                return
            
            program_name = program.get('name', 'Unknown')
            
            try:
                logger.info(f"[{current}/{total}] Тестирование программы: {program_name}")
                
                start_time = time.time()
                
                # Тестирование программы
                vulnerabilities = await self.tester.test_program(program)
                
                test_time = time.time() - start_time
                
                # Обновление результатов
                self.all_vulnerabilities.extend(vulnerabilities)
                self.tested_programs.add(program_name)
                
                # Обновление статистики сессии
                self.session.tested_programs += 1
                self.session.vulnerabilities_found += len(vulnerabilities)
                
                # Подсчет критических уязвимостей
                for vuln in vulnerabilities:
                    if vuln.severity == 'Critical':
                        self.session.critical_vulns += 1
                    elif vuln.severity == 'High':
                        self.session.high_severity_vulns += 1
                
                logger.info(f"[{current}/{total}] Завершено тестирование {program_name}. "
                           f"Найдено {len(vulnerabilities)} уязвимостей за {test_time:.1f}с")
                
                # Сохранение промежуточных результатов каждые 10 программ
                if current % 10 == 0:
                    await self._save_intermediate_results()
                
            except Exception as e:
                logger.error(f"[{current}/{total}] Ошибка тестирования {program_name}: {e}")
            
            # Rate limiting между программами
            await asyncio.sleep(self.rate_limit_delay)
    
    async def _save_intermediate_results(self):
        """Сохранение промежуточных результатов"""
        try:
            # Сохранение уязвимостей
            vulnerabilities_data = []
            for vuln in self.all_vulnerabilities:
                vuln_dict = {
                    'program_name': vuln.program_name,
                    'vulnerability_type': vuln.vulnerability_type,
                    'severity': vuln.severity,
                    'description': vuln.description,
                    'proof_of_concept': vuln.proof_of_concept,
                    'impact': vuln.impact,
                    'recommendation': vuln.recommendation,
                    'contract_address': vuln.contract_address,
                    'timestamp': vuln.timestamp.isoformat(),
                }
                vulnerabilities_data.append(vuln_dict)
            
            filename = f"vulnerabilities_{self.session.session_id}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(vulnerabilities_data, f, indent=2, ensure_ascii=False)
            
            # Сохранение статистики сессии
            session_data = {
                'session_id': self.session.session_id,
                'start_time': self.session.start_time.isoformat(),
                'end_time': self.session.end_time.isoformat() if self.session.end_time else None,
                'total_programs': self.session.total_programs,
                'tested_programs': self.session.tested_programs,
                'vulnerabilities_found': self.session.vulnerabilities_found,
                'high_severity_vulns': self.session.high_severity_vulns,
                'critical_vulns': self.session.critical_vulns,
                'status': self.session.status,
                'stats': self.stats,
            }
            
            session_filename = f"session_{self.session.session_id}.json"
            with open(session_filename, 'w', encoding='utf-8') as f:
                json.dump(session_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Промежуточные результаты сохранены: {len(self.all_vulnerabilities)} уязвимостей")
            
        except Exception as e:
            logger.error(f"Ошибка сохранения промежуточных результатов: {e}")
    
    def _update_session_stats(self):
        """Обновление статистики сессии"""
        if not self.session.start_time:
            return
        
        # Расчет времени выполнения
        end_time = self.session.end_time or datetime.now()
        duration_hours = (end_time - self.session.start_time).total_seconds() / 3600
        
        if duration_hours > 0:
            self.stats['programs_per_hour'] = self.session.tested_programs / duration_hours
            self.stats['vulnerabilities_per_hour'] = self.session.vulnerabilities_found / duration_hours
        
        if self.session.total_programs > 0:
            self.stats['success_rate'] = self.session.tested_programs / self.session.total_programs
        
        # Средний успех по программам
        if self.session.tested_programs > 0:
            self.stats['average_vulnerabilities_per_program'] = (
                self.session.vulnerabilities_found / self.session.tested_programs
            )
    
    async def pause_testing(self):
        """Приостановка тестирования"""
        logger.info("Приостановка тестирования...")
        self.pause_event.clear()
        self.session.status = "paused"
    
    async def resume_testing(self):
        """Возобновление тестирования"""
        logger.info("Возобновление тестирования...")
        self.pause_event.set()
        self.session.status = "running"
    
    async def stop_testing(self):
        """Остановка тестирования"""
        logger.info("Остановка тестирования...")
        self.is_running = False
        self.pause_event.set()  # Разблокируем ожидающие задачи
        
        if self.session:
            self.session.end_time = datetime.now()
            self.session.status = "stopped"
            await self._save_intermediate_results()
    
    def get_real_time_stats(self) -> Dict[str, Any]:
        """Получение статистики в реальном времени"""
        if not self.session:
            return {}
        
        current_time = datetime.now()
        duration = (current_time - self.session.start_time).total_seconds()
        
        return {
            'session_id': self.session.session_id,
            'status': self.session.status,
            'duration_seconds': duration,
            'duration_formatted': str(timedelta(seconds=int(duration))),
            'total_programs': self.session.total_programs,
            'tested_programs': self.session.tested_programs,
            'remaining_programs': self.session.total_programs - self.session.tested_programs,
            'progress_percentage': (self.session.tested_programs / self.session.total_programs * 100) if self.session.total_programs > 0 else 0,
            'vulnerabilities_found': self.session.vulnerabilities_found,
            'critical_vulns': self.session.critical_vulns,
            'high_severity_vulns': self.session.high_severity_vulns,
            'programs_per_hour': (self.session.tested_programs / (duration / 3600)) if duration > 0 else 0,
            'vulnerabilities_per_hour': (self.session.vulnerabilities_found / (duration / 3600)) if duration > 0 else 0,
            'estimated_completion': self._estimate_completion_time(),
        }
    
    def _estimate_completion_time(self) -> Optional[str]:
        """Оценка времени завершения"""
        if not self.session or self.session.tested_programs == 0:
            return None
        
        current_time = datetime.now()
        duration = (current_time - self.session.start_time).total_seconds()
        
        programs_per_second = self.session.tested_programs / duration
        remaining_programs = self.session.total_programs - self.session.tested_programs
        
        if programs_per_second > 0:
            estimated_seconds = remaining_programs / programs_per_second
            completion_time = current_time + timedelta(seconds=estimated_seconds)
            return completion_time.strftime('%Y-%m-%d %H:%M:%S')
        
        return None
    
    def generate_final_report(self) -> str:
        """Генерация финального отчета"""
        if not self.session:
            return "Сессия тестирования не найдена"
        
        stats = self.get_real_time_stats()
        
        report = f"# Отчет о массовом тестировании Immunefi\n\n"
        report += f"**Сессия:** {self.session.session_id}\n"
        report += f"**Статус:** {self.session.status}\n"
        report += f"**Время начала:** {self.session.start_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
        
        if self.session.end_time:
            report += f"**Время завершения:** {self.session.end_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
            duration = self.session.end_time - self.session.start_time
            report += f"**Продолжительность:** {str(duration)}\n"
        
        report += f"\n## Результаты тестирования:\n"
        report += f"- Всего программ: {self.session.total_programs}\n"
        report += f"- Протестировано программ: {self.session.tested_programs}\n"
        report += f"- Прогресс: {stats.get('progress_percentage', 0):.1f}%\n"
        report += f"- Найдено уязвимостей: {self.session.vulnerabilities_found}\n"
        report += f"- Критических уязвимостей: {self.session.critical_vulns}\n"
        report += f"- Высокой серьезности: {self.session.high_severity_vulns}\n"
        
        report += f"\n## Производительность:\n"
        report += f"- Программ в час: {stats.get('programs_per_hour', 0):.1f}\n"
        report += f"- Уязвимостей в час: {stats.get('vulnerabilities_per_hour', 0):.1f}\n"
        
        if self.session.tested_programs > 0:
            avg_vulns = self.session.vulnerabilities_found / self.session.tested_programs
            report += f"- Среднее количество уязвимостей на программу: {avg_vulns:.2f}\n"
        
        # Топ программы с уязвимостями
        program_vulns = {}
        for vuln in self.all_vulnerabilities:
            program_vulns[vuln.program_name] = program_vulns.get(vuln.program_name, 0) + 1
        
        if program_vulns:
            report += f"\n## Топ программы с уязвимостями:\n"
            sorted_programs = sorted(program_vulns.items(), key=lambda x: x[1], reverse=True)
            for i, (program, count) in enumerate(sorted_programs[:10], 1):
                report += f"{i}. {program}: {count} уязвимостей\n"
        
        return report

async def main():
    """Основная функция для запуска массового тестирования"""
    logger.info("Запуск Immunefi Mass Tester...")
    
    async with ImmunefiBountyMassTester(max_concurrent=5, rate_limit_delay=2.0) as mass_tester:
        try:
            # Запуск массового тестирования
            # Тестируем первые 50 программ или в течение 2 часов
            session = await mass_tester.start_mass_testing(
                target_programs=50,
                time_limit_hours=2.0
            )
            
            # Генерация финального отчета
            report = mass_tester.generate_final_report()
            
            # Сохранение отчета
            with open(f"final_report_{session.session_id}.md", 'w', encoding='utf-8') as f:
                f.write(report)
            
            logger.info("Массовое тестирование завершено успешно")
            print(f"\nФинальная статистика:")
            print(f"Протестировано программ: {session.tested_programs}")
            print(f"Найдено уязвимостей: {session.vulnerabilities_found}")
            print(f"Критических: {session.critical_vulns}")
            print(f"Высокой серьезности: {session.high_severity_vulns}")
            
        except KeyboardInterrupt:
            logger.info("Получен сигнал прерывания, остановка тестирования...")
            await mass_tester.stop_testing()
        except Exception as e:
            logger.error(f"Ошибка массового тестирования: {e}")
            raise

if __name__ == "__main__":
    asyncio.run(main())
