#!/usr/bin/env python3
"""
🚀 IMPROVED IMMUNEFI SCANNER
Улучшенный парсер Immunefi с обходом ошибки 405
"""

import asyncio
import aiohttp
import json
import re
from typing import Dict, List, Any, Optional
from bs4 import BeautifulSoup
import logging

# Настройка логирования
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ImprovedImmunefiBountyParser:
    """Улучшенный парсер программ Immunefi"""
    
    def __init__(self):
        self.session = None
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
    
    async def __aenter__(self):
        """Асинхронный контекстный менеджер"""
        connector = aiohttp.TCPConnector(limit=10, limit_per_host=5)
        timeout = aiohttp.ClientTimeout(total=30, connect=10)
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers=self.headers
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Закрытие сессии"""
        if self.session:
            await self.session.close()
    
    async def fetch_bounty_list(self) -> List[Dict[str, Any]]:
        """Получение списка программ с множественными методами"""
        logger.info("🚀 Запуск улучшенного парсинга Immunefi...")
        
        all_programs = []
        
        # Метод 1: Прямой парсинг главной страницы
        main_page_programs = await self._parse_main_page()
        if main_page_programs:
            all_programs.extend(main_page_programs)
            logger.info(f"✅ Главная страница: {len(main_page_programs)} программ")
        
        # Метод 2: Парсинг страницы bug-bounty
        bug_bounty_programs = await self._parse_bug_bounty_page()
        if bug_bounty_programs:
            # Объединяем без дубликатов
            existing_names = {p.get('name', '') for p in all_programs}
            new_programs = [p for p in bug_bounty_programs if p.get('name', '') not in existing_names]
            all_programs.extend(new_programs)
            logger.info(f"✅ Bug bounty страница: {len(new_programs)} новых программ")
        
        # Метод 3: API endpoints (если доступны)
        api_programs = await self._try_api_endpoints()
        if api_programs:
            existing_names = {p.get('name', '') for p in all_programs}
            new_programs = [p for p in api_programs if p.get('name', '') not in existing_names]
            all_programs.extend(new_programs)
            logger.info(f"✅ API endpoints: {len(new_programs)} новых программ")
        
        # Метод 4: Sitemap парсинг
        sitemap_programs = await self._parse_sitemap()
        if sitemap_programs:
            existing_names = {p.get('name', '') for p in all_programs}
            new_programs = [p for p in sitemap_programs if p.get('name', '') not in existing_names]
            all_programs.extend(new_programs)
            logger.info(f"✅ Sitemap: {len(new_programs)} новых программ")
        
        logger.info(f"🎯 ИТОГО НАЙДЕНО: {len(all_programs)} уникальных программ")
        return all_programs
    
    async def _parse_main_page(self) -> List[Dict[str, Any]]:
        """Парсинг главной страницы Immunefi"""
        try:
            url = "https://immunefi.com/"
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    html = await response.text()
                    soup = BeautifulSoup(html, 'html.parser')
                    
                    programs = []
                    
                    # Ищем различные селекторы для программ
                    selectors = [
                        'div[data-testid*="bounty"]',
                        'div[class*="bounty"]',
                        'div[class*="program"]',
                        'a[href*="/bounty/"]',
                        'div[class*="card"]'
                    ]
                    
                    for selector in selectors:
                        elements = soup.select(selector)
                        for element in elements:
                            program = self._extract_program_from_element(element)
                            if program and program not in programs:
                                programs.append(program)
                    
                    return programs
                    
        except Exception as e:
            logger.error(f"Ошибка парсинга главной страницы: {e}")
        
        return []
    
    async def _parse_bug_bounty_page(self) -> List[Dict[str, Any]]:
        """Парсинг страницы bug-bounty"""
        try:
            url = "https://immunefi.com/bug-bounty/"
            
            async with self.session.get(url) as response:
                if response.status == 200:
                    html = await response.text()
                    soup = BeautifulSoup(html, 'html.parser')
                    
                    programs = []
                    
                    # Ищем ссылки на программы
                    bounty_links = soup.find_all('a', href=re.compile(r'/bounty/[^/]+/?$'))
                    
                    for link in bounty_links:
                        href = link.get('href', '')
                        if href.startswith('/'):
                            href = 'https://immunefi.com' + href
                        
                        # Извлекаем название из ссылки или текста
                        name = link.get_text(strip=True)
                        if not name:
                            # Пытаемся извлечь из URL
                            slug = href.split('/bounty/')[-1].rstrip('/')
                            name = slug.replace('-', ' ').title()
                        
                        if name and len(name) > 2:
                            program = {
                                'name': name,
                                'url': href,
                                'slug': href.split('/bounty/')[-1].rstrip('/'),
                                'source': 'bug_bounty_page'
                            }
                            
                            # Ищем дополнительную информацию рядом с ссылкой
                            parent = link.parent
                            if parent:
                                # Ищем награду
                                reward_text = parent.get_text()
                                reward_match = re.search(r'\$[\d,]+(?:K|M)?', reward_text)
                                if reward_match:
                                    program['max_bounty'] = reward_match.group()
                                
                                # Ищем экосистему
                                ecosystem_keywords = ['Ethereum', 'Solana', 'Polygon', 'BSC', 'Avalanche', 'Arbitrum']
                                for keyword in ecosystem_keywords:
                                    if keyword.lower() in reward_text.lower():
                                        program['ecosystem'] = keyword
                                        break
                            
                            programs.append(program)
                    
                    # Также ищем в JSON данных на странице
                    scripts = soup.find_all('script', type='application/json')
                    for script in scripts:
                        try:
                            data = json.loads(script.string)
                            json_programs = self._extract_programs_from_json(data)
                            programs.extend(json_programs)
                        except:
                            continue
                    
                    return programs
                    
        except Exception as e:
            logger.error(f"Ошибка парсинга bug-bounty страницы: {e}")
        
        return []
    
    async def _try_api_endpoints(self) -> List[Dict[str, Any]]:
        """Попытка получения данных через различные API endpoints"""
        endpoints = [
            "https://immunefi.com/api/bounty-programs",
            "https://immunefi.com/api/v1/bounty-programs", 
            "https://immunefi.com/api/v2/bounty-programs",
            "https://api.immunefi.com/bounty-programs",
            "https://api.immunefi.com/v1/bounty-programs",
            "https://immunefi.com/_next/static/chunks/pages/bug-bounty.js",
            "https://immunefi.com/sitemap.xml"
        ]
        
        for endpoint in endpoints:
            try:
                async with self.session.get(endpoint) as response:
                    if response.status == 200:
                        content_type = response.headers.get('content-type', '')
                        
                        if 'application/json' in content_type:
                            data = await response.json()
                            programs = self._extract_programs_from_json(data)
                            if programs:
                                logger.info(f"✅ API endpoint {endpoint}: {len(programs)} программ")
                                return programs
                        
                        elif 'text/javascript' in content_type or 'application/javascript' in content_type:
                            text = await response.text()
                            programs = self._extract_programs_from_js(text)
                            if programs:
                                logger.info(f"✅ JS endpoint {endpoint}: {len(programs)} программ")
                                return programs
                                
            except Exception as e:
                continue
        
        return []
    
    async def _parse_sitemap(self) -> List[Dict[str, Any]]:
        """Парсинг sitemap для поиска программ"""
        try:
            sitemap_urls = [
                "https://immunefi.com/sitemap.xml",
                "https://immunefi.com/sitemap_index.xml",
                "https://immunefi.com/bounty-sitemap.xml"
            ]
            
            for sitemap_url in sitemap_urls:
                try:
                    async with self.session.get(sitemap_url) as response:
                        if response.status == 200:
                            xml_content = await response.text()
                            
                            # Ищем URL программ в sitemap
                            bounty_urls = re.findall(r'https://immunefi\.com/bounty/([^<]+)', xml_content)
                            
                            programs = []
                            for slug in bounty_urls:
                                slug = slug.rstrip('/')
                                if slug and len(slug) > 2:
                                    program = {
                                        'name': slug.replace('-', ' ').title(),
                                        'slug': slug,
                                        'url': f'https://immunefi.com/bounty/{slug}/',
                                        'source': 'sitemap'
                                    }
                                    programs.append(program)
                            
                            if programs:
                                logger.info(f"✅ Sitemap {sitemap_url}: {len(programs)} программ")
                                return programs
                                
                except Exception as e:
                    continue
                    
        except Exception as e:
            logger.error(f"Ошибка парсинга sitemap: {e}")
        
        return []
    
    def _extract_program_from_element(self, element) -> Optional[Dict[str, Any]]:
        """Извлечение информации о программе из HTML элемента"""
        try:
            # Ищем ссылку на программу
            link = element.find('a', href=re.compile(r'/bounty/'))
            if not link:
                link = element if element.name == 'a' and '/bounty/' in element.get('href', '') else None
            
            if link:
                href = link.get('href', '')
                if href.startswith('/'):
                    href = 'https://immunefi.com' + href
                
                name = link.get_text(strip=True)
                if not name:
                    name = href.split('/bounty/')[-1].rstrip('/').replace('-', ' ').title()
                
                if name and len(name) > 2:
                    return {
                        'name': name,
                        'url': href,
                        'slug': href.split('/bounty/')[-1].rstrip('/'),
                        'source': 'html_parsing'
                    }
        except:
            pass
        
        return None
    
    def _extract_programs_from_json(self, data) -> List[Dict[str, Any]]:
        """Извлечение программ из JSON данных"""
        programs = []
        
        try:
            # Рекурсивный поиск программ в JSON
            def find_programs(obj, path=""):
                if isinstance(obj, dict):
                    # Проверяем, является ли это программой
                    if self._looks_like_program(obj):
                        program = self._normalize_program_data(obj)
                        if program:
                            programs.append(program)
                    
                    # Рекурсивно ищем в значениях
                    for key, value in obj.items():
                        find_programs(value, f"{path}.{key}")
                
                elif isinstance(obj, list):
                    for i, item in enumerate(obj):
                        find_programs(item, f"{path}[{i}]")
            
            find_programs(data)
            
        except Exception as e:
            logger.error(f"Ошибка извлечения из JSON: {e}")
        
        return programs
    
    def _extract_programs_from_js(self, js_content: str) -> List[Dict[str, Any]]:
        """Извлечение программ из JavaScript кода"""
        programs = []
        
        try:
            # Ищем JSON объекты в JS коде
            json_matches = re.findall(r'\{[^{}]*"bounty"[^{}]*\}', js_content)
            json_matches.extend(re.findall(r'\{[^{}]*"program"[^{}]*\}', js_content))
            json_matches.extend(re.findall(r'\{[^{}]*"name"[^{}]*"slug"[^{}]*\}', js_content))
            
            for match in json_matches:
                try:
                    data = json.loads(match)
                    if self._looks_like_program(data):
                        program = self._normalize_program_data(data)
                        if program:
                            programs.append(program)
                except:
                    continue
            
            # Также ищем URL паттерны
            url_matches = re.findall(r'/bounty/([a-zA-Z0-9\-]+)', js_content)
            for slug in url_matches:
                if len(slug) > 2:
                    program = {
                        'name': slug.replace('-', ' ').title(),
                        'slug': slug,
                        'url': f'https://immunefi.com/bounty/{slug}/',
                        'source': 'js_parsing'
                    }
                    programs.append(program)
            
        except Exception as e:
            logger.error(f"Ошибка извлечения из JS: {e}")
        
        return programs
    
    def _looks_like_program(self, obj: Dict) -> bool:
        """Проверка, похож ли объект на программу bug bounty"""
        if not isinstance(obj, dict):
            return False
        
        # Ключевые поля программы
        program_indicators = [
            'name', 'slug', 'bounty', 'maxBounty', 'program', 
            'ecosystem', 'vulnerability', 'reward'
        ]
        
        return any(key in obj for key in program_indicators)
    
    def _normalize_program_data(self, data: Dict) -> Optional[Dict[str, Any]]:
        """Нормализация данных программы"""
        try:
            name = data.get('name') or data.get('title') or data.get('program')
            slug = data.get('slug') or data.get('id')
            
            if not name:
                return None
            
            program = {
                'name': str(name).strip(),
                'slug': str(slug).strip() if slug else name.lower().replace(' ', '-'),
                'url': f"https://immunefi.com/bounty/{slug}/" if slug else "",
                'max_bounty': data.get('maxBounty') or data.get('bounty') or data.get('reward') or 'Private',
                'ecosystem': data.get('ecosystem') or data.get('blockchain') or 'Unknown',
                'source': 'json_data'
            }
            
            # Дополнительные поля если есть
            optional_fields = ['totalPaid', 'vaultTvl', 'kycRequired', 'pocRequired', 'lastUpdated']
            for field in optional_fields:
                if field in data:
                    program[field] = data[field]
            
            return program
            
        except Exception as e:
            logger.error(f"Ошибка нормализации данных: {e}")
            return None

async def main():
    """Тестирование улучшенного парсера"""
    print("🚀 ТЕСТИРОВАНИЕ УЛУЧШЕННОГО IMMUNEFI ПАРСЕРА")
    print("=" * 60)
    
    async with ImprovedImmunefiBountyParser() as parser:
        programs = await parser.fetch_bounty_list()
        
        print(f"\n📊 РЕЗУЛЬТАТЫ:")
        print(f"   Всего программ: {len(programs)}")
        
        if programs:
            print(f"\n📋 ПЕРВЫЕ 10 ПРОГРАММ:")
            for i, program in enumerate(programs[:10], 1):
                print(f"   {i}. {program.get('name', 'Unknown')}")
                print(f"      URL: {program.get('url', 'N/A')}")
                print(f"      Награда: {program.get('max_bounty', 'Private')}")
                print(f"      Источник: {program.get('source', 'Unknown')}")
                print()
        
        # Сохранение результатов
        if programs:
            filename = f"improved_immunefi_programs_{int(time.time())}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(programs, f, indent=2, ensure_ascii=False)
            print(f"💾 Результаты сохранены: {filename}")

if __name__ == "__main__":
    import time
    asyncio.run(main())
