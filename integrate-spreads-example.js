// 📝 ПРИМЕР ИНТЕГРАЦИИ АНАЛИЗАТОРА СПРЕДОВ В BMETEORA.JS
// Показывает как добавить анализ спредов в существующий бот

const BMeteoraSpreadsIntegration = require('./bmeteora-spread-integration.js');

/**
 * 📝 ПРИМЕР ИНТЕГРАЦИИ В BMETEORA.JS
 * 
 * Добавьте этот код в ваш BMeteora.js для включения анализа спредов
 */

// 🔗 ДОБАВИТЬ В КОНСТРУКТОР MeteoraInternalArbitrageBot:
/*
constructor() {
    // ... существующий код ...
    
    // 🎯 ДОБАВЛЯЕМ АНАЛИЗАТОР СПРЕДОВ
    this.spreadsIntegration = null;
    this.spreadsEnabled = true; // Включить анализ спредов
    
    console.log('🎯 Анализатор спредов будет инициализирован');
}
*/

// 🚀 ДОБАВИТЬ В МЕТОД initialize():
/*
async initialize() {
    try {
        // ... существующий код инициализации ...
        
        // 🎯 ИНИЦИАЛИЗАЦИЯ АНАЛИЗАТОРА СПРЕДОВ
        if (this.spreadsEnabled) {
            console.log('🎯 Инициализация анализатора спредов...');
            this.spreadsIntegration = new BMeteoraSpreadsIntegration(this);
            await this.spreadsIntegration.initialize();
            console.log('✅ Анализатор спредов готов к работе');
        }
        
        // ... остальной код инициализации ...
        
    } catch (error) {
        console.error('❌ Ошибка инициализации:', error.message);
        throw error;
    }
}
*/

// 🔄 МОДИФИКАЦИЯ ОСНОВНОГО ЦИКЛА analyzeAndExecuteArbitrage():
/*
async analyzeAndExecuteArbitrage() {
    try {
        // ... существующий код ...
        
        // 🎯 АНАЛИЗ СПРЕДОВ (ЕСЛИ ВКЛЮЧЕН)
        let spreadOpportunities = [];
        if (this.spreadsIntegration && this.spreadsEnabled) {
            spreadOpportunities = await this.spreadsIntegration.analyzeCurrentSpreads();
            
            // 🔥 ПРИОРИТЕТ БОЛЬШИМ СПРЕДАМ
            const bigSpreads = spreadOpportunities.filter(opp => opp.spread.percent >= 0.5);
            if (bigSpreads.length > 0) {
                console.log(`🚨 БОЛЬШИЕ СПРЕДЫ ОБНАРУЖЕНЫ: ${bigSpreads.length} возможностей!`.red.bold);
                
                // 🎯 ИСПОЛНЯЕМ ЛУЧШИЙ СПРЕД НЕМЕДЛЕННО
                const bestSpread = bigSpreads[0];
                console.log(`🎯 Исполняем лучший спред: ${bestSpread.spread.percent.toFixed(4)}%`);
                
                // 🔥 КОНВЕРТИРУЕМ СПРЕД-ВОЗМОЖНОСТЬ В ФОРМАТ ОСНОВНОГО БОТА
                const spreadOpportunity = this.convertSpreadToArbitrageOpportunity(bestSpread);
                
                if (spreadOpportunity) {
                    console.log('🔥 Исполняем спред-арбитраж через flash loan...');
                    await this.executeFlashLoanArbitrage(spreadOpportunity);
                    return; // Выходим после исполнения спреда
                }
            }
        }
        
        // ... остальной существующий код анализа ...
        
    } catch (error) {
        console.error('❌ Ошибка в цикле анализа:', error.message);
    }
}
*/

// 🔄 ДОБАВИТЬ МЕТОД КОНВЕРТАЦИИ СПРЕД-ВОЗМОЖНОСТИ:
/*
convertSpreadToArbitrageOpportunity(spreadOpp) {
    try {
        // 🎯 КОНВЕРТИРУЕМ СПРЕД-ВОЗМОЖНОСТЬ В ФОРМАТ ОСНОВНОГО БОТА
        return {
            sellPool: {
                address: spreadOpp.sellPool.address,
                name: spreadOpp.sellPool.name,
                price: spreadOpp.sellPool.price,
                liquidity: spreadOpp.sellPool.liquidity
            },
            buyPool: {
                address: spreadOpp.buyPool.address,
                name: spreadOpp.buyPool.name,
                price: spreadOpp.buyPool.price,
                liquidity: spreadOpp.buyPool.liquidity
            },
            spread: spreadOpp.spread.percent,
            expectedProfit: spreadOpp.expectedProfit,
            loanAmount: spreadOpp.flashLoan.amountUSD,
            direction: spreadOpp.direction,
            type: 'SPREAD_ARBITRAGE',
            priority: spreadOpp.priority || 10,
            source: 'SPREAD_ANALYZER'
        };
    } catch (error) {
        console.error('❌ Ошибка конвертации спред-возможности:', error.message);
        return null;
    }
}
*/

// 📊 ДОБАВИТЬ В МЕТОД showProgress():
/*
showProgress() {
    // ... существующий код ...
    
    // 📊 СТАТИСТИКА СПРЕДОВ
    if (this.spreadsIntegration) {
        const spreadStats = this.spreadsIntegration.getIntegrationStats();
        console.log(`🎯 Анализов спредов: ${spreadStats.integration.totalSpreadsAnalyzed}`);
        console.log(`💰 Прибыль от спредов: $${spreadStats.integration.totalProfitFromSpreads.toFixed(2)}`);
    }
}
*/

// 🛑 ДОБАВИТЬ В МЕТОД stopEmergencyMode():
/*
stopEmergencyMode() {
    // ... существующий код ...
    
    // 🛑 ОСТАНОВКА АНАЛИЗАТОРА СПРЕДОВ
    if (this.spreadsIntegration) {
        this.spreadsIntegration.stop();
        console.log('🛑 Анализатор спредов остановлен');
    }
}
*/

/**
 * 🎯 ПОЛНЫЙ ПРИМЕР ИНТЕГРАЦИИ
 * 
 * Вот как будет выглядеть модифицированный BMeteora.js:
 */

console.log(`
🎯 ПРИМЕР ПОЛНОЙ ИНТЕГРАЦИИ В BMETEORA.JS:

1. 📦 ИМПОРТ В НАЧАЛЕ ФАЙЛА:
   const BMeteoraSpreadsIntegration = require('./bmeteora-spread-integration.js');

2. 🏗️ В КОНСТРУКТОРЕ:
   this.spreadsIntegration = null;
   this.spreadsEnabled = true;

3. 🚀 В МЕТОДЕ initialize():
   if (this.spreadsEnabled) {
       this.spreadsIntegration = new BMeteoraSpreadsIntegration(this);
       await this.spreadsIntegration.initialize();
   }

4. 🔄 В ОСНОВНОМ ЦИКЛЕ analyzeAndExecuteArbitrage():
   if (this.spreadsIntegration) {
       const spreadOpportunities = await this.spreadsIntegration.analyzeCurrentSpreads();
       // Обработка найденных спредов...
   }

5. 📊 В СТАТИСТИКЕ:
   if (this.spreadsIntegration) {
       this.spreadsIntegration.showStats();
   }

6. 🛑 ПРИ ОСТАНОВКЕ:
   if (this.spreadsIntegration) {
       this.spreadsIntegration.stop();
   }
`);

/**
 * 🔧 НАСТРОЙКИ КОНФИГУРАЦИИ
 */
const SPREAD_CONFIG = {
    // 🎯 ОСНОВНЫЕ НАСТРОЙКИ
    enableSpreadAnalysis: true,           // Включить анализ спредов
    enableAutoExecution: false,           // Автоисполнение (осторожно!)
    
    // 📊 ПОРОГИ СПРЕДОВ
    minSpreadForAlert: 0.1,              // 0.1% - минимум для алерта
    minSpreadForExecution: 0.2,          // 0.2% - минимум для исполнения
    emergencySpreadThreshold: 1.0,       // 1.0% - экстренное исполнение
    
    // ⏱️ ТАЙМИНГИ
    spreadAnalysisInterval: 1000,        // 1 секунда между анализами
    maxConcurrentAnalysis: 3,            // Максимум 3 анализа одновременно
    
    // 💰 ЛИМИТЫ
    maxFlashLoanForSpreads: 25000,       // $25k максимум для спред-арбитража
    minProfitForExecution: 10,           // $10 минимальная прибыль
    
    // 🔄 ПРИОРИТЕТЫ
    spreadPriorityMultiplier: 2.0,       // Умножитель приоритета для спредов
    enableSpreadPriority: true           // Приоритет спредам над обычным арбитражем
};

console.log('\n🔧 РЕКОМЕНДУЕМАЯ КОНФИГУРАЦИЯ:');
console.log(JSON.stringify(SPREAD_CONFIG, null, 2));

/**
 * 🚀 КОМАНДЫ ДЛЯ ЗАПУСКА С АНАЛИЗОМ СПРЕДОВ
 */
console.log(`
🚀 КОМАНДЫ ДЛЯ ЗАПУСКА:

1. 🎯 ОБЫЧНЫЙ РЕЖИМ С АНАЛИЗОМ СПРЕДОВ:
   node BMeteora.js

2. 🔥 ЭКСТРЕННЫЙ РЕЖИМ + СПРЕДЫ:
   node BMeteora.js --emergency

3. 🚨 ТОЛЬКО АНАЛИЗ СПРЕДОВ (БЕЗ ИСПОЛНЕНИЯ):
   node BMeteora.js --spreads-only

4. 🎯 ТЕСТ АНАЛИЗАТОРА СПРЕДОВ:
   node BMeteora.js --test-spreads

5. 📊 СТАТИСТИКА СПРЕДОВ:
   node BMeteora.js --spread-stats
`);

/**
 * 🎯 ПРЕИМУЩЕСТВА ИНТЕГРАЦИИ
 */
console.log(`
🎯 ПРЕИМУЩЕСТВА АНАЛИЗА СПРЕДОВ:

✅ ДОПОЛНИТЕЛЬНЫЕ ВОЗМОЖНОСТИ:
   - Обнаружение спредов между пулами
   - Атомарные flash loan арбитражи
   - БЕЗ залогов и долгов
   - БЕЗ комиссий flash loan

✅ УЛУЧШЕННАЯ ПРИБЫЛЬНОСТЬ:
   - Больше арбитражных возможностей
   - Приоритет большим спредам
   - Автоматическое исполнение
   - Детальная статистика

✅ БЕЗОПАСНОСТЬ:
   - Атомарные транзакции
   - Проверка ликвидности
   - Контроль рисков
   - Мониторинг в реальном времени

✅ ПРОИЗВОДИТЕЛЬНОСТЬ:
   - Кэширование данных
   - Параллельный анализ
   - Оптимизированные RPC запросы
   - Минимальная нагрузка на систему
`);

module.exports = {
    BMeteoraSpreadsIntegration,
    SPREAD_CONFIG,
    
    // 🎯 HELPER ФУНКЦИИ ДЛЯ ИНТЕГРАЦИИ
    integrateSpreadAnalyzer: async (meteoraBot) => {
        const integration = new BMeteoraSpreadsIntegration(meteoraBot);
        await integration.initialize();
        return integration;
    },
    
    // 📊 БЫСТРАЯ ПРОВЕРКА СПРЕДОВ
    quickSpreadCheck: async (meteoraBot) => {
        const integration = new BMeteoraSpreadsIntegration(meteoraBot);
        await integration.initialize();
        const opportunities = await integration.analyzeCurrentSpreads();
        integration.stop();
        return opportunities;
    }
};
