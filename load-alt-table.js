/**
 * 🔥 СКРИПТ ЗАГРУЗКИ ALT ТАБЛИЦЫ
 * Загружает Address Lookup Table из блокчейна и показывает все адреса
 */

const { Connection, PublicKey } = require('@solana/web3.js');
const fs = require('fs');

class ALTLoader {
    constructor() {
        this.connection = new Connection('https://api.mainnet-beta.solana.com', 'confirmed');
    }

    /**
     * 🔥 ЗАГРУЗИТЬ ALT ТАБЛИЦУ ИЗ БЛОКЧЕЙНА
     */
    async loadALTTable(altAddress) {
        console.log(`🔥 ЗАГРУЗКА ALT ТАБЛИЦЫ: ${altAddress}`);
        
        try {
            const altPubkey = new PublicKey(altAddress);
            
            // Загружаем ALT таблицу
            const altAccount = await this.connection.getAddressLookupTable(altPubkey);
            
            if (!altAccount || !altAccount.value) {
                throw new Error('ALT таблица не найдена!');
            }
            
            const addresses = altAccount.value.state.addresses;
            console.log(`✅ ЗАГРУЖЕНО ${addresses.length} АДРЕСОВ из ALT таблицы`);
            
            // Показываем первые 10 адресов
            console.log(`📊 ПЕРВЫЕ 10 АДРЕСОВ:`);
            addresses.slice(0, 10).forEach((addr, i) => {
                console.log(`   ${i}: ${addr.toString()}`);
            });
            
            if (addresses.length > 10) {
                console.log(`   ... и еще ${addresses.length - 10} адресов`);
            }
            
            return {
                address: altAddress,
                totalAddresses: addresses.length,
                addresses: addresses.map(addr => addr.toString())
            };
            
        } catch (error) {
            console.log(`❌ ОШИБКА ЗАГРУЗКИ ALT: ${error.message}`);
            throw error;
        }
    }

    /**
     * 🔥 СОХРАНИТЬ ALT ДАННЫЕ В ФАЙЛ
     */
    saveALTData(altData, filename) {
        console.log(`💾 СОХРАНЕНИЕ ALT ДАННЫХ В ФАЙЛ: ${filename}`);
        
        try {
            const jsonData = JSON.stringify(altData, null, 2);
            fs.writeFileSync(filename, jsonData);
            
            console.log(`✅ ALT ДАННЫЕ СОХРАНЕНЫ:`);
            console.log(`   Файл: ${filename}`);
            console.log(`   Адресов: ${altData.totalAddresses}`);
            console.log(`   Размер файла: ${jsonData.length} байт`);
            
        } catch (error) {
            console.log(`❌ ОШИБКА СОХРАНЕНИЯ: ${error.message}`);
            throw error;
        }
    }

    /**
     * 🔥 ЗАГРУЗИТЬ И СОХРАНИТЬ ALT ТАБЛИЦУ
     */
    async loadAndSaveALT(altAddress, filename) {
        console.log(`🚀 ЗАГРУЗКА И СОХРАНЕНИЕ ALT ТАБЛИЦЫ...`);
        console.log(`   ALT Address: ${altAddress}`);
        console.log(`   Output File: ${filename}`);
        
        try {
            // Загружаем ALT данные
            const altData = await this.loadALTTable(altAddress);
            
            // Сохраняем в файл
            this.saveALTData(altData, filename);
            
            console.log(`🎉 УСПЕШНО ЗАВЕРШЕНО!`);
            return altData;
            
        } catch (error) {
            console.log(`💥 ОШИБКА: ${error.message}`);
            throw error;
        }
    }
}

// 🔥 ОСНОВНАЯ ФУНКЦИЯ
async function main() {
    const loader = new ALTLoader();
    
    // ALT адреса для загрузки
    const altTables = [
        {
            name: 'Meteora ALT 1',
            address: 'G4fR7p8a4yAHE2i2WeGn8kMWgRjBQNKhKdJhQNjKKKKK',
            filename: 'meteora-alt-1.json'
        },
        {
            name: 'Custom ALT 2',
            address: 'FAeyUf4AdG7vTQ2Q89r9hWBkPdYkykJKG4CxgqArBGXe',
            filename: 'custom-alt-data-updated.json'
        }
    ];
    
    console.log(`🔥 ЗАГРУЗКА ${altTables.length} ALT ТАБЛИЦ...`);
    
    for (const alt of altTables) {
        console.log(`\n${'='.repeat(60)}`);
        console.log(`🔥 ${alt.name.toUpperCase()}`);
        console.log(`${'='.repeat(60)}`);
        
        try {
            await loader.loadAndSaveALT(alt.address, alt.filename);
        } catch (error) {
            console.log(`❌ ОШИБКА С ${alt.name}: ${error.message}`);
        }
    }
    
    console.log(`\n🎉 ВСЕ ALT ТАБЛИЦЫ ОБРАБОТАНЫ!`);
}

// Запуск скрипта
if (require.main === module) {
    main().catch(console.error);
}

module.exports = ALTLoader;
