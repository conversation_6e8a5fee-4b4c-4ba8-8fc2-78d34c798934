/**
 * 🔥 ОФИЦИАЛЬНОЕ РЕШЕНИЕ БЕЗ ПАТЧЕЙ!
 * 
 * ПРОБЛЕМА: buildFlashLoanTx вызывает makeEndFlashLoanIx БЕЗ endIndex
 * РЕШЕНИЕ: Переопределяем makeEndFlashLoanIx чтобы получать endIndex из buildFlashLoanTx
 */

const { PublicKey, TransactionInstruction } = require('@solana/web3.js');

console.log('🔥 ОФИЦИАЛЬНОЕ РЕШЕНИЕ MARGINFI БЕЗ ПАТЧЕЙ!');

/**
 * 🎯 ОФИЦИАЛЬНОЕ РЕШЕНИЕ: Создаем ПРАВИЛЬНЫЙ MarginfiAccountWrapper
 */
function createOfficialMarginfiAccountWrapper(originalWrapper) {
  console.log('🔧 Создаем ОФИЦИАЛЬНЫЙ MarginfiAccountWrapper с правильным makeEndFlashLoanIx...');
  
  // Создаем новый объект с теми же свойствами
  const officialWrapper = Object.create(Object.getPrototypeOf(originalWrapper));
  Object.assign(officialWrapper, originalWrapper);
  
  // 🔥 ПЕРЕОПРЕДЕЛЯЕМ buildFlashLoanTx ОФИЦИАЛЬНО!
  officialWrapper.buildFlashLoanTx = async function(args, lookupTables) {
    console.log('🚀 ОФИЦИАЛЬНЫЙ buildFlashLoanTx с правильным endIndex!');
    
    const endIndex = args.ixs.length + 1;
    console.log(`📊 Вычисленный endIndex: ${endIndex}`);
    
    const projectedActiveBalances = this._marginfiAccount.projectActiveBalancesNoCpi(this._program, args.ixs);
    console.log(`📊 Projected balances: ${projectedActiveBalances.length}`);
    
    // ✅ ПРАВИЛЬНО: передаем endIndex в makeBeginFlashLoanIx
    const beginFlashLoanIx = await this.makeBeginFlashLoanIx(endIndex);
    console.log('✅ Begin flash loan инструкция создана');
    
    // 🔥 ИСПРАВЛЕНИЕ: создаем ПРАВИЛЬНУЮ end flash loan инструкцию с endIndex!
    const endFlashLoanIx = await this.makeOfficialEndFlashLoanIx(projectedActiveBalances, endIndex);
    console.log('✅ End flash loan инструкция создана С ПРАВИЛЬНЫМ endIndex!');
    
    const flashloanIxs = [...beginFlashLoanIx.instructions, ...args.ixs, ...endFlashLoanIx.instructions];
    console.log(`📊 Всего flash loan инструкций: ${flashloanIxs.length}`);
    
    const totalLookupTables = [...(lookupTables ?? []), ...(args.addressLookupTableAccounts ?? [])];
    
    const { TransactionMessage, VersionedTransaction } = require('@solana/web3.js');
    const { addTransactionMetadata, TransactionType } = require('@mrgnlabs/mrgn-common');
    
    const blockhash = args.blockhash ?? (await this._program.provider.connection.getLatestBlockhash("confirmed")).blockhash;
    
    const message = new TransactionMessage({
      payerKey: this.client.wallet.publicKey,
      recentBlockhash: blockhash,
      instructions: flashloanIxs,
    }).compileToV0Message(totalLookupTables);
    
    const tx = addTransactionMetadata(new VersionedTransaction(message), {
      addressLookupTables: totalLookupTables,
      type: TransactionType.FLASHLOAN,
    });
    
    console.log('🎯 ОФИЦИАЛЬНАЯ flash loan транзакция создана!');
    return tx;
  };
  
  // 🔥 НОВЫЙ МЕТОД: makeOfficialEndFlashLoanIx с endIndex!
  officialWrapper.makeOfficialEndFlashLoanIx = async function(projectedActiveBalances, endIndex) {
    console.log(`🔧 Создаем ОФИЦИАЛЬНУЮ end flash loan инструкцию с endIndex: ${endIndex}`);
    
    // Получаем MARGINFI_PROGRAM_ID
    const MARGINFI_PROGRAM_ID = this._program.programId;
    console.log(`📋 MarginFi Program ID: ${MARGINFI_PROGRAM_ID.toString()}`);
    
    // Создаем instruction data с правильным форматом
    const instructionData = Buffer.alloc(16);
    instructionData.writeUInt8(1, 0); // Discriminator для end flash loan
    instructionData.writeBigUInt64LE(BigInt(endIndex), 8); // endIndex как u64
    
    console.log(`📊 Instruction data: ${instructionData.toString('hex')}`);
    console.log(`📊 Discriminator: ${instructionData.readUInt8(0)}`);
    console.log(`📊 EndIndex: ${instructionData.readBigUInt64LE(8)}`);
    
    // Создаем ключи для инструкции
    const keys = [
      { pubkey: this.address, isSigner: false, isWritable: true },
      { pubkey: this.client.wallet.publicKey, isSigner: true, isWritable: true }
    ];
    
    // Добавляем projected active balances как readonly ключи
    for (const balance of projectedActiveBalances) {
      keys.push({ pubkey: balance, isSigner: false, isWritable: false });
    }
    
    console.log(`📊 Всего ключей в end flash loan: ${keys.length}`);
    
    const endFlashLoanIx = new TransactionInstruction({
      keys: keys,
      programId: MARGINFI_PROGRAM_ID,
      data: instructionData
    });
    
    console.log('✅ ОФИЦИАЛЬНАЯ end flash loan инструкция создана!');
    
    return { 
      instructions: [endFlashLoanIx], 
      keys: [] 
    };
  };
  
  console.log('✅ ОФИЦИАЛЬНЫЙ MarginfiAccountWrapper создан!');
  return officialWrapper;
}

/**
 * 🎯 ПРИМЕНЯЕМ ОФИЦИАЛЬНОЕ РЕШЕНИЕ
 */
function applyOfficialSolution() {
  console.log('🚀 ПРИМЕНЯЕМ ОФИЦИАЛЬНОЕ РЕШЕНИЕ MARGINFI!');
  
  try {
    // Находим MarginfiAccountWrapper в обеих локациях
    const locations = [
      './solana-flash-loans/node_modules/@mrgnlabs/marginfi-client-v2/dist/models/account/wrapper',
      './node_modules/@mrgnlabs/marginfi-client-v2/dist/models/account/wrapper'
    ];
    
    for (const location of locations) {
      try {
        const wrapperModule = require(location);
        
        if (wrapperModule.MarginfiAccountWrapper) {
          console.log(`🔧 Найден MarginfiAccountWrapper в ${location}`);
          
          // Сохраняем оригинальный конструктор
          const OriginalMarginfiAccountWrapper = wrapperModule.MarginfiAccountWrapper;
          
          // 🔥 ПЕРЕОПРЕДЕЛЯЕМ КОНСТРУКТОР для создания ОФИЦИАЛЬНЫХ объектов!
          wrapperModule.MarginfiAccountWrapper = class extends OriginalMarginfiAccountWrapper {
            constructor(...args) {
              super(...args);
              
              // Применяем официальное решение к этому экземпляру
              const officialWrapper = createOfficialMarginfiAccountWrapper(this);
              
              // Копируем все методы обратно в this
              Object.setPrototypeOf(this, Object.getPrototypeOf(officialWrapper));
              Object.assign(this, officialWrapper);
            }
          };
          
          // Копируем статические методы
          Object.setPrototypeOf(wrapperModule.MarginfiAccountWrapper, OriginalMarginfiAccountWrapper);
          Object.assign(wrapperModule.MarginfiAccountWrapper, OriginalMarginfiAccountWrapper);
          
          console.log(`✅ ОФИЦИАЛЬНОЕ РЕШЕНИЕ применено в ${location}`);
        }
      } catch (error) {
        console.log(`⚠️ Модуль ${location} не найден: ${error.message}`);
      }
    }
    
    console.log('✅ ОФИЦИАЛЬНОЕ РЕШЕНИЕ MARGINFI применено!');
    
  } catch (error) {
    console.error(`❌ Ошибка применения официального решения: ${error.message}`);
    throw error;
  }
}

module.exports = {
  applyOfficialSolution,
  createOfficialMarginfiAccountWrapper
};
