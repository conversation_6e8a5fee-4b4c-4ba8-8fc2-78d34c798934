#!/usr/bin/env python3
"""
🚀 MASSIVE HUNTING CAMPAIGN
Массовая кампания поиска уязвимостей с максимальным покрытием
"""

import asyncio
import json
import time
from datetime import datetime
import logging

from master_bug_hunting_coordinator import MasterBugHuntingCoordinator

# Настройка логирования
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def run_massive_hunting_campaign():
    """Запуск массовой кампании поиска уязвимостей"""
    print("🚀 MASSIVE BUG HUNTING CAMPAIGN")
    print("=" * 60)
    print("🎯 ЦЕЛЬ: Максимальное покрытие всех доступных программ")
    print("⚡ СТРАТЕГИИ: Все 90+ методов")
    print("🔄 РЕЖИМ: Непрерывное тестирование")
    print("=" * 60)
    
    campaign_start = time.time()
    total_vulnerabilities = 0
    total_targets = 0
    total_strategies = 0
    cycles_completed = 0
    
    try:
        # Запуск нескольких циклов подряд
        for cycle in range(1, 6):  # 5 циклов для демонстрации
            print(f"\n🔄 ЦИКЛ {cycle}/5")
            print("-" * 40)
            
            cycle_start = time.time()
            
            try:
                async with MasterBugHuntingCoordinator() as coordinator:
                    # Увеличиваем количество целей с каждым циклом
                    max_targets = min(10 + cycle * 5, 50)  # От 15 до 50 целей
                    
                    print(f"🎯 Тестирование до {max_targets} целей...")
                    
                    # Запуск полного цикла
                    result = await coordinator.run_full_hunting_cycle(
                        max_targets=max_targets
                    )
                    
                    # Анализ результатов цикла
                    session_info = result.get('session_info', {})
                    cycle_vulnerabilities = session_info.get('vulnerabilities_found', 0)
                    cycle_targets = session_info.get('targets_processed', 0)
                    cycle_strategies = session_info.get('strategies_executed', 0)
                    cycle_time = time.time() - cycle_start
                    
                    # Обновление общей статистики
                    total_vulnerabilities += cycle_vulnerabilities
                    total_targets += cycle_targets
                    total_strategies += cycle_strategies
                    cycles_completed += 1
                    
                    # Вывод результатов цикла
                    print(f"✅ ЦИКЛ {cycle} ЗАВЕРШЕН:")
                    print(f"   🎯 Целей: {cycle_targets}")
                    print(f"   ⚡ Стратегий: {cycle_strategies}")
                    print(f"   🐛 Уязвимостей: {cycle_vulnerabilities}")
                    print(f"   ⏱️ Время: {cycle_time:.1f}с")
                    print(f"   📊 Эффективность: {cycle_vulnerabilities/cycle_strategies*100:.1f}%" if cycle_strategies > 0 else "   📊 Эффективность: 0%")
                    
                    # Проверка на критические находки
                    if cycle_vulnerabilities > 10:
                        print(f"   🚨 КРИТИЧЕСКИЕ НАХОДКИ: {cycle_vulnerabilities} уязвимостей!")
                    
                    # Сохранение промежуточного отчета
                    intermediate_report = {
                        'cycle': cycle,
                        'timestamp': datetime.now().isoformat(),
                        'cycle_results': result,
                        'cumulative_stats': {
                            'total_vulnerabilities': total_vulnerabilities,
                            'total_targets': total_targets,
                            'total_strategies': total_strategies,
                            'cycles_completed': cycles_completed
                        }
                    }
                    
                    report_filename = f"massive_campaign_cycle_{cycle}_{int(time.time())}.json"
                    with open(report_filename, 'w', encoding='utf-8') as f:
                        json.dump(intermediate_report, f, indent=2, ensure_ascii=False, default=str)
                    
                    print(f"   💾 Отчет цикла: {report_filename}")
                    
                    # Пауза между циклами (кроме последнего)
                    if cycle < 5:
                        print(f"   ⏸️ Пауза 30 секунд...")
                        await asyncio.sleep(30)
                    
            except Exception as e:
                print(f"   ❌ Ошибка в цикле {cycle}: {e}")
                continue
        
        # Финальная статистика
        campaign_duration = time.time() - campaign_start
        
        print(f"\n🎉 МАССОВАЯ КАМПАНИЯ ЗАВЕРШЕНА!")
        print("=" * 60)
        print(f"⏱️ ОБЩЕЕ ВРЕМЯ: {campaign_duration/60:.1f} минут")
        print(f"🔄 ЦИКЛОВ: {cycles_completed}")
        print(f"🎯 ВСЕГО ЦЕЛЕЙ: {total_targets}")
        print(f"⚡ ВСЕГО СТРАТЕГИЙ: {total_strategies}")
        print(f"🐛 ВСЕГО УЯЗВИМОСТЕЙ: {total_vulnerabilities}")
        print(f"📊 ОБЩАЯ ЭФФЕКТИВНОСТЬ: {total_vulnerabilities/total_strategies*100:.1f}%" if total_strategies > 0 else "📊 ОБЩАЯ ЭФФЕКТИВНОСТЬ: 0%")
        print(f"🚀 СКОРОСТЬ: {total_vulnerabilities/(campaign_duration/3600):.1f} уязвимостей/час")
        
        # Оценка потенциальной прибыли
        conservative_value = total_vulnerabilities * 10000  # $10K за уязвимость
        optimistic_value = total_vulnerabilities * 50000   # $50K за уязвимость
        
        print(f"\n💰 ПОТЕНЦИАЛЬНАЯ ПРИБЫЛЬ:")
        print(f"   💵 Консервативная: ${conservative_value:,}")
        print(f"   🚀 Оптимистичная: ${optimistic_value:,}")
        
        # Финальный отчет
        final_report = {
            'campaign_summary': {
                'start_time': datetime.fromtimestamp(campaign_start).isoformat(),
                'end_time': datetime.now().isoformat(),
                'duration_minutes': campaign_duration / 60,
                'cycles_completed': cycles_completed
            },
            'total_statistics': {
                'total_vulnerabilities': total_vulnerabilities,
                'total_targets': total_targets,
                'total_strategies': total_strategies,
                'overall_efficiency': total_vulnerabilities/total_strategies if total_strategies > 0 else 0,
                'vulnerabilities_per_hour': total_vulnerabilities/(campaign_duration/3600) if campaign_duration > 0 else 0
            },
            'financial_projection': {
                'conservative_value_usd': conservative_value,
                'optimistic_value_usd': optimistic_value,
                'avg_value_per_vulnerability': (conservative_value + optimistic_value) / 2 / total_vulnerabilities if total_vulnerabilities > 0 else 0
            },
            'recommendations': [
                f"Найдено {total_vulnerabilities} уязвимостей - начните подготовку отчетов",
                f"Эффективность {total_vulnerabilities/total_strategies*100:.1f}% - отличный результат" if total_strategies > 0 else "Требуется анализ эффективности",
                f"Скорость {total_vulnerabilities/(campaign_duration/3600):.1f} уязвимостей/час - масштабируйте систему" if campaign_duration > 0 else "Анализ скорости недоступен",
                "Рассмотрите непрерывную работу 24/7 для максимального покрытия"
            ]
        }
        
        final_report_filename = f"massive_campaign_final_report_{int(time.time())}.json"
        with open(final_report_filename, 'w', encoding='utf-8') as f:
            json.dump(final_report, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n💾 ФИНАЛЬНЫЙ ОТЧЕТ: {final_report_filename}")
        
        # Рекомендации
        print(f"\n💡 РЕКОМЕНДАЦИИ:")
        for rec in final_report['recommendations']:
            print(f"   {rec}")
        
        print(f"\n🚀 СИСТЕМА ГОТОВА К ПОЛНОМАСШТАБНОМУ РАЗВЕРТЫВАНИЮ!")
        
        return final_report
        
    except Exception as e:
        logger.error(f"❌ Критическая ошибка кампании: {e}")
        raise

async def main():
    """Главная функция"""
    await run_massive_hunting_campaign()

if __name__ == "__main__":
    asyncio.run(main())
