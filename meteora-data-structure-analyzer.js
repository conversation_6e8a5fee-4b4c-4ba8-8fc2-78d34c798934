#!/usr/bin/env node

/**
 * 🔥 METEORA DLMM DATA STRUCTURE ANALYZER
 * АНАЛИЗИРУЕТ РЕАЛЬНУЮ ТРАНЗАКЦИЮ И СОЗДАЕТ ПРАВИЛЬНУЮ СТРУКТУРУ ДАННЫХ
 */

const fs = require('fs');
const { BN } = require('@coral-xyz/anchor');

console.log('🔥 METEORA DLMM DATA STRUCTURE ANALYZER');
console.log('📋 Анализируем реальную транзакцию для создания правильной структуры данных...\n');

// 🔥 РЕАЛЬНАЯ ТРАНЗАКЦИЯ METEORA ADDLIQUIDITYBYSTATEGY2
const realTransaction = {
    signature: "x6eSZaFrt79QZfj1fddDLnfDbb4UnnPA9kus4hQTSC4Epz66M4yK9pCv68pVzgLE7QLzvnCBAiLC5rK8esXFkJP",
    instruction: "addLiquidityByStrategy2",
    discriminator: [3, 221, 149, 218, 111, 141, 118, 213],
    
    // ИЗ РЕАЛЬНОЙ ТРАНЗАКЦИИ
    data: {
        liquidityParameter: {
            type: "LiquidityParameterByStrategy",
            data: {
                amountX: "0", // SPL Token amount
                amountY: "***********", // WSOL amount  
                activeId: "-4361", // Active bin ID
                maxActiveBinSlippage: "...", // Max slippage
                strategyParameters: "..." // Strategy params
            }
        },
        remainingAccountsInfo: {
            type: "RemainingAccountsInfo",
            data: {
                slices: "..." // Account slices
            }
        }
    }
};

console.log('🔥 АНАЛИЗ РЕАЛЬНОЙ ТРАНЗАКЦИИ:');
console.log(`   Signature: ${realTransaction.signature}`);
console.log(`   Instruction: ${realTransaction.instruction}`);
console.log(`   Discriminator: [${realTransaction.discriminator.join(', ')}]`);
console.log('');

// 🔥 СОЗДАЕМ ПРАВИЛЬНУЮ СТРУКТУРУ ДАННЫХ ДЛЯ ADDLIQUIDITYBYSTATEGY2
function createAddLiquidityByStrategy2Data(amountX, amountY, activeId = -4361, maxSlippage = 1000) {
    console.log('🔧 СОЗДАНИЕ ПРАВИЛЬНОЙ СТРУКТУРЫ ДАННЫХ:');
    console.log(`   Amount X: ${amountX}`);
    console.log(`   Amount Y: ${amountY}`);
    console.log(`   Active ID: ${activeId}`);
    console.log(`   Max Slippage: ${maxSlippage}`);
    
    // 🔥 СТРУКТУРА ДЛЯ ADDLIQUIDITYBYSTATEGY2 (УПРОЩЕННАЯ)
    const data = Buffer.alloc(64); // Увеличиваем размер буфера
    let offset = 0;
    
    // 1. Discriminator (8 bytes)
    const discriminator = [3, 221, 149, 218, 111, 141, 118, 213];
    discriminator.forEach(byte => {
        data.writeUInt8(byte, offset);
        offset += 1;
    });
    
    // 2. Amount X (8 bytes, little-endian)
    const amountXBN = new BN(amountX);
    const amountXBytes = amountXBN.toBuffer('le', 8);
    amountXBytes.copy(data, offset);
    offset += 8;
    
    // 3. Amount Y (8 bytes, little-endian)  
    const amountYBN = new BN(amountY);
    const amountYBytes = amountYBN.toBuffer('le', 8);
    amountYBytes.copy(data, offset);
    offset += 8;
    
    // 4. Active ID (4 bytes, signed integer, little-endian)
    data.writeInt32LE(activeId, offset);
    offset += 4;
    
    // 5. Max Active Bin Slippage (2 bytes, little-endian)
    data.writeUInt16LE(maxSlippage, offset);
    offset += 2;
    
    // 6. Strategy Parameters (упрощенные - 0 для начала)
    // Можно добавить больше параметров по необходимости
    data.writeUInt32LE(0, offset); // Strategy type
    offset += 4;
    
    // 7. Remaining Accounts Info (упрощенные)
    data.writeUInt8(0, offset); // Slices count
    offset += 1;
    
    console.log(`✅ Структура данных создана, размер: ${offset} байт`);
    return data.slice(0, offset);
}

// 🔥 ТЕСТИРУЕМ СОЗДАНИЕ СТРУКТУРЫ
console.log('\n🧪 ТЕСТИРОВАНИЕ СОЗДАНИЯ СТРУКТУРЫ ДАННЫХ:\n');

// Тест 1: Добавление только WSOL (как в реальной транзакции)
const wsolOnlyData = createAddLiquidityByStrategy2Data(0, "***********", -4361, 1000);
console.log('📊 WSOL Only Data:');
console.log(`   Hex: ${wsolOnlyData.toString('hex')}`);
console.log(`   Array: [${Array.from(wsolOnlyData).join(', ')}]`);
console.log('');

// Тест 2: Добавление USDC и SOL (для нашего случая)
const usdcAmount = 1000000 * 1e6; // 1M USDC
const solAmount = 5500 * 1e9; // 5500 SOL
const dualTokenData = createAddLiquidityByStrategy2Data(usdcAmount, solAmount, -4361, 1000);
console.log('📊 Dual Token Data:');
console.log(`   Hex: ${dualTokenData.toString('hex')}`);
console.log(`   Array: [${Array.from(dualTokenData).join(', ')}]`);
console.log('');

// 🔥 СОЗДАЕМ JAVASCRIPT КОД ДЛЯ ЗАМЕНЫ
console.log('🔥 JAVASCRIPT КОД ДЛЯ ЗАМЕНЫ В НАШЕМ ПРОЕКТЕ:\n');

console.log('// ✅ ПРАВИЛЬНАЯ СТРУКТУРА ДАННЫХ ДЛЯ ADDLIQUIDITYBYSTATEGY2');
console.log('function createAddLiquidityByStrategy2Data(amountX, amountY, activeId = -4361, maxSlippage = 1000) {');
console.log('    const data = Buffer.alloc(64);');
console.log('    let offset = 0;');
console.log('    ');
console.log('    // 1. Discriminator');
console.log('    const discriminator = [3, 221, 149, 218, 111, 141, 118, 213];');
console.log('    discriminator.forEach(byte => {');
console.log('        data.writeUInt8(byte, offset);');
console.log('        offset += 1;');
console.log('    });');
console.log('    ');
console.log('    // 2. Amount X (8 bytes)');
console.log('    const amountXBN = new BN(amountX);');
console.log('    const amountXBytes = amountXBN.toBuffer("le", 8);');
console.log('    amountXBytes.copy(data, offset);');
console.log('    offset += 8;');
console.log('    ');
console.log('    // 3. Amount Y (8 bytes)');
console.log('    const amountYBN = new BN(amountY);');
console.log('    const amountYBytes = amountYBN.toBuffer("le", 8);');
console.log('    amountYBytes.copy(data, offset);');
console.log('    offset += 8;');
console.log('    ');
console.log('    // 4. Active ID (4 bytes)');
console.log('    data.writeInt32LE(activeId, offset);');
console.log('    offset += 4;');
console.log('    ');
console.log('    // 5. Max Slippage (2 bytes)');
console.log('    data.writeUInt16LE(maxSlippage, offset);');
console.log('    offset += 2;');
console.log('    ');
console.log('    // 6. Strategy Parameters (4 bytes)');
console.log('    data.writeUInt32LE(0, offset);');
console.log('    offset += 4;');
console.log('    ');
console.log('    // 7. Remaining Accounts (1 byte)');
console.log('    data.writeUInt8(0, offset);');
console.log('    offset += 1;');
console.log('    ');
console.log('    return data.slice(0, offset);');
console.log('}');

// 🔥 СОХРАНЯЕМ РЕЗУЛЬТАТЫ
const results = {
    timestamp: new Date().toISOString(),
    realTransaction: realTransaction,
    testData: {
        wsolOnly: {
            hex: wsolOnlyData.toString('hex'),
            array: Array.from(wsolOnlyData)
        },
        dualToken: {
            hex: dualTokenData.toString('hex'),
            array: Array.from(dualTokenData)
        }
    }
};

fs.writeFileSync('meteora-data-structure.json', JSON.stringify(results, null, 2));

console.log('\n✅ АНАЛИЗ ЗАВЕРШЕН!');
console.log('📁 Результаты сохранены в meteora-data-structure.json');
console.log('🔥 ГОТОВО! ТЕПЕРЬ МОЖНО ЗАМЕНИТЬ СТРУКТУРУ ДАННЫХ В КОДЕ!');
