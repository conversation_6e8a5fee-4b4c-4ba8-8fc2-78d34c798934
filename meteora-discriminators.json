{"timestamp": "2025-07-18T13:27:04.331Z", "program_id": "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo", "discriminators": {"add_liquidity": [181, 157, 89, 67, 143, 182, 52, 72], "add_liquidity_by_weight": [28, 140, 238, 99, 231, 162, 21, 149], "add_liquidity_by_strategy": [7, 3, 150, 127, 148, 40, 61, 200], "add_liquidity_by_strategy2": [3, 221, 149, 218, 111, 141, 118, 213], "add_liquidity_one_side": [94, 155, 103, 151, 70, 95, 220, 165], "remove_liquidity": [80, 85, 209, 72, 24, 206, 177, 108], "remove_liquidity_by_range": [26, 82, 102, 152, 240, 74, 105, 26], "remove_liquidity_by_range2": [204, 2, 195, 145, 53, 145, 145, 205], "swap": [248, 198, 158, 145, 225, 117, 135, 200], "swap2": [65, 75, 63, 76, 235, 91, 91, 136], "swap_exact_out": [250, 73, 101, 33, 38, 207, 75, 184], "swap_with_price_impact": [56, 173, 230, 208, 173, 228, 156, 205], "initialize_position": [219, 192, 234, 71, 190, 191, 102, 80], "update_position_operator": [202, 184, 103, 143, 180, 191, 116, 217], "close_position": [123, 134, 81, 0, 49, 68, 98, 98], "initialize_lb_pair": [45, 154, 237, 210, 221, 15, 166, 92], "initialize_bin_array": [35, 86, 19, 185, 78, 212, 75, 211], "increase_oracle_length": [190, 61, 125, 87, 103, 79, 158, 173], "claim_reward": [149, 95, 181, 242, 94, 90, 158, 162], "claim_fee": [169, 32, 79, 137, 136, 232, 70, 137], "claim_fee2": [112, 191, 101, 171, 28, 144, 127, 187], "fund_reward": [188, 50, 249, 165, 93, 151, 38, 63], "initialize_reward": [95, 135, 192, 196, 242, 129, 230, 68], "set_activation_point": [91, 249, 15, 165, 26, 129, 254, 125], "toggle_pair_status": [61, 115, 52, 23, 46, 13, 31, 144], "update_fee_parameters": [128, 128, 208, 91, 246, 53, 31, 176], "update_whitelisted_wallet": [4, 105, 92, 167, 132, 28, 9, 90]}, "main_discriminators": {"add_liquidity": [181, 157, 89, 67, 143, 182, 52, 72], "add_liquidity_by_strategy2": [3, 221, 149, 218, 111, 141, 118, 213], "add_liquidity_one_side": [94, 155, 103, 151, 70, 95, 220, 165], "remove_liquidity": [80, 85, 209, 72, 24, 206, 177, 108], "remove_liquidity_by_range2": [204, 2, 195, 145, 53, 145, 145, 205], "swap": [248, 198, 158, 145, 225, 117, 135, 200], "swap2": [65, 75, 63, 76, 235, 91, 91, 136], "claim_fee2": [112, 191, 101, 171, 28, 144, 127, 187]}}