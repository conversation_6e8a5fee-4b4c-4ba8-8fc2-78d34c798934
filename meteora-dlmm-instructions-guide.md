# 📚 **METEORA DLMM ИНСТРУКЦИИ - ОФИЦИАЛЬНОЕ РУКОВОДСТВО**

## 🔍 **ИЗУЧЕННАЯ ОФИЦИАЛЬНАЯ ДОКУМЕНТАЦИЯ:**

### **📖 ИСТОЧНИКИ:**
- **NPM Package**: `@meteora-ag/dlmm` v1.5.4
- **GitHub SDK**: https://github.com/MeteoraAg/dlmm-sdk
- **Официальная документация**: https://docs.meteora.ag/
- **Program ID**: `LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo`

---

## 🎯 **ПРАВИЛЬНЫЕ ИНСТРУКЦИИ ДЛЯ ДОБАВЛЕНИЯ ЛИКВИДНОСТИ:**

### **1️⃣ СОЗДАНИЕ ПОЗИЦИИ (`initializePosition`)**

```javascript
{
    programId: 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo',
    instruction: 'initializePosition',
    accounts: [
        { pubkey: userWallet, isSigner: true, isWritable: true },
        { pubkey: positionKeypair, isSigner: true, isWritable: true },
        { pubkey: lbPair, isSigner: false, isWritable: false },
        { pubkey: SystemProgram.programId, isSigner: false, isWritable: false }
    ],
    data: {
        lowerBinId: minBinId,
        width: binsCount
    }
}
```

### **2️⃣ ДОБАВЛЕНИЕ ЛИКВИДНОСТИ (`addLiquidityByStrategy`)**

```javascript
{
    programId: 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo',
    instruction: 'addLiquidityByStrategy',
    accounts: [
        { pubkey: userWallet, isSigner: true, isWritable: true },
        { pubkey: position, isSigner: false, isWritable: true },
        { pubkey: lbPair, isSigner: false, isWritable: true },
        { pubkey: binArrayLower, isSigner: false, isWritable: true },
        { pubkey: binArrayUpper, isSigner: false, isWritable: true },
        { pubkey: userTokenX, isSigner: false, isWritable: true },
        { pubkey: userTokenY, isSigner: false, isWritable: true },
        { pubkey: reserveX, isSigner: false, isWritable: true },
        { pubkey: reserveY, isSigner: false, isWritable: true },
        { pubkey: TokenProgram.programId, isSigner: false, isWritable: false },
        { pubkey: eventAuthority, isSigner: false, isWritable: false },
        { pubkey: program, isSigner: false, isWritable: false }
    ],
    data: {
        liquidityParameter: {
            amountX: totalXAmount,
            amountY: totalYAmount,
            binLiquidityDist: [
                { binId: -1, distributionX: amount1, distributionY: 0 },
                { binId: -2, distributionX: amount2, distributionY: 0 },
                // ... остальные bins
            ]
        }
    }
}
```

---

## 🏊 **ПРАВИЛЬНЫЕ ИНСТРУКЦИИ ДЛЯ ВЫВОДА ЛИКВИДНОСТИ:**

### **1️⃣ ВЫВОД ЛИКВИДНОСТИ (`removeLiquidity`)**

```javascript
{
    programId: 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo',
    instruction: 'removeLiquidity',
    accounts: [
        { pubkey: userWallet, isSigner: true, isWritable: true },
        { pubkey: position, isSigner: false, isWritable: true },
        { pubkey: lbPair, isSigner: false, isWritable: true },
        { pubkey: binArrayLower, isSigner: false, isWritable: true },
        { pubkey: binArrayUpper, isSigner: false, isWritable: true },
        { pubkey: userTokenX, isSigner: false, isWritable: true },
        { pubkey: userTokenY, isSigner: false, isWritable: true },
        { pubkey: reserveX, isSigner: false, isWritable: true },
        { pubkey: reserveY, isSigner: false, isWritable: true },
        { pubkey: TokenProgram.programId, isSigner: false, isWritable: false },
        { pubkey: eventAuthority, isSigner: false, isWritable: false },
        { pubkey: program, isSigner: false, isWritable: false }
    ],
    data: {
        binLiquidityRemoval: [
            { binId: -1, bpsToRemove: 10000 }, // 100% = 10000 BPS
            { binId: -2, bpsToRemove: 10000 },
            // ... остальные bins
        ]
    }
}
```

### **2️⃣ ЗАКРЫТИЕ ПОЗИЦИИ (`closePosition`)**

```javascript
{
    programId: 'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo',
    instruction: 'closePosition',
    accounts: [
        { pubkey: userWallet, isSigner: true, isWritable: true },
        { pubkey: position, isSigner: false, isWritable: true },
        { pubkey: lbPair, isSigner: false, isWritable: false },
        { pubkey: SystemProgram.programId, isSigner: false, isWritable: false }
    ],
    data: {}
}
```

---

## 🧮 **РАСЧЕТ BIN ARRAYS:**

### **📊 ФОРМУЛА BIN ARRAYS:**
```javascript
// В DLMM каждый bin array содержит 70 bins
const binArrayIndex = Math.floor(binId / 70);

// Для bins [-8, -7, -6, -5, -4, -3, -2, -1]:
// Все попадают в bin array с индексом -1
```

### **🔑 ГЕНЕРАЦИЯ PDA АДРЕСОВ:**
```javascript
// Bin Array PDA
const [binArrayPda] = PublicKey.findProgramAddressSync(
    [
        Buffer.from("bin_array"),
        lbPair.toBuffer(),
        Buffer.from(binArrayIndex.toString())
    ],
    METEORA_DLMM_PROGRAM_ID
);

// Reserve X PDA
const [reserveXPda] = PublicKey.findProgramAddressSync(
    [
        lbPair.toBuffer(),
        tokenXMint.toBuffer()
    ],
    METEORA_DLMM_PROGRAM_ID
);
```

---

## ⚡ **СТРАТЕГИИ ДОБАВЛЕНИЯ ЛИКВИДНОСТИ:**

### **🎯 SPOT STRATEGY (Равномерное распределение):**
```javascript
const strategy = {
    strategyType: StrategyType.Spot,
    minBinId: activeBinId - 10,
    maxBinId: activeBinId + 10
};
```

### **📈 CURVE STRATEGY (Концентрированная):**
```javascript
const strategy = {
    strategyType: StrategyType.Curve,
    minBinId: activeBinId - 5,
    maxBinId: activeBinId + 5
};
```

### **💰 BID-ASK STRATEGY (Односторонняя):**
```javascript
const strategy = {
    strategyType: StrategyType.BidAsk,
    minBinId: activeBinId,
    maxBinId: activeBinId + 20
};
```

---

## 🔧 **HELPER ФУНКЦИИ ИЗ SDK:**

### **📊 АВТОЗАПОЛНЕНИЕ КОЛИЧЕСТВА:**
```javascript
import { autoFillYByStrategy, StrategyType } from '@meteora-ag/dlmm';

const totalYAmount = autoFillYByStrategy(
    activeBin.binId,
    lbPair.binStep,
    totalXAmount,
    activeBin.xAmount,
    activeBin.yAmount,
    minBinId,
    maxBinId,
    StrategyType.Spot
);
```

### **🎯 ПОЛУЧЕНИЕ АКТИВНОГО BIN:**
```javascript
const activeBin = await dlmmPool.getActiveBin();
const activeBinPrice = dlmmPool.fromPricePerLamport(Number(activeBin.price));
```

---

## 🚨 **КРИТИЧЕСКИЕ МОМЕНТЫ:**

### **❌ ЧАСТЫЕ ОШИБКИ:**
1. **Неправильная инструкция**: `addLiquidityOneSide` не существует!
2. **Неправильные bin arrays**: Нужно рассчитывать через `Math.floor(binId / 70)`
3. **Отсутствие создания позиции**: Сначала `initializePosition`, потом `addLiquidity`
4. **Неправильные PDA**: Все адреса должны быть рассчитаны как PDA

### **✅ ПРАВИЛЬНЫЙ ПОРЯДОК:**
1. `initializePosition` - создание позиции
2. `addLiquidityByStrategy` - добавление ликвидности
3. ... торговля ...
4. `removeLiquidity` - вывод ликвидности
5. `closePosition` - закрытие позиции

---

## 📝 **ПРИМЕР ПОЛНОЙ ТРАНЗАКЦИИ:**

```javascript
// 1. Создание позиции
const initPositionIx = await dlmmPool.initializePositionAndAddLiquidityByStrategy({
    positionPubKey: newPosition.publicKey,
    user: user.publicKey,
    totalXAmount: new BN(1400000 * 1000000), // $1.4M USDC
    totalYAmount: new BN(0), // Односторонняя ликвидность
    strategy: {
        maxBinId: -1,
        minBinId: -8,
        strategyType: StrategyType.Spot
    }
});

// 2. Вывод ликвидности
const removeLiquidityIx = await dlmmPool.removeLiquidity({
    position: newPosition.publicKey,
    user: user.publicKey,
    fromBinId: -8,
    toBinId: -1,
    liquiditiesBpsToRemove: new Array(8).fill(new BN(10000)), // 100%
    shouldClaimAndClose: true
});
```

---

## 🎯 **СЛЕДУЮЩИЕ ШАГИ:**

1. **Обновить инструкции** в `solana-transaction-builder.js`
2. **Добавить расчет PDA** адресов
3. **Протестировать** с реальными данными
4. **Добавить обработку ошибок**
