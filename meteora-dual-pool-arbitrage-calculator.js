/**
 * 🎯 METEORA DUAL POOL ARBITRAGE CALCULATOR
 * 
 * Расчет арбитража между двумя пулами с собственной ликвидностью
 * Арбитражная разница: 0.05%
 * Объем торговли: $1M
 * У вас есть ликвидность в ОБОИХ пулах
 */

class MeteoraDualPoolArbitrageCalculator {
    constructor() {
        // Параметры арбитража
        this.ARBITRAGE_PARAMS = {
            price_difference_percent: 0.05, // 0.05% разница в цене
            trading_volume: 1000000, // $1M торговли
            base_price: 175 // $175 базовая цена SOL
        };
        
        // Пул 1 (дешевый - покупаем здесь)
        this.POOL_1_CHEAP = {
            name: 'Pool 1 (дешевый)',
            price: 175 * (1 - 0.0005), // $174.9125 (на 0.05% дешевле)
            your_liquidity_usdc: 1500000, // Ваша ликвидность $1.5M USDC
            your_liquidity_sol: 8571, // ~8571 SOL
            total_liquidity_usdc: 2000000, // Общая ликвидность $2M USDC
            total_liquidity_sol: 11428, // ~11428 SOL
            fee_rate: 0.0044 // 0.44% комиссия
        };
        
        // Пул 2 (дорогой - продаем здесь)
        this.POOL_2_EXPENSIVE = {
            name: 'Pool 2 (дорогой)',
            price: 175 * (1 + 0.0005), // $175.0875 (на 0.05% дороже)
            your_liquidity_usdc: 1200000, // Ваша ликвидность $1.2M USDC
            your_liquidity_sol: 6857, // ~6857 SOL
            total_liquidity_usdc: 1800000, // Общая ликвидность $1.8M USDC
            total_liquidity_sol: 10286, // ~10286 SOL
            fee_rate: 0.0044 // 0.44% комиссия
        };
        
        console.log('🎯 MeteoraDualPoolArbitrageCalculator инициализирован');
        console.log(`📊 Арбитражная разница: ${this.ARBITRAGE_PARAMS.price_difference_percent}%`);
        console.log(`💰 Объем торговли: $${this.ARBITRAGE_PARAMS.trading_volume.toLocaleString()}`);
    }

    /**
     * 📊 АНАЛИЗ ПУЛОВ И ЛИКВИДНОСТИ
     */
    analyzePools() {
        console.log('\n📊 АНАЛИЗ ПУЛОВ И ВАШЕЙ ЛИКВИДНОСТИ:');
        console.log('=' .repeat(70));
        
        const pool1 = this.POOL_1_CHEAP;
        const pool2 = this.POOL_2_EXPENSIVE;
        
        // Pool 1 анализ
        const pool1ShareUsdc = (pool1.your_liquidity_usdc / pool1.total_liquidity_usdc) * 100;
        const pool1ShareSol = (pool1.your_liquidity_sol / pool1.total_liquidity_sol) * 100;
        
        console.log(`🔵 ${pool1.name}:`);
        console.log(`   Цена SOL: $${pool1.price.toFixed(4)}`);
        console.log(`   Ваша доля USDC: ${pool1ShareUsdc.toFixed(2)}%`);
        console.log(`   Ваша доля SOL: ${pool1ShareSol.toFixed(2)}%`);
        console.log(`   Средняя доля: ${((pool1ShareUsdc + pool1ShareSol) / 2).toFixed(2)}%`);
        
        // Pool 2 анализ
        const pool2ShareUsdc = (pool2.your_liquidity_usdc / pool2.total_liquidity_usdc) * 100;
        const pool2ShareSol = (pool2.your_liquidity_sol / pool2.total_liquidity_sol) * 100;
        
        console.log(`\n🔴 ${pool2.name}:`);
        console.log(`   Цена SOL: $${pool2.price.toFixed(4)}`);
        console.log(`   Ваша доля USDC: ${pool2ShareUsdc.toFixed(2)}%`);
        console.log(`   Ваша доля SOL: ${pool2ShareSol.toFixed(2)}%`);
        console.log(`   Средняя доля: ${((pool2ShareUsdc + pool2ShareSol) / 2).toFixed(2)}%`);
        
        // Арбитражная возможность
        const priceDiff = pool2.price - pool1.price;
        const priceDiffPercent = (priceDiff / pool1.price) * 100;
        
        console.log(`\n💎 АРБИТРАЖНАЯ ВОЗМОЖНОСТЬ:`);
        console.log(`   Разница в цене: $${priceDiff.toFixed(4)} (${priceDiffPercent.toFixed(3)}%)`);
        console.log(`   Потенциальная прибыль: $${(this.ARBITRAGE_PARAMS.trading_volume * priceDiffPercent / 100).toFixed(2)}`);
        
        return {
            pool1Shares: { usdc: pool1ShareUsdc, sol: pool1ShareSol, average: (pool1ShareUsdc + pool1ShareSol) / 2 },
            pool2Shares: { usdc: pool2ShareUsdc, sol: pool2ShareSol, average: (pool2ShareUsdc + pool2ShareSol) / 2 },
            priceDiff,
            priceDiffPercent
        };
    }

    /**
     * 🔄 РАСЧЕТ АРБИТРАЖНОЙ ОПЕРАЦИИ
     */
    calculateArbitrage() {
        console.log('\n🔄 РАСЧЕТ АРБИТРАЖНОЙ ОПЕРАЦИИ:');
        console.log('=' .repeat(70));
        
        const volume = this.ARBITRAGE_PARAMS.trading_volume;
        const pool1 = this.POOL_1_CHEAP;
        const pool2 = this.POOL_2_EXPENSIVE;
        
        // Шаг 1: Покупка SOL в дешевом пуле
        const solToBuy = volume / pool1.price;
        console.log(`\n1️⃣ ПОКУПКА в Pool 1 (дешевый):`);
        console.log(`   Тратим: $${volume.toLocaleString()}`);
        console.log(`   Покупаем: ${solToBuy.toFixed(2)} SOL`);
        console.log(`   Цена: $${pool1.price.toFixed(4)} за SOL`);
        
        // Комиссии Pool 1
        const pool1Fees = volume * pool1.fee_rate;
        const pool1YourShare = (pool1.your_liquidity_usdc / pool1.total_liquidity_usdc + 
                               pool1.your_liquidity_sol / pool1.total_liquidity_sol) / 2;
        const pool1YourFeeIncome = pool1Fees * pool1YourShare;
        
        console.log(`   Комиссии: $${pool1Fees.toFixed(2)}`);
        console.log(`   Ваша доля комиссий: $${pool1YourFeeIncome.toFixed(2)} (${(pool1YourShare * 100).toFixed(2)}%)`);
        
        // Шаг 2: Продажа SOL в дорогом пуле
        const usdcFromSale = solToBuy * pool2.price;
        console.log(`\n2️⃣ ПРОДАЖА в Pool 2 (дорогой):`);
        console.log(`   Продаем: ${solToBuy.toFixed(2)} SOL`);
        console.log(`   Получаем: $${usdcFromSale.toLocaleString()}`);
        console.log(`   Цена: $${pool2.price.toFixed(4)} за SOL`);
        
        // Комиссии Pool 2
        const pool2Fees = usdcFromSale * pool2.fee_rate;
        const pool2YourShare = (pool2.your_liquidity_usdc / pool2.total_liquidity_usdc + 
                               pool2.your_liquidity_sol / pool2.total_liquidity_sol) / 2;
        const pool2YourFeeIncome = pool2Fees * pool2YourShare;
        
        console.log(`   Комиссии: $${pool2Fees.toFixed(2)}`);
        console.log(`   Ваша доля комиссий: $${pool2YourFeeIncome.toFixed(2)} (${(pool2YourShare * 100).toFixed(2)}%)`);
        
        return {
            grossProfit: usdcFromSale - volume,
            pool1FeeIncome: pool1YourFeeIncome,
            pool2FeeIncome: pool2YourFeeIncome,
            totalFeeIncome: pool1YourFeeIncome + pool2YourFeeIncome,
            totalFeesGenerated: pool1Fees + pool2Fees
        };
    }

    /**
     * 💰 ИТОГОВЫЙ РАСЧЕТ ПРИБЫЛИ
     */
    calculateTotalProfit() {
        console.log('\n💰 ИТОГОВЫЙ РАСЧЕТ ПРИБЫЛИ:');
        console.log('=' .repeat(50));
        
        const arbitrage = this.calculateArbitrage();
        const volume = this.ARBITRAGE_PARAMS.trading_volume;
        
        // Валовая прибыль от арбитража
        const grossArbitrageProfit = arbitrage.grossProfit;
        
        // Доходы от комиссий
        const totalFeeIncome = arbitrage.totalFeeIncome;
        
        // Расходы на комиссии (что вы платите как трейдер)
        const totalFeesYouPay = arbitrage.totalFeesGenerated;
        
        // Чистые расходы на комиссии
        const netFeeExpense = totalFeesYouPay - totalFeeIncome;
        
        // Итоговая прибыль
        const totalProfit = grossArbitrageProfit - netFeeExpense;
        
        console.log(`📈 Валовая прибыль от арбитража: $${grossArbitrageProfit.toFixed(2)}`);
        console.log(`💰 Доход от комиссий: $${totalFeeIncome.toFixed(2)}`);
        console.log(`💸 Расходы на комиссии: $${totalFeesYouPay.toFixed(2)}`);
        console.log(`🔄 Чистые расходы на комиссии: $${netFeeExpense.toFixed(2)}`);
        console.log(`🎯 ИТОГОВАЯ ПРИБЫЛЬ: $${totalProfit.toFixed(2)}`);
        
        // Анализ эффективности
        const profitPercent = (totalProfit / volume) * 100;
        console.log(`📊 Доходность: ${profitPercent.toFixed(4)}%`);
        
        return {
            grossArbitrageProfit,
            totalFeeIncome,
            netFeeExpense,
            totalProfit,
            profitPercent
        };
    }

    /**
     * 🧮 СРАВНЕНИЕ С ОБЫЧНЫМ АРБИТРАЖЕМ
     */
    compareWithRegularArbitrage() {
        console.log('\n🧮 СРАВНЕНИЕ С ОБЫЧНЫМ АРБИТРАЖЕМ:');
        console.log('=' .repeat(60));
        
        const volume = this.ARBITRAGE_PARAMS.trading_volume;
        const priceDiff = this.POOL_2_EXPENSIVE.price - this.POOL_1_CHEAP.price;
        const grossProfit = (priceDiff / this.POOL_1_CHEAP.price) * volume;
        
        // Обычный арбитраж (без собственной ликвидности)
        const regularFees = volume * 0.0044 * 2; // Комиссии в обоих пулах
        const regularNetProfit = grossProfit - regularFees;
        
        // Ваш арбитраж (с собственной ликвидностью)
        const yourResult = this.calculateTotalProfit();
        
        console.log(`🔸 ОБЫЧНЫЙ АРБИТРАЖ (без своей ликвидности):`);
        console.log(`   Валовая прибыль: $${grossProfit.toFixed(2)}`);
        console.log(`   Комиссии: $${regularFees.toFixed(2)}`);
        console.log(`   Чистая прибыль: $${regularNetProfit.toFixed(2)}`);
        
        console.log(`\n🔹 ВАШ АРБИТРАЖ (с собственной ликвидностью):`);
        console.log(`   Валовая прибыль: $${yourResult.grossArbitrageProfit.toFixed(2)}`);
        console.log(`   Чистые расходы на комиссии: $${yourResult.netFeeExpense.toFixed(2)}`);
        console.log(`   Чистая прибыль: $${yourResult.totalProfit.toFixed(2)}`);
        
        const advantage = yourResult.totalProfit - regularNetProfit;
        console.log(`\n💎 ВАШЕ ПРЕИМУЩЕСТВО: $${advantage.toFixed(2)}`);
        console.log(`   Это ${((advantage / regularNetProfit) * 100).toFixed(2)}% больше прибыли!`);
        
        return {
            regularProfit: regularNetProfit,
            yourProfit: yourResult.totalProfit,
            advantage: advantage
        };
    }
}

// Запуск расчетов
if (require.main === module) {
    const calculator = new MeteoraDualPoolArbitrageCalculator();
    
    // Анализ пулов
    const poolAnalysis = calculator.analyzePools();
    
    // Расчет арбитража
    const profitAnalysis = calculator.calculateTotalProfit();
    
    // Сравнение с обычным арбитражем
    const comparison = calculator.compareWithRegularArbitrage();
    
    console.log('\n🎯 ФИНАЛЬНЫЕ ВЫВОДЫ:');
    console.log('=' .repeat(50));
    console.log(`✅ При арбитраже $1M с разницей 0.05%:`);
    console.log(`   Ваша прибыль: $${profitAnalysis.totalProfit.toFixed(2)}`);
    console.log(`   Доходность: ${profitAnalysis.profitPercent.toFixed(4)}%`);
    console.log(`🔥 Преимущество собственной ликвидности: $${comparison.advantage.toFixed(2)}`);
}
