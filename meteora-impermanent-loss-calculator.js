/**
 * 🎯 METEORA IMPERMANENT LOSS CALCULATOR
 * 
 * Рассчитывает как ваша собственная торговля влияет на стоимость вашей ликвидности
 * КЛЮЧЕВОЙ ВОПРОС: Что происходит с вашим $1M ликвидности когда вы торгуете на $1M?
 */

class MeteoraImpermanentLossCalculator {
    constructor() {
        // Начальные условия
        this.INITIAL_CONDITIONS = {
            sol_price: 175, // $175 за SOL
            your_liquidity_usdc: 1000000, // Ваша ликвидность $1M
            your_liquidity_sol: 1000000 / 175, // ~5714 SOL эквивалент
            pool_total_usdc: 3000000, // Общая ликвидность пула $3M USDC
            pool_total_sol: 3000000 / 175, // ~17143 SOL эквивалент
            trading_volume: 1000000 // Объем торговли $1M
        };
        
        console.log('🎯 MeteoraImpermanentLossCalculator инициализирован');
        console.log(`📊 Начальная цена SOL: $${this.INITIAL_CONDITIONS.sol_price}`);
        console.log(`💧 Ваша ликвидность: $${this.INITIAL_CONDITIONS.your_liquidity_usdc.toLocaleString()}`);
    }

    /**
     * 📈 РАСЧЕТ ИЗМЕНЕНИЯ ЦЕНЫ ПРИ ТОРГОВЛЕ
     */
    calculatePriceImpact(tradingAmount, poolLiquidity, direction) {
        // Упрощенная модель AMM: x * y = k
        // При покупке SOL за USDC цена SOL растет
        // При продаже SOL за USDC цена SOL падает
        
        const impactPercent = (tradingAmount / poolLiquidity) * 100;
        
        // В DLMM impact меньше из-за концентрированной ликвидности
        const dlmmImpactReduction = 0.6; // 60% от обычного AMM impact
        const actualImpact = impactPercent * dlmmImpactReduction;
        
        let priceChange;
        if (direction === 'buy') {
            // Покупка SOL - цена растет
            priceChange = actualImpact;
        } else {
            // Продажа SOL - цена падает  
            priceChange = -actualImpact;
        }
        
        const newPrice = this.INITIAL_CONDITIONS.sol_price * (1 + priceChange / 100);
        
        return {
            impactPercent: actualImpact,
            priceChange: priceChange,
            oldPrice: this.INITIAL_CONDITIONS.sol_price,
            newPrice: newPrice,
            priceDifference: newPrice - this.INITIAL_CONDITIONS.sol_price
        };
    }

    /**
     * 💧 РАСЧЕТ ИЗМЕНЕНИЯ СТОИМОСТИ ЛИКВИДНОСТИ
     */
    calculateLiquidityValueChange(newSolPrice) {
        const initial = this.INITIAL_CONDITIONS;
        
        // Начальная стоимость вашей ликвидности
        const initialValue = initial.your_liquidity_usdc + (initial.your_liquidity_sol * initial.sol_price);
        
        // В AMM при изменении цены соотношение токенов меняется
        // Формула для constant product AMM: новое соотношение = sqrt(новая_цена / старая_цена)
        const priceRatio = newSolPrice / initial.sol_price;
        const liquidityRatio = Math.sqrt(priceRatio);
        
        // Новое количество токенов в вашей позиции
        const newSolAmount = initial.your_liquidity_sol / liquidityRatio;
        const newUsdcAmount = initial.your_liquidity_usdc * liquidityRatio;
        
        // Новая стоимость ликвидности
        const newValue = newUsdcAmount + (newSolAmount * newSolPrice);
        
        // Impermanent Loss
        const impermanentLoss = initialValue - newValue;
        const impermanentLossPercent = (impermanentLoss / initialValue) * 100;
        
        // Что было бы если просто держали токены (HODL)
        const hodlValue = initial.your_liquidity_usdc + (initial.your_liquidity_sol * newSolPrice);
        const hodlVsLp = hodlValue - newValue;
        
        return {
            initialValue,
            newValue,
            impermanentLoss,
            impermanentLossPercent,
            hodlValue,
            hodlVsLp,
            newSolAmount,
            newUsdcAmount,
            tokenChange: {
                solChange: newSolAmount - initial.your_liquidity_sol,
                usdcChange: newUsdcAmount - initial.your_liquidity_usdc
            }
        };
    }

    /**
     * 🎯 ПОЛНЫЙ АНАЛИЗ ВАШЕЙ СТРАТЕГИИ
     */
    analyzeYourStrategy() {
        console.log('\n🎯 АНАЛИЗ ВЛИЯНИЯ ВАШЕЙ ТОРГОВЛИ НА ЛИКВИДНОСТЬ:');
        console.log('=' .repeat(70));
        
        const initial = this.INITIAL_CONDITIONS;
        
        // Сценарий 1: Вы покупаете SOL за $1M (цена SOL растет)
        console.log('\n📈 СЦЕНАРИЙ 1: Покупка SOL за $1M');
        const buyImpact = this.calculatePriceImpact(
            initial.trading_volume, 
            initial.pool_total_usdc, 
            'buy'
        );
        
        console.log(`   Влияние на цену: +${buyImpact.priceChange.toFixed(2)}%`);
        console.log(`   Старая цена SOL: $${buyImpact.oldPrice}`);
        console.log(`   Новая цена SOL: $${buyImpact.newPrice.toFixed(2)}`);
        console.log(`   Рост цены: $${buyImpact.priceDifference.toFixed(2)}`);
        
        const buyLiquidityChange = this.calculateLiquidityValueChange(buyImpact.newPrice);
        
        console.log('\n💧 Влияние на вашу ликвидность:');
        console.log(`   Начальная стоимость: $${buyLiquidityChange.initialValue.toLocaleString()}`);
        console.log(`   Новая стоимость: $${buyLiquidityChange.newValue.toLocaleString()}`);
        console.log(`   Impermanent Loss: $${buyLiquidityChange.impermanentLoss.toFixed(2)} (${buyLiquidityChange.impermanentLossPercent.toFixed(3)}%)`);
        console.log(`   Новое количество SOL: ${buyLiquidityChange.newSolAmount.toFixed(0)} (${buyLiquidityChange.tokenChange.solChange.toFixed(0)})`);
        console.log(`   Новое количество USDC: $${buyLiquidityChange.newUsdcAmount.toLocaleString()} (${buyLiquidityChange.tokenChange.usdcChange > 0 ? '+' : ''}${buyLiquidityChange.tokenChange.usdcChange.toFixed(0)})`);
        
        // Сценарий 2: Вы продаете SOL за $1M (цена SOL падает)
        console.log('\n📉 СЦЕНАРИЙ 2: Продажа SOL за $1M');
        const sellImpact = this.calculatePriceImpact(
            initial.trading_volume, 
            initial.pool_total_usdc, 
            'sell'
        );
        
        console.log(`   Влияние на цену: ${sellImpact.priceChange.toFixed(2)}%`);
        console.log(`   Старая цена SOL: $${sellImpact.oldPrice}`);
        console.log(`   Новая цена SOL: $${sellImpact.newPrice.toFixed(2)}`);
        console.log(`   Падение цены: $${sellImpact.priceDifference.toFixed(2)}`);
        
        const sellLiquidityChange = this.calculateLiquidityValueChange(sellImpact.newPrice);
        
        console.log('\n💧 Влияние на вашу ликвидность:');
        console.log(`   Начальная стоимость: $${sellLiquidityChange.initialValue.toLocaleString()}`);
        console.log(`   Новая стоимость: $${sellLiquidityChange.newValue.toLocaleString()}`);
        console.log(`   Impermanent Loss: $${sellLiquidityChange.impermanentLoss.toFixed(2)} (${sellLiquidityChange.impermanentLossPercent.toFixed(3)}%)`);
        console.log(`   Новое количество SOL: ${sellLiquidityChange.newSolAmount.toFixed(0)} (${sellLiquidityChange.tokenChange.solChange > 0 ? '+' : ''}${sellLiquidityChange.tokenChange.solChange.toFixed(0)})`);
        console.log(`   Новое количество USDC: $${sellLiquidityChange.newUsdcAmount.toLocaleString()} (${sellLiquidityChange.tokenChange.usdcChange > 0 ? '+' : ''}${sellLiquidityChange.tokenChange.usdcChange.toFixed(0)})`);
        
        return {
            buyScenario: {
                priceImpact: buyImpact,
                liquidityChange: buyLiquidityChange
            },
            sellScenario: {
                priceImpact: sellImpact,
                liquidityChange: sellLiquidityChange
            }
        };
    }

    /**
     * 🧮 РАСЧЕТ ЧИСТОЙ ПРИБЫЛИ С УЧЕТОМ IMPERMANENT LOSS
     */
    calculateNetProfitWithIL(tradingProfit, impermanentLoss) {
        const netProfit = tradingProfit - Math.abs(impermanentLoss);
        
        console.log('\n🧮 ЧИСТАЯ ПРИБЫЛЬ С УЧЕТОМ IMPERMANENT LOSS:');
        console.log('=' .repeat(50));
        console.log(`   Прибыль от торговли: $${tradingProfit.toFixed(2)}`);
        console.log(`   Impermanent Loss: -$${Math.abs(impermanentLoss).toFixed(2)}`);
        console.log(`   ЧИСТАЯ ПРИБЫЛЬ: $${netProfit.toFixed(2)}`);
        
        if (netProfit > 0) {
            console.log(`   ✅ Стратегия ПРИБЫЛЬНА даже с учетом IL`);
        } else {
            console.log(`   ❌ Стратегия УБЫТОЧНА из-за IL`);
        }
        
        return netProfit;
    }
}

// Запуск анализа
if (require.main === module) {
    const calculator = new MeteoraImpermanentLossCalculator();
    
    // Анализ влияния торговли на ликвидность
    const analysis = calculator.analyzeYourStrategy();
    
    // Предположим, что от торговли вы получили $2,776 (из предыдущего расчета)
    const tradingProfit = 2776;
    
    // Расчет чистой прибыли для обоих сценариев
    console.log('\n🎯 ИТОГОВЫЙ АНАЛИЗ:');
    console.log('=' .repeat(70));
    
    calculator.calculateNetProfitWithIL(tradingProfit, analysis.buyScenario.liquidityChange.impermanentLoss);
    calculator.calculateNetProfitWithIL(tradingProfit, analysis.sellScenario.liquidityChange.impermanentLoss);
}
