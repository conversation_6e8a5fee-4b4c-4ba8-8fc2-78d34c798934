/**
 * 🎯 НОВАЯ СТРАТЕГИЯ METEORA АРБИТРАЖА
 * 
 * ОБНОВЛЕННАЯ ЛОГИКА:
 * 1. Flash Loan = 22% от ликвидности пула ($650K из $3M пула)
 * 2. Добавление ликвидности = 75% от займа ($500K)
 * 3. Арбитражная позиция = 25% от займа ($150K)
 * 4. Продажа в большом пуле ($7M)
 */

class MeteoraNewStrategyCalculator {
    constructor() {
        // Реальные данные пулов
        this.POOLS = {
            SMALL: {
                tvl: 3000000, // $3M базовый пул
                name: 'Meteora Small Pool'
            },
            LARGE: {
                tvl: 7000000, // $7M большой пул
                name: '<PERSON>eora Large Pool'
            }
        };
        
        this.FLASH_LOAN_FEE = 0.0009; // 0.09%
        this.METEORA_FEE = 0.0004; // 0.04% Meteora комиссия
        this.TRANSACTION_COST = 0.01; // $0.01 за транзакцию
        
        console.log('🎯 НОВАЯ СТРАТЕГИЯ MeteoraCalculator инициализирован');
        console.log(`   Маленький пул: $${this.POOLS.SMALL.tvl.toLocaleString()}`);
        console.log(`   Большой пул: $${this.POOLS.LARGE.tvl.toLocaleString()}`);
    }

    /**
     * 📈 РАСЧЕТ ВЛИЯНИЯ НА ЦЕНУ ОТ ДОБАВЛЕНИЯ ЛИКВИДНОСТИ
     */
    calculateLiquidityImpact(addedLiquidity, poolTvl) {
        // Добавление ликвидности увеличивает стабильность, но может влиять на цену
        const liquidityRatio = addedLiquidity / poolTvl;
        const priceStabilization = liquidityRatio * 0.5; // Стабилизирующий эффект
        
        return {
            liquidityRatio: (liquidityRatio * 100).toFixed(2),
            priceStabilization: priceStabilization.toFixed(4),
            newPoolTvl: poolTvl + addedLiquidity
        };
    }

    /**
     * 📈 РАСЧЕТ ПОВЫШЕНИЯ ЦЕНЫ ОТ АРБИТРАЖНОЙ ПОКУПКИ
     */
    calculateArbitragePriceIncrease(buyAmount, poolTvl) {
        const ratio = buyAmount / poolTvl;
        // Меньшая покупка = меньшее влияние на цену, но все еще значимое
        const priceIncrease = ratio * 100 * 2.0; // Коэффициент 2.0 для небольших покупок
        
        return {
            ratio: (ratio * 100).toFixed(2),
            priceIncrease: priceIncrease.toFixed(2),
            newPoolTvl: poolTvl + buyAmount
        };
    }

    /**
     * 💸 РАСЧЕТ SLIPPAGE
     */
    calculateSlippage(sellAmount, poolTvl) {
        const onePercent = poolTvl / 100;
        const slippagePercent = (sellAmount / onePercent);
        
        return {
            onePercent,
            slippagePercent: slippagePercent.toFixed(2),
            slippageLoss: sellAmount * (slippagePercent / 100)
        };
    }

    /**
     * 🎯 НОВАЯ СТРАТЕГИЯ: ЛИКВИДНОСТЬ + АРБИТРАЖ
     */
    calculateNewStrategy(poolTvl = 3000000) {
        console.log(`\n🎯 НОВАЯ СТРАТЕГИЯ: ЛИКВИДНОСТЬ + АРБИТРАЖ`);
        console.log(`🌊 Базовый пул: $${poolTvl.toLocaleString()}`);
        console.log('=' .repeat(70));
        
        // ИСПРАВЛЕННАЯ ЛОГИКА: Фокус на арбитраже
        const flashLoanAmount = poolTvl * 0.22; // 22% от пула = $660K
        const arbitrageAmount = flashLoanAmount * 0.77; // 77% займа на арбитраж = $508K
        const reserveAmount = flashLoanAmount * 0.23; // 23% займа резерв = $152K
        
        console.log(`💰 ИСПРАВЛЕННОЕ РАСПРЕДЕЛЕНИЕ СРЕДСТВ:`);
        console.log(`   🏦 Flash Loan: $${flashLoanAmount.toLocaleString()} (22% от пула)`);
        console.log(`   ⚡ Арбитражная позиция: $${arbitrageAmount.toLocaleString()} (77% займа)`);
        console.log(`   💰 Резерв для операций: $${reserveAmount.toLocaleString()} (23% займа)`);
        
        // ШАГ 1: Арбитражная покупка в базовом пуле (БЕЗ добавления ликвидности)
        const arbitrageImpact = this.calculateArbitragePriceIncrease(arbitrageAmount, poolTvl);
        console.log(`\n⚡ ШАГ 1 - АРБИТРАЖНАЯ ПОКУПКА:`);
        console.log(`   💰 Покупаем: $${arbitrageAmount.toLocaleString()}`);
        console.log(`   📊 Доля от базового пула: ${arbitrageImpact.ratio}%`);
        console.log(`   🚀 Повышение цены: +${arbitrageImpact.priceIncrease}%`);
        console.log(`   📈 Новый TVL: $${arbitrageImpact.newPoolTvl.toLocaleString()}`);
        
        // ШАГ 3: Продажа арбитражной позиции с бонусом
        const priceBonus = arbitrageAmount * (parseFloat(arbitrageImpact.priceIncrease) / 100);
        const grossRevenue = arbitrageAmount + priceBonus;
        
        // Slippage при продаже в том же пуле
        const slippageSmall = this.calculateSlippage(arbitrageAmount, arbitrageImpact.newPoolTvl);
        const netRevenueSmall = grossRevenue - slippageSmall.slippageLoss - (arbitrageAmount * this.METEORA_FEE);
        
        console.log(`\n💸 ШАГ 2 - ПРОДАЖА В МАЛЕНЬКОМ ПУЛЕ:`);
        console.log(`   💰 Продаем позицию: $${arbitrageAmount.toLocaleString()}`);
        console.log(`   🎁 Бонус от цены: +$${Math.round(priceBonus).toLocaleString()}`);
        console.log(`   📉 Slippage: ${slippageSmall.slippagePercent}% = -$${Math.round(slippageSmall.slippageLoss).toLocaleString()}`);
        console.log(`   💳 Комиссия: -$${Math.round(arbitrageAmount * this.METEORA_FEE).toLocaleString()}`);
        console.log(`   ✅ Получаем: $${Math.round(netRevenueSmall).toLocaleString()}`);
        
        // ШАГ 4: Продажа в большом пуле
        const slippageLarge = this.calculateSlippage(netRevenueSmall, this.POOLS.LARGE.tvl);
        const netRevenueLarge = netRevenueSmall - slippageLarge.slippageLoss - (netRevenueSmall * this.METEORA_FEE);
        
        console.log(`\n🌊 ШАГ 3 - ПРОДАЖА В БОЛЬШОМ ПУЛЕ:`);
        console.log(`   💰 Продаем: $${Math.round(netRevenueSmall).toLocaleString()}`);
        console.log(`   📉 Slippage: ${slippageLarge.slippagePercent}% = -$${Math.round(slippageLarge.slippageLoss).toLocaleString()}`);
        console.log(`   💳 Комиссия: -$${Math.round(netRevenueSmall * this.METEORA_FEE).toLocaleString()}`);
        console.log(`   ✅ Получаем: $${Math.round(netRevenueLarge).toLocaleString()}`);
        
        // ШАГ 5: Возврат Flash Loan и расчет прибыли
        const flashLoanFee = flashLoanAmount * this.FLASH_LOAN_FEE;
        const totalToReturn = flashLoanAmount + flashLoanFee;
        
        // ВАЖНО: Мы потратили только arbitrageAmount, ликвидность остается в пуле
        const actualSpent = arbitrageAmount;
        const netProfit = netRevenueLarge - actualSpent - this.TRANSACTION_COST;
        const roi = (netProfit / actualSpent * 100);
        
        // Простой расчет без LP токенов
        const totalProfit = netProfit; // Только арбитражная прибыль
        const totalRoi = roi; // Только арбитражный ROI
        
        console.log(`\n💰 ШАГ 4 - ФИНАЛЬНЫЙ РАСЧЕТ:`);
        console.log(`   💸 Потрачено на арбитраж: $${actualSpent.toLocaleString()}`);
        console.log(`   💵 Получено от продажи: $${Math.round(netRevenueLarge).toLocaleString()}`);
        console.log(`   🏦 Flash Loan возврат: $${Math.round(totalToReturn).toLocaleString()}`);
        console.log(`   💳 Транзакционные расходы: $${this.TRANSACTION_COST}`);
        console.log(`   🎯 ЧИСТАЯ ПРИБЫЛЬ: $${Math.round(netProfit).toLocaleString()}`);
        console.log(`   📈 ROI: ${roi.toFixed(2)}%`);
        
        return {
            flashLoanAmount,
            reserveAmount,
            arbitrageAmount,
            steps: {
                arbitrage: {
                    amount: arbitrageAmount,
                    priceIncrease: parseFloat(arbitrageImpact.priceIncrease),
                    priceBonus: Math.round(priceBonus)
                },
                sellSmall: {
                    grossRevenue: Math.round(grossRevenue),
                    slippage: parseFloat(slippageSmall.slippagePercent),
                    netRevenue: Math.round(netRevenueSmall)
                },
                sellLarge: {
                    amount: Math.round(netRevenueSmall),
                    slippage: parseFloat(slippageLarge.slippagePercent),
                    netRevenue: Math.round(netRevenueLarge)
                }
            },
            result: {
                netProfit: Math.round(netProfit),
                roi: parseFloat(roi.toFixed(2)),
                totalProfit: Math.round(totalProfit),
                totalRoi: parseFloat(totalRoi.toFixed(2)),
                profitable: totalProfit > 0
            }
        };
    }

    /**
     * 🏆 ТЕСТИРОВАНИЕ РАЗНЫХ РАЗМЕРОВ ПУЛОВ
     */
    testDifferentPoolSizes() {
        console.log(`\n🏆 ТЕСТИРОВАНИЕ РАЗНЫХ РАЗМЕРОВ ПУЛОВ`);
        console.log('=' .repeat(70));
        
        const poolSizes = [2000000, 3000000, 4000000, 5000000, 6000000];
        const results = [];
        
        poolSizes.forEach(poolSize => {
            console.log(`\n📊 ТЕСТ ПУЛА $${poolSize.toLocaleString()}:`);
            const result = this.calculateNewStrategy(poolSize);
            results.push({
                poolSize,
                flashLoan: result.flashLoanAmount,
                totalProfit: result.result.totalProfit,
                totalRoi: result.result.totalRoi
            });
            
            const status = result.result.profitable ? '✅' : '❌';
            const emoji = result.result.profitable ? '💚' : '🔴';
            console.log(`${status} Пул $${poolSize.toLocaleString()}: ${emoji} $${result.result.totalProfit.toLocaleString()} (${result.result.totalRoi}%)`);
        });
        
        return results;
    }
}

/**
 * 🎯 ГЛАВНАЯ ФУНКЦИЯ
 */
async function main() {
    console.log('🎯 ЗАПУСК НОВОЙ СТРАТЕГИИ METEORA АРБИТРАЖА...\n');
    
    const calculator = new MeteoraNewStrategyCalculator();
    
    try {
        // Тестируем базовую стратегию
        const baseResult = calculator.calculateNewStrategy();
        
        // Тестируем разные размеры пулов
        const testResults = calculator.testDifferentPoolSizes();
        
        console.log('\n🎉 АНАЛИЗ ЗАВЕРШЕН!');
        console.log(`🏆 БАЗОВАЯ СТРАТЕГИЯ ($3M пул):`);
        console.log(`   💰 Flash Loan: $${baseResult.flashLoanAmount.toLocaleString()}`);
        console.log(`   🎯 Общая прибыль: $${baseResult.result.totalProfit.toLocaleString()}`);
        console.log(`   📈 Общий ROI: ${baseResult.result.totalRoi}%`);
        console.log(`🚀 СТРАТЕГИЯ ГОТОВА К РЕАЛИЗАЦИИ!`);
        
        return baseResult;
        
    } catch (error) {
        console.error('❌ Ошибка расчета:', error);
        return null;
    }
}

// Запуск если файл выполняется напрямую
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { MeteoraNewStrategyCalculator };
