/**
 * 🎯 METEORA DLMM СОБСТВЕННАЯ ЛИКВИДНОСТЬ КАЛЬКУЛЯТОР
 * 
 * Рассчитывает прибыль от торговли на собственной ликвидности в DLMM
 * Учитывает bin-структуру и активные бины
 */

class MeteoraDLMMOwnLiquidityCalculator {
    constructor() {
        // Реальные данные пулов DLMM
        this.POOLS = {
            POOL_1: {
                name: 'Pool 1 (SOL/USDC)',
                existing_wsol: 21920, // Существующая WSOL ликвидность
                existing_usdc: 3396139, // Существующая USDC ликвидность
                current_price: 175, // $175 за SOL
                bin_step: 4, // 0.4% между bins
                fee: 0.0004, // 0.04%
                dynamic_fee: 0.004 // 0.4% динамическая комиссия
            },
            POOL_2: {
                name: 'Pool 2 (SOL/USDC)',
                existing_wsol: 15000, // Существующая WSOL ликвидность
                existing_usdc: 2625000, // Существующая USDC ликвидность
                current_price: 175, // $175 за SOL
                bin_step: 4, // 0.4% между bins
                fee: 0.0004, // 0.04%
                dynamic_fee: 0.004 // 0.4% динамическая комиссия
            }
        };
        
        // Ваша стратегия
        this.STRATEGY = {
            wsol_add_pool1: 8000, // WSOL добавляем в Pool 1
            usdc_add_pool2: 1500000, // USDC добавляем в Pool 2
            flash_loan_usdc: 2500000, // Flash loan USDC
            flash_loan_wsol: 8000, // Flash loan WSOL
            trading_amount: 1000000 // $1M для торговли
        };
        
        this.FLASH_LOAN_FEE = 0.0; // 0% MarginFi (без процентов)
        this.TRANSACTION_COST = 0.005; // $0.005 базовая комиссия
        
        console.log('🎯 MeteoraDLMMOwnLiquidityCalculator инициализирован');
        console.log('📊 Стратегия: Торговля на собственной ликвидности в DLMM');
    }

    /**
     * 💧 РАСЧЕТ ДОЛИ СОБСТВЕННОЙ ЛИКВИДНОСТИ В АКТИВНОМ БИНЕ
     */
    calculateOwnLiquidityShare(poolId, addedAmount, tokenType) {
        const pool = this.POOLS[poolId];
        
        if (tokenType === 'WSOL') {
            const totalWSol = pool.existing_wsol + addedAmount;
            const ownShare = addedAmount / totalWSol;
            
            return {
                totalLiquidity: totalWSol,
                ownLiquidity: addedAmount,
                ownSharePercent: (ownShare * 100).toFixed(2),
                existingLiquidity: pool.existing_wsol
            };
        } else if (tokenType === 'USDC') {
            const totalUSDC = pool.existing_usdc + addedAmount;
            const ownShare = addedAmount / totalUSDC;
            
            return {
                totalLiquidity: totalUSDC,
                ownLiquidity: addedAmount,
                ownSharePercent: (ownShare * 100).toFixed(2),
                existingLiquidity: pool.existing_usdc
            };
        }
    }

    /**
     * 🔥 РАСЧЕТ КОМИССИЙ ОТ ТОРГОВЛИ НА СОБСТВЕННОЙ ЛИКВИДНОСТИ
     */
    calculateTradingFeesOnOwnLiquidity(poolId, tradingVolume, ownSharePercent) {
        const pool = this.POOLS[poolId];
        
        // Общие комиссии от торговли
        const baseFee = tradingVolume * pool.fee;
        const dynamicFee = tradingVolume * pool.dynamic_fee;
        const totalFees = baseFee + dynamicFee;
        
        // Ваша доля от комиссий
        const ownFeeShare = totalFees * (ownSharePercent / 100);
        
        return {
            tradingVolume,
            baseFee,
            dynamicFee,
            totalFees,
            ownSharePercent,
            ownFeeShare,
            feeRate: ((baseFee + dynamicFee) / tradingVolume * 100).toFixed(3)
        };
    }

    /**
     * 🎯 РАСЧЕТ АРБИТРАЖА С СОБСТВЕННОЙ ЛИКВИДНОСТЬЮ
     */
    calculateArbitrageWithOwnLiquidity() {
        console.log('\n🎯 РАСЧЕТ АРБИТРАЖА С СОБСТВЕННОЙ ЛИКВИДНОСТЬЮ:');
        console.log('=' .repeat(60));
        
        // 1. Добавляем ликвидность в пулы
        const pool1Share = this.calculateOwnLiquidityShare('POOL_1', this.STRATEGY.wsol_add_pool1, 'WSOL');
        const pool2Share = this.calculateOwnLiquidityShare('POOL_2', this.STRATEGY.usdc_add_pool2, 'USDC');
        
        console.log('\n💧 ДОБАВЛЕНИЕ ЛИКВИДНОСТИ:');
        console.log(`Pool 1 - Добавляем ${this.STRATEGY.wsol_add_pool1} WSOL`);
        console.log(`   Наша доля: ${pool1Share.ownSharePercent}% от общей WSOL ликвидности`);
        console.log(`   Общая WSOL: ${pool1Share.totalLiquidity.toLocaleString()}`);
        
        console.log(`Pool 2 - Добавляем ${this.STRATEGY.usdc_add_pool2.toLocaleString()} USDC`);
        console.log(`   Наша доля: ${pool2Share.ownSharePercent}% от общей USDC ликвидности`);
        console.log(`   Общая USDC: ${pool2Share.totalLiquidity.toLocaleString()}`);
        
        // 2. Торговля: USDC → WSOL в Pool 2
        const buyTrade = this.calculateTradingFeesOnOwnLiquidity(
            'POOL_2', 
            this.STRATEGY.trading_amount, 
            parseFloat(pool2Share.ownSharePercent)
        );
        
        console.log('\n🔄 ТОРГОВЛЯ 1: USDC → WSOL (покупаем дешевле)');
        console.log(`   Объем торговли: $${buyTrade.tradingVolume.toLocaleString()}`);
        console.log(`   Общие комиссии: $${buyTrade.totalFees.toFixed(2)} (${buyTrade.feeRate}%)`);
        console.log(`   Наша доля комиссий: $${buyTrade.ownFeeShare.toFixed(2)}`);
        
        // 3. Торговля: WSOL → USDC в Pool 1
        const solAmount = this.STRATEGY.trading_amount / this.POOLS.POOL_1.current_price;
        const sellTrade = this.calculateTradingFeesOnOwnLiquidity(
            'POOL_1', 
            this.STRATEGY.trading_amount, 
            parseFloat(pool1Share.ownSharePercent)
        );
        
        console.log('\n🔄 ТОРГОВЛЯ 2: WSOL → USDC (продаем дороже)');
        console.log(`   Объем торговли: $${sellTrade.tradingVolume.toLocaleString()}`);
        console.log(`   Общие комиссии: $${sellTrade.totalFees.toFixed(2)} (${sellTrade.feeRate}%)`);
        console.log(`   Наша доля комиссий: $${sellTrade.ownFeeShare.toFixed(2)}`);
        
        // 4. Общая прибыль от комиссий
        const totalFeeIncome = buyTrade.ownFeeShare + sellTrade.ownFeeShare;
        
        // 5. Затраты
        const flashLoanCost = (this.STRATEGY.flash_loan_usdc + this.STRATEGY.flash_loan_wsol * this.POOLS.POOL_1.current_price) * this.FLASH_LOAN_FEE;
        const transactionCost = this.TRANSACTION_COST;
        const totalCosts = flashLoanCost + transactionCost;
        
        // 6. Чистая прибыль
        const netProfit = totalFeeIncome - totalCosts;
        
        console.log('\n💰 ИТОГОВЫЙ РАСЧЕТ:');
        console.log(`   Доход от комиссий Pool 1: $${sellTrade.ownFeeShare.toFixed(2)}`);
        console.log(`   Доход от комиссий Pool 2: $${buyTrade.ownFeeShare.toFixed(2)}`);
        console.log(`   Общий доход от комиссий: $${totalFeeIncome.toFixed(2)}`);
        console.log(`   Flash Loan комиссия: $${flashLoanCost.toFixed(2)}`);
        console.log(`   Транзакционные расходы: $${transactionCost.toFixed(2)}`);
        console.log(`   Общие расходы: $${totalCosts.toFixed(2)}`);
        console.log(`   ЧИСТАЯ ПРИБЫЛЬ: $${netProfit.toFixed(2)}`);
        
        return {
            feeIncome: totalFeeIncome,
            costs: totalCosts,
            netProfit: netProfit,
            pool1Share: pool1Share,
            pool2Share: pool2Share,
            buyTrade: buyTrade,
            sellTrade: sellTrade
        };
    }

    /**
     * 🧮 РАСЧЕТ МИНИМАЛЬНОЙ ЧУЖОЙ ЛИКВИДНОСТИ ДЛЯ ПРИБЫЛЬНОСТИ
     */
    calculateMinimumExternalLiquidity(targetProfit = 100) {
        console.log('\n🧮 РАСЧЕТ МИНИМАЛЬНОЙ ЧУЖОЙ ЛИКВИДНОСТИ:');
        console.log('=' .repeat(60));

        // Для прибыльности нужно, чтобы комиссии покрывали расходы + целевую прибыль
        const requiredFeeIncome = targetProfit + this.TRANSACTION_COST;

        // При торговле $1M с комиссией 0.44% (0.04% + 0.4%) нужна определенная доля
        const totalFeeRate = 0.0044; // 0.44%
        const totalFeesGenerated = this.STRATEGY.trading_amount * totalFeeRate * 2; // 2 торговли

        // Минимальная доля для получения требуемого дохода
        const requiredSharePercent = (requiredFeeIncome / totalFeesGenerated) * 100;

        console.log(`   Целевая прибыль: $${targetProfit}`);
        console.log(`   Требуемый доход от комиссий: $${requiredFeeIncome.toFixed(2)}`);
        console.log(`   Общие комиссии от торговли: $${totalFeesGenerated.toFixed(2)}`);
        console.log(`   Требуемая доля: ${requiredSharePercent.toFixed(2)}%`);

        return {
            targetProfit,
            requiredFeeIncome,
            totalFeesGenerated,
            requiredSharePercent
        };
    }

    /**
     * 🎯 ДЕТАЛЬНЫЙ АНАЛИЗ АКТИВНЫХ БИНОВ И ЧУЖОЙ ЛИКВИДНОСТИ
     */
    analyzeActiveBinLiquidity() {
        console.log('\n🎯 АНАЛИЗ АКТИВНЫХ БИНОВ И ЧУЖОЙ ЛИКВИДНОСТИ:');
        console.log('=' .repeat(70));

        // Предполагаем, что торговля происходит в активном бине
        const activeBinPrice = this.POOLS.POOL_1.current_price; // $175

        // В DLMM активный бин содержит обе валюты
        // Примерное распределение ликвидности в активном бине (10-20% от общей)
        const activeBinLiquidityRatio = 0.15; // 15% от общей ликвидности в активном бине

        // Pool 1 - активный бин ликвидность
        const pool1ActiveBinWSol = (this.POOLS.POOL_1.existing_wsol + this.STRATEGY.wsol_add_pool1) * activeBinLiquidityRatio;
        const pool1ActiveBinUSDC = pool1ActiveBinWSol * activeBinPrice;

        // Pool 2 - активный бин ликвидность
        const pool2ActiveBinUSDC = (this.POOLS.POOL_2.existing_usdc + this.STRATEGY.usdc_add_pool2) * activeBinLiquidityRatio;
        const pool2ActiveBinWSol = pool2ActiveBinUSDC / activeBinPrice;

        // Ваша доля в активных бинах
        const yourPool1ActiveWSol = this.STRATEGY.wsol_add_pool1 * activeBinLiquidityRatio;
        const yourPool2ActiveUSDC = this.STRATEGY.usdc_add_pool2 * activeBinLiquidityRatio;

        const pool1ActiveShare = (yourPool1ActiveWSol / pool1ActiveBinWSol) * 100;
        const pool2ActiveShare = (yourPool2ActiveUSDC / pool2ActiveBinUSDC) * 100;

        console.log('\n💧 АКТИВНЫЙ БИН ЛИКВИДНОСТЬ:');
        console.log(`Pool 1 активный бин:`);
        console.log(`   Общая WSOL в активном бине: ${pool1ActiveBinWSol.toFixed(0)}`);
        console.log(`   Ваша WSOL в активном бине: ${yourPool1ActiveWSol.toFixed(0)}`);
        console.log(`   Ваша доля в активном бине: ${pool1ActiveShare.toFixed(2)}%`);

        console.log(`Pool 2 активный бин:`);
        console.log(`   Общая USDC в активном бине: $${pool2ActiveBinUSDC.toLocaleString()}`);
        console.log(`   Ваша USDC в активном бине: $${yourPool2ActiveUSDC.toLocaleString()}`);
        console.log(`   Ваша доля в активном бине: ${pool2ActiveShare.toFixed(2)}%`);

        // Расчет комиссий только от активного бина
        const activeBinFeeIncome = this.calculateActiveBinFeeIncome(pool1ActiveShare, pool2ActiveShare);

        console.log('\n💰 ДОХОДЫ ОТ АКТИВНОГО БИНА:');
        console.log(`   Комиссии Pool 1 (активный бин): $${activeBinFeeIncome.pool1Fee.toFixed(2)}`);
        console.log(`   Комиссии Pool 2 (активный бин): $${activeBinFeeIncome.pool2Fee.toFixed(2)}`);
        console.log(`   Общий доход от активных бинов: $${activeBinFeeIncome.totalFee.toFixed(2)}`);

        return {
            pool1ActiveShare,
            pool2ActiveShare,
            activeBinFeeIncome,
            pool1ActiveBinWSol,
            pool2ActiveBinUSDC,
            yourPool1ActiveWSol,
            yourPool2ActiveUSDC
        };
    }

    /**
     * 💰 РАСЧЕТ КОМИССИЙ ОТ АКТИВНОГО БИНА
     */
    calculateActiveBinFeeIncome(pool1SharePercent, pool2SharePercent) {
        const tradingAmount = this.STRATEGY.trading_amount;
        const feeRate = 0.0044; // 0.44% (base + dynamic)

        // Комиссии генерируются только в активном бине
        const pool1Fee = (tradingAmount * feeRate) * (pool1SharePercent / 100);
        const pool2Fee = (tradingAmount * feeRate) * (pool2SharePercent / 100);
        const totalFee = pool1Fee + pool2Fee;

        return {
            pool1Fee,
            pool2Fee,
            totalFee
        };
    }
}

// Экспорт для использования
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MeteoraDLMMOwnLiquidityCalculator;
}

// Запуск расчетов
if (require.main === module) {
    const calculator = new MeteoraDLMMOwnLiquidityCalculator();

    // Основной расчет
    const result = calculator.calculateArbitrageWithOwnLiquidity();

    // Детальный анализ активных бинов
    const activeBinAnalysis = calculator.analyzeActiveBinLiquidity();

    // Расчет минимальной ликвидности
    calculator.calculateMinimumExternalLiquidity(100);
    calculator.calculateMinimumExternalLiquidity(500);
    calculator.calculateMinimumExternalLiquidity(1000);

    // Итоговые выводы
    console.log('\n🎯 ИТОГОВЫЕ ВЫВОДЫ:');
    console.log('=' .repeat(70));
    console.log(`✅ Ваша стратегия ПРИБЫЛЬНА: $${result.netProfit.toFixed(2)} чистой прибыли`);
    console.log(`📊 Вы зарабатываете на торговле через СОБСТВЕННУЮ ликвидность`);
    console.log(`💧 Доля в Pool 1: ${result.pool1Share.ownSharePercent}% (WSOL)`);
    console.log(`💧 Доля в Pool 2: ${result.pool2Share.ownSharePercent}% (USDC)`);
    console.log(`🔥 Активный бин Pool 1: ${activeBinAnalysis.pool1ActiveShare.toFixed(2)}% вашей доли`);
    console.log(`🔥 Активный бин Pool 2: ${activeBinAnalysis.pool2ActiveShare.toFixed(2)}% вашей доли`);
    console.log(`💰 Минимальная доля для $100 прибыли: 1.14%`);
    console.log(`💰 Ваша текущая доля превышает минимум в ${(parseFloat(result.pool1Share.ownSharePercent) / 1.14).toFixed(1)}x раз`);
}
