/**
 * 🎯 БЫСТРЫЙ РАСЧЕТ С РЕАЛЬНЫМИ ДАННЫМИ METEORA
 */

// РЕАЛЬНЫЕ данные из скриншота
const REAL_DATA = {
    tvl: 3468058,
    sol_amount: 9348.85,
    usdc_amount: 1873090.00,
    current_price: 172.02, // Реальная цена из UI
    calculated_price: 1873090.00 / 9348.85, // = 200.35
    base_fee: 0.001, // 0.1%
    dynamic_fee_current: 0.001000483, // 0.1000483%
    dynamic_fee_max: 0.10, // 10%
    protocol_fee: 0.00005002415 // 0.005002415%
};

console.log('🎯 БЫСТРЫЙ АНАЛИЗ METEORA АРБИТРАЖА');
console.log('=' .repeat(50));

console.log('📊 РЕАЛЬНЫЕ ДАННЫЕ:');
console.log(`   TVL: $${REAL_DATA.tvl.toLocaleString()}`);
console.log(`   SOL: ${REAL_DATA.sol_amount.toLocaleString()}`);
console.log(`   USDC: $${REAL_DATA.usdc_amount.toLocaleString()}`);
console.log(`   Цена в UI: $${REAL_DATA.current_price}`);
console.log(`   Расчетная цена: $${REAL_DATA.calculated_price.toFixed(2)}`);
console.log(`   Разница: ${((REAL_DATA.calculated_price - REAL_DATA.current_price) / REAL_DATA.current_price * 100).toFixed(1)}%`);

// СЦЕНАРИЙ: Добавляем $500K USDC
const USDC_TO_ADD = 500000;

console.log('\n💰 СЦЕНАРИЙ: ДОБАВЛЕНИЕ $500K USDC');
console.log('=' .repeat(50));

// Новый баланс
const new_usdc = REAL_DATA.usdc_amount + USDC_TO_ADD;
const usdc_increase = (USDC_TO_ADD / REAL_DATA.usdc_amount) * 100;

console.log(`📈 ИЗМЕНЕНИЕ БАЛАНСА:`);
console.log(`   Старый USDC: $${REAL_DATA.usdc_amount.toLocaleString()}`);
console.log(`   Новый USDC: $${new_usdc.toLocaleString()}`);
console.log(`   Увеличение: +${usdc_increase.toFixed(1)}%`);

// Влияние на цену (упрощенная модель)
const price_impact_percent = usdc_increase * 0.6; // Коэффициент 0.6
const new_price = REAL_DATA.current_price * (1 + price_impact_percent / 100);
const price_difference = new_price - REAL_DATA.current_price;

console.log(`\n🚀 ВЛИЯНИЕ НА ЦЕНУ SOL:`);
console.log(`   Старая цена: $${REAL_DATA.current_price}`);
console.log(`   Новая цена: $${new_price.toFixed(2)}`);
console.log(`   Рост: +${price_impact_percent.toFixed(1)}% (+$${price_difference.toFixed(2)})`);

// Dynamic Fee влияние
const volatility_increase = price_impact_percent / 5; // Волатильность
const new_dynamic_fee = Math.min(0.10, REAL_DATA.dynamic_fee_current + volatility_increase / 100);

console.log(`\n🔥 DYNAMIC FEE ИЗМЕНЕНИЕ:`);
console.log(`   Текущая: ${(REAL_DATA.dynamic_fee_current * 100).toFixed(4)}%`);
console.log(`   После добавления: ${(new_dynamic_fee * 100).toFixed(2)}%`);
console.log(`   Увеличение: ${((new_dynamic_fee - REAL_DATA.dynamic_fee_current) * 100).toFixed(2)}%`);

// Комиссии при добавлении ликвидности
const protocol_fee_cost = USDC_TO_ADD * REAL_DATA.protocol_fee;
const transaction_cost = 0.001; // Solana fee
const total_lp_cost = protocol_fee_cost + transaction_cost;

console.log(`\n💸 КОМИССИИ ПРИ ДОБАВЛЕНИИ ЛИКВИДНОСТИ:`);
console.log(`   Protocol Fee: $${protocol_fee_cost.toFixed(2)} (${(REAL_DATA.protocol_fee * 100).toFixed(4)}%)`);
console.log(`   Transaction: $${transaction_cost.toFixed(3)}`);
console.log(`   ИТОГО: $${total_lp_cost.toFixed(2)} (${(total_lp_cost / USDC_TO_ADD * 100).toFixed(4)}%)`);

// Арбитражная возможность
const flash_loan = 650000;
const reserve_for_arbitrage = flash_loan * 0.23; // 23% резерв
const sol_to_buy = reserve_for_arbitrage / REAL_DATA.current_price;
const arbitrage_revenue = sol_to_buy * new_price;
const arbitrage_profit_gross = arbitrage_revenue - reserve_for_arbitrage;

// Slippage при продаже в большом пуле
const large_pool_tvl = 7000000;
const slippage_percent = (arbitrage_revenue / (large_pool_tvl / 100));
const slippage_loss = arbitrage_revenue * (slippage_percent / 100);
const trading_fee = arbitrage_revenue * new_dynamic_fee;

const arbitrage_profit_net = arbitrage_profit_gross - slippage_loss - trading_fee;

console.log(`\n⚡ АРБИТРАЖНАЯ ВОЗМОЖНОСТЬ:`);
console.log(`   Flash Loan: $${flash_loan.toLocaleString()}`);
console.log(`   Резерв для арбитража: $${reserve_for_arbitrage.toLocaleString()}`);
console.log(`   Покупаем SOL: ${sol_to_buy.toFixed(2)} по $${REAL_DATA.current_price}`);
console.log(`   Продаем SOL: ${sol_to_buy.toFixed(2)} по $${new_price.toFixed(2)}`);
console.log(`   Валовая прибыль: $${arbitrage_profit_gross.toFixed(0)}`);
console.log(`   Slippage: -$${slippage_loss.toFixed(0)} (${slippage_percent.toFixed(1)}%)`);
console.log(`   Dynamic Fee: -$${trading_fee.toFixed(0)} (${(new_dynamic_fee * 100).toFixed(2)}%)`);
console.log(`   Чистая прибыль: $${arbitrage_profit_net.toFixed(0)}`);

// Flash Loan комиссия
const flash_loan_fee = flash_loan * 0.0009;
const total_costs = USDC_TO_ADD + total_lp_cost + flash_loan_fee;
const final_profit = arbitrage_profit_net - flash_loan_fee;
const roi = (final_profit / flash_loan) * 100;

console.log(`\n💰 ФИНАЛЬНЫЙ РАСЧЕТ:`);
console.log(`   Flash Loan комиссия: $${flash_loan_fee.toFixed(0)}`);
console.log(`   Общие расходы: $${total_costs.toFixed(0)}`);
console.log(`   🎯 ФИНАЛЬНАЯ ПРИБЫЛЬ: $${final_profit.toFixed(0)}`);
console.log(`   📈 ROI: ${roi.toFixed(2)}%`);

// Выводы
console.log(`\n🎯 ВЫВОДЫ:`);
console.log(`   ✅ Добавление $500K USDC поднимет цену на ${price_impact_percent.toFixed(1)}%`);
console.log(`   ✅ Dynamic Fee вырастет до ${(new_dynamic_fee * 100).toFixed(2)}%`);
console.log(`   ✅ Комиссии за LP минимальны: $${total_lp_cost.toFixed(2)}`);
console.log(`   ${final_profit > 0 ? '✅' : '❌'} Арбитраж ${final_profit > 0 ? 'прибыльный' : 'убыточный'}: $${final_profit.toFixed(0)}`);
console.log(`   📊 ROI: ${roi.toFixed(2)}%`);

if (final_profit > 0) {
    console.log(`\n🚀 СТРАТЕГИЯ РАБОТАЕТ!`);
    console.log(`   💰 Прибыль за операцию: $${final_profit.toFixed(0)}`);
    console.log(`   🔄 Потенциал в день: $${(final_profit * 10).toFixed(0)} (10 операций)`);
    console.log(`   📅 Потенциал в месяц: $${(final_profit * 300).toFixed(0)} (300 операций)`);
} else {
    console.log(`\n❌ СТРАТЕГИЯ УБЫТОЧНА`);
    console.log(`   🔧 Нужно оптимизировать параметры`);
}

console.log(`\n🎉 АНАЛИЗ ЗАВЕРШЕН!`);
