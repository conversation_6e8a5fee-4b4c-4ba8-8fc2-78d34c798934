# Meteora DLMM Swap Transaction Examples

## 1. TypeScript SDK Swap Example (from @meteora-ag/dlmm npm package)

### Installation
```bash
npm i @meteora-ag/dlmm @coral-xyz/anchor @solana/web3.js
```

### Basic Swap Implementation
```typescript
import DLMM from '@meteora-ag/dlmm'
import { Connection, PublicKey, Keypair } from '@solana/web3.js'
import { BN } from '@coral-xyz/anchor'

// Initialize DLMM instance
const USDC_USDT_POOL = new PublicKey('ARwi1S4DaiTG5DX7S4M4ZsrXqpMD1MrTmbu9ue2tpmEq')
const dlmmPool = await DLMM.create(connection, USDC_USDT_POOL);

// Swap Example
const swapAmount = new BN(0.1 * 10 ** 9); // 0.1 SOL
const swapYtoX = true; // Direction of swap

// Get bin arrays for swap
const binArrays = await dlmmPool.getBinArrayForSwap(swapYtoX);

// Get swap quote
const swapQuote = await dlmmPool.swapQuote(
  swapAmount,
  swapYtoX,
  new BN(1), // slippage tolerance
  binArrays
);

// Create swap transaction
const swapTx = await dlmmPool.swap({
  inToken: dlmmPool.tokenX.publicKey,
  binArraysPubkey: swapQuote.binArraysPubkey,
  inAmount: swapAmount,
  lbPair: dlmmPool.pubkey,
  user: user.publicKey,
  minOutAmount: swapQuote.minOutAmount,
  outToken: dlmmPool.tokenY.publicKey,
});

// Send transaction
try {
  const swapTxHash = await sendAndConfirmTransaction(connection, swapTx, [user]);
  console.log('Swap successful:', swapTxHash);
} catch (error) {
  console.error('Swap failed:', error);
}
```

## 2. Complete Swap Function Example
```typescript
async function performSwap(
  connection: Connection,
  dlmmPool: DLMM,
  user: Keypair,
  swapAmount: BN,
  swapYtoX: boolean
) {
  try {
    // Update pool state
    await dlmmPool.refetchStates();
    
    // Get bin arrays for swap direction
    const binArrays = await dlmmPool.getBinArrayForSwap(swapYtoX);
    
    // Calculate swap quote
    const swapQuote = await dlmmPool.swapQuote(
      swapAmount,
      swapYtoX,
      new BN(1), // slippage tolerance (1 basis point)
      binArrays
    );
    
    console.log('Swap Quote:', {
      inAmount: swapQuote.inAmount.toString(),
      outAmount: swapQuote.outAmount.toString(),
      minOutAmount: swapQuote.minOutAmount.toString(),
      priceImpact: swapQuote.priceImpact,
      fee: swapQuote.fee.toString()
    });
    
    // Create swap transaction
    const swapTx = await dlmmPool.swap({
      inToken: swapYtoX ? dlmmPool.tokenY.publicKey : dlmmPool.tokenX.publicKey,
      outToken: swapYtoX ? dlmmPool.tokenX.publicKey : dlmmPool.tokenY.publicKey,
      binArraysPubkey: swapQuote.binArraysPubkey,
      inAmount: swapAmount,
      lbPair: dlmmPool.pubkey,
      user: user.publicKey,
      minOutAmount: swapQuote.minOutAmount,
    });
    
    // Send and confirm transaction
    const swapTxHash = await sendAndConfirmTransaction(
      connection,
      swapTx,
      [user],
      { skipPreflight: false, preflightCommitment: "singleGossip" }
    );
    
    return {
      success: true,
      txHash: swapTxHash,
      quote: swapQuote
    };
    
  } catch (error) {
    console.error('Swap error:', error);
    return {
      success: false,
      error: error.message
    };
  }
}
```

## 3. Multi-Pool Creation Example
```typescript
// Create multiple DLMM instances
const poolAddresses = [
  new PublicKey('ARwi1S4DaiTG5DX7S4M4ZsrXqpMD1MrTmbu9ue2tpmEq'), // USDC-USDT
  new PublicKey('...'), // SOL-USDC
  new PublicKey('...'), // Other pools
];

const dlmmPools = await DLMM.createMultiple(connection, poolAddresses);
```

## 4. Pool Information Retrieval
```typescript
// Get active bin information
const activeBin = await dlmmPool.getActiveBin();
console.log('Active Bin:', {
  binId: activeBin.binId,
  price: activeBin.price
});

// Get fee information
const feeInfo = dlmmPool.getFeeInfo();
console.log('Fee Info:', feeInfo);

// Get dynamic fee
const dynamicFee = dlmmPool.getDynamicFee();
console.log('Dynamic Fee:', dynamicFee.toString());

// Get bins around active bin
const binsData = await dlmmPool.getBinsAroundActiveBin(10); // 10 bins each side
console.log('Bins around active:', binsData);
```

## 5. Price Conversion Utilities
```typescript
// Convert real price to lamport price
const realPrice = 1.5; // USDC per USDT
const lamportPrice = dlmmPool.toPricePerLamport(realPrice);

// Convert lamport price to real price
const pricePerToken = dlmmPool.fromPricePerLamport(Number(activeBin.price));

// Get price of specific bin
const binPrice = dlmmPool.getPriceOfBinByBinId(activeBin.binId);

// Get bin ID from price
const binId = dlmmPool.getBinIdFromPrice(realPrice, true); // true = round down
```

## 6. Error Handling Best Practices
```typescript
async function safeSwap(dlmmPool: DLMM, swapParams: any) {
  try {
    // Validate pool state
    await dlmmPool.refetchStates();
    
    // Check if pool has sufficient liquidity
    const activeBin = await dlmmPool.getActiveBin();
    if (!activeBin) {
      throw new Error('No active bin found');
    }
    
    // Perform swap with retry logic
    let retries = 3;
    while (retries > 0) {
      try {
        const result = await performSwap(/* params */);
        return result;
      } catch (error) {
        retries--;
        if (retries === 0) throw error;
        await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1s
      }
    }
  } catch (error) {
    console.error('Swap failed after retries:', error);
    throw error;
  }
}
```

## 7. Resources and Documentation

- NPM Package: https://www.npmjs.com/package/@meteora-ag/dlmm
- GitHub SDK: https://github.com/MeteoraAg/dlmm-sdk
- Official Documentation: https://docs.meteora.ag/integration/dlmm-integration/dlmm-sdk/dlmm-typescript-sdk
- Discord Support: https://discord.com/channels/841152225564950528/864859354335412224
- API Endpoint for Pool Data: https://dlmm-api.meteora.ag/pair/all

## 8. Important Notes

1. Always call `refetchStates()` before performing operations to ensure up-to-date pool state
2. Use appropriate slippage tolerance based on market conditions
3. Handle transaction failures gracefully with retry logic
4. Monitor gas fees and optimize transaction timing
5. Validate pool addresses and token mints before swapping
6. Consider using `skipPreflight: false` for better error reporting
7. The DLMM uses bin-based liquidity distribution for better capital efficiency
8. Dynamic fees adjust based on volatility and market conditions

## 9. Program ID and Key Addresses

- DLMM Program ID: LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo (mainnet)
- Pool addresses can be retrieved from the API or created using preset parameters
- Each pool has unique bin arrays that store liquidity data
