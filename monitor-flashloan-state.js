const { Connection, Keypair } = require('@solana/web3.js');
const { MarginfiClient, getConfig } = require('@mrgnlabs/marginfi-client-v2');
const { NodeWallet } = require('@mrgnlabs/mrgn-common');
const fs = require('fs');

class FlashLoanStateMonitor {
  constructor() {
    this.connection = new Connection('https://billowing-empty-patron.solana-mainnet.quiknode.pro/c79212a7508ec6a37353793d9be93c917898e858/', 'confirmed');
    this.wallet = null;
    this.marginfiClient = null;
    this.marginfiAccount = null;
  }

  async initialize() {
    try {
      const walletKeypair = Keypair.fromSecretKey(
        new Uint8Array(JSON.parse(fs.readFileSync('wallet.json', 'utf8')))
      );
      this.wallet = new NodeWallet(walletKeypair);
      
      const config = getConfig('production');
      this.marginfiClient = await MarginfiClient.fetch(config, this.wallet, this.connection);
      
      const marginfiAccounts = await this.marginfiClient.getMarginfiAccountsForAuthority();
      this.marginfiAccount = marginfiAccounts[0];
      
      console.log(`✅ Мониторинг инициализирован для аккаунта: ${this.marginfiAccount.address.toString()}`);
      return true;
    } catch (error) {
      console.error(`❌ Ошибка инициализации: ${error.message}`);
      return false;
    }
  }

  // МЕТОД 1: Проверка через чтение флагов аккаунта (может быть неточным из-за кэширования)
  async checkAccountFlags() {
    try {
      const accountInfo = await this.connection.getAccountInfo(this.marginfiAccount.address, 'finalized');
      if (!accountInfo) {
        throw new Error('Не удалось получить информацию об аккаунте');
      }

      const flags = accountInfo.data.readUInt8(8);
      const inFlashLoan = (flags & 64) !== 0; // Бит 6 = флаг ACCOUNT_IN_FLASHLOAN
      
      return {
        method: 'Account Flags',
        flags: flags,
        flagsHex: `0x${flags.toString(16)}`,
        flagsBinary: flags.toString(2).padStart(8, '0'),
        inFlashLoan: inFlashLoan,
        reliable: false // Может быть кэшировано
      };
    } catch (error) {
      return {
        method: 'Account Flags',
        error: error.message,
        reliable: false
      };
    }
  }

  // МЕТОД 2: Проверка через попытку создания flash loan (САМЫЙ НАДЕЖНЫЙ!)
  async checkFlashLoanCapability() {
    try {
      console.log('🧪 Тестируем возможность создания flash loan...');
      
      const flashLoanTx = await this.marginfiAccount.buildFlashLoanTx({
        ixs: [], // Пустые инструкции для теста
        signers: []
      });

      return {
        method: 'Flash Loan Test',
        canCreateFlashLoan: true,
        transactionSize: flashLoanTx.serialize().length,
        reliable: true, // САМЫЙ НАДЕЖНЫЙ МЕТОД!
        message: '✅ Flash loan можно создать - флаг сброшен!'
      };
    } catch (error) {
      let inFlashLoan = false;
      let errorType = 'Unknown';
      
      if (error.message.includes('6037') || error.message.includes('AccountInFlashloan')) {
        inFlashLoan = true;
        errorType = 'AccountInFlashloan';
      }
      
      return {
        method: 'Flash Loan Test',
        canCreateFlashLoan: false,
        inFlashLoan: inFlashLoan,
        errorType: errorType,
        error: error.message,
        reliable: true, // САМЫЙ НАДЕЖНЫЙ МЕТОД!
        message: inFlashLoan ? '❌ Аккаунт заблокирован - флаг НЕ сброшен!' : '❓ Другая ошибка'
      };
    }
  }

  // КОМПЛЕКСНАЯ ПРОВЕРКА
  async checkState() {
    console.log('\n🔍 ПРОВЕРКА СОСТОЯНИЯ FLASH LOAN');
    console.log('═══════════════════════════════════════════════════════════════');

    const flagsResult = await this.checkAccountFlags();
    const flashLoanResult = await this.checkFlashLoanCapability();

    console.log('\n📊 РЕЗУЛЬТАТЫ ПРОВЕРКИ:');
    console.log('─────────────────────────────────────────────────────────────');
    
    // Результат проверки флагов
    if (flagsResult.error) {
      console.log(`❌ Проверка флагов: ${flagsResult.error}`);
    } else {
      console.log(`🏁 Флаги аккаунта: ${flagsResult.flags} (${flagsResult.flagsHex})`);
      console.log(`📊 Бинарно: ${flagsResult.flagsBinary}`);
      console.log(`🔍 Flash loan флаг (бит 6): ${flagsResult.inFlashLoan ? '❌ УСТАНОВЛЕН' : '✅ НЕ УСТАНОВЛЕН'}`);
      console.log(`⚠️ Надежность: ${flagsResult.reliable ? 'ВЫСОКАЯ' : 'НИЗКАЯ (может быть кэш)'}`);
    }

    console.log('');
    
    // Результат проверки flash loan (ГЛАВНЫЙ!)
    if (flashLoanResult.error && !flashLoanResult.canCreateFlashLoan) {
      console.log(`🧪 Тест flash loan: ${flashLoanResult.message}`);
      console.log(`   Тип ошибки: ${flashLoanResult.errorType}`);
      console.log(`   Детали: ${flashLoanResult.error}`);
    } else {
      console.log(`🧪 Тест flash loan: ${flashLoanResult.message}`);
      console.log(`   Размер транзакции: ${flashLoanResult.transactionSize} байт`);
    }
    
    console.log(`✅ Надежность теста: ${flashLoanResult.reliable ? 'МАКСИМАЛЬНАЯ' : 'НИЗКАЯ'}`);

    console.log('\n🎯 ЗАКЛЮЧЕНИЕ:');
    console.log('─────────────────────────────────────────────────────────────');
    
    if (flashLoanResult.canCreateFlashLoan) {
      console.log('🎉 FLASH LOAN РАБОТАЕТ! Можно выполнять арбитраж!');
      console.log('💰 328.591 SOL flash loan доступен!');
    } else if (flashLoanResult.inFlashLoan) {
      console.log('🚨 АККАУНТ ЗАБЛОКИРОВАН! Флаг flash loan НЕ сброшен!');
      console.log('🛠️ Нужно повторить процедуру сброса!');
    } else {
      console.log('❓ Неизвестная проблема. Проверьте детали ошибки.');
    }

    return {
      flagsCheck: flagsResult,
      flashLoanCheck: flashLoanResult,
      canUseFlashLoan: flashLoanResult.canCreateFlashLoan
    };
  }

  // НЕПРЕРЫВНЫЙ МОНИТОРИНГ
  async startMonitoring(intervalSeconds = 30) {
    console.log(`🔄 Запуск мониторинга каждые ${intervalSeconds} секунд...`);
    console.log('Нажмите Ctrl+C для остановки');

    const monitor = async () => {
      console.log(`\n⏰ ${new Date().toLocaleTimeString()} - Проверка состояния...`);
      const result = await this.checkState();
      
      if (result.canUseFlashLoan) {
        console.log('🎉 Flash loan доступен! Мониторинг можно остановить.');
        return true; // Останавливаем мониторинг
      }
      
      return false; // Продолжаем мониторинг
    };

    // Первая проверка
    const shouldStop = await monitor();
    if (shouldStop) return;

    // Периодические проверки
    const interval = setInterval(async () => {
      const shouldStop = await monitor();
      if (shouldStop) {
        clearInterval(interval);
      }
    }, intervalSeconds * 1000);
  }
}

async function main() {
  const monitor = new FlashLoanStateMonitor();
  
  const initialized = await monitor.initialize();
  if (!initialized) {
    console.log('❌ Не удалось инициализировать мониторинг');
    return;
  }

  // Одноразовая проверка
  await monitor.checkState();
  
  // Раскомментируйте для непрерывного мониторинга:
  // await monitor.startMonitoring(30); // Проверка каждые 30 секунд
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = FlashLoanStateMonitor;
