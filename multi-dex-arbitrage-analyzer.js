#!/usr/bin/env node

/**
 * 🔍 МУЛЬТИ-DEX АРБИТРАЖНЫЙ АНАЛИЗАТОР
 * ═══════════════════════════════════════════════════════════════
 * 🔥 ЦЕЛЬ: Анализ арбитражных возможностей между Meteora, Raydium, Orca
 * 🚀 ФУНКЦИИ: Поиск спредов, расчет прибыли, выбор лучших возможностей
 * 🎯 ИНТЕГРАЦИЯ: Работает с UnifiedDexInterface
 */

const colors = require('colors');

// 🔥 ИМПОРТ КОНФИГУРАЦИИ
const {
    TRADING_CONFIG,
    calculateNetProfit,
    canExecuteTrade,
    getPositionSize
} = require('./trading-config.js');

// 🔥 ИМПОРТ КОНВЕРТЕРА СУММ
const { convertUsdToNativeAmount, formatAmountInUSD } = require('./centralized-amount-converter.js');

/**
 * 🔍 МУЛЬТИ-DEX АРБИТРАЖНЫЙ АНАЛИЗАТОР
 */
class MultiDexArbitrageAnalyzer {
    constructor(unifiedDexInterface) {
        this.unifiedDex = unifiedDexInterface;
        
        // 📊 СТАТИСТИКА
        this.stats = {
            totalAnalyses: 0,
            opportunitiesFound: 0,
            profitableOpportunities: 0,
            bestSpreadFound: 0,
            averageSpread: 0,
            lastAnalysisTime: null
        };

        // 🎯 НАСТРОЙКИ АНАЛИЗА
        this.analysisConfig = {
            minSpreadPercent: TRADING_CONFIG.MIN_SPREAD_PERCENT,
            minProfitUsd: TRADING_CONFIG.MIN_PROFIT_USD,
            maxPositionUsd: TRADING_CONFIG.POSITION_SIZES.MAX_POSITION_USD,
            supportedTokens: ['SOL/USDC', 'USDC/SOL']
        };

        console.log('🔍 Мульти-DEX арбитражный анализатор создан');
        console.log(`   Минимальный спред: ${this.analysisConfig.minSpreadPercent}%`);
        console.log(`   Минимальная прибыль: $${this.analysisConfig.minProfitUsd}`);
    }

    /**
     * 🚀 ОСНОВНОЙ АНАЛИЗ АРБИТРАЖНЫХ ВОЗМОЖНОСТЕЙ
     */
    async analyzeArbitrageOpportunities() {
        try {
            console.log('\n🔍 АНАЛИЗ МУЛЬТИ-DEX АРБИТРАЖНЫХ ВОЗМОЖНОСТЕЙ...'.yellow);
            
            this.stats.totalAnalyses++;
            const startTime = Date.now();

            // 1. Получаем цены со всех DEX
            const allPrices = await this.unifiedDex.getAllPrices();
            
            if (allPrices.size === 0) {
                console.log('⚠️ Цены не получены ни с одного DEX');
                return [];
            }

            console.log(`📊 Получено цен: ${allPrices.size}`);

            // 2. Группируем цены по токенам
            const pricesByToken = this.groupPricesByToken(allPrices);
            console.log(`🪙 Токенов для анализа: ${pricesByToken.size}`);

            // 3. Ищем арбитражные возможности
            const opportunities = [];

            for (const [token, prices] of pricesByToken.entries()) {
                if (prices.length < 2) {
                    console.log(`⚠️ ${token}: недостаточно цен (${prices.length})`);
                    continue;
                }

                const tokenOpportunities = this.findTokenArbitrageOpportunities(token, prices);
                opportunities.push(...tokenOpportunities);
            }

            // 4. Сортируем по прибыльности
            opportunities.sort((a, b) => b.expectedProfitUsd - a.expectedProfitUsd);

            // 5. Обновляем статистику
            this.updateStats(opportunities);

            const analysisTime = Date.now() - startTime;
            console.log(`✅ Анализ завершен за ${analysisTime}мс`);
            console.log(`🎯 Найдено возможностей: ${opportunities.length}`);

            if (opportunities.length > 0) {
                this.displayTopOpportunities(opportunities.slice(0, 3));
            }

            return opportunities;

        } catch (error) {
            console.error('❌ Ошибка анализа арбитражных возможностей:', error.message);
            return [];
        }
    }

    /**
     * 📊 ГРУППИРОВКА ЦЕН ПО ТОКЕНАМ
     */
    groupPricesByToken(allPrices) {
        const grouped = new Map();

        for (const [key, priceData] of allPrices.entries()) {
            // Извлекаем токен из ключа (например, "meteora_SOL/USDC_address" -> "SOL/USDC")
            const tokenMatch = key.match(/_(SOL\/USDC|USDC\/SOL)_/);
            if (!tokenMatch) continue;

            const token = tokenMatch[1];
            const dex = key.split('_')[0]; // meteora, raydium, orca

            if (!grouped.has(token)) {
                grouped.set(token, []);
            }

            grouped.get(token).push({
                ...priceData,
                dex: dex,
                poolAddress: key.split('_')[2] || 'unknown',
                originalKey: key
            });
        }

        return grouped;
    }

    /**
     * 🎯 ПОИСК АРБИТРАЖНЫХ ВОЗМОЖНОСТЕЙ ДЛЯ ТОКЕНА
     */
    findTokenArbitrageOpportunities(token, prices) {
        const opportunities = [];

        // Сортируем цены по возрастанию
        prices.sort((a, b) => a.price - b.price);

        const cheapest = prices[0];
        const mostExpensive = prices[prices.length - 1];

        // Проверяем все комбинации
        for (let i = 0; i < prices.length - 1; i++) {
            for (let j = i + 1; j < prices.length; j++) {
                const buyPrice = prices[i];
                const sellPrice = prices[j];

                if (sellPrice.price <= buyPrice.price) continue;

                const opportunity = this.analyzeOpportunity(token, buyPrice, sellPrice);
                
                if (opportunity && opportunity.isProfitable) {
                    opportunities.push(opportunity);
                }
            }
        }

        return opportunities;
    }

    /**
     * 🧮 АНАЛИЗ КОНКРЕТНОЙ ВОЗМОЖНОСТИ
     */
    analyzeOpportunity(token, buyPriceData, sellPriceData) {
        try {
            const spread = sellPriceData.price - buyPriceData.price;
            const spreadPercent = (spread / buyPriceData.price) * 100;

            // Проверяем минимальный спред
            if (spreadPercent < this.analysisConfig.minSpreadPercent) {
                return null;
            }

            // Определяем размер позиции (используем умеренный размер для анализа)
            const positionSizeUsd = getPositionSize('FLASH_LOAN_AMOUNT_USD') || 1000;

            // Рассчитываем прибыль
            const profitAnalysis = calculateNetProfit(
                buyPriceData.price,
                sellPriceData.price,
                positionSizeUsd,
                this.analysisConfig.minProfitUsd
            );

            // Создаем объект возможности
            const opportunity = {
                token: token,
                buyDex: buyPriceData.dex,
                sellDex: sellPriceData.dex,
                buyPool: buyPriceData.poolAddress,
                sellPool: sellPriceData.poolAddress,
                buyPrice: buyPriceData.price,
                sellPrice: sellPriceData.price,
                spread: spreadPercent,
                spreadUsd: spread,
                positionSizeUsd: positionSizeUsd,
                expectedProfitUsd: profitAnalysis.netProfit,
                roi: profitAnalysis.roi,
                isProfitable: profitAnalysis.isProfitable,
                fees: profitAnalysis.fees,
                timestamp: Date.now(),
                
                // Дополнительная информация
                buySource: buyPriceData.source,
                sellSource: sellPriceData.source,
                confidence: this.calculateConfidence(buyPriceData, sellPriceData),
                risk: this.calculateRisk(spreadPercent, buyPriceData.dex, sellPriceData.dex)
            };

            // Проверяем возможность выполнения
            const tradeCheck = canExecuteTrade(opportunity);
            opportunity.canExecute = tradeCheck.canTrade;
            opportunity.executeReason = tradeCheck.reason;

            return opportunity;

        } catch (error) {
            console.log(`⚠️ Ошибка анализа возможности: ${error.message}`);
            return null;
        }
    }

    /**
     * 🎯 РАСЧЕТ УВЕРЕННОСТИ В ВОЗМОЖНОСТИ
     */
    calculateConfidence(buyData, sellData) {
        let confidence = 0.5; // Базовая уверенность

        // Увеличиваем уверенность для известных DEX
        const reliableDexes = ['meteora', 'raydium', 'orca'];
        if (reliableDexes.includes(buyData.dex)) confidence += 0.2;
        if (reliableDexes.includes(sellData.dex)) confidence += 0.2;

        // Учитываем свежесть данных
        const now = Date.now();
        const buyAge = now - (buyData.timestamp || now);
        const sellAge = now - (sellData.timestamp || now);
        
        if (buyAge < 5000 && sellAge < 5000) confidence += 0.1; // Данные свежие

        return Math.min(confidence, 1.0);
    }

    /**
     * ⚠️ РАСЧЕТ РИСКА
     */
    calculateRisk(spreadPercent, buyDex, sellDex) {
        let risk = 0.3; // Базовый риск

        // Увеличиваем риск для очень больших спредов (возможно ошибка данных)
        if (spreadPercent > 5.0) risk += 0.3;
        if (spreadPercent > 10.0) risk += 0.4;

        // Снижаем риск для проверенных DEX
        const trustedDexes = ['meteora', 'raydium', 'orca'];
        if (trustedDexes.includes(buyDex) && trustedDexes.includes(sellDex)) {
            risk -= 0.2;
        }

        return Math.max(0.1, Math.min(risk, 1.0));
    }

    /**
     * 📊 ОТОБРАЖЕНИЕ ЛУЧШИХ ВОЗМОЖНОСТЕЙ
     */
    displayTopOpportunities(opportunities) {
        console.log('\n🏆 ТОП АРБИТРАЖНЫХ ВОЗМОЖНОСТЕЙ:'.green.bold);
        console.log('═'.repeat(80));

        opportunities.forEach((opp, index) => {
            console.log(`\n${index + 1}. ${opp.token} - Спред: ${opp.spread.toFixed(4)}%`.cyan.bold);
            console.log(`   💰 Прибыль: $${opp.expectedProfitUsd.toFixed(2)} (ROI: ${opp.roi.toFixed(2)}%)`);
            console.log(`   📈 Купить:  ${opp.buyDex.toUpperCase()} по $${opp.buyPrice.toFixed(4)}`);
            console.log(`   📉 Продать: ${opp.sellDex.toUpperCase()} по $${opp.sellPrice.toFixed(4)}`);
            console.log(`   🎯 Уверенность: ${(opp.confidence * 100).toFixed(1)}% | Риск: ${(opp.risk * 100).toFixed(1)}%`);
            console.log(`   ✅ Можно выполнить: ${opp.canExecute ? 'ДА' : 'НЕТ'} ${!opp.canExecute ? `(${opp.executeReason})` : ''}`);
        });

        console.log('═'.repeat(80));
    }

    /**
     * 📊 ОБНОВЛЕНИЕ СТАТИСТИКИ
     */
    updateStats(opportunities) {
        this.stats.opportunitiesFound += opportunities.length;
        this.stats.profitableOpportunities += opportunities.filter(o => o.isProfitable).length;
        this.stats.lastAnalysisTime = Date.now();

        if (opportunities.length > 0) {
            const spreads = opportunities.map(o => o.spread);
            this.stats.bestSpreadFound = Math.max(this.stats.bestSpreadFound, Math.max(...spreads));
            this.stats.averageSpread = spreads.reduce((a, b) => a + b, 0) / spreads.length;
        }
    }

    /**
     * 🎯 ВЫБОР ЛУЧШЕЙ ВОЗМОЖНОСТИ ДЛЯ ВЫПОЛНЕНИЯ
     */
    selectBestOpportunity(opportunities) {
        if (!opportunities || opportunities.length === 0) {
            return null;
        }

        // Фильтруем только выполнимые возможности
        const executable = opportunities.filter(o => o.canExecute && o.isProfitable);
        
        if (executable.length === 0) {
            console.log('⚠️ Нет выполнимых арбитражных возможностей');
            return null;
        }

        // Сортируем по комбинированному скору (прибыль * уверенность / риск)
        executable.sort((a, b) => {
            const scoreA = (a.expectedProfitUsd * a.confidence) / a.risk;
            const scoreB = (b.expectedProfitUsd * b.confidence) / b.risk;
            return scoreB - scoreA;
        });

        const best = executable[0];
        
        console.log(`🎯 ВЫБРАНА ЛУЧШАЯ ВОЗМОЖНОСТЬ:`.green.bold);
        console.log(`   ${best.token}: ${best.buyDex} → ${best.sellDex}`);
        console.log(`   Прибыль: $${best.expectedProfitUsd.toFixed(2)} (${best.spread.toFixed(4)}%)`);
        console.log(`   Скор: ${((best.expectedProfitUsd * best.confidence) / best.risk).toFixed(2)}`);

        return best;
    }

    /**
     * 📊 ПОЛУЧЕНИЕ СТАТИСТИКИ
     */
    getStats() {
        return {
            ...this.stats,
            analysisConfig: this.analysisConfig
        };
    }

    /**
     * 🔄 СБРОС СТАТИСТИКИ
     */
    resetStats() {
        this.stats = {
            totalAnalyses: 0,
            opportunitiesFound: 0,
            profitableOpportunities: 0,
            bestSpreadFound: 0,
            averageSpread: 0,
            lastAnalysisTime: null
        };
        console.log('📊 Статистика анализатора сброшена');
    }
}

module.exports = MultiDexArbitrageAnalyzer;
