#!/usr/bin/env node

/**
 * 🎯 ПРАВИЛЬНЫЙ КАЛЬКУЛЯТОР НА ОСНОВЕ ОФИЦИАЛЬНОЙ ЛОГИКИ METEORA DLMM
 * 
 * ОСНОВАНО НА ОФИЦИАЛЬНОЙ ДОКУМЕНТАЦИИ:
 * - Bins структура: активный bin + односторонние bins
 * - Формула ликвидности: P⋅x + y = L
 * - Стратегии распределения: Spot, Curve, BidAsk
 * 
 * НАША СТРАТЕГИЯ:
 * 1. Flash Loan $1,800,000 USDC
 * 2. Добавить $1,400,000 USDC в bins ВЫШЕ текущей цены (односторонняя ликвидность)
 * 3. Купить SOL за $400,000 USDC в большом пуле
 * 4. Продать SOL в нашем пуле (активирует наши bins с USDC)
 * 5. Забрать ликвидность + комиссии
 * 6. Вернуть займ
 */

class OfficialDLMMStrategyCalculator {
    constructor() {
        // 🚀 ОПТИМИЗИРОВАННЫЕ ПАРАМЕТРЫ СТРАТЕГИИ
        this.STRATEGY = {
            flash_loan: 1800000,      // $1.8M USDC займ
            liquidity_add: 1400000,   // $1.4M USDC в ликвидность
            trading_amount: 400000,   // $400K USDC на торговлю
            target_price_increase: 0.05, // 5% вместо 20% (концентрированная ликвидность)
        };

        // 📊 РЕАЛЬНЫЕ ДАННЫЕ ПУЛОВ
        this.POOLS = {
            large: {
                name: 'Большой пул (покупка SOL)',
                tvl: 7000000,             // $7M TVL
                sol_price: 171.01,        // Текущая цена SOL
                slippage: 0.001           // 0.1% slippage
            },
            medium: {
                name: 'Средний пул (наша ликвидность)',
                tvl: 3000000,             // $3M TVL
                sol_price: 171.01,        // Базовая цена SOL
                bin_step: 10,             // 10 bps (0.1%)
                dynamic_fee: 0.005        // 0.5% комиссия
            }
        };

        // 💸 РЕАЛЬНЫЕ РАСХОДЫ
        this.COSTS = {
            gas_fee: 0.01,               // $0.01 за транзакцию (реально)
            slippage_buy: 0.001,         // 0.1% при покупке
            slippage_sell: 0,            // 0% при продаже (наш пул!)
        };

        console.log('🎯 ОФИЦИАЛЬНЫЙ DLMM КАЛЬКУЛЯТОР ИНИЦИАЛИЗИРОВАН');
    }

    /**
     * 📊 РАСЧЕТ BINS ДЛЯ ОДНОСТОРОННЕЙ ЛИКВИДНОСТИ (ОФИЦИАЛЬНАЯ ЛОГИКА)
     */
    calculateOfficialBins() {
        console.log('\n📊 РАСЧЕТ BINS ПО ОФИЦИАЛЬНОЙ ЛОГИКЕ DLMM...');
        
        const currentPrice = this.POOLS.medium.sol_price;
        const binStep = this.POOLS.medium.bin_step / 10000; // 0.001 (10 bps)
        const liquidityAmount = this.STRATEGY.liquidity_add;
        
        console.log(`   Текущая цена SOL: $${currentPrice}`);
        console.log(`   Bin Step: ${this.POOLS.medium.bin_step} bps (${binStep})`);
        console.log(`   Ликвидность для добавления: $${liquidityAmount.toLocaleString()}`);
        
        // ОФИЦИАЛЬНАЯ ЛОГИКА: bins выше текущей цены содержат только токен Y (USDC)
        // ОПТИМИЗАЦИЯ: концентрированная ликвидность в узком диапазоне
        const bins = [];
        const targetPriceIncrease = this.STRATEGY.target_price_increase; // 5% для концентрации
        const targetPrice = currentPrice * (1 + targetPriceIncrease);
        
        // Рассчитываем количество bins до целевой цены
        const binsNeeded = Math.ceil(Math.log(targetPrice / currentPrice) / Math.log(1 + binStep));
        const liquidityPerBin = liquidityAmount / binsNeeded;
        
        console.log(`   Целевая цена: $${targetPrice.toFixed(2)} (+${(targetPriceIncrease * 100).toFixed(1)}%)`);
        console.log(`   Bins нужно: ${binsNeeded}`);
        console.log(`   USDC на bin: $${liquidityPerBin.toLocaleString()}`);
        
        // Создаем bins
        for (let i = 1; i <= binsNeeded; i++) {
            const binPrice = currentPrice * Math.pow(1 + binStep, i);
            
            // ОФИЦИАЛЬНАЯ ФОРМУЛА: P⋅x + y = L
            // Для bins выше цены: x = 0, y = L (только USDC)
            const binLiquidity = liquidityPerBin;
            const usdcAmount = binLiquidity; // y = L (только USDC)
            const solAmount = 0;             // x = 0 (нет SOL)
            
            bins.push({
                binId: i,
                price: binPrice,
                usdcAmount: usdcAmount,
                solAmount: solAmount,
                liquidity: binLiquidity
            });
            
            if (i <= 5) { // Показываем первые 5 bins
                console.log(`   Bin ${i}: $${binPrice.toFixed(2)} | USDC: $${usdcAmount.toLocaleString()} | SOL: ${solAmount}`);
            }
        }
        
        if (binsNeeded > 5) {
            console.log(`   ... и еще ${binsNeeded - 5} bins до $${targetPrice.toFixed(2)}`);
        }
        
        const totalCoverage = bins.reduce((sum, bin) => sum + (bin.usdcAmount / bin.price), 0);
        const tradingAmount = this.STRATEGY.trading_amount;
        const solToBuy = tradingAmount / this.POOLS.large.sol_price;
        const coverageRatio = totalCoverage / solToBuy;
        
        console.log(`\n   📊 АНАЛИЗ ПОКРЫТИЯ:`);
        console.log(`   SOL для покупки: ${solToBuy.toFixed(2)} SOL`);
        console.log(`   SOL покрытие bins: ${totalCoverage.toFixed(2)} SOL`);
        console.log(`   Коэффициент покрытия: ${(coverageRatio * 100).toFixed(1)}%`);
        console.log(`   ${coverageRatio >= 1.0 ? '✅ ДОСТАТОЧНО' : '❌ НЕДОСТАТОЧНО'}`);
        
        return {
            bins: bins,
            totalBins: binsNeeded,
            liquidityPerBin: liquidityPerBin,
            totalCoverage: totalCoverage,
            solToBuy: solToBuy,
            coverageRatio: coverageRatio,
            priceRange: {
                min: currentPrice,
                max: targetPrice
            }
        };
    }

    /**
     * 💰 РАСЧЕТ ПРИБЫЛЬНОСТИ ПО ОФИЦИАЛЬНОЙ ЛОГИКЕ
     */
    calculateOfficialProfitability(binsData) {
        console.log('\n💰 РАСЧЕТ ПРИБЫЛЬНОСТИ ПО ОФИЦИАЛЬНОЙ ЛОГИКЕ...');
        
        const tradingAmount = this.STRATEGY.trading_amount;
        const solToBuy = binsData.solToBuy;
        const bins = binsData.bins;
        
        console.log('\n📋 ПОШАГОВЫЙ РАСЧЕТ:');
        
        // ШАГ 1: Покупка SOL в большом пуле
        const buyPrice = this.POOLS.large.sol_price;
        const buySlippage = tradingAmount * this.COSTS.slippage_buy;
        const totalBuyCost = tradingAmount + buySlippage;
        
        console.log(`   1️⃣ ПОКУПКА SOL В БОЛЬШОМ ПУЛЕ:`);
        console.log(`      ${solToBuy.toFixed(2)} SOL по $${buyPrice} = $${tradingAmount.toLocaleString()}`);
        console.log(`      Slippage: $${buySlippage.toFixed(0)} (0.1%)`);
        console.log(`      Общая стоимость: $${totalBuyCost.toLocaleString()}`);
        
        // ШАГ 2: Продажа SOL в нашем пуле (активация bins)
        console.log(`   2️⃣ ПРОДАЖА SOL В НАШЕМ ПУЛЕ (АКТИВАЦИЯ BINS):`);
        
        let remainingSol = solToBuy;
        let totalUsdcReceived = 0;
        let totalFeesEarned = 0;
        let binsUsed = 0;
        
        // Продаем SOL через наши bins (снизу вверх по цене)
        for (const bin of bins) {
            if (remainingSol <= 0) break;
            
            // Максимум SOL который может купить этот bin
            const maxSolForBin = bin.usdcAmount / bin.price;
            const solToSell = Math.min(remainingSol, maxSolForBin);
            
            // USDC получаем за проданный SOL
            const usdcReceived = solToSell * bin.price;
            
            // Комиссии с этой торговли (наша доля)
            const tradingVolume = usdcReceived;
            const binFees = tradingVolume * this.POOLS.medium.dynamic_fee;
            
            totalUsdcReceived += usdcReceived;
            totalFeesEarned += binFees;
            remainingSol -= solToSell;
            binsUsed++;
            
            if (binsUsed <= 3) { // Показываем первые 3 активации
                console.log(`      Bin ${bin.binId} ($${bin.price.toFixed(2)}): ${solToSell.toFixed(2)} SOL → $${usdcReceived.toFixed(0)} + $${binFees.toFixed(0)} комиссий`);
            }
        }
        
        if (binsUsed > 3) {
            console.log(`      ... активировано еще ${binsUsed - 3} bins`);
        }
        
        console.log(`      Всего активировано bins: ${binsUsed}`);
        console.log(`      Общая выручка: $${totalUsdcReceived.toLocaleString()}`);
        console.log(`      Общие комиссии: $${totalFeesEarned.toFixed(0)}`);
        
        // ШАГ 3: Прибыль от торговли
        const tradingProfit = totalUsdcReceived - totalBuyCost;
        console.log(`   3️⃣ ПРИБЫЛЬ ОТ ТОРГОВЛИ: $${totalUsdcReceived.toLocaleString()} - $${totalBuyCost.toLocaleString()} = $${tradingProfit.toFixed(0)}`);
        
        // ШАГ 4: Забираем оставшуюся ликвидность
        const usedLiquidity = binsUsed * binsData.liquidityPerBin;
        const remainingLiquidity = this.STRATEGY.liquidity_add - usedLiquidity;
        
        console.log(`   4️⃣ ОСТАВШАЯСЯ ЛИКВИДНОСТЬ:`);
        console.log(`      Использовано: $${usedLiquidity.toLocaleString()}`);
        console.log(`      Остается: $${remainingLiquidity.toLocaleString()}`);
        
        // ШАГ 5: Общая прибыль
        const totalRevenue = tradingProfit + totalFeesEarned;
        const gasCost = this.COSTS.gas_fee;
        const netProfit = totalRevenue - gasCost;
        const roi = (netProfit / this.STRATEGY.flash_loan) * 100;
        
        console.log(`   5️⃣ ИТОГОВЫЙ РЕЗУЛЬТАТ:`);
        console.log(`      Прибыль от торговли: $${tradingProfit.toFixed(0)}`);
        console.log(`      Доход от комиссий: $${totalFeesEarned.toFixed(0)}`);
        console.log(`      Общая выручка: $${totalRevenue.toFixed(0)}`);
        console.log(`      Gas расходы: $${gasCost}`);
        console.log(`      🎯 ЧИСТАЯ ПРИБЫЛЬ: $${netProfit.toFixed(0)}`);
        console.log(`      📈 ROI: ${roi.toFixed(2)}%`);
        console.log(`      ${roi >= 3.0 ? '✅ ВЫСОКОПРИБЫЛЬНО' : roi >= 1.0 ? '✅ ПРИБЫЛЬНО' : '❌ НЕ ПРИБЫЛЬНО'}`);
        
        return {
            tradingProfit: tradingProfit,
            feesEarned: totalFeesEarned,
            totalRevenue: totalRevenue,
            gasCost: gasCost,
            netProfit: netProfit,
            roi: roi,
            isProfitable: roi >= 1.0,
            binsUsed: binsUsed,
            remainingLiquidity: remainingLiquidity,
            details: {
                buyPrice: buyPrice,
                totalBuyCost: totalBuyCost,
                totalUsdcReceived: totalUsdcReceived,
                avgSellPrice: totalUsdcReceived / solToBuy
            }
        };
    }

    /**
     * 🚀 ПОЛНЫЙ РАСЧЕТ ПО ОФИЦИАЛЬНОЙ ЛОГИКЕ
     */
    async calculateOfficialStrategy() {
        console.log('🚀 РАСЧЕТ СТРАТЕГИИ ПО ОФИЦИАЛЬНОЙ ЛОГИКЕ METEORA DLMM');
        console.log('=' .repeat(80));
        
        try {
            // 1. Расчет bins по официальной логике
            const binsData = this.calculateOfficialBins();
            
            if (binsData.coverageRatio < 1.0) {
                throw new Error(`Недостаточное покрытие: ${(binsData.coverageRatio * 100).toFixed(1)}%`);
            }
            
            // 2. Расчет прибыльности
            const profitability = this.calculateOfficialProfitability(binsData);
            
            console.log('\n🎉 РАСЧЕТ ЗАВЕРШЕН ПО ОФИЦИАЛЬНОЙ ЛОГИКЕ!');
            console.log(`💰 Чистая прибыль: $${profitability.netProfit.toFixed(0)}`);
            console.log(`📈 ROI: ${profitability.roi.toFixed(2)}%`);
            console.log(`🎯 Статус: ${profitability.isProfitable ? 'ПРИБЫЛЬНО' : 'НЕ ПРИБЫЛЬНО'}`);
            console.log(`📊 Bins использовано: ${profitability.binsUsed} из ${binsData.totalBins}`);
            
            return {
                binsData,
                profitability,
                success: true
            };
            
        } catch (error) {
            console.error('❌ ОШИБКА:', error.message);
            return {
                error: error.message,
                success: false
            };
        }
    }
}

// 🧪 ЗАПУСК ОФИЦИАЛЬНОГО РАСЧЕТА
if (require.main === module) {
    async function runOfficialCalculation() {
        const calculator = new OfficialDLMMStrategyCalculator();
        const result = await calculator.calculateOfficialStrategy();
        
        if (result.success) {
            console.log('\n✅ ОФИЦИАЛЬНЫЙ РАСЧЕТ УСПЕШЕН!');
            console.log('🎯 СТРАТЕГИЯ ОСНОВАНА НА ОФИЦИАЛЬНОЙ ЛОГИКЕ METEORA DLMM!');
        } else {
            console.log('\n❌ РАСЧЕТ ПРОВАЛЕН!');
        }
    }
    
    runOfficialCalculation().catch(console.error);
}

module.exports = OfficialDLMMStrategyCalculator;
