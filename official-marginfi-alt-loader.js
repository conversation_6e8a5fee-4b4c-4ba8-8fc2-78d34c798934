/**
 * 🔥 ОФИЦИАЛЬНЫЙ MARGINFI ALT LOADER
 * Загружает официальные MarginFi ALT таблицы из исходного кода SDK
 * + 1 кастомная ALT таблица пользователя
 */

const { Connection, PublicKey, AddressLookupTableAccount } = require('@solana/web3.js');
const fs = require('fs');

class OfficialMarginFiALTLoader {
    constructor(connection) {
        this.connection = connection;
        
        // 🔥 ОФИЦИАЛЬНЫЕ MARGINFI ALT ТАБЛИЦЫ ИЗ ИСХОДНОГО КОДА SDK
        // Источник: https://raw.githubusercontent.com/mrgnlabs/mrgn-ts/main/packages/marginfi-client-v2/src/constants.ts
        this.OFFICIAL_MARGINFI_GROUP = "4qp6Fx6tnZkY5Wropq9wUYgtFxXKwE6viZxFHg3rdAG8";
        this.OFFICIAL_MARGINFI_ALT_ADDRESSES = [
            "HGmknUTUmeovMc9ryERNWG6UFZDFDVr9xrum3ZhyL4fC", // MarginFi ALT 1
            "5FuKF7C1tJji2mXZuJ14U9oDb37is5mmvYLf4KwojoF1", // MarginFi ALT 2
            "FEFhAFKz48P3w82Ds5VhvyEDwhRqu2FejmnuxEPZ8wNR"  // MarginFi ALT 3
        ];
        
        // 🔥 КАСТОМНАЯ ALT ТАБЛИЦА ПОЛЬЗОВАТЕЛЯ
        this.CUSTOM_ALT_ADDRESS = "FAeyUf4AdG7vTQ2Q89r9hWBkPdYkykJKG4CxgqArBGXe";
        
        this.loadedALTTables = [];
        this.cacheFile = 'official-marginfi-alt-cache.json';
    }

    /**
     * 🔥 ЗАГРУЗКА ВСЕХ ALT ТАБЛИЦ (3 ОФИЦИАЛЬНЫЕ + 1 КАСТОМНАЯ)
     */
    async loadAllALTTables() {
        try {
            console.log('🔥 ЗАГРУЖАЕМ ОФИЦИАЛЬНЫЕ MARGINFI ALT ТАБЛИЦЫ...');
            
            // Пытаемся загрузить из кэша
            if (await this.loadFromCache()) {
                console.log('✅ ALT таблицы загружены из кэша');
                return this.loadedALTTables;
            }
            
            // Загружаем из блокчейна
            console.log('🔄 Загружаем ALT таблицы из блокчейна...');
            await this.loadFromBlockchain();
            
            // Сохраняем в кэш
            await this.saveToCache();
            
            console.log(`✅ Загружено ALT таблиц: ${this.loadedALTTables.length}/4`);
            return this.loadedALTTables;
            
        } catch (error) {
            console.error('❌ Ошибка загрузки ALT таблиц:', error.message);
            throw error;
        }
    }

    /**
     * 🔥 ЗАГРУЗКА ИЗ БЛОКЧЕЙНА
     */
    async loadFromBlockchain() {
        const allAddresses = [...this.OFFICIAL_MARGINFI_ALT_ADDRESSES, this.CUSTOM_ALT_ADDRESS];
        
        for (let i = 0; i < allAddresses.length; i++) {
            const address = allAddresses[i];
            const isCustom = i === allAddresses.length - 1;
            const name = isCustom ? 'custom' : `marginfi${i + 1}`;
            
            try {
                console.log(`📋 Загрузка ${name} ALT: ${address.slice(0, 8)}...`);
                
                const altAccount = await this.connection.getAddressLookupTable(new PublicKey(address));
                
                if (altAccount?.value) {
                    this.loadedALTTables.push(altAccount.value);
                    console.log(`✅ ${name} ALT загружена: ${altAccount.value.state.addresses.length} адресов`);
                } else {
                    console.log(`❌ ${name} ALT не найдена в блокчейне`);
                }
                
            } catch (error) {
                console.log(`❌ Ошибка загрузки ${name} ALT: ${error.message}`);
            }
        }
    }

    /**
     * 🔥 ЗАГРУЗКА ИЗ КЭША
     */
    async loadFromCache() {
        try {
            if (!fs.existsSync(this.cacheFile)) {
                return false;
            }
            
            const cacheData = JSON.parse(fs.readFileSync(this.cacheFile, 'utf8'));
            
            // Проверяем актуальность кэша (6 часов)
            const cacheAge = Date.now() - cacheData.timestamp;
            const maxAge = 6 * 60 * 60 * 1000; // 6 часов
            
            if (cacheAge > maxAge) {
                console.log('⚠️ Кэш устарел, обновляем...');
                return false;
            }
            
            // Восстанавливаем ALT таблицы из кэша
            this.loadedALTTables = [];
            
            for (const cachedTable of cacheData.tables) {
                const altTable = {
                    key: new PublicKey(cachedTable.key),
                    state: {
                        addresses: cachedTable.addresses.map(addr => new PublicKey(addr))
                    }
                };
                
                this.loadedALTTables.push(altTable);
            }
            
            console.log(`✅ Загружено из кэша: ${this.loadedALTTables.length} ALT таблиц`);
            return true;
            
        } catch (error) {
            console.log(`⚠️ Ошибка загрузки кэша: ${error.message}`);
            return false;
        }
    }

    /**
     * 🔥 СОХРАНЕНИЕ В КЭШ
     */
    async saveToCache() {
        try {
            const cacheData = {
                timestamp: Date.now(),
                source: 'Official MarginFi SDK + Custom',
                tables: this.loadedALTTables.map(table => ({
                    key: table.key.toString(),
                    addresses: table.state.addresses.map(addr => addr.toString())
                }))
            };
            
            fs.writeFileSync(this.cacheFile, JSON.stringify(cacheData, null, 2));
            console.log(`💾 ALT таблицы сохранены в кэш: ${this.cacheFile}`);
            
        } catch (error) {
            console.log(`⚠️ Ошибка сохранения кэша: ${error.message}`);
        }
    }

    /**
     * 🔥 ПОЛУЧЕНИЕ ALT ТАБЛИЦ ДЛЯ КОМПИЛЯЦИИ ТРАНЗАКЦИЙ
     */
    getALTTablesForCompilation() {
        if (this.loadedALTTables.length === 0) {
            throw new Error('ALT таблицы не загружены! Вызовите loadAllALTTables() сначала.');
        }
        
        return this.loadedALTTables;
    }

    /**
     * 🔥 СТАТИСТИКА ALT ТАБЛИЦ
     */
    getStatistics() {
        const totalAddresses = this.loadedALTTables.reduce((sum, table) => 
            sum + table.state.addresses.length, 0
        );
        
        return {
            totalTables: this.loadedALTTables.length,
            totalAddresses,
            tables: this.loadedALTTables.map((table, index) => ({
                index,
                key: table.key.toString(),
                addresses: table.state.addresses.length
            }))
        };
    }

    /**
     * 🔥 ПРИНУДИТЕЛЬНОЕ ОБНОВЛЕНИЕ ИЗ БЛОКЧЕЙНА
     */
    async forceRefresh() {
        console.log('🔄 Принудительное обновление ALT таблиц...');
        
        // Удаляем кэш
        if (fs.existsSync(this.cacheFile)) {
            fs.unlinkSync(this.cacheFile);
        }
        
        // Очищаем загруженные таблицы
        this.loadedALTTables = [];
        
        // Загружаем заново
        return await this.loadAllALTTables();
    }

    /**
     * 🔥 ПРОВЕРКА ГОТОВНОСТИ
     */
    isReady() {
        return this.loadedALTTables.length > 0;
    }

    /**
     * 🔥 ОПИСАНИЕ СИСТЕМЫ
     */
    describe() {
        const stats = this.getStatistics();
        
        console.log('\n📊 ОФИЦИАЛЬНАЯ MARGINFI ALT СИСТЕМА:');
        console.log(`   📋 Всего таблиц: ${stats.totalTables}/4`);
        console.log(`   🔗 Всего адресов: ${stats.totalAddresses}`);
        console.log(`   ✅ Готовность: ${this.isReady() ? 'ГОТОВА' : 'НЕ ГОТОВА'}`);
        
        stats.tables.forEach(table => {
            const name = table.index < 3 ? `MarginFi ${table.index + 1}` : 'Custom';
            console.log(`   ${table.index + 1}. ${name}: ${table.addresses} адресов`);
        });
        
        return stats;
    }
}

module.exports = OfficialMarginFiALTLoader;
