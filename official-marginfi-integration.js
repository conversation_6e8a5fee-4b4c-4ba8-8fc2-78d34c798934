const { Connection, PublicKey, Keypair } = require('@solana/web3.js');
const { MarginfiClient, getConfig, MarginfiAccountWrapper } = require('@mrgnlabs/marginfi-client-v2');
const { NodeWallet } = require('@mrgnlabs/mrgn-common');

/**
 * 🔥 ОФИЦИАЛЬНАЯ ИНТЕГРАЦИЯ MARGINFI SDK
 * Цель: использовать правильные инструкции и структуру аккаунтов
 */

class OfficialMarginFiIntegration {
    constructor(connection, wallet) {
        this.connection = connection;
        this.wallet = wallet;
        this.marginfiClient = null;
        this.marginfiAccount = null;
        this.config = null;
    }

    /**
     * 🚀 ИНИЦИАЛИЗАЦИЯ ОФИЦИАЛЬНОГО MARGINFI CLIENT
     */
    async initialize() {
        try {
            console.log('🚀 Инициализация официального MarginFi SDK...');
            
            // Создаем NodeWallet из нашего кошелька
            const nodeWallet = {
                publicKey: this.wallet.publicKey,
                signTransaction: async (tx) => {
                    tx.sign(this.wallet);
                    return tx;
                },
                signAllTransactions: async (txs) => {
                    txs.forEach(tx => tx.sign(this.wallet));
                    return txs;
                }
            };
            
            // Получаем конфигурацию для production
            this.config = getConfig("production");
            console.log(`✅ Конфигурация загружена: ${this.config.environment}`);
            console.log(`   Program ID: ${this.config.programId.toString()}`);
            console.log(`   Group: ${this.config.groupPk.toString()}`);
            
            // Инициализируем MarginFi client
            this.marginfiClient = await MarginfiClient.fetch(
                this.config,
                nodeWallet,
                this.connection
            );
            
            console.log('✅ MarginFi Client инициализирован');
            console.log(`   Wallet: ${this.marginfiClient.wallet.publicKey.toString()}`);
            console.log(`   Group: ${this.marginfiClient.groupAddress.toString()}`);
            
            // 🎯 ИСПОЛЬЗУЕМ ВАШ СУЩЕСТВУЮЩИЙ MARGINFI АККАУНТ
            try {
                const existingAccounts = await this.marginfiClient.getMarginfiAccountsForAuthority();

                if (existingAccounts.length > 0) {
                    this.marginfiAccount = existingAccounts[0];
                    console.log(`✅ Найден существующий MarginFi аккаунт: ${this.marginfiAccount.address.toString()}`);
                } else {
                    // Если не найден через getMarginfiAccountsForAuthority, попробуем загрузить напрямую
                    const knownAccountAddress = new PublicKey('********************************************');
                    this.marginfiAccount = await MarginfiAccountWrapper.fetch(knownAccountAddress, this.marginfiClient);
                    console.log(`✅ Загружен известный MarginFi аккаунт: ${this.marginfiAccount.address.toString()}`);
                }
            } catch (error) {
                console.log(`⚠️ Ошибка загрузки MarginFi аккаунта: ${error.message}`);
                console.log('   Продолжаем без аккаунта для получения информации о банках');
            }
            
            return true;
            
        } catch (error) {
            console.error('❌ Ошибка инициализации MarginFi SDK:', error.message);
            throw error;
        }
    }

    /**
     * 🎯 СОЗДАНИЕ ПРАВИЛЬНОЙ BORROW ИНСТРУКЦИИ ЧЕРЕЗ ОФИЦИАЛЬНЫЙ SDK
     */
    async createOfficialBorrowInstruction(tokenSymbol, amount) {
        try {
            console.log(`\n🎯 Создание официальной borrow инструкции для ${tokenSymbol}...`);
            
            if (!this.marginfiAccount) {
                throw new Error('MarginFi аккаунт не инициализирован');
            }
            
            // Получаем банк по символу токена
            const bank = this.marginfiClient.getBankByTokenSymbol(tokenSymbol);
            if (!bank) {
                throw new Error(`Банк для ${tokenSymbol} не найден`);
            }
            
            console.log(`✅ Банк найден: ${bank.address.toString()}`);
            console.log(`   Mint: ${bank.mint.toString()}`);
            console.log(`   Symbol: ${bank.tokenSymbol || 'N/A'}`);
            
            // Создаем borrow инструкцию через официальный SDK
            const borrowIx = await this.marginfiAccount.makeBorrowIx(amount, bank.address);
            
            console.log('✅ Официальная borrow инструкция создана');
            console.log(`   Инструкций: ${borrowIx.instructions.length}`);
            
            // Выводим детальную информацию об инструкциях
            borrowIx.instructions.forEach((ix, index) => {
                console.log(`   Инструкция ${index}:`);
                console.log(`     Program: ${ix.programId.toString()}`);
                console.log(`     Аккаунтов: ${ix.keys.length}`);
                console.log(`     Data length: ${ix.data.length}`);
                console.log(`     Data (hex): ${ix.data.toString('hex')}`);
                
                // Выводим первые 8 байт как discriminator
                if (ix.data.length >= 8) {
                    const discriminator = ix.data.slice(0, 8);
                    console.log(`     Discriminator: [${Array.from(discriminator).join(', ')}]`);
                    console.log(`     Discriminator (hex): ${discriminator.toString('hex')}`);
                }
                
                // Выводим структуру аккаунтов
                console.log(`     Аккаунты:`);
                ix.keys.forEach((key, keyIndex) => {
                    console.log(`       ${keyIndex}: ${key.pubkey.toString()} (signer: ${key.isSigner}, writable: ${key.isWritable})`);
                });
            });
            
            return borrowIx.instructions;
            
        } catch (error) {
            console.error(`❌ Ошибка создания borrow инструкции:`, error.message);
            throw error;
        }
    }

    /**
     * 🎯 СОЗДАНИЕ ПРАВИЛЬНОЙ REPAY ИНСТРУКЦИИ ЧЕРЕЗ ОФИЦИАЛЬНЫЙ SDK
     */
    async createOfficialRepayInstruction(tokenSymbol, amount, repayAll = false) {
        try {
            console.log(`\n🎯 Создание официальной repay инструкции для ${tokenSymbol}...`);
            
            if (!this.marginfiAccount) {
                throw new Error('MarginFi аккаунт не инициализирован');
            }
            
            // Получаем банк по символу токена
            const bank = this.marginfiClient.getBankByTokenSymbol(tokenSymbol);
            if (!bank) {
                throw new Error(`Банк для ${tokenSymbol} не найден`);
            }
            
            console.log(`✅ Банк найден: ${bank.address.toString()}`);
            
            // Создаем repay инструкцию через официальный SDK
            const repayIx = await this.marginfiAccount.makeRepayIx(amount, bank.address, repayAll);
            
            console.log('✅ Официальная repay инструкция создана');
            console.log(`   Инструкций: ${repayIx.instructions.length}`);
            
            // Выводим детальную информацию об инструкциях
            repayIx.instructions.forEach((ix, index) => {
                console.log(`   Инструкция ${index}:`);
                console.log(`     Program: ${ix.programId.toString()}`);
                console.log(`     Аккаунтов: ${ix.keys.length}`);
                console.log(`     Data length: ${ix.data.length}`);
                console.log(`     Data (hex): ${ix.data.toString('hex')}`);
                
                // Выводим первые 8 байт как discriminator
                if (ix.data.length >= 8) {
                    const discriminator = ix.data.slice(0, 8);
                    console.log(`     Discriminator: [${Array.from(discriminator).join(', ')}]`);
                    console.log(`     Discriminator (hex): ${discriminator.toString('hex')}`);
                }
            });
            
            return repayIx.instructions;
            
        } catch (error) {
            console.error(`❌ Ошибка создания repay инструкции:`, error.message);
            throw error;
        }
    }

    /**
     * 🔥 СОЗДАНИЕ FLASH LOAN ТРАНЗАКЦИИ ЧЕРЕЗ ОФИЦИАЛЬНЫЙ SDK
     */
    async createOfficialFlashLoanTransaction(borrowInstructions, swapInstructions, repayInstructions) {
        try {
            console.log('\n🔥 Создание официальной Flash Loan транзакции...');
            
            if (!this.marginfiAccount) {
                throw new Error('MarginFi аккаунт не инициализирован');
            }
            
            // Объединяем все инструкции
            const allInstructions = [
                ...borrowInstructions,
                ...swapInstructions,
                ...repayInstructions
            ];
            
            console.log(`✅ Всего инструкций для Flash Loan: ${allInstructions.length}`);
            
            // Создаем Flash Loan транзакцию через официальный SDK
            const flashLoanTx = await this.marginfiAccount.buildFlashLoanTx({
                ixs: allInstructions,
                signers: []
            });
            
            console.log('✅ Официальная Flash Loan транзакция создана');
            console.log(`   Тип: ${flashLoanTx.constructor.name}`);
            
            return flashLoanTx;
            
        } catch (error) {
            console.error('❌ Ошибка создания Flash Loan транзакции:', error.message);
            throw error;
        }
    }

    /**
     * 📊 ПОЛУЧЕНИЕ ИНФОРМАЦИИ О БАНКАХ
     */
    async getBankInfo(tokenSymbol) {
        try {
            const bank = this.marginfiClient.getBankByTokenSymbol(tokenSymbol);
            if (!bank) {
                throw new Error(`Банк для ${tokenSymbol} не найден`);
            }
            
            return {
                address: bank.address,
                mint: bank.mint,
                symbol: bank.tokenSymbol,
                group: bank.group,
                liquidityVault: bank.liquidityVault,
                assetShareValue: bank.assetShareValue,
                liabilityShareValue: bank.liabilityShareValue
            };
            
        } catch (error) {
            console.error(`❌ Ошибка получения информации о банке ${tokenSymbol}:`, error.message);
            throw error;
        }
    }

    /**
     * 📋 ПОЛУЧЕНИЕ ВСЕХ ДОСТУПНЫХ БАНКОВ
     */
    async getAllBanks() {
        try {
            const banks = [];
            
            // Получаем все банки из client
            for (const [address, bank] of this.marginfiClient.banks) {
                banks.push({
                    address: address.toString(),
                    mint: bank.mint.toString(),
                    symbol: bank.tokenSymbol || 'Unknown',
                    group: bank.group.toString()
                });
            }
            
            return banks;
            
        } catch (error) {
            console.error('❌ Ошибка получения списка банков:', error.message);
            throw error;
        }
    }
}

module.exports = OfficialMarginFiIntegration;
