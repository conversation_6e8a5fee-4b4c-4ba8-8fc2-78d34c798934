/**
 * 🧹 PHANTOM DEBT CLEANER - АВТОМАТИЧЕСКАЯ ОЧИСТКА PHANTOM DEBT
 * 
 * Решение для MarginFi Error 6009 "RiskEngine rejected due to either bad health or stale oracles"
 * 
 * Автоматически:
 * - Проверяет health factor аккаунта
 * - Погашает phantom debt если найден
 * - Обновляет устаревшие oracle данные
 * - Создает новый аккаунт если очистка невозможна
 * 
 * <AUTHOR> Phantom Debt Fix Team
 * @version 1.0.0
 */

const { PublicKey } = require('@solana/web3.js');

class PhantomDebtCleaner {
  constructor(marginfiClient, wallet, connection) {
    this.marginfiClient = marginfiClient;
    this.wallet = wallet;
    this.connection = connection;
    
    // Константы для health factor
    this.MINIMUM_HEALTH_FACTOR = 1.1;  // Минимальный health factor для операций
    this.CRITICAL_HEALTH_FACTOR = 1.05; // Критический health factor
    this.SAFE_HEALTH_FACTOR = 1.5;     // Безопасный health factor
    
    // Константы для oracle
    this.MAX_ORACLE_AGE_SEC = 60;      // Максимальный возраст oracle данных
    
    this.stats = {
      totalCleanups: 0,
      successfulCleanups: 0,
      failedCleanups: 0,
      newAccountsCreated: 0,
      debtsCleared: 0
    };
  }

  /**
   * 🧹 ГЛАВНЫЙ МЕТОД: АВТОМАТИЧЕСКАЯ ОЧИСТКА PHANTOM DEBT
   */
  async cleanPhantomDebtBeforeFlashLoan(marginfiAccount) {
    console.log(`\n🧹 АВТОМАТИЧЕСКАЯ ОЧИСТКА PHANTOM DEBT`);
    console.log(`═══════════════════════════════════════════════════════════════`);
    
    this.stats.totalCleanups++;
    
    try {
      // 1. Проверяем текущее состояние аккаунта
      const healthCheck = await this.checkAccountHealth(marginfiAccount);
      
      if (healthCheck.isHealthy) {
        console.log(`✅ Аккаунт здоров: Health Factor = ${healthCheck.healthFactor.toFixed(4)}`);
        console.log(`💰 Assets: $${healthCheck.assets.toFixed(2)}`);
        console.log(`💸 Liabilities: $${healthCheck.liabilities.toFixed(2)}`);
        return { success: true, action: 'NO_CLEANUP_NEEDED', marginfiAccount };
      }

      console.log(`🚨 ОБНАРУЖЕН PHANTOM DEBT!`);
      console.log(`❤️ Health Factor: ${healthCheck.healthFactor.toFixed(4)} < ${this.MINIMUM_HEALTH_FACTOR}`);
      console.log(`💰 Assets: $${healthCheck.assets.toFixed(2)}`);
      console.log(`💸 Liabilities: $${healthCheck.liabilities.toFixed(2)}`);

      // 2. Пытаемся очистить phantom debt
      const cleanupResult = await this.clearPhantomDebt(marginfiAccount);
      
      if (cleanupResult.success) {
        console.log(`✅ PHANTOM DEBT ОЧИЩЕН УСПЕШНО!`);
        this.stats.successfulCleanups++;
        this.stats.debtsCleared++;
        return { success: true, action: 'DEBT_CLEARED', marginfiAccount };
      }

      // 3. Если очистка не удалась, создаем новый аккаунт
      console.log(`⚠️ Очистка phantom debt не удалась, создаем новый аккаунт...`);
      const newAccount = await this.createFreshAccount();
      
      if (newAccount) {
        console.log(`✅ НОВЫЙ ЧИСТЫЙ АККАУНТ СОЗДАН!`);
        console.log(`🆔 Новый аккаунт: ${newAccount.address.toString()}`);
        this.stats.successfulCleanups++;
        this.stats.newAccountsCreated++;
        return { success: true, action: 'NEW_ACCOUNT_CREATED', marginfiAccount: newAccount };
      }

      // 4. Если ничего не помогло
      console.log(`❌ Не удалось очистить phantom debt или создать новый аккаунт`);
      this.stats.failedCleanups++;
      return { success: false, action: 'CLEANUP_FAILED', error: 'Все методы очистки провалились' };

    } catch (error) {
      console.error(`❌ Ошибка автоматической очистки phantom debt: ${error.message}`);
      this.stats.failedCleanups++;
      return { success: false, action: 'CLEANUP_ERROR', error: error.message };
    }
  }

  /**
   * 🏥 ПРОВЕРКА HEALTH FACTOR АККАУНТА
   */
  async checkAccountHealth(marginfiAccount) {
    try {
      console.log(`🏥 Проверяем health factor аккаунта: ${marginfiAccount.address.toString()}`);
      
      // Обновляем данные аккаунта
      await marginfiAccount.reload();
      
      // Получаем health components (проверяем доступность метода)
      let healthComponents;
      if (typeof marginfiAccount.getHealthComponents === 'function') {
        healthComponents = marginfiAccount.getHealthComponents();
      } else if (typeof marginfiAccount.computeHealthComponents === 'function') {
        healthComponents = marginfiAccount.computeHealthComponents();
      } else {
        // Fallback: используем балансы напрямую
        console.log(`⚠️ getHealthComponents недоступен, используем fallback метод`);
        return this.calculateHealthFromBalances(marginfiAccount);
      }
      
      if (!healthComponents) {
        return {
          isHealthy: false,
          healthFactor: 0,
          assets: 0,
          liabilities: 0,
          reason: 'NO_HEALTH_DATA'
        };
      }

      const assets = healthComponents.assets.toNumber() / 1e6; // USD
      const liabilities = healthComponents.liabilities.toNumber() / 1e6; // USD
      
      // Вычисляем health factor по официальной формуле MarginFi
      const healthFactor = liabilities > 0 ? assets / (liabilities + 1) : 999;
      
      const isHealthy = healthFactor >= this.MINIMUM_HEALTH_FACTOR;
      
      let status = 'UNKNOWN';
      let reason = '';
      
      if (healthFactor >= this.SAFE_HEALTH_FACTOR) {
        status = 'EXCELLENT';
        reason = 'SAFE_HEALTH_FACTOR';
      } else if (healthFactor >= this.MINIMUM_HEALTH_FACTOR) {
        status = 'GOOD';
        reason = 'ACCEPTABLE_HEALTH_FACTOR';
      } else if (healthFactor >= this.CRITICAL_HEALTH_FACTOR) {
        status = 'WARNING';
        reason = 'LOW_HEALTH_FACTOR';
      } else {
        status = 'CRITICAL';
        reason = 'PHANTOM_DEBT_DETECTED';
      }

      return {
        isHealthy,
        healthFactor,
        assets,
        liabilities,
        status,
        reason
      };

    } catch (error) {
      console.error(`❌ Ошибка проверки health factor: ${error.message}`);
      return {
        isHealthy: false,
        healthFactor: 0,
        assets: 0,
        liabilities: 0,
        status: 'ERROR',
        reason: 'HEALTH_CHECK_ERROR',
        error: error.message
      };
    }
  }

  /**
   * 🧹 ОЧИСТКА PHANTOM DEBT
   */
  async clearPhantomDebt(marginfiAccount) {
    console.log(`🧹 Начинаем очистку phantom debt...`);
    
    try {
      // 1. Получаем все балансы с долгами
      const balances = marginfiAccount.balances;
      const debtBalances = [];
      
      for (const balance of balances) {
        if (balance.active && balance.liabilityShares.gt(0)) {
          const bank = this.marginfiClient.getBankByPk(balance.bankPk);
          if (bank) {
            debtBalances.push({ balance, bank });
            console.log(`💸 Найден долг в ${bank.tokenSymbol}: ${balance.liabilityShares.toString()} shares`);
          }
        }
      }

      if (debtBalances.length === 0) {
        console.log(`✅ Phantom debt не найден - аккаунт чист`);
        return { success: true, action: 'NO_DEBT_FOUND' };
      }

      console.log(`🎯 Найдено ${debtBalances.length} долгов для погашения`);

      // 2. Погашаем каждый долг с repayAll=true
      let clearedDebts = 0;
      
      for (const { balance, bank } of debtBalances) {
        try {
          console.log(`💸 Погашаем долг ${bank.tokenSymbol}...`);
          
          // Используем repayAll=true для полного погашения
          const signature = await marginfiAccount.repay(
            0,              // amount = 0 означает погасить все
            bank.address,   // bank address
            true            // repayAll = true (КРИТИЧЕСКИ ВАЖНО!)
          );

          console.log(`✅ Долг ${bank.tokenSymbol} погашен!`);
          console.log(`🔗 Транзакция: ${signature}`);
          clearedDebts++;

        } catch (repayError) {
          console.error(`❌ Ошибка погашения долга ${bank.tokenSymbol}: ${repayError.message}`);
          
          if (repayError.message.includes('insufficient funds')) {
            console.log(`💡 Недостаточно средств для погашения долга ${bank.tokenSymbol}`);
          }
        }
      }

      if (clearedDebts > 0) {
        console.log(`✅ Погашено ${clearedDebts} из ${debtBalances.length} долгов`);
        
        // Ждем подтверждения транзакций
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Проверяем результат
        const finalHealthCheck = await this.checkAccountHealth(marginfiAccount);
        
        if (finalHealthCheck.isHealthy) {
          console.log(`🎉 PHANTOM DEBT ПОЛНОСТЬЮ ОЧИЩЕН!`);
          console.log(`❤️ Новый Health Factor: ${finalHealthCheck.healthFactor.toFixed(4)}`);
          return { success: true, action: 'DEBT_CLEARED', clearedDebts };
        } else {
          console.log(`⚠️ Phantom debt частично очищен, но health factor все еще низкий`);
          return { success: false, action: 'PARTIAL_CLEANUP', clearedDebts };
        }
      } else {
        console.log(`❌ Не удалось погасить ни одного долга`);
        return { success: false, action: 'NO_DEBTS_CLEARED' };
      }

    } catch (error) {
      console.error(`❌ Ошибка очистки phantom debt: ${error.message}`);
      return { success: false, action: 'CLEANUP_ERROR', error: error.message };
    }
  }

  /**
   * 🆕 СОЗДАНИЕ НОВОГО ЧИСТОГО АККАУНТА
   */
  async createFreshAccount() {
    console.log(`🆕 Создаем новый чистый MarginFi аккаунт...`);
    
    try {
      const newAccount = await this.marginfiClient.createMarginfiAccount();
      
      console.log(`✅ Новый аккаунт создан: ${newAccount.address.toString()}`);
      console.log(`🏥 Health Factor: ∞ (нет долгов)`);
      console.log(`💰 Assets: $0.00`);
      console.log(`💸 Liabilities: $0.00`);
      
      return newAccount;
      
    } catch (error) {
      console.error(`❌ Ошибка создания нового аккаунта: ${error.message}`);
      return null;
    }
  }

  /**
   * 🔄 FALLBACK РАСЧЕТ HEALTH FACTOR ИЗ БАЛАНСОВ
   */
  calculateHealthFromBalances(marginfiAccount) {
    try {
      const balances = marginfiAccount.balances || [];
      let totalAssets = 0;
      let totalLiabilities = 0;

      for (const balance of balances) {
        if (balance.active) {
          const bank = this.marginfiClient.getBankByPk(balance.bankPk);
          if (bank) {
            const oraclePrice = this.marginfiClient.getOraclePriceByBank(bank.address);
            if (oraclePrice) {
              const { assets, liabilities } = balance.computeUsdValue(bank, oraclePrice);
              totalAssets += assets.toNumber() / 1e6;
              totalLiabilities += liabilities.toNumber() / 1e6;
            }
          }
        }
      }

      const healthFactor = totalLiabilities > 0 ? totalAssets / (totalLiabilities + 1) : 999;
      const isHealthy = healthFactor >= this.MINIMUM_HEALTH_FACTOR;

      return {
        isHealthy,
        healthFactor,
        assets: totalAssets,
        liabilities: totalLiabilities,
        status: isHealthy ? 'GOOD' : 'CRITICAL',
        reason: isHealthy ? 'ACCEPTABLE_HEALTH_FACTOR' : 'PHANTOM_DEBT_DETECTED'
      };

    } catch (error) {
      console.error(`❌ Ошибка fallback расчета health factor: ${error.message}`);
      return {
        isHealthy: false,
        healthFactor: 0,
        assets: 0,
        liabilities: 0,
        status: 'ERROR',
        reason: 'FALLBACK_CALCULATION_ERROR'
      };
    }
  }

  /**
   * 📊 СТАТИСТИКА ОЧИСТКИ
   */
  getStats() {
    return {
      ...this.stats,
      successRate: this.stats.totalCleanups > 0
        ? (this.stats.successfulCleanups / this.stats.totalCleanups * 100).toFixed(1) + '%'
        : '0%'
    };
  }

  /**
   * 📋 ВЫВОД СТАТИСТИКИ
   */
  printStats() {
    const stats = this.getStats();
    console.log(`\n📊 СТАТИСТИКА PHANTOM DEBT CLEANER:`);
    console.log(`═══════════════════════════════════════════════════════════════`);
    console.log(`🧹 Всего очисток: ${stats.totalCleanups}`);
    console.log(`✅ Успешных: ${stats.successfulCleanups}`);
    console.log(`❌ Неудачных: ${stats.failedCleanups}`);
    console.log(`📈 Успешность: ${stats.successRate}`);
    console.log(`💸 Долгов погашено: ${stats.debtsCleared}`);
    console.log(`🆕 Новых аккаунтов: ${stats.newAccountsCreated}`);
  }
}

module.exports = { PhantomDebtCleaner };
