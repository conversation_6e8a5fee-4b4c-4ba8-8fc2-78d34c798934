devnetType: docker

defaultFee: 2000
defaultStake: 10000

borChainId: 15001
borDockerBuildContext: '../../bor'

heimdallChainId: heimdall-15001
heimdallDockerBuildContext: 'https://github.com/0xPolygon/heimdall-v2.git#develop'

genesisContractsRepo: https://github.com/maticnetwork/genesis-contracts.git
genesisContractsBranch: master

contractsRepo: https://github.com/0xPolygon/pos-contracts.git
contractsBranch: anvil-pos

blockTime:
  - '2'
blockNumber:
  - '0'

sprintSize:
  - '64'
sprintSizeBlockNumber:
  - '0'

numOfBorValidators: 3
numOfBorSentries: 0
numOfBorArchiveNodes: 0

devnetBorFlags: config,config,config

numOfErigonValidators: 0
numOfErigonSentries: 0
numOfErigonArchiveNodes: 0

ethHostUser: ubuntu
ethURL: http://anvil:9545

mnemonic: 'clock radar mass judge dismiss just intact mind resemble fringe diary casino'
