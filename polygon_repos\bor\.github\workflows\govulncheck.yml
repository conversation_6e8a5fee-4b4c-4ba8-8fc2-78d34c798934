name: Govuln
on: [push, pull_request]

jobs:
  govulncheck:
    name: Run govulncheck
    runs-on: ubuntu-latest
    steps:
      - uses: actions/setup-go@v5
        with:
          go-version: '1.24.3'
          check-latest: true
      - uses: actions/checkout@v4
      - uses: technote-space/get-diff-action@v6
        with:
          PATTERNS: |
            **/*.go
            go.mod
            go.sum
            Makefile
      - name: govulncheck
        run: make vulncheck
        if: env.GIT_DIFF != ''
