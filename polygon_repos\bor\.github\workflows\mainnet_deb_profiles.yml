name: mainnet_deb_profiles

on:
  push:
    branches:
      - 'master'
    paths:
      - '**'
    tags:
      - 'v*.*.*'
      - 'v*.*.*-*'

jobs:
  build:
    permissions:
      id-token: write
      contents: write
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      # Variables
      - name: Adding TAG to ENV
        run: echo "GIT_TAG=`echo $(git describe --tags --abbrev=0)`" >> $GITHUB_ENV
      - name: adding version
        run: |
          NUMERIC_VERSION=$( echo ${{ env.GIT_TAG }} | sed 's/[^0-9.]//g' ) 
          echo "VERSION=$NUMERIC_VERSION" >> $GITHUB_ENV

      - name: Making directory structure for yaml
        run: mkdir -p packaging/deb/bor/var/lib/bor
      - name: making directory structure for the systemd
        run: mkdir -p packaging/deb/bor/lib/systemd/system
      - name: Delete control file
        run: rm -rf packaging/deb/bor/DEBIAN/control

      # Control file creation
      - name: create control file
        run: |
          touch packaging/deb/bor/DEBIAN/control
          echo "Package: bor-profile" >> packaging/deb/bor/DEBIAN/control
          echo "Version: ${{ env.VERSION }}" >> packaging/deb/bor/DEBIAN/control
          echo "Section: base" >> packaging/deb/bor/DEBIAN/control
          echo "Priority: optional" >> packaging/deb/bor/DEBIAN/control
          echo "Architecture: all" >> packaging/deb/bor/DEBIAN/control
          echo "Maintainer: <EMAIL>" >> packaging/deb/bor/DEBIAN/control
          echo "Description: bor profile package" >> packaging/deb/bor/DEBIAN/control

      # Bootnode Profile
      - name: Prepping ${{ env.NETWORK }} ${{ env.NODE }} bootnode for ${{ env.ARCH }}
        run: cp -rp  packaging/deb/bor packaging/deb/bor-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}
        env:
          ARCH: all
          NODE: bootnode
          NETWORK: mainnet
      - name: Putting toml for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: cp -rp packaging/templates/mainnet-v1/sentry/sentry/bor/config.toml packaging/deb/bor-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/var/lib/bor/
        env:
          ARCH: all
          NODE: bootnode
          NETWORK: mainnet
      - name: Copying the preinst for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: cp -rp packaging/templates/package_scripts/preinst packaging/deb/bor-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/DEBIAN/preinst
        env:
          ARCH: all
          NODE: bootnode
          NETWORK: mainnet
      - name: Copying the postinst for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: cp -rp packaging/templates/package_scripts/postinst.profile packaging/deb/bor-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/DEBIAN/postinst
        env:
          ARCH: all
          NODE: bootnode
          NETWORK: mainnet
      - name: Copying the prerm for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: cp -rp packaging/templates/package_scripts/prerm packaging/deb/bor-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/DEBIAN/prerm
        env:
          ARCH: all
          NODE: bootnode
          NETWORK: mainnet
      - name: Copying the postrm for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: cp -rp packaging/templates/package_scripts/postrm packaging/deb/bor-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/DEBIAN/postrm
        env:
          ARCH: all
          NODE: bootnode
          NETWORK: mainnet
      - name: Copying systemd file for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: cp -rp packaging/templates/systemd/bor_bootnode.service packaging/deb/bor-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/lib/systemd/system/bor.service
        env:
          ARCH: all
          NODE: bootnode
          NETWORK: mainnet
      - name: Running package build for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: dpkg-deb --build --root-owner-group packaging/deb/bor-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}
        env:
          ARCH: all
          NODE: bootnode
          NETWORK: mainnet

      # Sentry Profile
      - name: Setting up ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: cp -rp packaging/deb/bor packaging/deb/bor-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}
        env:
          ARCH: all
          NODE: sentry
          NETWORK: mainnet
      - name: Putting toml for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: cp -rp packaging/templates/mainnet-v1/sentry/sentry/bor/config.toml packaging/deb/bor-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/var/lib/bor/
        env:
          ARCH: all
          NODE: sentry
          NETWORK: mainnet
      - name: Copying the preinst for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: cp -rp packaging/templates/package_scripts/preinst packaging/deb/bor-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/DEBIAN/preinst
        env:
          ARCH: all
          NODE: sentry
          NETWORK: mainnet
      - name: Copying the postinst for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: cp -rp packaging/templates/package_scripts/postinst.profile packaging/deb/bor-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/DEBIAN/postinst
        env:
          ARCH: all
          NODE: sentry
          NETWORK: mainnet
      - name: Copying the prerm for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: cp -rp packaging/templates/package_scripts/prerm packaging/deb/bor-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/DEBIAN/prerm
        env:
          ARCH: all
          NODE: sentry
          NETWORK: mainnet
      - name: Copying the postrm for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: cp -rp packaging/templates/package_scripts/postrm packaging/deb/bor-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/DEBIAN/postrm
        env:
          ARCH: all
          NODE: sentry
          NETWORK: mainnet
      - name: Copying systemd file for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: cp -rp packaging/templates/systemd/bor.service packaging/deb/bor-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/lib/systemd/system/
        env:
          ARCH: all
          NODE: sentry
          NETWORK: mainnet
      - name: Building ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }} profile
        run: dpkg-deb --build --root-owner-group packaging/deb/bor-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}
        env:
          ARCH: all
          NODE: sentry
          NETWORK: mainnet

      # PBSS Sentry
      - name: Setting up ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: cp -rp packaging/deb/bor packaging/deb/bor-pbss-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}
        env:
          ARCH: all
          NODE: sentry
          NETWORK: mainnet
      - name: Putting toml for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: cp -rp packaging/templates/mainnet-v1/sentry/sentry/bor/pbss_config.toml packaging/deb/bor-pbss-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/var/lib/bor/config.toml
        env:
          ARCH: all
          NODE: sentry
          NETWORK: mainnet
      - name: Copying the preinst for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: cp -rp packaging/templates/package_scripts/preinst packaging/deb/bor-pbss-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/DEBIAN/preinst
        env:
          ARCH: all
          NODE: sentry
          NETWORK: mainnet
      - name: Copying the postinst for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: cp -rp packaging/templates/package_scripts/postinst.profile packaging/deb/bor-pbss-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/DEBIAN/postinst
        env:
          ARCH: all
          NODE: sentry
          NETWORK: mainnet
      - name: Copying the prerm for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: cp -rp packaging/templates/package_scripts/prerm packaging/deb/bor-pbss-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/DEBIAN/prerm
        env:
          ARCH: all
          NODE: sentry
          NETWORK: mainnet
      - name: Copying the postrm for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: cp -rp packaging/templates/package_scripts/postrm packaging/deb/bor-pbss-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/DEBIAN/postrm
        env:
          ARCH: all
          NODE: sentry
          NETWORK: mainnet
      - name: Copying systemd file for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: cp -rp packaging/templates/systemd/bor.service packaging/deb/bor-pbss-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/lib/systemd/system/
        env:
          ARCH: all
          NODE: sentry
          NETWORK: mainnet
      - name: Building ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }} profile
        run: dpkg-deb --build --root-owner-group packaging/deb/bor-pbss-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}
        env:
          ARCH: all
          NODE: sentry
          NETWORK: mainnet

      # Validator Profile
      - name: Prepping Bor ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: cp -rp packaging/deb/bor packaging/deb/bor-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}
        env:
          ARCH: all
          NODE: validator
          NETWORK: mainnet
      - name: Copying the preinst for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: cp -rp packaging/templates/package_scripts/preinst packaging/deb/bor-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/DEBIAN/preinst
        env:
          ARCH: all
          NODE: validator
          NETWORK: mainnet
      - name: Copying Postinstall script for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: cp -rp packaging/templates/package_scripts/postinst.profile packaging/deb/bor-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/DEBIAN/postinst
        env:
          ARCH: all
          NODE: validator
          NETWORK: mainnet
      - name: Copying the prerm for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: cp -rp packaging/templates/package_scripts/prerm packaging/deb/bor-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/DEBIAN/prerm
        env:
          ARCH: all
          NODE: validator
          NETWORK: mainnet
      - name: Copying the postrm for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: cp -rp packaging/templates/package_scripts/postrm packaging/deb/bor-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/DEBIAN/postrm
        env:
          ARCH: all
          NODE: validator
          NETWORK: mainnet
      - name: Copying systemd file for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: cp -rp packaging/templates/systemd/bor.service packaging/deb/bor-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/lib/systemd/system/
        env:
          ARCH: all
          NODE: validator
          NETWORK: mainnet
      - name: Copying config.toml for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: cp -rp packaging/templates/mainnet-v1/sentry/validator/bor/config.toml packaging/deb/bor-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/var/lib/bor/
        env:
          ARCH: all
          NODE: validator
          NETWORK: mainnet
      - name: Building bor ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }} profile
        run: dpkg-deb --build --root-owner-group packaging/deb/bor-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}
        env:
          ARCH: all
          NODE: validator
          NETWORK: mainnet

      # PBSS Validator
      - name: Prepping Bor ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: cp -rp packaging/deb/bor packaging/deb/bor-pbss-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}
        env:
          ARCH: all
          NODE: validator
          NETWORK: mainnet
      - name: Copying the preinst for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: cp -rp packaging/templates/package_scripts/preinst packaging/deb/bor-pbss-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/DEBIAN/preinst
        env:
          ARCH: all
          NODE: validator
          NETWORK: mainnet
      - name: Copying Postinstall script for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: cp -rp packaging/templates/package_scripts/postinst.profile packaging/deb/bor-pbss-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/DEBIAN/postinst
        env:
          ARCH: all
          NODE: validator
          NETWORK: mainnet
      - name: Copying the prerm for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: cp -rp packaging/templates/package_scripts/prerm packaging/deb/bor-pbss-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/DEBIAN/prerm
        env:
          ARCH: all
          NODE: validator
          NETWORK: mainnet
      - name: Copying the postrm for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: cp -rp packaging/templates/package_scripts/postrm packaging/deb/bor-pbss-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/DEBIAN/postrm
        env:
          ARCH: all
          NODE: validator
          NETWORK: mainnet
      - name: Copying systemd file for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: cp -rp packaging/templates/systemd/bor.service packaging/deb/bor-pbss-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/lib/systemd/system/
        env:
          ARCH: all
          NODE: validator
          NETWORK: mainnet
      - name: Copying config.toml for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: cp -rp packaging/templates/mainnet-v1/sentry/validator/bor/pbss_config.toml packaging/deb/bor-pbss-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/var/lib/bor/config.toml
        env:
          ARCH: all
          NODE: validator
          NETWORK: mainnet
      - name: Building bor ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }} profile
        run: dpkg-deb --build --root-owner-group packaging/deb/bor-pbss-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}
        env:
          ARCH: all
          NODE: validator
          NETWORK: mainnet

      # Archive Profile
      - name: Creating bor ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: cp -rp packaging/deb/bor packaging/deb/bor-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}
        env:
          ARCH: all
          NODE: archive
          NETWORK: mainnet
      - name: Copying profile preinst file for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: cp -rp packaging/templates/package_scripts/preinst packaging/deb/bor-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/DEBIAN/preinst
        env:
          ARCH: all
          NODE: archive
          NETWORK: mainnet
      - name: Copying the profile postinst for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: cp -rp packaging/templates/package_scripts/postinst.profile packaging/deb/bor-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/DEBIAN/postinst
        env:
          ARCH: all
          NODE: archive
          NETWORK: mainnet
      - name: Copying profile prerm file for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: cp -rp packaging/templates/package_scripts/prerm packaging/deb/bor-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/DEBIAN/prerm
        env:
          ARCH: all
          NODE: archive
          NETWORK: mainnet
      - name: Copying profile postrm file for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: cp -rp packaging/templates/package_scripts/postrm packaging/deb/bor-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/DEBIAN/postrm
        env:
          ARCH: all
          NODE: archive
          NETWORK: mainnet
      - name: Copying systemd file for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: cp -rp packaging/templates/systemd/bor.service packaging/deb/bor-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/lib/systemd/system/
        env:
          ARCH: all
          NODE: archive
          NETWORK: mainnet
      - name: Copying the toml for ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }}
        run: cp -rp packaging/templates/mainnet-v1/archive/config.toml packaging/deb/bor-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}/var/lib/bor/
        env:
          ARCH: all
          NODE: archive
          NETWORK: mainnet
      - name: Building bor ${{ env.NODE }} on ${{ env.NETWORK }} on ${{ env.ARCH }} profile
        run: dpkg-deb --build --root-owner-group packaging/deb/bor-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}
        env:
          ARCH: all
          NODE: archive
          NETWORK: mainnet

      # Shasum
      - name: shasum the bor debian package
        run: cd packaging/deb/ && sha256sum bor-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}.deb > bor-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}.deb.checksum
        env:
          ARCH: all
          NODE: bootnode
          NETWORK: mainnet
      - name: shasum the bor debian package
        run: cd packaging/deb/ && sha256sum bor-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}.deb > bor-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}.deb.checksum
        env:
          ARCH: all
          NODE: sentry
          NETWORK: mainnet
      - name: shasum the bor pbss debian package
        run: cd packaging/deb/ && sha256sum bor-pbss-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}.deb > bor-pbss-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}.deb.checksum
        env:
          ARCH: all
          NODE: sentry
          NETWORK: mainnet
      - name: shasum the bor-${{ env.ARCH }} debian package
        run: cd packaging/deb/ && sha256sum bor-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}.deb > bor-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}.deb.checksum
        env:
          ARCH: all
          NODE: validator
          NETWORK: mainnet
      - name: shasum the bor pbss debian package
        run: cd packaging/deb/ && sha256sum bor-pbss-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}.deb > bor-pbss-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}.deb.checksum
        env:
          ARCH: all
          NODE: validator
          NETWORK: mainnet
      - name: shasum the bor debian package
        run: cd packaging/deb/ && sha256sum bor-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}.deb > bor-${{ env.NETWORK }}-${{ env.NODE }}-config_${{ env.GIT_TAG }}-${{ env.ARCH }}.deb.checksum
        env:
          ARCH: all
          NODE: archive
          NETWORK: mainnet

      ############ Check and Upload ##########################
      - name: Confirming package built
        run: ls -ltr packaging/deb/ | grep bor

      - name: Release bor Packages
        uses: softprops/action-gh-release@v2
        with:
          tag_name: ${{ env.GIT_TAG }}
          make_latest: false
          files: |
            packaging/deb/bor-mainnet-**.deb
            packaging/deb/bor-pbss-mainnet-**.deb
            packaging/deb/bor-mainnet-**.deb.checksum
            packaging/deb/bor-pbss-mainnet-**.deb.checksum
