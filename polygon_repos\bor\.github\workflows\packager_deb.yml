name: packager_deb

on:
  push:
    branches:
      - 'master'
    paths:
      - '**'
    tags:
      - 'v*.*.*'
      - 'v*.*.*-*'

jobs:
  build:
    permissions:
      id-token: write
      contents: write
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Set up Go
        uses: actions/setup-go@master
        with:
          go-version: 1.24.3
      # Variables
      - name: Adding TAG to ENV
        run: echo "GIT_TAG=`echo $(git describe --tags --abbrev=0)`" >> $GITHUB_ENV
      - name: adding version
        run: |
          NUMERIC_VERSION=$( echo ${{ env.GIT_TAG }} | sed 's/[^0-9.]//g' ) 
          echo "VERSION=$NUMERIC_VERSION" >> $GITHUB_ENV

      - name: Cleaning repo
        run: make clean
      - name: Building for amd64
        run: make bor

      - name: Making directory structure
        run: mkdir -p packaging/deb/bor/usr/bin
      - name: Copying necessary files
        run: cp -rp build/bin/bor packaging/deb/bor/usr/bin/
      - name: Delete control file
        run: rm -rf packaging/deb/bor/DEBIAN/control

      # Control file creation
      - name: create control file
        run: |
          touch packaging/deb/bor/DEBIAN/control
          echo "Package: bor" >> packaging/deb/bor/DEBIAN/control
          echo "Version: ${{ env.VERSION }}" >> packaging/deb/bor/DEBIAN/control
          echo "Section: base" >> packaging/deb/bor/DEBIAN/control
          echo "Priority: optional" >> packaging/deb/bor/DEBIAN/control
          echo "Architecture: amd64" >> packaging/deb/bor/DEBIAN/control
          echo "Maintainer: <EMAIL>" >> packaging/deb/bor/DEBIAN/control
          echo "Description: bor binary package" >> packaging/deb/bor/DEBIAN/control

      - name: Creating package for binary for bor ${{ env.ARCH }}
        run: cp -rp packaging/deb/bor packaging/deb/bor-${{ env.GIT_TAG }}-${{ env.ARCH }}
        env:
          ARCH: amd64

      - name: Running package build
        run: dpkg-deb --build --root-owner-group packaging/deb/bor-${{ env.GIT_TAG }}-${{ env.ARCH }}
        env:
          ARCH: amd64

      - name: Cleaning build directory for arm64 build
        run: make clean

      - name: Updating the apt-get
        run: sudo apt-get update -y

      - name: Adding requirements for cross compile
        run: sudo apt-get install g++-aarch64-linux-gnu gcc-aarch64-linux-gnu

      - name: delete amd64 control file
        run: rm -rf packaging/deb/bor/DEBIAN/control

      - name: Building bor for arm64
        run: GOARCH=arm64 GOOS=linux CC=aarch64-linux-gnu-gcc CXX=aarch64-linux-gnu-g++ CGO_ENABLED=1 go build -o build/bin/bor ./cmd/cli/main.go

      - name: Copying necessary binary post arm64 build
        run: cp -rp build/bin/bor packaging/deb/bor/usr/bin/

      # Control file for arm64 creation
      - name: create control file
        run: |
          touch packaging/deb/bor/DEBIAN/control
          echo "Package: bor" >> packaging/deb/bor/DEBIAN/control
          echo "Version: ${{ env.VERSION }}" >> packaging/deb/bor/DEBIAN/control
          echo "Section: base" >> packaging/deb/bor/DEBIAN/control
          echo "Priority: optional" >> packaging/deb/bor/DEBIAN/control
          echo "Architecture: arm64" >> packaging/deb/bor/DEBIAN/control
          echo "Maintainer: <EMAIL>" >> packaging/deb/bor/DEBIAN/control
          echo "Description: bor binary package" >> packaging/deb/bor/DEBIAN/control

      - name: Creating package for binary for bor ${{ env.ARCH }}
        run: cp -rp packaging/deb/bor packaging/deb/bor-${{ env.GIT_TAG }}-${{ env.ARCH }}
        env:
          ARCH: arm64

      - name: Running package build
        run: dpkg-deb --build --root-owner-group packaging/deb/bor-${{ env.GIT_TAG }}-${{ env.ARCH }}
        env:
          ARCH: arm64

      # Shasum
      - name: shasum the bor debian package
        run: cd packaging/deb/ && sha256sum bor-${{ env.GIT_TAG }}-${{ env.ARCH }}.deb > bor-${{ env.GIT_TAG }}-${{ env.ARCH }}.deb.checksum
        env:
          ARCH: amd64

      - name: shasum the bor debian package
        run: cd packaging/deb/ && sha256sum bor-${{ env.GIT_TAG }}-${{ env.ARCH }}.deb > bor-${{ env.GIT_TAG }}-${{ env.ARCH }}.deb.checksum
        env:
          ARCH: arm64

      - name: Release bor Packages
        uses: softprops/action-gh-release@v2
        with:
          tag_name: ${{ env.GIT_TAG }}
          make_latest: false
          files: |
            packaging/deb/bor**.deb
            packaging/deb/bor**.deb.checksum
