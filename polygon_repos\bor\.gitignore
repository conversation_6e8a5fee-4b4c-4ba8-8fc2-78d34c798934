# See http://help.github.com/ignore-files/ for more about ignoring files.
#
# If you find yourself ignoring temporary files generated by your text editor
# or operating system, you probably want to add a global ignore instead:
#   git config --global core.excludesfile ~/.gitignore_global

*/**/*un~
*/**/*.test
*un~
.DS_Store
*/**/.DS_Store

cover.out

#*
.#*
*#
*~
.project
.settings

# used by the Makefile
/build/_workspace/
/build/cache/
/build/bin/
/geth*.zip

# used by the build/ci.go archive + upload tool
/geth*.tar.gz
/geth*.tar.gz.sig
/geth*.tar.gz.asc
/geth*.zip.sig
/geth*.zip.asc


# travis
profile.tmp
profile.cov

# IdeaIDE
.idea
*.iml

# VS Code
.vscode

tests/spec-tests/

# binaries
cmd/abidump/abidump
cmd/abigen/abigen
cmd/blsync/blsync
cmd/clef/clef
cmd/devp2p/devp2p
cmd/era/era
cmd/ethkey/ethkey
cmd/evm/evm
cmd/geth/geth
cmd/rlpdump/rlpdump
cmd/workload/workload