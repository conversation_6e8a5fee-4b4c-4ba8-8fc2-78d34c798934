project_name: bor

release:
  disable: true
  draft: true
  prerelease: auto

builds:
  - id: darwin-amd64
    main: ./cmd/cli
    binary: bor
    goos:
      - darwin
    goarch:
      - amd64
    env:
      - CC=o64-clang
      - CXX=o64-clang++
    tags:
      - netgo
    ldflags:
      -s -w

  - id: darwin-arm64
    main: ./cmd/cli
    binary: bor
    goos:
      - darwin
    goarch:
      - arm64
    env:
      - CC=oa64-clang
      - CXX=oa64-clang++
    tags:
      - netgo
    ldflags:
      -s -w

  - id: linux-amd64
    main: ./cmd/cli
    binary: bor
    goos:
      - linux
    goarch:
      - amd64
    env:
      - CC=gcc
      - CXX=g++
    tags:
      - netgo
    ldflags:
      # We need to build a static binary because we are building in a glibc based system and running in a musl container 
      -s -w -extldflags "-static"

  - id: linux-arm64
    main: ./cmd/cli
    binary: bor
    goos:
      - linux
    goarch:
      - arm64
    env:
      - CC=aarch64-linux-gnu-gcc
      - CXX=aarch64-linux-gnu-g++
    tags:
      - netgo
    ldflags:
      # We need to build a static binary because we are building in a glibc based system and running in a musl container 
      -s -w -extldflags "-static"

nfpms:
  - vendor: 0xPolygon
    homepage: https://polygon.technology
    maintainer: Polygon Team <<EMAIL>>
    description: Polygon Blockchain
    license: GPLv3 LGPLv3

    bindir: /usr/local/bin

    formats:
      - apk
      - deb
      - rpm

    contents:
      - dst: /var/lib/bor
        type: dir
        file_info:
          mode: 0777
      - src: builder/files/bor.service
        dst: /lib/systemd/system/bor.service
        type: config
      - src: builder/files/genesis-mainnet-v1.json
        dst: /etc/bor/genesis-mainnet-v1.json
        type: config
      - src: builder/files/genesis-testnet-v4.json
        dst: /etc/bor/genesis-testnet-v4.json
        type: config
      - src: builder/files/genesis-amoy.json
        dst: /etc/bor/genesis-amoy.json
        type: config
      - src: builder/files/config.toml
        dst: /var/lib/bor/config.toml
        type: config

    scripts:
      postinstall: builder/files/bor-post-install.sh

snapshot:
  name_template: "{{ .Tag }}.next"

dockers:
  - image_templates:
      - 0xpolygon/{{ .ProjectName }}:{{ .Version }}-amd64
    dockerfile: Dockerfile.release
    use: buildx
    goarch: amd64
    ids:
      - linux-amd64
    build_flag_templates:
      - --platform=linux/amd64
    extra_files:
      - builder/files/genesis-mainnet-v1.json
      - builder/files/genesis-testnet-v4.json
      - builder/files/genesis-amoy.json

  - image_templates:
      - 0xpolygon/{{ .ProjectName }}:{{ .Version }}-arm64
    dockerfile: Dockerfile.release
    use: buildx
    goarch: arm64
    ids:
      - linux-arm64
    build_flag_templates:
      - --platform=linux/arm64/v8
    extra_files:
      - builder/files/genesis-mainnet-v1.json
      - builder/files/genesis-testnet-v4.json
      - builder/files/genesis-amoy.json

docker_manifests:
  - name_template: 0xpolygon/{{ .ProjectName }}:{{ .Version }}
    image_templates:
      - 0xpolygon/{{ .ProjectName }}:{{ .Version }}-amd64
      - 0xpolygon/{{ .ProjectName }}:{{ .Version }}-arm64

  - name_template: 0xpolygon/{{ .ProjectName }}:latest
    image_templates:
    - 0xpolygon/{{ .ProjectName }}:{{ .Version }}-amd64
    - 0xpolygon/{{ .ProjectName }}:{{ .Version }}-arm64

announce:
  slack:
    enabled: true
    # The name of the channel that the user selected as a destination for webhook messages.
    channel: '#code-releases'
