# Build Geth in a stock Go builder container
FROM golang:1.24-alpine AS builder

RUN apk add --no-cache make gcc musl-dev linux-headers git

ADD . /bor
RUN cd /bor && make bor-all

# Pull all binaries into a second stage deploy alpine container
FROM alpine:latest

RUN set -x \
    && apk add --update --no-cache \
    ca-certificates \
    && rm -rf /var/cache/apk/*
COPY --from=builder /bor/build/bin/* /usr/bin/

EXPOSE 8545 8546 30303 30303/udp
