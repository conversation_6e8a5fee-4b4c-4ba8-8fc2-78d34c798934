// Code generated via abigen V2 - DO NOT EDIT.
// This file is a generated binding and any manual changes will be lost.

package bindtests

import (
	"bytes"
	"errors"
	"math/big"

	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/accounts/abi/bind/v2"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
)

// Reference imports to suppress errors if they are not otherwise used.
var (
	_ = bytes.Equal
	_ = errors.New
	_ = big.NewInt
	_ = common.Big1
	_ = types.BloomLookup
	_ = abi.ConvertType
)

// OutputCheckerMetaData contains all meta data concerning the OutputChecker contract.
var OutputCheckerMetaData = bind.MetaData{
	ABI: "[{\"type\":\"function\",\"name\":\"noOutput\",\"constant\":true,\"inputs\":[],\"outputs\":[]},{\"type\":\"function\",\"name\":\"namedOutput\",\"constant\":true,\"inputs\":[],\"outputs\":[{\"name\":\"str\",\"type\":\"string\"}]},{\"type\":\"function\",\"name\":\"anonOutput\",\"constant\":true,\"inputs\":[],\"outputs\":[{\"name\":\"\",\"type\":\"string\"}]},{\"type\":\"function\",\"name\":\"namedOutputs\",\"constant\":true,\"inputs\":[],\"outputs\":[{\"name\":\"str1\",\"type\":\"string\"},{\"name\":\"str2\",\"type\":\"string\"}]},{\"type\":\"function\",\"name\":\"collidingOutputs\",\"constant\":true,\"inputs\":[],\"outputs\":[{\"name\":\"str\",\"type\":\"string\"},{\"name\":\"Str\",\"type\":\"string\"}]},{\"type\":\"function\",\"name\":\"anonOutputs\",\"constant\":true,\"inputs\":[],\"outputs\":[{\"name\":\"\",\"type\":\"string\"},{\"name\":\"\",\"type\":\"string\"}]},{\"type\":\"function\",\"name\":\"mixedOutputs\",\"constant\":true,\"inputs\":[],\"outputs\":[{\"name\":\"\",\"type\":\"string\"},{\"name\":\"str\",\"type\":\"string\"}]}]",
	ID:  "cc1d4e235801a590b506d5130b0cca90a1",
}

// OutputChecker is an auto generated Go binding around an Ethereum contract.
type OutputChecker struct {
	abi abi.ABI
}

// NewOutputChecker creates a new instance of OutputChecker.
func NewOutputChecker() *OutputChecker {
	parsed, err := OutputCheckerMetaData.ParseABI()
	if err != nil {
		panic(errors.New("invalid ABI: " + err.Error()))
	}
	return &OutputChecker{abi: *parsed}
}

// Instance creates a wrapper for a deployed contract instance at the given address.
// Use this to create the instance object passed to abigen v2 library functions Call, Transact, etc.
func (c *OutputChecker) Instance(backend bind.ContractBackend, addr common.Address) *bind.BoundContract {
	return bind.NewBoundContract(addr, c.abi, backend, backend, backend)
}

// PackAnonOutput is the Go binding used to pack the parameters required for calling
// the contract method with ID 0x008bda05.
//
// Solidity: function anonOutput() returns(string)
func (outputChecker *OutputChecker) PackAnonOutput() []byte {
	enc, err := outputChecker.abi.Pack("anonOutput")
	if err != nil {
		panic(err)
	}
	return enc
}

// UnpackAnonOutput is the Go binding that unpacks the parameters returned
// from invoking the contract method with ID 0x008bda05.
//
// Solidity: function anonOutput() returns(string)
func (outputChecker *OutputChecker) UnpackAnonOutput(data []byte) (string, error) {
	out, err := outputChecker.abi.Unpack("anonOutput", data)
	if err != nil {
		return *new(string), err
	}
	out0 := *abi.ConvertType(out[0], new(string)).(*string)
	return out0, err
}

// PackAnonOutputs is the Go binding used to pack the parameters required for calling
// the contract method with ID 0x3c401115.
//
// Solidity: function anonOutputs() returns(string, string)
func (outputChecker *OutputChecker) PackAnonOutputs() []byte {
	enc, err := outputChecker.abi.Pack("anonOutputs")
	if err != nil {
		panic(err)
	}
	return enc
}

// AnonOutputsOutput serves as a container for the return parameters of contract
// method AnonOutputs.
type AnonOutputsOutput struct {
	Arg0 string
	Arg1 string
}

// UnpackAnonOutputs is the Go binding that unpacks the parameters returned
// from invoking the contract method with ID 0x3c401115.
//
// Solidity: function anonOutputs() returns(string, string)
func (outputChecker *OutputChecker) UnpackAnonOutputs(data []byte) (AnonOutputsOutput, error) {
	out, err := outputChecker.abi.Unpack("anonOutputs", data)
	outstruct := new(AnonOutputsOutput)
	if err != nil {
		return *outstruct, err
	}
	outstruct.Arg0 = *abi.ConvertType(out[0], new(string)).(*string)
	outstruct.Arg1 = *abi.ConvertType(out[1], new(string)).(*string)
	return *outstruct, err

}

// PackCollidingOutputs is the Go binding used to pack the parameters required for calling
// the contract method with ID 0xeccbc1ee.
//
// Solidity: function collidingOutputs() returns(string str, string Str)
func (outputChecker *OutputChecker) PackCollidingOutputs() []byte {
	enc, err := outputChecker.abi.Pack("collidingOutputs")
	if err != nil {
		panic(err)
	}
	return enc
}

// CollidingOutputsOutput serves as a container for the return parameters of contract
// method CollidingOutputs.
type CollidingOutputsOutput struct {
	Str  string
	Str0 string
}

// UnpackCollidingOutputs is the Go binding that unpacks the parameters returned
// from invoking the contract method with ID 0xeccbc1ee.
//
// Solidity: function collidingOutputs() returns(string str, string Str)
func (outputChecker *OutputChecker) UnpackCollidingOutputs(data []byte) (CollidingOutputsOutput, error) {
	out, err := outputChecker.abi.Unpack("collidingOutputs", data)
	outstruct := new(CollidingOutputsOutput)
	if err != nil {
		return *outstruct, err
	}
	outstruct.Str = *abi.ConvertType(out[0], new(string)).(*string)
	outstruct.Str0 = *abi.ConvertType(out[1], new(string)).(*string)
	return *outstruct, err

}

// PackMixedOutputs is the Go binding used to pack the parameters required for calling
// the contract method with ID 0x21b77b44.
//
// Solidity: function mixedOutputs() returns(string, string str)
func (outputChecker *OutputChecker) PackMixedOutputs() []byte {
	enc, err := outputChecker.abi.Pack("mixedOutputs")
	if err != nil {
		panic(err)
	}
	return enc
}

// MixedOutputsOutput serves as a container for the return parameters of contract
// method MixedOutputs.
type MixedOutputsOutput struct {
	Arg0 string
	Str  string
}

// UnpackMixedOutputs is the Go binding that unpacks the parameters returned
// from invoking the contract method with ID 0x21b77b44.
//
// Solidity: function mixedOutputs() returns(string, string str)
func (outputChecker *OutputChecker) UnpackMixedOutputs(data []byte) (MixedOutputsOutput, error) {
	out, err := outputChecker.abi.Unpack("mixedOutputs", data)
	outstruct := new(MixedOutputsOutput)
	if err != nil {
		return *outstruct, err
	}
	outstruct.Arg0 = *abi.ConvertType(out[0], new(string)).(*string)
	outstruct.Str = *abi.ConvertType(out[1], new(string)).(*string)
	return *outstruct, err

}

// PackNamedOutput is the Go binding used to pack the parameters required for calling
// the contract method with ID 0x5e632bd5.
//
// Solidity: function namedOutput() returns(string str)
func (outputChecker *OutputChecker) PackNamedOutput() []byte {
	enc, err := outputChecker.abi.Pack("namedOutput")
	if err != nil {
		panic(err)
	}
	return enc
}

// UnpackNamedOutput is the Go binding that unpacks the parameters returned
// from invoking the contract method with ID 0x5e632bd5.
//
// Solidity: function namedOutput() returns(string str)
func (outputChecker *OutputChecker) UnpackNamedOutput(data []byte) (string, error) {
	out, err := outputChecker.abi.Unpack("namedOutput", data)
	if err != nil {
		return *new(string), err
	}
	out0 := *abi.ConvertType(out[0], new(string)).(*string)
	return out0, err
}

// PackNamedOutputs is the Go binding used to pack the parameters required for calling
// the contract method with ID 0x7970a189.
//
// Solidity: function namedOutputs() returns(string str1, string str2)
func (outputChecker *OutputChecker) PackNamedOutputs() []byte {
	enc, err := outputChecker.abi.Pack("namedOutputs")
	if err != nil {
		panic(err)
	}
	return enc
}

// NamedOutputsOutput serves as a container for the return parameters of contract
// method NamedOutputs.
type NamedOutputsOutput struct {
	Str1 string
	Str2 string
}

// UnpackNamedOutputs is the Go binding that unpacks the parameters returned
// from invoking the contract method with ID 0x7970a189.
//
// Solidity: function namedOutputs() returns(string str1, string str2)
func (outputChecker *OutputChecker) UnpackNamedOutputs(data []byte) (NamedOutputsOutput, error) {
	out, err := outputChecker.abi.Unpack("namedOutputs", data)
	outstruct := new(NamedOutputsOutput)
	if err != nil {
		return *outstruct, err
	}
	outstruct.Str1 = *abi.ConvertType(out[0], new(string)).(*string)
	outstruct.Str2 = *abi.ConvertType(out[1], new(string)).(*string)
	return *outstruct, err

}

// PackNoOutput is the Go binding used to pack the parameters required for calling
// the contract method with ID 0x625f0306.
//
// Solidity: function noOutput() returns()
func (outputChecker *OutputChecker) PackNoOutput() []byte {
	enc, err := outputChecker.abi.Pack("noOutput")
	if err != nil {
		panic(err)
	}
	return enc
}
