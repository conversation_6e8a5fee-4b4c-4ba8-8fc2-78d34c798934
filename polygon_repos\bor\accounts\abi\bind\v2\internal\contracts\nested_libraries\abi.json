{"contracts": {"contract.sol:Array": {"abi": [], "bin": "61044261004d600b8282823980515f1a6073146041577f4e487b71000000000000000000000000000000000000000000000000000000005f525f60045260245ffd5b305f52607381538281f3fe7300000000000000000000000000000000000000003014608060405260043610610034575f3560e01c8063677ca2d814610038575b5f80fd5b818015610043575f80fd5b5061005e60048036038101906100599190610235565b610060565b005b5f8280549050116100a6576040517f08c379a000000000000000000000000000000000000000000000000000000000815260040161009d906102cd565b60405180910390fd5b81600183805490506100b89190610318565b815481106100c9576100c861034b565b5b905f5260205f2001548282815481106100e5576100e461034b565b5b905f5260205f2001819055508181815481106101045761010361034b565b5b905f5260205f20015473__$e0273646c631009d12385ab5282af2d432$__63ee05608590916040518263ffffffff1660e01b81526004016101459190610387565b602060405180830381865af4158015610160573d5f803e3d5ffd5b505050506040513d601f19601f8201168201806040525081019061018491906103b4565b8282815481106101975761019661034b565b5b905f5260205f200181905550818054806101b4576101b36103df565b5b600190038181905f5260205f20015f905590555050565b5f80fd5b5f819050919050565b6101e1816101cf565b81146101eb575f80fd5b50565b5f813590506101fc816101d8565b92915050565b5f819050919050565b61021481610202565b811461021e575f80fd5b50565b5f8135905061022f8161020b565b92915050565b5f806040838503121561024b5761024a6101cb565b5b5f610258858286016101ee565b925050602061026985828601610221565b9150509250929050565b5f82825260208201905092915050565b7f43616e27742072656d6f76652066726f6d20656d7074792061727261790000005f82015250565b5f6102b7601d83610273565b91506102c282610283565b602082019050919050565b5f6020820190508181035f8301526102e4816102ab565b9050919050565b7f4e487b71000000000000000000000000000000000000000000000000000000005f52601160045260245ffd5b5f61032282610202565b915061032d83610202565b9250828203905081811115610345576103446102eb565b5b92915050565b7f4e487b71000000000000000000000000000000000000000000000000000000005f52603260045260245ffd5b61038181610202565b82525050565b5f60208201905061039a5f830184610378565b92915050565b5f815190506103ae8161020b565b92915050565b5f602082840312156103c9576103c86101cb565b5b5f6103d6848285016103a0565b91505092915050565b7f4e487b71000000000000000000000000000000000000000000000000000000005f52603160045260245ffdfea26469706673582212200680afb351728e7eaa7168f68e59cd7151eff98288314447ad7638a444ed11de64736f6c634300081a0033"}, "contract.sol:RecursiveDep": {"abi": [{"inputs": [{"internalType": "uint256", "name": "val", "type": "uint256"}], "name": "AddOne", "outputs": [{"internalType": "uint256", "name": "ret", "type": "uint256"}], "stateMutability": "pure", "type": "function"}], "bin": "61019d61004d600b8282823980515f1a6073146041577f4e487b71000000000000000000000000000000000000000000000000000000005f525f60045260245ffd5b305f52607381538281f3fe7300000000000000000000000000000000000000003014608060405260043610610034575f3560e01c8063ee05608514610038575b5f80fd5b610052600480360381019061004d91906100b4565b610068565b60405161005f91906100ee565b60405180910390f35b5f6001826100769190610134565b9050919050565b5f80fd5b5f819050919050565b61009381610081565b811461009d575f80fd5b50565b5f813590506100ae8161008a565b92915050565b5f602082840312156100c9576100c861007d565b5b5f6100d6848285016100a0565b91505092915050565b6100e881610081565b82525050565b5f6020820190506101015f8301846100df565b92915050565b7f4e487b71000000000000000000000000000000000000000000000000000000005f52601160045260245ffd5b5f61013e82610081565b915061014983610081565b925082820190508082111561016157610160610107565b5b9291505056fea2646970667358221220d392325a1e387a65c76bff6fecec456650b48856b1e00afc4fa76fb9181da23c64736f6c634300081a0033"}, "contract.sol:TestArray": {"abi": [{"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "arr", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "value", "type": "uint256"}], "name": "testArrayRemove", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bin": "6080604052348015600e575f80fd5b506103438061001c5f395ff3fe608060405234801561000f575f80fd5b5060043610610034575f3560e01c806371e5ee5f14610038578063807fc49a14610068575b5f80fd5b610052600480360381019061004d91906101f0565b610084565b60405161005f919061022a565b60405180910390f35b610082600480360381019061007d91906101f0565b6100a3565b005b5f8181548110610092575f80fd5b905f5260205f20015f915090505481565b5f5b60038110156100e0575f81908060018154018082558091505060019003905f5260205f20015f909190919091505580806001019150506100a5565b505f73__$37f5055d0d00ca8ab20a50453e6986094c$__63677ca2d8909160016040518363ffffffff1660e01b815260040161011d92919061028c565b5f6040518083038186803b158015610133575f80fd5b505af4158015610145573d5f803e3d5ffd5b5050505060025f805490501461015e5761015d6102b3565b5b5f805f81548110610172576101716102e0565b5b905f5260205f20015414610189576101886102b3565b5b60025f60018154811061019f5761019e6102e0565b5b905f5260205f200154146101b6576101b56102b3565b5b50565b5f80fd5b5f819050919050565b6101cf816101bd565b81146101d9575f80fd5b50565b5f813590506101ea816101c6565b92915050565b5f60208284031215610205576102046101b9565b5b5f610212848285016101dc565b91505092915050565b610224816101bd565b82525050565b5f60208201905061023d5f83018461021b565b92915050565b8082525050565b5f819050919050565b5f819050919050565b5f61027661027161026c8461024a565b610253565b6101bd565b9050919050565b6102868161025c565b82525050565b5f60408201905061029f5f830185610243565b6102ac602083018461027d565b9392505050565b7f4e487b71000000000000000000000000000000000000000000000000000000005f52600160045260245ffd5b7f4e487b71000000000000000000000000000000000000000000000000000000005f52603260045260245ffdfea26469706673582212204be2c6230af664b290f016e88cfac62bf7c08823b1fd1bcce8bdcd7fbb785b8a64736f6c634300081a0033"}}, "version": "0.8.26+commit.8a97fa7a.Darwin.appleclang"}