// This file originates from the SatoshiLabs Trezor `common` repository at:
//   https://github.com/trezor/trezor-common/blob/master/protob/messages-common.proto
// dated 28.05.2019, commit 893fd219d4a01bcffa0cd9cfa631856371ec5aa9.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.27.1
// source: messages-common.proto

package trezor

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Failure_FailureType int32

const (
	Failure_Failure_UnexpectedMessage Failure_FailureType = 1
	Failure_Failure_ButtonExpected    Failure_FailureType = 2
	Failure_Failure_DataError         Failure_FailureType = 3
	Failure_Failure_ActionCancelled   Failure_FailureType = 4
	Failure_Failure_PinExpected       Failure_FailureType = 5
	Failure_Failure_PinCancelled      Failure_FailureType = 6
	Failure_Failure_PinInvalid        Failure_FailureType = 7
	Failure_Failure_InvalidSignature  Failure_FailureType = 8
	Failure_Failure_ProcessError      Failure_FailureType = 9
	Failure_Failure_NotEnoughFunds    Failure_FailureType = 10
	Failure_Failure_NotInitialized    Failure_FailureType = 11
	Failure_Failure_PinMismatch       Failure_FailureType = 12
	Failure_Failure_FirmwareError     Failure_FailureType = 99
)

// Enum value maps for Failure_FailureType.
var (
	Failure_FailureType_name = map[int32]string{
		1:  "Failure_UnexpectedMessage",
		2:  "Failure_ButtonExpected",
		3:  "Failure_DataError",
		4:  "Failure_ActionCancelled",
		5:  "Failure_PinExpected",
		6:  "Failure_PinCancelled",
		7:  "Failure_PinInvalid",
		8:  "Failure_InvalidSignature",
		9:  "Failure_ProcessError",
		10: "Failure_NotEnoughFunds",
		11: "Failure_NotInitialized",
		12: "Failure_PinMismatch",
		99: "Failure_FirmwareError",
	}
	Failure_FailureType_value = map[string]int32{
		"Failure_UnexpectedMessage": 1,
		"Failure_ButtonExpected":    2,
		"Failure_DataError":         3,
		"Failure_ActionCancelled":   4,
		"Failure_PinExpected":       5,
		"Failure_PinCancelled":      6,
		"Failure_PinInvalid":        7,
		"Failure_InvalidSignature":  8,
		"Failure_ProcessError":      9,
		"Failure_NotEnoughFunds":    10,
		"Failure_NotInitialized":    11,
		"Failure_PinMismatch":       12,
		"Failure_FirmwareError":     99,
	}
)

func (x Failure_FailureType) Enum() *Failure_FailureType {
	p := new(Failure_FailureType)
	*p = x

	return p
}

func (x Failure_FailureType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Failure_FailureType) Descriptor() protoreflect.EnumDescriptor {
	return file_messages_common_proto_enumTypes[0].Descriptor()
}

func (Failure_FailureType) Type() protoreflect.EnumType {
	return &file_messages_common_proto_enumTypes[0]
}

func (x Failure_FailureType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *Failure_FailureType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = Failure_FailureType(num)
	return nil
}

// Deprecated: Use Failure_FailureType.Descriptor instead.
func (Failure_FailureType) EnumDescriptor() ([]byte, []int) {
	return file_messages_common_proto_rawDescGZIP(), []int{1, 0}
}

// *
// Type of button request
type ButtonRequest_ButtonRequestType int32

const (
	ButtonRequest_ButtonRequest_Other                 ButtonRequest_ButtonRequestType = 1
	ButtonRequest_ButtonRequest_FeeOverThreshold      ButtonRequest_ButtonRequestType = 2
	ButtonRequest_ButtonRequest_ConfirmOutput         ButtonRequest_ButtonRequestType = 3
	ButtonRequest_ButtonRequest_ResetDevice           ButtonRequest_ButtonRequestType = 4
	ButtonRequest_ButtonRequest_ConfirmWord           ButtonRequest_ButtonRequestType = 5
	ButtonRequest_ButtonRequest_WipeDevice            ButtonRequest_ButtonRequestType = 6
	ButtonRequest_ButtonRequest_ProtectCall           ButtonRequest_ButtonRequestType = 7
	ButtonRequest_ButtonRequest_SignTx                ButtonRequest_ButtonRequestType = 8
	ButtonRequest_ButtonRequest_FirmwareCheck         ButtonRequest_ButtonRequestType = 9
	ButtonRequest_ButtonRequest_Address               ButtonRequest_ButtonRequestType = 10
	ButtonRequest_ButtonRequest_PublicKey             ButtonRequest_ButtonRequestType = 11
	ButtonRequest_ButtonRequest_MnemonicWordCount     ButtonRequest_ButtonRequestType = 12
	ButtonRequest_ButtonRequest_MnemonicInput         ButtonRequest_ButtonRequestType = 13
	ButtonRequest_ButtonRequest_PassphraseType        ButtonRequest_ButtonRequestType = 14
	ButtonRequest_ButtonRequest_UnknownDerivationPath ButtonRequest_ButtonRequestType = 15
)

// Enum value maps for ButtonRequest_ButtonRequestType.
var (
	ButtonRequest_ButtonRequestType_name = map[int32]string{
		1:  "ButtonRequest_Other",
		2:  "ButtonRequest_FeeOverThreshold",
		3:  "ButtonRequest_ConfirmOutput",
		4:  "ButtonRequest_ResetDevice",
		5:  "ButtonRequest_ConfirmWord",
		6:  "ButtonRequest_WipeDevice",
		7:  "ButtonRequest_ProtectCall",
		8:  "ButtonRequest_SignTx",
		9:  "ButtonRequest_FirmwareCheck",
		10: "ButtonRequest_Address",
		11: "ButtonRequest_PublicKey",
		12: "ButtonRequest_MnemonicWordCount",
		13: "ButtonRequest_MnemonicInput",
		14: "ButtonRequest_PassphraseType",
		15: "ButtonRequest_UnknownDerivationPath",
	}
	ButtonRequest_ButtonRequestType_value = map[string]int32{
		"ButtonRequest_Other":                 1,
		"ButtonRequest_FeeOverThreshold":      2,
		"ButtonRequest_ConfirmOutput":         3,
		"ButtonRequest_ResetDevice":           4,
		"ButtonRequest_ConfirmWord":           5,
		"ButtonRequest_WipeDevice":            6,
		"ButtonRequest_ProtectCall":           7,
		"ButtonRequest_SignTx":                8,
		"ButtonRequest_FirmwareCheck":         9,
		"ButtonRequest_Address":               10,
		"ButtonRequest_PublicKey":             11,
		"ButtonRequest_MnemonicWordCount":     12,
		"ButtonRequest_MnemonicInput":         13,
		"ButtonRequest_PassphraseType":        14,
		"ButtonRequest_UnknownDerivationPath": 15,
	}
)

func (x ButtonRequest_ButtonRequestType) Enum() *ButtonRequest_ButtonRequestType {
	p := new(ButtonRequest_ButtonRequestType)
	*p = x

	return p
}

func (x ButtonRequest_ButtonRequestType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ButtonRequest_ButtonRequestType) Descriptor() protoreflect.EnumDescriptor {
	return file_messages_common_proto_enumTypes[1].Descriptor()
}

func (ButtonRequest_ButtonRequestType) Type() protoreflect.EnumType {
	return &file_messages_common_proto_enumTypes[1]
}

func (x ButtonRequest_ButtonRequestType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *ButtonRequest_ButtonRequestType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = ButtonRequest_ButtonRequestType(num)
	return nil
}

// Deprecated: Use ButtonRequest_ButtonRequestType.Descriptor instead.
func (ButtonRequest_ButtonRequestType) EnumDescriptor() ([]byte, []int) {
	return file_messages_common_proto_rawDescGZIP(), []int{2, 0}
}

// *
// Type of PIN request
type PinMatrixRequest_PinMatrixRequestType int32

const (
	PinMatrixRequest_PinMatrixRequestType_Current   PinMatrixRequest_PinMatrixRequestType = 1
	PinMatrixRequest_PinMatrixRequestType_NewFirst  PinMatrixRequest_PinMatrixRequestType = 2
	PinMatrixRequest_PinMatrixRequestType_NewSecond PinMatrixRequest_PinMatrixRequestType = 3
)

// Enum value maps for PinMatrixRequest_PinMatrixRequestType.
var (
	PinMatrixRequest_PinMatrixRequestType_name = map[int32]string{
		1: "PinMatrixRequestType_Current",
		2: "PinMatrixRequestType_NewFirst",
		3: "PinMatrixRequestType_NewSecond",
	}
	PinMatrixRequest_PinMatrixRequestType_value = map[string]int32{
		"PinMatrixRequestType_Current":   1,
		"PinMatrixRequestType_NewFirst":  2,
		"PinMatrixRequestType_NewSecond": 3,
	}
)

func (x PinMatrixRequest_PinMatrixRequestType) Enum() *PinMatrixRequest_PinMatrixRequestType {
	p := new(PinMatrixRequest_PinMatrixRequestType)
	*p = x

	return p
}

func (x PinMatrixRequest_PinMatrixRequestType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PinMatrixRequest_PinMatrixRequestType) Descriptor() protoreflect.EnumDescriptor {
	return file_messages_common_proto_enumTypes[2].Descriptor()
}

func (PinMatrixRequest_PinMatrixRequestType) Type() protoreflect.EnumType {
	return &file_messages_common_proto_enumTypes[2]
}

func (x PinMatrixRequest_PinMatrixRequestType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *PinMatrixRequest_PinMatrixRequestType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = PinMatrixRequest_PinMatrixRequestType(num)
	return nil
}

// Deprecated: Use PinMatrixRequest_PinMatrixRequestType.Descriptor instead.
func (PinMatrixRequest_PinMatrixRequestType) EnumDescriptor() ([]byte, []int) {
	return file_messages_common_proto_rawDescGZIP(), []int{4, 0}
}

// *
// Response: Success of the previous request
// @end
type Success struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Message *string `protobuf:"bytes,1,opt,name=message" json:"message,omitempty"` // human readable description of action or request-specific payload
}

func (x *Success) Reset() {
	*x = Success{}
	if protoimpl.UnsafeEnabled {
		mi := &file_messages_common_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Success) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Success) ProtoMessage() {}

func (x *Success) ProtoReflect() protoreflect.Message {
	mi := &file_messages_common_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Success.ProtoReflect.Descriptor instead.
func (*Success) Descriptor() ([]byte, []int) {
	return file_messages_common_proto_rawDescGZIP(), []int{0}
}

func (x *Success) GetMessage() string {
	if x != nil && x.Message != nil {
		return *x.Message
	}

	return ""
}

// *
// Response: Failure of the previous request
// @end
type Failure struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code    *Failure_FailureType `protobuf:"varint,1,opt,name=code,enum=hw.trezor.messages.common.Failure_FailureType" json:"code,omitempty"` // computer-readable definition of the error state
	Message *string              `protobuf:"bytes,2,opt,name=message" json:"message,omitempty"`                                               // human-readable message of the error state
}

func (x *Failure) Reset() {
	*x = Failure{}
	if protoimpl.UnsafeEnabled {
		mi := &file_messages_common_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Failure) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Failure) ProtoMessage() {}

func (x *Failure) ProtoReflect() protoreflect.Message {
	mi := &file_messages_common_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Failure.ProtoReflect.Descriptor instead.
func (*Failure) Descriptor() ([]byte, []int) {
	return file_messages_common_proto_rawDescGZIP(), []int{1}
}

func (x *Failure) GetCode() Failure_FailureType {
	if x != nil && x.Code != nil {
		return *x.Code
	}

	return Failure_Failure_UnexpectedMessage
}

func (x *Failure) GetMessage() string {
	if x != nil && x.Message != nil {
		return *x.Message
	}

	return ""
}

// *
// Response: Device is waiting for HW button press.
// @auxstart
// @next ButtonAck
type ButtonRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code *ButtonRequest_ButtonRequestType `protobuf:"varint,1,opt,name=code,enum=hw.trezor.messages.common.ButtonRequest_ButtonRequestType" json:"code,omitempty"`
	Data *string                          `protobuf:"bytes,2,opt,name=data" json:"data,omitempty"`
}

func (x *ButtonRequest) Reset() {
	*x = ButtonRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_messages_common_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ButtonRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ButtonRequest) ProtoMessage() {}

func (x *ButtonRequest) ProtoReflect() protoreflect.Message {
	mi := &file_messages_common_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ButtonRequest.ProtoReflect.Descriptor instead.
func (*ButtonRequest) Descriptor() ([]byte, []int) {
	return file_messages_common_proto_rawDescGZIP(), []int{2}
}

func (x *ButtonRequest) GetCode() ButtonRequest_ButtonRequestType {
	if x != nil && x.Code != nil {
		return *x.Code
	}

	return ButtonRequest_ButtonRequest_Other
}

func (x *ButtonRequest) GetData() string {
	if x != nil && x.Data != nil {
		return *x.Data
	}

	return ""
}

// *
// Request: Computer agrees to wait for HW button press
// @auxend
type ButtonAck struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ButtonAck) Reset() {
	*x = ButtonAck{}
	if protoimpl.UnsafeEnabled {
		mi := &file_messages_common_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ButtonAck) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ButtonAck) ProtoMessage() {}

func (x *ButtonAck) ProtoReflect() protoreflect.Message {
	mi := &file_messages_common_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ButtonAck.ProtoReflect.Descriptor instead.
func (*ButtonAck) Descriptor() ([]byte, []int) {
	return file_messages_common_proto_rawDescGZIP(), []int{3}
}

// *
// Response: Device is asking computer to show PIN matrix and awaits PIN encoded using this matrix scheme
// @auxstart
// @next PinMatrixAck
type PinMatrixRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type *PinMatrixRequest_PinMatrixRequestType `protobuf:"varint,1,opt,name=type,enum=hw.trezor.messages.common.PinMatrixRequest_PinMatrixRequestType" json:"type,omitempty"`
}

func (x *PinMatrixRequest) Reset() {
	*x = PinMatrixRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_messages_common_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PinMatrixRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PinMatrixRequest) ProtoMessage() {}

func (x *PinMatrixRequest) ProtoReflect() protoreflect.Message {
	mi := &file_messages_common_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PinMatrixRequest.ProtoReflect.Descriptor instead.
func (*PinMatrixRequest) Descriptor() ([]byte, []int) {
	return file_messages_common_proto_rawDescGZIP(), []int{4}
}

func (x *PinMatrixRequest) GetType() PinMatrixRequest_PinMatrixRequestType {
	if x != nil && x.Type != nil {
		return *x.Type
	}

	return PinMatrixRequest_PinMatrixRequestType_Current
}

// *
// Request: Computer responds with encoded PIN
// @auxend
type PinMatrixAck struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pin *string `protobuf:"bytes,1,req,name=pin" json:"pin,omitempty"` // matrix encoded PIN entered by user
}

func (x *PinMatrixAck) Reset() {
	*x = PinMatrixAck{}
	if protoimpl.UnsafeEnabled {
		mi := &file_messages_common_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PinMatrixAck) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PinMatrixAck) ProtoMessage() {}

func (x *PinMatrixAck) ProtoReflect() protoreflect.Message {
	mi := &file_messages_common_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PinMatrixAck.ProtoReflect.Descriptor instead.
func (*PinMatrixAck) Descriptor() ([]byte, []int) {
	return file_messages_common_proto_rawDescGZIP(), []int{5}
}

func (x *PinMatrixAck) GetPin() string {
	if x != nil && x.Pin != nil {
		return *x.Pin
	}

	return ""
}

// *
// Response: Device awaits encryption passphrase
// @auxstart
// @next PassphraseAck
type PassphraseRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OnDevice *bool `protobuf:"varint,1,opt,name=on_device,json=onDevice" json:"on_device,omitempty"` // passphrase is being entered on the device
}

func (x *PassphraseRequest) Reset() {
	*x = PassphraseRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_messages_common_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PassphraseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PassphraseRequest) ProtoMessage() {}

func (x *PassphraseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_messages_common_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PassphraseRequest.ProtoReflect.Descriptor instead.
func (*PassphraseRequest) Descriptor() ([]byte, []int) {
	return file_messages_common_proto_rawDescGZIP(), []int{6}
}

func (x *PassphraseRequest) GetOnDevice() bool {
	if x != nil && x.OnDevice != nil {
		return *x.OnDevice
	}

	return false
}

// *
// Request: Send passphrase back
// @next PassphraseStateRequest
type PassphraseAck struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Passphrase *string `protobuf:"bytes,1,opt,name=passphrase" json:"passphrase,omitempty"`
	State      []byte  `protobuf:"bytes,2,opt,name=state" json:"state,omitempty"` // expected device state
}

func (x *PassphraseAck) Reset() {
	*x = PassphraseAck{}
	if protoimpl.UnsafeEnabled {
		mi := &file_messages_common_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PassphraseAck) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PassphraseAck) ProtoMessage() {}

func (x *PassphraseAck) ProtoReflect() protoreflect.Message {
	mi := &file_messages_common_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PassphraseAck.ProtoReflect.Descriptor instead.
func (*PassphraseAck) Descriptor() ([]byte, []int) {
	return file_messages_common_proto_rawDescGZIP(), []int{7}
}

func (x *PassphraseAck) GetPassphrase() string {
	if x != nil && x.Passphrase != nil {
		return *x.Passphrase
	}

	return ""
}

func (x *PassphraseAck) GetState() []byte {
	if x != nil {
		return x.State
	}

	return nil
}

// *
// Response: Device awaits passphrase state
// @next PassphraseStateAck
type PassphraseStateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	State []byte `protobuf:"bytes,1,opt,name=state" json:"state,omitempty"` // actual device state
}

func (x *PassphraseStateRequest) Reset() {
	*x = PassphraseStateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_messages_common_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PassphraseStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PassphraseStateRequest) ProtoMessage() {}

func (x *PassphraseStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_messages_common_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PassphraseStateRequest.ProtoReflect.Descriptor instead.
func (*PassphraseStateRequest) Descriptor() ([]byte, []int) {
	return file_messages_common_proto_rawDescGZIP(), []int{8}
}

func (x *PassphraseStateRequest) GetState() []byte {
	if x != nil {
		return x.State
	}

	return nil
}

// *
// Request: Send passphrase state back
// @auxend
type PassphraseStateAck struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PassphraseStateAck) Reset() {
	*x = PassphraseStateAck{}
	if protoimpl.UnsafeEnabled {
		mi := &file_messages_common_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PassphraseStateAck) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PassphraseStateAck) ProtoMessage() {}

func (x *PassphraseStateAck) ProtoReflect() protoreflect.Message {
	mi := &file_messages_common_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PassphraseStateAck.ProtoReflect.Descriptor instead.
func (*PassphraseStateAck) Descriptor() ([]byte, []int) {
	return file_messages_common_proto_rawDescGZIP(), []int{9}
}

// *
// Structure representing BIP32 (hierarchical deterministic) node
// Used for imports of private key into the device and exporting public key out of device
// @embed
type HDNodeType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Depth       *uint32 `protobuf:"varint,1,req,name=depth" json:"depth,omitempty"`
	Fingerprint *uint32 `protobuf:"varint,2,req,name=fingerprint" json:"fingerprint,omitempty"`
	ChildNum    *uint32 `protobuf:"varint,3,req,name=child_num,json=childNum" json:"child_num,omitempty"`
	ChainCode   []byte  `protobuf:"bytes,4,req,name=chain_code,json=chainCode" json:"chain_code,omitempty"`
	PrivateKey  []byte  `protobuf:"bytes,5,opt,name=private_key,json=privateKey" json:"private_key,omitempty"`
	PublicKey   []byte  `protobuf:"bytes,6,opt,name=public_key,json=publicKey" json:"public_key,omitempty"`
}

func (x *HDNodeType) Reset() {
	*x = HDNodeType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_messages_common_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HDNodeType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HDNodeType) ProtoMessage() {}

func (x *HDNodeType) ProtoReflect() protoreflect.Message {
	mi := &file_messages_common_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HDNodeType.ProtoReflect.Descriptor instead.
func (*HDNodeType) Descriptor() ([]byte, []int) {
	return file_messages_common_proto_rawDescGZIP(), []int{10}
}

func (x *HDNodeType) GetDepth() uint32 {
	if x != nil && x.Depth != nil {
		return *x.Depth
	}

	return 0
}

func (x *HDNodeType) GetFingerprint() uint32 {
	if x != nil && x.Fingerprint != nil {
		return *x.Fingerprint
	}

	return 0
}

func (x *HDNodeType) GetChildNum() uint32 {
	if x != nil && x.ChildNum != nil {
		return *x.ChildNum
	}

	return 0
}

func (x *HDNodeType) GetChainCode() []byte {
	if x != nil {
		return x.ChainCode
	}

	return nil
}

func (x *HDNodeType) GetPrivateKey() []byte {
	if x != nil {
		return x.PrivateKey
	}

	return nil
}

func (x *HDNodeType) GetPublicKey() []byte {
	if x != nil {
		return x.PublicKey
	}

	return nil
}

var File_messages_common_proto protoreflect.FileDescriptor

var file_messages_common_proto_rawDesc = []byte{
	0x0a, 0x15, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x68, 0x77, 0x2e, 0x74, 0x72, 0x65, 0x7a,
	0x6f, 0x72, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x22, 0x23, 0x0a, 0x07, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a,
	0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0xd5, 0x03, 0x0a, 0x07, 0x46, 0x61, 0x69, 0x6c,
	0x75, 0x72, 0x65, 0x12, 0x42, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x2e, 0x2e, 0x68, 0x77, 0x2e, 0x74, 0x72, 0x65, 0x7a, 0x6f, 0x72, 0x2e, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x61,
	0x69, 0x6c, 0x75, 0x72, 0x65, 0x2e, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x22, 0xeb, 0x02, 0x0a, 0x0b, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1d, 0x0a, 0x19, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x55, 0x6e, 0x65,
	0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x10, 0x01,
	0x12, 0x1a, 0x0a, 0x16, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x42, 0x75, 0x74, 0x74,
	0x6f, 0x6e, 0x45, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x10, 0x02, 0x12, 0x15, 0x0a, 0x11,
	0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x44, 0x61, 0x74, 0x61, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x10, 0x03, 0x12, 0x1b, 0x0a, 0x17, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x41,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x65, 0x64, 0x10, 0x04,
	0x12, 0x17, 0x0a, 0x13, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x50, 0x69, 0x6e, 0x45,
	0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x10, 0x05, 0x12, 0x18, 0x0a, 0x14, 0x46, 0x61, 0x69,
	0x6c, 0x75, 0x72, 0x65, 0x5f, 0x50, 0x69, 0x6e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x65,
	0x64, 0x10, 0x06, 0x12, 0x16, 0x0a, 0x12, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x50,
	0x69, 0x6e, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x10, 0x07, 0x12, 0x1c, 0x0a, 0x18, 0x46,
	0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x53, 0x69,
	0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x10, 0x08, 0x12, 0x18, 0x0a, 0x14, 0x46, 0x61, 0x69,
	0x6c, 0x75, 0x72, 0x65, 0x5f, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x10, 0x09, 0x12, 0x1a, 0x0a, 0x16, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x4e,
	0x6f, 0x74, 0x45, 0x6e, 0x6f, 0x75, 0x67, 0x68, 0x46, 0x75, 0x6e, 0x64, 0x73, 0x10, 0x0a, 0x12,
	0x1a, 0x0a, 0x16, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x4e, 0x6f, 0x74, 0x49, 0x6e,
	0x69, 0x74, 0x69, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x10, 0x0b, 0x12, 0x17, 0x0a, 0x13, 0x46,
	0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x50, 0x69, 0x6e, 0x4d, 0x69, 0x73, 0x6d, 0x61, 0x74,
	0x63, 0x68, 0x10, 0x0c, 0x12, 0x19, 0x0a, 0x15, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f,
	0x46, 0x69, 0x72, 0x6d, 0x77, 0x61, 0x72, 0x65, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0x63, 0x22,
	0xe6, 0x04, 0x0a, 0x0d, 0x42, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x4e, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x3a, 0x2e, 0x68, 0x77, 0x2e, 0x74, 0x72, 0x65, 0x7a, 0x6f, 0x72, 0x2e, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x75, 0x74, 0x74,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x42, 0x75, 0x74, 0x74, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xf0, 0x03, 0x0a, 0x11, 0x42, 0x75, 0x74, 0x74, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x13, 0x42,
	0x75, 0x74, 0x74, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x4f, 0x74, 0x68,
	0x65, 0x72, 0x10, 0x01, 0x12, 0x22, 0x0a, 0x1e, 0x42, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x46, 0x65, 0x65, 0x4f, 0x76, 0x65, 0x72, 0x54, 0x68, 0x72,
	0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x10, 0x02, 0x12, 0x1f, 0x0a, 0x1b, 0x42, 0x75, 0x74, 0x74,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72,
	0x6d, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x10, 0x03, 0x12, 0x1d, 0x0a, 0x19, 0x42, 0x75, 0x74,
	0x74, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x52, 0x65, 0x73, 0x65, 0x74,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x10, 0x04, 0x12, 0x1d, 0x0a, 0x19, 0x42, 0x75, 0x74, 0x74,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72,
	0x6d, 0x57, 0x6f, 0x72, 0x64, 0x10, 0x05, 0x12, 0x1c, 0x0a, 0x18, 0x42, 0x75, 0x74, 0x74, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x57, 0x69, 0x70, 0x65, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x10, 0x06, 0x12, 0x1d, 0x0a, 0x19, 0x42, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x43, 0x61,
	0x6c, 0x6c, 0x10, 0x07, 0x12, 0x18, 0x0a, 0x14, 0x42, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x53, 0x69, 0x67, 0x6e, 0x54, 0x78, 0x10, 0x08, 0x12, 0x1f,
	0x0a, 0x1b, 0x42, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x46, 0x69, 0x72, 0x6d, 0x77, 0x61, 0x72, 0x65, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x10, 0x09, 0x12,
	0x19, 0x0a, 0x15, 0x42, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x10, 0x0a, 0x12, 0x1b, 0x0a, 0x17, 0x42, 0x75,
	0x74, 0x74, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x50, 0x75, 0x62, 0x6c,
	0x69, 0x63, 0x4b, 0x65, 0x79, 0x10, 0x0b, 0x12, 0x23, 0x0a, 0x1f, 0x42, 0x75, 0x74, 0x74, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x4d, 0x6e, 0x65, 0x6d, 0x6f, 0x6e, 0x69,
	0x63, 0x57, 0x6f, 0x72, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x10, 0x0c, 0x12, 0x1f, 0x0a, 0x1b,
	0x42, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x4d, 0x6e,
	0x65, 0x6d, 0x6f, 0x6e, 0x69, 0x63, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x10, 0x0d, 0x12, 0x20, 0x0a,
	0x1c, 0x42, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x50,
	0x61, 0x73, 0x73, 0x70, 0x68, 0x72, 0x61, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x10, 0x0e, 0x12,
	0x27, 0x0a, 0x23, 0x42, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x44, 0x65, 0x72, 0x69, 0x76, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x50, 0x61, 0x74, 0x68, 0x10, 0x0f, 0x22, 0x0b, 0x0a, 0x09, 0x42, 0x75, 0x74, 0x74,
	0x6f, 0x6e, 0x41, 0x63, 0x6b, 0x22, 0xe9, 0x01, 0x0a, 0x10, 0x50, 0x69, 0x6e, 0x4d, 0x61, 0x74,
	0x72, 0x69, 0x78, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x54, 0x0a, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x40, 0x2e, 0x68, 0x77, 0x2e, 0x74, 0x72,
	0x65, 0x7a, 0x6f, 0x72, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x69, 0x6e, 0x4d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x69, 0x6e, 0x4d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x22, 0x7f, 0x0a, 0x14, 0x50, 0x69, 0x6e, 0x4d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x1c, 0x50, 0x69, 0x6e, 0x4d,
	0x61, 0x74, 0x72, 0x69, 0x78, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x5f, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x10, 0x01, 0x12, 0x21, 0x0a, 0x1d, 0x50, 0x69,
	0x6e, 0x4d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x5f, 0x4e, 0x65, 0x77, 0x46, 0x69, 0x72, 0x73, 0x74, 0x10, 0x02, 0x12, 0x22, 0x0a,
	0x1e, 0x50, 0x69, 0x6e, 0x4d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4e, 0x65, 0x77, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x10,
	0x03, 0x22, 0x20, 0x0a, 0x0c, 0x50, 0x69, 0x6e, 0x4d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x41, 0x63,
	0x6b, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x02, 0x28, 0x09, 0x52, 0x03,
	0x70, 0x69, 0x6e, 0x22, 0x30, 0x0a, 0x11, 0x50, 0x61, 0x73, 0x73, 0x70, 0x68, 0x72, 0x61, 0x73,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x6f, 0x6e, 0x5f, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x6f, 0x6e, 0x44,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x22, 0x45, 0x0a, 0x0d, 0x50, 0x61, 0x73, 0x73, 0x70, 0x68, 0x72,
	0x61, 0x73, 0x65, 0x41, 0x63, 0x6b, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x61, 0x73, 0x73, 0x70, 0x68,
	0x72, 0x61, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x61, 0x73, 0x73,
	0x70, 0x68, 0x72, 0x61, 0x73, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x22, 0x2e, 0x0a, 0x16,
	0x50, 0x61, 0x73, 0x73, 0x70, 0x68, 0x72, 0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x22, 0x14, 0x0a, 0x12,
	0x50, 0x61, 0x73, 0x73, 0x70, 0x68, 0x72, 0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x41,
	0x63, 0x6b, 0x22, 0xc0, 0x01, 0x0a, 0x0a, 0x48, 0x44, 0x4e, 0x6f, 0x64, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x64, 0x65, 0x70, 0x74, 0x68, 0x18, 0x01, 0x20, 0x02, 0x28, 0x0d,
	0x52, 0x05, 0x64, 0x65, 0x70, 0x74, 0x68, 0x12, 0x20, 0x0a, 0x0b, 0x66, 0x69, 0x6e, 0x67, 0x65,
	0x72, 0x70, 0x72, 0x69, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x02, 0x28, 0x0d, 0x52, 0x0b, 0x66, 0x69,
	0x6e, 0x67, 0x65, 0x72, 0x70, 0x72, 0x69, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x68, 0x69,
	0x6c, 0x64, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x02, 0x28, 0x0d, 0x52, 0x08, 0x63, 0x68,
	0x69, 0x6c, 0x64, 0x4e, 0x75, 0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x02, 0x28, 0x0c, 0x52, 0x09, 0x63, 0x68, 0x61, 0x69,
	0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65,
	0x5f, 0x6b, 0x65, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a, 0x70, 0x72, 0x69, 0x76,
	0x61, 0x74, 0x65, 0x4b, 0x65, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63,
	0x5f, 0x6b, 0x65, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x70, 0x75, 0x62, 0x6c,
	0x69, 0x63, 0x4b, 0x65, 0x79, 0x42, 0x3b, 0x5a, 0x39, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x74, 0x68, 0x65, 0x72, 0x65, 0x75, 0x6d, 0x2f, 0x67, 0x6f, 0x2d,
	0x65, 0x74, 0x68, 0x65, 0x72, 0x65, 0x75, 0x6d, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x73, 0x2f, 0x75, 0x73, 0x62, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2f, 0x74, 0x72, 0x65, 0x7a,
	0x6f, 0x72,
}

var (
	file_messages_common_proto_rawDescOnce sync.Once
	file_messages_common_proto_rawDescData = file_messages_common_proto_rawDesc
)

func file_messages_common_proto_rawDescGZIP() []byte {
	file_messages_common_proto_rawDescOnce.Do(func() {
		file_messages_common_proto_rawDescData = protoimpl.X.CompressGZIP(file_messages_common_proto_rawDescData)
	})
	return file_messages_common_proto_rawDescData
}

var file_messages_common_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_messages_common_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_messages_common_proto_goTypes = []any{
	(Failure_FailureType)(0),                   // 0: hw.trezor.messages.common.Failure.FailureType
	(ButtonRequest_ButtonRequestType)(0),       // 1: hw.trezor.messages.common.ButtonRequest.ButtonRequestType
	(PinMatrixRequest_PinMatrixRequestType)(0), // 2: hw.trezor.messages.common.PinMatrixRequest.PinMatrixRequestType
	(*Success)(nil),                            // 3: hw.trezor.messages.common.Success
	(*Failure)(nil),                            // 4: hw.trezor.messages.common.Failure
	(*ButtonRequest)(nil),                      // 5: hw.trezor.messages.common.ButtonRequest
	(*ButtonAck)(nil),                          // 6: hw.trezor.messages.common.ButtonAck
	(*PinMatrixRequest)(nil),                   // 7: hw.trezor.messages.common.PinMatrixRequest
	(*PinMatrixAck)(nil),                       // 8: hw.trezor.messages.common.PinMatrixAck
	(*PassphraseRequest)(nil),                  // 9: hw.trezor.messages.common.PassphraseRequest
	(*PassphraseAck)(nil),                      // 10: hw.trezor.messages.common.PassphraseAck
	(*PassphraseStateRequest)(nil),             // 11: hw.trezor.messages.common.PassphraseStateRequest
	(*PassphraseStateAck)(nil),                 // 12: hw.trezor.messages.common.PassphraseStateAck
	(*HDNodeType)(nil),                         // 13: hw.trezor.messages.common.HDNodeType
}
var file_messages_common_proto_depIdxs = []int32{
	0, // 0: hw.trezor.messages.common.Failure.code:type_name -> hw.trezor.messages.common.Failure.FailureType
	1, // 1: hw.trezor.messages.common.ButtonRequest.code:type_name -> hw.trezor.messages.common.ButtonRequest.ButtonRequestType
	2, // 2: hw.trezor.messages.common.PinMatrixRequest.type:type_name -> hw.trezor.messages.common.PinMatrixRequest.PinMatrixRequestType
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_messages_common_proto_init() }
func file_messages_common_proto_init() {
	if File_messages_common_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_messages_common_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*Success); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_messages_common_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*Failure); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_messages_common_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*ButtonRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_messages_common_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*ButtonAck); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_messages_common_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*PinMatrixRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_messages_common_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*PinMatrixAck); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_messages_common_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*PassphraseRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_messages_common_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*PassphraseAck); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_messages_common_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*PassphraseStateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_messages_common_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*PassphraseStateAck); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_messages_common_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*HDNodeType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_messages_common_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_messages_common_proto_goTypes,
		DependencyIndexes: file_messages_common_proto_depIdxs,
		EnumInfos:         file_messages_common_proto_enumTypes,
		MessageInfos:      file_messages_common_proto_msgTypes,
	}.Build()
	File_messages_common_proto = out.File
	file_messages_common_proto_rawDesc = nil
	file_messages_common_proto_goTypes = nil
	file_messages_common_proto_depIdxs = nil
}
