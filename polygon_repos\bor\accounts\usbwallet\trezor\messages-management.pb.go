// This file originates from the SatoshiLabs Trezor `common` repository at:
//   https://github.com/trezor/trezor-common/blob/master/protob/messages-management.proto
// dated 28.05.2019, commit 893fd219d4a01bcffa0cd9cfa631856371ec5aa9.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.34.2
// 	protoc        v5.27.1
// source: messages-management.proto

package trezor

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// *
// Structure representing passphrase source
type ApplySettings_PassphraseSourceType int32

const (
	ApplySettings_ASK    ApplySettings_PassphraseSourceType = 0
	ApplySettings_DEVICE ApplySettings_PassphraseSourceType = 1
	ApplySettings_HOST   ApplySettings_PassphraseSourceType = 2
)

// Enum value maps for ApplySettings_PassphraseSourceType.
var (
	ApplySettings_PassphraseSourceType_name = map[int32]string{
		0: "ASK",
		1: "DEVICE",
		2: "HOST",
	}
	ApplySettings_PassphraseSourceType_value = map[string]int32{
		"ASK":    0,
		"DEVICE": 1,
		"HOST":   2,
	}
)

func (x ApplySettings_PassphraseSourceType) Enum() *ApplySettings_PassphraseSourceType {
	p := new(ApplySettings_PassphraseSourceType)
	*p = x

	return p
}

func (x ApplySettings_PassphraseSourceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ApplySettings_PassphraseSourceType) Descriptor() protoreflect.EnumDescriptor {
	return file_messages_management_proto_enumTypes[0].Descriptor()
}

func (ApplySettings_PassphraseSourceType) Type() protoreflect.EnumType {
	return &file_messages_management_proto_enumTypes[0]
}

func (x ApplySettings_PassphraseSourceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *ApplySettings_PassphraseSourceType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = ApplySettings_PassphraseSourceType(num)
	return nil
}

// Deprecated: Use ApplySettings_PassphraseSourceType.Descriptor instead.
func (ApplySettings_PassphraseSourceType) EnumDescriptor() ([]byte, []int) {
	return file_messages_management_proto_rawDescGZIP(), []int{4, 0}
}

// *
// Type of recovery procedure. These should be used as bitmask, e.g.,
// `RecoveryDeviceType_ScrambledWords | RecoveryDeviceType_Matrix`
// listing every method supported by the host computer.
//
// Note that ScrambledWords must be supported by every implementation
// for backward compatibility; there is no way to not support it.
type RecoveryDevice_RecoveryDeviceType int32

const (
	// use powers of two when extending this field
	RecoveryDevice_RecoveryDeviceType_ScrambledWords RecoveryDevice_RecoveryDeviceType = 0 // words in scrambled order
	RecoveryDevice_RecoveryDeviceType_Matrix         RecoveryDevice_RecoveryDeviceType = 1 // matrix recovery type
)

// Enum value maps for RecoveryDevice_RecoveryDeviceType.
var (
	RecoveryDevice_RecoveryDeviceType_name = map[int32]string{
		0: "RecoveryDeviceType_ScrambledWords",
		1: "RecoveryDeviceType_Matrix",
	}
	RecoveryDevice_RecoveryDeviceType_value = map[string]int32{
		"RecoveryDeviceType_ScrambledWords": 0,
		"RecoveryDeviceType_Matrix":         1,
	}
)

func (x RecoveryDevice_RecoveryDeviceType) Enum() *RecoveryDevice_RecoveryDeviceType {
	p := new(RecoveryDevice_RecoveryDeviceType)
	*p = x

	return p
}

func (x RecoveryDevice_RecoveryDeviceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RecoveryDevice_RecoveryDeviceType) Descriptor() protoreflect.EnumDescriptor {
	return file_messages_management_proto_enumTypes[1].Descriptor()
}

func (RecoveryDevice_RecoveryDeviceType) Type() protoreflect.EnumType {
	return &file_messages_management_proto_enumTypes[1]
}

func (x RecoveryDevice_RecoveryDeviceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *RecoveryDevice_RecoveryDeviceType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = RecoveryDevice_RecoveryDeviceType(num)
	return nil
}

// Deprecated: Use RecoveryDevice_RecoveryDeviceType.Descriptor instead.
func (RecoveryDevice_RecoveryDeviceType) EnumDescriptor() ([]byte, []int) {
	return file_messages_management_proto_rawDescGZIP(), []int{17, 0}
}

// *
// Type of Recovery Word request
type WordRequest_WordRequestType int32

const (
	WordRequest_WordRequestType_Plain   WordRequest_WordRequestType = 0
	WordRequest_WordRequestType_Matrix9 WordRequest_WordRequestType = 1
	WordRequest_WordRequestType_Matrix6 WordRequest_WordRequestType = 2
)

// Enum value maps for WordRequest_WordRequestType.
var (
	WordRequest_WordRequestType_name = map[int32]string{
		0: "WordRequestType_Plain",
		1: "WordRequestType_Matrix9",
		2: "WordRequestType_Matrix6",
	}
	WordRequest_WordRequestType_value = map[string]int32{
		"WordRequestType_Plain":   0,
		"WordRequestType_Matrix9": 1,
		"WordRequestType_Matrix6": 2,
	}
)

func (x WordRequest_WordRequestType) Enum() *WordRequest_WordRequestType {
	p := new(WordRequest_WordRequestType)
	*p = x

	return p
}

func (x WordRequest_WordRequestType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WordRequest_WordRequestType) Descriptor() protoreflect.EnumDescriptor {
	return file_messages_management_proto_enumTypes[2].Descriptor()
}

func (WordRequest_WordRequestType) Type() protoreflect.EnumType {
	return &file_messages_management_proto_enumTypes[2]
}

func (x WordRequest_WordRequestType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *WordRequest_WordRequestType) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = WordRequest_WordRequestType(num)
	return nil
}

// Deprecated: Use WordRequest_WordRequestType.Descriptor instead.
func (WordRequest_WordRequestType) EnumDescriptor() ([]byte, []int) {
	return file_messages_management_proto_rawDescGZIP(), []int{18, 0}
}

// *
// Request: Reset device to default state and ask for device details
// @start
// @next Features
type Initialize struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	State          []byte `protobuf:"bytes,1,opt,name=state" json:"state,omitempty"`                                          // assumed device state, clear session if set and different
	SkipPassphrase *bool  `protobuf:"varint,2,opt,name=skip_passphrase,json=skipPassphrase" json:"skip_passphrase,omitempty"` // this session should always assume empty passphrase
}

func (x *Initialize) Reset() {
	*x = Initialize{}
	if protoimpl.UnsafeEnabled {
		mi := &file_messages_management_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Initialize) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Initialize) ProtoMessage() {}

func (x *Initialize) ProtoReflect() protoreflect.Message {
	mi := &file_messages_management_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Initialize.ProtoReflect.Descriptor instead.
func (*Initialize) Descriptor() ([]byte, []int) {
	return file_messages_management_proto_rawDescGZIP(), []int{0}
}

func (x *Initialize) GetState() []byte {
	if x != nil {
		return x.State
	}

	return nil
}

func (x *Initialize) GetSkipPassphrase() bool {
	if x != nil && x.SkipPassphrase != nil {
		return *x.SkipPassphrase
	}

	return false
}

// *
// Request: Ask for device details (no device reset)
// @start
// @next Features
type GetFeatures struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetFeatures) Reset() {
	*x = GetFeatures{}
	if protoimpl.UnsafeEnabled {
		mi := &file_messages_management_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFeatures) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFeatures) ProtoMessage() {}

func (x *GetFeatures) ProtoReflect() protoreflect.Message {
	mi := &file_messages_management_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFeatures.ProtoReflect.Descriptor instead.
func (*GetFeatures) Descriptor() ([]byte, []int) {
	return file_messages_management_proto_rawDescGZIP(), []int{1}
}

// *
// Response: Reports various information about the device
// @end
type Features struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Vendor               *string `protobuf:"bytes,1,opt,name=vendor" json:"vendor,omitempty"`                                                          // name of the manufacturer, e.g. "trezor.io"
	MajorVersion         *uint32 `protobuf:"varint,2,opt,name=major_version,json=majorVersion" json:"major_version,omitempty"`                         // major version of the firmware/bootloader, e.g. 1
	MinorVersion         *uint32 `protobuf:"varint,3,opt,name=minor_version,json=minorVersion" json:"minor_version,omitempty"`                         // minor version of the firmware/bootloader, e.g. 0
	PatchVersion         *uint32 `protobuf:"varint,4,opt,name=patch_version,json=patchVersion" json:"patch_version,omitempty"`                         // patch version of the firmware/bootloader, e.g. 0
	BootloaderMode       *bool   `protobuf:"varint,5,opt,name=bootloader_mode,json=bootloaderMode" json:"bootloader_mode,omitempty"`                   // is device in bootloader mode?
	DeviceId             *string `protobuf:"bytes,6,opt,name=device_id,json=deviceId" json:"device_id,omitempty"`                                      // device's unique identifier
	PinProtection        *bool   `protobuf:"varint,7,opt,name=pin_protection,json=pinProtection" json:"pin_protection,omitempty"`                      // is device protected by PIN?
	PassphraseProtection *bool   `protobuf:"varint,8,opt,name=passphrase_protection,json=passphraseProtection" json:"passphrase_protection,omitempty"` // is node/mnemonic encrypted using passphrase?
	Language             *string `protobuf:"bytes,9,opt,name=language" json:"language,omitempty"`                                                      // device language
	Label                *string `protobuf:"bytes,10,opt,name=label" json:"label,omitempty"`                                                           // device description label
	Initialized          *bool   `protobuf:"varint,12,opt,name=initialized" json:"initialized,omitempty"`                                              // does device contain seed?
	Revision             []byte  `protobuf:"bytes,13,opt,name=revision" json:"revision,omitempty"`                                                     // SCM revision of firmware
	BootloaderHash       []byte  `protobuf:"bytes,14,opt,name=bootloader_hash,json=bootloaderHash" json:"bootloader_hash,omitempty"`                   // hash of the bootloader
	Imported             *bool   `protobuf:"varint,15,opt,name=imported" json:"imported,omitempty"`                                                    // was storage imported from an external source?
	PinCached            *bool   `protobuf:"varint,16,opt,name=pin_cached,json=pinCached" json:"pin_cached,omitempty"`                                 // is PIN already cached in session?
	PassphraseCached     *bool   `protobuf:"varint,17,opt,name=passphrase_cached,json=passphraseCached" json:"passphrase_cached,omitempty"`            // is passphrase already cached in session?
	FirmwarePresent      *bool   `protobuf:"varint,18,opt,name=firmware_present,json=firmwarePresent" json:"firmware_present,omitempty"`               // is valid firmware loaded?
	NeedsBackup          *bool   `protobuf:"varint,19,opt,name=needs_backup,json=needsBackup" json:"needs_backup,omitempty"`                           // does storage need backup? (equals to Storage.needs_backup)
	Flags                *uint32 `protobuf:"varint,20,opt,name=flags" json:"flags,omitempty"`                                                          // device flags (equals to Storage.flags)
	Model                *string `protobuf:"bytes,21,opt,name=model" json:"model,omitempty"`                                                           // device hardware model
	FwMajor              *uint32 `protobuf:"varint,22,opt,name=fw_major,json=fwMajor" json:"fw_major,omitempty"`                                       // reported firmware version if in bootloader mode
	FwMinor              *uint32 `protobuf:"varint,23,opt,name=fw_minor,json=fwMinor" json:"fw_minor,omitempty"`                                       // reported firmware version if in bootloader mode
	FwPatch              *uint32 `protobuf:"varint,24,opt,name=fw_patch,json=fwPatch" json:"fw_patch,omitempty"`                                       // reported firmware version if in bootloader mode
	FwVendor             *string `protobuf:"bytes,25,opt,name=fw_vendor,json=fwVendor" json:"fw_vendor,omitempty"`                                     // reported firmware vendor if in bootloader mode
	FwVendorKeys         []byte  `protobuf:"bytes,26,opt,name=fw_vendor_keys,json=fwVendorKeys" json:"fw_vendor_keys,omitempty"`                       // reported firmware vendor keys (their hash)
	UnfinishedBackup     *bool   `protobuf:"varint,27,opt,name=unfinished_backup,json=unfinishedBackup" json:"unfinished_backup,omitempty"`            // report unfinished backup (equals to Storage.unfinished_backup)
	NoBackup             *bool   `protobuf:"varint,28,opt,name=no_backup,json=noBackup" json:"no_backup,omitempty"`                                    // report no backup (equals to Storage.no_backup)
}

func (x *Features) Reset() {
	*x = Features{}
	if protoimpl.UnsafeEnabled {
		mi := &file_messages_management_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Features) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Features) ProtoMessage() {}

func (x *Features) ProtoReflect() protoreflect.Message {
	mi := &file_messages_management_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Features.ProtoReflect.Descriptor instead.
func (*Features) Descriptor() ([]byte, []int) {
	return file_messages_management_proto_rawDescGZIP(), []int{2}
}

func (x *Features) GetVendor() string {
	if x != nil && x.Vendor != nil {
		return *x.Vendor
	}

	return ""
}

func (x *Features) GetMajorVersion() uint32 {
	if x != nil && x.MajorVersion != nil {
		return *x.MajorVersion
	}

	return 0
}

func (x *Features) GetMinorVersion() uint32 {
	if x != nil && x.MinorVersion != nil {
		return *x.MinorVersion
	}

	return 0
}

func (x *Features) GetPatchVersion() uint32 {
	if x != nil && x.PatchVersion != nil {
		return *x.PatchVersion
	}

	return 0
}

func (x *Features) GetBootloaderMode() bool {
	if x != nil && x.BootloaderMode != nil {
		return *x.BootloaderMode
	}

	return false
}

func (x *Features) GetDeviceId() string {
	if x != nil && x.DeviceId != nil {
		return *x.DeviceId
	}

	return ""
}

func (x *Features) GetPinProtection() bool {
	if x != nil && x.PinProtection != nil {
		return *x.PinProtection
	}

	return false
}

func (x *Features) GetPassphraseProtection() bool {
	if x != nil && x.PassphraseProtection != nil {
		return *x.PassphraseProtection
	}

	return false
}

func (x *Features) GetLanguage() string {
	if x != nil && x.Language != nil {
		return *x.Language
	}

	return ""
}

func (x *Features) GetLabel() string {
	if x != nil && x.Label != nil {
		return *x.Label
	}

	return ""
}

func (x *Features) GetInitialized() bool {
	if x != nil && x.Initialized != nil {
		return *x.Initialized
	}

	return false
}

func (x *Features) GetRevision() []byte {
	if x != nil {
		return x.Revision
	}

	return nil
}

func (x *Features) GetBootloaderHash() []byte {
	if x != nil {
		return x.BootloaderHash
	}

	return nil
}

func (x *Features) GetImported() bool {
	if x != nil && x.Imported != nil {
		return *x.Imported
	}

	return false
}

func (x *Features) GetPinCached() bool {
	if x != nil && x.PinCached != nil {
		return *x.PinCached
	}

	return false
}

func (x *Features) GetPassphraseCached() bool {
	if x != nil && x.PassphraseCached != nil {
		return *x.PassphraseCached
	}

	return false
}

func (x *Features) GetFirmwarePresent() bool {
	if x != nil && x.FirmwarePresent != nil {
		return *x.FirmwarePresent
	}

	return false
}

func (x *Features) GetNeedsBackup() bool {
	if x != nil && x.NeedsBackup != nil {
		return *x.NeedsBackup
	}

	return false
}

func (x *Features) GetFlags() uint32 {
	if x != nil && x.Flags != nil {
		return *x.Flags
	}

	return 0
}

func (x *Features) GetModel() string {
	if x != nil && x.Model != nil {
		return *x.Model
	}

	return ""
}

func (x *Features) GetFwMajor() uint32 {
	if x != nil && x.FwMajor != nil {
		return *x.FwMajor
	}

	return 0
}

func (x *Features) GetFwMinor() uint32 {
	if x != nil && x.FwMinor != nil {
		return *x.FwMinor
	}

	return 0
}

func (x *Features) GetFwPatch() uint32 {
	if x != nil && x.FwPatch != nil {
		return *x.FwPatch
	}

	return 0
}

func (x *Features) GetFwVendor() string {
	if x != nil && x.FwVendor != nil {
		return *x.FwVendor
	}

	return ""
}

func (x *Features) GetFwVendorKeys() []byte {
	if x != nil {
		return x.FwVendorKeys
	}

	return nil
}

func (x *Features) GetUnfinishedBackup() bool {
	if x != nil && x.UnfinishedBackup != nil {
		return *x.UnfinishedBackup
	}

	return false
}

func (x *Features) GetNoBackup() bool {
	if x != nil && x.NoBackup != nil {
		return *x.NoBackup
	}

	return false
}

// *
// Request: clear session (removes cached PIN, passphrase, etc).
// @start
// @next Success
type ClearSession struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ClearSession) Reset() {
	*x = ClearSession{}
	if protoimpl.UnsafeEnabled {
		mi := &file_messages_management_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClearSession) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClearSession) ProtoMessage() {}

func (x *ClearSession) ProtoReflect() protoreflect.Message {
	mi := &file_messages_management_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClearSession.ProtoReflect.Descriptor instead.
func (*ClearSession) Descriptor() ([]byte, []int) {
	return file_messages_management_proto_rawDescGZIP(), []int{3}
}

// *
// Request: change language and/or label of the device
// @start
// @next Success
// @next Failure
type ApplySettings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Language         *string                             `protobuf:"bytes,1,opt,name=language" json:"language,omitempty"`
	Label            *string                             `protobuf:"bytes,2,opt,name=label" json:"label,omitempty"`
	UsePassphrase    *bool                               `protobuf:"varint,3,opt,name=use_passphrase,json=usePassphrase" json:"use_passphrase,omitempty"`
	Homescreen       []byte                              `protobuf:"bytes,4,opt,name=homescreen" json:"homescreen,omitempty"`
	PassphraseSource *ApplySettings_PassphraseSourceType `protobuf:"varint,5,opt,name=passphrase_source,json=passphraseSource,enum=hw.trezor.messages.management.ApplySettings_PassphraseSourceType" json:"passphrase_source,omitempty"`
	AutoLockDelayMs  *uint32                             `protobuf:"varint,6,opt,name=auto_lock_delay_ms,json=autoLockDelayMs" json:"auto_lock_delay_ms,omitempty"`
	DisplayRotation  *uint32                             `protobuf:"varint,7,opt,name=display_rotation,json=displayRotation" json:"display_rotation,omitempty"` // in degrees from North
}

func (x *ApplySettings) Reset() {
	*x = ApplySettings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_messages_management_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplySettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplySettings) ProtoMessage() {}

func (x *ApplySettings) ProtoReflect() protoreflect.Message {
	mi := &file_messages_management_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplySettings.ProtoReflect.Descriptor instead.
func (*ApplySettings) Descriptor() ([]byte, []int) {
	return file_messages_management_proto_rawDescGZIP(), []int{4}
}

func (x *ApplySettings) GetLanguage() string {
	if x != nil && x.Language != nil {
		return *x.Language
	}

	return ""
}

func (x *ApplySettings) GetLabel() string {
	if x != nil && x.Label != nil {
		return *x.Label
	}

	return ""
}

func (x *ApplySettings) GetUsePassphrase() bool {
	if x != nil && x.UsePassphrase != nil {
		return *x.UsePassphrase
	}

	return false
}

func (x *ApplySettings) GetHomescreen() []byte {
	if x != nil {
		return x.Homescreen
	}

	return nil
}

func (x *ApplySettings) GetPassphraseSource() ApplySettings_PassphraseSourceType {
	if x != nil && x.PassphraseSource != nil {
		return *x.PassphraseSource
	}

	return ApplySettings_ASK
}

func (x *ApplySettings) GetAutoLockDelayMs() uint32 {
	if x != nil && x.AutoLockDelayMs != nil {
		return *x.AutoLockDelayMs
	}

	return 0
}

func (x *ApplySettings) GetDisplayRotation() uint32 {
	if x != nil && x.DisplayRotation != nil {
		return *x.DisplayRotation
	}

	return 0
}

// *
// Request: set flags of the device
// @start
// @next Success
// @next Failure
type ApplyFlags struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Flags *uint32 `protobuf:"varint,1,opt,name=flags" json:"flags,omitempty"` // bitmask, can only set bits, not unset
}

func (x *ApplyFlags) Reset() {
	*x = ApplyFlags{}
	if protoimpl.UnsafeEnabled {
		mi := &file_messages_management_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplyFlags) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplyFlags) ProtoMessage() {}

func (x *ApplyFlags) ProtoReflect() protoreflect.Message {
	mi := &file_messages_management_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplyFlags.ProtoReflect.Descriptor instead.
func (*ApplyFlags) Descriptor() ([]byte, []int) {
	return file_messages_management_proto_rawDescGZIP(), []int{5}
}

func (x *ApplyFlags) GetFlags() uint32 {
	if x != nil && x.Flags != nil {
		return *x.Flags
	}

	return 0
}

// *
// Request: Starts workflow for setting/changing/removing the PIN
// @start
// @next Success
// @next Failure
type ChangePin struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Remove *bool `protobuf:"varint,1,opt,name=remove" json:"remove,omitempty"` // is PIN removal requested?
}

func (x *ChangePin) Reset() {
	*x = ChangePin{}
	if protoimpl.UnsafeEnabled {
		mi := &file_messages_management_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChangePin) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangePin) ProtoMessage() {}

func (x *ChangePin) ProtoReflect() protoreflect.Message {
	mi := &file_messages_management_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangePin.ProtoReflect.Descriptor instead.
func (*ChangePin) Descriptor() ([]byte, []int) {
	return file_messages_management_proto_rawDescGZIP(), []int{6}
}

func (x *ChangePin) GetRemove() bool {
	if x != nil && x.Remove != nil {
		return *x.Remove
	}

	return false
}

// *
// Request: Test if the device is alive, device sends back the message in Success response
// @start
// @next Success
type Ping struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Message              *string `protobuf:"bytes,1,opt,name=message" json:"message,omitempty"`                                                        // message to send back in Success message
	ButtonProtection     *bool   `protobuf:"varint,2,opt,name=button_protection,json=buttonProtection" json:"button_protection,omitempty"`             // ask for button press
	PinProtection        *bool   `protobuf:"varint,3,opt,name=pin_protection,json=pinProtection" json:"pin_protection,omitempty"`                      // ask for PIN if set in device
	PassphraseProtection *bool   `protobuf:"varint,4,opt,name=passphrase_protection,json=passphraseProtection" json:"passphrase_protection,omitempty"` // ask for passphrase if set in device
}

func (x *Ping) Reset() {
	*x = Ping{}
	if protoimpl.UnsafeEnabled {
		mi := &file_messages_management_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Ping) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Ping) ProtoMessage() {}

func (x *Ping) ProtoReflect() protoreflect.Message {
	mi := &file_messages_management_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Ping.ProtoReflect.Descriptor instead.
func (*Ping) Descriptor() ([]byte, []int) {
	return file_messages_management_proto_rawDescGZIP(), []int{7}
}

func (x *Ping) GetMessage() string {
	if x != nil && x.Message != nil {
		return *x.Message
	}

	return ""
}

func (x *Ping) GetButtonProtection() bool {
	if x != nil && x.ButtonProtection != nil {
		return *x.ButtonProtection
	}

	return false
}

func (x *Ping) GetPinProtection() bool {
	if x != nil && x.PinProtection != nil {
		return *x.PinProtection
	}

	return false
}

func (x *Ping) GetPassphraseProtection() bool {
	if x != nil && x.PassphraseProtection != nil {
		return *x.PassphraseProtection
	}

	return false
}

// *
// Request: Abort last operation that required user interaction
// @start
// @next Failure
type Cancel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Cancel) Reset() {
	*x = Cancel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_messages_management_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Cancel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Cancel) ProtoMessage() {}

func (x *Cancel) ProtoReflect() protoreflect.Message {
	mi := &file_messages_management_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Cancel.ProtoReflect.Descriptor instead.
func (*Cancel) Descriptor() ([]byte, []int) {
	return file_messages_management_proto_rawDescGZIP(), []int{8}
}

// *
// Request: Request a sample of random data generated by hardware RNG. May be used for testing.
// @start
// @next Entropy
// @next Failure
type GetEntropy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Size *uint32 `protobuf:"varint,1,req,name=size" json:"size,omitempty"` // size of requested entropy
}

func (x *GetEntropy) Reset() {
	*x = GetEntropy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_messages_management_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEntropy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEntropy) ProtoMessage() {}

func (x *GetEntropy) ProtoReflect() protoreflect.Message {
	mi := &file_messages_management_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEntropy.ProtoReflect.Descriptor instead.
func (*GetEntropy) Descriptor() ([]byte, []int) {
	return file_messages_management_proto_rawDescGZIP(), []int{9}
}

func (x *GetEntropy) GetSize() uint32 {
	if x != nil && x.Size != nil {
		return *x.Size
	}

	return 0
}

// *
// Response: Reply with random data generated by internal RNG
// @end
type Entropy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Entropy []byte `protobuf:"bytes,1,req,name=entropy" json:"entropy,omitempty"` // chunk of random generated bytes
}

func (x *Entropy) Reset() {
	*x = Entropy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_messages_management_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Entropy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Entropy) ProtoMessage() {}

func (x *Entropy) ProtoReflect() protoreflect.Message {
	mi := &file_messages_management_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Entropy.ProtoReflect.Descriptor instead.
func (*Entropy) Descriptor() ([]byte, []int) {
	return file_messages_management_proto_rawDescGZIP(), []int{10}
}

func (x *Entropy) GetEntropy() []byte {
	if x != nil {
		return x.Entropy
	}

	return nil
}

// *
// Request: Request device to wipe all sensitive data and settings
// @start
// @next Success
// @next Failure
type WipeDevice struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *WipeDevice) Reset() {
	*x = WipeDevice{}
	if protoimpl.UnsafeEnabled {
		mi := &file_messages_management_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WipeDevice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WipeDevice) ProtoMessage() {}

func (x *WipeDevice) ProtoReflect() protoreflect.Message {
	mi := &file_messages_management_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WipeDevice.ProtoReflect.Descriptor instead.
func (*WipeDevice) Descriptor() ([]byte, []int) {
	return file_messages_management_proto_rawDescGZIP(), []int{11}
}

// *
// Request: Load seed and related internal settings from the computer
// @start
// @next Success
// @next Failure
type LoadDevice struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Mnemonic             *string     `protobuf:"bytes,1,opt,name=mnemonic" json:"mnemonic,omitempty"`                                                      // seed encoded as BIP-39 mnemonic (12, 18 or 24 words)
	Node                 *HDNodeType `protobuf:"bytes,2,opt,name=node" json:"node,omitempty"`                                                              // BIP-32 node
	Pin                  *string     `protobuf:"bytes,3,opt,name=pin" json:"pin,omitempty"`                                                                // set PIN protection
	PassphraseProtection *bool       `protobuf:"varint,4,opt,name=passphrase_protection,json=passphraseProtection" json:"passphrase_protection,omitempty"` // enable master node encryption using passphrase
	Language             *string     `protobuf:"bytes,5,opt,name=language,def=english" json:"language,omitempty"`                                          // device language
	Label                *string     `protobuf:"bytes,6,opt,name=label" json:"label,omitempty"`                                                            // device label
	SkipChecksum         *bool       `protobuf:"varint,7,opt,name=skip_checksum,json=skipChecksum" json:"skip_checksum,omitempty"`                         // do not test mnemonic for valid BIP-39 checksum
	U2FCounter           *uint32     `protobuf:"varint,8,opt,name=u2f_counter,json=u2fCounter" json:"u2f_counter,omitempty"`                               // U2F counter
}

// Default values for LoadDevice fields.
const (
	Default_LoadDevice_Language = string("english")
)

func (x *LoadDevice) Reset() {
	*x = LoadDevice{}
	if protoimpl.UnsafeEnabled {
		mi := &file_messages_management_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoadDevice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadDevice) ProtoMessage() {}

func (x *LoadDevice) ProtoReflect() protoreflect.Message {
	mi := &file_messages_management_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadDevice.ProtoReflect.Descriptor instead.
func (*LoadDevice) Descriptor() ([]byte, []int) {
	return file_messages_management_proto_rawDescGZIP(), []int{12}
}

func (x *LoadDevice) GetMnemonic() string {
	if x != nil && x.Mnemonic != nil {
		return *x.Mnemonic
	}

	return ""
}

func (x *LoadDevice) GetNode() *HDNodeType {
	if x != nil {
		return x.Node
	}

	return nil
}

func (x *LoadDevice) GetPin() string {
	if x != nil && x.Pin != nil {
		return *x.Pin
	}

	return ""
}

func (x *LoadDevice) GetPassphraseProtection() bool {
	if x != nil && x.PassphraseProtection != nil {
		return *x.PassphraseProtection
	}

	return false
}

func (x *LoadDevice) GetLanguage() string {
	if x != nil && x.Language != nil {
		return *x.Language
	}

	return Default_LoadDevice_Language
}

func (x *LoadDevice) GetLabel() string {
	if x != nil && x.Label != nil {
		return *x.Label
	}

	return ""
}

func (x *LoadDevice) GetSkipChecksum() bool {
	if x != nil && x.SkipChecksum != nil {
		return *x.SkipChecksum
	}

	return false
}

func (x *LoadDevice) GetU2FCounter() uint32 {
	if x != nil && x.U2FCounter != nil {
		return *x.U2FCounter
	}

	return 0
}

// *
// Request: Ask device to do initialization involving user interaction
// @start
// @next EntropyRequest
// @next Failure
type ResetDevice struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DisplayRandom        *bool   `protobuf:"varint,1,opt,name=display_random,json=displayRandom" json:"display_random,omitempty"`                      // display entropy generated by the device before asking for additional entropy
	Strength             *uint32 `protobuf:"varint,2,opt,name=strength,def=256" json:"strength,omitempty"`                                             // strength of seed in bits
	PassphraseProtection *bool   `protobuf:"varint,3,opt,name=passphrase_protection,json=passphraseProtection" json:"passphrase_protection,omitempty"` // enable master node encryption using passphrase
	PinProtection        *bool   `protobuf:"varint,4,opt,name=pin_protection,json=pinProtection" json:"pin_protection,omitempty"`                      // enable PIN protection
	Language             *string `protobuf:"bytes,5,opt,name=language,def=english" json:"language,omitempty"`                                          // device language
	Label                *string `protobuf:"bytes,6,opt,name=label" json:"label,omitempty"`                                                            // device label
	U2FCounter           *uint32 `protobuf:"varint,7,opt,name=u2f_counter,json=u2fCounter" json:"u2f_counter,omitempty"`                               // U2F counter
	SkipBackup           *bool   `protobuf:"varint,8,opt,name=skip_backup,json=skipBackup" json:"skip_backup,omitempty"`                               // postpone seed backup to BackupDevice workflow
	NoBackup             *bool   `protobuf:"varint,9,opt,name=no_backup,json=noBackup" json:"no_backup,omitempty"`                                     // indicate that no backup is going to be made
}

// Default values for ResetDevice fields.
const (
	Default_ResetDevice_Strength = uint32(256)
	Default_ResetDevice_Language = string("english")
)

func (x *ResetDevice) Reset() {
	*x = ResetDevice{}
	if protoimpl.UnsafeEnabled {
		mi := &file_messages_management_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResetDevice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetDevice) ProtoMessage() {}

func (x *ResetDevice) ProtoReflect() protoreflect.Message {
	mi := &file_messages_management_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetDevice.ProtoReflect.Descriptor instead.
func (*ResetDevice) Descriptor() ([]byte, []int) {
	return file_messages_management_proto_rawDescGZIP(), []int{13}
}

func (x *ResetDevice) GetDisplayRandom() bool {
	if x != nil && x.DisplayRandom != nil {
		return *x.DisplayRandom
	}

	return false
}

func (x *ResetDevice) GetStrength() uint32 {
	if x != nil && x.Strength != nil {
		return *x.Strength
	}

	return Default_ResetDevice_Strength
}

func (x *ResetDevice) GetPassphraseProtection() bool {
	if x != nil && x.PassphraseProtection != nil {
		return *x.PassphraseProtection
	}

	return false
}

func (x *ResetDevice) GetPinProtection() bool {
	if x != nil && x.PinProtection != nil {
		return *x.PinProtection
	}

	return false
}

func (x *ResetDevice) GetLanguage() string {
	if x != nil && x.Language != nil {
		return *x.Language
	}

	return Default_ResetDevice_Language
}

func (x *ResetDevice) GetLabel() string {
	if x != nil && x.Label != nil {
		return *x.Label
	}

	return ""
}

func (x *ResetDevice) GetU2FCounter() uint32 {
	if x != nil && x.U2FCounter != nil {
		return *x.U2FCounter
	}

	return 0
}

func (x *ResetDevice) GetSkipBackup() bool {
	if x != nil && x.SkipBackup != nil {
		return *x.SkipBackup
	}

	return false
}

func (x *ResetDevice) GetNoBackup() bool {
	if x != nil && x.NoBackup != nil {
		return *x.NoBackup
	}

	return false
}

// *
// Request: Perform backup of the device seed if not backed up using ResetDevice
// @start
// @next Success
type BackupDevice struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *BackupDevice) Reset() {
	*x = BackupDevice{}
	if protoimpl.UnsafeEnabled {
		mi := &file_messages_management_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BackupDevice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BackupDevice) ProtoMessage() {}

func (x *BackupDevice) ProtoReflect() protoreflect.Message {
	mi := &file_messages_management_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BackupDevice.ProtoReflect.Descriptor instead.
func (*BackupDevice) Descriptor() ([]byte, []int) {
	return file_messages_management_proto_rawDescGZIP(), []int{14}
}

// *
// Response: Ask for additional entropy from host computer
// @next EntropyAck
type EntropyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EntropyRequest) Reset() {
	*x = EntropyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_messages_management_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EntropyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EntropyRequest) ProtoMessage() {}

func (x *EntropyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_messages_management_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EntropyRequest.ProtoReflect.Descriptor instead.
func (*EntropyRequest) Descriptor() ([]byte, []int) {
	return file_messages_management_proto_rawDescGZIP(), []int{15}
}

// *
// Request: Provide additional entropy for seed generation function
// @next Success
type EntropyAck struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Entropy []byte `protobuf:"bytes,1,opt,name=entropy" json:"entropy,omitempty"` // 256 bits (32 bytes) of random data
}

func (x *EntropyAck) Reset() {
	*x = EntropyAck{}
	if protoimpl.UnsafeEnabled {
		mi := &file_messages_management_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EntropyAck) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EntropyAck) ProtoMessage() {}

func (x *EntropyAck) ProtoReflect() protoreflect.Message {
	mi := &file_messages_management_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EntropyAck.ProtoReflect.Descriptor instead.
func (*EntropyAck) Descriptor() ([]byte, []int) {
	return file_messages_management_proto_rawDescGZIP(), []int{16}
}

func (x *EntropyAck) GetEntropy() []byte {
	if x != nil {
		return x.Entropy
	}

	return nil
}

// *
// Request: Start recovery workflow asking user for specific words of mnemonic
// Used to recovery device safely even on untrusted computer.
// @start
// @next WordRequest
type RecoveryDevice struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WordCount            *uint32 `protobuf:"varint,1,opt,name=word_count,json=wordCount" json:"word_count,omitempty"`                                  // number of words in BIP-39 mnemonic
	PassphraseProtection *bool   `protobuf:"varint,2,opt,name=passphrase_protection,json=passphraseProtection" json:"passphrase_protection,omitempty"` // enable master node encryption using passphrase
	PinProtection        *bool   `protobuf:"varint,3,opt,name=pin_protection,json=pinProtection" json:"pin_protection,omitempty"`                      // enable PIN protection
	Language             *string `protobuf:"bytes,4,opt,name=language,def=english" json:"language,omitempty"`                                          // device language
	Label                *string `protobuf:"bytes,5,opt,name=label" json:"label,omitempty"`                                                            // device label
	EnforceWordlist      *bool   `protobuf:"varint,6,opt,name=enforce_wordlist,json=enforceWordlist" json:"enforce_wordlist,omitempty"`                // enforce BIP-39 wordlist during the process
	// 7 reserved for unused recovery method
	Type       *RecoveryDevice_RecoveryDeviceType `protobuf:"varint,8,opt,name=type,enum=hw.trezor.messages.management.RecoveryDevice_RecoveryDeviceType" json:"type,omitempty"` // supported recovery type
	U2FCounter *uint32                            `protobuf:"varint,9,opt,name=u2f_counter,json=u2fCounter" json:"u2f_counter,omitempty"`                                        // U2F counter
	DryRun     *bool                              `protobuf:"varint,10,opt,name=dry_run,json=dryRun" json:"dry_run,omitempty"`                                                   // perform dry-run recovery workflow (for safe mnemonic validation)
}

// Default values for RecoveryDevice fields.
const (
	Default_RecoveryDevice_Language = string("english")
)

func (x *RecoveryDevice) Reset() {
	*x = RecoveryDevice{}
	if protoimpl.UnsafeEnabled {
		mi := &file_messages_management_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecoveryDevice) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecoveryDevice) ProtoMessage() {}

func (x *RecoveryDevice) ProtoReflect() protoreflect.Message {
	mi := &file_messages_management_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecoveryDevice.ProtoReflect.Descriptor instead.
func (*RecoveryDevice) Descriptor() ([]byte, []int) {
	return file_messages_management_proto_rawDescGZIP(), []int{17}
}

func (x *RecoveryDevice) GetWordCount() uint32 {
	if x != nil && x.WordCount != nil {
		return *x.WordCount
	}

	return 0
}

func (x *RecoveryDevice) GetPassphraseProtection() bool {
	if x != nil && x.PassphraseProtection != nil {
		return *x.PassphraseProtection
	}

	return false
}

func (x *RecoveryDevice) GetPinProtection() bool {
	if x != nil && x.PinProtection != nil {
		return *x.PinProtection
	}

	return false
}

func (x *RecoveryDevice) GetLanguage() string {
	if x != nil && x.Language != nil {
		return *x.Language
	}

	return Default_RecoveryDevice_Language
}

func (x *RecoveryDevice) GetLabel() string {
	if x != nil && x.Label != nil {
		return *x.Label
	}

	return ""
}

func (x *RecoveryDevice) GetEnforceWordlist() bool {
	if x != nil && x.EnforceWordlist != nil {
		return *x.EnforceWordlist
	}

	return false
}

func (x *RecoveryDevice) GetType() RecoveryDevice_RecoveryDeviceType {
	if x != nil && x.Type != nil {
		return *x.Type
	}

	return RecoveryDevice_RecoveryDeviceType_ScrambledWords
}

func (x *RecoveryDevice) GetU2FCounter() uint32 {
	if x != nil && x.U2FCounter != nil {
		return *x.U2FCounter
	}

	return 0
}

func (x *RecoveryDevice) GetDryRun() bool {
	if x != nil && x.DryRun != nil {
		return *x.DryRun
	}

	return false
}

// *
// Response: Device is waiting for user to enter word of the mnemonic
// Its position is shown only on device's internal display.
// @next WordAck
type WordRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type *WordRequest_WordRequestType `protobuf:"varint,1,opt,name=type,enum=hw.trezor.messages.management.WordRequest_WordRequestType" json:"type,omitempty"`
}

func (x *WordRequest) Reset() {
	*x = WordRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_messages_management_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WordRequest) ProtoMessage() {}

func (x *WordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_messages_management_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WordRequest.ProtoReflect.Descriptor instead.
func (*WordRequest) Descriptor() ([]byte, []int) {
	return file_messages_management_proto_rawDescGZIP(), []int{18}
}

func (x *WordRequest) GetType() WordRequest_WordRequestType {
	if x != nil && x.Type != nil {
		return *x.Type
	}

	return WordRequest_WordRequestType_Plain
}

// *
// Request: Computer replies with word from the mnemonic
// @next WordRequest
// @next Success
// @next Failure
type WordAck struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Word *string `protobuf:"bytes,1,req,name=word" json:"word,omitempty"` // one word of mnemonic on asked position
}

func (x *WordAck) Reset() {
	*x = WordAck{}
	if protoimpl.UnsafeEnabled {
		mi := &file_messages_management_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WordAck) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WordAck) ProtoMessage() {}

func (x *WordAck) ProtoReflect() protoreflect.Message {
	mi := &file_messages_management_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WordAck.ProtoReflect.Descriptor instead.
func (*WordAck) Descriptor() ([]byte, []int) {
	return file_messages_management_proto_rawDescGZIP(), []int{19}
}

func (x *WordAck) GetWord() string {
	if x != nil && x.Word != nil {
		return *x.Word
	}

	return ""
}

// *
// Request: Set U2F counter
// @start
// @next Success
type SetU2FCounter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	U2FCounter *uint32 `protobuf:"varint,1,opt,name=u2f_counter,json=u2fCounter" json:"u2f_counter,omitempty"` // counter
}

func (x *SetU2FCounter) Reset() {
	*x = SetU2FCounter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_messages_management_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetU2FCounter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetU2FCounter) ProtoMessage() {}

func (x *SetU2FCounter) ProtoReflect() protoreflect.Message {
	mi := &file_messages_management_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetU2FCounter.ProtoReflect.Descriptor instead.
func (*SetU2FCounter) Descriptor() ([]byte, []int) {
	return file_messages_management_proto_rawDescGZIP(), []int{20}
}

func (x *SetU2FCounter) GetU2FCounter() uint32 {
	if x != nil && x.U2FCounter != nil {
		return *x.U2FCounter
	}

	return 0
}

var File_messages_management_proto protoreflect.FileDescriptor

var file_messages_management_proto_rawDesc = []byte{
	0x0a, 0x19, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2d, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1d, 0x68, 0x77, 0x2e,
	0x74, 0x72, 0x65, 0x7a, 0x6f, 0x72, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x1a, 0x15, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x73, 0x2d, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x4b, 0x0a, 0x0a, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x6b, 0x69, 0x70, 0x5f, 0x70, 0x61,
	0x73, 0x73, 0x70, 0x68, 0x72, 0x61, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e,
	0x73, 0x6b, 0x69, 0x70, 0x50, 0x61, 0x73, 0x73, 0x70, 0x68, 0x72, 0x61, 0x73, 0x65, 0x22, 0x0d,
	0x0a, 0x0b, 0x47, 0x65, 0x74, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x22, 0x8c, 0x07,
	0x0a, 0x08, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x61, 0x6a, 0x6f, 0x72, 0x5f, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c, 0x6d, 0x61, 0x6a, 0x6f, 0x72,
	0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x69, 0x6e, 0x6f, 0x72,
	0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0c,
	0x6d, 0x69, 0x6e, 0x6f, 0x72, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d,
	0x70, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0c, 0x70, 0x61, 0x74, 0x63, 0x68, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x27, 0x0a, 0x0f, 0x62, 0x6f, 0x6f, 0x74, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x72, 0x5f,
	0x6d, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x62, 0x6f, 0x6f, 0x74,
	0x6c, 0x6f, 0x61, 0x64, 0x65, 0x72, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x70, 0x69, 0x6e, 0x5f, 0x70,
	0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0d, 0x70, 0x69, 0x6e, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x33,
	0x0a, 0x15, 0x70, 0x61, 0x73, 0x73, 0x70, 0x68, 0x72, 0x61, 0x73, 0x65, 0x5f, 0x70, 0x72, 0x6f,
	0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x70,
	0x61, 0x73, 0x73, 0x70, 0x68, 0x72, 0x61, 0x73, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x6c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x20, 0x0a, 0x0b, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c,
	0x69, 0x7a, 0x65, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x72, 0x65, 0x76, 0x69, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x27, 0x0a, 0x0f, 0x62, 0x6f, 0x6f, 0x74, 0x6c, 0x6f, 0x61, 0x64, 0x65,
	0x72, 0x5f, 0x68, 0x61, 0x73, 0x68, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0e, 0x62, 0x6f,
	0x6f, 0x74, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x72, 0x48, 0x61, 0x73, 0x68, 0x12, 0x1a, 0x0a, 0x08,
	0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08,
	0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x69, 0x6e, 0x5f,
	0x63, 0x61, 0x63, 0x68, 0x65, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x70, 0x69,
	0x6e, 0x43, 0x61, 0x63, 0x68, 0x65, 0x64, 0x12, 0x2b, 0x0a, 0x11, 0x70, 0x61, 0x73, 0x73, 0x70,
	0x68, 0x72, 0x61, 0x73, 0x65, 0x5f, 0x63, 0x61, 0x63, 0x68, 0x65, 0x64, 0x18, 0x11, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x10, 0x70, 0x61, 0x73, 0x73, 0x70, 0x68, 0x72, 0x61, 0x73, 0x65, 0x43, 0x61,
	0x63, 0x68, 0x65, 0x64, 0x12, 0x29, 0x0a, 0x10, 0x66, 0x69, 0x72, 0x6d, 0x77, 0x61, 0x72, 0x65,
	0x5f, 0x70, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f,
	0x66, 0x69, 0x72, 0x6d, 0x77, 0x61, 0x72, 0x65, 0x50, 0x72, 0x65, 0x73, 0x65, 0x6e, 0x74, 0x12,
	0x21, 0x0a, 0x0c, 0x6e, 0x65, 0x65, 0x64, 0x73, 0x5f, 0x62, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x18,
	0x13, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x6e, 0x65, 0x65, 0x64, 0x73, 0x42, 0x61, 0x63, 0x6b,
	0x75, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x6c, 0x61, 0x67, 0x73, 0x18, 0x14, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x05, 0x66, 0x6c, 0x61, 0x67, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x19,
	0x0a, 0x08, 0x66, 0x77, 0x5f, 0x6d, 0x61, 0x6a, 0x6f, 0x72, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0d,
	0x52, 0x07, 0x66, 0x77, 0x4d, 0x61, 0x6a, 0x6f, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x66, 0x77, 0x5f,
	0x6d, 0x69, 0x6e, 0x6f, 0x72, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x66, 0x77, 0x4d,
	0x69, 0x6e, 0x6f, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x66, 0x77, 0x5f, 0x70, 0x61, 0x74, 0x63, 0x68,
	0x18, 0x18, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x66, 0x77, 0x50, 0x61, 0x74, 0x63, 0x68, 0x12,
	0x1b, 0x0a, 0x09, 0x66, 0x77, 0x5f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x19, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x66, 0x77, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x24, 0x0a, 0x0e,
	0x66, 0x77, 0x5f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x6b, 0x65, 0x79, 0x73, 0x18, 0x1a,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x0c, 0x66, 0x77, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x4b, 0x65,
	0x79, 0x73, 0x12, 0x2b, 0x0a, 0x11, 0x75, 0x6e, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64,
	0x5f, 0x62, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x75,
	0x6e, 0x66, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x42, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x12,
	0x1b, 0x0a, 0x09, 0x6e, 0x6f, 0x5f, 0x62, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x18, 0x1c, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x08, 0x6e, 0x6f, 0x42, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x22, 0x0e, 0x0a, 0x0c,
	0x43, 0x6c, 0x65, 0x61, 0x72, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x87, 0x03, 0x0a,
	0x0d, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x1a,
	0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c,
	0x12, 0x25, 0x0a, 0x0e, 0x75, 0x73, 0x65, 0x5f, 0x70, 0x61, 0x73, 0x73, 0x70, 0x68, 0x72, 0x61,
	0x73, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x75, 0x73, 0x65, 0x50, 0x61, 0x73,
	0x73, 0x70, 0x68, 0x72, 0x61, 0x73, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x68, 0x6f, 0x6d, 0x65, 0x73,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a, 0x68, 0x6f, 0x6d,
	0x65, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x12, 0x6e, 0x0a, 0x11, 0x70, 0x61, 0x73, 0x73, 0x70,
	0x68, 0x72, 0x61, 0x73, 0x65, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x41, 0x2e, 0x68, 0x77, 0x2e, 0x74, 0x72, 0x65, 0x7a, 0x6f, 0x72, 0x2e, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73,
	0x2e, 0x50, 0x61, 0x73, 0x73, 0x70, 0x68, 0x72, 0x61, 0x73, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x70, 0x61, 0x73, 0x73, 0x70, 0x68, 0x72, 0x61, 0x73,
	0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x2b, 0x0a, 0x12, 0x61, 0x75, 0x74, 0x6f, 0x5f,
	0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x64, 0x65, 0x6c, 0x61, 0x79, 0x5f, 0x6d, 0x73, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x0f, 0x61, 0x75, 0x74, 0x6f, 0x4c, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x6c,
	0x61, 0x79, 0x4d, 0x73, 0x12, 0x29, 0x0a, 0x10, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f,
	0x72, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f,
	0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x52, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22,
	0x35, 0x0a, 0x14, 0x50, 0x61, 0x73, 0x73, 0x70, 0x68, 0x72, 0x61, 0x73, 0x65, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x07, 0x0a, 0x03, 0x41, 0x53, 0x4b, 0x10, 0x00,
	0x12, 0x0a, 0x0a, 0x06, 0x44, 0x45, 0x56, 0x49, 0x43, 0x45, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04,
	0x48, 0x4f, 0x53, 0x54, 0x10, 0x02, 0x22, 0x22, 0x0a, 0x0a, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x46,
	0x6c, 0x61, 0x67, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x6c, 0x61, 0x67, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x05, 0x66, 0x6c, 0x61, 0x67, 0x73, 0x22, 0x23, 0x0a, 0x09, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x50, 0x69, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x6d, 0x6f, 0x76,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x22,
	0xa9, 0x01, 0x0a, 0x04, 0x50, 0x69, 0x6e, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x62, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x5f, 0x70, 0x72, 0x6f,
	0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x62,
	0x75, 0x74, 0x74, 0x6f, 0x6e, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x25, 0x0a, 0x0e, 0x70, 0x69, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x70, 0x69, 0x6e, 0x50, 0x72, 0x6f, 0x74,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x33, 0x0a, 0x15, 0x70, 0x61, 0x73, 0x73, 0x70, 0x68,
	0x72, 0x61, 0x73, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x70, 0x61, 0x73, 0x73, 0x70, 0x68, 0x72, 0x61, 0x73,
	0x65, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x08, 0x0a, 0x06, 0x43,
	0x61, 0x6e, 0x63, 0x65, 0x6c, 0x22, 0x20, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x72,
	0x6f, 0x70, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x01, 0x20, 0x02, 0x28,
	0x0d, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x22, 0x23, 0x0a, 0x07, 0x45, 0x6e, 0x74, 0x72, 0x6f,
	0x70, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x74, 0x72, 0x6f, 0x70, 0x79, 0x18, 0x01, 0x20,
	0x02, 0x28, 0x0c, 0x52, 0x07, 0x65, 0x6e, 0x74, 0x72, 0x6f, 0x70, 0x79, 0x22, 0x0c, 0x0a, 0x0a,
	0x57, 0x69, 0x70, 0x65, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x22, 0xab, 0x02, 0x0a, 0x0a, 0x4c,
	0x6f, 0x61, 0x64, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x6e, 0x65,
	0x6d, 0x6f, 0x6e, 0x69, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x6e, 0x65,
	0x6d, 0x6f, 0x6e, 0x69, 0x63, 0x12, 0x39, 0x0a, 0x04, 0x6e, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x68, 0x77, 0x2e, 0x74, 0x72, 0x65, 0x7a, 0x6f, 0x72, 0x2e,
	0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x48, 0x44, 0x4e, 0x6f, 0x64, 0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x6e, 0x6f, 0x64, 0x65,
	0x12, 0x10, 0x0a, 0x03, 0x70, 0x69, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x70,
	0x69, 0x6e, 0x12, 0x33, 0x0a, 0x15, 0x70, 0x61, 0x73, 0x73, 0x70, 0x68, 0x72, 0x61, 0x73, 0x65,
	0x5f, 0x70, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x14, 0x70, 0x61, 0x73, 0x73, 0x70, 0x68, 0x72, 0x61, 0x73, 0x65, 0x50, 0x72, 0x6f,
	0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75,
	0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x3a, 0x07, 0x65, 0x6e, 0x67, 0x6c, 0x69,
	0x73, 0x68, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x61, 0x62,
	0x65, 0x6c, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x6b, 0x69, 0x70, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b,
	0x73, 0x75, 0x6d, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x73, 0x6b, 0x69, 0x70, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x73, 0x75, 0x6d, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x32, 0x66, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x75, 0x32,
	0x66, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x22, 0xcb, 0x02, 0x0a, 0x0b, 0x52, 0x65, 0x73,
	0x65, 0x74, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x64, 0x69, 0x73, 0x70,
	0x6c, 0x61, 0x79, 0x5f, 0x72, 0x61, 0x6e, 0x64, 0x6f, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0d, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x52, 0x61, 0x6e, 0x64, 0x6f, 0x6d, 0x12,
	0x1f, 0x0a, 0x08, 0x73, 0x74, 0x72, 0x65, 0x6e, 0x67, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x3a, 0x03, 0x32, 0x35, 0x36, 0x52, 0x08, 0x73, 0x74, 0x72, 0x65, 0x6e, 0x67, 0x74, 0x68,
	0x12, 0x33, 0x0a, 0x15, 0x70, 0x61, 0x73, 0x73, 0x70, 0x68, 0x72, 0x61, 0x73, 0x65, 0x5f, 0x70,
	0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x14, 0x70, 0x61, 0x73, 0x73, 0x70, 0x68, 0x72, 0x61, 0x73, 0x65, 0x50, 0x72, 0x6f, 0x74, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x70, 0x69, 0x6e, 0x5f, 0x70, 0x72, 0x6f,
	0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x70,
	0x69, 0x6e, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x08,
	0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x3a, 0x07,
	0x65, 0x6e, 0x67, 0x6c, 0x69, 0x73, 0x68, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x32, 0x66, 0x5f, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x75, 0x32,
	0x66, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x6b, 0x69, 0x70,
	0x5f, 0x62, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x73,
	0x6b, 0x69, 0x70, 0x42, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x6f, 0x5f,
	0x62, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x6e, 0x6f,
	0x42, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x22, 0x0e, 0x0a, 0x0c, 0x42, 0x61, 0x63, 0x6b, 0x75, 0x70,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x22, 0x10, 0x0a, 0x0e, 0x45, 0x6e, 0x74, 0x72, 0x6f, 0x70,
	0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x26, 0x0a, 0x0a, 0x45, 0x6e, 0x74, 0x72,
	0x6f, 0x70, 0x79, 0x41, 0x63, 0x6b, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x74, 0x72, 0x6f, 0x70,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x65, 0x6e, 0x74, 0x72, 0x6f, 0x70, 0x79,
	0x22, 0xdd, 0x03, 0x0a, 0x0e, 0x52, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x77, 0x6f, 0x72, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09, 0x77, 0x6f, 0x72, 0x64, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x33, 0x0a, 0x15, 0x70, 0x61, 0x73, 0x73, 0x70, 0x68, 0x72, 0x61, 0x73, 0x65,
	0x5f, 0x70, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x14, 0x70, 0x61, 0x73, 0x73, 0x70, 0x68, 0x72, 0x61, 0x73, 0x65, 0x50, 0x72, 0x6f,
	0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x70, 0x69, 0x6e, 0x5f, 0x70,
	0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0d, 0x70, 0x69, 0x6e, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x23,
	0x0a, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x3a, 0x07, 0x65, 0x6e, 0x67, 0x6c, 0x69, 0x73, 0x68, 0x52, 0x08, 0x6c, 0x61, 0x6e, 0x67, 0x75,
	0x61, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x29, 0x0a, 0x10, 0x65, 0x6e, 0x66,
	0x6f, 0x72, 0x63, 0x65, 0x5f, 0x77, 0x6f, 0x72, 0x64, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0f, 0x65, 0x6e, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x57, 0x6f, 0x72, 0x64,
	0x6c, 0x69, 0x73, 0x74, 0x12, 0x54, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x40, 0x2e, 0x68, 0x77, 0x2e, 0x74, 0x72, 0x65, 0x7a, 0x6f, 0x72, 0x2e, 0x6d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x32,
	0x66, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0a, 0x75, 0x32, 0x66, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x64,
	0x72, 0x79, 0x5f, 0x72, 0x75, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x64, 0x72,
	0x79, 0x52, 0x75, 0x6e, 0x22, 0x5a, 0x0a, 0x12, 0x52, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x79,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x25, 0x0a, 0x21, 0x52, 0x65,
	0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x5f, 0x53, 0x63, 0x72, 0x61, 0x6d, 0x62, 0x6c, 0x65, 0x64, 0x57, 0x6f, 0x72, 0x64, 0x73, 0x10,
	0x00, 0x12, 0x1d, 0x0a, 0x19, 0x52, 0x65, 0x63, 0x6f, 0x76, 0x65, 0x72, 0x79, 0x44, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x10, 0x01,
	0x22, 0xc5, 0x01, 0x0a, 0x0b, 0x57, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x4e, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3a,
	0x2e, 0x68, 0x77, 0x2e, 0x74, 0x72, 0x65, 0x7a, 0x6f, 0x72, 0x2e, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x73, 0x2e, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x57,
	0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x57, 0x6f, 0x72, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x22, 0x66, 0x0a, 0x0f, 0x57, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x15, 0x57, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x50, 0x6c, 0x61, 0x69, 0x6e, 0x10, 0x00, 0x12, 0x1b,
	0x0a, 0x17, 0x57, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x5f, 0x4d, 0x61, 0x74, 0x72, 0x69, 0x78, 0x39, 0x10, 0x01, 0x12, 0x1b, 0x0a, 0x17, 0x57,
	0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x5f, 0x4d,
	0x61, 0x74, 0x72, 0x69, 0x78, 0x36, 0x10, 0x02, 0x22, 0x1d, 0x0a, 0x07, 0x57, 0x6f, 0x72, 0x64,
	0x41, 0x63, 0x6b, 0x12, 0x12, 0x0a, 0x04, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x01, 0x20, 0x02, 0x28,
	0x09, 0x52, 0x04, 0x77, 0x6f, 0x72, 0x64, 0x22, 0x30, 0x0a, 0x0d, 0x53, 0x65, 0x74, 0x55, 0x32,
	0x46, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x32, 0x66, 0x5f,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a, 0x75,
	0x32, 0x66, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x42, 0x79, 0x0a, 0x23, 0x63, 0x6f, 0x6d,
	0x2e, 0x73, 0x61, 0x74, 0x6f, 0x73, 0x68, 0x69, 0x6c, 0x61, 0x62, 0x73, 0x2e, 0x74, 0x72, 0x65,
	0x7a, 0x6f, 0x72, 0x2e, 0x6c, 0x69, 0x62, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x42, 0x17, 0x54, 0x72, 0x65, 0x7a, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5a, 0x39, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x74, 0x68, 0x65, 0x72, 0x65, 0x75, 0x6d, 0x2f, 0x67,
	0x6f, 0x2d, 0x65, 0x74, 0x68, 0x65, 0x72, 0x65, 0x75, 0x6d, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x73, 0x2f, 0x75, 0x73, 0x62, 0x77, 0x61, 0x6c, 0x6c, 0x65, 0x74, 0x2f, 0x74, 0x72,
	0x65, 0x7a, 0x6f, 0x72,
}

var (
	file_messages_management_proto_rawDescOnce sync.Once
	file_messages_management_proto_rawDescData = file_messages_management_proto_rawDesc
)

func file_messages_management_proto_rawDescGZIP() []byte {
	file_messages_management_proto_rawDescOnce.Do(func() {
		file_messages_management_proto_rawDescData = protoimpl.X.CompressGZIP(file_messages_management_proto_rawDescData)
	})
	return file_messages_management_proto_rawDescData
}

var file_messages_management_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_messages_management_proto_msgTypes = make([]protoimpl.MessageInfo, 21)
var file_messages_management_proto_goTypes = []any{
	(ApplySettings_PassphraseSourceType)(0), // 0: hw.trezor.messages.management.ApplySettings.PassphraseSourceType
	(RecoveryDevice_RecoveryDeviceType)(0),  // 1: hw.trezor.messages.management.RecoveryDevice.RecoveryDeviceType
	(WordRequest_WordRequestType)(0),        // 2: hw.trezor.messages.management.WordRequest.WordRequestType
	(*Initialize)(nil),                      // 3: hw.trezor.messages.management.Initialize
	(*GetFeatures)(nil),                     // 4: hw.trezor.messages.management.GetFeatures
	(*Features)(nil),                        // 5: hw.trezor.messages.management.Features
	(*ClearSession)(nil),                    // 6: hw.trezor.messages.management.ClearSession
	(*ApplySettings)(nil),                   // 7: hw.trezor.messages.management.ApplySettings
	(*ApplyFlags)(nil),                      // 8: hw.trezor.messages.management.ApplyFlags
	(*ChangePin)(nil),                       // 9: hw.trezor.messages.management.ChangePin
	(*Ping)(nil),                            // 10: hw.trezor.messages.management.Ping
	(*Cancel)(nil),                          // 11: hw.trezor.messages.management.Cancel
	(*GetEntropy)(nil),                      // 12: hw.trezor.messages.management.GetEntropy
	(*Entropy)(nil),                         // 13: hw.trezor.messages.management.Entropy
	(*WipeDevice)(nil),                      // 14: hw.trezor.messages.management.WipeDevice
	(*LoadDevice)(nil),                      // 15: hw.trezor.messages.management.LoadDevice
	(*ResetDevice)(nil),                     // 16: hw.trezor.messages.management.ResetDevice
	(*BackupDevice)(nil),                    // 17: hw.trezor.messages.management.BackupDevice
	(*EntropyRequest)(nil),                  // 18: hw.trezor.messages.management.EntropyRequest
	(*EntropyAck)(nil),                      // 19: hw.trezor.messages.management.EntropyAck
	(*RecoveryDevice)(nil),                  // 20: hw.trezor.messages.management.RecoveryDevice
	(*WordRequest)(nil),                     // 21: hw.trezor.messages.management.WordRequest
	(*WordAck)(nil),                         // 22: hw.trezor.messages.management.WordAck
	(*SetU2FCounter)(nil),                   // 23: hw.trezor.messages.management.SetU2FCounter
	(*HDNodeType)(nil),                      // 24: hw.trezor.messages.common.HDNodeType
}
var file_messages_management_proto_depIdxs = []int32{
	0,  // 0: hw.trezor.messages.management.ApplySettings.passphrase_source:type_name -> hw.trezor.messages.management.ApplySettings.PassphraseSourceType
	24, // 1: hw.trezor.messages.management.LoadDevice.node:type_name -> hw.trezor.messages.common.HDNodeType
	1,  // 2: hw.trezor.messages.management.RecoveryDevice.type:type_name -> hw.trezor.messages.management.RecoveryDevice.RecoveryDeviceType
	2,  // 3: hw.trezor.messages.management.WordRequest.type:type_name -> hw.trezor.messages.management.WordRequest.WordRequestType
	4,  // [4:4] is the sub-list for method output_type
	4,  // [4:4] is the sub-list for method input_type
	4,  // [4:4] is the sub-list for extension type_name
	4,  // [4:4] is the sub-list for extension extendee
	0,  // [0:4] is the sub-list for field type_name
}

func init() { file_messages_management_proto_init() }
func file_messages_management_proto_init() {
	if File_messages_management_proto != nil {
		return
	}
	file_messages_common_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_messages_management_proto_msgTypes[0].Exporter = func(v any, i int) any {
			switch v := v.(*Initialize); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_messages_management_proto_msgTypes[1].Exporter = func(v any, i int) any {
			switch v := v.(*GetFeatures); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_messages_management_proto_msgTypes[2].Exporter = func(v any, i int) any {
			switch v := v.(*Features); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_messages_management_proto_msgTypes[3].Exporter = func(v any, i int) any {
			switch v := v.(*ClearSession); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_messages_management_proto_msgTypes[4].Exporter = func(v any, i int) any {
			switch v := v.(*ApplySettings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_messages_management_proto_msgTypes[5].Exporter = func(v any, i int) any {
			switch v := v.(*ApplyFlags); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_messages_management_proto_msgTypes[6].Exporter = func(v any, i int) any {
			switch v := v.(*ChangePin); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_messages_management_proto_msgTypes[7].Exporter = func(v any, i int) any {
			switch v := v.(*Ping); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_messages_management_proto_msgTypes[8].Exporter = func(v any, i int) any {
			switch v := v.(*Cancel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_messages_management_proto_msgTypes[9].Exporter = func(v any, i int) any {
			switch v := v.(*GetEntropy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_messages_management_proto_msgTypes[10].Exporter = func(v any, i int) any {
			switch v := v.(*Entropy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_messages_management_proto_msgTypes[11].Exporter = func(v any, i int) any {
			switch v := v.(*WipeDevice); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_messages_management_proto_msgTypes[12].Exporter = func(v any, i int) any {
			switch v := v.(*LoadDevice); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_messages_management_proto_msgTypes[13].Exporter = func(v any, i int) any {
			switch v := v.(*ResetDevice); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_messages_management_proto_msgTypes[14].Exporter = func(v any, i int) any {
			switch v := v.(*BackupDevice); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_messages_management_proto_msgTypes[15].Exporter = func(v any, i int) any {
			switch v := v.(*EntropyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_messages_management_proto_msgTypes[16].Exporter = func(v any, i int) any {
			switch v := v.(*EntropyAck); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_messages_management_proto_msgTypes[17].Exporter = func(v any, i int) any {
			switch v := v.(*RecoveryDevice); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_messages_management_proto_msgTypes[18].Exporter = func(v any, i int) any {
			switch v := v.(*WordRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_messages_management_proto_msgTypes[19].Exporter = func(v any, i int) any {
			switch v := v.(*WordAck); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_messages_management_proto_msgTypes[20].Exporter = func(v any, i int) any {
			switch v := v.(*SetU2FCounter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_messages_management_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   21,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_messages_management_proto_goTypes,
		DependencyIndexes: file_messages_management_proto_depIdxs,
		EnumInfos:         file_messages_management_proto_enumTypes,
		MessageInfos:      file_messages_management_proto_msgTypes,
	}.Build()
	File_messages_management_proto = out.File
	file_messages_management_proto_rawDesc = nil
	file_messages_management_proto_goTypes = nil
	file_messages_management_proto_depIdxs = nil
}
