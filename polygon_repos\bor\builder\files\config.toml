# NOTE: Uncomment and configure the following 8 fields in case you run a validator:
# `mine`, `etherbase`, `nodiscover`, `maxpeers`, `keystore`, `allow-insecure-unlock`, `password`, `unlock`

chain = "mainnet" # Set it to `amoy` for testnet
# identity = "Annon-Identity"
# verbosity = 3
# vmdebug = false
datadir = "/var/lib/bor/data"
# ancient = ""
# "db.engine" = "pebble"
# "state.scheme" = "path"
# keystore = "/var/lib/bor/keystore"
# "rpc.batchlimit" = 100
# "rpc.returndatalimit" = 100000
syncmode = "full"
# gcmode = "full"
# snapshot = true
# "bor.logs" = false
# ethstats = ""
# devfakeauthor = false
# ["eth.requiredblocks"]

# [log]
  # vmodule = ""
  # json = false
  # backtrace = ""
  # debug = true
  # enable-block-tracking = false

[p2p]
  # maxpeers = 1
  # nodiscover = true
  # maxpendpeers = 50
  # bind = "0.0.0.0"
  # port = 30303
  # nat = "any"
  # netrestrict = ""
  # nodekey = ""
  # nodekeyhex = ""
  [p2p.discovery]
    # v4disc = true
    # v5disc = true
    bootnodes = ["enode://b8f1cc9c5d4403703fbf377116469667d2b1823c0daf16b7250aa576bacf399e42c3930ccfcb02c5df6879565a2b8931335565f0e8d3f8e72385ecf4a4bf160a@3.36.224.80:30303", "enode://8729e0c825f3d9cad382555f3e46dcff21af323e89025a0e6312df541f4a9e73abfa562d64906f5e59c51fe6f0501b3e61b07979606c56329c020ed739910759@54.194.245.5:30303"]
    # Uncomment below `bootnodes` field for Amoy
    # bootnodes = ["enode://bce861be777e91b0a5a49d58a51e14f32f201b4c6c2d1fbea6c7a1f14756cbb3f931f3188d6b65de8b07b53ff28d03b6e366d09e56360d2124a9fc5a15a0913d@**************:30303", "enode://4a3dc0081a346d26a73d79dd88216a9402d2292318e2db9947dbc97ea9c4afb2498dc519c0af04420dc13a238c279062da0320181e7c1461216ce4513bfd40bf@**************:30303"]
    # bootnodesv4 = []
    # bootnodesv5 = []
    # static-nodes = []
    # trusted-nodes = []
    dns = [ "enrtree://<EMAIL>" ] # For pos mainnet
    # Uncomment below `dns` field for Amoy
    # dns = [ "enrtree://<EMAIL>" ]


# [heimdall]
  # url = "http://localhost:1317"
  # "bor.without" = false
  # grpc-address = ""

[txpool]
  nolocals = true
  pricelimit = 2********00
  accountslots = 16
  globalslots = 131072
  accountqueue = 64
  globalqueue = 131072
  lifetime = "1h30m0s"
  # locals = []
  # journal = ""
  # rejournal = "1h0m0s"
  # pricebump = 10

[miner]
  gaslimit = ********
  gasprice = "2********00"
  # mine = true
  # etherbase = "VALIDATOR ADDRESS"
  # extradata = ""
  # recommit = "2m5s"
  # commitinterrupt = true


# [jsonrpc]
#   ipcdisable = false
#   ipcpath = ""
#   gascap = ********
#   evmtimeout = "5s"
#   txfeecap = 5.0
#   allow-unprotected-txs = false
#   enabledeprecatedpersonal = false
#   [jsonrpc.http]
#     enabled = false
#     port = 8545
#     prefix = ""
#     host = "localhost"
#     api = ["eth", "net", "web3", "txpool", "bor"]
#     vhosts = ["*"]
#     corsdomain = ["*"]
#     ep-size = 40
#     ep-requesttimeout = "0s"
#   [jsonrpc.ws]
#     enabled = false
#     port = 8546
#     prefix = ""
#     host = "localhost"
#     api = ["web3", "net"]
#     origins = ["*"]
#     ep-size = 40
#     ep-requesttimeout = "0s"
#   [jsonrpc.graphql]
#     enabled = false
#     port = 0
#     prefix = ""
#     host = ""
#     vhosts = ["*"]
#     corsdomain = ["*"]
#   [jsonrpc.auth]
#     jwtsecret = ""
#     addr = "localhost"
#     port = 8551
#     vhosts = ["localhost"]
#   [jsonrpc.timeouts]
#     read = "10s"
#     write = "30s"
#     idle = "2m0s"

[gpo]
  # blocks = 20
  # percentile = 60
  # maxheaderhistory = 1024
  # maxblockhistory = 1024
  # maxprice = "********00000"
  ignoreprice = "2********00"

[telemetry]
  metrics = true
  # expensive = false
  # prometheus-addr = "127.0.0.1:7071"
  # opencollector-endpoint = ""
  # [telemetry.influx]
    # influxdb = false
    # endpoint = ""
    # database = ""
    # username = ""
    # password = ""
    # influxdbv2 = false
    # token = ""
    # bucket = ""
    # organization = ""
  # [telemetry.influx.tags]

# [cache]
  # cache = 1024
  # gc = 25
  # snapshot = 10
  # database = 50
  # trie = 15
  # noprefetch = false
  # preimages = false
  # txlookuplimit = 2350000
  # triesinmemory = 128
  # blocklogs = 32
  # timeout = "1h0m0s"
  # fdlimit = 0

[accounts]
  # allow-insecure-unlock = true
  # password = "/var/lib/bor/password.txt"
  # unlock = ["VALIDATOR ADDRESS"]
  # lightkdf = false
  # disable-bor-wallet = false

# [grpc]
  # addr = ":3131"

# [developer]
  # dev = false
  # period = 0
  # gaslimit = ********

# [parallelevm]
  # enable = true
  # procs = 8
  # enforce = false

# [pprof]
#   pprof = false
#   port = 6060
#   addr = "127.0.0.1"
#   memprofilerate = 524288
#   blockprofilerate = 0
