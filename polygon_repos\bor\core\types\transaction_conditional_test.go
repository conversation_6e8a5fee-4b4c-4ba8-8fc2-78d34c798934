package types

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/require"

	"github.com/ethereum/go-ethereum/common"
)

func TestKnownAccounts(t *testing.T) {
	t.Parallel()

	requestRaw := []byte(`{"******************************************": "0x000000000000000000000000313aadca1750caadc7bcb26ff08175c95dcf8e38", "******************************************": {"0x0000000000000000000000000000000000000000000000000000000000000aaa": "0x0000000000000000000000000000000000000000000000000000000000000bbb", "0x0000000000000000000000000000000000000000000000000000000000000ccc": "0x0000000000000000000000000000000000000000000000000000000000000ddd"}}`)

	accs := &KnownAccounts{}

	err := json.Unmarshal(requestRaw, accs)
	require.NoError(t, err)

	expected := &KnownAccounts{
		common.HexToAddress("******************************************"): SingleFromHex("0x000000000000000000000000313aadca1750caadc7bcb26ff08175c95dcf8e38"),
		common.HexToAddress("******************************************"): FromMap(map[string]string{
			"0x0000000000000000000000000000000000000000000000000000000000000aaa": "0x0000000000000000000000000000000000000000000000000000000000000bbb",
			"0x0000000000000000000000000000000000000000000000000000000000000ccc": "0x0000000000000000000000000000000000000000000000000000000000000ddd",
		}),
	}

	require.Equal(t, expected, accs)
}
