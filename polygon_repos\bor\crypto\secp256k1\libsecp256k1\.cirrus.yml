env:
  ### cirrus config
  CIRRUS_CLONE_DEPTH: 1
  ### compiler options
  HOST:
  WRAPPER_CMD:
  # Specific warnings can be disabled with -Wno-error=foo.
  # -pedantic-errors is not equivalent to -Werror=pedantic and thus not implied by -Werror according to the GCC manual.
  WERROR_CFLAGS: -Werror -pedantic-errors
  MAKEFLAGS: -j4
  BUILD: check
  ### secp256k1 config
  ECMULTWINDOW: 15
  ECMULTGENKB: 22
  ASM: no
  WIDEMUL: auto
  WITH_VALGRIND: yes
  EXTRAFLAGS:
  ### secp256k1 modules
  EXPERIMENTAL: no
  ECDH: no
  RECOVERY: no
  EXTRAKEYS: no
  SCHNORRSIG: no
  MUSIG: no
  ELLSWIFT: no
  ### test options
  SECP256K1_TEST_ITERS: 64
  BENCH: yes
  SECP256K1_BENCH_ITERS: 2
  CTIMETESTS: yes
  # Compile and run the tests
  EXAMPLES: yes

cat_logs_snippet: &CAT_LOGS
  always:
    cat_tests_log_script:
      - cat tests.log || true
    cat_noverify_tests_log_script:
      - cat noverify_tests.log || true
    cat_exhaustive_tests_log_script:
      - cat exhaustive_tests.log || true
    cat_ctime_tests_log_script:
      - cat ctime_tests.log || true
    cat_bench_log_script:
      - cat bench.log || true
    cat_config_log_script:
      - cat config.log || true
    cat_test_env_script:
      - cat test_env.log || true
    cat_ci_env_script:
      - env

linux_arm64_container_snippet: &LINUX_ARM64_CONTAINER
  env_script:
    - env | tee /tmp/env
  build_script:
    - DOCKER_BUILDKIT=1 docker build --file "ci/linux-debian.Dockerfile" --tag="ci_secp256k1_arm"
    - docker image prune --force  # Cleanup stale layers
  test_script:
    - docker run --rm --mount "type=bind,src=./,dst=/ci_secp256k1" --env-file /tmp/env --replace --name "ci_secp256k1_arm" "ci_secp256k1_arm" bash -c "cd /ci_secp256k1/ && ./ci/ci.sh"

task:
  name: "ARM64: Linux (Debian stable)"
  persistent_worker:
    labels:
      type: arm64
  env:
    ECDH: yes
    RECOVERY: yes
    EXTRAKEYS: yes
    SCHNORRSIG: yes
    MUSIG: yes
    ELLSWIFT: yes
  matrix:
     # Currently only gcc-snapshot, the other compilers are tested on GHA with QEMU
     - env: { CC: 'gcc-snapshot' }
  << : *LINUX_ARM64_CONTAINER
  << : *CAT_LOGS

task:
  name: "ARM64: Linux (Debian stable), Valgrind"
  persistent_worker:
    labels:
      type: arm64
  env:
    ECDH: yes
    RECOVERY: yes
    EXTRAKEYS: yes
    SCHNORRSIG: yes
    MUSIG: yes
    ELLSWIFT: yes
    WRAPPER_CMD: 'valgrind --error-exitcode=42'
    SECP256K1_TEST_ITERS: 2
  matrix:
     - env: { CC: 'gcc' }
     - env: { CC: 'clang' }
     - env: { CC: 'gcc-snapshot' }
     - env: { CC: 'clang-snapshot' }
  << : *LINUX_ARM64_CONTAINER
  << : *CAT_LOGS
