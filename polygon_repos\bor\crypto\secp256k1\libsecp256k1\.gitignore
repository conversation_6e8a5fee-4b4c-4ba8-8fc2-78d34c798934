bench
bench_ecmult
bench_internal
noverify_tests
tests
exhaustive_tests
precompute_ecmult_gen
precompute_ecmult
ctime_tests
ecdh_example
ecdsa_example
schnorr_example
ellswift_example
musig_example
*.exe
*.so
*.a
*.csv
*.log
*.trs
*.sage.py

Makefile
configure
.libs/
Makefile.in
aclocal.m4
autom4te.cache/
config.log
config.status
conftest*
*.tar.gz
*.la
libtool
.deps/
.dirstamp
*.lo
*.o
*~

coverage/
coverage.html
coverage.*.html
*.gcda
*.gcno
*.gcov

build-aux/ar-lib
build-aux/config.guess
build-aux/config.sub
build-aux/depcomp
build-aux/install-sh
build-aux/ltmain.sh
build-aux/m4/libtool.m4
build-aux/m4/lt~obsolete.m4
build-aux/m4/ltoptions.m4
build-aux/m4/ltsugar.m4
build-aux/m4/ltversion.m4
build-aux/missing
build-aux/compile
build-aux/test-driver
libsecp256k1.pc

### CMake
/CMakeUserPresets.json
# Default CMake build directory.
/build
