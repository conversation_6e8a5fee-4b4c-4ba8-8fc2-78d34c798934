# This configuration file is for reference and learning purpose only.
# The default value of the flags is provided below (except a few flags which has custom defaults which are explicitly mentioned).
# Recommended values for mainnet and/or amoy are also provided.

chain = "mainnet"               # Name of the chain to sync ("mainnet" or "amoy") or path to a genesis file
identity = "Annon-Identity"     # Name/Identity of the node (default = OS hostname)
verbosity = 3                   # Logging verbosity for the server (5=trace|4=debug|3=info|2=warn|1=error|0=crit) (`log-level` was replaced by `verbosity`, and thus will be deprecated soon)
vmdebug = false                 # Record information useful for VM and contract debugging
datadir = "var/lib/bor"         # Path of the data directory to store information
ancient = ""                    # Data directory for ancient chain segments (default = inside chaindata)
"db.engine" = "pebble"          # Used to select leveldb or pebble as database (default = pebble)
"state.scheme" = "path"         # Used to select the state scheme (default = path)
keystore = ""                   # Path of the directory where keystores are located
"rpc.batchlimit" = 100          # Maximum number of messages in a batch (default=100, use 0 for no limits)
"rpc.returndatalimit" = 100000  # Maximum size (in bytes) a result of an rpc request could have (default=100000, use 0 for no limits)
syncmode = "full"               # Blockchain sync mode ("full" or "snap")
gcmode = "full"                 # Blockchain garbage collection mode ("full", "archive")
snapshot = true                 # Enables the snapshot-database mode
"bor.logs" = false              # Enables bor log retrieval
ethstats = ""                   # Reporting URL of a ethstats service (nodename:secret@host:port)
devfakeauthor = false           # Run miner without validator set authorization [dev mode] : Use with '--bor.withoutheimdall' (default: false)

["eth.requiredblocks"]  # Comma separated block number-to-hash mappings to require for peering (<number>=<hash>) (default = empty map)
  "31000000" = "0x2087b9e2b353209c2c21e370c82daa12278efd0fe5f0febe6c29035352cf050e"
  "32000000" = "0x875500011e5eecc0c554f95d07b31cf59df4ca2505f4dbbfffa7d4e4da917c68"

[log]
  vmodule = ""                    # Per-module verbosity: comma-separated list of <pattern>=<level> (e.g. eth/*=5,p2p=4)
  json = false                    # Format logs with JSON
  backtrace = ""                  # Request a stack trace at a specific logging statement (e.g. "block.go:271")
  debug = true                    # Prepends log messages with call-site location (file and line number)
  enable-block-tracking = false   # Enables additional logging of information collected while tracking block lifecycle

[p2p]
  maxpeers = 50               # Maximum number of network peers (network disabled if set to 0)
  maxpendpeers = 50           # Maximum number of pending connection attempts
  bind = "0.0.0.0"            # Network binding address
  port = 30303                # Network listening port
  nodiscover = false          # Disables the peer discovery mechanism (manual peer addition)
  nat = "any"                 # NAT port mapping mechanism (any|none|upnp|pmp|extip:<IP>)
  netrestrict = ""            # Restricts network communication to the given IP networks (CIDR masks)
  nodekey = ""                # P2P node key file
  nodekeyhex = ""             # P2P node key as hex
  txarrivalwait = "500ms"     # Maximum duration to wait before requesting an announced transaction
  txannouncementonly = false  # Whether to only announce transactions to peers
  [p2p.discovery]
    v4disc = true       # Enables the V4 discovery mechanism
    v5disc = true       # Enables the V5 discovery mechanism
    bootnodes = []      # Comma separated enode URLs for P2P discovery bootstrap
    bootnodesv4 = []    # List of initial v4 bootnodes
    bootnodesv5 = []    # List of initial v5 bootnodes
    static-nodes = []   # List of static nodes
    trusted-nodes = []  # List of trusted nodes
    dns = []            # List of enrtree:// URLs which will be queried for nodes to connect to

[heimdall]
  url = "http://localhost:1317"  # URL of Heimdall service
  "bor.without" = false          # Run without Heimdall service (for testing purpose)
  grpc-address = ""              # Address of Heimdall gRPC service

[txpool]
  locals = []                   # Comma separated accounts to treat as locals (no flush, priority inclusion)
  nolocals = false              # Disables price exemptions for locally submitted transactions
  journal = "transactions.rlp"  # Disk journal for local transaction to survive node restarts
  rejournal = "1h0m0s"          # Time interval to regenerate the local transaction journal
  pricelimit = 2********00      # Minimum gas price limit to enforce for acceptance into the pool. Regardless the value set, it will be enforced to 2********00 for all networks
  pricebump = 10                # Price bump percentage to replace an already existing transaction
  accountslots = 16             # Minimum number of executable transaction slots guaranteed per account
  globalslots = 32768           # Maximum number of executable transaction slots for all accounts
  accountqueue = 16             # Maximum number of non-executable transaction slots permitted per account
  globalqueue = 32768           # Maximum number of non-executable transaction slots for all accounts
  lifetime = "3h0m0s"           # Maximum amount of time non-executable transaction are queued

[miner]
  mine = false             # Enable mining
  etherbase = ""           # Public address for block mining rewards
  extradata = ""           # Block extra data set by the miner (default = client version)
  gaslimit = ********      # Target gas ceiling for mined blocks
  gasprice = "2********00"  # Minimum gas price for mining a transaction. Regardless the value set, it will be enforced to 2********00 for all networks
  recommit = "2m5s"        # The time interval for miner to re-create mining work
  commitinterrupt = true   # Interrupt the current mining work when time is exceeded and create partial blocks

[jsonrpc]
  ipcdisable = false                               # Disable the IPC-RPC server
  ipcpath = ""                                     # Filename for IPC socket/pipe within the datadir (explicit paths escape it)
  gascap = ********                                # Sets a cap on gas that can be used in eth_call/estimateGas (0=infinite)
  evmtimeout = "5s"                                # Sets a timeout used for eth_call (0=infinite)
  txfeecap = 5.0                                   # Sets a cap on transaction fee (in ether) that can be sent via the RPC APIs (0 = no cap)
  allow-unprotected-txs = false                    # Allow for unprotected (non EIP155 signed) transactions to be submitted via RPC (default: false)
  enabledeprecatedpersonal = false                 # Enables the (deprecated) personal namespace
  [jsonrpc.http]
    enabled = false                                # Enable the HTTP-RPC server
    port = 8545                                    # http.port
    prefix = ""                                    # http.rpcprefix
    host = "localhost"                             # HTTP-RPC server listening interface
    api = ["eth", "net", "web3", "txpool", "bor"]  # API's offered over the HTTP-RPC interface
    vhosts = ["localhost"]                         # Comma separated list of virtual hostnames from which to accept requests (server enforced). Accepts '*' wildcard.
    corsdomain = ["localhost"]                     # Comma separated list of domains from which to accept cross origin requests (browser enforced)
    ep-size = 40                                   # Maximum size of workers to run in rpc execution pool for HTTP requests (default: 40)
    ep-requesttimeout = "0s"                       # Request Timeout for rpc execution pool for HTTP requests (default: 0s, 0s = disabled)
  [jsonrpc.ws]
    enabled = false          # Enable the WS-RPC server
    port = 8546              # WS-RPC server listening port
    prefix = ""              # HTTP path prefix on which JSON-RPC is served. Use '/' to serve on all paths.
    host = "localhost"       # ws.addr
    api = ["net", "web3"]    # API's offered over the WS-RPC interface
    origins = ["localhost"]  # Origins from which to accept websockets requests
    ep-size = 40             # Maximum size of workers to run in rpc execution pool for WS requests (default: 40)
    ep-requesttimeout = "0s" # Request Timeout for rpc execution pool for WS requests (default: 0s, 0s = disabled)
  [jsonrpc.graphql]
    enabled = false             # Enable GraphQL on the HTTP-RPC server. Note that GraphQL can only be started if an HTTP server is started as well.
    port = 0                    #
    prefix = ""                 #
    host = ""                   #
    vhosts = ["localhost"]      # Comma separated list of virtual hostnames from which to accept requests (server enforced). Accepts '*' wildcard.
    corsdomain = ["localhost"]  # Comma separated list of domains from which to accept cross origin requests (browser enforced)
  [jsonrpc.auth]
    jwtsecret = ""              # Path to a JWT secret to use for authenticated RPC endpoints
    addr = "localhost"          # Listening address for authenticated APIs
    port = 8551                 # Listening port for authenticated APIs
    vhosts = ["localhost"]      # Comma separated list of virtual hostnames from which to accept requests (server enforced). Accepts '*' wildcard.
  [jsonrpc.timeouts]
    read = "10s"
    write = "30s"
    idle = "2m0s"

[gpo]
  blocks = 20                 # Number of recent blocks to check for gas prices
  percentile = 60             # Suggested gas price is the given percentile of a set of recent transaction gas prices
  maxheaderhistory = 1024     # Maximum header history of gasprice oracle
  maxblockhistory = 1024      # Maximum block history of gasprice oracle
  maxprice = "********00000"  # Maximum gas price will be recommended by gpo
  ignoreprice = "2********00"           # Gas price below which gpo will ignore transactions. Regardless the value set, it will be enforced to 2********00 for all networks

[telemetry]
  metrics = false                            # Enable metrics collection and reporting
  expensive = false                          # Enable expensive metrics collection and reporting
  prometheus-addr = "127.0.0.1:7071"         # Address for Prometheus Server
  opencollector-endpoint = ""                # OpenCollector Endpoint (host:port)
  [telemetry.influx]
    influxdb = false    # Enable metrics export/push to an external InfluxDB database (v1)
    endpoint = ""       # InfluxDB API endpoint to report metrics to
    database = ""       # InfluxDB database name to push reported metrics to
    username = ""       # Username to authorize access to the database
    password = ""       # Password to authorize access to the database
    influxdbv2 = false  # Enable metrics export/push to an external InfluxDB v2 database
    token = ""          # Token to authorize access to the database (v2 only)
    bucket = ""         # InfluxDB bucket name to push reported metrics to (v2 only)
    organization = ""   # InfluxDB organization name (v2 only)
    [telemetry.influx.tags]  # Comma-separated InfluxDB tags (key/values) attached to all measurements
      cloud = "aws"
      host = "annon-host"
      ip = "99.911.221.66"
      region = "us-north-1"

[cache]
  cache = 1024             # Megabytes of memory allocated to internal caching (recommended for mainnet = 4096, default suitable for amoy/devnet)
  gc = 25                  # Percentage of cache memory allowance to use for trie pruning (default = 25% full mode, 0% archive mode)
  snapshot = 10            # Percentage of cache memory allowance to use for snapshot caching (default = 10% full mode, 20% archive mode)
  database = 50            # Percentage of cache memory allowance to use for database io
  trie = 15                # Percentage of cache memory allowance to use for trie caching (default = 15% full mode, 30% archive mode)
  noprefetch = false       # Disable heuristic state prefetch during block import (less CPU and disk IO, more time waiting for data)
  preimages = false        # Enable recording the SHA3/keccak preimages of trie keys
  txlookuplimit = 2350000  # Number of recent blocks to maintain transactions index for (default = about 56 days, 0 = entire chain)
  triesinmemory = 128      # Number of block states (tries) to keep in memory
  blocklogs = 32           # Size (in number of blocks) of the log cache for filtering
  timeout = "1h0m0s"       # Time after which the Merkle Patricia Trie is stored to disc from memory
  fdlimit = 0              # Raise the open file descriptor resource limit (default = system fd limit)

[accounts]
  unlock = []                    # Comma separated list of accounts to unlock
  password = ""                  # Password file to use for non-interactive password input
  allow-insecure-unlock = false  # Allow insecure account unlocking when account-related RPCs are exposed by http
  lightkdf = false               # Reduce key-derivation RAM & CPU usage at some expense of KDF strength
  disable-bor-wallet = true      # Disable the personal wallet endpoints

[grpc]
  addr = ":3131" # Address and port to bind the GRPC server

[developer]
  dev = false          # Enable developer mode with ephemeral proof-of-authority network and a pre-funded developer account, mining enabled
  period = 0           # Block period to use in developer mode (0 = mine only if transaction pending)
  gaslimit = ********  # Initial block gas limit

[parallelevm]
  enable = true     # Enables parallel execution using Block STM
  procs = 8         # Number of speculative processes (cores) in Block STM
  enforce = false   # Use only Block STM for execution and skip serial execution

[pprof]
  pprof = false            # Enable the pprof HTTP server
  port = 6060              # pprof HTTP server listening port
  addr = "127.0.0.1"       # pprof HTTP server listening interface
  memprofilerate = 524288  # Turn on memory profiling with the given rate
  blockprofilerate = 0     # Turn on block profiling with the given rate
