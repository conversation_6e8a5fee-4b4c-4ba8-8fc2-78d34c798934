# Peers

The ```peers``` command groups actions to interact with peers:

- [```peers add```](./peers_add.md): Joins the local client to another remote peer.

- [```peers list```](./peers_list.md): Lists the connected peers to the Bor client.

- [```peers remove```](./peers_remove.md): Disconnects the local client from a connected peer if exists.

- [```peers status```](./peers_status.md): Display the status of a peer by its id.