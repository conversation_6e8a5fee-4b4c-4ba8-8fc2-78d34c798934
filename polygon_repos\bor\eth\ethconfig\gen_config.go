// Code generated by github.com/fjl/gencodec. DO NOT EDIT.

package ethconfig

import (
	"encoding/json"
	"math/big"
	"time"

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core"
	"github.com/ethereum/go-ethereum/core/history"
	"github.com/ethereum/go-ethereum/core/txpool/blobpool"
	"github.com/ethereum/go-ethereum/core/txpool/legacypool"
	"github.com/ethereum/go-ethereum/eth/downloader"
	"github.com/ethereum/go-ethereum/eth/gasprice"
	"github.com/ethereum/go-ethereum/miner"
)

// MarshalJSON marshals as JSON.
func (c Config) MarshalJSON() ([]byte, error) {
	type Config struct {
		Genesis                              *core.Genesis `toml:",omitempty"`
		NetworkId                            uint64
		SyncMode                             downloader.SyncMode
		HistoryMode                          history.HistoryMode
		EthDiscoveryURLs                     []string
		SnapDiscoveryURLs                    []string
		NoPruning                            bool
		NoPrefetch                           bool
		TxLookupLimit                        uint64 `toml:",omitempty"`
		TransactionHistory                   uint64 `toml:",omitempty"`
		LogHistory                           uint64 `toml:",omitempty"`
		LogNoHistory                         bool   `toml:",omitempty"`
		LogExportCheckpoints                 string
		StateHistory                         uint64                 `toml:",omitempty"`
		StateScheme                          string                 `toml:",omitempty"`
		RequiredBlocks                       map[uint64]common.Hash `toml:"-"`
		SkipBcVersionCheck                   bool                   `toml:"-"`
		DatabaseHandles                      int                    `toml:"-"`
		DatabaseCache                        int
		DatabaseFreezer                      string
		LevelDbCompactionTableSize           uint64
		LevelDbCompactionTableSizeMultiplier float64
		LevelDbCompactionTotalSize           uint64
		LevelDbCompactionTotalSizeMultiplier float64
		TrieCleanCache                       int
		TrieDirtyCache                       int
		TrieTimeout                          time.Duration
		SnapshotCache                        int
		Preimages                            bool
		TriesInMemory                        uint64
		FilterLogCacheSize                   int
		Miner                                miner.Config
		TxPool                               legacypool.Config
		BlobPool                             blobpool.Config
		GPO                                  gasprice.Config
		EnablePreimageRecording              bool
		VMTrace                              string
		VMTraceJsonConfig                    string
		RPCGasCap                            uint64
		RPCReturnDataLimit                   uint64
		RPCEVMTimeout                        time.Duration
		RPCTxFeeCap                          float64
		OverridePrague                       *big.Int `toml:",omitempty"`
		HeimdallURL                          string
		HeimdallTimeout                      time.Duration
		WithoutHeimdall                      bool
		HeimdallgRPCAddress                  string
		HeimdallWSAddress                    string
		RunHeimdall                          bool
		RunHeimdallArgs                      string
		UseHeimdallApp                       bool
		BorLogs                              bool
		ParallelEVM                          core.ParallelEVMConfig `toml:",omitempty"`
		DevFakeAuthor                        bool                   `hcl:"devfakeauthor,optional" toml:"devfakeauthor,optional"`
		OverrideVerkle                       *big.Int               `toml:",omitempty"`
		EnableBlockTracking                  bool
	}
	var enc Config
	enc.Genesis = c.Genesis
	enc.NetworkId = c.NetworkId
	enc.SyncMode = c.SyncMode
	enc.HistoryMode = c.HistoryMode
	enc.EthDiscoveryURLs = c.EthDiscoveryURLs
	enc.SnapDiscoveryURLs = c.SnapDiscoveryURLs
	enc.NoPruning = c.NoPruning
	enc.NoPrefetch = c.NoPrefetch
	enc.TxLookupLimit = c.TxLookupLimit
	enc.TransactionHistory = c.TransactionHistory
	enc.LogHistory = c.LogHistory
	enc.LogNoHistory = c.LogNoHistory
	enc.LogExportCheckpoints = c.LogExportCheckpoints
	enc.StateHistory = c.StateHistory
	enc.StateScheme = c.StateScheme
	enc.RequiredBlocks = c.RequiredBlocks
	enc.SkipBcVersionCheck = c.SkipBcVersionCheck
	enc.DatabaseHandles = c.DatabaseHandles
	enc.DatabaseCache = c.DatabaseCache
	enc.DatabaseFreezer = c.DatabaseFreezer
	enc.LevelDbCompactionTableSize = c.LevelDbCompactionTableSize
	enc.LevelDbCompactionTableSizeMultiplier = c.LevelDbCompactionTableSizeMultiplier
	enc.LevelDbCompactionTotalSize = c.LevelDbCompactionTotalSize
	enc.LevelDbCompactionTotalSizeMultiplier = c.LevelDbCompactionTotalSizeMultiplier
	enc.TrieCleanCache = c.TrieCleanCache
	enc.TrieDirtyCache = c.TrieDirtyCache
	enc.TrieTimeout = c.TrieTimeout
	enc.SnapshotCache = c.SnapshotCache
	enc.Preimages = c.Preimages
	enc.TriesInMemory = c.TriesInMemory
	enc.FilterLogCacheSize = c.FilterLogCacheSize
	enc.Miner = c.Miner
	enc.TxPool = c.TxPool
	enc.BlobPool = c.BlobPool
	enc.GPO = c.GPO
	enc.EnablePreimageRecording = c.EnablePreimageRecording
	enc.VMTrace = c.VMTrace
	enc.VMTraceJsonConfig = c.VMTraceJsonConfig
	enc.RPCGasCap = c.RPCGasCap
	enc.RPCReturnDataLimit = c.RPCReturnDataLimit
	enc.RPCEVMTimeout = c.RPCEVMTimeout
	enc.RPCTxFeeCap = c.RPCTxFeeCap
	enc.OverridePrague = c.OverridePrague
	enc.HeimdallURL = c.HeimdallURL
	enc.HeimdallTimeout = c.HeimdallTimeout
	enc.WithoutHeimdall = c.WithoutHeimdall
	enc.HeimdallgRPCAddress = c.HeimdallgRPCAddress
	enc.HeimdallWSAddress = c.HeimdallWSAddress
	enc.RunHeimdall = c.RunHeimdall
	enc.RunHeimdallArgs = c.RunHeimdallArgs
	enc.UseHeimdallApp = c.UseHeimdallApp
	enc.BorLogs = c.BorLogs
	enc.ParallelEVM = c.ParallelEVM
	enc.DevFakeAuthor = c.DevFakeAuthor
	enc.OverrideVerkle = c.OverrideVerkle
	enc.EnableBlockTracking = c.EnableBlockTracking
	return json.Marshal(&enc)
}

// UnmarshalJSON unmarshals from JSON.
func (c *Config) UnmarshalJSON(input []byte) error {
	type Config struct {
		Genesis                              *core.Genesis `toml:",omitempty"`
		NetworkId                            *uint64
		SyncMode                             *downloader.SyncMode
		HistoryMode                          *history.HistoryMode
		EthDiscoveryURLs                     []string
		SnapDiscoveryURLs                    []string
		NoPruning                            *bool
		NoPrefetch                           *bool
		TxLookupLimit                        *uint64 `toml:",omitempty"`
		TransactionHistory                   *uint64 `toml:",omitempty"`
		LogHistory                           *uint64 `toml:",omitempty"`
		LogNoHistory                         *bool   `toml:",omitempty"`
		LogExportCheckpoints                 *string
		StateHistory                         *uint64                `toml:",omitempty"`
		StateScheme                          *string                `toml:",omitempty"`
		RequiredBlocks                       map[uint64]common.Hash `toml:"-"`
		SkipBcVersionCheck                   *bool                  `toml:"-"`
		DatabaseHandles                      *int                   `toml:"-"`
		DatabaseCache                        *int
		DatabaseFreezer                      *string
		LevelDbCompactionTableSize           *uint64
		LevelDbCompactionTableSizeMultiplier *float64
		LevelDbCompactionTotalSize           *uint64
		LevelDbCompactionTotalSizeMultiplier *float64
		TrieCleanCache                       *int
		TrieDirtyCache                       *int
		TrieTimeout                          *time.Duration
		SnapshotCache                        *int
		Preimages                            *bool
		TriesInMemory                        *uint64
		FilterLogCacheSize                   *int
		Miner                                *miner.Config
		TxPool                               *legacypool.Config
		BlobPool                             *blobpool.Config
		GPO                                  *gasprice.Config
		EnablePreimageRecording              *bool
		VMTrace                              *string
		VMTraceJsonConfig                    *string
		RPCGasCap                            *uint64
		RPCReturnDataLimit                   *uint64
		RPCEVMTimeout                        *time.Duration
		RPCTxFeeCap                          *float64
		OverridePrague                       *big.Int `toml:",omitempty"`
		HeimdallURL                          *string
		HeimdallTimeout                      *time.Duration
		WithoutHeimdall                      *bool
		HeimdallgRPCAddress                  *string
		HeimdallWSAddress                    *string
		RunHeimdall                          *bool
		RunHeimdallArgs                      *string
		UseHeimdallApp                       *bool
		BorLogs                              *bool
		ParallelEVM                          *core.ParallelEVMConfig `toml:",omitempty"`
		DevFakeAuthor                        *bool                   `hcl:"devfakeauthor,optional" toml:"devfakeauthor,optional"`
		OverrideVerkle                       *big.Int                `toml:",omitempty"`
		EnableBlockTracking                  *bool
	}
	var dec Config
	if err := json.Unmarshal(input, &dec); err != nil {
		return err
	}
	if dec.Genesis != nil {
		c.Genesis = dec.Genesis
	}
	if dec.NetworkId != nil {
		c.NetworkId = *dec.NetworkId
	}
	if dec.SyncMode != nil {
		c.SyncMode = *dec.SyncMode
	}
	if dec.HistoryMode != nil {
		c.HistoryMode = *dec.HistoryMode
	}
	if dec.EthDiscoveryURLs != nil {
		c.EthDiscoveryURLs = dec.EthDiscoveryURLs
	}
	if dec.SnapDiscoveryURLs != nil {
		c.SnapDiscoveryURLs = dec.SnapDiscoveryURLs
	}
	if dec.NoPruning != nil {
		c.NoPruning = *dec.NoPruning
	}
	if dec.NoPrefetch != nil {
		c.NoPrefetch = *dec.NoPrefetch
	}
	if dec.TxLookupLimit != nil {
		c.TxLookupLimit = *dec.TxLookupLimit
	}
	if dec.TransactionHistory != nil {
		c.TransactionHistory = *dec.TransactionHistory
	}
	if dec.LogHistory != nil {
		c.LogHistory = *dec.LogHistory
	}
	if dec.LogNoHistory != nil {
		c.LogNoHistory = *dec.LogNoHistory
	}
	if dec.LogExportCheckpoints != nil {
		c.LogExportCheckpoints = *dec.LogExportCheckpoints
	}
	if dec.StateHistory != nil {
		c.StateHistory = *dec.StateHistory
	}
	if dec.StateScheme != nil {
		c.StateScheme = *dec.StateScheme
	}
	if dec.RequiredBlocks != nil {
		c.RequiredBlocks = dec.RequiredBlocks
	}
	if dec.SkipBcVersionCheck != nil {
		c.SkipBcVersionCheck = *dec.SkipBcVersionCheck
	}
	if dec.DatabaseHandles != nil {
		c.DatabaseHandles = *dec.DatabaseHandles
	}
	if dec.DatabaseCache != nil {
		c.DatabaseCache = *dec.DatabaseCache
	}
	if dec.DatabaseFreezer != nil {
		c.DatabaseFreezer = *dec.DatabaseFreezer
	}
	if dec.LevelDbCompactionTableSize != nil {
		c.LevelDbCompactionTableSize = *dec.LevelDbCompactionTableSize
	}
	if dec.LevelDbCompactionTableSizeMultiplier != nil {
		c.LevelDbCompactionTableSizeMultiplier = *dec.LevelDbCompactionTableSizeMultiplier
	}
	if dec.LevelDbCompactionTotalSize != nil {
		c.LevelDbCompactionTotalSize = *dec.LevelDbCompactionTotalSize
	}
	if dec.LevelDbCompactionTotalSizeMultiplier != nil {
		c.LevelDbCompactionTotalSizeMultiplier = *dec.LevelDbCompactionTotalSizeMultiplier
	}
	if dec.TrieCleanCache != nil {
		c.TrieCleanCache = *dec.TrieCleanCache
	}
	if dec.TrieDirtyCache != nil {
		c.TrieDirtyCache = *dec.TrieDirtyCache
	}
	if dec.TrieTimeout != nil {
		c.TrieTimeout = *dec.TrieTimeout
	}
	if dec.SnapshotCache != nil {
		c.SnapshotCache = *dec.SnapshotCache
	}
	if dec.Preimages != nil {
		c.Preimages = *dec.Preimages
	}
	if dec.TriesInMemory != nil {
		c.TriesInMemory = *dec.TriesInMemory
	}
	if dec.FilterLogCacheSize != nil {
		c.FilterLogCacheSize = *dec.FilterLogCacheSize
	}
	if dec.Miner != nil {
		c.Miner = *dec.Miner
	}
	if dec.TxPool != nil {
		c.TxPool = *dec.TxPool
	}
	if dec.BlobPool != nil {
		c.BlobPool = *dec.BlobPool
	}
	if dec.GPO != nil {
		c.GPO = *dec.GPO
	}
	if dec.EnablePreimageRecording != nil {
		c.EnablePreimageRecording = *dec.EnablePreimageRecording
	}
	if dec.VMTrace != nil {
		c.VMTrace = *dec.VMTrace
	}
	if dec.VMTraceJsonConfig != nil {
		c.VMTraceJsonConfig = *dec.VMTraceJsonConfig
	}
	if dec.RPCGasCap != nil {
		c.RPCGasCap = *dec.RPCGasCap
	}
	if dec.RPCReturnDataLimit != nil {
		c.RPCReturnDataLimit = *dec.RPCReturnDataLimit
	}
	if dec.RPCEVMTimeout != nil {
		c.RPCEVMTimeout = *dec.RPCEVMTimeout
	}
	if dec.RPCTxFeeCap != nil {
		c.RPCTxFeeCap = *dec.RPCTxFeeCap
	}
	if dec.OverridePrague != nil {
		c.OverridePrague = dec.OverridePrague
	}
	if dec.HeimdallURL != nil {
		c.HeimdallURL = *dec.HeimdallURL
	}
	if dec.HeimdallTimeout != nil {
		c.HeimdallTimeout = *dec.HeimdallTimeout
	}
	if dec.WithoutHeimdall != nil {
		c.WithoutHeimdall = *dec.WithoutHeimdall
	}
	if dec.HeimdallgRPCAddress != nil {
		c.HeimdallgRPCAddress = *dec.HeimdallgRPCAddress
	}
	if dec.HeimdallWSAddress != nil {
		c.HeimdallWSAddress = *dec.HeimdallWSAddress
	}
	if dec.RunHeimdall != nil {
		c.RunHeimdall = *dec.RunHeimdall
	}
	if dec.RunHeimdallArgs != nil {
		c.RunHeimdallArgs = *dec.RunHeimdallArgs
	}
	if dec.UseHeimdallApp != nil {
		c.UseHeimdallApp = *dec.UseHeimdallApp
	}
	if dec.BorLogs != nil {
		c.BorLogs = *dec.BorLogs
	}
	if dec.ParallelEVM != nil {
		c.ParallelEVM = *dec.ParallelEVM
	}
	if dec.DevFakeAuthor != nil {
		c.DevFakeAuthor = *dec.DevFakeAuthor
	}
	if dec.OverrideVerkle != nil {
		c.OverrideVerkle = dec.OverrideVerkle
	}
	if dec.EnableBlockTracking != nil {
		c.EnableBlockTracking = *dec.EnableBlockTracking
	}
	return nil
}
