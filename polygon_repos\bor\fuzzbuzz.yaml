# bmt  keystore  rlp  trie  whisperv6

base: ubuntu:16.04
targets:
  - name: rlp
    language: go
    version: "1.13"
    corpus: ./fuzzers/rlp/corpus
    harness:
      function: Fuzz
      package: github.com/ethereum/go-ethereum/tests/fuzzers/rlp
      checkout: github.com/ethereum/go-ethereum/
  - name: keystore
    language: go
    version: "1.13"
    corpus: ./fuzzers/keystore/corpus
    harness:
      function: Fuzz
      package: github.com/ethereum/go-ethereum/tests/fuzzers/keystore
      checkout: github.com/ethereum/go-ethereum/
  - name: trie
    language: go
    version: "1.13"
    corpus: ./fuzzers/trie/corpus
    harness:
      function: Fuzz
      package: github.com/ethereum/go-ethereum/tests/fuzzers/trie
      checkout: github.com/ethereum/go-ethereum/
  - name: txfetcher
    language: go
    version: "1.13"
    corpus: ./fuzzers/txfetcher/corpus
    harness:
      function: Fuzz
      package: github.com/ethereum/go-ethereum/tests/fuzzers/txfetcher
      checkout: github.com/ethereum/go-ethereum/
  - name: whisperv6
    language: go
    version: "1.13"
    corpus: ./fuzzers/whisperv6/corpus
    harness:
      function: Fuzz
      package: github.com/ethereum/go-ethereum/tests/fuzzers/whisperv6
      checkout: github.com/ethereum/go-ethereum/
