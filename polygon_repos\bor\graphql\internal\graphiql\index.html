<!--
 *  Copyright (c) 2021 GraphQL Contributors
 *  All rights reserved.
 *
 *  This source code is licensed under the license found in the
 *  LICENSE file in the root directory of this source tree.
-->
<!DOCTYPE html>
<html lang="en">
<head>
    <title>GraphiQL</title>
    <style>
        body {
            height: 100%;
            margin: 0;
            width: 100%;
            overflow: hidden;
        }

        #graphiql {
            height: 100vh;
        }
    </style>

    <script src="/graphql/ui/react.production.min.js"></script>
    <script src="/graphql/ui/react-dom.production.min.js"></script>

    <link rel="stylesheet" href="/graphql/ui/graphiql.min.css" />
</head>

<body>
<div id="graphiql">Loading...</div>
<script src="/graphql/ui/graphiql.min.js" type="application/javascript"></script>
<script>
    ReactDOM.render(
        React.createElement(GraphiQL, {
            fetcher: GraphiQL.createFetcher({
                url: '/graphql',
            }),
            defaultEditorToolsVisibility: true,
        }),
        document.getElementById('graphiql'),
    );
</script>
</body>
</html>