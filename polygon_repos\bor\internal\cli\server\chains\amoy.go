package chains

import (
	"math/big"

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core"
	"github.com/ethereum/go-ethereum/params"
)

var amoyTestnet = &Chain{
	NetworkId: 80002,
	Genesis: &core.Genesis{
		Config: &params.ChainConfig{
			ChainID:             big.NewInt(80002),
			HomesteadBlock:      big.NewInt(0),
			DAOForkBlock:        nil,
			DAOForkSupport:      true,
			EIP150Block:         big.NewInt(0),
			EIP155Block:         big.NewInt(0),
			EIP158Block:         big.NewInt(0),
			ByzantiumBlock:      big.NewInt(0),
			ConstantinopleBlock: big.NewInt(0),
			PetersburgBlock:     big.NewInt(0),
			IstanbulBlock:       big.NewInt(0),
			MuirGlacierBlock:    big.NewInt(0),
			BerlinBlock:         big.NewInt(0),
			LondonBlock:         big.NewInt(73100),
			ShanghaiBlock:       big.NewInt(73100),
			CancunBlock:         big.NewInt(5423600),
			PragueBlock:         big.NewInt(22765056),
			Bor: &params.BorConfig{
				JaipurBlock:    big.NewInt(73100),
				DelhiBlock:     big.NewInt(73100),
				IndoreBlock:    big.NewInt(73100),
				AhmedabadBlock: big.NewInt(11865856),
				BhilaiBlock:    big.NewInt(22765056),
				StateSyncConfirmationDelay: map[string]uint64{
					"0": 128,
				},
				Period: map[string]uint64{
					"0": 2,
				},
				ProducerDelay: map[string]uint64{
					"0": 4,
				},
				Sprint: map[string]uint64{
					"0": 16,
				},
				BackupMultiplier: map[string]uint64{
					"0": 2,
				},
				ValidatorContract:     "0x0000000000000000000000000000000000001000",
				StateReceiverContract: "0x0000000000000000000000000000000000001001",
				OverrideStateSyncRecordsInRange: []params.BlockRangeOverride{
					{StartBlock: 23184305, EndBlock: 23220127, Value: 0},
				},
				BurntContract: map[string]string{
					"0":     "0x000000000000000000000000000000000000dead",
					"73100": "0xeCDD77cE6f146cCf5dab707941d318Bd50eeD2C9",
				},
				BlockAlloc: map[string]interface{}{
					"11865856": map[string]interface{}{
						// StateReceiver contract
						"0000000000000000000000000000000000001001": map[string]interface{}{
							"balance": "0x0",
							"code":    "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",
						},
						// MRC20 contract
						"0000000000000000000000000000000000001010": map[string]interface{}{
							"balance": "0x0",
							"code":    "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",
						},
						// WMATIC contract
						"360ad4f9a9A8EFe9A8DCB5f461c4Cc1047E1Dcf9": map[string]interface{}{
							"balance": "0x0",
							"code":    "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",
						},
					},
					"12121856": map[string]interface{}{
						"360ad4f9a9A8EFe9A8DCB5f461c4Cc1047E1Dcf9": map[string]interface{}{
							"balance": "0x0",
							"code":    "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",
						},
					},
				},
			},
		},
		Nonce:      0,
		Timestamp:  1700225065,
		GasLimit:   10000000,
		Difficulty: big.NewInt(1),
		Mixhash:    common.HexToHash("0x0000000000000000000000000000000000000000000000000000000000000000"),
		Coinbase:   common.HexToAddress("0x0000000000000000000000000000000000000000"),
		Alloc:      readPrealloc("allocs/amoy.json"),
	},
	Bootnodes: params.AmoyBootnodes,
}
