// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.21.12
// source: internal/cli/server/proto/server.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DebugPprofRequest_Type int32

const (
	DebugPprofRequest_LOOKUP DebugPprofRequest_Type = 0
	DebugPprofRequest_CPU    DebugPprofRequest_Type = 1
	DebugPprofRequest_TRACE  DebugPprofRequest_Type = 2
)

// Enum value maps for DebugPprofRequest_Type.
var (
	DebugPprofRequest_Type_name = map[int32]string{
		0: "LOOKUP",
		1: "CPU",
		2: "TRACE",
	}
	DebugPprofRequest_Type_value = map[string]int32{
		"LOOKUP": 0,
		"CPU":    1,
		"TRACE":  2,
	}
)

func (x DebugPprofRequest_Type) Enum() *DebugPprofRequest_Type {
	p := new(DebugPprofRequest_Type)
	*p = x

	return p
}

func (x DebugPprofRequest_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DebugPprofRequest_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_internal_cli_server_proto_server_proto_enumTypes[0].Descriptor()
}

func (DebugPprofRequest_Type) Type() protoreflect.EnumType {
	return &file_internal_cli_server_proto_server_proto_enumTypes[0]
}

func (x DebugPprofRequest_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DebugPprofRequest_Type.Descriptor instead.
func (DebugPprofRequest_Type) EnumDescriptor() ([]byte, []int) {
	return file_internal_cli_server_proto_server_proto_rawDescGZIP(), []int{19, 0}
}

type TraceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Number int64 `protobuf:"varint,1,opt,name=number,proto3" json:"number,omitempty"`
}

func (x *TraceRequest) Reset() {
	*x = TraceRequest{}

	if protoimpl.UnsafeEnabled {
		mi := &file_internal_cli_server_proto_server_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TraceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TraceRequest) ProtoMessage() {}

func (x *TraceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_internal_cli_server_proto_server_proto_msgTypes[0]

	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}

		return ms
	}

	return mi.MessageOf(x)
}

// Deprecated: Use TraceRequest.ProtoReflect.Descriptor instead.
func (*TraceRequest) Descriptor() ([]byte, []int) {
	return file_internal_cli_server_proto_server_proto_rawDescGZIP(), []int{0}
}

func (x *TraceRequest) GetNumber() int64 {
	if x != nil {
		return x.Number
	}

	return 0
}

type TraceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *TraceResponse) Reset() {
	*x = TraceResponse{}

	if protoimpl.UnsafeEnabled {
		mi := &file_internal_cli_server_proto_server_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TraceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TraceResponse) ProtoMessage() {}

func (x *TraceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_internal_cli_server_proto_server_proto_msgTypes[1]

	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}

		return ms
	}

	return mi.MessageOf(x)
}

// Deprecated: Use TraceResponse.ProtoReflect.Descriptor instead.
func (*TraceResponse) Descriptor() ([]byte, []int) {
	return file_internal_cli_server_proto_server_proto_rawDescGZIP(), []int{1}
}

type ChainWatchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ChainWatchRequest) Reset() {
	*x = ChainWatchRequest{}

	if protoimpl.UnsafeEnabled {
		mi := &file_internal_cli_server_proto_server_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChainWatchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChainWatchRequest) ProtoMessage() {}

func (x *ChainWatchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_internal_cli_server_proto_server_proto_msgTypes[2]

	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}

		return ms
	}

	return mi.MessageOf(x)
}

// Deprecated: Use ChainWatchRequest.ProtoReflect.Descriptor instead.
func (*ChainWatchRequest) Descriptor() ([]byte, []int) {
	return file_internal_cli_server_proto_server_proto_rawDescGZIP(), []int{2}
}

type ChainWatchResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Oldchain []*BlockStub `protobuf:"bytes,1,rep,name=oldchain,proto3" json:"oldchain,omitempty"`
	Newchain []*BlockStub `protobuf:"bytes,2,rep,name=newchain,proto3" json:"newchain,omitempty"`
	Type     string       `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
}

func (x *ChainWatchResponse) Reset() {
	*x = ChainWatchResponse{}

	if protoimpl.UnsafeEnabled {
		mi := &file_internal_cli_server_proto_server_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChainWatchResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChainWatchResponse) ProtoMessage() {}

func (x *ChainWatchResponse) ProtoReflect() protoreflect.Message {
	mi := &file_internal_cli_server_proto_server_proto_msgTypes[3]

	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}

		return ms
	}

	return mi.MessageOf(x)
}

// Deprecated: Use ChainWatchResponse.ProtoReflect.Descriptor instead.
func (*ChainWatchResponse) Descriptor() ([]byte, []int) {
	return file_internal_cli_server_proto_server_proto_rawDescGZIP(), []int{3}
}

func (x *ChainWatchResponse) GetOldchain() []*BlockStub {
	if x != nil {
		return x.Oldchain
	}

	return nil
}

func (x *ChainWatchResponse) GetNewchain() []*BlockStub {
	if x != nil {
		return x.Newchain
	}

	return nil
}

func (x *ChainWatchResponse) GetType() string {
	if x != nil {
		return x.Type
	}

	return ""
}

type BlockStub struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Hash   string `protobuf:"bytes,1,opt,name=hash,proto3" json:"hash,omitempty"`
	Number uint64 `protobuf:"varint,2,opt,name=number,proto3" json:"number,omitempty"`
}

func (x *BlockStub) Reset() {
	*x = BlockStub{}

	if protoimpl.UnsafeEnabled {
		mi := &file_internal_cli_server_proto_server_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BlockStub) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockStub) ProtoMessage() {}

func (x *BlockStub) ProtoReflect() protoreflect.Message {
	mi := &file_internal_cli_server_proto_server_proto_msgTypes[4]

	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}

		return ms
	}

	return mi.MessageOf(x)
}

// Deprecated: Use BlockStub.ProtoReflect.Descriptor instead.
func (*BlockStub) Descriptor() ([]byte, []int) {
	return file_internal_cli_server_proto_server_proto_rawDescGZIP(), []int{4}
}

func (x *BlockStub) GetHash() string {
	if x != nil {
		return x.Hash
	}

	return ""
}

func (x *BlockStub) GetNumber() uint64 {
	if x != nil {
		return x.Number
	}

	return 0
}

type PeersAddRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enode   string `protobuf:"bytes,1,opt,name=enode,proto3" json:"enode,omitempty"`
	Trusted bool   `protobuf:"varint,2,opt,name=trusted,proto3" json:"trusted,omitempty"`
}

func (x *PeersAddRequest) Reset() {
	*x = PeersAddRequest{}

	if protoimpl.UnsafeEnabled {
		mi := &file_internal_cli_server_proto_server_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PeersAddRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PeersAddRequest) ProtoMessage() {}

func (x *PeersAddRequest) ProtoReflect() protoreflect.Message {
	mi := &file_internal_cli_server_proto_server_proto_msgTypes[5]

	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}

		return ms
	}

	return mi.MessageOf(x)
}

// Deprecated: Use PeersAddRequest.ProtoReflect.Descriptor instead.
func (*PeersAddRequest) Descriptor() ([]byte, []int) {
	return file_internal_cli_server_proto_server_proto_rawDescGZIP(), []int{5}
}

func (x *PeersAddRequest) GetEnode() string {
	if x != nil {
		return x.Enode
	}

	return ""
}

func (x *PeersAddRequest) GetTrusted() bool {
	if x != nil {
		return x.Trusted
	}

	return false
}

type PeersAddResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PeersAddResponse) Reset() {
	*x = PeersAddResponse{}

	if protoimpl.UnsafeEnabled {
		mi := &file_internal_cli_server_proto_server_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PeersAddResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PeersAddResponse) ProtoMessage() {}

func (x *PeersAddResponse) ProtoReflect() protoreflect.Message {
	mi := &file_internal_cli_server_proto_server_proto_msgTypes[6]

	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}

		return ms
	}

	return mi.MessageOf(x)
}

// Deprecated: Use PeersAddResponse.ProtoReflect.Descriptor instead.
func (*PeersAddResponse) Descriptor() ([]byte, []int) {
	return file_internal_cli_server_proto_server_proto_rawDescGZIP(), []int{6}
}

type PeersRemoveRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enode   string `protobuf:"bytes,1,opt,name=enode,proto3" json:"enode,omitempty"`
	Trusted bool   `protobuf:"varint,2,opt,name=trusted,proto3" json:"trusted,omitempty"`
}

func (x *PeersRemoveRequest) Reset() {
	*x = PeersRemoveRequest{}

	if protoimpl.UnsafeEnabled {
		mi := &file_internal_cli_server_proto_server_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PeersRemoveRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PeersRemoveRequest) ProtoMessage() {}

func (x *PeersRemoveRequest) ProtoReflect() protoreflect.Message {
	mi := &file_internal_cli_server_proto_server_proto_msgTypes[7]

	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}

		return ms
	}

	return mi.MessageOf(x)
}

// Deprecated: Use PeersRemoveRequest.ProtoReflect.Descriptor instead.
func (*PeersRemoveRequest) Descriptor() ([]byte, []int) {
	return file_internal_cli_server_proto_server_proto_rawDescGZIP(), []int{7}
}

func (x *PeersRemoveRequest) GetEnode() string {
	if x != nil {
		return x.Enode
	}

	return ""
}

func (x *PeersRemoveRequest) GetTrusted() bool {
	if x != nil {
		return x.Trusted
	}

	return false
}

type PeersRemoveResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PeersRemoveResponse) Reset() {
	*x = PeersRemoveResponse{}

	if protoimpl.UnsafeEnabled {
		mi := &file_internal_cli_server_proto_server_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PeersRemoveResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PeersRemoveResponse) ProtoMessage() {}

func (x *PeersRemoveResponse) ProtoReflect() protoreflect.Message {
	mi := &file_internal_cli_server_proto_server_proto_msgTypes[8]

	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}

		return ms
	}

	return mi.MessageOf(x)
}

// Deprecated: Use PeersRemoveResponse.ProtoReflect.Descriptor instead.
func (*PeersRemoveResponse) Descriptor() ([]byte, []int) {
	return file_internal_cli_server_proto_server_proto_rawDescGZIP(), []int{8}
}

type PeersListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *PeersListRequest) Reset() {
	*x = PeersListRequest{}

	if protoimpl.UnsafeEnabled {
		mi := &file_internal_cli_server_proto_server_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PeersListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PeersListRequest) ProtoMessage() {}

func (x *PeersListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_internal_cli_server_proto_server_proto_msgTypes[9]

	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}

		return ms
	}

	return mi.MessageOf(x)
}

// Deprecated: Use PeersListRequest.ProtoReflect.Descriptor instead.
func (*PeersListRequest) Descriptor() ([]byte, []int) {
	return file_internal_cli_server_proto_server_proto_rawDescGZIP(), []int{9}
}

type PeersListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Peers []*Peer `protobuf:"bytes,1,rep,name=peers,proto3" json:"peers,omitempty"`
}

func (x *PeersListResponse) Reset() {
	*x = PeersListResponse{}

	if protoimpl.UnsafeEnabled {
		mi := &file_internal_cli_server_proto_server_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PeersListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PeersListResponse) ProtoMessage() {}

func (x *PeersListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_internal_cli_server_proto_server_proto_msgTypes[10]

	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}

		return ms
	}

	return mi.MessageOf(x)
}

// Deprecated: Use PeersListResponse.ProtoReflect.Descriptor instead.
func (*PeersListResponse) Descriptor() ([]byte, []int) {
	return file_internal_cli_server_proto_server_proto_rawDescGZIP(), []int{10}
}

func (x *PeersListResponse) GetPeers() []*Peer {
	if x != nil {
		return x.Peers
	}

	return nil
}

type PeersStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enode string `protobuf:"bytes,1,opt,name=enode,proto3" json:"enode,omitempty"`
}

func (x *PeersStatusRequest) Reset() {
	*x = PeersStatusRequest{}

	if protoimpl.UnsafeEnabled {
		mi := &file_internal_cli_server_proto_server_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PeersStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PeersStatusRequest) ProtoMessage() {}

func (x *PeersStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_internal_cli_server_proto_server_proto_msgTypes[11]

	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}

		return ms
	}

	return mi.MessageOf(x)
}

// Deprecated: Use PeersStatusRequest.ProtoReflect.Descriptor instead.
func (*PeersStatusRequest) Descriptor() ([]byte, []int) {
	return file_internal_cli_server_proto_server_proto_rawDescGZIP(), []int{11}
}

func (x *PeersStatusRequest) GetEnode() string {
	if x != nil {
		return x.Enode
	}

	return ""
}

type PeersStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Peer *Peer `protobuf:"bytes,1,opt,name=peer,proto3" json:"peer,omitempty"`
}

func (x *PeersStatusResponse) Reset() {
	*x = PeersStatusResponse{}

	if protoimpl.UnsafeEnabled {
		mi := &file_internal_cli_server_proto_server_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PeersStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PeersStatusResponse) ProtoMessage() {}

func (x *PeersStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_internal_cli_server_proto_server_proto_msgTypes[12]

	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}

		return ms
	}

	return mi.MessageOf(x)
}

// Deprecated: Use PeersStatusResponse.ProtoReflect.Descriptor instead.
func (*PeersStatusResponse) Descriptor() ([]byte, []int) {
	return file_internal_cli_server_proto_server_proto_rawDescGZIP(), []int{12}
}

func (x *PeersStatusResponse) GetPeer() *Peer {
	if x != nil {
		return x.Peer
	}

	return nil
}

type Peer struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Enode   string   `protobuf:"bytes,2,opt,name=enode,proto3" json:"enode,omitempty"`
	Enr     string   `protobuf:"bytes,3,opt,name=enr,proto3" json:"enr,omitempty"`
	Caps    []string `protobuf:"bytes,4,rep,name=caps,proto3" json:"caps,omitempty"`
	Name    string   `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	Trusted bool     `protobuf:"varint,6,opt,name=trusted,proto3" json:"trusted,omitempty"`
	Static  bool     `protobuf:"varint,7,opt,name=static,proto3" json:"static,omitempty"`
}

func (x *Peer) Reset() {
	*x = Peer{}

	if protoimpl.UnsafeEnabled {
		mi := &file_internal_cli_server_proto_server_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Peer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Peer) ProtoMessage() {}

func (x *Peer) ProtoReflect() protoreflect.Message {
	mi := &file_internal_cli_server_proto_server_proto_msgTypes[13]

	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}

		return ms
	}

	return mi.MessageOf(x)
}

// Deprecated: Use Peer.ProtoReflect.Descriptor instead.
func (*Peer) Descriptor() ([]byte, []int) {
	return file_internal_cli_server_proto_server_proto_rawDescGZIP(), []int{13}
}

func (x *Peer) GetId() string {
	if x != nil {
		return x.Id
	}

	return ""
}

func (x *Peer) GetEnode() string {
	if x != nil {
		return x.Enode
	}

	return ""
}

func (x *Peer) GetEnr() string {
	if x != nil {
		return x.Enr
	}

	return ""
}

func (x *Peer) GetCaps() []string {
	if x != nil {
		return x.Caps
	}

	return nil
}

func (x *Peer) GetName() string {
	if x != nil {
		return x.Name
	}

	return ""
}

func (x *Peer) GetTrusted() bool {
	if x != nil {
		return x.Trusted
	}

	return false
}

func (x *Peer) GetStatic() bool {
	if x != nil {
		return x.Static
	}

	return false
}

type ChainSetHeadRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Number uint64 `protobuf:"varint,1,opt,name=number,proto3" json:"number,omitempty"`
}

func (x *ChainSetHeadRequest) Reset() {
	*x = ChainSetHeadRequest{}

	if protoimpl.UnsafeEnabled {
		mi := &file_internal_cli_server_proto_server_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChainSetHeadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChainSetHeadRequest) ProtoMessage() {}

func (x *ChainSetHeadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_internal_cli_server_proto_server_proto_msgTypes[14]

	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}

		return ms
	}

	return mi.MessageOf(x)
}

// Deprecated: Use ChainSetHeadRequest.ProtoReflect.Descriptor instead.
func (*ChainSetHeadRequest) Descriptor() ([]byte, []int) {
	return file_internal_cli_server_proto_server_proto_rawDescGZIP(), []int{14}
}

func (x *ChainSetHeadRequest) GetNumber() uint64 {
	if x != nil {
		return x.Number
	}

	return 0
}

type ChainSetHeadResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ChainSetHeadResponse) Reset() {
	*x = ChainSetHeadResponse{}

	if protoimpl.UnsafeEnabled {
		mi := &file_internal_cli_server_proto_server_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChainSetHeadResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChainSetHeadResponse) ProtoMessage() {}

func (x *ChainSetHeadResponse) ProtoReflect() protoreflect.Message {
	mi := &file_internal_cli_server_proto_server_proto_msgTypes[15]

	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}

		return ms
	}

	return mi.MessageOf(x)
}

// Deprecated: Use ChainSetHeadResponse.ProtoReflect.Descriptor instead.
func (*ChainSetHeadResponse) Descriptor() ([]byte, []int) {
	return file_internal_cli_server_proto_server_proto_rawDescGZIP(), []int{15}
}

type StatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Wait bool `protobuf:"varint,1,opt,name=Wait,proto3" json:"Wait,omitempty"`
}

func (x *StatusRequest) Reset() {
	*x = StatusRequest{}

	if protoimpl.UnsafeEnabled {
		mi := &file_internal_cli_server_proto_server_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatusRequest) ProtoMessage() {}

func (x *StatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_internal_cli_server_proto_server_proto_msgTypes[16]

	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}

		return ms
	}

	return mi.MessageOf(x)
}

// Deprecated: Use StatusRequest.ProtoReflect.Descriptor instead.
func (*StatusRequest) Descriptor() ([]byte, []int) {
	return file_internal_cli_server_proto_server_proto_rawDescGZIP(), []int{16}
}

func (x *StatusRequest) GetWait() bool {
	if x != nil {
		return x.Wait
	}

	return false
}

type StatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CurrentBlock  *Header                 `protobuf:"bytes,1,opt,name=currentBlock,proto3" json:"currentBlock,omitempty"`
	CurrentHeader *Header                 `protobuf:"bytes,2,opt,name=currentHeader,proto3" json:"currentHeader,omitempty"`
	NumPeers      int64                   `protobuf:"varint,3,opt,name=numPeers,proto3" json:"numPeers,omitempty"`
	SyncMode      string                  `protobuf:"bytes,4,opt,name=syncMode,proto3" json:"syncMode,omitempty"`
	Syncing       *StatusResponse_Syncing `protobuf:"bytes,5,opt,name=syncing,proto3" json:"syncing,omitempty"`
	Forks         []*StatusResponse_Fork  `protobuf:"bytes,6,rep,name=forks,proto3" json:"forks,omitempty"`
}

func (x *StatusResponse) Reset() {
	*x = StatusResponse{}

	if protoimpl.UnsafeEnabled {
		mi := &file_internal_cli_server_proto_server_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatusResponse) ProtoMessage() {}

func (x *StatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_internal_cli_server_proto_server_proto_msgTypes[17]

	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}

		return ms
	}

	return mi.MessageOf(x)
}

// Deprecated: Use StatusResponse.ProtoReflect.Descriptor instead.
func (*StatusResponse) Descriptor() ([]byte, []int) {
	return file_internal_cli_server_proto_server_proto_rawDescGZIP(), []int{17}
}

func (x *StatusResponse) GetCurrentBlock() *Header {
	if x != nil {
		return x.CurrentBlock
	}

	return nil
}

func (x *StatusResponse) GetCurrentHeader() *Header {
	if x != nil {
		return x.CurrentHeader
	}

	return nil
}

func (x *StatusResponse) GetNumPeers() int64 {
	if x != nil {
		return x.NumPeers
	}

	return 0
}

func (x *StatusResponse) GetSyncMode() string {
	if x != nil {
		return x.SyncMode
	}

	return ""
}

func (x *StatusResponse) GetSyncing() *StatusResponse_Syncing {
	if x != nil {
		return x.Syncing
	}

	return nil
}

func (x *StatusResponse) GetForks() []*StatusResponse_Fork {
	if x != nil {
		return x.Forks
	}

	return nil
}

type Header struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Hash   string `protobuf:"bytes,1,opt,name=hash,proto3" json:"hash,omitempty"`
	Number uint64 `protobuf:"varint,2,opt,name=number,proto3" json:"number,omitempty"`
}

func (x *Header) Reset() {
	*x = Header{}

	if protoimpl.UnsafeEnabled {
		mi := &file_internal_cli_server_proto_server_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Header) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Header) ProtoMessage() {}

func (x *Header) ProtoReflect() protoreflect.Message {
	mi := &file_internal_cli_server_proto_server_proto_msgTypes[18]

	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}

		return ms
	}

	return mi.MessageOf(x)
}

// Deprecated: Use Header.ProtoReflect.Descriptor instead.
func (*Header) Descriptor() ([]byte, []int) {
	return file_internal_cli_server_proto_server_proto_rawDescGZIP(), []int{18}
}

func (x *Header) GetHash() string {
	if x != nil {
		return x.Hash
	}

	return ""
}

func (x *Header) GetNumber() uint64 {
	if x != nil {
		return x.Number
	}

	return 0
}

type DebugPprofRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type    DebugPprofRequest_Type `protobuf:"varint,1,opt,name=type,proto3,enum=proto.DebugPprofRequest_Type" json:"type,omitempty"`
	Profile string                 `protobuf:"bytes,2,opt,name=profile,proto3" json:"profile,omitempty"`
	Seconds int64                  `protobuf:"varint,3,opt,name=seconds,proto3" json:"seconds,omitempty"`
}

func (x *DebugPprofRequest) Reset() {
	*x = DebugPprofRequest{}

	if protoimpl.UnsafeEnabled {
		mi := &file_internal_cli_server_proto_server_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DebugPprofRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DebugPprofRequest) ProtoMessage() {}

func (x *DebugPprofRequest) ProtoReflect() protoreflect.Message {
	mi := &file_internal_cli_server_proto_server_proto_msgTypes[19]

	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}

		return ms
	}

	return mi.MessageOf(x)
}

// Deprecated: Use DebugPprofRequest.ProtoReflect.Descriptor instead.
func (*DebugPprofRequest) Descriptor() ([]byte, []int) {
	return file_internal_cli_server_proto_server_proto_rawDescGZIP(), []int{19}
}

func (x *DebugPprofRequest) GetType() DebugPprofRequest_Type {
	if x != nil {
		return x.Type
	}

	return DebugPprofRequest_LOOKUP
}

func (x *DebugPprofRequest) GetProfile() string {
	if x != nil {
		return x.Profile
	}

	return ""
}

func (x *DebugPprofRequest) GetSeconds() int64 {
	if x != nil {
		return x.Seconds
	}

	return 0
}

type DebugBlockRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Number int64 `protobuf:"varint,1,opt,name=number,proto3" json:"number,omitempty"`
}

func (x *DebugBlockRequest) Reset() {
	*x = DebugBlockRequest{}

	if protoimpl.UnsafeEnabled {
		mi := &file_internal_cli_server_proto_server_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DebugBlockRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DebugBlockRequest) ProtoMessage() {}

func (x *DebugBlockRequest) ProtoReflect() protoreflect.Message {
	mi := &file_internal_cli_server_proto_server_proto_msgTypes[20]

	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}

		return ms
	}

	return mi.MessageOf(x)
}

// Deprecated: Use DebugBlockRequest.ProtoReflect.Descriptor instead.
func (*DebugBlockRequest) Descriptor() ([]byte, []int) {
	return file_internal_cli_server_proto_server_proto_rawDescGZIP(), []int{20}
}

func (x *DebugBlockRequest) GetNumber() int64 {
	if x != nil {
		return x.Number
	}

	return 0
}

type DebugFileResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Event:
	//
	//	*DebugFileResponse_Open_
	//	*DebugFileResponse_Input_
	//	*DebugFileResponse_Eof
	Event isDebugFileResponse_Event `protobuf_oneof:"event"`
}

func (x *DebugFileResponse) Reset() {
	*x = DebugFileResponse{}

	if protoimpl.UnsafeEnabled {
		mi := &file_internal_cli_server_proto_server_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DebugFileResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DebugFileResponse) ProtoMessage() {}

func (x *DebugFileResponse) ProtoReflect() protoreflect.Message {
	mi := &file_internal_cli_server_proto_server_proto_msgTypes[21]

	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}

		return ms
	}

	return mi.MessageOf(x)
}

// Deprecated: Use DebugFileResponse.ProtoReflect.Descriptor instead.
func (*DebugFileResponse) Descriptor() ([]byte, []int) {
	return file_internal_cli_server_proto_server_proto_rawDescGZIP(), []int{21}
}

func (m *DebugFileResponse) GetEvent() isDebugFileResponse_Event {
	if m != nil {
		return m.Event
	}

	return nil
}

func (x *DebugFileResponse) GetOpen() *DebugFileResponse_Open {
	if x, ok := x.GetEvent().(*DebugFileResponse_Open_); ok {
		return x.Open
	}

	return nil
}

func (x *DebugFileResponse) GetInput() *DebugFileResponse_Input {
	if x, ok := x.GetEvent().(*DebugFileResponse_Input_); ok {
		return x.Input
	}

	return nil
}

func (x *DebugFileResponse) GetEof() *emptypb.Empty {
	if x, ok := x.GetEvent().(*DebugFileResponse_Eof); ok {
		return x.Eof
	}

	return nil
}

type isDebugFileResponse_Event interface {
	isDebugFileResponse_Event()
}

type DebugFileResponse_Open_ struct {
	Open *DebugFileResponse_Open `protobuf:"bytes,1,opt,name=open,proto3,oneof"`
}

type DebugFileResponse_Input_ struct {
	Input *DebugFileResponse_Input `protobuf:"bytes,2,opt,name=input,proto3,oneof"`
}

type DebugFileResponse_Eof struct {
	Eof *emptypb.Empty `protobuf:"bytes,3,opt,name=eof,proto3,oneof"`
}

func (*DebugFileResponse_Open_) isDebugFileResponse_Event() {}

func (*DebugFileResponse_Input_) isDebugFileResponse_Event() {}

func (*DebugFileResponse_Eof) isDebugFileResponse_Event() {}

type StatusResponse_Fork struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name     string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Block    int64  `protobuf:"varint,2,opt,name=block,proto3" json:"block,omitempty"`
	Disabled bool   `protobuf:"varint,3,opt,name=disabled,proto3" json:"disabled,omitempty"`
}

func (x *StatusResponse_Fork) Reset() {
	*x = StatusResponse_Fork{}

	if protoimpl.UnsafeEnabled {
		mi := &file_internal_cli_server_proto_server_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatusResponse_Fork) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatusResponse_Fork) ProtoMessage() {}

func (x *StatusResponse_Fork) ProtoReflect() protoreflect.Message {
	mi := &file_internal_cli_server_proto_server_proto_msgTypes[22]

	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}

		return ms
	}

	return mi.MessageOf(x)
}

// Deprecated: Use StatusResponse_Fork.ProtoReflect.Descriptor instead.
func (*StatusResponse_Fork) Descriptor() ([]byte, []int) {
	return file_internal_cli_server_proto_server_proto_rawDescGZIP(), []int{17, 0}
}

func (x *StatusResponse_Fork) GetName() string {
	if x != nil {
		return x.Name
	}

	return ""
}

func (x *StatusResponse_Fork) GetBlock() int64 {
	if x != nil {
		return x.Block
	}

	return 0
}

func (x *StatusResponse_Fork) GetDisabled() bool {
	if x != nil {
		return x.Disabled
	}

	return false
}

type StatusResponse_Syncing struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StartingBlock int64 `protobuf:"varint,1,opt,name=startingBlock,proto3" json:"startingBlock,omitempty"`
	HighestBlock  int64 `protobuf:"varint,2,opt,name=highestBlock,proto3" json:"highestBlock,omitempty"`
	CurrentBlock  int64 `protobuf:"varint,3,opt,name=currentBlock,proto3" json:"currentBlock,omitempty"`
}

func (x *StatusResponse_Syncing) Reset() {
	*x = StatusResponse_Syncing{}

	if protoimpl.UnsafeEnabled {
		mi := &file_internal_cli_server_proto_server_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StatusResponse_Syncing) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StatusResponse_Syncing) ProtoMessage() {}

func (x *StatusResponse_Syncing) ProtoReflect() protoreflect.Message {
	mi := &file_internal_cli_server_proto_server_proto_msgTypes[23]

	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}

		return ms
	}

	return mi.MessageOf(x)
}

// Deprecated: Use StatusResponse_Syncing.ProtoReflect.Descriptor instead.
func (*StatusResponse_Syncing) Descriptor() ([]byte, []int) {
	return file_internal_cli_server_proto_server_proto_rawDescGZIP(), []int{17, 1}
}

func (x *StatusResponse_Syncing) GetStartingBlock() int64 {
	if x != nil {
		return x.StartingBlock
	}

	return 0
}

func (x *StatusResponse_Syncing) GetHighestBlock() int64 {
	if x != nil {
		return x.HighestBlock
	}

	return 0
}

func (x *StatusResponse_Syncing) GetCurrentBlock() int64 {
	if x != nil {
		return x.CurrentBlock
	}

	return 0
}

type DebugFileResponse_Open struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Headers map[string]string `protobuf:"bytes,1,rep,name=headers,proto3" json:"headers,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *DebugFileResponse_Open) Reset() {
	*x = DebugFileResponse_Open{}

	if protoimpl.UnsafeEnabled {
		mi := &file_internal_cli_server_proto_server_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DebugFileResponse_Open) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DebugFileResponse_Open) ProtoMessage() {}

func (x *DebugFileResponse_Open) ProtoReflect() protoreflect.Message {
	mi := &file_internal_cli_server_proto_server_proto_msgTypes[24]

	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}

		return ms
	}

	return mi.MessageOf(x)
}

// Deprecated: Use DebugFileResponse_Open.ProtoReflect.Descriptor instead.
func (*DebugFileResponse_Open) Descriptor() ([]byte, []int) {
	return file_internal_cli_server_proto_server_proto_rawDescGZIP(), []int{21, 0}
}

func (x *DebugFileResponse_Open) GetHeaders() map[string]string {
	if x != nil {
		return x.Headers
	}

	return nil
}

type DebugFileResponse_Input struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []byte `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *DebugFileResponse_Input) Reset() {
	*x = DebugFileResponse_Input{}

	if protoimpl.UnsafeEnabled {
		mi := &file_internal_cli_server_proto_server_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DebugFileResponse_Input) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DebugFileResponse_Input) ProtoMessage() {}

func (x *DebugFileResponse_Input) ProtoReflect() protoreflect.Message {
	mi := &file_internal_cli_server_proto_server_proto_msgTypes[25]

	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}

		return ms
	}

	return mi.MessageOf(x)
}

// Deprecated: Use DebugFileResponse_Input.ProtoReflect.Descriptor instead.
func (*DebugFileResponse_Input) Descriptor() ([]byte, []int) {
	return file_internal_cli_server_proto_server_proto_rawDescGZIP(), []int{21, 1}
}

func (x *DebugFileResponse_Input) GetData() []byte {
	if x != nil {
		return x.Data
	}

	return nil
}

var File_internal_cli_server_proto_server_proto protoreflect.FileDescriptor

var file_internal_cli_server_proto_server_proto_rawDesc = []byte{
	0x0a, 0x26, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x63, 0x6c, 0x69, 0x2f, 0x73,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x26, 0x0a, 0x0c,
	0x54, 0x72, 0x61, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x22, 0x0f, 0x0a, 0x0d, 0x54, 0x72, 0x61, 0x63, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x13, 0x0a, 0x11, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x57, 0x61,
	0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x84, 0x01, 0x0a, 0x12, 0x43,
	0x68, 0x61, 0x69, 0x6e, 0x57, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x2c, 0x0a, 0x08, 0x6f, 0x6c, 0x64, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x42, 0x6c, 0x6f, 0x63,
	0x6b, 0x53, 0x74, 0x75, 0x62, 0x52, 0x08, 0x6f, 0x6c, 0x64, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12,
	0x2c, 0x0a, 0x08, 0x6e, 0x65, 0x77, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x10, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x53,
	0x74, 0x75, 0x62, 0x52, 0x08, 0x6e, 0x65, 0x77, 0x63, 0x68, 0x61, 0x69, 0x6e, 0x12, 0x12, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x22, 0x37, 0x0a, 0x09, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x53, 0x74, 0x75, 0x62, 0x12, 0x12,
	0x0a, 0x04, 0x68, 0x61, 0x73, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x61,
	0x73, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x41, 0x0a, 0x0f, 0x50, 0x65,
	0x65, 0x72, 0x73, 0x41, 0x64, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a,
	0x05, 0x65, 0x6e, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6e,
	0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x72, 0x75, 0x73, 0x74, 0x65, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x74, 0x72, 0x75, 0x73, 0x74, 0x65, 0x64, 0x22, 0x12, 0x0a,
	0x10, 0x50, 0x65, 0x65, 0x72, 0x73, 0x41, 0x64, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x44, 0x0a, 0x12, 0x50, 0x65, 0x65, 0x72, 0x73, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6e, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6e, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x74, 0x72, 0x75, 0x73, 0x74, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07,
	0x74, 0x72, 0x75, 0x73, 0x74, 0x65, 0x64, 0x22, 0x15, 0x0a, 0x13, 0x50, 0x65, 0x65, 0x72, 0x73,
	0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x12,
	0x0a, 0x10, 0x50, 0x65, 0x65, 0x72, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x22, 0x36, 0x0a, 0x11, 0x50, 0x65, 0x65, 0x72, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x21, 0x0a, 0x05, 0x70, 0x65, 0x65, 0x72, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50,
	0x65, 0x65, 0x72, 0x52, 0x05, 0x70, 0x65, 0x65, 0x72, 0x73, 0x22, 0x2a, 0x0a, 0x12, 0x50, 0x65,
	0x65, 0x72, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x14, 0x0a, 0x05, 0x65, 0x6e, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x65, 0x6e, 0x6f, 0x64, 0x65, 0x22, 0x36, 0x0a, 0x13, 0x50, 0x65, 0x65, 0x72, 0x73, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1f, 0x0a,
	0x04, 0x70, 0x65, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x65, 0x65, 0x72, 0x52, 0x04, 0x70, 0x65, 0x65, 0x72, 0x22, 0x98,
	0x01, 0x0a, 0x04, 0x50, 0x65, 0x65, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6e, 0x6f, 0x64, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6e, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a,
	0x03, 0x65, 0x6e, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x65, 0x6e, 0x72, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x61, 0x70, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x63,
	0x61, 0x70, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x72, 0x75, 0x73, 0x74,
	0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x74, 0x72, 0x75, 0x73, 0x74, 0x65,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x69, 0x63, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x69, 0x63, 0x22, 0x2d, 0x0a, 0x13, 0x43, 0x68, 0x61,
	0x69, 0x6e, 0x53, 0x65, 0x74, 0x48, 0x65, 0x61, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x16, 0x0a, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x16, 0x0a, 0x14, 0x43, 0x68, 0x61, 0x69,
	0x6e, 0x53, 0x65, 0x74, 0x48, 0x65, 0x61, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x23, 0x0a, 0x0d, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x57, 0x61, 0x69, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x04, 0x57, 0x61, 0x69, 0x74, 0x22, 0xe2, 0x03, 0x0a, 0x0e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x31, 0x0a, 0x0c, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0c, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x33, 0x0a, 0x0d, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x75, 0x6d, 0x50, 0x65, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x08, 0x6e, 0x75, 0x6d, 0x50, 0x65, 0x65, 0x72, 0x73, 0x12, 0x1a, 0x0a, 0x08,
	0x73, 0x79, 0x6e, 0x63, 0x4d, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x73, 0x79, 0x6e, 0x63, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x37, 0x0a, 0x07, 0x73, 0x79, 0x6e, 0x63,
	0x69, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x53, 0x79, 0x6e, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x07, 0x73, 0x79, 0x6e, 0x63, 0x69, 0x6e,
	0x67, 0x12, 0x30, 0x0a, 0x05, 0x66, 0x6f, 0x72, 0x6b, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x46, 0x6f, 0x72, 0x6b, 0x52, 0x05, 0x66, 0x6f,
	0x72, 0x6b, 0x73, 0x1a, 0x4c, 0x0a, 0x04, 0x46, 0x6f, 0x72, 0x6b, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05,
	0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x1a, 0x77, 0x0a, 0x07, 0x53, 0x79, 0x6e, 0x63, 0x69, 0x6e, 0x67, 0x12, 0x24, 0x0a, 0x0d,
	0x73, 0x74, 0x61, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0d, 0x73, 0x74, 0x61, 0x72, 0x74, 0x69, 0x6e, 0x67, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x12, 0x22, 0x0a, 0x0c, 0x68, 0x69, 0x67, 0x68, 0x65, 0x73, 0x74, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x68, 0x69, 0x67, 0x68, 0x65, 0x73,
	0x74, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x22, 0x0a, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x74, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x22, 0x34, 0x0a, 0x06, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x61, 0x73, 0x68, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x68, 0x61, 0x73, 0x68, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x22, 0xa2, 0x01, 0x0a, 0x11, 0x44, 0x65, 0x62, 0x75, 0x67, 0x50, 0x70, 0x72, 0x6f, 0x66, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x31, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x62,
	0x75, 0x67, 0x50, 0x70, 0x72, 0x6f, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x22, 0x26, 0x0a,
	0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0a, 0x0a, 0x06, 0x4c, 0x4f, 0x4f, 0x4b, 0x55, 0x50, 0x10,
	0x00, 0x12, 0x07, 0x0a, 0x03, 0x43, 0x50, 0x55, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x54, 0x52,
	0x41, 0x43, 0x45, 0x10, 0x02, 0x22, 0x2b, 0x0a, 0x11, 0x44, 0x65, 0x62, 0x75, 0x67, 0x42, 0x6c,
	0x6f, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x22, 0xdd, 0x02, 0x0a, 0x11, 0x44, 0x65, 0x62, 0x75, 0x67, 0x46, 0x69, 0x6c, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x33, 0x0a, 0x04, 0x6f, 0x70, 0x65, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44,
	0x65, 0x62, 0x75, 0x67, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x4f, 0x70, 0x65, 0x6e, 0x48, 0x00, 0x52, 0x04, 0x6f, 0x70, 0x65, 0x6e, 0x12, 0x36, 0x0a,
	0x05, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x62, 0x75, 0x67, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x48, 0x00, 0x52, 0x05,
	0x69, 0x6e, 0x70, 0x75, 0x74, 0x12, 0x2a, 0x0a, 0x03, 0x65, 0x6f, 0x66, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x48, 0x00, 0x52, 0x03, 0x65, 0x6f,
	0x66, 0x1a, 0x88, 0x01, 0x0a, 0x04, 0x4f, 0x70, 0x65, 0x6e, 0x12, 0x44, 0x0a, 0x07, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x62, 0x75, 0x67, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x4f, 0x70, 0x65, 0x6e, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73,
	0x1a, 0x3a, 0x0a, 0x0c, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x1b, 0x0a, 0x05,
	0x49, 0x6e, 0x70, 0x75, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x42, 0x07, 0x0a, 0x05, 0x65, 0x76, 0x65,
	0x6e, 0x74, 0x32, 0xdb, 0x04, 0x0a, 0x03, 0x42, 0x6f, 0x72, 0x12, 0x3b, 0x0a, 0x08, 0x50, 0x65,
	0x65, 0x72, 0x73, 0x41, 0x64, 0x64, 0x12, 0x16, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50,
	0x65, 0x65, 0x72, 0x73, 0x41, 0x64, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x17,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x65, 0x65, 0x72, 0x73, 0x41, 0x64, 0x64, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x44, 0x0a, 0x0b, 0x50, 0x65, 0x65, 0x72, 0x73,
	0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x12, 0x19, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50,
	0x65, 0x65, 0x72, 0x73, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x65, 0x65, 0x72, 0x73, 0x52,
	0x65, 0x6d, 0x6f, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3e, 0x0a,
	0x09, 0x50, 0x65, 0x65, 0x72, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x17, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x50, 0x65, 0x65, 0x72, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x65, 0x65, 0x72,
	0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x44, 0x0a,
	0x0b, 0x50, 0x65, 0x65, 0x72, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x19, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x50, 0x65, 0x65, 0x72, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e,
	0x50, 0x65, 0x65, 0x72, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x47, 0x0a, 0x0c, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x74, 0x48,
	0x65, 0x61, 0x64, 0x12, 0x1a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x68, 0x61, 0x69,
	0x6e, 0x53, 0x65, 0x74, 0x48, 0x65, 0x61, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x53, 0x65, 0x74,
	0x48, 0x65, 0x61, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x35, 0x0a, 0x06,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x14, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x43, 0x0a, 0x0a, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x57, 0x61, 0x74, 0x63,
	0x68, 0x12, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x57,
	0x61, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x2e, 0x43, 0x68, 0x61, 0x69, 0x6e, 0x57, 0x61, 0x74, 0x63, 0x68, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x30, 0x01, 0x12, 0x42, 0x0a, 0x0a, 0x44, 0x65, 0x62, 0x75,
	0x67, 0x50, 0x70, 0x72, 0x6f, 0x66, 0x12, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44,
	0x65, 0x62, 0x75, 0x67, 0x50, 0x70, 0x72, 0x6f, 0x66, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x62, 0x75, 0x67, 0x46, 0x69,
	0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x30, 0x01, 0x12, 0x42, 0x0a, 0x0a,
	0x44, 0x65, 0x62, 0x75, 0x67, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x18, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x2e, 0x44, 0x65, 0x62, 0x75, 0x67, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x18, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2e, 0x44, 0x65, 0x62,
	0x75, 0x67, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x30, 0x01,
	0x42, 0x1c, 0x5a, 0x1a, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x63, 0x6c,
	0x69, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_internal_cli_server_proto_server_proto_rawDescOnce sync.Once
	file_internal_cli_server_proto_server_proto_rawDescData = file_internal_cli_server_proto_server_proto_rawDesc
)

func file_internal_cli_server_proto_server_proto_rawDescGZIP() []byte {
	file_internal_cli_server_proto_server_proto_rawDescOnce.Do(func() {
		file_internal_cli_server_proto_server_proto_rawDescData = protoimpl.X.CompressGZIP(file_internal_cli_server_proto_server_proto_rawDescData)
	})

	return file_internal_cli_server_proto_server_proto_rawDescData
}

var file_internal_cli_server_proto_server_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_internal_cli_server_proto_server_proto_msgTypes = make([]protoimpl.MessageInfo, 27)
var file_internal_cli_server_proto_server_proto_goTypes = []interface{}{
	(DebugPprofRequest_Type)(0),     // 0: proto.DebugPprofRequest.Type
	(*TraceRequest)(nil),            // 1: proto.TraceRequest
	(*TraceResponse)(nil),           // 2: proto.TraceResponse
	(*ChainWatchRequest)(nil),       // 3: proto.ChainWatchRequest
	(*ChainWatchResponse)(nil),      // 4: proto.ChainWatchResponse
	(*BlockStub)(nil),               // 5: proto.BlockStub
	(*PeersAddRequest)(nil),         // 6: proto.PeersAddRequest
	(*PeersAddResponse)(nil),        // 7: proto.PeersAddResponse
	(*PeersRemoveRequest)(nil),      // 8: proto.PeersRemoveRequest
	(*PeersRemoveResponse)(nil),     // 9: proto.PeersRemoveResponse
	(*PeersListRequest)(nil),        // 10: proto.PeersListRequest
	(*PeersListResponse)(nil),       // 11: proto.PeersListResponse
	(*PeersStatusRequest)(nil),      // 12: proto.PeersStatusRequest
	(*PeersStatusResponse)(nil),     // 13: proto.PeersStatusResponse
	(*Peer)(nil),                    // 14: proto.Peer
	(*ChainSetHeadRequest)(nil),     // 15: proto.ChainSetHeadRequest
	(*ChainSetHeadResponse)(nil),    // 16: proto.ChainSetHeadResponse
	(*StatusRequest)(nil),           // 17: proto.StatusRequest
	(*StatusResponse)(nil),          // 18: proto.StatusResponse
	(*Header)(nil),                  // 19: proto.Header
	(*DebugPprofRequest)(nil),       // 20: proto.DebugPprofRequest
	(*DebugBlockRequest)(nil),       // 21: proto.DebugBlockRequest
	(*DebugFileResponse)(nil),       // 22: proto.DebugFileResponse
	(*StatusResponse_Fork)(nil),     // 23: proto.StatusResponse.Fork
	(*StatusResponse_Syncing)(nil),  // 24: proto.StatusResponse.Syncing
	(*DebugFileResponse_Open)(nil),  // 25: proto.DebugFileResponse.Open
	(*DebugFileResponse_Input)(nil), // 26: proto.DebugFileResponse.Input
	nil,                             // 27: proto.DebugFileResponse.Open.HeadersEntry
	(*emptypb.Empty)(nil),           // 28: google.protobuf.Empty
}
var file_internal_cli_server_proto_server_proto_depIdxs = []int32{
	5,  // 0: proto.ChainWatchResponse.oldchain:type_name -> proto.BlockStub
	5,  // 1: proto.ChainWatchResponse.newchain:type_name -> proto.BlockStub
	14, // 2: proto.PeersListResponse.peers:type_name -> proto.Peer
	14, // 3: proto.PeersStatusResponse.peer:type_name -> proto.Peer
	19, // 4: proto.StatusResponse.currentBlock:type_name -> proto.Header
	19, // 5: proto.StatusResponse.currentHeader:type_name -> proto.Header
	24, // 6: proto.StatusResponse.syncing:type_name -> proto.StatusResponse.Syncing
	23, // 7: proto.StatusResponse.forks:type_name -> proto.StatusResponse.Fork
	0,  // 8: proto.DebugPprofRequest.type:type_name -> proto.DebugPprofRequest.Type
	25, // 9: proto.DebugFileResponse.open:type_name -> proto.DebugFileResponse.Open
	26, // 10: proto.DebugFileResponse.input:type_name -> proto.DebugFileResponse.Input
	28, // 11: proto.DebugFileResponse.eof:type_name -> google.protobuf.Empty
	27, // 12: proto.DebugFileResponse.Open.headers:type_name -> proto.DebugFileResponse.Open.HeadersEntry
	6,  // 13: proto.Bor.PeersAdd:input_type -> proto.PeersAddRequest
	8,  // 14: proto.Bor.PeersRemove:input_type -> proto.PeersRemoveRequest
	10, // 15: proto.Bor.PeersList:input_type -> proto.PeersListRequest
	12, // 16: proto.Bor.PeersStatus:input_type -> proto.PeersStatusRequest
	15, // 17: proto.Bor.ChainSetHead:input_type -> proto.ChainSetHeadRequest
	17, // 18: proto.Bor.Status:input_type -> proto.StatusRequest
	3,  // 19: proto.Bor.ChainWatch:input_type -> proto.ChainWatchRequest
	20, // 20: proto.Bor.DebugPprof:input_type -> proto.DebugPprofRequest
	21, // 21: proto.Bor.DebugBlock:input_type -> proto.DebugBlockRequest
	7,  // 22: proto.Bor.PeersAdd:output_type -> proto.PeersAddResponse
	9,  // 23: proto.Bor.PeersRemove:output_type -> proto.PeersRemoveResponse
	11, // 24: proto.Bor.PeersList:output_type -> proto.PeersListResponse
	13, // 25: proto.Bor.PeersStatus:output_type -> proto.PeersStatusResponse
	16, // 26: proto.Bor.ChainSetHead:output_type -> proto.ChainSetHeadResponse
	18, // 27: proto.Bor.Status:output_type -> proto.StatusResponse
	4,  // 28: proto.Bor.ChainWatch:output_type -> proto.ChainWatchResponse
	22, // 29: proto.Bor.DebugPprof:output_type -> proto.DebugFileResponse
	22, // 30: proto.Bor.DebugBlock:output_type -> proto.DebugFileResponse
	22, // [22:31] is the sub-list for method output_type
	13, // [13:22] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_internal_cli_server_proto_server_proto_init() }
func file_internal_cli_server_proto_server_proto_init() {
	if File_internal_cli_server_proto_server_proto != nil {
		return
	}

	if !protoimpl.UnsafeEnabled {
		file_internal_cli_server_proto_server_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TraceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_cli_server_proto_server_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TraceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_cli_server_proto_server_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChainWatchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_cli_server_proto_server_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChainWatchResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_cli_server_proto_server_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BlockStub); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_cli_server_proto_server_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PeersAddRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_cli_server_proto_server_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PeersAddResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_cli_server_proto_server_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PeersRemoveRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_cli_server_proto_server_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PeersRemoveResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_cli_server_proto_server_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PeersListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_cli_server_proto_server_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PeersListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_cli_server_proto_server_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PeersStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_cli_server_proto_server_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PeersStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_cli_server_proto_server_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Peer); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_cli_server_proto_server_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChainSetHeadRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_cli_server_proto_server_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChainSetHeadResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_cli_server_proto_server_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_cli_server_proto_server_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_cli_server_proto_server_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Header); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_cli_server_proto_server_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DebugPprofRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_cli_server_proto_server_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DebugBlockRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_cli_server_proto_server_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DebugFileResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_cli_server_proto_server_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StatusResponse_Fork); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_cli_server_proto_server_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StatusResponse_Syncing); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_cli_server_proto_server_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DebugFileResponse_Open); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_internal_cli_server_proto_server_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DebugFileResponse_Input); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}

	file_internal_cli_server_proto_server_proto_msgTypes[21].OneofWrappers = []interface{}{
		(*DebugFileResponse_Open_)(nil),
		(*DebugFileResponse_Input_)(nil),
		(*DebugFileResponse_Eof)(nil),
	}

	type x struct{}

	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_internal_cli_server_proto_server_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   27,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_internal_cli_server_proto_server_proto_goTypes,
		DependencyIndexes: file_internal_cli_server_proto_server_proto_depIdxs,
		EnumInfos:         file_internal_cli_server_proto_server_proto_enumTypes,
		MessageInfos:      file_internal_cli_server_proto_server_proto_msgTypes,
	}.Build()
	File_internal_cli_server_proto_server_proto = out.File
	file_internal_cli_server_proto_server_proto_rawDesc = nil
	file_internal_cli_server_proto_server_proto_goTypes = nil
	file_internal_cli_server_proto_server_proto_depIdxs = nil
}
