package test

import "github.com/ethereum/go-ethereum/rlp"
import "io"

func (obj *Test) EncodeRLP(_w io.Writer) error {
	w := rlp.NewEncoderBuffer(_w)
	_tmp0 := w.List()
	if obj.Int == nil {
		w.Write(rlp.EmptyString)
	} else {
		if obj.Int.Sign() == -1 {
			return rlp.ErrNegativeBigInt
		}
		w.WriteBigInt(obj.Int)
	}
	if obj.IntNoPtr.Sign() == -1 {
		return rlp.ErrNegativeBigInt
	}
	w.WriteBigInt(&obj.IntNoPtr)
	w.ListEnd(_tmp0)
	return w.Flush()
}

func (obj *Test) DecodeRLP(dec *rlp.Stream) error {
	var _tmp0 Test
	{
		if _, err := dec.List(); err != nil {
			return err
		}
		// Int:
		_tmp1, err := dec.BigInt()
		if err != nil {
			return err
		}
		_tmp0.Int = _tmp1
		// IntNoPtr:
		_tmp2, err := dec.BigInt()
		if err != nil {
			return err
		}
		_tmp0.IntNoPtr = (*_tmp2)
		if err := dec.ListEnd(); err != nil {
			return err
		}
	}
	*obj = _tmp0
	return nil
}
