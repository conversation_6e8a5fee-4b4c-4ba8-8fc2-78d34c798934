package test

import "github.com/ethereum/go-ethereum/rlp"
import "github.com/holiman/uint256"
import "io"

func (obj *Test) EncodeRLP(_w io.Writer) error {
	w := rlp.NewEncoderBuffer(_w)
	_tmp0 := w.List()
	if obj.Int == nil {
		w.Write(rlp.EmptyString)
	} else {
		w.WriteUint256(obj.Int)
	}
	w.WriteUint256(&obj.IntNoPtr)
	w.ListEnd(_tmp0)
	return w.Flush()
}

func (obj *Test) DecodeRLP(dec *rlp.Stream) error {
	var _tmp0 Test
	{
		if _, err := dec.List(); err != nil {
			return err
		}
		// Int:
		var _tmp1 uint256.Int
		if err := dec.ReadUint256(&_tmp1); err != nil {
			return err
		}
		_tmp0.Int = &_tmp1
		// IntNoPtr:
		var _tmp2 uint256.Int
		if err := dec.ReadUint256(&_tmp2); err != nil {
			return err
		}
		_tmp0.IntNoPtr = _tmp2
		if err := dec.ListEnd(); err != nil {
			return err
		}
	}
	*obj = _tmp0
	return nil
}
