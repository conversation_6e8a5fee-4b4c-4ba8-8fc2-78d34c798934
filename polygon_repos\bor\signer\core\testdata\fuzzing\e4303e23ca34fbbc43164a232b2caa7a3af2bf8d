{"types": {"EIP712Domain": [{"name": "name", "type": "string"}, {"name": "version", "type": "string"}, {"name": "chainId", "type": "int"}, {"name": "verifyingContract", "type": "address"}], "Person": [{"name": "name", "type": "string"}, {"name": "wallet", "type": "address"}], "Mail": [{"name": "from", "type": "Person"}, {"name": "to", "type": "Mail"}, {"name": "s", "type": "Person"}]}, "primaryType": "Mail", "domain": {"name": "l", "version": "1", "chainId": "", "verifyingContract": "******************************************"}, "message": {"from": {"name": "", "wallet": "******************************************"}, "to": {"name": "", "wallet": "******************************************"}, "": ""}}