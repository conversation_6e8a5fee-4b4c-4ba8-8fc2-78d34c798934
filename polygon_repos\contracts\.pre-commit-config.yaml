repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: mixed-line-ending
        args: ["--fix=lf"]
        description: Forces to replace line ending by the UNIX 'lf' character.
        exclude: "^docs/autogen"
  - repo: local
    hooks:
      - id: format
        name: Format solidity code
        description: Format solidity code with `forge fmt`
        language: system
        entry: forge fmt
        exclude: "^lib/"
        pass_filenames: true
      - id: doc
        name: Generate documentation
        description: Generate docs with `forge doc`
        language: system
        # generates docs and unstages files if only the commit hash changed within the file, this way only when the documentation is updated, the documentation needs to be regenerated and only the changed files are pushed
        entry: "scripts/util/doc_gen.sh"
        pass_filenames: false
  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: "v3.0.3"
    hooks:
      - id: prettier
        name: Format non solidity files with prettier
        exclude: "^docs/autogen"
