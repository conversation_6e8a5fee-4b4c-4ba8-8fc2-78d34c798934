# Summary
- [Home](README.md)
# contracts
  - [❱ child](contracts/child/README.md)
    - [❱ bor](contracts/child/bor/README.md)
      - [StateReceiver](contracts/child/bor/StateReceiver.sol/interface.StateReceiver.md)
      - [StateSyncerVerifier](contracts/child/bor/StateSyncerVerifier.sol/contract.StateSyncerVerifier.md)
    - [❱ misc](contracts/child/misc/README.md)
      - [LibEIP712Domain](contracts/child/misc/EIP712.sol/contract.LibEIP712Domain.md)
      - [IParentToken](contracts/child/misc/IParentToken.sol/interface.IParentToken.md)
      - [LibTokenTransferOrder](contracts/child/misc/LibTokenTransferOrder.sol/contract.LibTokenTransferOrder.md)
      - [MarketplaceToken](contracts/child/misc/Marketplace.sol/interface.MarketplaceToken.md)
      - [Marketplace](contracts/child/misc/Marketplace.sol/contract.Marketplace.md)
      - [ParentTokenMock](contracts/child/misc/ParentTokenMock.sol/contract.ParentTokenMock.md)
    - [❱ proxifiedChildToken](contracts/child/proxifiedChildToken/README.md)
      - [ChildERC20Proxified](contracts/child/proxifiedChildToken/ChildERC20Proxified.sol/contract.ChildERC20Proxified.md)
      - [ChildERC721Proxified](contracts/child/proxifiedChildToken/ChildERC721Proxified.sol/contract.ChildERC721Proxified.md)
      - [ChildTokenProxy](contracts/child/proxifiedChildToken/ChildTokenProxy.sol/contract.ChildTokenProxy.md)
    - [BaseERC20](contracts/child/BaseERC20.sol/contract.BaseERC20.md)
    - [BaseERC20NoSig](contracts/child/BaseERC20NoSig.sol/contract.BaseERC20NoSig.md)
    - [ChildChain](contracts/child/ChildChain.sol/contract.ChildChain.md)
    - [ChildERC20](contracts/child/ChildERC20.sol/contract.ChildERC20.md)
    - [ChildERC721](contracts/child/ChildERC721.sol/contract.ChildERC721.md)
    - [ChildERC721Mintable](contracts/child/ChildERC721Mintable.sol/contract.ChildERC721Mintable.md)
    - [ChildToken](contracts/child/ChildToken.sol/contract.ChildToken.md)
    - [ERC20Detailed](contracts/child/ERC20Detailed.sol/contract.ERC20Detailed.md)
    - [MRC20](contracts/child/MRC20.sol/contract.MRC20.md)
  - [❱ common](contracts/common/README.md)
    - [❱ gnosis](contracts/common/gnosis/README.md)
      - [SelfAuthorized](contracts/common/gnosis/GnosisSafe.sol/contract.SelfAuthorized.md)
      - [MasterCopy](contracts/common/gnosis/GnosisSafe.sol/contract.MasterCopy.md)
      - [Module](contracts/common/gnosis/GnosisSafe.sol/contract.Module.md)
      - [Enum](contracts/common/gnosis/GnosisSafe.sol/contract.Enum.md)
      - [Executor](contracts/common/gnosis/GnosisSafe.sol/contract.Executor.md)
      - [SecuredTokenTransfer](contracts/common/gnosis/GnosisSafe.sol/contract.SecuredTokenTransfer.md)
      - [ModuleManager](contracts/common/gnosis/GnosisSafe.sol/contract.ModuleManager.md)
      - [OwnerManager](contracts/common/gnosis/GnosisSafe.sol/contract.OwnerManager.md)
      - [FallbackManager](contracts/common/gnosis/GnosisSafe.sol/contract.FallbackManager.md)
      - [SignatureDecoder](contracts/common/gnosis/GnosisSafe.sol/contract.SignatureDecoder.md)
      - [ISignatureValidatorConstants](contracts/common/gnosis/GnosisSafe.sol/contract.ISignatureValidatorConstants.md)
      - [ISignatureValidator](contracts/common/gnosis/GnosisSafe.sol/contract.ISignatureValidator.md)
      - [SafeMath](contracts/common/gnosis/GnosisSafe.sol/library.SafeMath.md)
      - [GnosisSafe](contracts/common/gnosis/GnosisSafe.sol/contract.GnosisSafe.md)
      - [GnosisSafeProxy](contracts/common/gnosis/GnosisSafeProxy.sol/contract.GnosisSafeProxy.md)
    - [❱ governance](contracts/common/governance/README.md)
      - [Governable](contracts/common/governance/Governable.sol/contract.Governable.md)
      - [Governance](contracts/common/governance/Governance.sol/contract.Governance.md)
      - [GovernanceProxy](contracts/common/governance/GovernanceProxy.sol/contract.GovernanceProxy.md)
      - [IGovernance](contracts/common/governance/IGovernance.sol/interface.IGovernance.md)
    - [❱ lib](contracts/common/lib/README.md)
      - [BytesLib](contracts/common/lib/BytesLib.sol/library.BytesLib.md)
      - [Common](contracts/common/lib/Common.sol/library.Common.md)
      - [ECVerify](contracts/common/lib/ECVerify.sol/library.ECVerify.md)
      - [ExitPayloadReader](contracts/common/lib/ExitPayloadReader.sol/library.ExitPayloadReader.md)
      - [Merkle](contracts/common/lib/Merkle.sol/library.Merkle.md)
      - [MerklePatriciaProof](contracts/common/lib/MerklePatriciaProof.sol/library.MerklePatriciaProof.md)
      - [PriorityQueue](contracts/common/lib/PriorityQueue.sol/contract.PriorityQueue.md)
      - [RLPEncode](contracts/common/lib/RLPEncode.sol/library.RLPEncode.md)
    - [❱ misc](contracts/common/misc/README.md)
      - [ContractReceiver](contracts/common/misc/ContractReceiver.sol/contract.ContractReceiver.md)
      - [DelegateProxy](contracts/common/misc/DelegateProxy.sol/contract.DelegateProxy.md)
      - [DelegateProxyForwarder](contracts/common/misc/DelegateProxyForwarder.sol/contract.DelegateProxyForwarder.md)
      - [DrainStakeManager](contracts/common/misc/DrainStakeManager.sol/contract.DrainStakeManager.md)
      - [Drainable](contracts/common/misc/Drainable.sol/contract.Drainable.md)
      - [ERCProxy](contracts/common/misc/ERCProxy.sol/interface.ERCProxy.md)
      - [Proxy](contracts/common/misc/Proxy.sol/contract.Proxy.md)
      - [ProxyStorage](contracts/common/misc/ProxyStorage.sol/contract.ProxyStorage.md)
      - [UpgradableProxy](contracts/common/misc/UpgradableProxy.sol/contract.UpgradableProxy.md)
    - [❱ mixin](contracts/common/mixin/README.md)
      - [ChainIdMixin](contracts/common/mixin/ChainIdMixin.sol/contract.ChainIdMixin.md)
      - [GovernanceLockable](contracts/common/mixin/GovernanceLockable.sol/contract.GovernanceLockable.md)
      - [Initializable](contracts/common/mixin/Initializable.sol/contract.Initializable.md)
      - [Lockable](contracts/common/mixin/Lockable.sol/contract.Lockable.md)
      - [OwnableLockable](contracts/common/mixin/OwnableLockable.sol/contract.OwnableLockable.md)
      - [RootChainable](contracts/common/mixin/RootChainable.sol/contract.RootChainable.md)
    - [❱ tokens](contracts/common/tokens/README.md)
      - [ERC20NonTradable](contracts/common/tokens/ERC20NonTradable.sol/contract.ERC20NonTradable.md)
      - [ERC20NonTransferable](contracts/common/tokens/ERC20NonTransferable.sol/contract.ERC20NonTransferable.md)
      - [ERC721PlasmaMintable](contracts/common/tokens/ERC721PlasmaMintable.sol/contract.ERC721PlasmaMintable.md)
      - [MaticWETH](contracts/common/tokens/MaticWETH.sol/contract.MaticWETH.md)
      - [POLTokenMock](contracts/common/tokens/POLTokenMock.sol/contract.POLTokenMock.md)
      - [RootERC721](contracts/common/tokens/RootERC721.sol/contract.RootERC721.md)
      - [TestToken](contracts/common/tokens/TestToken.sol/contract.TestToken.md)
      - [WETH](contracts/common/tokens/WETH.sol/contract.WETH.md)
    - [Registry](contracts/common/Registry.sol/contract.Registry.md)
  - [❱ root](contracts/root/README.md)
    - [❱ depositManager](contracts/root/depositManager/README.md)
      - [IPolygonMigration](contracts/root/depositManager/DepositManager.sol/interface.IPolygonMigration.md)
      - [DepositManager](contracts/root/depositManager/DepositManager.sol/contract.DepositManager.md)
      - [DepositManagerProxy](contracts/root/depositManager/DepositManagerProxy.sol/contract.DepositManagerProxy.md)
      - [DepositManagerHeader](contracts/root/depositManager/DepositManagerStorage.sol/contract.DepositManagerHeader.md)
      - [DepositManagerStorage](contracts/root/depositManager/DepositManagerStorage.sol/contract.DepositManagerStorage.md)
      - [IDepositManager](contracts/root/depositManager/IDepositManager.sol/interface.IDepositManager.md)
    - [❱ predicates](contracts/root/predicates/README.md)
      - [ERC20Predicate](contracts/root/predicates/ERC20Predicate.sol/contract.ERC20Predicate.md)
      - [ERC20PredicateBurnOnly](contracts/root/predicates/ERC20PredicateBurnOnly.sol/contract.ERC20PredicateBurnOnly.md)
      - [ERC721Predicate](contracts/root/predicates/ERC721Predicate.sol/contract.ERC721Predicate.md)
      - [ERC721PredicateBurnOnly](contracts/root/predicates/ERC721PredicateBurnOnly.sol/contract.ERC721PredicateBurnOnly.md)
      - [IPredicate](contracts/root/predicates/IPredicate.sol/interface.IPredicate.md)
      - [PredicateUtils](contracts/root/predicates/IPredicate.sol/contract.PredicateUtils.md)
      - [IErcPredicate](contracts/root/predicates/IPredicate.sol/contract.IErcPredicate.md)
      - [MarketplacePredicate](contracts/root/predicates/MarketplacePredicate.sol/contract.MarketplacePredicate.md)
      - [MintableERC721Predicate](contracts/root/predicates/MintableERC721Predicate.sol/contract.MintableERC721Predicate.md)
      - [TransferWithSigPredicate](contracts/root/predicates/TransferWithSigPredicate.sol/contract.TransferWithSigPredicate.md)
      - [TransferWithSigUtils](contracts/root/predicates/TransferWithSigUtils.sol/library.TransferWithSigUtils.md)
    - [❱ stateSyncer](contracts/root/stateSyncer/README.md)
      - [StateSender](contracts/root/stateSyncer/StateSender.sol/contract.StateSender.md)
    - [❱ withdrawManager](contracts/root/withdrawManager/README.md)
      - [ExitNFT](contracts/root/withdrawManager/ExitNFT.sol/contract.ExitNFT.md)
      - [IWithdrawManager](contracts/root/withdrawManager/IWithdrawManager.sol/contract.IWithdrawManager.md)
      - [WithdrawManager](contracts/root/withdrawManager/WithdrawManager.sol/contract.WithdrawManager.md)
      - [WithdrawManagerProxy](contracts/root/withdrawManager/WithdrawManagerProxy.sol/contract.WithdrawManagerProxy.md)
      - [ExitsDataStructure](contracts/root/withdrawManager/WithdrawManagerStorage.sol/contract.ExitsDataStructure.md)
      - [WithdrawManagerHeader](contracts/root/withdrawManager/WithdrawManagerStorage.sol/contract.WithdrawManagerHeader.md)
      - [WithdrawManagerStorage](contracts/root/withdrawManager/WithdrawManagerStorage.sol/contract.WithdrawManagerStorage.md)
    - [IRootChain](contracts/root/IRootChain.sol/interface.IRootChain.md)
    - [RootChain](contracts/root/RootChain.sol/contract.RootChain.md)
    - [RootChainProxy](contracts/root/RootChainProxy.sol/contract.RootChainProxy.md)
    - [RootChainHeader](contracts/root/RootChainStorage.sol/contract.RootChainHeader.md)
    - [RootChainStorage](contracts/root/RootChainStorage.sol/contract.RootChainStorage.md)
  - [❱ staking](contracts/staking/README.md)
    - [❱ slashing](contracts/staking/slashing/README.md)
      - [ISlashingManager](contracts/staking/slashing/ISlashingManager.sol/contract.ISlashingManager.md)
      - [SlashingManager](contracts/staking/slashing/SlashingManager.sol/contract.SlashingManager.md)
    - [❱ stakeManager](contracts/staking/stakeManager/README.md)
      - [IStakeManager](contracts/staking/stakeManager/IStakeManager.sol/contract.IStakeManager.md)
      - [StakeManager](contracts/staking/stakeManager/StakeManager.sol/contract.StakeManager.md)
      - [StakeManagerExtension](contracts/staking/stakeManager/StakeManagerExtension.sol/contract.StakeManagerExtension.md)
      - [StakeManagerProxy](contracts/staking/stakeManager/StakeManagerProxy.sol/contract.StakeManagerProxy.md)
      - [StakeManagerStorage](contracts/staking/stakeManager/StakeManagerStorage.sol/contract.StakeManagerStorage.md)
      - [StakeManagerStorageExtension](contracts/staking/stakeManager/StakeManagerStorageExtension.sol/contract.StakeManagerStorageExtension.md)
      - [StakingNFT](contracts/staking/stakeManager/StakingNFT.sol/contract.StakingNFT.md)
    - [❱ validatorShare](contracts/staking/validatorShare/README.md)
      - [IValidatorShare](contracts/staking/validatorShare/IValidatorShare.sol/contract.IValidatorShare.md)
      - [ValidatorShare](contracts/staking/validatorShare/ValidatorShare.sol/contract.ValidatorShare.md)
      - [ValidatorShareFactory](contracts/staking/validatorShare/ValidatorShareFactory.sol/contract.ValidatorShareFactory.md)
      - [ValidatorShareProxy](contracts/staking/validatorShare/ValidatorShareProxy.sol/contract.ValidatorShareProxy.md)
    - [IStakeManagerEventsHub](contracts/staking/EventsHub.sol/contract.IStakeManagerEventsHub.md)
    - [EventsHub](contracts/staking/EventsHub.sol/contract.EventsHub.md)
    - [EventsHubProxy](contracts/staking/EventsHubProxy.sol/contract.EventsHubProxy.md)
    - [IStakeManagerLocal](contracts/staking/StakingInfo.sol/contract.IStakeManagerLocal.md)
    - [StakingInfo](contracts/staking/StakingInfo.sol/contract.StakingInfo.md)
  - [❱ test](contracts/test/README.md)
    - [❱ Proxy](contracts/test/Proxy/README.md)
      - [ProxyTestImpl](contracts/test/Proxy/ProxyTestImpl.sol/contract.ProxyTestImpl.md)
      - [ProxyTestImplStorageLayoutChange](contracts/test/Proxy/ProxyTestImplStorageLayoutChange.sol/contract.ProxyTestImplStorageLayoutChange.md)
    - [ContractWithFallback](contracts/test/ContractActor.sol/contract.ContractWithFallback.md)
    - [ContractWithoutFallback](contracts/test/ContractActor.sol/contract.ContractWithoutFallback.md)
    - [ContractWitRevertingFallback](contracts/test/ContractActor.sol/contract.ContractWitRevertingFallback.md)
    - [GovernanceLockableTest](contracts/test/GovernanceLockableTest.sol/contract.GovernanceLockableTest.md)
    - [MarketplacePredicateTest](contracts/test/MarketplacePredicateTest.sol/contract.MarketplacePredicateTest.md)
    - [PolygonMigrationTest](contracts/test/PolygonMigrationTest.sol/contract.PolygonMigrationTest.md)
    - [StakeManagerTest](contracts/test/StakeManagerTest.sol/contract.StakeManagerTest.md)
    - [StakeManagerTestable](contracts/test/StakeManagerTestable.sol/contract.StakeManagerTestable.md)
    - [TestMRC20](contracts/test/TestMaticChildERC20.sol/contract.TestMRC20.md)
    - [ValidatorShareTest](contracts/test/ValidatorShareTest.sol/contract.ValidatorShareTest.md)
  - [Migrations](contracts/Migrations.sol/contract.Migrations.md)
