# Governance
[Git Source](https://github.com/maticnetwork/contracts/blob/155f729fd8db0676297384375468d4d45b8aa44e/contracts/common/governance/Governance.sol)

**Inherits:**
[ProxyStorage](/contracts/common/misc/ProxyStorage.sol/contract.ProxyStorage.md), [IGovernance](/contracts/common/governance/IGovernance.sol/interface.IGovernance.md)


## Functions
### update


```solidity
function update(address target, bytes memory data) public onlyOwner;
```

