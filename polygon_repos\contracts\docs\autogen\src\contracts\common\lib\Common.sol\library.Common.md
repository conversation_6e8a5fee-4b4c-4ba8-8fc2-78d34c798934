# Common
[Git Source](https://github.com/maticnetwork/contracts/blob/155f729fd8db0676297384375468d4d45b8aa44e/contracts/common/lib/Common.sol)


## Functions
### getV


```solidity
function getV(bytes memory v, uint16 chainId) public pure returns (uint8);
```

### isContract


```solidity
function isContract(address _addr) public view returns (bool);
```

### toUint8


```solidity
function toUint8(bytes memory _arg) public pure returns (uint8);
```

### toUint16


```solidity
function toUint16(bytes memory _arg) public pure returns (uint16);
```

