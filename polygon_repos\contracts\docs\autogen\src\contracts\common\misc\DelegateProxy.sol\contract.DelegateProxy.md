# DelegateProxy
[Git Source](https://github.com/maticnetwork/contracts/blob/155f729fd8db0676297384375468d4d45b8aa44e/contracts/common/misc/DelegateProxy.sol)

**Inherits:**
[ERCProxy](/contracts/common/misc/ERCProxy.sol/interface.ERCProxy.md), [DelegateProxyForwarder](/contracts/common/misc/DelegateProxyForwarder.sol/contract.DelegateProxyForwarder.md)


## Functions
### proxyType


```solidity
function proxyType() external pure returns (uint256 proxyTypeId);
```

### implementation


```solidity
function implementation() external view returns (address);
```

