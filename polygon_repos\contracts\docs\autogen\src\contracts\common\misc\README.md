

# Contents
- [ContractReceiver](ContractReceiver.sol/contract.ContractReceiver.md)
- [DelegateProxy](DelegateProxy.sol/contract.DelegateProxy.md)
- [DelegateProxyForwarder](DelegateProxyForwarder.sol/contract.DelegateProxyForwarder.md)
- [DrainStakeManager](DrainStakeManager.sol/contract.DrainStakeManager.md)
- [Drainable](Drainable.sol/contract.Drainable.md)
- [ERCProxy](ERCProxy.sol/interface.ERCProxy.md)
- [Proxy](Proxy.sol/contract.Proxy.md)
- [ProxyStorage](ProxyStorage.sol/contract.ProxyStorage.md)
- [UpgradableProxy](UpgradableProxy.sol/contract.UpgradableProxy.md)
