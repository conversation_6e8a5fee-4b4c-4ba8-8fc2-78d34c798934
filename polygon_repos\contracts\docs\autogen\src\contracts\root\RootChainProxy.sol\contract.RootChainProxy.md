# RootChainProxy
[Git Source](https://github.com/maticnetwork/contracts/blob/155f729fd8db0676297384375468d4d45b8aa44e/contracts/root/RootChainProxy.sol)

**Inherits:**
[Proxy](/contracts/common/misc/Proxy.sol/contract.Proxy.md), [RootChainStorage](/contracts/root/RootChainStorage.sol/contract.RootChainStorage.md)


## Functions
### constructor


```solidity
constructor(address _proxyTo, address _registry, string memory _heimdallId) public Proxy(_proxyTo);
```

