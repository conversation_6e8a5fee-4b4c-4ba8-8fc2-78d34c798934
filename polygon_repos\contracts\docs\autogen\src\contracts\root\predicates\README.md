

# Contents
- [ERC20Predicate](ERC20Predicate.sol/contract.ERC20Predicate.md)
- [ERC20PredicateBurnOnly](ERC20PredicateBurnOnly.sol/contract.ERC20PredicateBurnOnly.md)
- [ERC721Predicate](ERC721Predicate.sol/contract.ERC721Predicate.md)
- [ERC721PredicateBurnOnly](ERC721PredicateBurnOnly.sol/contract.ERC721PredicateBurnOnly.md)
- [IPredicate](IPredicate.sol/interface.IPredicate.md)
- [PredicateUtils](IPredicate.sol/contract.PredicateUtils.md)
- [IErcPredicate](IPredicate.sol/contract.IErcPredicate.md)
- [MarketplacePredicate](MarketplacePredicate.sol/contract.MarketplacePredicate.md)
- [MintableERC721Predicate](MintableERC721Predicate.sol/contract.MintableERC721Predicate.md)
- [TransferWithSigPredicate](TransferWithSigPredicate.sol/contract.TransferWithSigPredicate.md)
- [TransferWithSigUtils](TransferWithSigUtils.sol/library.TransferWithSigUtils.md)
