# ValidatorShareProxy
[Git Source](https://github.com/maticnetwork/contracts/blob/155f729fd8db0676297384375468d4d45b8aa44e/contracts/staking/validatorShare/ValidatorShareProxy.sol)

**Inherits:**
[UpgradableProxy](/contracts/common/misc/UpgradableProxy.sol/contract.UpgradableProxy.md)


## Functions
### constructor


```solidity
constructor(address _registry) public UpgradableProxy(_registry);
```

### loadImplementation


```solidity
function loadImplementation() internal view returns (address);
```

