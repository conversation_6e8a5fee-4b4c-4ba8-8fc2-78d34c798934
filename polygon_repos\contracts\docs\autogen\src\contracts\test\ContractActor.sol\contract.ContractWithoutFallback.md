# ContractWithoutFallback
[Git Source](https://github.com/maticnetwork/contracts/blob/155f729fd8db0676297384375468d4d45b8aa44e/contracts/test/ContractActor.sol)


## Functions
### deposit


```solidity
function deposit(address depositManager, address token, uint256 amount) public;
```

### startExitWithDepositedTokens


```solidity
function startExitWithDepositedTokens(address payable withdrawManager, uint256 depositId, address token, uint256 amountOrToken) public payable;
```

