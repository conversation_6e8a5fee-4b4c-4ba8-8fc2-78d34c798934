{"name": "matic-protocol", "description": "New repo using Hardhat and Foundry", "version": "0.3.2", "main": "hardhat.config.cjs", "type": "module", "directories": {"lib": "lib", "test": "test"}, "scripts": {"build": "forge build", "test:hardhat": "npx hardhat test", "test:foundry": "forge test", "test:ci": "scripts/run-test.sh", "testrpc": "ganache --chain.hardfork istanbul --wallet.mnemonic 'clock radar mass judge dismiss just intact mind resemble fringe diary casino' -p 8545 --gasLimit ******** --gasPrice 0 --chain.allowUnlimitedContractSize --accounts 200", "template:process": "node scripts/process-templates.cjs", "coverage": "LOCAL_NETWORK=true npx hardhat coverage --solcoverjs .solcover.cjs", "bor:simulate": "cd test-bor-docker && bash run-docker.sh", "bor:stop": "cd test-bor-docker && bash stop-docker.sh", "bor:clean": "cd test-bor-docker && bash clean.sh"}, "keywords": [], "author": "<PERSON><PERSON> <jd<PERSON><PERSON>@gmail.com>, <PERSON> <<EMAIL>", "license": "MIT", "config": {"mnemonics": "clock radar mass judge dismiss just intact mind resemble fringe diary casino"}, "devDependencies": {"@openzeppelin/test-helpers": "^0.5.16", "@nomicfoundation/hardhat-toolbox": "^2.0.0", "@nomiclabs/hardhat-web3": "2.0.0", "esm": "3.2.25", "bip39": "^2.5.0", "dotenv": "^16.3.1", "ethereumjs-wallet": "1.0.2", "eth-sig-util": "^2.1.1", "prettier": "^3.0.3", "prettier-plugin-solidity": "1.1.3", "solhint": "^3.6.2", "solidity-coverage": "0.8.4", "eslint": "^8.55.0"}, "dependencies": {"ethereumjs-block": "^2.2.2", "ethereumjs-tx": "^1.3.7", "ethereumjs-util": "^6.0.0", "ganache": "7.9.2", "import-toml": "^1.0.0", "merkle-patricia-tree": "^2.3.2", "mocha": "10.2.0", "nunjucks": "^3.2.0", "openzeppelin-solidity": "2.2.0", "solidity-rlp": "^2.0.0"}, "eslintConfig": {"env": {"browser": false, "es2022": true, "mocha": true, "node": true}, "parserOptions": {"sourceType": "module"}, "globals": {"web3": true}}, "mocha": {"require": "hardhat/register", "timeout": 40000000}}