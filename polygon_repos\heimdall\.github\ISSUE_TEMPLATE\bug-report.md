---
name: Bug Report
about: Create a report to help us squash bugs!

---
<!--
Please fill in as much of the template below as you can.

Be ready for followup questions, and please respond in a timely
manner. We might ask you to provide additional logs and data (heimdall & bor).
-->

**Heimdall version** (use `heimdall version` or `git rev-parse --verify HEAD` if installed from source):


**Environment**:
- **OS** (e.g. from /etc/os-release):
- **Install tools**:
- **Others**:


**What happened**:


**What you expected to happen**:


**Have you tried the latest version**: yes/no

**How to reproduce it** (as minimally and precisely as possible):

**Logs (paste a small part showing an error (< 10 lines) or link a pastebin, gist, etc. containing more of the log file)**:

**Config (you can paste only the changes you've made)**:

**node command runtime flags**:

**`/dump_consensus_state` output for consensus bugs**

**Anything else we need to know**: