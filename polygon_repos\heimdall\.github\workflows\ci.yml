name: CI

on:
  push:
    branches:
      - "master"
      - "qa"
      - "develop"
  pull_request:
    branches:
      - "**"
    types: [opened, synchronize]

jobs:
  build:
    name: build
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Install Go
        uses: actions/setup-go@v5
        with:
          go-version: 1.23.x

      - name: "Build binaries"
        run: make build

  lint:
    name: lint
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Install Go
        uses: actions/setup-go@v5
        with:
          go-version: 1.23.x

      - name: "Run linter"
        run: make lint

  test:
    name: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Install Go
        uses: actions/setup-go@v5
        with:
          go-version: 1.23.x

      - name: "Run tests"
        run: make tests

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v2
        with:
          file: ./cover.out


  e2e-tests:
    if: (github.event.action != 'closed' || github.event.pull_request.merged == true)
    strategy:
      matrix:
        os: [ ubuntu-22.04 ] # list of os: https://github.com/actions/virtual-environments
    runs-on: ${{ matrix.os }}
    steps:
        - uses: actions/checkout@v4
          with:
            path: heimdall

        - uses: actions/setup-go@v5
          with:
            go-version: 1.23.x

        - name: Checkout matic-cli
          uses: actions/checkout@v4
          with:
            repository: maticnetwork/matic-cli
            ref: master
            path: matic-cli

        - name: Install dependencies on Linux
          if: runner.os == 'Linux'
          run: |
            sudo apt update
            sudo apt install build-essential
            curl https://raw.githubusercontent.com/creationix/nvm/master/install.sh | bash
            sudo apt install jq curl

        - uses: actions/setup-node@v4
          with:
            node-version: '18.19.0'
            cache: 'npm'
            cache-dependency-path: |
              matic-cli/package-lock.json
              matic-cli/devnet/code/contracts/package-lock.json
              matic-cli/devnet/code/genesis-contracts/package-lock.json
              matic-cli/devnet/code/genesis-contracts/matic-contracts/package-lock.json

        - name: Install solc-select
          run: |
            sudo apt update
            sudo apt install python3 python3-pip -y
            sudo ln -sf /usr/bin/python3 /usr/bin/python
            pip install solc-select

        - name: Install Solidity Version
          run: |
            solc-select install 0.5.17
            solc-select install 0.6.12
            solc-select use 0.5.17
            solc --version

        - name: Install Foundry
          uses: foundry-rs/foundry-toolchain@v1

        - name: Bootstrap devnet
          run: |
            cd matic-cli
            npm install --prefer-offline --no-audit --progress=false
            mkdir devnet
            cd devnet
            ../bin/matic-cli.js setup devnet -c ../../heimdall/.github/matic-cli-config.yml

        - name: Launch devnet
          run: |
            cd matic-cli/devnet
            bash ../util-scripts/docker/devnet_setup.sh
            cd -
            timeout 2m bash heimdall/integration-tests/bor_health.sh

        - name: Run smoke tests
          run: |
            echo "Deposit 100 matic for each account to bor network"
            cd matic-cli/devnet/devnet
            SCRIPT_ADDRESS=$(jq -r '.[0].address' signer-dump.json)
            SCRIPT_PRIVATE_KEY=$(jq -r '.[0].priv_key' signer-dump.json)
            cd ../code/pos-contracts
            CONTRACT_ADDRESS=$(jq -r .root.tokens.MaticToken contractAddresses.json)
            forge script scripts/matic-cli-scripts/Deposit.s.sol:MaticDeposit --rpc-url http://localhost:9545 --private-key $SCRIPT_PRIVATE_KEY --broadcast --sig "run(address,address,uint256)" $SCRIPT_ADDRESS $CONTRACT_ADDRESS 100000000000000000000
            cd ../../../..
            timeout 60m bash heimdall/integration-tests/smoke_test.sh

        - name: Resolve absolute path for logs
          id: pathfix
          run: |
            echo "ABS_LOG_PATH=$(realpath matic-cli/devnet/logs)" >> $GITHUB_ENV

        - name: Upload logs
          if: always()
          uses: PaloAltoNetworks/upload-secure-artifact@main
          with:
            name: logs_${{ github.run_id }}
            path: ${{ env.ABS_LOG_PATH }}

        - name: Package code and chain data
          if: always()
          run: |
            cd matic-cli/devnet
            docker compose down --remove-orphans
            cd -
            mkdir -p ${{ github.run_id }}/matic-cli
            sudo mv heimdall ${{ github.run_id }}
            sudo mv matic-cli/devnet ${{ github.run_id }}/matic-cli
            sudo tar --warning=no-file-changed --exclude='.git' -czf code.tar.gz ${{ github.run_id }}

        - name: Upload code and chain data
          if: always()
          uses: PaloAltoNetworks/upload-secure-artifact@main
          with:
            name: code_${{ github.run_id }}
            path: code.tar.gz
