project_name: heimdall

release:
  disable: true
  draft: true
  prerelease: auto

builds:
  - &xbuildd
    id: heimdalld-darwin-amd64
    main: ./cmd/heimdalld
    binary: heimdalld
    goos:
      - darwin
    goarch:
      - amd64
    tags:
      - netgo
    env:
      - CC=o64-clang
      - CXX=o64-clang++
    ldflags: &ldflags
      - -s -w
      - -X github.com/maticnetwork/heimdall/version.Name=heimdall
      - -X github.com/maticnetwork/heimdall/version.ServerName=heimdalld
      - -X github.com/maticnetwork/heimdall/version.ClientName=heimdallcli
      - -X github.com/maticnetwork/heimdall/version.Version={{.Version}}
      - -X github.com/maticnetwork/heimdall/version.Commit={{.Commit}}
      - -X github.com/cosmos/cosmos-sdk/version.Name=heimdall
      - -X github.com/cosmos/cosmos-sdk/version.ServerName=heimdalld
      - -X github.com/cosmos/cosmos-sdk/version.ClientName=heimdallcli
      - -X github.com/cosmos/cosmos-sdk/version.Version={{.Version}}
      - -X github.com/cosmos/cosmos-sdk/version.Commit={{.Commit}}
  
  - <<: *xbuildd
    id: heimdalld-darwin-arm64
    goos:
      - darwin
    goarch:
      - arm64
    env:
      - CC=oa64-clang
      - CXX=oa64-clang++
  
  - <<: *xbuildd
    id: heimdalld-linux-amd64
    goos:
      - linux
    goarch:
      - amd64
    env:
      - CC=gcc
      - CXX=g++
    ldflags:
      # We need to build a static binary because we are building in a glibc based system and running in a musl container
      - -s -w -extldflags "-static"
      - -X github.com/maticnetwork/heimdall/version.Name=heimdall
      - -X github.com/maticnetwork/heimdall/version.ServerName=heimdalld
      - -X github.com/maticnetwork/heimdall/version.ClientName=heimdallcli
      - -X github.com/maticnetwork/heimdall/version.Version={{.Version}}
      - -X github.com/maticnetwork/heimdall/version.Commit={{.Commit}}
      - -X github.com/cosmos/cosmos-sdk/version.Name=heimdall
      - -X github.com/cosmos/cosmos-sdk/version.ServerName=heimdalld
      - -X github.com/cosmos/cosmos-sdk/version.ClientName=heimdallcli
      - -X github.com/cosmos/cosmos-sdk/version.Version={{.Version}}
      - -X github.com/cosmos/cosmos-sdk/version.Commit={{.Commit}}

  - <<: *xbuildd
    id: heimdalld-linux-arm64
    goos:
      - linux
    goarch:
      - arm64
    env:
      - CC=aarch64-linux-gnu-gcc
      - CXX=aarch64-linux-gnu-g++
    ldflags:
      # We need to build a static binary because we are building in a glibc based system and running in a musl container
      - -s -w -extldflags "-static"
      - -X github.com/maticnetwork/heimdall/version.Name=heimdall
      - -X github.com/maticnetwork/heimdall/version.ServerName=heimdalld
      - -X github.com/maticnetwork/heimdall/version.ClientName=heimdallcli
      - -X github.com/maticnetwork/heimdall/version.Version={{.Version}}
      - -X github.com/maticnetwork/heimdall/version.Commit={{.Commit}}
      - -X github.com/cosmos/cosmos-sdk/version.Name=heimdall
      - -X github.com/cosmos/cosmos-sdk/version.ServerName=heimdalld
      - -X github.com/cosmos/cosmos-sdk/version.ClientName=heimdallcli
      - -X github.com/cosmos/cosmos-sdk/version.Version={{.Version}}
      - -X github.com/cosmos/cosmos-sdk/version.Commit={{.Commit}}

  # heimdallcli

  - &xbuildcli
    id: heimdallcli-darwin-amd64
    main: ./cmd/heimdallcli
    binary: heimdallcli
    goos:
      - darwin
    goarch:
      - amd64
    tags:
      - netgo
    env:
      - CC=o64-clang
      - CXX=o64-clang++
    ldflags:
      - -s -w
      - -X github.com/maticnetwork/heimdall/version.Name=heimdall
      - -X github.com/maticnetwork/heimdall/version.ServerName=heimdalld
      - -X github.com/maticnetwork/heimdall/version.ClientName=heimdallcli
      - -X github.com/maticnetwork/heimdall/version.Version={{.Version}}
      - -X github.com/maticnetwork/heimdall/version.Commit={{.Commit}}
      - -X github.com/cosmos/cosmos-sdk/version.Name=heimdall
      - -X github.com/cosmos/cosmos-sdk/version.ServerName=heimdalld
      - -X github.com/cosmos/cosmos-sdk/version.ClientName=heimdallcli
      - -X github.com/cosmos/cosmos-sdk/version.Version={{.Version}}
      - -X github.com/cosmos/cosmos-sdk/version.Commit={{.Commit}}
  
  - <<: *xbuildcli
    id: heimdallcli-darwin-arm64
    goos:
      - darwin
    goarch:
      - arm64
    env:
      - CC=oa64-clang
      - CXX=oa64-clang++
  
  - <<: *xbuildcli
    id: heimdallcli-linux-amd64
    goos:
      - linux
    goarch:
      - amd64
    env:
      - CC=gcc
      - CXX=g++
    ldflags:
      # We need to build a static binary because we are building in a glibc based system and running in a musl container
      - -linkmode external -extldflags "-static"
      - -X github.com/maticnetwork/heimdall/version.Name=heimdall
      - -X github.com/maticnetwork/heimdall/version.ServerName=heimdalld
      - -X github.com/maticnetwork/heimdall/version.ClientName=heimdallcli
      - -X github.com/maticnetwork/heimdall/version.Version={{.Version}}
      - -X github.com/maticnetwork/heimdall/version.Commit={{.Commit}}
      - -X github.com/cosmos/cosmos-sdk/version.Name=heimdall
      - -X github.com/cosmos/cosmos-sdk/version.ServerName=heimdalld
      - -X github.com/cosmos/cosmos-sdk/version.ClientName=heimdallcli
      - -X github.com/cosmos/cosmos-sdk/version.Version={{.Version}}
      - -X github.com/cosmos/cosmos-sdk/version.Commit={{.Commit}}

  - <<: *xbuildcli
    id: heimdallcli-linux-arm64
    goos:
      - linux
    goarch:
      - arm64
    env:
      - CC=aarch64-linux-gnu-gcc
      - CXX=aarch64-linux-gnu-g++
    ldflags:
      # We need to build a static binary because we are building in a glibc based system and running in a musl container
      - -linkmode external -extldflags "-static"
      - -X github.com/maticnetwork/heimdall/version.Name=heimdall
      - -X github.com/maticnetwork/heimdall/version.ServerName=heimdalld
      - -X github.com/maticnetwork/heimdall/version.ClientName=heimdallcli
      - -X github.com/maticnetwork/heimdall/version.Version={{.Version}}
      - -X github.com/maticnetwork/heimdall/version.Commit={{.Commit}}
      - -X github.com/cosmos/cosmos-sdk/version.Name=heimdall
      - -X github.com/cosmos/cosmos-sdk/version.ServerName=heimdalld
      - -X github.com/cosmos/cosmos-sdk/version.ClientName=heimdallcli
      - -X github.com/cosmos/cosmos-sdk/version.Version={{.Version}}
      - -X github.com/cosmos/cosmos-sdk/version.Commit={{.Commit}}

nfpms:
  - vendor: 0xPolygon
    homepage: https://polygon.technology
    maintainer: Polygon Team <<EMAIL>>
    description: Polygon Blockchain
    license: GPLv3 LGPLv3

    bindir: /usr/local/bin

    formats:
      - apk
      - deb
      - rpm

    contents:
      - src: builder/files/heimdalld.service
        dst: /lib/systemd/system/heimdalld.service
        type: config
      - src: builder/files/genesis-mainnet-v1.json
        dst: /etc/heimdall/genesis-mainnet-v1.json
        type: config
      - src: builder/files/genesis-testnet-v4.json
        dst: /etc/heimdall/genesis-testnet-v4.json
        type: config
      - dst: /var/lib/heimdall
        type: dir
        file_info:
          mode: 0777

    scripts:
      postinstall: builder/files/heimdall-post-install.sh

snapshot:
  name_template: "{{ .Tag }}.next"

dockers:
  - image_templates:
      - 0xpolygon/{{ .ProjectName }}:{{ .Version }}-amd64
    dockerfile: Dockerfile.release
    use: buildx
    goarch: amd64
    ids:
      - heimdalld-linux-amd64
      - heimdallcli-linux-amd64
    build_flag_templates:
      - --platform=linux/amd64
    extra_files:
      - docker/entrypoint.sh
      - builder/files/genesis-mainnet-v1.json
      - builder/files/genesis-testnet-v4.json
  
  - image_templates:
      - 0xpolygon/{{ .ProjectName }}:{{ .Version }}-arm64
    dockerfile: Dockerfile.release
    use: buildx
    goarch: arm64
    ids:
      - heimdalld-linux-arm64
      - heimdallcli-linux-arm64
    build_flag_templates:
      - --platform=linux/arm64/v8
    extra_files:
      - docker/entrypoint.sh
      - builder/files/genesis-mainnet-v1.json
      - builder/files/genesis-testnet-v4.json

docker_manifests:
  - name_template: 0xpolygon/{{ .ProjectName }}:{{ .Version }}
    image_templates:
      - 0xpolygon/{{ .ProjectName }}:{{ .Version }}-amd64
      - 0xpolygon/{{ .ProjectName }}:{{ .Version }}-arm64

  - name_template: 0xpolygon/{{ .ProjectName }}:latest
    image_templates:
    - 0xpolygon/{{ .ProjectName }}:{{ .Version }}-amd64
    - 0xpolygon/{{ .ProjectName }}:{{ .Version }}-arm64

announce:
  slack:
    enabled: true
    # The name of the channel that the user selected as a destination for webhook messages.
    channel: '#code-releases'
