// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/maticnetwork/heimdall/auth/types (interfaces: NodeQuerier)

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockNodeQuerier is a mock of NodeQuerier interface.
type MockNodeQuerier struct {
	ctrl     *gomock.Controller
	recorder *MockNodeQuerierMockRecorder
}

// MockNodeQuerierMockRecorder is the mock recorder for MockNodeQuerier.
type MockNodeQuerierMockRecorder struct {
	mock *MockNodeQuerier
}

// NewMockNodeQuerier creates a new mock instance.
func NewMockNodeQuerier(ctrl *gomock.Controller) *MockNodeQuerier {
	mock := &MockNodeQuerier{ctrl: ctrl}
	mock.recorder = &MockNodeQuerierMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNodeQuerier) EXPECT() *MockNodeQuerierMockRecorder {
	return m.recorder
}

// QueryWithData mocks base method.
func (m *MockNodeQuerier) QueryWithData(arg0 string, arg1 []byte) ([]byte, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryWithData", arg0, arg1)
	ret0, _ := ret[0].([]byte)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// QueryWithData indicates an expected call of QueryWithData.
func (mr *MockNodeQuerierMockRecorder) QueryWithData(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryWithData", reflect.TypeOf((*MockNodeQuerier)(nil).QueryWithData), arg0, arg1)
}
