[{"constant": false, "inputs": [{"internalType": "uint256", "name": "validatorId", "type": "uint256"}, {"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint256", "name": "totalStaked", "type": "uint256"}], "name": "logDelReStaked", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "validatorId", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "auctionAmount", "type": "uint256"}], "name": "logStartAuction", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint256", "name": "fee", "type": "uint256"}], "name": "logClaimFee", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"internalType": "uint256", "name": "validatorId", "type": "uint256"}], "name": "getValidatorContractAddress", "outputs": [{"internalType": "address", "name": "ValidatorContract", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "validatorId", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "total", "type": "uint256"}], "name": "logReStaked", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "signer", "type": "address"}, {"internalType": "bytes", "name": "signer<PERSON><PERSON><PERSON>", "type": "bytes"}, {"internalType": "uint256", "name": "validatorId", "type": "uint256"}, {"internalType": "uint256", "name": "activationEpoch", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "total", "type": "uint256"}], "name": "logStaked", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "getAccountStateRoot", "outputs": [{"internalType": "bytes32", "name": "accountStateRoot", "type": "bytes32"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "validatorId", "type": "uint256"}], "name": "logStakeUpdate", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint256", "name": "validatorId", "type": "uint256"}, {"internalType": "uint256", "name": "deactivationEpoch", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "logUnstakeInit", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"internalType": "uint256", "name": "validatorId", "type": "uint256"}], "name": "getStakerDetails", "outputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "reward", "type": "uint256"}, {"internalType": "uint256", "name": "activationEpoch", "type": "uint256"}, {"internalType": "uint256", "name": "deactivationEpoch", "type": "uint256"}, {"internalType": "address", "name": "signer", "type": "address"}, {"internalType": "uint256", "name": "_status", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "registry", "outputs": [{"internalType": "contract Registry", "name": "", "type": "address"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "validatorId", "type": "uint256"}, {"internalType": "uint256", "name": "exitEpoch", "type": "uint256"}, {"internalType": "address", "name": "signer", "type": "address"}], "name": "logJailed", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "newDynasty", "type": "uint256"}, {"internalType": "uint256", "name": "oldDynasty", "type": "uint256"}], "name": "logDynastyValueChange", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "newProposerBon<PERSON>", "type": "uint256"}, {"internalType": "uint256", "name": "oldProposerBon<PERSON>", "type": "uint256"}], "name": "logProposerBonusChange", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint256", "name": "fee", "type": "uint256"}], "name": "logTopUpFee", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint256", "name": "validatorId", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "total", "type": "uint256"}], "name": "logUnstaked", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "validatorId", "type": "uint256"}, {"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint256", "name": "rewards", "type": "uint256"}, {"internalType": "uint256", "name": "tokens", "type": "uint256"}], "name": "logDelClaimRewards", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "validatorId", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "totalAmount", "type": "uint256"}], "name": "logClaimRewards", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "newReward", "type": "uint256"}, {"internalType": "uint256", "name": "oldReward", "type": "uint256"}], "name": "logRewardUpdate", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "validatorId", "type": "uint256"}, {"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "bytes", "name": "signer<PERSON><PERSON><PERSON>", "type": "bytes"}], "name": "logSignerChange", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "validatorId", "type": "uint256"}, {"internalType": "address", "name": "signer", "type": "address"}], "name": "logUnJailed", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "validatorId", "type": "uint256"}, {"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "tokens", "type": "uint256"}], "name": "logShareMinted", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "validatorId", "type": "uint256"}, {"internalType": "uint256", "name": "newCommissionRate", "type": "uint256"}, {"internalType": "uint256", "name": "oldCommissionRate", "type": "uint256"}], "name": "logUpdateCommissionRate", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"internalType": "uint256", "name": "validatorId", "type": "uint256"}], "name": "totalValidatorStake", "outputs": [{"internalType": "uint256", "name": "validatorStake", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "validatorId", "type": "uint256"}, {"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "logDelUnstaked", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "newValidatorId", "type": "uint256"}, {"internalType": "uint256", "name": "oldValidatorId", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "logConfirmAuction", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "validatorNonce", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "validatorId", "type": "uint256"}, {"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "tokens", "type": "uint256"}], "name": "logShareBurned", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "newThreshold", "type": "uint256"}, {"internalType": "uint256", "name": "oldThreshold", "type": "uint256"}], "name": "logThresholdChange", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": false, "inputs": [{"internalType": "uint256", "name": "nonce", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "logSlashed", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_registry", "type": "address"}], "payable": false, "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "signer", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "validatorId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "nonce", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "activationEpoch", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "total", "type": "uint256"}, {"indexed": false, "internalType": "bytes", "name": "signer<PERSON><PERSON><PERSON>", "type": "bytes"}], "name": "Staked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "validatorId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "total", "type": "uint256"}], "name": "Unstaked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "validatorId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "nonce", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "deactivationEpoch", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "UnstakeInit", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "validatorId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "nonce", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"indexed": true, "internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "address"}, {"indexed": false, "internalType": "bytes", "name": "signer<PERSON><PERSON><PERSON>", "type": "bytes"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "validatorId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "total", "type": "uint256"}], "name": "ReStaked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "validatorId", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "exitEpoch", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "signer", "type": "address"}], "name": "Jailed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "validatorId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "signer", "type": "address"}], "name": "UnJailed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "nonce", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "Slashed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "newThreshold", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "oldThreshold", "type": "uint256"}], "name": "ThresholdChange", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "newDynasty", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "oldDynasty", "type": "uint256"}], "name": "DynastyValueChange", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "newProposerBon<PERSON>", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "oldProposerBon<PERSON>", "type": "uint256"}], "name": "ProposerBonusChange", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "newReward", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "oldReward", "type": "uint256"}], "name": "RewardUpdate", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "validatorId", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "nonce", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "newAmount", "type": "uint256"}], "name": "StakeUpdate", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "validatorId", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "totalAmount", "type": "uint256"}], "name": "ClaimRewards", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "validatorId", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "auctionAmount", "type": "uint256"}], "name": "StartAuction", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "newValidatorId", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "oldValidatorId", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "ConfirmAuction", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "fee", "type": "uint256"}], "name": "TopUpFee", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "fee", "type": "uint256"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "validatorId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "tokens", "type": "uint256"}], "name": "ShareMinted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "validatorId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "tokens", "type": "uint256"}], "name": "ShareBurned", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "validatorId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "rewards", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "tokens", "type": "uint256"}], "name": "DelClaimRewards", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "validatorId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "totalStaked", "type": "uint256"}], "name": "DelReStaked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "validatorId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "DelUnstaked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "validatorId", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "newCommissionRate", "type": "uint256"}, {"indexed": true, "internalType": "uint256", "name": "oldCommissionRate", "type": "uint256"}], "name": "UpdateCommissionRate", "type": "event"}]