[{"constant": true, "inputs": [{"name": "span", "type": "uint256"}], "name": "getSpan", "outputs": [{"name": "number", "type": "uint256"}, {"name": "startBlock", "type": "uint256"}, {"name": "endBlock", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "vote", "type": "bytes"}, {"name": "sigs", "type": "bytes"}, {"name": "txBytes", "type": "bytes"}, {"name": "proof", "type": "bytes"}], "name": "commitSpan", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}, {"constant": true, "inputs": [], "name": "currentSpanNumber", "outputs": [{"name": "", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "getNextSpan", "outputs": [{"name": "number", "type": "uint256"}, {"name": "startBlock", "type": "uint256"}, {"name": "endBlock", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "getInitialValidators", "outputs": [{"name": "", "type": "address[]"}, {"name": "", "type": "uint256[]"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "getCurrentSpan", "outputs": [{"name": "number", "type": "uint256"}, {"name": "startBlock", "type": "uint256"}, {"name": "endBlock", "type": "uint256"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": true, "inputs": [], "name": "getValidators", "outputs": [{"name": "", "type": "address[]"}, {"name": "", "type": "uint256[]"}], "payable": false, "stateMutability": "view", "type": "function"}, {"constant": false, "inputs": [{"name": "vote", "type": "bytes"}, {"name": "sigs", "type": "bytes"}, {"name": "txBytes", "type": "bytes"}, {"name": "proof", "type": "bytes"}], "name": "validateValidatorSet", "outputs": [], "payable": false, "stateMutability": "nonpayable", "type": "function"}]