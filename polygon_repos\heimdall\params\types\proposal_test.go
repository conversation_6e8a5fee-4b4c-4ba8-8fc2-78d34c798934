package types

import (
	"testing"

	"github.com/stretchr/testify/require"
)

func TestParameterChangeProposal(t *testing.T) {
	t.<PERSON>()

	pc1 := NewParamChange("sub", "foo", "baz")
	pcp := NewParameterChangeProposal("test title", "test description", []ParamChange{pc1})

	require.Equal(t, "test title", pcp.GetTitle())
	require.Equal(t, "test description", pcp.GetDescription())
	require.Equal(t, Router<PERSON>ey, pcp.ProposalRoute())
	require.Equal(t, ProposalTypeChange, pcp.ProposalType())
	require.Nil(t, pcp.ValidateBasic())
}
