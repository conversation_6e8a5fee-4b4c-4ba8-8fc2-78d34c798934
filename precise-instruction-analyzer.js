#!/usr/bin/env node

/**
 * 🔍 ТОЧНЫЙ АНАЛИЗАТОР ВСЕХ ИНСТРУКЦИЙ
 * 
 * Анализирует:
 * 1. Все 24 инструкции по порядку
 * 2. Точное использование каждого адреса
 * 3. Какие 4 адреса не сжимаются
 * 4. Эффективность существующих ALT таблиц
 */

const { 
    Connection, 
    PublicKey, 
    Keypair
} = require('@solana/web3.js');
const bs58 = require('bs58');

// 🔥 ИМПОРТИРУЕМ ПОЛНУЮ СИСТЕМУ
const CompleteFlashLoanWithLiquidity = require('./complete-flash-loan-with-liquidity.js');

require('dotenv').config();

class PreciseInstructionAnalyzer {
    constructor() {
        // 🌐 CONNECTION
        this.connection = new Connection(
            process.env.QUICKNODE_RPC_URL || process.env.SOLANA_RPC_URL,
            'confirmed'
        );
        
        // 🔑 WALLET
        this.wallet = null;
        
        // 🔥 ПОЛНАЯ СИСТЕМА
        this.flashLoanSystem = null;
        
        console.log('🔍 PRECISE INSTRUCTION ANALYZER ИНИЦИАЛИЗИРОВАН');
    }

    /**
     * 🔑 ИНИЦИАЛИЗАЦИЯ
     */
    async initialize() {
        console.log('\n🔑 ИНИЦИАЛИЗАЦИЯ ТОЧНОГО АНАЛИЗАТОРА...');
        
        const privateKeyBytes = bs58.default.decode(process.env.WALLET_PRIVATE_KEY);
        this.wallet = Keypair.fromSecretKey(privateKeyBytes);
        
        console.log(`   Кошелек: ${this.wallet.publicKey.toString()}`);
        
        // 🔥 ИНИЦИАЛИЗАЦИЯ ПОЛНОЙ СИСТЕМЫ
        console.log('🔥 ИНИЦИАЛИЗАЦИЯ ПОЛНОЙ СИСТЕМЫ...');
        this.flashLoanSystem = new CompleteFlashLoanWithLiquidity();
        await this.flashLoanSystem.initialize();
        
        console.log('   ✅ Готов к точному анализу');
    }

    /**
     * 🏷️ ПОЛУЧЕНИЕ ИМЕНИ ПРОГРАММЫ
     */
    getProgramName(programId) {
        const programNames = {
            'ComputeBudget111111111111111111111111111111': 'ComputeBudget',
            'MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA': 'MarginFi',
            'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo': 'Meteora DLMM',
            'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA': 'Token Program',
            '********************************': 'System Program',
            'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL': 'Associated Token'
        };
        
        return programNames[programId] || 'Unknown Program';
    }

    /**
     * 📏 ТОЧНЫЙ РАСЧЕТ РАЗМЕРА ИНСТРУКЦИИ
     */
    calculateInstructionSize(instruction) {
        let size = 0;
        
        // 1 байт - индекс программы
        size += 1;
        
        // 1 байт - количество аккаунтов
        size += 1;
        
        // По 1 байту на каждый аккаунт (индекс в таблице аккаунтов)
        size += (instruction.keys?.length || 0);
        
        // 4 байта - длина данных
        size += 4;
        
        // Данные инструкции
        size += (instruction.data?.length || 0);
        
        return size;
    }

    /**
     * 📊 ПОЛНЫЙ АНАЛИЗ ВСЕХ ИНСТРУКЦИЙ
     */
    async runPreciseAnalysis() {
        console.log('\n🔍 ЗАПУСК ТОЧНОГО АНАЛИЗА ВСЕХ ИНСТРУКЦИЙ...');
        console.log('=' .repeat(80));
        
        try {
            // 🔥 СОЗДАЕМ ПОЛНУЮ ТРАНЗАКЦИЮ
            console.log('🔥 СОЗДАНИЕ ПОЛНОЙ ТРАНЗАКЦИИ...');
            const result = await this.flashLoanSystem.createCompleteFlashLoanTransaction();
            
            if (!result.success) {
                throw new Error('Не удалось создать транзакцию для анализа');
            }
            
            const instructions = result.instructions;
            console.log(`✅ Транзакция создана: ${instructions.length} инструкций`);
            
            // 📋 АНАЛИЗ КАЖДОЙ ИНСТРУКЦИИ
            console.log('\n📋 ПОЛНЫЙ СПИСОК ВСЕХ ИНСТРУКЦИЙ:');
            console.log('=' .repeat(80));
            
            let totalSize = 0;
            const addressUsage = new Map();
            const instructionDetails = [];
            
            instructions.forEach((instruction, index) => {
                const programId = instruction.programId.toString();
                const programName = this.getProgramName(programId);
                const size = this.calculateInstructionSize(instruction);
                const accountCount = instruction.keys?.length || 0;
                const dataSize = instruction.data?.length || 0;
                
                // Подсчет использования адресов
                // Программа
                if (!addressUsage.has(programId)) {
                    addressUsage.set(programId, {
                        address: programId,
                        type: 'program',
                        name: programName,
                        count: 0,
                        instructions: []
                    });
                }
                addressUsage.get(programId).count++;
                addressUsage.get(programId).instructions.push(index);
                
                // Аккаунты
                instruction.keys?.forEach(key => {
                    const address = key.pubkey.toString();
                    if (!addressUsage.has(address)) {
                        addressUsage.set(address, {
                            address: address,
                            type: 'account',
                            name: this.getAccountName(address),
                            count: 0,
                            instructions: [],
                            isSigner: key.isSigner,
                            isWritable: key.isWritable
                        });
                    }
                    addressUsage.get(address).count++;
                    addressUsage.get(address).instructions.push(index);
                });
                
                const detail = {
                    index,
                    programId,
                    programName,
                    accountCount,
                    dataSize,
                    totalSize: size,
                    accounts: instruction.keys?.map(key => ({
                        address: key.pubkey.toString(),
                        shortAddress: key.pubkey.toString().slice(0, 8) + '...',
                        isSigner: key.isSigner,
                        isWritable: key.isWritable
                    })) || []
                };
                
                instructionDetails.push(detail);
                totalSize += size;
                
                console.log(`📋 ИНСТРУКЦИЯ ${index}:`);
                console.log(`   🏷️  Программа: ${programName}`);
                console.log(`   👥 Аккаунтов: ${accountCount}`);
                console.log(`   📊 Данных: ${dataSize} байт`);
                console.log(`   📏 Размер: ${size} байт`);
                
                if (accountCount > 0) {
                    console.log(`   🔑 Аккаунты:`);
                    instruction.keys.forEach((key, keyIndex) => {
                        const flags = [];
                        if (key.isSigner) flags.push('signer');
                        if (key.isWritable) flags.push('writable');
                        const shortAddr = key.pubkey.toString().slice(0, 8) + '...';
                        console.log(`      ${keyIndex}: ${shortAddr} (${flags.join(', ') || 'readonly'})`);
                    });
                }
                console.log('');
            });
            
            // 📊 АНАЛИЗ ИСПОЛЬЗОВАНИЯ АДРЕСОВ
            console.log('\n📊 АНАЛИЗ ИСПОЛЬЗОВАНИЯ АДРЕСОВ:');
            console.log('=' .repeat(80));
            
            const sortedAddresses = Array.from(addressUsage.values())
                .sort((a, b) => b.count - a.count);
            
            console.log(`📋 Всего уникальных адресов: ${sortedAddresses.length}`);
            
            // Адреса, используемые более 1 раза (кандидаты для ALT)
            const altCandidates = sortedAddresses.filter(addr => addr.count > 1);
            const uniqueAddresses = sortedAddresses.filter(addr => addr.count === 1);
            
            console.log(`🔄 Кандидатов для ALT: ${altCandidates.length}`);
            console.log(`🔍 Уникальных адресов (1 использование): ${uniqueAddresses.length}`);
            
            // 🎯 ТОП КАНДИДАТЫ ДЛЯ ALT
            console.log('\n🎯 ТОП КАНДИДАТЫ ДЛЯ ALT СЖАТИЯ:');
            console.log('=' .repeat(50));
            
            altCandidates.slice(0, 10).forEach((addr, index) => {
                const savings = (32 - 1) * (addr.count - 1);
                console.log(`${index + 1}. ${addr.name || addr.address.slice(0, 8) + '...'} (${addr.type})`);
                console.log(`   📊 Использований: ${addr.count}`);
                console.log(`   💾 Экономия: ${savings} байт`);
                console.log(`   📋 В инструкциях: ${addr.instructions.join(', ')}`);
                console.log('');
            });
            
            // 🔍 УНИКАЛЬНЫЕ АДРЕСА (НЕ СЖИМАЮТСЯ)
            console.log('\n🔍 УНИКАЛЬНЫЕ АДРЕСА (НЕ СЖИМАЮТСЯ ALT):');
            console.log('=' .repeat(50));
            
            uniqueAddresses.forEach((addr, index) => {
                console.log(`${index + 1}. ${addr.name || addr.address.slice(0, 8) + '...'} (${addr.type})`);
                console.log(`   📋 В инструкции: ${addr.instructions[0]}`);
                console.log(`   📊 Размер: 32 байт (не сжимается)`);
                console.log('');
            });
            
            // 📊 РАСЧЕТ СЖАТИЯ
            console.log('\n📊 РАСЧЕТ ЭФФЕКТИВНОСТИ СЖАТИЯ:');
            console.log('=' .repeat(50));
            
            let totalSavings = 0;
            let altTableSize = 0;
            
            altCandidates.forEach(candidate => {
                const savings = (32 - 1) * (candidate.count - 1);
                totalSavings += savings;
                altTableSize += 32;
            });
            
            const uniqueSize = uniqueAddresses.length * 32;
            const netSavings = totalSavings - altTableSize;
            const compressionRatio = totalSavings > 0 ? (totalSavings / (totalSavings + altTableSize)) : 0;
            
            console.log(`💾 Потенциальная экономия: ${totalSavings} байт`);
            console.log(`📊 Размер ALT таблицы: ${altTableSize} байт`);
            console.log(`✨ Чистая экономия: ${netSavings} байт`);
            console.log(`🔍 Размер уникальных адресов: ${uniqueSize} байт`);
            console.log(`📈 Коэффициент сжатия: ${(compressionRatio * 100).toFixed(1)}%`);
            
            // 📏 ИТОГОВЫЙ РАЗМЕР
            console.log('\n📏 ИТОГОВЫЙ РАЗМЕР ТРАНЗАКЦИИ:');
            console.log('=' .repeat(50));
            
            const baseTransactionSize = 64; // Заголовок
            const totalTransactionSize = baseTransactionSize + totalSize;
            const compressedSize = totalTransactionSize - netSavings;
            
            console.log(`📏 Размер заголовка: ${baseTransactionSize} байт`);
            console.log(`📏 Размер инструкций: ${totalSize} байт`);
            console.log(`📏 Общий размер: ${totalTransactionSize} байт`);
            console.log(`🗜️  Размер после сжатия: ${compressedSize} байт`);
            console.log(`🎯 Лимит Solana: 1232 байт`);
            console.log(`📈 Запас до сжатия: ${1232 - totalTransactionSize} байт`);
            console.log(`📈 Запас после сжатия: ${1232 - compressedSize} байт`);
            
            return {
                success: true,
                totalInstructions: instructions.length,
                totalSize: totalTransactionSize,
                compressedSize,
                altCandidates: altCandidates.length,
                uniqueAddresses: uniqueAddresses.length,
                compressionSavings: netSavings
            };
            
        } catch (error) {
            console.error(`❌ ОШИБКА АНАЛИЗА: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 🏷️ ПОЛУЧЕНИЕ ИМЕНИ АККАУНТА
     */
    getAccountName(address) {
        const knownAccounts = {
            'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV': 'Wallet',
            '68rtTtSuEPR84Wo1xWGs6ytBttn7JN33Ux8WsDp38FHk': 'SOL Account',
            '********************************************': 'USDC Account',
            'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y': 'DLMM Pool',
            '********************************************': 'MarginFi Account',
            'CCKtUs6Cgwo4aaQUmBPmyoApH2gUDErxNZCAntD6LYGh': 'WSOL Bank',
            'So********************************111111112': 'WSOL Mint',
            'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v': 'USDC Mint'
        };
        
        return knownAccounts[address] || null;
    }
}

// 🚀 ЗАПУСК АНАЛИЗА
if (require.main === module) {
    async function main() {
        const analyzer = new PreciseInstructionAnalyzer();
        
        try {
            await analyzer.initialize();
            const results = await analyzer.runPreciseAnalysis();
            
            if (results.success) {
                console.log('\n🎉 ТОЧНЫЙ АНАЛИЗ ЗАВЕРШЕН УСПЕШНО!');
                process.exit(0);
            } else {
                console.log('\n❌ АНАЛИЗ ЗАВЕРШЕН С ОШИБКАМИ');
                process.exit(1);
            }
            
        } catch (error) {
            console.error('💥 КРИТИЧЕСКАЯ ОШИБКА:', error.message);
            process.exit(1);
        }
    }
    
    main().catch(console.error);
}

module.exports = PreciseInstructionAnalyzer;
