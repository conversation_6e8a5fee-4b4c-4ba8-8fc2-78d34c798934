#!/usr/bin/env node

/**
 * 🚨 ДЕТЕКТОР АНОМАЛИЙ ЦЕН
 * ═══════════════════════════════════════════════════════════════
 * 🔥 ЦЕЛЬ: Обнаружение ложных арбитражных сигналов
 * 🎯 ПРОВЕРКИ: Price impact, ликвидность, устаревшие данные
 * ⚠️ ЗАЩИТА: От убытков из-за неточных данных
 */

// const colors = require('colors'); // Убираем зависимость для простоты

// 🔥 ИМПОРТ КОНФИГУРАЦИИ
const { TRADING_CONFIG } = require('./trading-config.js');

/**
 * 🚨 ДЕТЕКТОР АНОМАЛИЙ ЦЕН
 */
class PriceAnomalyDetector {
    constructor() {
        // 🎯 ПОРОГИ ДЛЯ ОБНАРУЖЕНИЯ АНОМАЛИЙ
        this.thresholds = {
            MAX_REALISTIC_SPREAD: 5.0,      // 5% максимальный реалистичный спред
            MIN_LIQUIDITY_USD: 10000,       // $10k минимальная ликвидность
            MAX_PRICE_IMPACT: 2.0,          // 2% максимальный price impact
            MAX_DATA_AGE_MS: 30000,         // 30 сек максимальный возраст данных
            MIN_VOLUME_24H: 50000,          // $50k минимальный объем за 24ч
            MAX_SLIPPAGE: 1.0,              // 1% максимальный slippage
            ORACLE_DEVIATION_MAX: 3.0       // 3% максимальное отклонение от оракула
        };

        // 📊 СТАТИСТИКА
        this.stats = {
            totalChecks: 0,
            anomaliesDetected: 0,
            falsePositives: 0,
            savedFromLoss: 0
        };

        console.log('🚨 Детектор аномалий цен инициализирован');
        console.log(`   Максимальный реалистичный спред: ${this.thresholds.MAX_REALISTIC_SPREAD}%`);
    }

    /**
     * 🔍 ОСНОВНАЯ ПРОВЕРКА АНОМАЛИЙ
     */
    async detectPriceAnomaly(priceData) {
        try {
            console.log('\n🔍 АНАЛИЗ АНОМАЛИЙ ЦЕН...');
            console.log('═'.repeat(50));

            this.stats.totalChecks++;

            const anomalies = [];
            const warnings = [];

            // 1. 📊 АНАЛИЗ СПРЕДА
            const spreadCheck = this.checkSpreadAnomaly(priceData);
            if (spreadCheck.isAnomaly) {
                anomalies.push(spreadCheck);
            } else if (spreadCheck.isWarning) {
                warnings.push(spreadCheck);
            }

            // 2. 💧 ПРОВЕРКА ЛИКВИДНОСТИ
            const liquidityCheck = await this.checkLiquidityAnomaly(priceData);
            if (liquidityCheck.isAnomaly) {
                anomalies.push(liquidityCheck);
            }

            // 3. ⏰ ПРОВЕРКА СВЕЖЕСТИ ДАННЫХ
            const freshnessCheck = this.checkDataFreshness(priceData);
            if (freshnessCheck.isAnomaly) {
                anomalies.push(freshnessCheck);
            }

            // 4. 📈 ПРОВЕРКА PRICE IMPACT
            const impactCheck = await this.checkPriceImpact(priceData);
            if (impactCheck.isAnomaly) {
                anomalies.push(impactCheck);
            }

            // 5. 🎯 ПРОВЕРКА ОБЪЕМА
            const volumeCheck = await this.checkVolumeAnomaly(priceData);
            if (volumeCheck.isAnomaly) {
                anomalies.push(volumeCheck);
            }

            // 6. 🔮 ПРОВЕРКА ОТКЛОНЕНИЯ ОТ ОРАКУЛА
            const oracleCheck = await this.checkOracleDeviation(priceData);
            if (oracleCheck.isAnomaly) {
                anomalies.push(oracleCheck);
            }

            // 📊 РЕЗУЛЬТАТ АНАЛИЗА
            const result = {
                isAnomalous: anomalies.length > 0,
                hasWarnings: warnings.length > 0,
                anomalies: anomalies,
                warnings: warnings,
                riskLevel: this.calculateRiskLevel(anomalies, warnings),
                recommendation: this.getRecommendation(anomalies, warnings),
                timestamp: Date.now()
            };

            if (result.isAnomalous) {
                this.stats.anomaliesDetected++;
                console.log('🚨 АНОМАЛИЯ ОБНАРУЖЕНА!');
            } else if (result.hasWarnings) {
                console.log('⚠️ Предупреждения найдены');
            } else {
                console.log('✅ Цены выглядят нормально');
            }

            this.displayAnalysisResults(result);

            return result;

        } catch (error) {
            console.error('❌ Ошибка анализа аномалий:', error.message);
            return {
                isAnomalous: true,
                error: error.message,
                recommendation: 'SKIP_TRADE'
            };
        }
    }

    /**
     * 📊 ПРОВЕРКА АНОМАЛИЙ СПРЕДА
     */
    checkSpreadAnomaly(priceData) {
        const { buyPrice, sellPrice, spread } = priceData;

        console.log(`📊 Проверка спреда: ${spread.toFixed(4)}%`);

        // Проверка на нереально большой спред
        if (spread > this.thresholds.MAX_REALISTIC_SPREAD) {
            return {
                type: 'SPREAD_TOO_HIGH',
                isAnomaly: true,
                severity: 'HIGH',
                message: `Спред ${spread.toFixed(2)}% превышает реалистичный максимум ${this.thresholds.MAX_REALISTIC_SPREAD}%`,
                details: {
                    actualSpread: spread,
                    maxRealistic: this.thresholds.MAX_REALISTIC_SPREAD,
                    buyPrice: buyPrice,
                    sellPrice: sellPrice
                }
            };
        }

        // Предупреждение о высоком спреде
        if (spread > this.thresholds.MAX_REALISTIC_SPREAD * 0.7) {
            return {
                type: 'SPREAD_HIGH_WARNING',
                isWarning: true,
                severity: 'MEDIUM',
                message: `Высокий спред ${spread.toFixed(2)}% - требует осторожности`
            };
        }

        return { type: 'SPREAD_OK', isAnomaly: false };
    }

    /**
     * 💧 ПРОВЕРКА ЛИКВИДНОСТИ
     */
    async checkLiquidityAnomaly(priceData) {
        console.log('💧 Проверка ликвидности...');

        try {
            // Эмуляция проверки ликвидности (в реальности нужно запросить данные пула)
            const estimatedLiquidity = await this.estimatePoolLiquidity(priceData);

            if (estimatedLiquidity < this.thresholds.MIN_LIQUIDITY_USD) {
                return {
                    type: 'LOW_LIQUIDITY',
                    isAnomaly: true,
                    severity: 'HIGH',
                    message: `Низкая ликвидность: $${estimatedLiquidity.toLocaleString()} < $${this.thresholds.MIN_LIQUIDITY_USD.toLocaleString()}`,
                    details: {
                        actualLiquidity: estimatedLiquidity,
                        minRequired: this.thresholds.MIN_LIQUIDITY_USD
                    }
                };
            }

            return { type: 'LIQUIDITY_OK', isAnomaly: false };

        } catch (error) {
            return {
                type: 'LIQUIDITY_CHECK_FAILED',
                isAnomaly: true,
                severity: 'MEDIUM',
                message: `Не удалось проверить ликвидность: ${error.message}`
            };
        }
    }

    /**
     * ⏰ ПРОВЕРКА СВЕЖЕСТИ ДАННЫХ
     */
    checkDataFreshness(priceData) {
        console.log('⏰ Проверка свежести данных...');

        const now = Date.now();
        const dataAge = now - (priceData.timestamp || now);

        if (dataAge > this.thresholds.MAX_DATA_AGE_MS) {
            return {
                type: 'STALE_DATA',
                isAnomaly: true,
                severity: 'HIGH',
                message: `Устаревшие данные: ${Math.round(dataAge / 1000)}с > ${this.thresholds.MAX_DATA_AGE_MS / 1000}с`,
                details: {
                    dataAge: dataAge,
                    maxAge: this.thresholds.MAX_DATA_AGE_MS
                }
            };
        }

        return { type: 'DATA_FRESH', isAnomaly: false };
    }

    /**
     * 📈 ПРОВЕРКА PRICE IMPACT
     */
    async checkPriceImpact(priceData) {
        console.log('📈 Проверка price impact...');

        try {
            // Эмуляция расчета price impact
            const estimatedImpact = await this.estimatePriceImpact(priceData);

            if (estimatedImpact > this.thresholds.MAX_PRICE_IMPACT) {
                return {
                    type: 'HIGH_PRICE_IMPACT',
                    isAnomaly: true,
                    severity: 'HIGH',
                    message: `Высокий price impact: ${estimatedImpact.toFixed(2)}% > ${this.thresholds.MAX_PRICE_IMPACT}%`,
                    details: {
                        actualImpact: estimatedImpact,
                        maxAllowed: this.thresholds.MAX_PRICE_IMPACT
                    }
                };
            }

            return { type: 'PRICE_IMPACT_OK', isAnomaly: false };

        } catch (error) {
            return {
                type: 'PRICE_IMPACT_CHECK_FAILED',
                isAnomaly: true,
                severity: 'MEDIUM',
                message: `Не удалось проверить price impact: ${error.message}`
            };
        }
    }

    /**
     * 🎯 ПРОВЕРКА ОБЪЕМА
     */
    async checkVolumeAnomaly(priceData) {
        console.log('🎯 Проверка объема торгов...');

        try {
            const volume24h = await this.get24hVolume(priceData);

            if (volume24h < this.thresholds.MIN_VOLUME_24H) {
                return {
                    type: 'LOW_VOLUME',
                    isAnomaly: true,
                    severity: 'MEDIUM',
                    message: `Низкий объем: $${volume24h.toLocaleString()} < $${this.thresholds.MIN_VOLUME_24H.toLocaleString()}`,
                    details: {
                        actualVolume: volume24h,
                        minRequired: this.thresholds.MIN_VOLUME_24H
                    }
                };
            }

            return { type: 'VOLUME_OK', isAnomaly: false };

        } catch (error) {
            return {
                type: 'VOLUME_CHECK_FAILED',
                isAnomaly: false, // Не критично
                severity: 'LOW',
                message: `Не удалось проверить объем: ${error.message}`
            };
        }
    }

    /**
     * 🔮 ПРОВЕРКА ОТКЛОНЕНИЯ ОТ ОРАКУЛА
     */
    async checkOracleDeviation(priceData) {
        console.log('🔮 Проверка отклонения от оракула...');

        try {
            const oraclePrice = await this.getOraclePrice(priceData.token);
            const avgPrice = (priceData.buyPrice + priceData.sellPrice) / 2;
            const deviation = Math.abs((avgPrice - oraclePrice) / oraclePrice) * 100;

            if (deviation > this.thresholds.ORACLE_DEVIATION_MAX) {
                return {
                    type: 'ORACLE_DEVIATION',
                    isAnomaly: true,
                    severity: 'HIGH',
                    message: `Отклонение от оракула: ${deviation.toFixed(2)}% > ${this.thresholds.ORACLE_DEVIATION_MAX}%`,
                    details: {
                        oraclePrice: oraclePrice,
                        avgPrice: avgPrice,
                        deviation: deviation
                    }
                };
            }

            return { type: 'ORACLE_OK', isAnomaly: false };

        } catch (error) {
            return {
                type: 'ORACLE_CHECK_FAILED',
                isAnomaly: false, // Не критично
                severity: 'LOW',
                message: `Не удалось проверить оракул: ${error.message}`
            };
        }
    }

    /**
     * 🧮 ВСПОМОГАТЕЛЬНЫЕ ФУНКЦИИ (ЭМУЛЯЦИЯ)
     */
    async estimatePoolLiquidity(priceData) {
        // В реальности здесь должен быть запрос к пулу
        return Math.random() * 100000 + 50000; // $50k-$150k
    }

    async estimatePriceImpact(priceData) {
        // В реальности здесь должен быть расчет на основе размера сделки и ликвидности
        return Math.random() * 3; // 0-3%
    }

    async get24hVolume(priceData) {
        // В реальности здесь должен быть запрос к API
        return Math.random() * 200000 + 100000; // $100k-$300k
    }

    async getOraclePrice(token) {
        // В реальности здесь должен быть запрос к Pyth или другому оракулу
        return 160 + (Math.random() - 0.5) * 10; // $155-$165
    }

    /**
     * ⚠️ РАСЧЕТ УРОВНЯ РИСКА
     */
    calculateRiskLevel(anomalies, warnings) {
        const highSeverityCount = anomalies.filter(a => a.severity === 'HIGH').length;
        const mediumSeverityCount = anomalies.filter(a => a.severity === 'MEDIUM').length;

        if (highSeverityCount >= 2) return 'CRITICAL';
        if (highSeverityCount >= 1) return 'HIGH';
        if (mediumSeverityCount >= 2) return 'MEDIUM';
        if (warnings.length > 0) return 'LOW';
        return 'MINIMAL';
    }

    /**
     * 💡 ПОЛУЧЕНИЕ РЕКОМЕНДАЦИИ
     */
    getRecommendation(anomalies, warnings) {
        const riskLevel = this.calculateRiskLevel(anomalies, warnings);

        switch (riskLevel) {
            case 'CRITICAL':
                return 'SKIP_TRADE';
            case 'HIGH':
                return 'SKIP_TRADE';
            case 'MEDIUM':
                return 'REDUCE_POSITION';
            case 'LOW':
                return 'PROCEED_WITH_CAUTION';
            default:
                return 'PROCEED_NORMAL';
        }
    }

    /**
     * 📊 ОТОБРАЖЕНИЕ РЕЗУЛЬТАТОВ
     */
    displayAnalysisResults(result) {
        console.log(`\n📊 РЕЗУЛЬТАТЫ АНАЛИЗА АНОМАЛИЙ:`);
        console.log(`   Уровень риска: ${result.riskLevel}`);
        console.log(`   Рекомендация: ${result.recommendation}`);

        if (result.anomalies.length > 0) {
            console.log(`\n🚨 ОБНАРУЖЕННЫЕ АНОМАЛИИ:`);
            result.anomalies.forEach((anomaly, i) => {
                console.log(`   ${i + 1}. [${anomaly.severity}] ${anomaly.message}`);
            });
        }

        if (result.warnings.length > 0) {
            console.log(`\n⚠️ ПРЕДУПРЕЖДЕНИЯ:`);
            result.warnings.forEach((warning, i) => {
                console.log(`   ${i + 1}. ${warning.message}`);
            });
        }
    }

    /**
     * 📊 ПОЛУЧЕНИЕ СТАТИСТИКИ
     */
    getStats() {
        return {
            ...this.stats,
            thresholds: this.thresholds
        };
    }
}

module.exports = PriceAnomalyDetector;
