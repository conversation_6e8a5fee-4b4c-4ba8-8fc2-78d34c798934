const { Connection, PublicKey, Transaction, VersionedTransaction, MessageV0 } = require('@solana/web3.js');
const { Keypair } = require('@solana/web3.js');

/**
 * 🔥 ПРАВИЛЬНАЯ КОМПИЛЯЦИЯ ТРАНЗАКЦИИ С ALT ТАБЛИЦАМИ
 * 
 * ПРАВИЛЬНАЯ ПОСЛЕДОВАТЕЛЬНОСТЬ:
 * 1. Создать ВСЕ инструкции (flash loan start + swaps + flash loan end)
 * 2. Собрать их в Transaction
 * 3. Вызвать compileToV0Message() с ALT таблицами
 * 4. compileToV0Message() автоматически дедуплицирует аккаунты
 * 5. Получается полная транзакция без дублирования с ALT сжатием
 */

class ProperTransactionCompiler {
    constructor(connection, wallet) {
        this.connection = connection;
        this.wallet = wallet;
        
        // 🔥 ALT ТАБЛИЦЫ: 3 MARGINFI + 1 КАСТОМНАЯ
        this.ALT_ADDRESSES = [
            'HGmknUTUmeovMc9ryERNWG6UFZDFDVr9xrum3ZhyL4fC', // MarginFi ALT 1 (256 адресов)
            '5FuKF7C1tJji2mXZuJ14U9oDb37is5mmvYLf4KwojoF1', // MarginFi ALT 2 (256 адресов)  
            'FEFhAFKz48P3w82Ds5VhvyEDwhRqu2FejmnuxEPZ8wNR', // MarginFi ALT 3 (19 адресов)
            'FAeyUf4AdG7vTQ2Q89r9hWBkPdYkykJKG4CxgqArBGXe'  // Кастомная ALT (18 адресов)
        ];
        
        this.loadedALTTables = [];
    }

    /**
     * 🔥 ЗАГРУЗКА ВСЕХ ALT ТАБЛИЦ
     */
    async loadALTTables() {
        console.log('🔥 ЗАГРУЖАЕМ ВСЕ ALT ТАБЛИЦЫ...');
        this.loadedALTTables = [];
        
        for (const address of this.ALT_ADDRESSES) {
            try {
                console.log(`🔧 Загружаем ALT: ${address}`);
                const altAccount = await this.connection.getAddressLookupTable(new PublicKey(address));
                
                if (altAccount && altAccount.value) {
                    this.loadedALTTables.push(altAccount.value);
                    console.log(`✅ ALT загружена: ${address} (${altAccount.value.state.addresses.length} адресов)`);
                } else {
                    console.log(`❌ ALT недоступна: ${address}`);
                }
            } catch (error) {
                console.log(`❌ Ошибка загрузки ALT ${address}: ${error.message}`);
            }
        }
        
        console.log(`✅ Загружено ALT таблиц: ${this.loadedALTTables.length}/4`);
        return this.loadedALTTables;
    }

    /**
     * 🔥 ПРАВИЛЬНАЯ КОМПИЛЯЦИЯ ТРАНЗАКЦИИ
     * 
     * @param {Array} allInstructions - ВСЕ инструкции (flash loan start + swaps + flash loan end)
     * @returns {VersionedTransaction} - Правильно скомпилированная транзакция
     */
    async compileTransaction(allInstructions) {
        console.log('🔥 ПРАВИЛЬНАЯ КОМПИЛЯЦИЯ ТРАНЗАКЦИИ...');
        console.log(`📋 Всего инструкций: ${allInstructions.length}`);
        
        // 🔧 ЭТАП 1: Загружаем ALT таблицы
        if (this.loadedALTTables.length === 0) {
            await this.loadALTTables();
        }
        
        // 🔧 ЭТАП 2: Получаем последний blockhash
        console.log('🔧 Получаем последний blockhash...');
        const { blockhash } = await this.connection.getLatestBlockhash();
        
        // 🔧 ЭТАП 3: Создаем обычную Transaction с ВСЕМИ инструкциями
        console.log('🔧 Создаем Transaction с ВСЕМИ инструкциями...');
        const transaction = new Transaction();
        transaction.recentBlockhash = blockhash;
        transaction.feePayer = this.wallet.publicKey;
        
        // Добавляем ВСЕ инструкции
        allInstructions.forEach(ix => transaction.add(ix));
        
        console.log(`✅ Transaction создана с ${transaction.instructions.length} инструкциями`);
        
        // 🔧 ЭТАП 4: КРИТИЧЕСКИЙ! TransactionMessage.compileToV0Message() с ALT таблицами
        console.log('🔥 КРИТИЧЕСКИЙ ЭТАП: TransactionMessage.compileToV0Message() с ALT таблицами...');
        console.log(`   ALT таблиц: ${this.loadedALTTables.length}`);

        // ✅ ПРАВИЛЬНЫЙ API ПО ОФИЦИАЛЬНОЙ ДОКУМЕНТАЦИИ SOLANA
        const { TransactionMessage } = require('@solana/web3.js');
        const message = new TransactionMessage({
            payerKey: this.wallet.publicKey,
            recentBlockhash: blockhash,
            instructions: transaction.instructions,
        }).compileToV0Message(this.loadedALTTables); // ✅ ALT таблицы передаются в compileToV0Message()
        
        console.log('✅ TransactionMessage.compileToV0Message() выполнен!');
        console.log(`   Статических аккаунтов: ${message.staticAccountKeys.length}`);
        console.log(`   ALT lookups: ${message.addressTableLookups?.length || 0}`);
        console.log(`   Инструкций: ${message.compiledInstructions.length}`);
        
        // 🔧 ЭТАП 5: Создаем VersionedTransaction
        const versionedTx = new VersionedTransaction(message);
        
        console.log('✅ VersionedTransaction создана!');
        console.log(`   Размер до подписи: ${versionedTx.serialize().length} байт`);
        
        return versionedTx;
    }

    /**
     * 🔥 ДИАГНОСТИКА ТРАНЗАКЦИИ
     */
    async diagnoseTransaction(versionedTx) {
        console.log('🔍 ДИАГНОСТИКА ТРАНЗАКЦИИ:');
        console.log(`   Тип: ${versionedTx.constructor.name}`);
        console.log(`   Версия: ${versionedTx.version}`);
        console.log(`   Подписей: ${versionedTx.signatures.length}`);
        
        if (versionedTx.message) {
            const msg = versionedTx.message;
            console.log(`   Статических аккаунтов: ${msg.staticAccountKeys.length}`);
            console.log(`   Инструкций: ${msg.compiledInstructions.length}`);
            console.log(`   ALT lookups: ${msg.addressTableLookups?.length || 0}`);
            
            // Проверяем на дублирование
            const staticKeys = msg.staticAccountKeys.map(key => key.toString());
            const uniqueKeys = [...new Set(staticKeys)];
            
            if (staticKeys.length !== uniqueKeys.length) {
                console.log(`❌ ДУБЛИРОВАНИЕ в статических аккаунтах: ${staticKeys.length} -> ${uniqueKeys.length}`);
                
                // Находим дубликаты
                const duplicates = staticKeys.filter((key, index) => staticKeys.indexOf(key) !== index);
                console.log(`   Дубликаты: ${[...new Set(duplicates)].join(', ')}`);
            } else {
                console.log(`✅ Дублирования в статических аккаунтах НЕТ`);
            }
        }
        
        return versionedTx;
    }

    /**
     * 🔥 ПОДПИСАНИЕ И ОТПРАВКА
     */
    async signAndSend(versionedTx) {
        console.log('🔥 ПОДПИСАНИЕ И ОТПРАВКА...');
        
        // Подписываем
        versionedTx.sign([this.wallet]);
        console.log('✅ Транзакция подписана');
        
        // Сериализуем
        const serialized = versionedTx.serialize();
        console.log(`✅ Транзакция сериализована: ${serialized.length} байт`);
        
        // Отправляем
        try {
            const signature = await this.connection.sendRawTransaction(serialized, {
                skipPreflight: false,
                preflightCommitment: 'confirmed',
                maxRetries: 3
            });
            
            console.log(`✅ Транзакция отправлена: ${signature}`);
            return signature;
            
        } catch (error) {
            console.error(`❌ Ошибка отправки: ${error.message}`);
            throw error;
        }
    }
}

module.exports = ProperTransactionCompiler;
