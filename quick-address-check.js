const { Keypair } = require('@solana/web3.js');
const fs = require('fs');

// Загружаем wallet.json
const walletData = JSON.parse(fs.readFileSync('wallet.json', 'utf8'));
const keypair = Keypair.fromSecretKey(new Uint8Array(walletData));
const address = keypair.publicKey.toString();

console.log('Фактический адрес из wallet.json:', address);
console.log('Ожидаемый адрес из Solscan:     bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV');
console.log('Совпадают:', address === 'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV' ? 'ДА' : 'НЕТ');
