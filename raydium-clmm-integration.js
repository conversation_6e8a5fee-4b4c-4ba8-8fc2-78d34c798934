#!/usr/bin/env node

/**
 * ⚡ RAYDIUM CLMM ИНТЕГРАЦИЯ
 * ═══════════════════════════════════════════════════════════════
 * 🔥 ЦЕЛЬ: Интеграция с Raydium Concentrated Liquidity Market Maker
 * 🚀 SDK: @raydium-io/raydium-sdk-v2
 * 🎯 ФУНКЦИИ: Получение цен, создание swap инструкций, управление пулами
 */

const { PublicKey, Connection, Keypair } = require('@solana/web3.js');
const { BN } = require('@coral-xyz/anchor');
const colors = require('colors');

// 🔥 ИМПОРТ RAYDIUM SDK V2
let Raydium, TxVersion, TokenAmount;
let parseTokenAccountResp = null;

try {
    const raydiumSdk = require('@raydium-io/raydium-sdk-v2');
    Raydium = raydiumSdk.Raydium;
    TxVersion = raydiumSdk.TxVersion;
    TokenAmount = raydiumSdk.TokenAmount;

    // Пытаемся импортировать parseTokenAccountResp из разных мест
    parseTokenAccountResp = raydiumSdk.parseTokenAccountResp ||
                           raydiumSdk.utils?.parseTokenAccountResp ||
                           raydiumSdk.common?.parseTokenAccountResp;

    console.log('✅ Raydium SDK V2 импортирован успешно');
    console.log(`📊 parseTokenAccountResp доступен: ${!!parseTokenAccountResp}`);
} catch (error) {
    console.log('⚠️ Raydium SDK V2 не найден, используем fallback режим');
    console.log(`   Ошибка: ${error.message}`);
}

// 🔥 ИМПОРТ КОНФИГУРАЦИИ
const {
    TRADING_CONFIG,
    getTokenMint,
    getTechnicalLimit
} = require('./trading-config.js');

// 🔥 ИМПОРТ КОНВЕРТЕРА СУММ
const { convertUsdToNativeAmount, convertNativeToUsdAmount } = require('./centralized-amount-converter.js');

/**
 * ⚡ RAYDIUM CLMM ИНТЕГРАЦИЯ
 */
class RaydiumCLMMIntegration {
    constructor(connection, wallet, universalCacheManager = null) {
        this.connection = connection;
        this.wallet = wallet;
        this.universalCacheManager = universalCacheManager;
        this.raydium = null;
        this.initialized = false;

        // 📊 ИЗВЕСТНЫЕ RAYDIUM CLMM ПУЛЫ SOL/USDC
        this.knownPools = new Map([
            ['SOL/USDC_CLMM_1', {
                address: '61R1ndXxvsWXXkWSyNkCxnzwd3zUNB8Q2ibmkiLPC8ht', // Пример адреса
                tokenA: getTokenMint('SOL'),
                tokenB: getTokenMint('USDC'),
                fee: 0.0025, // 0.25%
                tickSpacing: 64
            }],
            ['SOL/USDC_CLMM_2', {
                address: '7XawhbbxtsRcQA8KTkHT9f9nc6d69UwqCDh6U5EEbEmX', // Пример адреса
                tokenA: getTokenMint('SOL'),
                tokenB: getTokenMint('USDC'),
                fee: 0.0005, // 0.05%
                tickSpacing: 10
            }]
        ]);

        // 📊 СТАТИСТИКА
        this.stats = {
            priceQueries: 0,
            swapInstructions: 0,
            errors: 0,
            lastUpdate: null
        };

        console.log('⚡ Raydium CLMM интеграция создана');
        console.log(`   Известных пулов: ${this.knownPools.size}`);
    }

    /**
     * 🚀 ИНИЦИАЛИЗАЦИЯ RAYDIUM SDK
     */
    async initialize() {
        try {
            console.log('🚀 Инициализация Raydium CLMM...'.yellow);

            if (!Raydium) {
                console.log('⚠️ Raydium SDK недоступен, используем API fallback');
                this.initialized = false;
                return false;
            }

            // 1. Получаем token accounts пользователя
            console.log('📊 Получение token accounts...');
            let tokenAccounts = {};

            if (parseTokenAccountResp) {
                try {
                    tokenAccounts = await parseTokenAccountResp({
                        owner: this.wallet.publicKey,
                        connection: this.connection,
                    });
                    console.log(`✅ Token accounts получены: ${Object.keys(tokenAccounts).length}`);
                } catch (error) {
                    console.log(`⚠️ Ошибка получения token accounts: ${error.message}`);
                    console.log('🔄 Используем пустой объект token accounts');
                    tokenAccounts = {};
                }
            } else {
                console.log('⚠️ parseTokenAccountResp недоступен, используем пустой объект');
                tokenAccounts = {};
            }

            // 2. Инициализируем Raydium SDK
            console.log('⚡ Инициализация Raydium SDK...');

            try {
                this.raydium = await Raydium.load({
                    connection: this.connection,
                    owner: this.wallet.publicKey,
                    signAllTransactions: async (txs) => {
                        console.log(`🔐 Подписание ${txs.length} транзакций...`);
                        return txs.map(tx => {
                            tx.sign(this.wallet);
                            return tx;
                        });
                    },
                    tokenAccounts: tokenAccounts,
                    cluster: 'mainnet', // или 'devnet' для тестирования
                    urlConfigs: {
                        BASE_HOST: 'https://api.raydium.io/',
                    },
                });
            } catch (loadError) {
                console.log(`⚠️ Ошибка Raydium.load: ${loadError.message}`);
                console.log('🔄 Пытаемся альтернативную инициализацию...');

                // Альтернативная инициализация без tokenAccounts
                this.raydium = await Raydium.load({
                    connection: this.connection,
                    owner: this.wallet.publicKey,
                    cluster: 'mainnet',
                    urlConfigs: {
                        BASE_HOST: 'https://api.raydium.io/',
                    },
                });
            }

            console.log('✅ Raydium SDK инициализирован');

            // 3. Загружаем информацию о пулах
            await this.loadPoolsInfo();

            this.initialized = true;
            console.log('✅ Raydium CLMM интеграция готова к работе!'.green);

            return true;

        } catch (error) {
            console.error('❌ Ошибка инициализации Raydium CLMM:', error.message);
            this.stats.errors++;
            this.initialized = false;
            return false;
        }
    }

    /**
     * 📊 ЗАГРУЗКА ИНФОРМАЦИИ О ПУЛАХ
     */
    async loadPoolsInfo() {
        try {
            console.log('📊 Загрузка информации о Raydium CLMM пулах...');

            if (!this.raydium) {
                throw new Error('Raydium SDK не инициализирован');
            }

            // Получаем список всех CLMM пулов
            const poolsInfo = await this.raydium.api.fetchPoolById({
                ids: Array.from(this.knownPools.values()).map(pool => pool.address)
            });

            console.log(`✅ Информация о ${poolsInfo.length} пулах загружена`);

            // Обновляем информацию о пулах
            for (const poolInfo of poolsInfo) {
                for (const [key, pool] of this.knownPools.entries()) {
                    if (pool.address === poolInfo.id) {
                        pool.info = poolInfo;
                        console.log(`   ${key}: обновлен`);
                    }
                }
            }

        } catch (error) {
            console.log(`⚠️ Ошибка загрузки информации о пулах: ${error.message}`);
        }
    }

    /**
     * 💰 ПОЛУЧЕНИЕ ЦЕН СО ВСЕХ RAYDIUM CLMM ПУЛОВ
     */
    async getAllPrices() {
        try {
            console.log('💰 Получение цен Raydium CLMM...');

            if (!this.initialized) {
                console.log('⚠️ Raydium CLMM не инициализирован, используем API fallback');
                return await this.getPricesViaAPI();
            }

            const prices = new Map();

            for (const [poolKey, poolData] of this.knownPools.entries()) {
                try {
                    const price = await this.getPoolPrice(poolData.address);
                    if (price) {
                        prices.set(`${poolKey}_${poolData.address}`, price);
                        console.log(`   ${poolKey}: $${price.toFixed(4)}`);
                    }
                } catch (error) {
                    console.log(`   ❌ ${poolKey}: ${error.message}`);
                }
            }

            this.stats.priceQueries++;
            this.stats.lastUpdate = Date.now();

            console.log(`✅ Raydium CLMM: получено ${prices.size} цен`);
            return prices;

        } catch (error) {
            console.error('❌ Ошибка получения цен Raydium CLMM:', error.message);
            this.stats.errors++;
            return new Map();
        }
    }

    /**
     * 📊 ПОЛУЧЕНИЕ ЦЕНЫ КОНКРЕТНОГО ПУЛА
     */
    async getPoolPrice(poolAddress) {
        try {
            if (!this.raydium) {
                throw new Error('Raydium SDK не инициализирован');
            }

            // Получаем информацию о пуле
            const poolInfo = await this.raydium.api.fetchPoolById({
                ids: [poolAddress]
            });

            if (!poolInfo || poolInfo.length === 0) {
                throw new Error('Информация о пуле не найдена');
            }

            const pool = poolInfo[0];

            // Рассчитываем цену на основе резервов
            const price = this.calculatePriceFromPool(pool);

            return price;

        } catch (error) {
            console.log(`⚠️ Ошибка получения цены пула ${poolAddress}: ${error.message}`);
            return null;
        }
    }

    /**
     * 🧮 РАСЧЕТ ЦЕНЫ ИЗ ДАННЫХ ПУЛА
     */
    calculatePriceFromPool(poolData) {
        try {
            // Для CLMM пулов цена рассчитывается из sqrtPriceX64
            if (poolData.sqrtPriceX64) {
                // Формула: price = (sqrtPriceX64 / 2^64)^2
                const sqrtPrice = new BN(poolData.sqrtPriceX64);
                const Q64 = new BN(2).pow(new BN(64));
                
                // Преобразуем в число для расчета
                const sqrtPriceFloat = sqrtPrice.toNumber() / Q64.toNumber();
                const price = Math.pow(sqrtPriceFloat, 2);

                // Корректируем на разность decimals (SOL=9, USDC=6)
                const adjustedPrice = price * Math.pow(10, 9 - 6);

                return adjustedPrice;
            }

            // Fallback: используем резервы токенов
            if (poolData.baseReserve && poolData.quoteReserve) {
                const baseReserve = new BN(poolData.baseReserve);
                const quoteReserve = new BN(poolData.quoteReserve);

                // Цена = quoteReserve / baseReserve
                const price = quoteReserve.toNumber() / baseReserve.toNumber();

                // Корректируем на decimals
                return price * Math.pow(10, 9 - 6);
            }

            throw new Error('Недостаточно данных для расчета цены');

        } catch (error) {
            console.log(`⚠️ Ошибка расчета цены: ${error.message}`);
            return null;
        }
    }

    /**
     * 🌐 ПОЛУЧЕНИЕ ЦЕН ЧЕРЕЗ API (FALLBACK)
     */
    async getPricesViaAPI() {
        try {
            console.log('🌐 Получение цен Raydium через API...');

            // Используем публичный API Raydium
            const response = await fetch('https://api.raydium.io/v2/main/pairs', {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'User-Agent': 'Raydium-Integration/1.0'
                },
                timeout: 5000
            });

            if (!response.ok) {
                throw new Error(`API ответил с кодом ${response.status}`);
            }

            const data = await response.json();
            const prices = new Map();

            // Ищем SOL/USDC пары
            for (const [pairId, pairData] of Object.entries(data)) {
                if (pairData.name && pairData.name.includes('SOL-USDC')) {
                    const price = parseFloat(pairData.price || 0);
                    if (price > 0) {
                        prices.set(`SOL/USDC_${pairId}`, price);
                    }
                }
            }

            console.log(`✅ API Raydium: получено ${prices.size} цен`);
            return prices;

        } catch (error) {
            console.log(`⚠️ Ошибка API Raydium: ${error.message}`);
            return new Map();
        }
    }

    /**
     * 🚀 СОЗДАНИЕ SWAP ИНСТРУКЦИИ
     */
    async createSwapInstruction(poolAddress, amountIn, tokenIn, tokenOut, slippage = 0.5) {
        try {
            console.log(`🚀 Создание Raydium CLMM swap инструкции...`);
            console.log(`   Пул: ${poolAddress}`);
            console.log(`   Сумма: ${amountIn}`);
            console.log(`   Направление: ${tokenIn} → ${tokenOut}`);

            if (!this.initialized || !this.raydium) {
                throw new Error('Raydium SDK не инициализирован');
            }

            // Получаем информацию о пуле
            const poolInfo = await this.raydium.api.fetchPoolById({
                ids: [poolAddress]
            });

            if (!poolInfo || poolInfo.length === 0) {
                throw new Error('Информация о пуле не найдена');
            }

            // Создаем TokenAmount объект
            const inputAmount = new TokenAmount(
                { mint: new PublicKey(tokenIn), decimals: tokenIn === getTokenMint('SOL') ? 9 : 6 },
                amountIn
            );

            // Рассчитываем минимальную выходную сумму с учетом slippage
            const minAmountOut = Math.floor(amountIn * (1 - slippage / 100));

            // Создаем swap инструкцию
            const { execute } = await this.raydium.liquidity.swap({
                poolInfo: poolInfo[0],
                amountIn: inputAmount,
                amountOut: new TokenAmount(
                    { mint: new PublicKey(tokenOut), decimals: tokenOut === getTokenMint('SOL') ? 9 : 6 },
                    minAmountOut
                ),
                fixedSide: 'in',
                inputMint: new PublicKey(tokenIn),
                txVersion: TxVersion.V0,
            });

            this.stats.swapInstructions++;

            console.log('✅ Raydium CLMM swap инструкция создана');

            return {
                success: true,
                instruction: execute,
                poolAddress: poolAddress,
                amountIn: amountIn,
                minAmountOut: minAmountOut,
                dex: 'raydium'
            };

        } catch (error) {
            console.error(`❌ Ошибка создания Raydium swap инструкции: ${error.message}`);
            this.stats.errors++;
            
            return {
                success: false,
                error: error.message,
                dex: 'raydium'
            };
        }
    }

    /**
     * 📊 ПОЛУЧЕНИЕ СТАТИСТИКИ
     */
    getStats() {
        return {
            ...this.stats,
            initialized: this.initialized,
            knownPools: this.knownPools.size,
            sdkAvailable: !!Raydium
        };
    }

    /**
     * 🔄 СБРОС СТАТИСТИКИ
     */
    resetStats() {
        this.stats = {
            priceQueries: 0,
            swapInstructions: 0,
            errors: 0,
            lastUpdate: null
        };
        console.log('📊 Статистика Raydium сброшена');
    }
}

module.exports = RaydiumCLMMIntegration;
