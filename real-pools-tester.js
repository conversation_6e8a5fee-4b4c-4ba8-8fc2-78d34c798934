/**
 * 🌊 REAL METEORA POOLS TESTER
 * 
 * Тестирование с реальными Meteora DLMM пулами:
 * - BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y ($2.9M TVL)
 * - HTvjzsfX3yU6BUodCjZ5vZkUrAxMDTrBs3CJaq43ashR ($743K TVL)
 */

const { InstructionTester } = require('./instruction-testing');

class RealPoolsTester {
    constructor() {
        this.tester = new InstructionTester();
        
        // 🌊 РЕАЛЬНЫЕ METEORA DLMM ПУЛЫ
        this.REAL_POOLS = {
            PRIMARY: {
                address: 'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y',
                name: 'SOL-USDC Primary',
                tvl: '$2,926,841.00',
                binStep: 10,
                baseFee: '0.1%',
                liquidity: {
                    SOL: 8162.93,
                    USDC: 1561741.00
                },
                status: 'HIGH_LIQUIDITY'
            },
            SECONDARY: {
                address: 'HTvjzsfX3yU6BUodCjZ5vZkUrAxMDTrBs3CJaq43ashR',
                name: 'SOL-USDC Secondary',
                tvl: '$743,646.04',
                binStep: 1,
                baseFee: '0.01%',
                liquidity: {
                    SOL: 2067.97,
                    USDC: 398352.89
                },
                status: 'MEDIUM_LIQUIDITY'
            }
        };
        
        console.log('🌊 RealPoolsTester инициализирован с реальными Meteora пулами');
    }

    /**
     * 🧪 ТЕСТИРОВАНИЕ ОСНОВНОГО ПУЛА ($2.9M TVL)
     */
    async testPrimaryPool() {
        console.log('\n🥇 ТЕСТИРОВАНИЕ ОСНОВНОГО ПУЛА ($2.9M TVL)');
        console.log('=' .repeat(80));
        
        const pool = this.REAL_POOLS.PRIMARY;
        console.log(`📊 Пул: ${pool.name}`);
        console.log(`💰 TVL: ${pool.tvl}`);
        console.log(`🔢 Bin Step: ${pool.binStep}`);
        console.log(`💸 Base Fee: ${pool.baseFee}`);
        console.log(`🪙 Ликвидность: ${pool.liquidity.SOL} SOL, ${pool.liquidity.USDC} USDC`);
        
        const results = {};
        
        try {
            // 1. Тест Add Liquidity
            console.log('\n💧 ТЕСТ ADD LIQUIDITY...');
            results.addLiquidity = await this.tester.createAddLiquidityInstruction(
                pool.address,
                100000,  // 0.1 USDC (небольшая сумма для теста)
                1000     // 0.001 SOL
            );
            
            // 2. Тест Swap
            console.log('\n🔄 ТЕСТ SWAP...');
            results.swap = await this.tester.createSwapInstruction(
                pool.address,
                50000,   // 0.05 USDC
                'USDC',
                'SOL'
            );
            
            // 3. Тест Remove Liquidity
            console.log('\n💸 ТЕСТ REMOVE LIQUIDITY...');
            results.removeLiquidity = await this.tester.createRemoveLiquidityInstruction(
                pool.address,
                100000   // LP токены
            );
            
            return {
                pool: pool.name,
                address: pool.address,
                results,
                success: true
            };
            
        } catch (error) {
            console.error(`❌ Ошибка тестирования основного пула:`, error);
            return {
                pool: pool.name,
                address: pool.address,
                error: error.message,
                success: false
            };
        }
    }

    /**
     * 🧪 ТЕСТИРОВАНИЕ ВТОРИЧНОГО ПУЛА ($743K TVL)
     */
    async testSecondaryPool() {
        console.log('\n🥈 ТЕСТИРОВАНИЕ ВТОРИЧНОГО ПУЛА ($743K TVL)');
        console.log('=' .repeat(80));
        
        const pool = this.REAL_POOLS.SECONDARY;
        console.log(`📊 Пул: ${pool.name}`);
        console.log(`💰 TVL: ${pool.tvl}`);
        console.log(`🔢 Bin Step: ${pool.binStep}`);
        console.log(`💸 Base Fee: ${pool.baseFee}`);
        console.log(`🪙 Ликвидность: ${pool.liquidity.SOL} SOL, ${pool.liquidity.USDC} USDC`);
        
        const results = {};
        
        try {
            // 1. Тест Add Liquidity (меньшие суммы для меньшего пула)
            console.log('\n💧 ТЕСТ ADD LIQUIDITY...');
            results.addLiquidity = await this.tester.createAddLiquidityInstruction(
                pool.address,
                50000,   // 0.05 USDC (еще меньше для вторичного пула)
                500      // 0.0005 SOL
            );
            
            // 2. Тест Swap
            console.log('\n🔄 ТЕСТ SWAP...');
            results.swap = await this.tester.createSwapInstruction(
                pool.address,
                25000,   // 0.025 USDC
                'USDC',
                'SOL'
            );
            
            // 3. Тест Remove Liquidity
            console.log('\n💸 ТЕСТ REMOVE LIQUIDITY...');
            results.removeLiquidity = await this.tester.createRemoveLiquidityInstruction(
                pool.address,
                50000    // LP токены
            );
            
            return {
                pool: pool.name,
                address: pool.address,
                results,
                success: true
            };
            
        } catch (error) {
            console.error(`❌ Ошибка тестирования вторичного пула:`, error);
            return {
                pool: pool.name,
                address: pool.address,
                error: error.message,
                success: false
            };
        }
    }

    /**
     * 🚀 ПОЛНОЕ ТЕСТИРОВАНИЕ ОБОИХ ПУЛОВ
     */
    async testAllRealPools() {
        console.log('\n🌊 ЗАПУСК ПОЛНОГО ТЕСТИРОВАНИЯ РЕАЛЬНЫХ METEORA ПУЛОВ');
        console.log('=' .repeat(100));
        
        const results = {
            timestamp: new Date().toISOString(),
            pools: {}
        };
        
        // Тестируем основной пул
        results.pools.primary = await this.testPrimaryPool();
        
        // Тестируем вторичный пул
        results.pools.secondary = await this.testSecondaryPool();
        
        // Итоговый отчет
        console.log('\n📊 ИТОГОВЫЙ ОТЧЕТ ТЕСТИРОВАНИЯ РЕАЛЬНЫХ ПУЛОВ');
        console.log('=' .repeat(100));
        
        Object.entries(results.pools).forEach(([key, poolResult]) => {
            const status = poolResult.success ? '✅' : '❌';
            console.log(`${status} ${poolResult.pool}: ${poolResult.success ? 'SUCCESS' : 'FAILED'}`);
            
            if (poolResult.success && poolResult.results) {
                const { addLiquidity, swap, removeLiquidity } = poolResult.results;
                console.log(`   └─ Add Liquidity: ${addLiquidity?.success ? '✅' : '❌'}`);
                console.log(`   └─ Swap: ${swap?.success ? '✅' : '❌'}`);
                console.log(`   └─ Remove Liquidity: ${removeLiquidity?.success ? '✅' : '❌'}`);
            }
            
            if (!poolResult.success) {
                console.log(`   └─ Error: ${poolResult.error}`);
            }
        });
        
        const successCount = Object.values(results.pools).filter(p => p.success).length;
        const totalCount = Object.keys(results.pools).length;
        
        console.log(`\n📈 СТАТИСТИКА: ${successCount}/${totalCount} пулов успешно протестированы`);
        console.log(`🎯 Процент успеха: ${(successCount/totalCount*100).toFixed(1)}%`);
        
        if (successCount === totalCount) {
            console.log('\n🎉 ВСЕ РЕАЛЬНЫЕ ПУЛЫ РАБОТАЮТ! ГОТОВЫ К АРБИТРАЖУ!');
        } else {
            console.log('\n⚠️  НЕКОТОРЫЕ ПУЛЫ ТРЕБУЮТ ДОРАБОТКИ');
        }
        
        return results;
    }

    /**
     * 💰 СОЗДАНИЕ ПОЛНОЙ АРБИТРАЖНОЙ ТРАНЗАКЦИИ С РЕАЛЬНЫМ ПУЛОМ
     */
    async createRealArbitrageTransaction(arbitrageAmount = 100000) {
        console.log('\n💰 СОЗДАНИЕ ПОЛНОЙ АРБИТРАЖНОЙ ТРАНЗАКЦИИ С РЕАЛЬНЫМ ПУЛОМ');
        console.log('=' .repeat(100));
        console.log(`💵 Сумма арбитража: ${arbitrageAmount} микроюнитов USDC`);
        console.log(`🌊 Используем основной пул: ${this.REAL_POOLS.PRIMARY.address}`);
        
        try {
            const result = await this.tester.createFullFlashLoanTransaction(arbitrageAmount);
            
            if (result.success) {
                console.log('\n✅ ПОЛНАЯ АРБИТРАЖНАЯ ТРАНЗАКЦИЯ СОЗДАНА!');
                console.log(`📦 Инструкций: ${result.instructions.length}`);
                console.log(`💰 Потенциальная прибыль: Зависит от разницы цен между пулами`);
                console.log(`💸 Стоимость тестирования: ~$0.001 (центы!)`);
                
                return result;
            } else {
                throw new Error('Failed to create arbitrage transaction');
            }
            
        } catch (error) {
            console.error(`❌ Ошибка создания арбитражной транзакции:`, error);
            return {
                success: false,
                error: error.message
            };
        }
    }
}

/**
 * 🚀 ГЛАВНАЯ ФУНКЦИЯ ТЕСТИРОВАНИЯ
 */
async function main() {
    console.log('🌊 ЗАПУСК ТЕСТИРОВАНИЯ РЕАЛЬНЫХ METEORA DLMM ПУЛОВ...\n');
    
    const tester = new RealPoolsTester();
    
    try {
        // 1. Тестируем все реальные пулы
        const poolResults = await tester.testAllRealPools();
        
        // 2. Создаем полную арбитражную транзакцию
        const arbitrageResult = await tester.createRealArbitrageTransaction(100000); // 0.1 USDC
        
        console.log('\n🎯 ТЕСТИРОВАНИЕ ЗАВЕРШЕНО!');
        console.log('🚀 СИСТЕМА ГОТОВА К РЕАЛЬНОМУ АРБИТРАЖУ!');
        
        return {
            pools: poolResults,
            arbitrage: arbitrageResult,
            status: 'READY_FOR_PRODUCTION'
        };
        
    } catch (error) {
        console.error('❌ Критическая ошибка:', error);
        return {
            error: error.message,
            status: 'NEEDS_DEBUGGING'
        };
    }
}

// Запуск если файл выполняется напрямую
if (require.main === module) {
    main().catch(console.error);
}

module.exports = { RealPoolsTester };
