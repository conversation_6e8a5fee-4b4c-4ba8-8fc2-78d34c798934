const fs = require('fs');

console.log('🔥 УДАЛЕНИЕ ВСЕХ УПОМИНАНИЙ MeteoraHybridImplementation...');

// Читаем файл построчно
const lines = fs.readFileSync('BMeteora.js', 'utf8').split('\n');

let changesCount = 0;

// Находим и заменяем все упоминания
for (let i = 0; i < lines.length; i++) {
    if (lines[i].includes('// new MeteoraHybridImplementation')) {
        console.log(`Найдена строка ${i + 1}: ${lines[i]}`);
        lines[i] = '            this.meteoraSDK = null; // 🚫 УДАЛЕНО: MeteoraHybridImplementation не нужен';
        console.log(`Заменена на: ${lines[i]}`);
        changesCount++;
    }
}

// Записываем обратно
fs.writeFileSync('BMeteora.js', lines.join('\n'));

console.log(`✅ УДАЛЕНИЕ ЗАВЕРШЕНО! Изменений: ${changesCount}`);

// Проверяем
const content = fs.readFileSync('BMeteora.js', 'utf8');
const hybridMentions = (content.match(/MeteoraHybridImplementation/g) || []).length;
console.log(`📊 Осталось упоминаний MeteoraHybridImplementation: ${hybridMentions}`);

if (hybridMentions <= 1) { // Только в комментарии об удалении
    console.log('🎉 УСПЕХ! Все проблемные упоминания удалены');
} else {
    console.log('❌ Есть еще упоминания');
}

console.log('🚀 ГОТОВ К ЗАПУСКУ БОТА!');
