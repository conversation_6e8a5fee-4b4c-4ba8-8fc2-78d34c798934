#!/usr/bin/env node

/**
 * 🔧 ОБРАТНЫЙ ИНЖИНИРИНГ ANCHOR DISCRIMINATOR
 */

const crypto = require('crypto');

async function reverseEngineerDiscriminator() {
  try {
    console.log('🔧 ОБРАТНЫЙ ИНЖИНИРИНГ ANCHOR DISCRIMINATOR');
    console.log('═══════════════════════════════════════════════════════════');

    // Ожидаемый discriminator из ошибки
    const expected = [105, 124, 201, 106, 153, 2, 8, 156];
    const expectedBuffer = Buffer.from(expected);
    
    console.log(`🎯 ЦЕЛЬ: Найти метод который дает discriminator [${expected.join(', ')}]`);
    console.log(`📊 Hex: ${expectedBuffer.toString('hex')}`);
    
    // Список возможных названий методов для flash loan
    const methodVariants = [
      // Стандартные варианты
      "lending_account_start_flashloan",
      "start_flashloan", 
      "begin_flashloan",
      "flashloan_start",
      "flashloan_begin",
      
      // С префиксами
      "marginfi_account::lending_account_start_flashloan",
      "marginfi::lending_account_start_flashloan",
      "lending::start_flashloan",
      
      // Camel case варианты
      "LendingAccountStartFlashloan",
      "StartFlashloan",
      "BeginFlashloan",
      
      // Snake case варианты
      "lending_account_start_flash_loan",
      "start_flash_loan",
      "begin_flash_loan",
      
      // Другие возможные варианты
      "account_start_flashloan",
      "marginfi_start_flashloan",
      "lending_start_flashloan",
      
      // Без префикса global
      "lending_account_start_flashloan",
      
      // С другими префиксами
      "instruction::lending_account_start_flashloan",
      "ix::lending_account_start_flashloan",
      "accounts::lending_account_start_flashloan"
    ];
    
    console.log(`\n🔍 ТЕСТИРУЕМ ${methodVariants.length} ВАРИАНТОВ:`);
    
    let found = false;
    
    for (const method of methodVariants) {
      // Тестируем с префиксом global:
      const globalMethod = `global:${method}`;
      const globalHash = crypto.createHash('sha256').update(globalMethod).digest();
      const globalDiscriminator = globalHash.slice(0, 8);
      const globalArray = Array.from(globalDiscriminator);
      const globalMatches = JSON.stringify(expected) === JSON.stringify(globalArray);
      
      // Тестируем без префикса
      const directHash = crypto.createHash('sha256').update(method).digest();
      const directDiscriminator = directHash.slice(0, 8);
      const directArray = Array.from(directDiscriminator);
      const directMatches = JSON.stringify(expected) === JSON.stringify(directArray);
      
      if (globalMatches || directMatches) {
        console.log(`🎉 НАЙДЕН ПРАВИЛЬНЫЙ МЕТОД!`);
        console.log(`   Метод: "${globalMatches ? globalMethod : method}"`);
        console.log(`   Discriminator: [${globalMatches ? globalArray.join(', ') : directArray.join(', ')}]`);
        console.log(`   Hex: ${(globalMatches ? globalDiscriminator : directDiscriminator).toString('hex')}`);
        found = true;
        break;
      } else {
        // Показываем только первые несколько для краткости
        if (methodVariants.indexOf(method) < 5) {
          console.log(`   "${method}": [${globalArray.slice(0, 3).join(', ')}...] ❌`);
        }
      }
    }
    
    if (!found) {
      console.log(`❌ НЕ НАЙДЕН ТОЧНЫЙ МЕТОД!`);
      console.log(`💡 ВОЗМОЖНЫЕ ПРИЧИНЫ:`);
      console.log(`   1. Используется кастомный discriminator`);
      console.log(`   2. Метод имеет нестандартное название`);
      console.log(`   3. Используется другой алгоритм хеширования`);
      console.log(`   4. Discriminator задан вручную в коде`);
      
      console.log(`\n🔧 СОЗДАЕМ DISCRIMINATOR НАПРЯМУЮ:`);
      
      // Создаем instruction data с точным discriminator
      const exactDiscriminator = Buffer.from(expected);
      const endIndex = 8;
      const instructionData = Buffer.alloc(16);
      
      exactDiscriminator.copy(instructionData, 0);
      instructionData.writeBigUInt64LE(BigInt(endIndex), 8);
      
      console.log(`📊 ТОЧНЫЙ INSTRUCTION DATA:`);
      console.log(`   Discriminator: [${Array.from(exactDiscriminator).join(', ')}]`);
      console.log(`   End Index: ${endIndex}`);
      console.log(`   Full buffer: ${instructionData.toString('hex')}`);
      console.log(`   Buffer length: ${instructionData.length} bytes`);
      
      console.log(`\n💡 РЕШЕНИЕ:`);
      console.log(`✅ Использовать точный discriminator: Buffer.from([105, 124, 201, 106, 153, 2, 8, 156])`);
      console.log(`✅ Создать instruction data вручную с правильным discriminator`);
      console.log(`✅ Не полагаться на автоматическое создание discriminator`);
    }
    
    console.log(`\n🔧 ДОПОЛНИТЕЛЬНАЯ ИНФОРМАЦИЯ:`);
    console.log(`📋 Anchor обычно использует: SHA256("global:method_name").slice(0, 8)`);
    console.log(`📋 Но MarginFi может использовать кастомный discriminator`);
    console.log(`📋 Discriminator может быть задан вручную в #[instruction] атрибуте`);
    
    console.log(`\n🎯 ПЛАН ДЕЙСТВИЙ:`);
    console.log(`1. Использовать точный discriminator из ошибки`);
    console.log(`2. Создать instruction data вручную`);
    console.log(`3. Не полагаться на SDK для создания discriminator`);
    console.log(`4. Тестировать с реальным MarginFi аккаунтом`);
    
  } catch (error) {
    console.error(`❌ Ошибка: ${error.message}`);
    console.error(error.stack);
  }
}

reverseEngineerDiscriminator().catch(console.error);
