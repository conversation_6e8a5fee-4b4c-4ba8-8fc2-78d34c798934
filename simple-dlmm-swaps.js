#!/usr/bin/env node

/**
 * 🚀 SIMPLE DLMM SWAPS WITHOUT FLASH LOAN
 * 
 * ТЕСТИРУЕМ РЕАЛЬНЫЕ METEORA DLMM SWAPS БЕЗ FLASH LOAN
 */

const {
    Connection,
    PublicKey,
    Keypair,
    Transaction,
    VersionedTransaction,
    TransactionMessage,
    sendAndConfirmTransaction
} = require('@solana/web3.js');
const { 
    getAssociatedTokenAddress
} = require('@solana/spl-token');
const bs58 = require('bs58');

// 🔥 ИМПОРТИРУЕМ METEORA SDK
const MeteoraHybridImplementation = require('./meteora-hybrid-implementation.js');

require('dotenv').config();

class SimpleDLMMSwaps {
    constructor() {
        // 🌐 CONNECTION
        this.connection = new Connection(
            process.env.QUICKNODE_RPC_URL || process.env.SOLANA_RPC_URL,
            'confirmed'
        );
        
        // 🔑 WALLET
        this.wallet = null;
        
        // 🏊 РАБОЧИЙ DLMM ПУЛ
        this.DLMM_POOL = new PublicKey('BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y');
        
        // 🪙 ТОКЕНЫ
        this.TOKENS = {
            USDC: new PublicKey('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'),
            SOL: new PublicKey('So11111111111111111111111111111111111111112')
        };
        
        // 🔥 METEORA SDK
        this.meteoraSDK = null;
        
        console.log('🚀 SIMPLE DLMM SWAPS ИНИЦИАЛИЗИРОВАН');
    }

    /**
     * 🔑 ИНИЦИАЛИЗАЦИЯ
     */
    async initialize() {
        console.log('\n🔑 ИНИЦИАЛИЗАЦИЯ...');
        
        const privateKeyBytes = bs58.default.decode(process.env.WALLET_PRIVATE_KEY);
        this.wallet = Keypair.fromSecretKey(privateKeyBytes);
        
        console.log(`   Кошелек: ${this.wallet.publicKey.toString()}`);
        
        // 🔥 ИНИЦИАЛИЗАЦИЯ METEORA SDK
        console.log('🔥 ИНИЦИАЛИЗАЦИЯ METEORA SDK...');
        this.meteoraSDK = new MeteoraHybridImplementation(this.connection, this.wallet);
        
        console.log('   ✅ Готов к работе');
    }

    /**
     * 🔄 СОЗДАНИЕ ПРОСТОГО SWAP
     */
    async createSimpleSwap(amountIn, tokenIn, tokenOut, executeReal = false) {
        console.log(`\n🔄 СОЗДАНИЕ ${tokenIn} → ${tokenOut} SWAP...`);
        console.log(`   Сумма: ${amountIn} (${tokenIn})`);
        console.log(`   Пул: ${this.DLMM_POOL.toString()}`);
        
        try {
            // Получаем token аккаунты
            const userTokenInAccount = await getAssociatedTokenAddress(
                tokenIn === 'USDC' ? this.TOKENS.USDC : this.TOKENS.SOL,
                this.wallet.publicKey
            );
            const userTokenOutAccount = await getAssociatedTokenAddress(
                tokenOut === 'USDC' ? this.TOKENS.USDC : this.TOKENS.SOL,
                this.wallet.publicKey
            );
            
            console.log(`   From Account: ${userTokenInAccount.toString()}`);
            console.log(`   To Account: ${userTokenOutAccount.toString()}`);
            
            // Параметры swap
            const swapYtoX = tokenIn === 'USDC'; // USDC -> SOL = true, SOL -> USDC = false
            // 🔥 ИСПОЛЬЗУЕМ null ДЛЯ minAmountOut - ПУСТЬ SDK САМ РАССЧИТАЕТ!
            const minAmountOut = null; // SDK сам рассчитает правильный minAmountOut
            
            console.log(`   SwapYtoX: ${swapYtoX}`);
            console.log(`   Min Amount Out: ${minAmountOut}`);
            
            // 🔥 СОЗДАЕМ SWAP ЧЕРЕЗ METEORA SDK
            console.log('\n🔥 СОЗДАНИЕ SWAP...');
            console.log(`   Параметры:`);
            console.log(`     poolAddress: ${this.DLMM_POOL.toString()}`);
            console.log(`     amountIn: ${amountIn} (type: ${typeof amountIn})`);
            console.log(`     minAmountOut: ${minAmountOut} (type: ${typeof minAmountOut})`);
            console.log(`     swapYtoX: ${swapYtoX} (type: ${typeof swapYtoX})`);
            console.log(`     fromAccount: ${userTokenInAccount.toString()}`);
            console.log(`     toAccount: ${userTokenOutAccount.toString()}`);

            const swapResult = await this.meteoraSDK.createStableSwapInstruction(
                this.DLMM_POOL.toString(),
                amountIn,
                minAmountOut,
                swapYtoX,
                userTokenInAccount,
                userTokenOutAccount
            );
            
            if (!swapResult.success) {
                throw new Error(swapResult.error);
            }
            
            console.log(`   ✅ SWAP СОЗДАН: ${swapResult.instructions.length} инструкций`);
            console.log(`   🔑 Аккаунтов: ${swapResult.accountsCount}`);

            // 🔥 СОЗДАЕМ ПРАВИЛЬНУЮ VersionedTransaction ДЛЯ METEORA DLMM
            console.log(`\n📦 СОЗДАНИЕ VersionedTransaction ДЛЯ METEORA DLMM...`);

            const { blockhash } = await this.connection.getLatestBlockhash();

            // Создаем TransactionMessage
            const message = new TransactionMessage({
                payerKey: this.wallet.publicKey,
                recentBlockhash: blockhash,
                instructions: swapResult.instructions
            });

            // Создаем VersionedTransaction
            const transaction = new VersionedTransaction(message.compileToV0Message());

            console.log(`   ✅ VersionedTransaction создана`);
            console.log(`   📊 Инструкций: ${swapResult.instructions.length}`);
            
            // 🧪 СИМУЛЯЦИЯ
            console.log('\n🧪 СИМУЛЯЦИЯ...');

            try {
                // 🔥 VersionedTransaction УЖЕ ИМЕЕТ BLOCKHASH И PAYER
                // НЕ НУЖНО УСТАНАВЛИВАТЬ recentBlockhash и feePayer для VersionedTransaction

                console.log(`   🧪 Запуск симуляции VersionedTransaction...`);

                const simulation = await this.connection.simulateTransaction(
                    transaction,
                    {
                        sigVerify: false,
                        commitment: 'processed',
                        replaceRecentBlockhash: true
                    }
                );

                console.log(`   Simulation result: ${simulation.value.err ? 'ERROR' : 'SUCCESS'}`);

                if (simulation.value.err) {
                    console.log('   ❌ СИМУЛЯЦИЯ ПРОВАЛЕНА');
                    console.log(`      Ошибка: ${JSON.stringify(simulation.value.err)}`);

                    if (simulation.value.logs) {
                        console.log('   📋 Логи:');
                        simulation.value.logs.forEach((log, index) => {
                            console.log(`      ${index + 1}. ${log}`);
                        });
                    }

                    return { success: false, error: simulation.value.err };
                } else {
                    console.log('   ✅ СИМУЛЯЦИЯ УСПЕШНА!');
                    console.log(`      Compute Units: ${simulation.value.unitsConsumed || 'N/A'}`);

                    // 🚀 РЕАЛЬНОЕ ВЫПОЛНЕНИЕ
                    if (executeReal) {
                        console.log('\n🚀 ВЫПОЛНЕНИЕ РЕАЛЬНОЙ ТРАНЗАКЦИИ...');

                        try {
                            // 🔥 ПОДПИСЫВАЕМ VersionedTransaction ДЛЯ РЕАЛЬНОГО ВЫПОЛНЕНИЯ
                            transaction.sign([this.wallet]);

                            // 🔥 ДЛЯ VersionedTransaction ИСПОЛЬЗУЕМ connection.sendTransaction
                            const signature = await this.connection.sendTransaction(transaction, {
                                maxRetries: 3,
                                skipPreflight: false
                            });

                            // Ждем подтверждения
                            const confirmation = await this.connection.confirmTransaction(signature, 'confirmed');

                            console.log(`🎉 ТРАНЗАКЦИЯ ВЫПОЛНЕНА: ${signature}`);
                            console.log(`🔗 Explorer: https://solscan.io/tx/${signature}`);

                            return {
                                success: true,
                                signature: signature,
                                simulation: simulation.value
                            };

                        } catch (execError) {
                            console.error(`❌ ОШИБКА ВЫПОЛНЕНИЯ: ${execError.message}`);
                            return { success: false, error: execError.message };
                        }
                    } else {
                        console.log('\n⚠️ РЕАЛЬНОЕ ВЫПОЛНЕНИЕ ОТКЛЮЧЕНО');
                        console.log('   Для выполнения установите executeReal=true');

                        return {
                            success: true,
                            simulation: simulation.value,
                            transaction: transaction
                        };
                    }
                }

            } catch (simError) {
                console.error(`❌ ОШИБКА СИМУЛЯЦИИ: ${simError.message}`);
                console.error(`   Stack: ${simError.stack}`);
                return { success: false, error: simError.message };
            }

            
        } catch (error) {
            console.error(`❌ ОШИБКА СОЗДАНИЯ SWAP: ${error.message}`);
            return { success: false, error: error.message };
        }
    }

    /**
     * 🔄 АРБИТРАЖНАЯ ПОСЛЕДОВАТЕЛЬНОСТЬ
     */
    async executeArbitrageSequence(initialAmount = 1000000, executeReal = false) {
        console.log('\n🔄 АРБИТРАЖНАЯ ПОСЛЕДОВАТЕЛЬНОСТЬ...');
        console.log(`   Начальная сумма: ${initialAmount / 1e6} USDC`);
        console.log(`   Реальное выполнение: ${executeReal ? 'ДА' : 'НЕТ'}`);
        
        const results = [];
        
        // 1. USDC → SOL
        console.log('\n1️⃣ ПЕРВЫЙ SWAP: USDC → SOL');
        const buyResult = await this.createSimpleSwap(
            initialAmount,
            'USDC',
            'SOL',
            executeReal
        );
        results.push({ step: 'buy', result: buyResult });
        
        if (!buyResult.success) {
            console.log('❌ Первый swap провален, останавливаем');
            return { success: false, results };
        }
        
        // Ждем немного между swaps
        if (executeReal) {
            console.log('⏳ Ожидание 3 секунды...');
            await new Promise(resolve => setTimeout(resolve, 3000));
        }
        
        // 2. SOL → USDC (примерная сумма)
        const estimatedSOL = Math.floor(initialAmount / 160 * 1e9); // Примерно по курсу $160/SOL
        
        console.log('\n2️⃣ ВТОРОЙ SWAP: SOL → USDC');
        const sellResult = await this.createSimpleSwap(
            estimatedSOL,
            'SOL',
            'USDC',
            executeReal
        );
        results.push({ step: 'sell', result: sellResult });
        
        // Анализ результатов
        console.log('\n📊 АНАЛИЗ РЕЗУЛЬТАТОВ:');
        const buySuccess = buyResult.success;
        const sellSuccess = sellResult.success;
        
        console.log(`   Buy Swap: ${buySuccess ? '✅' : '❌'}`);
        console.log(`   Sell Swap: ${sellSuccess ? '✅' : '❌'}`);
        
        if (buySuccess && sellSuccess) {
            console.log('🎉 АРБИТРАЖНАЯ ПОСЛЕДОВАТЕЛЬНОСТЬ УСПЕШНА!');
            
            if (executeReal) {
                console.log(`🔗 Buy TX: https://solscan.io/tx/${buyResult.signature}`);
                console.log(`🔗 Sell TX: https://solscan.io/tx/${sellResult.signature}`);
            }
        } else {
            console.log('❌ Арбитражная последовательность провалена');
        }
        
        return {
            success: buySuccess && sellSuccess,
            results: results
        };
    }

    /**
     * 🧪 ТЕСТОВЫЕ SWAPS
     */
    async runTestSwaps() {
        console.log('\n🧪 ТЕСТОВЫЕ SWAPS...');
        
        const testCases = [
            { amount: 100000, tokenIn: 'USDC', tokenOut: 'SOL', name: '0.1 USDC → SOL' },
            { amount: 10000000, tokenIn: 'SOL', tokenOut: 'USDC', name: '0.01 SOL → USDC' },
            { amount: 1000000, tokenIn: 'USDC', tokenOut: 'SOL', name: '1 USDC → SOL' }
        ];
        
        const results = [];
        
        for (const testCase of testCases) {
            console.log(`\n📋 ТЕСТ: ${testCase.name}`);
            
            const result = await this.createSimpleSwap(
                testCase.amount,
                testCase.tokenIn,
                testCase.tokenOut,
                false // Только симуляция
            );
            
            results.push({
                name: testCase.name,
                success: result.success,
                error: result.error
            });
        }
        
        // Итоги
        console.log('\n📊 ИТОГИ ТЕСТОВ:');
        results.forEach(result => {
            const status = result.success ? '✅' : '❌';
            const details = result.success ? 'OK' : result.error;
            console.log(`   ${status} ${result.name}: ${details}`);
        });
        
        const successCount = results.filter(r => r.success).length;
        console.log(`\n🎯 УСПЕШНЫХ ТЕСТОВ: ${successCount}/${results.length}`);
        
        return results;
    }
}

// 🚀 ЗАПУСК
if (require.main === module) {
    async function main() {
        const swapper = new SimpleDLMMSwaps();
        
        try {
            // Инициализация
            await swapper.initialize();
            
            // Выбор режима
            const args = process.argv.slice(2);
            const mode = args[0] || 'test';
            const executeReal = args.includes('--real');
            
            console.log(`\n🎯 РЕЖИМ: ${mode.toUpperCase()}`);
            console.log(`🚀 РЕАЛЬНОЕ ВЫПОЛНЕНИЕ: ${executeReal ? 'ДА' : 'НЕТ'}`);
            
            if (executeReal) {
                console.log('\n⚠️ ВНИМАНИЕ: РЕАЛЬНЫЕ ТРАНЗАКЦИИ БУДУТ ВЫПОЛНЕНЫ!');
                console.log('⚠️ УБЕДИТЕСЬ ЧТО У ВАС ЕСТЬ ТОКЕНЫ НА БАЛАНСЕ!');
            }
            
            let result;
            
            switch (mode) {
                case 'test':
                    result = await swapper.runTestSwaps();
                    break;
                    
                case 'arbitrage':
                    const amount = parseInt(args[1]) || 1000000; // 1 USDC по умолчанию
                    result = await swapper.executeArbitrageSequence(amount, executeReal);
                    break;
                    
                case 'single':
                    const swapAmount = parseInt(args[1]) || 100000; // 0.1 USDC
                    const tokenIn = args[2] || 'USDC';
                    const tokenOut = args[3] || 'SOL';
                    result = await swapper.createSimpleSwap(swapAmount, tokenIn, tokenOut, executeReal);
                    break;
                    
                default:
                    console.log('❌ Неизвестный режим. Используйте: test, arbitrage, single');
                    return;
            }
            
            if (result.success) {
                console.log('\n🎉 ОПЕРАЦИЯ ЗАВЕРШЕНА УСПЕШНО!');
            } else {
                console.log('\n❌ ОПЕРАЦИЯ ПРОВАЛЕНА!');
            }
            
        } catch (error) {
            console.error('💥 КРИТИЧЕСКАЯ ОШИБКА:', error.message);
        }
    }
    
    main().catch(console.error);
}

module.exports = SimpleDLMMSwaps;
