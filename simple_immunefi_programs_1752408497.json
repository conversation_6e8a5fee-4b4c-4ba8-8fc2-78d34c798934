[{"name": "Uniswap V3", "slug": "uniswap", "url": "https://immunefi.com/bounty/uniswap/", "max_bounty": "$2,250,000", "ecosystem": "Ethereum", "contracts": ["******************************************", "******************************************", "******************************************"], "endpoints": ["https://api.uniswap.org/v1/"], "priority_score": 0.95, "source": "known_programs", "kyc_required": false, "poc_required": true, "vulnerability_types": ["Smart Contract", "Web/App"], "last_updated": "2024-01-01"}, {"name": "Compound Finance", "slug": "compound", "url": "https://immunefi.com/bounty/compound/", "max_bounty": "$1,000,000", "ecosystem": "Ethereum", "contracts": ["******************************************", "******************************************", "******************************************"], "endpoints": ["https://api.compound.finance/api/v2/"], "priority_score": 0.88, "source": "known_programs", "kyc_required": false, "poc_required": true, "vulnerability_types": ["Smart Contract", "Web/App"], "last_updated": "2024-01-01"}, {"name": "Aave Protocol", "slug": "aave", "url": "https://immunefi.com/bounty/aave/", "max_bounty": "$1,000,000", "ecosystem": "Ethereum", "contracts": ["******************************************", "******************************************", "******************************************"], "endpoints": ["https://aave-api-v2.aave.com/"], "priority_score": 0.85, "source": "known_programs", "kyc_required": false, "poc_required": true, "vulnerability_types": ["Smart Contract", "Web/App"], "last_updated": "2024-01-01"}, {"name": "Curve Finance", "slug": "curve", "url": "https://immunefi.com/bounty/curve/", "max_bounty": "$1,000,000", "ecosystem": "Ethereum", "contracts": ["******************************************", "******************************************", "******************************************"], "endpoints": ["https://api.curve.fi/"], "priority_score": 0.82, "source": "known_programs", "kyc_required": false, "poc_required": true, "vulnerability_types": ["Smart Contract", "Web/App"], "last_updated": "2024-01-01"}, {"name": "Solana Foundation", "slug": "solana", "url": "https://immunefi.com/bounty/solana/", "max_bounty": "$2,000,000", "ecosystem": "Solana", "contracts": ["********************************", "TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA", "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL"], "endpoints": ["https://api.mainnet-beta.solana.com"], "priority_score": 0.92, "source": "known_programs", "kyc_required": false, "poc_required": true, "vulnerability_types": ["Smart Contract", "Web/App"], "last_updated": "2024-01-01"}, {"name": "Polygon", "slug": "polygon", "url": "https://immunefi.com/bounty/polygon/", "max_bounty": "$2,000,000", "ecosystem": "Polygon", "contracts": ["0x0000000000000000000000000000000000001010"], "endpoints": ["https://polygon-rpc.com/"], "priority_score": 0.9, "source": "known_programs", "kyc_required": false, "poc_required": true, "vulnerability_types": ["Smart Contract", "Web/App"], "last_updated": "2024-01-01"}, {"name": "Chainlink", "slug": "chainlink", "url": "https://immunefi.com/bounty/chainlink/", "max_bounty": "$1,000,000", "ecosystem": "Multi-chain", "contracts": ["0x514910771AF9Ca656af840dff83E8264EcF986CA"], "endpoints": ["https://api.chain.link/"], "priority_score": 0.87, "source": "known_programs", "kyc_required": false, "poc_required": true, "vulnerability_types": ["Smart Contract", "Web/App"], "last_updated": "2024-01-01"}, {"name": "SushiSwap", "slug": "sushiswap", "url": "https://immunefi.com/bounty/sushiswap/", "max_bounty": "$1,000,000", "ecosystem": "Multi-chain", "contracts": ["0xd9e1cE17f2641f24aE83637ab66a2cca9C378B9F"], "endpoints": ["https://api.sushi.com/"], "priority_score": 0.84, "source": "known_programs", "kyc_required": false, "poc_required": true, "vulnerability_types": ["Smart Contract", "Web/App"], "last_updated": "2024-01-01"}, {"name": "PancakeSwap", "slug": "pancakeswap", "url": "https://immunefi.com/bounty/pancakeswap/", "max_bounty": "$1,000,000", "ecosystem": "BSC", "contracts": ["******************************************"], "endpoints": ["https://api.pancakeswap.info/"], "priority_score": 0.81, "source": "known_programs", "kyc_required": false, "poc_required": true, "vulnerability_types": ["Smart Contract", "Web/App"], "last_updated": "2024-01-01"}, {"name": "Yearn Finance", "slug": "yearn", "url": "https://immunefi.com/bounty/yearn/", "max_bounty": "$500,000", "ecosystem": "Ethereum", "contracts": ["******************************************"], "endpoints": ["https://api.yearn.finance/"], "priority_score": 0.78, "source": "known_programs", "kyc_required": false, "poc_required": true, "vulnerability_types": ["Smart Contract", "Web/App"], "last_updated": "2024-01-01"}, {"name": "MakerDAO", "slug": "makerdao", "url": "https://immunefi.com/bounty/makerdao/", "ecosystem": "Ethereum", "source": "pattern_generation", "max_bounty": "$1,000,000", "priority_score": 1.0, "contracts": ["******************************************"], "endpoints": ["https://api.makerdao.com/"], "kyc_required": false, "poc_required": true, "vulnerability_types": ["Smart Contract"], "last_updated": "2024-01-01"}, {"name": "Synthetix", "slug": "synthetix", "url": "https://immunefi.com/bounty/synthetix/", "ecosystem": "Ethereum", "source": "pattern_generation", "max_bounty": "$1,000,000", "priority_score": 1.0, "contracts": ["******************************************"], "endpoints": ["https://api.synthetix.com/"], "kyc_required": false, "poc_required": true, "vulnerability_types": ["Smart Contract"], "last_updated": "2024-01-01"}, {"name": "Balancer", "slug": "balancer", "url": "https://immunefi.com/bounty/balancer/", "ecosystem": "Ethereum", "source": "pattern_generation", "max_bounty": "$500,000", "priority_score": 0.9500000000000001, "contracts": ["******************************************"], "endpoints": ["https://api.balancer.com/"], "kyc_required": false, "poc_required": true, "vulnerability_types": ["Smart Contract"], "last_updated": "2024-01-01"}, {"name": "1inch", "slug": "1inch", "url": "https://immunefi.com/bounty/1inch/", "ecosystem": "Ethereum", "source": "pattern_generation", "max_bounty": "$500,000", "priority_score": 0.9500000000000001, "contracts": ["******************************************"], "endpoints": ["https://api.1inch.com/"], "kyc_required": false, "poc_required": true, "vulnerability_types": ["Smart Contract"], "last_updated": "2024-01-01"}, {"name": "Gnosis", "slug": "gnosis", "url": "https://immunefi.com/bounty/gnosis/", "ecosystem": "Ethereum", "source": "pattern_generation", "max_bounty": "$100,000", "priority_score": 0.9, "contracts": ["******************************************"], "endpoints": ["https://api.gnosis.com/"], "kyc_required": false, "poc_required": true, "vulnerability_types": ["Smart Contract"], "last_updated": "2024-01-01"}, {"name": "Serum", "slug": "serum", "url": "https://immunefi.com/bounty/serum/", "ecosystem": "Solana", "source": "pattern_generation", "max_bounty": "$500,000", "priority_score": 0.9, "contracts": ["******************************************"], "endpoints": ["https://api.serum.com/"], "kyc_required": false, "poc_required": true, "vulnerability_types": ["Smart Contract"], "last_updated": "2024-01-01"}, {"name": "Raydium", "slug": "raydium", "url": "https://immunefi.com/bounty/raydium/", "ecosystem": "Solana", "source": "pattern_generation", "max_bounty": "$500,000", "priority_score": 0.9, "contracts": ["******************************************"], "endpoints": ["https://api.raydium.com/"], "kyc_required": false, "poc_required": true, "vulnerability_types": ["Smart Contract"], "last_updated": "2024-01-01"}, {"name": "Orca", "slug": "orca", "url": "https://immunefi.com/bounty/orca/", "ecosystem": "Solana", "source": "pattern_generation", "max_bounty": "$100,000", "priority_score": 0.85, "contracts": ["0xffffffffffffffffffffffff91e0884d9fd44a50"], "endpoints": ["https://api.orca.com/"], "kyc_required": false, "poc_required": true, "vulnerability_types": ["Smart Contract"], "last_updated": "2024-01-01"}, {"name": "Mango Markets", "slug": "mango-markets", "url": "https://immunefi.com/bounty/mango-markets/", "ecosystem": "Solana", "source": "pattern_generation", "max_bounty": "$100,000", "priority_score": 0.85, "contracts": ["0x0000000000000000000000007f0271a0e2b22005"], "endpoints": ["https://api.mango-markets.com/"], "kyc_required": false, "poc_required": true, "vulnerability_types": ["Smart Contract"], "last_updated": "2024-01-01"}, {"name": "Marinade", "slug": "marinade", "url": "https://immunefi.com/bounty/marinade/", "ecosystem": "Solana", "source": "pattern_generation", "max_bounty": "$100,000", "priority_score": 0.85, "contracts": ["0xffffffffffffffffffffffffe2e60d276ddd3505"], "endpoints": ["https://api.marinade.com/"], "kyc_required": false, "poc_required": true, "vulnerability_types": ["Smart Contract"], "last_updated": "2024-01-01"}, {"name": "QuickSwap", "slug": "quickswap", "url": "https://immunefi.com/bounty/quickswap/", "ecosystem": "Polygon", "source": "pattern_generation", "max_bounty": "$100,000", "priority_score": 0.7999999999999999, "contracts": ["0xffffffffffffffffffffffff92e5d8bc31f51e3a"], "endpoints": ["https://api.quickswap.com/"], "kyc_required": false, "poc_required": true, "vulnerability_types": ["Smart Contract"], "last_updated": "2024-01-01"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "slug": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://immunefi.com/bounty/aavegotchi/", "ecosystem": "Polygon", "source": "pattern_generation", "max_bounty": "$100,000", "priority_score": 0.7999999999999999, "contracts": ["0x00000000000000000000000060db3807ecc2797b"], "endpoints": ["https://api.aavegotchi.com/"], "kyc_required": false, "poc_required": true, "vulnerability_types": ["Smart Contract"], "last_updated": "2024-01-01"}, {"name": "Polymarket", "slug": "polymarket", "url": "https://immunefi.com/bounty/polymarket/", "ecosystem": "Polygon", "source": "pattern_generation", "max_bounty": "$100,000", "priority_score": 0.7999999999999999, "contracts": ["0xffffffffffffffffffffffffd94189f4a7f63dec"], "endpoints": ["https://api.polymarket.com/"], "kyc_required": false, "poc_required": true, "vulnerability_types": ["Smart Contract"], "last_updated": "2024-01-01"}, {"name": "Gains Network", "slug": "gains-network", "url": "https://immunefi.com/bounty/gains-network/", "ecosystem": "Polygon", "source": "pattern_generation", "max_bounty": "$100,000", "priority_score": 0.7999999999999999, "contracts": ["0xfffffffffffffffffffffffffc8a56c79a6513bb"], "endpoints": ["https://api.gains-network.com/"], "kyc_required": false, "poc_required": true, "vulnerability_types": ["Smart Contract"], "last_updated": "2024-01-01"}, {"name": "Venus", "slug": "venus", "url": "https://immunefi.com/bounty/venus/", "ecosystem": "BSC", "source": "pattern_generation", "max_bounty": "$100,000", "priority_score": 0.75, "contracts": ["0x0000000000000000000000004ef9f613b89fa4c9"], "endpoints": ["https://api.venus.com/"], "kyc_required": false, "poc_required": true, "vulnerability_types": ["Smart Contract"], "last_updated": "2024-01-01"}, {"name": "Alpaca Finance", "slug": "alpaca-finance", "url": "https://immunefi.com/bounty/alpaca-finance/", "ecosystem": "BSC", "source": "pattern_generation", "max_bounty": "$100,000", "priority_score": 0.75, "contracts": ["0xffffffffffffffffffffffffde6ceac480b5b739"], "endpoints": ["https://api.alpaca-finance.com/"], "kyc_required": false, "poc_required": true, "vulnerability_types": ["Smart Contract"], "last_updated": "2024-01-01"}, {"name": "Biswap", "slug": "biswap", "url": "https://immunefi.com/bounty/biswap/", "ecosystem": "BSC", "source": "pattern_generation", "max_bounty": "$100,000", "priority_score": 0.75, "contracts": ["0xffffffffffffffffffffffff8a489fade8674f12"], "endpoints": ["https://api.biswap.com/"], "kyc_required": false, "poc_required": true, "vulnerability_types": ["Smart Contract"], "last_updated": "2024-01-01"}, {"name": "El<PERSON><PERSON>", "slug": "ellipsis", "url": "https://immunefi.com/bounty/ellipsis/", "ecosystem": "BSC", "source": "pattern_generation", "max_bounty": "$100,000", "priority_score": 0.75, "contracts": ["0x0000000000000000000000002c074e9b64a2e38e"], "endpoints": ["https://api.ellipsis.com/"], "kyc_required": false, "poc_required": true, "vulnerability_types": ["Smart Contract"], "last_updated": "2024-01-01"}, {"name": "Trader <PERSON>", "slug": "trader-joe", "url": "https://immunefi.com/bounty/trader-joe/", "ecosystem": "Avalanche", "source": "pattern_generation", "max_bounty": "$1,000,000", "priority_score": 0.8500000000000001, "contracts": ["0xffffffffffffffffffffffffe6ee2a69bfa056fd"], "endpoints": ["https://api.trader-joe.com/"], "kyc_required": false, "poc_required": true, "vulnerability_types": ["Smart Contract"], "last_updated": "2024-01-01"}, {"name": "<PERSON><PERSON>", "slug": "<PERSON><PERSON>", "url": "https://immunefi.com/bounty/benqi/", "ecosystem": "Avalanche", "source": "pattern_generation", "max_bounty": "$100,000", "priority_score": 0.75, "contracts": ["0xffffffffffffffffffffffff8253a01cb3ebc231"], "endpoints": ["https://api.benqi.com/"], "kyc_required": false, "poc_required": true, "vulnerability_types": ["Smart Contract"], "last_updated": "2024-01-01"}, {"name": "Pangolin", "slug": "pangolin", "url": "https://immunefi.com/bounty/pangolin/", "ecosystem": "Avalanche", "source": "pattern_generation", "max_bounty": "$100,000", "priority_score": 0.75, "contracts": ["0xffffffffffffffffffffffffd99861bd46e0b412"], "endpoints": ["https://api.pangolin.com/"], "kyc_required": false, "poc_required": true, "vulnerability_types": ["Smart Contract"], "last_updated": "2024-01-01"}, {"name": "<PERSON><PERSON>", "slug": "yield-yak", "url": "https://immunefi.com/bounty/yield-yak/", "ecosystem": "Avalanche", "source": "pattern_generation", "max_bounty": "$100,000", "priority_score": 0.75, "contracts": ["0xffffffffffffffffffffffffebc10ab7d460bd5c"], "endpoints": ["https://api.yield-yak.com/"], "kyc_required": false, "poc_required": true, "vulnerability_types": ["Smart Contract"], "last_updated": "2024-01-01"}, {"name": "GMX", "slug": "gmx", "url": "https://immunefi.com/bounty/gmx/", "ecosystem": "Arbitrum", "source": "pattern_generation", "max_bounty": "$1,000,000", "priority_score": 0.8999999999999999, "contracts": ["0xffffffffffffffffffffffffa48ce3e0cc8c0fa0"], "endpoints": ["https://api.gmx.com/"], "kyc_required": false, "poc_required": true, "vulnerability_types": ["Smart Contract"], "last_updated": "2024-01-01"}, {"name": "Dopex", "slug": "dopex", "url": "https://immunefi.com/bounty/dopex/", "ecosystem": "Arbitrum", "source": "pattern_generation", "max_bounty": "$100,000", "priority_score": 0.7999999999999999, "contracts": ["0xfffffffffffffffffffffffff37174e2786c8bf3"], "endpoints": ["https://api.dopex.com/"], "kyc_required": false, "poc_required": true, "vulnerability_types": ["Smart Contract"], "last_updated": "2024-01-01"}, {"name": "Radiant Capital", "slug": "radiant-capital", "url": "https://immunefi.com/bounty/radiant-capital/", "ecosystem": "Arbitrum", "source": "pattern_generation", "max_bounty": "$100,000", "priority_score": 0.7999999999999999, "contracts": ["0xffffffffffffffffffffffffb794c34b7e8196cf"], "endpoints": ["https://api.radiant-capital.com/"], "kyc_required": false, "poc_required": true, "vulnerability_types": ["Smart Contract"], "last_updated": "2024-01-01"}, {"name": "<PERSON><PERSON>", "slug": "camelot", "url": "https://immunefi.com/bounty/camelot/", "ecosystem": "Arbitrum", "source": "pattern_generation", "max_bounty": "$100,000", "priority_score": 0.7999999999999999, "contracts": ["0xffffffffffffffffffffffffa385cefc22226abc"], "endpoints": ["https://api.camelot.com/"], "kyc_required": false, "poc_required": true, "vulnerability_types": ["Smart Contract"], "last_updated": "2024-01-01"}, {"name": "Velodrome", "slug": "velodrome", "url": "https://immunefi.com/bounty/velodrome/", "ecosystem": "Optimism", "source": "pattern_generation", "max_bounty": "$100,000", "priority_score": 0.7999999999999999, "contracts": ["0x0000000000000000000000003dfdbd551013cc4c"], "endpoints": ["https://api.velodrome.com/"], "kyc_required": false, "poc_required": true, "vulnerability_types": ["Smart Contract"], "last_updated": "2024-01-01"}, {"name": "Synthetix", "slug": "synthetix", "url": "https://immunefi.com/bounty/synthetix/", "ecosystem": "Optimism", "source": "pattern_generation", "max_bounty": "$1,000,000", "priority_score": 0.8999999999999999, "contracts": ["******************************************"], "endpoints": ["https://api.synthetix.com/"], "kyc_required": false, "poc_required": true, "vulnerability_types": ["Smart Contract"], "last_updated": "2024-01-01"}, {"name": "K<PERSON><PERSON>", "slug": "kwenta", "url": "https://immunefi.com/bounty/kwenta/", "ecosystem": "Optimism", "source": "pattern_generation", "max_bounty": "$100,000", "priority_score": 0.7999999999999999, "contracts": ["0xffffffffffffffffffffffff91610c747d3eefc1"], "endpoints": ["https://api.kwenta.com/"], "kyc_required": false, "poc_required": true, "vulnerability_types": ["Smart Contract"], "last_updated": "2024-01-01"}, {"name": "Lyra", "slug": "lyra", "url": "https://immunefi.com/bounty/lyra/", "ecosystem": "Optimism", "source": "pattern_generation", "max_bounty": "$100,000", "priority_score": 0.7999999999999999, "contracts": ["0x0000000000000000000000000c9bd6faedf26c83"], "endpoints": ["https://api.lyra.com/"], "kyc_required": false, "poc_required": true, "vulnerability_types": ["Smart Contract"], "last_updated": "2024-01-01"}]