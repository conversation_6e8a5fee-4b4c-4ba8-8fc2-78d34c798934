# 🚀 FLASH LOAN АРБИТРАЖ

## 📦 Установленные SDK
- ✅ @mrgnlabs/marginfi-client-v2 - Flash loans
- ✅ @orca-so/whirlpools - Concentrated liquidity
- ✅ @solana/web3.js - <PERSON><PERSON> операции

## 🎯 Быстрый старт

1. **Настройка:**
   ```bash
   cp .env.example .env
   # Отредактируйте .env с вашими настройками
   ```

2. **Запуск:**
   ```bash
   node arbitrage-example.js
   ```

## ⚠️ ВАЖНО
- Тестируйте на devnet перед mainnet
- Убедитесь, что у вас достаточно SOL для комиссий
- Flash loans требуют возврата в той же транзакции

## 🔧 Файлы
- `arbitrage-example.js` - Основной скрипт
- `marginfi-flash-loan.js` - Marginfi интеграция
- `orca-integration.js` - Orca интеграция
- `arbitrage-strategy.js` - Стратегия арбитража
