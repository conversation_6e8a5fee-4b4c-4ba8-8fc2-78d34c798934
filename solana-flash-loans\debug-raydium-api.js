#!/usr/bin/env node

/**
 * 🔍 ОТЛАДКА RAYDIUM TRADE API
 *
 * Тестируем разные варианты параметров для Raydium Trade API
 */

const axios = require('axios');

class RaydiumAPIDebugger {
  constructor() {
    this.testTokens = {
      SOL: 'So11111111111111111111111111111111111111112',
      USDC: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'
    };
  }

  async debugRaydiumAPI() {
    console.log('🔍 ОТЛАДКА RAYDIUM TRADE API');
    console.log('═══════════════════════════════════════════════════════');

    // Тест 1: Базовые параметры
    await this.testBasicParams();

    // Тест 2: Альтернативные endpoints
    await this.testAlternativeEndpoints();

    // Тест 3: Проверка доступности API
    await this.testAPIAvailability();
  }

  /**
   * 🧪 ТЕСТ БАЗОВЫХ ПАРАМЕТРОВ
   */
  async testBasicParams() {
    console.log('\n🧪 ТЕСТ 1: БАЗОВЫЕ ПАРАМЕТРЫ');
    console.log('─────────────────────────────────────────');

    const paramVariations = [
      // Вариант 1: Как в документации
      {
        name: 'Документация',
        params: {
          inputMint: this.testTokens.SOL,
          outputMint: this.testTokens.USDC,
          amount: '1000000000',
          slippageBps: '50',
          txVersion: 'V0'
        }
      },
      // Вариант 2: Без txVersion
      {
        name: 'Без txVersion',
        params: {
          inputMint: this.testTokens.SOL,
          outputMint: this.testTokens.USDC,
          amount: '1000000000',
          slippageBps: '50'
        }
      },
      // Вариант 3: С дополнительными параметрами
      {
        name: 'Расширенные параметры',
        params: {
          inputMint: this.testTokens.SOL,
          outputMint: this.testTokens.USDC,
          amount: '1000000000',
          slippageBps: '50',
          txVersion: 'V0',
          onlyDirectRoutes: 'false'
        }
      }
    ];

    for (const variation of paramVariations) {
      await this.testSingleVariation(variation);
    }
  }

  /**
   * 🔄 ТЕСТ ОДНОГО ВАРИАНТА
   */
  async testSingleVariation(variation) {
    const startTime = Date.now();

    try {
      console.log(`\n   📋 ${variation.name}:`);
      console.log(`      Параметры: ${JSON.stringify(variation.params, null, 6)}`);

      const response = await axios.get('https://transaction-v1.raydium.io/compute/swap-base-in', {
        params: variation.params,
        timeout: 10000,
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'Raydium-Debug/1.0'
        }
      });

      const responseTime = Date.now() - startTime;

      console.log(`      ✅ Ответ получен за ${responseTime}ms`);
      console.log(`      📊 Статус: ${response.status}`);
      console.log(`      📦 Данные: ${JSON.stringify(response.data, null, 6).substring(0, 200)}...`);

      if (response.data && response.data.data && response.data.data.outAmount) {
        const outputAmount = parseFloat(response.data.data.outAmount) / 1e6;
        console.log(`      💰 Результат: 1 SOL → ${outputAmount.toFixed(2)} USDC`);
      }

    } catch (error) {
      const responseTime = Date.now() - startTime;
      console.log(`      ❌ Ошибка за ${responseTime}ms: ${error.message}`);

      if (error.response) {
        console.log(`      📊 HTTP статус: ${error.response.status}`);
        console.log(`      📦 Ответ: ${JSON.stringify(error.response.data, null, 6)}`);
      }
    }
  }

  /**
   * 🔄 ТЕСТ АЛЬТЕРНАТИВНЫХ ENDPOINTS
   */
  async testAlternativeEndpoints() {
    console.log('\n🔄 ТЕСТ 2: АЛЬТЕРНАТИВНЫЕ ENDPOINTS');
    console.log('─────────────────────────────────────────');

    const endpoints = [
      'https://transaction-v1.raydium.io/compute/swap-base-in',
      'https://api.raydium.io/v2/ammV3/ammPools',
      'https://api.raydium.io/v2/main/price',
      'https://api.raydium.io/v2/sdk/liquidity/mainnet.json'
    ];

    for (const endpoint of endpoints) {
      await this.testEndpoint(endpoint);
    }
  }

  /**
   * 🌐 ТЕСТ ENDPOINT
   */
  async testEndpoint(endpoint) {
    const startTime = Date.now();

    try {
      console.log(`\n   🌐 ${endpoint}:`);

      const response = await axios.get(endpoint, {
        timeout: 10000,
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'Raydium-Debug/1.0'
        }
      });

      const responseTime = Date.now() - startTime;
      console.log(`      ✅ Доступен за ${responseTime}ms`);
      console.log(`      📊 Статус: ${response.status}`);

      if (response.data) {
        const dataStr = JSON.stringify(response.data).substring(0, 100);
        console.log(`      📦 Данные: ${dataStr}...`);
      }

    } catch (error) {
      const responseTime = Date.now() - startTime;
      console.log(`      ❌ Недоступен за ${responseTime}ms: ${error.message}`);
    }
  }

  /**
   * 🔍 ПРОВЕРКА ДОСТУПНОСТИ API
   */
  async testAPIAvailability() {
    console.log('\n🔍 ТЕСТ 3: ПРОВЕРКА ДОСТУПНОСТИ API');
    console.log('─────────────────────────────────────────');

    // Проверяем базовый домен
    await this.testBaseDomain();

    // Проверяем статус API
    await this.testAPIStatus();
  }

  /**
   * 🏠 ТЕСТ БАЗОВОГО ДОМЕНА
   */
  async testBaseDomain() {
    const domains = [
      'https://transaction-v1.raydium.io',
      'https://api.raydium.io',
      'https://raydium.io'
    ];

    for (const domain of domains) {
      const startTime = Date.now();

      try {
        const response = await axios.get(domain, {
          timeout: 5000,
          headers: {
            'User-Agent': 'Raydium-Debug/1.0'
          }
        });

        const responseTime = Date.now() - startTime;
        console.log(`   ✅ ${domain}: доступен за ${responseTime}ms`);

      } catch (error) {
        const responseTime = Date.now() - startTime;
        console.log(`   ❌ ${domain}: недоступен за ${responseTime}ms`);
      }
    }
  }

  /**
   * 📊 ТЕСТ СТАТУСА API
   */
  async testAPIStatus() {
    try {
      console.log('\n   📊 Проверка статуса API...');

      // Пробуем получить информацию о версии API
      const response = await axios.get('https://transaction-v1.raydium.io/health', {
        timeout: 5000
      });

      console.log(`   ✅ API статус: ${JSON.stringify(response.data)}`);

    } catch (error) {
      console.log(`   ⚠️  Статус API недоступен: ${error.message}`);
    }
  }
}

// Запуск отладки
async function main() {
  const apiDebugger = new RaydiumAPIDebugger();
  await apiDebugger.debugRaydiumAPI();

  console.log('\n🔧 РЕКОМЕНДАЦИИ:');
  console.log('   1. Проверьте правильность параметров');
  console.log('   2. Убедитесь что API доступен');
  console.log('   3. Попробуйте альтернативные endpoints');
  console.log('   4. Используйте Raydium SDK v2 вместо прямых API вызовов');
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = RaydiumAPIDebugger;
