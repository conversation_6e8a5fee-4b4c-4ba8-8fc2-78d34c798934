/**
 * 🔍 ПРОВЕРКА СИСТЕМЫ НА ОШИБКИ И ИСПРАВЛЕНИЯ
 */

import { 
  DEX_CONFIG, 
  TOKEN_CONFIG, 
  ARBITRAGE_PAIRS,
  getActiveDEXes,
  getActiveTokens,
  getTokenBySymbol,
  getDEXByName,
  EXPANSION_STATS
} from './dex-config.js';

class ErrorChecker {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.fixes = [];
    
    console.log('🔍 STARTING COMPREHENSIVE ERROR CHECK');
    console.log('═══════════════════════════════════════════════');
  }

  /**
   * ✅ ПРОВЕРКА КОНФИГУРАЦИИ DEX
   */
  checkDEXConfig() {
    console.log('\n🔧 CHECKING DEX CONFIGURATION...');
    
    const dexes = Object.entries(DEX_CONFIG);
    const activeDEXes = getActiveDEXes();
    
    // Проверяем количество DEX
    if (dexes.length !== 8) {
      this.errors.push(`Expected 8 DEXes, found ${dexes.length}`);
    } else {
      console.log('✅ DEX count: 8/8 correct');
    }
    
    // Проверяем активные DEX
    if (activeDEXes.length !== 8) {
      this.errors.push(`Expected 8 active DEXes, found ${activeDEXes.length}`);
    } else {
      console.log('✅ Active DEXes: 8/8 correct');
    }
    
    // Проверяем каждый DEX
    dexes.forEach(([name, config]) => {
      if (!config.name || !config.type || !config.programId) {
        this.errors.push(`DEX ${name} missing required fields`);
      }
      
      if (typeof config.fees !== 'number') {
        this.errors.push(`DEX ${name} fees must be a number`);
      }
      
      if (!['active', 'pending'].includes(config.status)) {
        this.errors.push(`DEX ${name} invalid status: ${config.status}`);
      }
    });
    
    console.log(`✅ DEX validation completed`);
  }

  /**
   * 💰 ПРОВЕРКА КОНФИГУРАЦИИ ТОКЕНОВ
   */
  checkTokenConfig() {
    console.log('\n💰 CHECKING TOKEN CONFIGURATION...');
    
    const tokens = Object.entries(TOKEN_CONFIG);
    const activeTokens = getActiveTokens();
    
    // Проверяем количество токенов
    if (tokens.length !== 15) {
      this.errors.push(`Expected 15 tokens, found ${tokens.length}`);
    } else {
      console.log('✅ Token count: 15/15 correct');
    }
    
    // Проверяем активные токены
    if (activeTokens.length !== 15) {
      this.errors.push(`Expected 15 active tokens, found ${activeTokens.length}`);
    } else {
      console.log('✅ Active tokens: 15/15 correct');
    }
    
    // Проверяем каждый токен
    tokens.forEach(([symbol, config]) => {
      if (!config.name || !config.symbol || !config.mint) {
        this.errors.push(`Token ${symbol} missing required fields`);
      }
      
      if (typeof config.decimals !== 'number' || config.decimals < 0) {
        this.errors.push(`Token ${symbol} invalid decimals: ${config.decimals}`);
      }
      
      if (typeof config.price_usd !== 'number' || config.price_usd <= 0) {
        this.errors.push(`Token ${symbol} invalid price: ${config.price_usd}`);
      }
      
      if (!['tier1', 'tier2', 'tier3'].includes(config.liquidity_tier)) {
        this.errors.push(`Token ${symbol} invalid tier: ${config.liquidity_tier}`);
      }
      
      if (!['active', 'pending'].includes(config.status)) {
        this.errors.push(`Token ${symbol} invalid status: ${config.status}`);
      }
    });
    
    console.log(`✅ Token validation completed`);
  }

  /**
   * 🎯 ПРОВЕРКА АРБИТРАЖНЫХ ПАР
   */
  checkArbitragePairs() {
    console.log('\n🎯 CHECKING ARBITRAGE PAIRS...');
    
    if (!Array.isArray(ARBITRAGE_PAIRS)) {
      this.errors.push('ARBITRAGE_PAIRS must be an array');
      return;
    }
    
    console.log(`📊 Total pairs: ${ARBITRAGE_PAIRS.length}`);
    
    ARBITRAGE_PAIRS.forEach((pair, index) => {
      if (!pair.token1 || !pair.token2) {
        this.errors.push(`Pair ${index}: missing token1 or token2`);
      }
      
      if (!getTokenBySymbol(pair.token1)) {
        this.errors.push(`Pair ${index}: token1 '${pair.token1}' not found in TOKEN_CONFIG`);
      }
      
      if (!getTokenBySymbol(pair.token2)) {
        this.errors.push(`Pair ${index}: token2 '${pair.token2}' not found in TOKEN_CONFIG`);
      }
      
      if (![1, 2, 3].includes(pair.tier)) {
        this.errors.push(`Pair ${index}: invalid tier ${pair.tier}`);
      }
      
      if (typeof pair.min_volume_usd !== 'number' || pair.min_volume_usd <= 0) {
        this.errors.push(`Pair ${index}: invalid min_volume_usd ${pair.min_volume_usd}`);
      }
    });
    
    console.log(`✅ Arbitrage pairs validation completed`);
  }

  /**
   * 📊 ПРОВЕРКА СТАТИСТИКИ
   */
  checkStats() {
    console.log('\n📊 CHECKING STATISTICS...');
    
    const stats = EXPANSION_STATS;
    
    // Проверяем текущие значения
    if (stats.current.dexes !== 8) {
      this.errors.push(`Stats current dexes should be 8, got ${stats.current.dexes}`);
    }
    
    if (stats.current.tokens !== 15) {
      this.errors.push(`Stats current tokens should be 15, got ${stats.current.tokens}`);
    }
    
    if (stats.current.pairs !== 120) {
      this.errors.push(`Stats current pairs should be 120, got ${stats.current.pairs}`);
    }
    
    // Проверяем проценты
    if (stats.progress.dexes_percent !== 100) {
      this.errors.push(`DEX progress should be 100%, got ${stats.progress.dexes_percent}%`);
    }
    
    if (stats.progress.tokens_percent !== 100) {
      this.errors.push(`Token progress should be 100%, got ${stats.progress.tokens_percent}%`);
    }
    
    if (stats.progress.pairs_percent !== 100) {
      this.errors.push(`Pairs progress should be 100%, got ${stats.progress.pairs_percent}%`);
    }
    
    console.log(`✅ Statistics validation completed`);
  }

  /**
   * 🔧 ПРОВЕРКА УТИЛИТАРНЫХ ФУНКЦИЙ
   */
  checkUtilityFunctions() {
    console.log('\n🔧 CHECKING UTILITY FUNCTIONS...');
    
    try {
      const activeDEXes = getActiveDEXes();
      const activeTokens = getActiveTokens();
      
      if (!Array.isArray(activeDEXes)) {
        this.errors.push('getActiveDEXes() should return an array');
      }
      
      if (!Array.isArray(activeTokens)) {
        this.errors.push('getActiveTokens() should return an array');
      }
      
      // Тестируем getTokenBySymbol
      const solToken = getTokenBySymbol('SOL');
      if (!solToken || solToken.symbol !== 'SOL') {
        this.errors.push('getTokenBySymbol("SOL") failed');
      }
      
      // Тестируем getDEXByName
      const jupiterDEX = getDEXByName('jupiter');
      if (!jupiterDEX || jupiterDEX.name !== 'Jupiter') {
        this.errors.push('getDEXByName("jupiter") failed');
      }
      
      console.log(`✅ Utility functions validation completed`);
      
    } catch (error) {
      this.errors.push(`Utility function error: ${error.message}`);
    }
  }

  /**
   * 🚀 ПРОВЕРКА ИМПОРТОВ И ЭКСПОРТОВ
   */
  checkImportsExports() {
    console.log('\n🚀 CHECKING IMPORTS/EXPORTS...');
    
    // Проверяем что все экспорты доступны
    const requiredExports = [
      'DEX_CONFIG',
      'TOKEN_CONFIG', 
      'ARBITRAGE_PAIRS',
      'getActiveDEXes',
      'getActiveTokens',
      'EXPANSION_STATS'
    ];
    
    requiredExports.forEach(exportName => {
      try {
        eval(exportName);
        console.log(`✅ Export ${exportName} available`);
      } catch (error) {
        this.errors.push(`Export ${exportName} not available: ${error.message}`);
      }
    });
  }

  /**
   * 🔍 ЗАПУСК ПОЛНОЙ ПРОВЕРКИ
   */
  async runFullCheck() {
    this.checkDEXConfig();
    this.checkTokenConfig();
    this.checkArbitragePairs();
    this.checkStats();
    this.checkUtilityFunctions();
    this.checkImportsExports();
    
    console.log('\n📋 ERROR CHECK RESULTS:');
    console.log('═══════════════════════════════════════════════');
    
    if (this.errors.length === 0) {
      console.log('🎉 NO ERRORS FOUND! System is working correctly.');
    } else {
      console.log(`❌ FOUND ${this.errors.length} ERRORS:`);
      this.errors.forEach((error, i) => {
        console.log(`   ${i+1}. ${error}`);
      });
    }
    
    if (this.warnings.length > 0) {
      console.log(`\n⚠️ WARNINGS (${this.warnings.length}):`);
      this.warnings.forEach((warning, i) => {
        console.log(`   ${i+1}. ${warning}`);
      });
    }
    
    console.log('\n🎯 SYSTEM STATUS:');
    console.log(`   DEXes: ${getActiveDEXes().length}/8 active`);
    console.log(`   Tokens: ${getActiveTokens().length}/15 active`);
    console.log(`   Pairs: ${ARBITRAGE_PAIRS.length} configured`);
    console.log(`   Errors: ${this.errors.length}`);
    console.log(`   Warnings: ${this.warnings.length}`);
    
    return {
      errors: this.errors,
      warnings: this.warnings,
      success: this.errors.length === 0
    };
  }
}

// 🔍 ЗАПУСК ПРОВЕРКИ
async function main() {
  const checker = new ErrorChecker();
  const result = await checker.runFullCheck();
  
  if (result.success) {
    console.log('\n✅ SYSTEM VALIDATION PASSED!');
    process.exit(0);
  } else {
    console.log('\n❌ SYSTEM VALIDATION FAILED!');
    process.exit(1);
  }
}

main().catch(console.error);
