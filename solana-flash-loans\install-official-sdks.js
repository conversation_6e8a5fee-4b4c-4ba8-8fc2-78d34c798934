/**
 * 📦 УСТАНОВКА ОФИЦИАЛЬНЫХ SDK
 * Скрипт для установки всех необходимых официальных SDK
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

class SDKInstaller {
  constructor() {
    this.requiredPackages = {
      // Solana Core
      '@solana/web3.js': '^1.87.6',
      '@solana/spl-token': '^0.3.9',
      
      // Marginfi (Flash Loans)
      '@mrgnlabs/marginfi-client-v2': '^2.0.0',
      '@mrgnlabs/mrgn-common': '^2.0.0',
      
      // Orca (AMM)
      '@orca-so/whirlpools-sdk': '^0.13.0',
      '@orca-so/common-sdk': '^0.5.0',
      
      // Raydium (AMM)
      '@raydium-io/raydium-sdk': '^1.3.1-beta.58',
      
      // Jupiter (Aggregator) - используется через API
      // '@jup-ag/core': '^6.0.0', // Если понадобится SDK
      
      // Utilities
      'node-fetch': '^3.3.2',
      'bn.js': '^5.2.1',
      'decimal.js': '^10.4.3'
    };
    
    this.optionalPackages = {
      // Дополнительные DEX SDK (если понадобятся)
      '@saberhq/token-utils': '^1.15.0',
      '@project-serum/anchor': '^0.28.0'
    };
    
    console.log('📦 SDK INSTALLER INITIALIZED');
  }

  /**
   * 📋 ПРОВЕРКА ТЕКУЩИХ ЗАВИСИМОСТЕЙ
   */
  checkCurrentDependencies() {
    console.log('\n📋 CHECKING CURRENT DEPENDENCIES...');
    
    try {
      const packageJsonPath = path.join(process.cwd(), 'package.json');
      
      if (!fs.existsSync(packageJsonPath)) {
        console.log('❌ package.json not found');
        return { exists: false, dependencies: {} };
      }
      
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
      const allDeps = {
        ...packageJson.dependencies || {},
        ...packageJson.devDependencies || {}
      };
      
      console.log('✅ package.json found');
      console.log(`📊 Current dependencies: ${Object.keys(allDeps).length}`);
      
      return { exists: true, dependencies: allDeps };
    } catch (error) {
      console.error('❌ Error reading package.json:', error.message);
      return { exists: false, dependencies: {} };
    }
  }

  /**
   * 🔍 АНАЛИЗ НЕДОСТАЮЩИХ ПАКЕТОВ
   */
  analyzeMissingPackages(currentDeps) {
    console.log('\n🔍 ANALYZING MISSING PACKAGES...');
    
    const missing = [];
    const outdated = [];
    const upToDate = [];
    
    for (const [packageName, requiredVersion] of Object.entries(this.requiredPackages)) {
      const currentVersion = currentDeps[packageName];
      
      if (!currentVersion) {
        missing.push({ name: packageName, version: requiredVersion });
      } else if (currentVersion !== requiredVersion) {
        outdated.push({ 
          name: packageName, 
          current: currentVersion, 
          required: requiredVersion 
        });
      } else {
        upToDate.push({ name: packageName, version: currentVersion });
      }
    }
    
    console.log(`📊 Analysis results:`);
    console.log(`   ❌ Missing: ${missing.length}`);
    console.log(`   ⚠️ Outdated: ${outdated.length}`);
    console.log(`   ✅ Up to date: ${upToDate.length}`);
    
    if (missing.length > 0) {
      console.log('\n❌ MISSING PACKAGES:');
      missing.forEach(pkg => {
        console.log(`   - ${pkg.name}@${pkg.version}`);
      });
    }
    
    if (outdated.length > 0) {
      console.log('\n⚠️ OUTDATED PACKAGES:');
      outdated.forEach(pkg => {
        console.log(`   - ${pkg.name}: ${pkg.current} → ${pkg.required}`);
      });
    }
    
    return { missing, outdated, upToDate };
  }

  /**
   * 📦 УСТАНОВКА ПАКЕТОВ
   */
  async installPackages(packages, type = 'required') {
    console.log(`\n📦 INSTALLING ${type.toUpperCase()} PACKAGES...`);
    
    if (packages.length === 0) {
      console.log('✅ No packages to install');
      return true;
    }
    
    try {
      const packageList = packages.map(pkg => 
        typeof pkg === 'string' ? pkg : `${pkg.name}@${pkg.version || pkg.required}`
      );
      
      console.log(`📋 Installing: ${packageList.join(', ')}`);
      
      // Используем npm install
      const command = `npm install ${packageList.join(' ')}`;
      console.log(`🔧 Running: ${command}`);
      
      execSync(command, { 
        stdio: 'inherit',
        cwd: process.cwd()
      });
      
      console.log('✅ Installation completed successfully');
      return true;
    } catch (error) {
      console.error('❌ Installation failed:', error.message);
      return false;
    }
  }

  /**
   * 🧪 ТЕСТИРОВАНИЕ УСТАНОВЛЕННЫХ ПАКЕТОВ
   */
  async testInstalledPackages() {
    console.log('\n🧪 TESTING INSTALLED PACKAGES...');
    
    const testResults = {};
    
    // Тест Solana Web3.js
    try {
      const { Connection, PublicKey } = await import('@solana/web3.js');
      const connection = new Connection('https://api.mainnet-beta.solana.com');
      testResults['@solana/web3.js'] = { success: true, version: 'imported' };
      console.log('✅ @solana/web3.js working');
    } catch (error) {
      testResults['@solana/web3.js'] = { success: false, error: error.message };
      console.log('❌ @solana/web3.js failed:', error.message);
    }
    
    // Тест SPL Token
    try {
      const { TOKEN_PROGRAM_ID } = await import('@solana/spl-token');
      testResults['@solana/spl-token'] = { success: true, version: 'imported' };
      console.log('✅ @solana/spl-token working');
    } catch (error) {
      testResults['@solana/spl-token'] = { success: false, error: error.message };
      console.log('❌ @solana/spl-token failed:', error.message);
    }
    
    // Тест Marginfi
    try {
      const { MarginfiClient } = await import('@mrgnlabs/marginfi-client-v2');
      testResults['@mrgnlabs/marginfi-client-v2'] = { success: true, version: 'imported' };
      console.log('✅ @mrgnlabs/marginfi-client-v2 working');
    } catch (error) {
      testResults['@mrgnlabs/marginfi-client-v2'] = { success: false, error: error.message };
      console.log('❌ @mrgnlabs/marginfi-client-v2 failed:', error.message);
    }
    
    // Тест Orca (опционально)
    try {
      const orcaSDK = await import('@orca-so/whirlpools-sdk');
      testResults['@orca-so/whirlpools-sdk'] = { success: true, version: 'imported' };
      console.log('✅ @orca-so/whirlpools-sdk working');
    } catch (error) {
      testResults['@orca-so/whirlpools-sdk'] = { success: false, error: error.message };
      console.log('⚠️ @orca-so/whirlpools-sdk not available (optional)');
    }
    
    // Тест Raydium (опционально)
    try {
      const raydiumSDK = await import('@raydium-io/raydium-sdk');
      testResults['@raydium-io/raydium-sdk'] = { success: true, version: 'imported' };
      console.log('✅ @raydium-io/raydium-sdk working');
    } catch (error) {
      testResults['@raydium-io/raydium-sdk'] = { success: false, error: error.message };
      console.log('⚠️ @raydium-io/raydium-sdk not available (optional)');
    }
    
    const successCount = Object.values(testResults).filter(r => r.success).length;
    const totalCount = Object.keys(testResults).length;
    
    console.log(`\n📊 Package test results: ${successCount}/${totalCount} working`);
    
    return testResults;
  }

  /**
   * 🚀 ПОЛНАЯ УСТАНОВКА И НАСТРОЙКА
   */
  async fullInstallation() {
    console.log('\n🚀 STARTING FULL SDK INSTALLATION...');
    console.log('═══════════════════════════════════════════════');
    
    // 1. Проверяем текущие зависимости
    const { exists, dependencies } = this.checkCurrentDependencies();
    
    if (!exists) {
      console.log('\n📝 Creating package.json...');
      const packageJson = {
        name: 'solana-arbitrage-system',
        version: '1.0.0',
        type: 'module',
        description: 'Solana DEX Arbitrage System with Flash Loans',
        main: 'index.js',
        scripts: {
          start: 'node index.js',
          test: 'node test.js'
        },
        dependencies: {},
        devDependencies: {}
      };
      
      fs.writeFileSync('package.json', JSON.stringify(packageJson, null, 2));
      console.log('✅ package.json created');
    }
    
    // 2. Анализируем недостающие пакеты
    const { missing, outdated } = this.analyzeMissingPackages(dependencies);
    
    // 3. Устанавливаем недостающие пакеты
    const toInstall = [...missing, ...outdated];
    if (toInstall.length > 0) {
      const success = await this.installPackages(toInstall, 'required');
      if (!success) {
        console.log('❌ Failed to install required packages');
        return false;
      }
    }
    
    // 4. Тестируем установленные пакеты
    const testResults = await this.testInstalledPackages();
    
    // 5. Показываем итоговый отчет
    this.showFinalReport(testResults);
    
    return true;
  }

  /**
   * 📋 ИТОГОВЫЙ ОТЧЕТ
   */
  showFinalReport(testResults) {
    console.log('\n📋 FINAL INSTALLATION REPORT');
    console.log('═══════════════════════════════════════════════');
    
    const corePackages = [
      '@solana/web3.js',
      '@solana/spl-token',
      '@mrgnlabs/marginfi-client-v2'
    ];
    
    const optionalPackages = [
      '@orca-so/whirlpools-sdk',
      '@raydium-io/raydium-sdk'
    ];
    
    console.log('🔧 CORE PACKAGES (Required):');
    corePackages.forEach(pkg => {
      const result = testResults[pkg];
      const status = result?.success ? '✅' : '❌';
      console.log(`   ${status} ${pkg}`);
    });
    
    console.log('\n🔌 OPTIONAL PACKAGES (DEX SDKs):');
    optionalPackages.forEach(pkg => {
      const result = testResults[pkg];
      const status = result?.success ? '✅' : '⚠️';
      console.log(`   ${status} ${pkg}`);
    });
    
    const coreWorking = corePackages.every(pkg => testResults[pkg]?.success);
    
    if (coreWorking) {
      console.log('\n🎉 INSTALLATION SUCCESSFUL!');
      console.log('✅ All core packages are working');
      console.log('🚀 Ready to run arbitrage system');
    } else {
      console.log('\n⚠️ INSTALLATION INCOMPLETE');
      console.log('❌ Some core packages failed');
      console.log('🔧 Manual installation may be required');
    }
    
    console.log('\n📚 NEXT STEPS:');
    console.log('1. Run: node official-sdk-integration.js');
    console.log('2. Test: node atomic-transactions.js');
    console.log('3. Execute: node smart-contracts.js');
  }
}

// 🚀 ЗАПУСК УСТАНОВКИ
async function main() {
  const installer = new SDKInstaller();
  await installer.fullInstallation();
}

// Запускаем только если файл выполняется напрямую
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export default SDKInstaller;
