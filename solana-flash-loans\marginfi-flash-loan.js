/**
 * 🥇 MARGINFI FLASH LOAN ИНТЕГРАЦИЯ - 0% КОМИССИЙ!
 * ═══════════════════════════════════════════════════════════════
 * 🎯 ЛУЧШИЙ ПРОТОКОЛ: MarginFi - БЕЗ КОМИССИЙ на flash loans
 * ✅ Официальный SDK с TypeScript поддержкой
 * ✅ Крупнейший TVL на Solana
 * ✅ Atomic transactions поддержка
 * 🔥 ПРИНУДИТЕЛЬНАЯ ОЧИСТКА КЭШ ДЛЯ BN.JS ИСПРАВЛЕНИЙ!
 * ═══════════════════════════════════════════════════════════════
 */

// 🔥 ЗАГРУЖАЕМ .env.solana ДЛЯ QUICKNODE RPC!
require('dotenv').config({ path: '.env.solana' });
console.log('🔧 .env.solana загружен для QuickNode RPC endpoints');

// 🔥 ЧИСТАЯ АРХИТЕКТУРА MARGINFI FLASH LOAN
console.log(`🔥 НОВАЯ АРХИТЕКТУРА: Используем чистые компоненты без патчей...`);

// 🔥 НИЗКОУРОВНЕВЫЙ ПОДХОД: НЕТ MARGINFI SDK!
// ❌ УДАЛЕНО: MarginfiClient, getConfig, NodeWallet
// ✅ ИСПОЛЬЗУЕМ: Прямые TransactionInstruction и PublicKey
const { PublicKey, Transaction, VersionedTransaction, Connection, SystemProgram, TransactionInstruction } = require('@solana/web3.js');
const BN = require('bn.js'); // 🔥 BN.JS ДЛЯ БОЛЬШИХ ЧИСЕЛ В SOLANA!
// 🔥 ИСПРАВЛЕНО: Используем правильный импорт Jupiter Swap Instructions (БЕЗ деструктуризации!)
const JupiterSwapInstructions = require('../jupiter-swap-instructions');
// 🧹 АВТОМАТИЧЕСКАЯ ОЧИСТКА PHANTOM DEBT
const { PhantomDebtCleaner } = require('../phantom-debt-cleaner');

/**
 * 🔧 КРИТИЧЕСКАЯ НОРМАЛИЗАЦИЯ ИНСТРУКЦИЙ - ИСПРАВЛЕНИЕ ix.programId.equals ОШИБКИ
 * Проблема: некоторые SDK возвращают programId как строку, а не PublicKey
 * Решение: конвертируем все programId в объекты PublicKey с принудительной проверкой
 */
function normalizeInstructions(instructions) {
  console.log(`🔧 КРИТИЧЕСКАЯ НОРМАЛИЗАЦИЯ: Получено ${instructions?.length || 0} инструкций`);

  if (!Array.isArray(instructions)) {
    console.log(`❌ instructions не является массивом: ${typeof instructions}`);
    return [];
  }

  if (instructions.length === 0) {
    console.log(`⚠️ Пустой массив инструкций`);
    return [];
  }

  return instructions.map((ix, index) => {
    console.log(`\n🔍 КРИТИЧЕСКАЯ НОРМАЛИЗАЦИЯ ИНСТРУКЦИИ ${index + 1}:`);
    console.log(`   Тип: ${typeof ix}`);
    console.log(`   Конструктор: ${ix.constructor?.name}`);
    console.log(`   programId тип: ${typeof ix.programId}`);
    console.log(`   programId значение: ${ix.programId}`);

    try {
      // 🚨 КРИТИЧЕСКАЯ ПРОВЕРКА: Если это уже правильная TransactionInstruction, не трогаем её
      if (ix instanceof TransactionInstruction &&
          ix.programId instanceof PublicKey &&
          typeof ix.programId.equals === 'function') {
        console.log(`   ✅ Это уже правильная TransactionInstruction с корректным programId`);
        console.log(`   📊 Аккаунтов: ${ix.keys.length}, Data: ${ix.data.length} байт`);
        return ix;
      }

      // 🔥 НОВОЕ: Проверяем является ли это compiled instruction
      if (typeof ix.programIdIndex === 'number' && ix.accountKeyIndexes) {
        console.log(`   🔧 Это compiled instruction с programIdIndex: ${ix.programIdIndex}`);
        console.log(`   ❌ ОШИБКА: Compiled instructions нельзя нормализовать без accountKeys!`);
        console.log(`   💡 Compiled instructions должны обрабатываться ДО компиляции`);

        // Логируем детали для отладки
        console.error(`   Инструкция: ${JSON.stringify(ix, null, 2)}`);

        // 🔥 ИСПРАВЛЕНИЕ: Возвращаем пустую инструкцию вместо ошибки
        console.log(`   🔧 ИСПРАВЛЕНИЕ: Пропускаем compiled instruction`);
        return null; // Будет отфильтровано позже
      }

      console.log(`   🔧 НОРМАЛИЗАЦИЯ ТРЕБУЕТСЯ - создаем новую TransactionInstruction`);

      // Проверяем наличие обязательных полей
      if (!ix.programId) {
        console.error(`   ❌ Отсутствует programId`);
        console.error(`   Инструкция: ${JSON.stringify(ix, null, 2)}`);

        // 🔥 ИСПРАВЛЕНИЕ: Возвращаем null вместо ошибки
        console.log(`   🔧 ИСПРАВЛЕНИЕ: Пропускаем инструкцию без programId`);
        return null; // Будет отфильтровано позже
      }

      // Если programId не является PublicKey, конвертируем
      let programId = ix.programId;
      console.log(`   🔄 Конвертируем programId из ${typeof programId}`);

      if (typeof programId === 'string') {
        programId = new PublicKey(programId);
        console.log(`   ✅ Создан PublicKey из строки: ${programId.toString()}`);
      } else if (!(programId instanceof PublicKey)) {
        // Если это объект с toString методом
        programId = new PublicKey(programId.toString());
        console.log(`   ✅ Создан PublicKey из объекта: ${programId.toString()}`);
      }

      // 🚨 КРИТИЧЕСКАЯ ДИАГНОСТИКА: ПРОВЕРЯЕМ ВСЕ ВОЗМОЖНЫЕ ПОЛЯ С АККАУНТАМИ
      console.log(`   🔍 ДИАГНОСТИКА ПОЛЕЙ ИНСТРУКЦИИ:`);
      console.log(`     ix.keys: ${ix.keys ? `массив ${ix.keys.length}` : 'отсутствует'}`);
      console.log(`     ix.accounts: ${ix.accounts ? `массив ${ix.accounts.length}` : 'отсутствует'}`);
      console.log(`     ix.accountMetas: ${ix.accountMetas ? `массив ${ix.accountMetas.length}` : 'отсутствует'}`);
      console.log(`     ix.accountKeys: ${ix.accountKeys ? `массив ${ix.accountKeys.length}` : 'отсутствует'}`);
      console.log(`     Все поля ix:`, Object.keys(ix));

      // Нормализуем keys (проверяем ВСЕ возможные форматы)
      let keys = [];
      if (ix.keys && Array.isArray(ix.keys) && ix.keys.length > 0) {
        console.log(`   🔄 Используем ix.keys (${ix.keys.length} ключей)`);
        keys = ix.keys;
      } else if (ix.accounts && Array.isArray(ix.accounts) && ix.accounts.length > 0) {
        console.log(`   🔄 Используем ix.accounts (${ix.accounts.length} аккаунтов)`);
        keys = ix.accounts;
      } else if (ix.accountMetas && Array.isArray(ix.accountMetas) && ix.accountMetas.length > 0) {
        console.log(`   🔄 Используем ix.accountMetas (${ix.accountMetas.length} мета-аккаунтов)`);
        keys = ix.accountMetas;
      } else if (ix.accountKeys && Array.isArray(ix.accountKeys) && ix.accountKeys.length > 0) {
        console.log(`   🔄 Используем ix.accountKeys (${ix.accountKeys.length} ключей аккаунтов)`);
        keys = ix.accountKeys.map(key => ({
          pubkey: key,
          isSigner: false,
          isWritable: false
        }));
      } else {
        console.log(`   🚨 КРИТИЧЕСКАЯ ПРОБЛЕМА: НЕТ АККАУНТОВ В ИНСТРУКЦИИ!`);
        console.log(`   💡 Это может быть setup инструкция или инструкция без аккаунтов`);
        console.log(`   🔧 Проверяем, является ли это валидной инструкцией...`);

        // Если это уже TransactionInstruction, возвращаем как есть
        if (ix instanceof TransactionInstruction) {
          console.log(`   ✅ Это уже TransactionInstruction, возвращаем без изменений`);
          return ix;
        }

        keys = [];
      }

      const normalizedKeys = keys.map((key, keyIndex) => {
        console.log(`     Ключ ${keyIndex + 1}: pubkey=${typeof key.pubkey}, isSigner=${key.isSigner}, isWritable=${key.isWritable}`);
        return {
          pubkey: key.pubkey instanceof PublicKey ? key.pubkey : new PublicKey(key.pubkey.toString()),
          isSigner: Boolean(key.isSigner),
          isWritable: Boolean(key.isWritable)
        };
      });

      // Нормализуем data
      let data = Buffer.alloc(0);
      if (ix.data) {
        if (Buffer.isBuffer(ix.data)) {
          data = ix.data;
          console.log(`   ✅ data уже Buffer (${data.length} байт)`);
        } else if (typeof ix.data === 'string') {
          data = Buffer.from(ix.data, 'base64');
          console.log(`   ✅ data конвертирован из base64 (${data.length} байт)`);
        } else if (Array.isArray(ix.data)) {
          data = Buffer.from(ix.data);
          console.log(`   ✅ data конвертирован из массива (${data.length} байт)`);
        }
      }

      // Создаем правильную инструкцию
      const normalizedIx = new TransactionInstruction({
        keys: normalizedKeys,
        programId,
        data
      });

      console.log(`   ✅ СОЗДАН TransactionInstruction:`);
      console.log(`     programId: ${normalizedIx.programId.toString()}`);
      console.log(`     programId.equals доступен: ${typeof normalizedIx.programId.equals}`);
      console.log(`     keys: ${normalizedIx.keys.length}`);
      console.log(`     data: ${normalizedIx.data.length} байт`);

      return normalizedIx;
    } catch (error) {
      console.error(`❌ КРИТИЧЕСКАЯ ОШИБКА нормализации инструкции ${index + 1}: ${error.message}`);
      console.error(`   Инструкция:`, JSON.stringify(ix, null, 2));
      throw error;
    }
  }).filter(ix => ix !== null); // 🔥 ИСПРАВЛЕНИЕ: Фильтруем null значения
}

class MarginfiFlashLoan {
  constructor(connection, wallet) {
    this.connection = connection;
    this.wallet = wallet;
    this.client = null;
    this.marginfiAccount = null;
    // 🚫 FALLBACK РЕЖИМ ЗАПРЕЩЕН - ТОЛЬКО РЕАЛЬНЫЕ MARGINFI FLASH LOANS!

    // 🚀 РЕАЛЬНЫЙ РЕЖИМ!
    this.fastMode = true;
    this.enableBundleSimulation = false; // 🔥 СИМУЛЯЦИЯ ПОЛНОСТЬЮ УДАЛЕНА!
    this.enableSimulation = false; // 🔥 ВСЯ СИМУЛЯЦИЯ ПОЛНОСТЬЮ УДАЛЕНА!
    console.log('🚀 РЕАЛЬНЫЙ РЕЖИМ ВКЛЮЧЕН В MARGINFI CONSTRUCTOR!');

    // ✅ ИНИЦИАЛИЗИРУЕМ JUPITER SWAP INSTRUCTIONS С МУЛЬТИХОП ПОДДЕРЖКОЙ
    this.jupiterSwapInstructions = new JupiterSwapInstructions(connection, wallet);

    // 🧹 ИНИЦИАЛИЗИРУЕМ АВТОМАТИЧЕСКУЮ ОЧИСТКУ PHANTOM DEBT
    this.phantomDebtCleaner = null; // Будет инициализирован после создания client

    // 🔥 ВКЛЮЧАЕМ МУЛЬТИХОП РЕЖИМ
    this.enableMultihop = true;
    this.multihopOptimization = true;
    console.log('🔥 МУЛЬТИХОП РЕЖИМ ВКЛЮЧЕН В MARGINFI FLASH LOAN!');

    // 🎯 СТАТИСТИКА FLASH LOANS С МУЛЬТИХОП
    this.stats = {
      totalLoans: 0,
      successfulLoans: 0,
      failedLoans: 0,
      totalVolume: 0,
      totalFees: 0, // ВСЕГДА 0 для MarginFi!
      multihopLoans: 0, // 🔥 НОВАЯ СТАТИСТИКА
      classicLoans: 0,
      avgMultihopSavings: 0 // Экономия от мультихоп
    };

    // 🚀 ПРЕДПОДГОТОВЛЕННЫЕ ТРАНЗАКЦИИ (21-53мс скорость!)
    this.preparedTransactions = new Map(); // Кэш готовых транзакций
    this.templateInstructions = new Map(); // Шаблоны инструкций
    this.popularTokens = [
      'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // USDC
      'So********************************111111112',   // SOL
      '7vfCXTUXx5WJV5JADk17DUJ4ksgau7utNKj4b963voxs',   // WETH
      'mSoLzYCxHdYgdzU16g5QSh3i5K3z3KZK7ytfqcJm7So'    // mSOL
    ];
    this.preparationInProgress = new Set(); // Предотвращение дублирования
    this.cachedBlockhash = null;
    this.cachedBanks = new Map();
    this.lastCacheUpdate = 0;
    this.CACHE_TTL = 30000; // 30 секунд

    // 🚀 НОВЫЕ ОПТИМИЗИРОВАННЫЕ КЭШИ ДЛЯ УСКОРЕНИЯ
    this.instructionCache = new Map(); // Кэш borrow/repay инструкций
    this.bankDataCache = new Map(); // Кэш bank data с TTL
    this.blockhashCache = { value: null, timestamp: 0, ttl: 5000 }; // 5 секунд TTL
    this.performanceStats = {
      cacheHits: 0,
      cacheMisses: 0,
      avgCreationTime: 0,
      fastestTime: Infinity,
      slowestTime: 0
    };

    // 🚀 ИСПРАВЛЕНИЕ: НАСТРОЙКИ УЖЕ УСТАНОВЛЕНЫ ВЫШЕ - НЕ ПЕРЕЗАПИСЫВАЕМ!
    // this.enableBundleSimulation уже установлен в false выше
    // this.fastMode уже установлен в true выше

    // 🎯 НОВОЕ: ADDRESS LOOKUP TABLE ДЛЯ МАКСИМАЛЬНОЙ ОПТИМИЗАЦИИ РАЗМЕРА ТРАНЗАКЦИЙ
    this.flashLoanALT = null;
    this.altCache = new Map(); // Кэш ALT для разных токенов
    this.ALT_TTL = 300000; // 5 минут TTL для ALT
    this.altAddresses = new Set(); // Уникальные адреса для ALT

    console.log('🥇 MarginFi Flash Loan Manager инициализирован (0.09% комиссий!)');
    console.log('🪐 Jupiter Swap Instructions готов к созданию арбитражных инструкций');
    console.log('🎯 Address Lookup Tables готовы для минимизации размера транзакций');
    console.log('🚀 Предподготовленные транзакции: 21-53мс скорость!');
    console.log(`⚡ Bundle simulation: ${this.enableBundleSimulation ? 'включена' : 'отключена'}`);
    console.log(`🏃 Быстрый режим: ${this.fastMode ? 'включен' : 'отключен'}`);
  }

  /**
   * 💰 СОЗДАТЬ BUNDLE TIP ИНСТРУКЦИЮ (СОБСТВЕННАЯ РЕАЛИЗАЦИЯ)
   */
  makeBundleTipIx(payerPublicKey, tipLamports = 1000) {
    // Jito tip адрес (официальный)
    const jitoTipAccounts = [
      'Cw8CFyM9FkoMi7K7Crf6HNQqf4uEMzpKw6QNghXLvLkY',
      'DttWaMuVvTiduZRnguLF7jNxTgiMBZ1hyAumKUiL2KRL',
      '96gYZGLnJYVFmbjzopPSU6QiEV5fGqZNyN9nmNhvrZU5',
      '3AVi9Tg9Uo68tJfuvoKvqKNWKkC5wPdSSdeBnizKZ6jT',
      'HFqU5x63VTqvQss8hp11i4wVV8bD44PvwucfZ2bU7gRe',
      'ADaUMid9yfUytqMBgopwjb2DTLSokTSzL1zt6iGPaS49',
      'ADuUkR4vqLUMWXxW9gh6D6L8pMSawimctcNZ5pGwDcEt',
      'DfXygSm4jCyNCybVYYK6DwvWqjKee8pbDmJGcLWNDXjh'
    ];

    // Выбираем случайный tip аккаунт
    const randomTipAccount = jitoTipAccounts[Math.floor(Math.random() * jitoTipAccounts.length)];

    console.log(`💰 Создаем bundle tip: ${tipLamports} lamports -> ${randomTipAccount}`);

    return SystemProgram.transfer({
      fromPubkey: payerPublicKey,
      toPubkey: new PublicKey(randomTipAccount),
      lamports: tipLamports
    });
  }

  /**
   * 🎯 СОЗДАНИЕ ADDRESS LOOKUP TABLE ДЛЯ МАКСИМАЛЬНОЙ ОПТИМИЗАЦИИ РАЗМЕРА ТРАНЗАКЦИЙ
   */
  async createFlashLoanALT(tokenMint) {
    try {
      console.log('🎯 Создание Address Lookup Table для Flash Loan оптимизации...');

      // Проверяем кэш
      const cacheKey = `alt_${tokenMint}`;
      const cached = this.altCache.get(cacheKey);
      if (cached && Date.now() - cached.timestamp < this.ALT_TTL) {
        console.log('✅ Используем кэшированный ALT');
        return cached.alt;
      }

      // 🔥 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: ПРАВИЛЬНЫЙ ДОСТУП К БАНКАМ ПО НОВОЙ ДОКУМЕНТАЦИИ
      console.log(`🔍 Поиск банка для токена: ${tokenMint}`);

      const banks = this.client.banks;
      if (!banks || banks.size === 0) {
        throw new Error('MarginFi банки недоступны');
      }

      let bank = null;

      // Ищем банк по mint адресу
      for (const [bankAddress, bankData] of banks) {
        if (bankData.mint.toString() === tokenMint) {
          bank = bankData;
          break;
        }
      }

      if (!bank) {
        throw new Error(`Bank not found for token: ${tokenMint}`);
      }

      console.log(`✅ Банк найден: ${bank.address.toString()}`);
      console.log(`📊 Банк информация:`);
      console.log(`   Mint: ${bank.mint.toString()}`);
      console.log(`   Symbol: ${bank.tokenSymbol || 'N/A'}`);
      console.log(`   Decimals: ${bank.mintDecimals || 'N/A'}`);


      // Собираем все адреса для ALT
      const addresses = [
        this.wallet.publicKey,                    // Пользователь
        bank.address,                            // Банк токена
        bank.config.mint,                        // Mint токена
        this.client.config.groupPk,              // MarginFi Group
        SystemProgram.programId,                 // System Program
        new PublicKey('********************************'), // System Program
        new PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA'), // Token Program
        new PublicKey('ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL'), // Associated Token Program
        // Jupiter Program IDs
        new PublicKey('JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4'), // Jupiter V6
        new PublicKey('JUP4Fb2cqiRUcaTHdrPC8h2gNsA2ETXiPDD33WcGuJB'), // Jupiter V4
      ];

      // Добавляем адреса MarginFi аккаунта если есть
      if (this.marginfiAccount && this.marginfiAccount.address) {
        addresses.push(this.marginfiAccount.address);
      }

      // Создаем ALT
      const slot = await this.connection.getSlot();
      const [createALTIx, altAddress] = await this.connection.getAddressLookupTable(
        this.wallet.publicKey,
        this.wallet.publicKey,
        slot
      );

      console.log(`🎯 ALT адрес: ${altAddress.toString()}`);
      console.log(`🔑 Добавляем ${addresses.length} адресов в ALT`);

      // Кэшируем результат
      this.altCache.set(cacheKey, {
        alt: altAddress,
        addresses: addresses,
        timestamp: Date.now()
      });

      return altAddress;

    } catch (error) {
      console.log(`❌ Ошибка создания ALT: ${error.message}`);
      return null;
    }
  }

  /**
   * 🛡️ ОПТИМАЛЬНЫЙ RATE LIMITER ДЛЯ HELIUS FREE TIER
   */
  wrapWithRateLimit(originalMethod, delayMs) {
    let lastRequestTime = 0;
    let requestCount = 0;
    let windowStart = Date.now();

    // Helius Free Tier: 10 RPS максимум
    const maxRequestsPerSecond = parseInt(process.env.HELIUS_RPC_LIMIT) || 10;
    const requestDelay = parseInt(process.env.HELIUS_REQUEST_DELAY) || 100; // 100ms = 10 RPS

    return async (...args) => {
      const now = Date.now();

      // Сброс счетчика каждую секунду
      if (now - windowStart >= 1000) {
        requestCount = 0;
        windowStart = now;
      }

      // Проверяем лимит RPS
      if (requestCount >= maxRequestsPerSecond) {
        const waitTime = 1000 - (now - windowStart);
        console.log(`🚀 Helius RPS лимит (${maxRequestsPerSecond}), ждем ${waitTime}мс...`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
        requestCount = 0;
        windowStart = Date.now();
      }

      // Минимальная задержка между запросами
      const timeSinceLastRequest = now - lastRequestTime;
      if (timeSinceLastRequest < requestDelay) {
        const waitTime = requestDelay - timeSinceLastRequest;
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }

      requestCount++;
      lastRequestTime = Date.now();

      console.log(`📊 Helius запрос ${requestCount}/${maxRequestsPerSecond} в текущей секунде`);
      return originalMethod(...args);
    };
  }

  /**
   * 🔧 ПРАВИЛЬНАЯ ИНИЦИАЛИЗАЦИЯ MARGINFI CLIENT (ПО ОФИЦИАЛЬНОЙ ДОКУМЕНТАЦИИ!)
   */
  async initialize(existingConnection = null, rateLimiter = null) {
    // 🔥 УБИРАЕМ ВСЮ ХУЙНЮ С RETRY! БЫСТРАЯ ИНИЦИАЛИЗАЦИЯ!
    console.log(`🚀 БЫСТРАЯ MarginFi инициализация БЕЗ RETRY!`);

    const result = await this.performInitialization(existingConnection, rateLimiter);
    if (result) {
      console.log(`✅ MarginFi инициализирован успешно!`);
      return true;
    }

    return false;
  }

  /**
   * 🔧 ПРОСТАЯ ИНИЦИАЛИЗАЦИЯ БЕЗ RETRY (ИСПРАВЛЕНО!)
   */
  async performInitialization(existingConnection = null, rateLimiter = null) {
    try {
      console.log('🚀 ПРОСТАЯ MarginFi инициализация БЕЗ RETRY!');

      // ✅ ДОБАВЛЯЕМ RATE LIMITER ДЛЯ ИЗБЕЖАНИЯ 429 ОШИБОК
      this.rateLimiter = rateLimiter;

      // 🔥 ИСПОЛЬЗУЕМ ОФИЦИАЛЬНЫЙ getConfig ИЗ MARGINFI SDK!
      console.log('🔥 ПОЛУЧАЕМ ОФИЦИАЛЬНЫЙ CONFIG ИЗ MARGINFI SDK...');
      const config = getConfig("production");
      console.log('✅ Официальный MarginFi config получен:', {
        environment: config.environment,
        programId: config.programId?.toString(),
        groupPk: config.groupPk?.toString()
      });

      // 🔥 ИСПОЛЬЗУЕМ QUICKNODE ИЗ .env.solana!
      const quicknodeUrl = process.env.QUICKNODE2_RPC_URL || 'https://billowing-empty-patron.solana-mainnet.quiknode.pro/c79212a7508ec6a37353793d9be93c917898e858/';
      console.log(`🔍 QUICKNODE2_RPC_URL: ${quicknodeUrl.substring(0, 50)}...`);

      // 🚨 ПРОСТОЙ CONNECTION БЕЗ КАСТОМНЫХ АГЕНТОВ!
      const { Connection } = require('@solana/web3.js');
      const connectionToUse = new Connection(quicknodeUrl, {
        commitment: 'confirmed',
        confirmTransactionInitialTimeout: 60000,  // ✅ 60 секунд для MarginFi
        disableRetryOnRateLimit: false
      });

      console.log(`🔧 MarginFi connection создан: ${quicknodeUrl.substring(0, 50)}...`);
      const connection = connectionToUse;

      // ✅ СОЗДАЕМ NODEWALLET ПРАВИЛЬНО
      console.log('👤 Создаем NodeWallet...');
      let nodeWallet;
      if (this.wallet.payer) {
        console.log('🔧 Используем wallet.payer для NodeWallet');
        nodeWallet = new NodeWallet(this.wallet.payer);
      } else if (this.wallet.publicKey && this.wallet.secretKey) {
        console.log('🔧 Используем wallet как Keypair для NodeWallet');
        nodeWallet = new NodeWallet(this.wallet);
      } else {
        console.log('🔧 Создаем Keypair из secretKey для NodeWallet');
        const { Keypair } = require('@solana/web3.js');
        const keypair = Keypair.fromSecretKey(this.wallet.secretKey || this.wallet.payer?.secretKey);
        nodeWallet = new NodeWallet(keypair);
      }

      // 🚀 ПРОСТАЯ ИНИЦИАЛИЗАЦИЯ MARGINFI БЕЗ RETRY!
      console.log('🚀 ПРОСТАЯ инициализация MarginFi БЕЗ RETRY!');
      console.log(`🔧 Config: ${config ? 'OK' : 'NULL'}`);
      console.log(`🔧 NodeWallet: ${nodeWallet ? 'OK' : 'NULL'}`);
      console.log(`🔧 Connection: ${connection.rpcEndpoint}`);

      // 🔥 ПРОСТАЯ ИНИЦИАЛИЗАЦИЯ С readOnly ДЛЯ МИНИМИЗАЦИИ RPC ЗАПРОСОВ
      console.log('⏳ Инициализируем MarginfiClient.fetch() с readOnly...');
      const startTime = Date.now();

      this.client = await MarginfiClient.fetch(config, nodeWallet, connection, {
        readOnly: true, // 🔥 ТОЛЬКО ЧТЕНИЕ для минимизации запросов
        confirmOpts: {
          commitment: 'confirmed'
        }
      });

      const duration = Date.now() - startTime;
      console.log(`✅ MarginFi инициализирован за ${duration}мс!`);
      console.log(`🔍 Client type: ${this.client.constructor.name}`);
      console.log(`🔍 Client group: ${this.client.groupAddress?.toString()}`);
      console.log(`🔍 Banks count: ${this.client.banks?.size || 0}`);

      // ✅ УСТАНАВЛИВАЕМ isInitialized = true!
      this.isInitialized = true;
      console.log(`✅ isInitialized = ${this.isInitialized}`);

      if (this.client) {
        console.log('✅ MarginFi client инициализирован!');
        this.connection = connection;

        // 🔥 ПРОСТОЕ ПРОДОЛЖЕНИЕ ИНИЦИАЛИЗАЦИИ
        console.log('⚡ Загружаем существующий MarginFi account...');

        try {
          // 🎯 ИСПОЛЬЗУЕМ КОНКРЕТНЫЙ MARGINFI АККАУНТ
          const knownAccountAddress = '3dPECtRBPcYtcJG6wWDCr7VCyzxttDTjgGgo9gWC9vmU';
          const knownAuthority = 'bbTGcf2JUtznXJviHy4FokNvJNzu5q4aAML3yT3mQuV';

          console.log(`🎯 Загружаем конкретный MarginFi аккаунт: ${knownAccountAddress}`);
          console.log(`👤 Authority: ${knownAuthority}`);

          // Сначала пробуем загрузить все аккаунты для authority
          const existingAccounts = await this.client.getMarginfiAccountsForAuthority();
          console.log(`📊 Найдено аккаунтов для authority: ${existingAccounts.length}`);

          if (existingAccounts && existingAccounts.length > 0) {
            // Ищем конкретный аккаунт
            const targetAccount = existingAccounts.find(acc =>
              acc.address.toString() === knownAccountAddress
            );

            if (targetAccount) {
              this.marginfiAccount = targetAccount;
              await this.marginfiAccount.reload();
              console.log(`✅ Целевой MarginFi аккаунт найден и загружен: ${this.marginfiAccount.address.toString()}`);
            } else {
              // Используем первый доступный аккаунт
              this.marginfiAccount = existingAccounts[0];
              await this.marginfiAccount.reload();
              console.log(`✅ Используем первый доступный аккаунт: ${this.marginfiAccount.address.toString()}`);
              console.log(`⚠️ Целевой аккаунт ${knownAccountAddress} не найден в списке`);
            }
          } else {
            // Пробуем загрузить напрямую через MarginfiAccountWrapper
            console.log(`🔄 Пробуем загрузить аккаунт напрямую...`);
            const { MarginfiAccountWrapper } = require('@mrgnlabs/marginfi-client-v2');
            const { PublicKey } = require('@solana/web3.js');

            const accountPubkey = new PublicKey(knownAccountAddress);
            this.marginfiAccount = await MarginfiAccountWrapper.fetch(accountPubkey, this.client);

            if (this.marginfiAccount) {
              console.log(`✅ MarginFi аккаунт загружен напрямую: ${this.marginfiAccount.address.toString()}`);
            } else {
              throw new Error('Не удалось загрузить MarginFi аккаунт ни одним способом');
            }
          }
        } catch (loadError) {
          console.log(`❌ Ошибка загрузки аккаунта: ${loadError.message}`);
          throw new Error(`Не удалось загрузить MarginFi аккаунт: ${loadError.message}`);
        }

        console.log('✅ MarginFi полностью готов к работе!');
        this.isInitialized = true;
        return true;
      } else {
        throw new Error('MarginFi client не создан');
      }

    } catch (error) {
      console.error('❌ ОШИБКА MarginFi:', error.message);
      throw error;
    }
  }

  /**
   * ⚡ ПРЕДВАРИТЕЛЬНАЯ ПОДГОТОВКА ДЛЯ БЫСТРЫХ ТРАНЗАКЦИЙ (НОВОЕ!)
   */
  async prepareForFastTrading() {
    try {
      console.log('⚡ Подготовка для быстрых транзакций...');

      // ✅ ПРЕДВАРИТЕЛЬНО ПОЛУЧАЕМ BLOCKHASH С RATE LIMITER
      const startTime = Date.now();
      if (this.rateLimiter) {
        this.cachedBlockhash = await this.rateLimiter.executeWithRateLimit('RPC_READ', async () => {
          return await this.connection.getLatestBlockhash('processed');
        });
      } else {
        this.cachedBlockhash = await this.connection.getLatestBlockhash('processed');
      }
      console.log(`✅ Blockhash кэширован за ${Date.now() - startTime}мс`);

      // ✅ ПРЕДВАРИТЕЛЬНО ПОЛУЧАЕМ БАНКИ
      if (this.client) {
        const banksStartTime = Date.now();
        this.cachedBanks = this.client.banks;
        console.log(`✅ Банки кэшированы за ${Date.now() - banksStartTime}мс`);

        // ✅ ПРЕДВАРИТЕЛЬНО ПОЛУЧАЕМ USDC БАНК (САМЫЙ ПОПУЛЯРНЫЙ)
        this.cachedUsdcBank = this.client.getBankByTokenSymbol("USDC");
        if (this.cachedUsdcBank) {
          console.log(`✅ USDC банк предварительно загружен`);
        }

        // ✅ ПРЕДВАРИТЕЛЬНО ПОЛУЧАЕМ SOL БАНК
        this.cachedSolBank = this.client.getBankByTokenSymbol("SOL");
        if (this.cachedSolBank) {
          console.log(`✅ SOL банк предварительно загружен`);
        }
      }

      // ✅ ОТКЛЮЧАЕМ АВТОМАТИЧЕСКОЕ ОБНОВЛЕНИЕ BLOCKHASH - ЭКОНОМИМ RPC ЗАПРОСЫ!
      console.log(`🚫 Автоматическое обновление blockhash ОТКЛЮЧЕНО для экономии RPC запросов`);
      console.log(`💡 Blockhash будет обновляться только при необходимости (перед транзакциями)`);

      // Сохраняем ссылку для ручного обновления при необходимости
      this.blockhashUpdateInterval = null;

      console.log(`🚀 Система готова к транзакциям за < 14мс!`);

    } catch (error) {
      console.error(`❌ Ошибка подготовки: ${error.message}`);
    }
  }

  /**
   * 🔄 ОБНОВЛЕНИЕ BLOCKHASH ТОЛЬКО ПРИ НЕОБХОДИМОСТИ
   */
  async updateBlockhashIfNeeded() {
    try {
      // Проверяем возраст текущего blockhash
      const now = Date.now();
      const blockhashAge = this.cachedBlockhash ? (now - this.cachedBlockhash.timestamp) : Infinity;

      // Обновляем только если blockhash старше 10 секунд или отсутствует (СВЕЖИЙ ДЛЯ ТОРГОВЛИ!)
      if (blockhashAge > 10000 || !this.cachedBlockhash) {
        console.log(`🔄 Обновляем blockhash (возраст: ${Math.round(blockhashAge/1000)}с)`);

        // 🚫 BLOCKHASH ЗАПРОСЫ ОТКЛЮЧЕНЫ - ПОЛУЧАЕМ ТОЛЬКО ПЕРЕД ОТПРАВКОЙ!
        console.log('🚫 Blockhash запросы отключены - получаем только перед отправкой!');

        this.cachedBlockhash.timestamp = now;
        console.log(`✅ Blockhash обновлен: ${this.cachedBlockhash.blockhash.slice(0, 8)}...`);
      }

      return this.cachedBlockhash;
    } catch (error) {
      console.log(`⚠️ Ошибка обновления blockhash: ${error.message}`);
      return this.cachedBlockhash;
    }
  }

  /**
   * 🚀 ПРЕДПОДГОТОВКА ТРАНЗАКЦИЙ ДЛЯ ПОПУЛЯРНЫХ ТОКЕНОВ (21-53мс)
   */
  async preparePopularTokenTransactions() {
    try {
      console.log('🚀 Предподготовка транзакций для популярных токенов...');

      // 💰 ТРЕБОВАНИЕ: ТОЛЬКО $10,000 ЗАЙМ! (ИСПРАВЛЕНО ДЛЯ 53-BIT ЛИМИТА)
      const commonAmounts = [
        69230769,   // $10,000 в lamports (ИСПРАВЛЕНО: убрали 3 нуля для 53-bit безопасности)
      ];

      let preparedCount = 0;

      for (const tokenMint of this.popularTokens) {
        for (const amount of commonAmounts) {
          try {
            await this.prepareTransactionTemplate(tokenMint, amount);
            preparedCount++;
          } catch (error) {
            console.log(`⚠️ Не удалось подготовить ${tokenMint} ${amount}: ${error.message}`);
          }
        }
      }

      console.log(`✅ Предподготовлено ${preparedCount} шаблонов транзакций`);
      console.log(`🚀 Скорость выполнения: 21-53мс вместо 1099мс!`);

    } catch (error) {
      console.error(`❌ Ошибка предподготовки транзакций: ${error.message}`);
    }
  }

  /**
   * 🔧 ПОДГОТОВКА ШАБЛОНА ТРАНЗАКЦИИ
   */
  async prepareTransactionTemplate(tokenMint, amount) {
    // ✅ ИСПРАВЛЕНО: Сначала корректируем amount, потом создаем templateKey
    let adjustedAmount = amount;

    // Для SOL токенов используем меньшую сумму чтобы избежать "byte array longer than desired length"
    // 🔥 ИСПРАВЛЕНО: Конвертируем tokenMint в строку для сравнения
    const tokenMintStr = typeof tokenMint === 'string' ? tokenMint : tokenMint.toString();

    if (tokenMintStr === 'So********************************111111112' || // SOL
        tokenMintStr === 'mSoLzYCxHdYgdzU16g5QSh3i5K3z3KZK7ytfqcJm7So') { // mSOL
      adjustedAmount = Math.floor(amount / 1000); // Уменьшаем в 1000 раз для SOL токенов
      console.log(`🔧 SOL/mSOL токен обнаружен: уменьшаем amount с ${amount} до ${adjustedAmount}`);
    }

    const templateKey = `${tokenMintStr}_${adjustedAmount}`; // ✅ ИСПОЛЬЗУЕМ СКОРРЕКТИРОВАННЫЙ AMOUNT

    if (this.preparationInProgress.has(templateKey)) {
      return; // Уже готовится
    }

    this.preparationInProgress.add(templateKey);

    try {
      console.log(`🔧 Подготовка шаблона: ${tokenMint.slice(0, 8)}... ${adjustedAmount}`);

      // ✅ ИСПРАВЛЕНО: Безопасное получение банка для токена
      let bank;
      try {
        // 🔥 ИСПРАВЛЕНО: Конвертируем tokenMint в строку для сравнения
        const tokenMintStr = typeof tokenMint === 'string' ? tokenMint : tokenMint.toString();

        if (tokenMintStr === 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v' && this.cachedUsdcBank) {
          bank = this.cachedUsdcBank;
          console.log(`🏦 Используем кэшированный USDC банк`);
        } else if (tokenMintStr === 'So********************************111111112' && this.cachedSolBank) {
          bank = this.cachedSolBank;
          console.log(`🏦 Используем кэшированный SOL банк`);
        } else {
          console.log(`🔍 Ищем банк для токена: ${tokenMintStr.slice(0, 8)}...`);
          const mintPubkey = new PublicKey(tokenMint);
          bank = this.client.getBankByMint(mintPubkey);

          if (!bank) {
            // ✅ FALLBACK: Ищем банк в списке всех банков
            console.log(`🔄 Fallback: ищем банк в списке всех банков...`);
            for (const [address, bankData] of this.client.banks) {
              if (bankData.mint.equals(mintPubkey)) {
                bank = bankData;
                console.log(`✅ Найден банк через поиск: ${address.toString().slice(0, 8)}...`);
                break;
              }
            }
          }
        }

        if (!bank) {
          throw new Error(`Банк не найден для токена ${tokenMint.slice(0, 8)}...`);
        }

        console.log(`✅ Банк найден: ${bank.address.toString().slice(0, 8)}...`);

      } catch (bankError) {
        console.error(`❌ Ошибка получения банка: ${bankError.message}`);
        throw new Error(`Не удалось получить банк для ${tokenMint.slice(0, 8)}...: ${bankError.message}`);
      }

      // ✅ ИСПОЛЬЗУЕМ УЖЕ СКОРРЕКТИРОВАННЫЙ adjustedAmount ИЗ НАЧАЛА ФУНКЦИИ

      // ✅ ИСПРАВЛЕНО: Создаем простой flash loan без комиссии для тестирования
      console.log(`🔧 Создаем инструкции: amount=${adjustedAmount} (БЕЗ комиссии для тестирования)`);

      // 🔥 СОЗДАЕМ ПРАВИЛЬНЫЕ FLASH LOAN ИНСТРУКЦИИ ДЛЯ ПРЕДПОДГОТОВКИ
      console.log(`🚀 СОЗДАЕМ ПРАВИЛЬНЫЕ FLASH LOAN ИНСТРУКЦИИ (БЕЗ ДОЛГОВ)...`);

      try {
        // 🔥 УДАЛЕНО! makeBorrowIx и makeRepayIx БОЛЬШЕ НЕ ИСПОЛЬЗУЮТСЯ!
        // ИСПОЛЬЗУЕМ ТОЛЬКО buildFlashLoanTx!
        console.log(`✅ ИСПОЛЬЗУЕМ ТОЛЬКО buildFlashLoanTx - makeBorrowIx/makeRepayIx УДАЛЕНЫ!`);

        // 🔥 УДАЛЕНО! prepareBorrowIx и prepareRepayIx больше не существуют!
        // ИСПОЛЬЗУЕМ ТОЛЬКО buildFlashLoanTx!

        console.log(`✅ makeBorrowIx/makeRepayIx УДАЛЕНЫ - используем buildFlashLoanTx!`);
        console.log(`🚫 ШАБЛОНЫ БОЛЬШЕ НЕ НУЖНЫ - buildFlashLoanTx создает все автоматически!`);

      } catch (marginfiError) {
        console.log(`❌ Ошибка создания Flash Loan инструкций: ${marginfiError.message}`);
        throw new Error(`Flash Loan предподготовка провалена: ${marginfiError.message}`);
      }

    } catch (error) {
      console.log(`❌ Ошибка подготовки шаблона ${templateKey}: ${error.message}`);
    } finally {
      this.preparationInProgress.delete(templateKey);
    }
  }

  /**
   * ✅ ИСПРАВЛЕНО: ИСПОЛЬЗУЕМ ОФИЦИАЛЬНУЮ ЛОГИКУ MARGINFI SDK
   * Проблема была НЕ в endIndex, а в дополнительных инструкциях!
   */
  async buildFlashLoanTxWithAutoEndIndex(args, lookupTables) {
    try {
      console.log(`🚨 КРИТИЧЕСКИЙ ВЫЗОВ: buildFlashLoanTxWithAutoEndIndex ЗАПУЩЕН!`);
      console.log(`🔧 ОФИЦИАЛЬНАЯ ЛОГИКА MARGINFI SDK...`);
      console.log(`📊 args.ixs.length: ${args.ixs.length}`);

      // ✅ АНАЛИЗ ОФИЦИАЛЬНОГО КОДА MARGINFI:
      // const endIndex = args.ixs.length + 1;  ← ОФИЦИАЛЬНАЯ ФОРМУЛА
      // const flashloanIxs = [...beginFlashLoanIx.instructions, ...args.ixs, ...endFlashLoanIx.instructions];
      // Структура: [beginFlashLoan(0), ...args.ixs(1-N), endFlashLoan(N+1)]

      console.log(`✅ ИСПОЛЬЗУЕМ ОРИГИНАЛЬНЫЙ buildFlashLoanTx БЕЗ МОДИФИКАЦИЙ`);
      console.log(`📊 ОФИЦИАЛЬНАЯ ФОРМУЛА: endIndex = args.ixs.length + 1 = ${args.ixs.length + 1}`);

      // ✅ НОВЫЙ ПОДХОД: ПЕРЕДАЕМ ВСЕ ИНСТРУКЦИИ В MARGINFI SDK!
      // MarginFi SDK сам правильно структурирует транзакцию:
      // [beginFlashLoan, ...ВСЕ_НАШИ_ИНСТРУКЦИИ, endFlashLoan]

      // ✅ ОФИЦИАЛЬНАЯ ДОКУМЕНТАЦИЯ MARGINFI:
      // end_index: u64: The index of the instruction where the flashloan should end
      // Это означает что end_index должен указывать на позицию lending_account_end_flashloan!

      console.log(`🎯 ПЕРЕДАЕМ ВСЕ ${args.ixs.length} ИНСТРУКЦИЙ В MARGINFI SDK`);
      console.log(`📊 MarginFi создаст структуру: [beginFlashLoan, ...${args.ixs.length}_инструкций, endFlashLoan]`);

      // ✅ ИСПРАВЛЯЕМ endIndex НА ОСНОВЕ ОФИЦИАЛЬНОЙ ДОКУМЕНТАЦИИ!
      // MarginFi рассчитывает: endIndex = args.ixs.length + 1
      // Но нужно: endIndex = позиция lending_account_end_flashloan (0-based)
      // В структуре [beginFlashLoan(0), ...args.ixs(1-N), endFlashLoan(N+1)]
      // endFlashLoan на позиции N+1, но MarginFi ожидает 0-based индекс
      // Значит нужно: endIndex = N+1-1 = N = args.ixs.length

      const originalMakeBeginFlashLoanIx = this.marginfiAccount.makeBeginFlashLoanIx.bind(this.marginfiAccount);

      this.marginfiAccount.makeBeginFlashLoanIx = async (endIndex) => {
        console.log(`🚨🚨🚨 КРИТИЧЕСКИЙ ПЕРЕХВАТ makeBeginFlashLoanIx: получен endIndex = ${endIndex} 🚨🚨🚨`);

        // 🔥 ИСПРАВЛЕНИЕ НА ОСНОВЕ УСПЕШНОЙ ТРАНЗАКЦИИ:
        // В успешной транзакции end_index: 14 точно указывает на позицию lending_account_end_flashloan (#15 в 1-based = 14 в 0-based)
        // MarginFi SDK правильно рассчитывает endIndex, НЕ НУЖНА КОРРЕКЦИЯ!

        console.log(`✅ ИСПОЛЬЗУЕМ ТОЧНЫЙ endIndex БЕЗ КОРРЕКЦИИ: ${endIndex}`);
        console.log(`📊 АНАЛИЗ: lending_account_end_flashloan будет на позиции ${endIndex} (0-based)`);

        return await originalMakeBeginFlashLoanIx(endIndex);
      };

      try {
        // ✅ ПЕРЕДАЕМ ВСЕ ИНСТРУКЦИИ С ИСПРАВЛЕННЫМ endIndex!
        const result = await this.marginfiAccount.buildFlashLoanTx(args, lookupTables);

        // Восстанавливаем оригинальный метод
        this.marginfiAccount.makeBeginFlashLoanIx = originalMakeBeginFlashLoanIx;

        return result;

      } catch (buildError) {
        // Восстанавливаем оригинальный метод в случае ошибки
        this.marginfiAccount.makeBeginFlashLoanIx = originalMakeBeginFlashLoanIx;
        throw buildError;
      }

      console.log(`✅ ОФИЦИАЛЬНАЯ ЛОГИКА MARGINFI ЗАВЕРШЕНА!`);
      return result;

    } catch (error) {
      console.error(`❌ Ошибка в buildFlashLoanTxWithAutoEndIndex: ${error.message}`);
      throw error;
    }
  }

  /**
   * ⚡ БЫСТРОЕ СОЗДАНИЕ FLASH LOAN ИЗ ШАБЛОНА (21-53мс!)
   */
  async createFastFlashLoan(tokenMint, amount, arbitrageInstructions = []) {
    const startTime = Date.now();

    try {
      // 🔥 ИСПРАВЛЕНО: Конвертируем tokenMint в строку для slice()
      const tokenMintStr = typeof tokenMint === 'string' ? tokenMint : tokenMint.toString();

      // 🧹 АВТОМАТИЧЕСКАЯ ОЧИСТКА PHANTOM DEBT ПЕРЕД FAST FLASH LOAN
      // 🔧 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: ПРИНУДИТЕЛЬНО СОЗДАЕМ НОВЫЙ АККАУНТ ДЛЯ КАЖДОЙ FAST ОПЕРАЦИИ
      // Это решает проблему "Unknown instruction error" при repayAll: true в последовательных операциях
      console.log(`\n🔧 ПРИНУДИТЕЛЬНОЕ СОЗДАНИЕ НОВОГО АККАУНТА ДЛЯ FAST FLASH LOAN...`);
      console.log(`💡 ПРИЧИНА: repayAll=true в buildFlashLoanTx не работает с "грязными" аккаунтами`);

      // 🔥 ИСПОЛЬЗУЕМ ТОЛЬКО СУЩЕСТВУЮЩИЙ АККАУНТ - НИКАКИХ НОВЫХ!
      console.log(`⚡ ИСПОЛЬЗУЕМ СУЩЕСТВУЮЩИЙ MarginFi аккаунт: ${this.marginfiAccount.address.toString()}`);

      if (this.phantomDebtCleaner && this.marginfiAccount) {
        try {
          console.log(`🧹 Очищаем phantom debt в существующем аккаунте...`);
          const cleanupResult = await this.phantomDebtCleaner.cleanPhantomDebtBeforeFlashLoan(this.marginfiAccount);

          if (cleanupResult.success) {
            console.log(`✅ Phantom debt очищен в аккаунте: ${this.marginfiAccount.address.toString()}`);
          } else {
            console.log(`⚠️ Не удалось очистить phantom debt, продолжаем с существующим состоянием`);
          }
        } catch (cleanupError) {
          console.log(`⚠️ Ошибка очистки phantom debt: ${cleanupError.message}`);
          console.log(`🔧 Продолжаем с существующим аккаунтом без очистки`);
        }
      }
      console.log(`⚡ БЫСТРОЕ создание flash loan из шаблона: ${amount} ${tokenMintStr.slice(0, 8)}...`);

      // 🚨 КРИТИЧЕСКАЯ ЗАЩИТА: ПРЕДОТВРАЩАЕМ ОГРОМНЫЕ СУММЫ В createFastFlashLoan!
      let numericAmount;
      if (typeof amount === 'object' && amount !== null) {
        numericAmount = parseFloat(amount.toString());
      } else if (typeof amount === 'string') {
        numericAmount = parseFloat(amount);
      } else {
        numericAmount = Number(amount);
      }

      if (isNaN(numericAmount) || numericAmount <= 0) {
        throw new Error(`КРИТИЧЕСКАЯ ОШИБКА: amount не является валидным числом! amount=${amount}, numericAmount=${numericAmount}`);
      }

      // 🚨 КРИТИЧЕСКАЯ ПРОВЕРКА: ПРЕДОТВРАЩАЕМ ОГРОМНЫЕ СУММЫ!
      // ✅ ИСПРАВЛЕНО: Увеличен лимит для flash loan арбитража!
      if (numericAmount > 10000000000000) { // Больше 10 триллионов ($10,000,000)
        console.log(`🚨 КРИТИЧЕСКАЯ ОШИБКА в createFastFlashLoan: Сумма слишком большая! ${numericAmount}`);
        console.log(`🚨 Максимально допустимая сумма: 10,000,000,000,000 (10 триллионов микроюнитов = $10,000,000)`);
        console.log(`🚨 ПОЛУЧЕННАЯ СУММА: ${numericAmount} (${(numericAmount/1000000).toLocaleString()} USD)`);
        throw new Error(`КРИТИЧЕСКАЯ ОШИБКА в createFastFlashLoan: Сумма ${numericAmount} превышает максимально допустимую ($10,000,000)!`);
      }

      // Ищем подходящий шаблон
      const templateKey = `${tokenMint}_${amount}`;
      let template = this.templateInstructions.get(templateKey);

      if (!template) {
        // Ищем ближайший шаблон по сумме
        // 🔥 ИСПРАВЛЕНО: Конвертируем tokenMint в строку для сравнения
        const tokenMintStr = typeof tokenMint === 'string' ? tokenMint : tokenMint.toString();

        const availableTemplates = Array.from(this.templateInstructions.keys())
          .filter(key => key.startsWith(tokenMintStr))
          .map(key => {
            const [mint, amt] = key.split('_');
            return { key, amount: parseInt(amt) };
          })
          .sort((a, b) => Math.abs(a.amount - amount) - Math.abs(b.amount - amount));

        if (availableTemplates.length > 0) {
          template = this.templateInstructions.get(availableTemplates[0].key);
          console.log(`🔧 Используем ближайший шаблон: ${availableTemplates[0].key}`);
        }
      }

      if (!template) {
        console.log(`⚠️ Шаблон не найден, создаем обычным способом...`);
        return await this.createFlashLoan(tokenMint, amount, arbitrageInstructions);
      }

      // ✅ ИСПОЛЬЗУЕМ ПРЕДПОДГОТОВЛЕННЫЙ ШАБЛОН
      console.log(`🚀 Используем предподготовленный шаблон (возраст: ${Date.now() - template.createdAt}мс)`);

      // Проверяем актуальность шаблона
      if (Date.now() - template.createdAt > this.CACHE_TTL) {
        console.log(`⚠️ Шаблон устарел, обновляем...`);
        await this.prepareTransactionTemplate(tokenMint, amount);
        template = this.templateInstructions.get(templateKey);
      }

      // ✅ ИСПРАВЛЕНО: СОЗДАЕМ ПРОСТОЙ FLASH LOAN КАК В ОФИЦИАЛЬНОМ ПРИМЕРЕ
      console.log(`🔧 Создаем простой flash loan без арбитражных инструкций для тестирования...`);

      // Получаем банк
      let bank;

      if (tokenMintStr === 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v' && this.cachedUsdcBank) {
        bank = this.cachedUsdcBank;
      } else if (tokenMintStr === 'So********************************111111112' && this.cachedSolBank) {
        bank = this.cachedSolBank;
      } else {
        const mintPubkey = typeof tokenMint === 'string' ? new PublicKey(tokenMint) : tokenMint;
        bank = this.client.getBankByMint(mintPubkey);
      }

      if (!bank) {
        throw new Error(`Банк не найден для ${tokenMintStr.slice(0, 8)}...`);
      }

      // 🔥 УБИРАЕМ TIMEOUT - ПУСТЬ MARGINFI РАБОТАЕТ НОРМАЛЬНО!
      console.log(`🔧 Создаем MarginFi инструкции БЕЗ timeout...`);

      // 🔥 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: ПОЛУЧАЕМ ALT ИЗ JUPITER ПЕРЕД FLASH LOAN!
      let jupiterALTAccounts = [];
      let jupiterALTAddresses = [];

      // 🔥 ИСПРАВЛЕНО: ИСПОЛЬЗУЕМ ЕДИНЫЙ CompleteALTManager КАК В MASTER-TRANSACTION-CONTROLLER!
      console.log(`🎯 ИСПОЛЬЗУЕМ CompleteALTManager для получения ALT...`);

      try {
        // Инициализируем единый CompleteALTManager
        if (!this.completeALTManager) {
          const CompleteALTManager = require('../complete-alt-manager.js');
          this.completeALTManager = new CompleteALTManager(this.connection);
          console.log(`✅ CompleteALTManager инициализирован (как в master-transaction-controller)`);
        }

        // Создаем тестовый Jupiter ответ для получения ALT
        if (this.jupiterSwapInstructions) {
          console.log(`🔄 Получаем Jupiter swap для извлечения ALT...`);

          const inputMint = tokenMint === 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'
            ? 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v' // USDC
            : 'So********************************111111112';   // SOL
          const outputMint = tokenMint === 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'
            ? 'So********************************111111112'   // SOL
            : 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'; // USDC

          const testSwapData = await this.jupiterSwapInstructions.createSwapInstructions(
            inputMint,
            outputMint,
            Math.floor(amount * 0.1), // 10% от суммы для теста
            300 // 3% slippage
          );

          // Используем единый CompleteALTManager для загрузки ALT
          const loadedALT = await this.completeALTManager.loadAllALT(testSwapData);

          if (loadedALT && loadedALT.length > 0) {
            jupiterALTAccounts = loadedALT;
            console.log(`✅ CompleteALTManager загрузил ${loadedALT.length} ALT таблиц`);
          }
        }
      } catch (altError) {
        console.log(`⚠️ Ошибка CompleteALTManager: ${altError.message}`);
        console.log(`💡 Продолжаем без Jupiter ALT`);
      }

      // Удалены неиспользуемые переменные borrowIx, repayIx
      try {
        console.log(`⏳ Попытка создания реальных MarginFi инструкций...`);

        // 🔥 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: ПРАВИЛЬНАЯ ИНТЕРПРЕТАЦИЯ AMOUNT!
        // ПРОБЛЕМА: amount может быть native amount (огромное число) или UI amount
        // РЕШЕНИЕ: Определяем тип и конвертируем в UI amount

        let amountUi;
        if (numericAmount > 1000000) {
          // Если больше 1 миллиона, скорее всего это native amount
          amountUi = this.convertNativeToUiAmount(numericAmount, 'USDC') // ИСПРАВЛЕНО: Правильная конвертация; // Конвертируем в UI amount (для USDC)
          console.log(`🔄 КОНВЕРТАЦИЯ: Native amount ${numericAmount} -> UI amount ${amountUi}`);
        } else {
          // Если меньше 1 миллиона, скорее всего это уже UI amount
          amountUi = numericAmount;
          console.log(`✅ ИНТЕРПРЕТАЦИЯ: Уже UI amount ${amountUi}`);
        }

        // Безопасная сумма займа для предотвращения "Assertion failed"
        const safeAmount = Math.min(amountUi, 100); // Максимум $100 UI amount для безопасности
        if (safeAmount !== amountUi) {
          console.log(`⚠️ Сумма flash loan уменьшена для безопасности: $${amountUi} -> $${safeAmount}`);
        }

      // 🔥 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: repayAll = false предотвращает "Assertion failed"
        // 🔥 АТОМАРНЫЙ FLASH LOAN БЕЗ СОСТОЯНИЯ И ЗАЛОГОВ
        console.log(`⚡ СОЗДАЕМ АТОМАРНЫЙ FLASH LOAN:`);
        console.log(`   💡 Займ и возврат в одной транзакции`);
        console.log(`   💡 Нет промежуточного состояния аккаунта`);
        console.log(`   💡 Нет залогов - полностью безрисковый`);
        console.log(`   💡 repayAll = true ОБЯЗАТЕЛЬНО для атомарности`);

        // Для атомарных flash loans ВСЕГДА repayAll = true
        console.log(`🔧 АТОМАРНЫЙ РЕЖИМ: repayAll = true (займ ДОЛЖЕН быть полностью возвращен)`);

        // 🔥 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: НЕ СОЗДАЕМ BORROW/REPAY ИНСТРУКЦИИ!
        // buildFlashLoanTx создает их автоматически согласно официальной документации
        console.log(`🔥 ИСПРАВЛЕНО: buildFlashLoanTx создает borrow/repay автоматически`);
        console.log(`📚 ОФИЦИАЛЬНАЯ ДОКУМЕНТАЦИЯ: передаем только арбитражные инструкции`);

        // Убираем создание borrow/repay инструкций - они больше не нужны!
        // ✅ СОЗДАЕМ BORROW И REPAY ИНСТРУКЦИИ ДЛЯ FLASH LOAN
        // console.log(`🔧 Создаем borrow инструкции для Flash Loan...`);
        // const borrowPromise = this.marginfiAccount.makeBorrowIx(safeAmount, bank.address);
        // console.log(`🔧 Создаем repay инструкции для Flash Loan...`);
        // const repayPromise = this.marginfiAccount.makeRepayIx(safeAmount, bank.address, true);
        // borrowIx = await Promise.race([borrowPromise, marginfiTimeout]);
        // repayIx = await Promise.race([repayPromise, marginfiTimeout]);

        console.log(`✅ Переходим к использованию buildFlashLoanTx без borrow/repay`);

      } catch (error) {
        console.log(`❌ MarginFi timeout или ошибка: ${error.message}`);
        throw new Error(`MarginFi инструкции недоступны: ${error.message}`);
      }

      // 🔧 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Нормализуем ТОЛЬКО арбитражные инструкции
      // Borrow/Repay инструкции создаются автоматически в buildFlashLoanTx
      const normalizedArbitrageInstructions = normalizeInstructions(arbitrageInstructions);

      console.log(`✅ Arbitrage инструкций: ${normalizedArbitrageInstructions.length}`);
      console.log(`✅ Borrow/Repay: создаются автоматически в buildFlashLoanTx`);

      // ✅ ИСПРАВЛЕНО: СОЗДАЕМ FLASH LOAN С АРБИТРАЖНЫМИ ИНСТРУКЦИЯМИ
      console.log(`🔧 Создаем flash loan с ${normalizedArbitrageInstructions.length} арбитражными инструкциями...`);

      // 🔥 УБИРАЕМ TIMEOUT - ПУСТЬ buildFlashLoanTx РАБОТАЕТ НОРМАЛЬНО!
      console.log(`🔧 Создаем buildFlashLoanTx БЕЗ timeout...`);

      // 🔥 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: ПЕРЕДАЕМ ALT В buildFlashLoanTx!
      console.log(`🔥 ИСПРАВЛЯЕМ ГЛАВНУЮ ПРОБЛЕМУ: Передаем ALT в buildFlashLoanTx!`);
      console.log(`   📊 Jupiter ALT аккаунтов: ${jupiterALTAccounts?.length || 0}`);
      console.log(`   📊 Jupiter ALT адресов: ${jupiterALTAddresses?.length || 0}`);

      // 🔧 ИСПРАВЛЕНО: ALT не всегда обязательны для простых flash loans
      if (jupiterALTAccounts && jupiterALTAccounts.length > 0) {
        console.log(`✅ ALT НАЙДЕНЫ: ${jupiterALTAccounts.length} Jupiter ALT таблиц загружено`);
      } else {
        console.log(`⚠️ ALT НЕ НАЙДЕНЫ: Продолжаем без Jupiter ALT (для простых операций это нормально)`);
        jupiterALTAccounts = []; // Убеждаемся что это пустой массив
      }

      // 🔥 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: buildFlashLoanTx ДОЛЖЕН ПОЛУЧАТЬ ТОЛЬКО АРБИТРАЖНЫЕ ИНСТРУКЦИИ!
      console.log(`🔥 ИСПРАВЛЕНО: buildFlashLoanTx создает borrow/repay автоматически!`);
      console.log(`📚 ПО ОФИЦИАЛЬНОЙ ДОКУМЕНТАЦИИ: передаем только арбитражные инструкции`);

      // ✅ ПРАВИЛЬНО: Передаем ТОЛЬКО арбитражные инструкции
      // buildFlashLoanTx сам создаст borrow/repay инструкции автоматически
      const buildPromise = this.marginfiAccount.buildFlashLoanTx({
        ixs: normalizedArbitrageInstructions, // ✅ ТОЛЬКО арбитражные инструкции!
        signers: [],                          // ✅ Пустой массив подписантов
        repayAll: true                        // ✅ ИСПРАВЛЕНО: repayAll: true для правильного flash loan!
      }, jupiterALTAccounts);                 // ✅ ALT таблицы

      const flashLoanTx = await buildPromise;

      const elapsedTime = Date.now() - startTime;
      console.log(`✅ БЫСТРЫЙ flash loan создан за ${elapsedTime}мс (цель: 21-53мс)`);

      if (elapsedTime <= 53) {
        console.log(`🚀 ОТЛИЧНО! Скорость в пределах цели: ${elapsedTime}мс <= 53мс`);
      } else {
        console.log(`⚠️ Медленнее цели: ${elapsedTime}мс > 53мс`);
      }

      return {
        success: true,
        transaction: flashLoanTx,
        template,
        elapsedTime,
        stats: {
          borrowInstructions: template.borrowInstructions ? template.borrowInstructions.length : 0, // 🔥 БЕЗОПАСНАЯ ПРОВЕРКА!
          arbitrageInstructions: arbitrageInstructions ? arbitrageInstructions.length : 0, // 🔥 БЕЗОПАСНАЯ ПРОВЕРКА!
          repayInstructions: template.repayInstructions ? template.repayInstructions.length : 0, // 🔥 БЕЗОПАСНАЯ ПРОВЕРКА!
          totalInstructions: (template.borrowInstructions ? template.borrowInstructions.length : 0) + (arbitrageInstructions ? arbitrageInstructions.length : 0) + (template.repayInstructions ? template.repayInstructions.length : 0) // 🔥 БЕЗОПАСНАЯ ПРОВЕРКА!
        }
      };

    } catch (error) {
      const elapsedTime = Date.now() - startTime;
      console.error(`❌ Ошибка быстрого flash loan за ${elapsedTime}мс: ${error.message}`);
      return {
        success: false,
        error: error.message,
        elapsedTime
      };
    }
  }

  /**
   * 👤 ПОЛУЧИТЬ ИЛИ СОЗДАТЬ MARGINFI ACCOUNT (УДАЛЕН - ДУБЛИРУЕТ ОСНОВНУЮ ЛОГИКУ)
   * ❌ ЭТОТ МЕТОД УДАЛЕН ДЛЯ ПРЕДОТВРАЩЕНИЯ ДУБЛИРОВАНИЯ КОДА
   */
  async getOrCreateMarginfiAccount() {
    console.warn('❌ МЕТОД getOrCreateMarginfiAccount УДАЛЕН!');
    console.warn('🔧 ИСПОЛЬЗУЙТЕ ОСНОВНУЮ ЛОГИКУ ИНИЦИАЛИЗАЦИИ В КОНСТРУКТОРЕ!');
    throw new Error('getOrCreateMarginfiAccount УДАЛЕН - используйте основную логику инициализации!');
  }

  /**
   * 🔥 МЕТОД ИЗ БЭКАПА: createOfficialEndFlashLoanIx
   */
  async createOfficialEndFlashLoanIx(projectedActiveBalances, endIndex) {
    console.log(`🔧 Создаем ОФИЦИАЛЬНУЮ end flash loan инструкцию с endIndex: ${endIndex}`);

    // Получаем MARGINFI_PROGRAM_ID
    const MARGINFI_PROGRAM_ID = this.marginfiAccount._program.programId;

    // Создаем instruction data с правильным форматом как в бэкапе
    const instructionData = Buffer.alloc(16);
    instructionData.writeUInt8(1, 0); // Discriminator для end flash loan
    instructionData.writeBigUInt64LE(BigInt(endIndex), 8); // endIndex как u64

    // Создаем ключи для инструкции
    const keys = [
      { pubkey: this.marginfiAccount.address, isSigner: false, isWritable: true },
      { pubkey: this.marginfiClient.wallet.publicKey, isSigner: true, isWritable: true }
    ];

    // Добавляем projected active balances как readonly ключи
    for (const balance of projectedActiveBalances) {
      keys.push({ pubkey: balance, isSigner: false, isWritable: false });
    }

    const { TransactionInstruction } = require('@solana/web3.js');

    const endFlashLoanIx = new TransactionInstruction({
      keys: keys,
      programId: MARGINFI_PROGRAM_ID,
      data: instructionData
    });

    console.log(`✅ ОФИЦИАЛЬНАЯ end flash loan инструкция создана с ${keys.length} ключами`);

    return {
      instructions: [endFlashLoanIx],
      keys: []
    };
  }

  /**
   * 🔥 НОВЫЙ МЕТОД: createCompleteFlashLoanWithAllInstructions
   *
   * СОЗДАЕТ ВСЕ 4 MARGINFI FLASH LOAN ИНСТРУКЦИИ КАК В УСПЕШНОЙ СДЕЛКЕ:
   * 1. lending_account_start_flashloan
   * 2. lending_account_borrow
   * 3. Арбитражные инструкции
   * 4. lending_account_repay
   * 5. lending_account_end_flashloan
   */
  async createCompleteFlashLoanWithAllInstructions(arbitrageInstructions, altAccounts, amount, tokenMint) {
    try {
      console.log(`🔥 СОЗДАНИЕ ПОЛНОЙ FLASH LOAN СТРУКТУРЫ С ВСЕМИ 4 MARGINFI ИНСТРУКЦИЯМИ!`);
      console.log(`   Арбитражных инструкций: ${arbitrageInstructions.length}`);
      console.log(`   ALT таблиц: ${altAccounts.length}`);
      console.log(`   Сумма займа: ${amount}`);
      console.log(`   Токен: ${tokenMint}`);

      // 1. СОЗДАЕМ START FLASH LOAN ИНСТРУКЦИЮ
      console.log(`🚀 1. Создаем lending_account_start_flashloan...`);
      const endIndex = arbitrageInstructions.length + 3; // +3 для borrow, repay, end
      const startFlashLoanIx = await this.marginfiAccount.makeBeginFlashLoanIx(endIndex);
      console.log(`✅ Start flash loan создана с endIndex: ${endIndex}`);

      // 2. СОЗДАЕМ BORROW ИНСТРУКЦИЮ
      console.log(`💰 2. Создаем lending_account_borrow...`);
      const bank = this.marginfiClient.getBankByMint(new PublicKey(tokenMint));
      const borrowIx = await this.marginfiAccount.makeBorrowIx(amount, bank.address);
      console.log(`✅ Borrow инструкция создана для ${amount} токенов`);

      // 3. АРБИТРАЖНЫЕ ИНСТРУКЦИИ (уже готовы)
      console.log(`🔄 3. Добавляем ${arbitrageInstructions.length} арбитражных инструкций...`);

      // 4. СОЗДАЕМ REPAY ИНСТРУКЦИЮ
      console.log(`💸 4. Создаем lending_account_repay...`);
      const repayIx = await this.marginfiAccount.makeRepayIx(amount, bank.address, true);
      console.log(`✅ Repay инструкция создана с repayAll=true`);

      // 5. СОЗДАЕМ END FLASH LOAN ИНСТРУКЦИЮ
      console.log(`🏁 5. Создаем lending_account_end_flashloan...`);
      const projectedActiveBalances = this.marginfiAccount.projectActiveBalancesNoCpi(
        this.marginfiAccount._program,
        arbitrageInstructions
      );
      const endFlashLoanIx = await this.createEndFlashLoanInstruction(projectedActiveBalances, endIndex);
      console.log(`✅ End flash loan инструкция создана`);

      // 6. СОБИРАЕМ ВСЕ ИНСТРУКЦИИ В ПРАВИЛЬНОМ ПОРЯДКЕ
      const allInstructions = [
        ...startFlashLoanIx.instructions,  // 1. start_flashloan
        ...borrowIx.instructions,          // 2. borrow
        ...arbitrageInstructions,          // 3. arbitrage
        ...repayIx.instructions,           // 4. repay
        ...endFlashLoanIx.instructions     // 5. end_flashloan
      ];

      console.log(`🎯 ПОЛНАЯ FLASH LOAN СТРУКТУРА СОЗДАНА:`);
      console.log(`   Start flash loan: ${startFlashLoanIx.instructions.length} инструкций`);
      console.log(`   Borrow: ${borrowIx.instructions.length} инструкций`);
      console.log(`   Arbitrage: ${arbitrageInstructions.length} инструкций`);
      console.log(`   Repay: ${repayIx.instructions.length} инструкций`);
      console.log(`   End flash loan: ${endFlashLoanIx.instructions.length} инструкций`);
      console.log(`   ВСЕГО: ${allInstructions.length} инструкций`);

      // 7. СОЗДАЕМ VERSIONED TRANSACTION
      const { TransactionMessage, VersionedTransaction } = require('@solana/web3.js');

      const blockhash = (await this.marginfiClient.provider.connection.getLatestBlockhash("confirmed")).blockhash;

      const message = new TransactionMessage({
        payerKey: this.marginfiClient.wallet.publicKey,
        recentBlockhash: blockhash,
        instructions: allInstructions,
      }).compileToV0Message(altAccounts);

      const flashLoanTx = new VersionedTransaction(message);

      console.log(`🎉 ПОЛНАЯ FLASH LOAN ТРАНЗАКЦИЯ СОЗДАНА С ВСЕМИ 4 MARGINFI ИНСТРУКЦИЯМИ!`);
      return flashLoanTx;

    } catch (error) {
      console.error(`❌ Ошибка создания полной flash loan структуры: ${error.message}`);
      throw error;
    }
  }

  /**
   * 🔧 СОЗДАНИЕ END FLASH LOAN ИНСТРУКЦИИ
   */
  async createEndFlashLoanInstruction(projectedActiveBalances, endIndex) {
    console.log(`🔧 Создаем end flash loan инструкцию с endIndex: ${endIndex}`);

    // Получаем MARGINFI_PROGRAM_ID
    const MARGINFI_PROGRAM_ID = this.marginfiAccount._program.programId;

    // Создаем instruction data с правильным форматом
    const instructionData = Buffer.alloc(16);
    instructionData.writeUInt8(1, 0); // Discriminator для end flash loan
    instructionData.writeBigUInt64LE(BigInt(endIndex), 8); // endIndex как u64

    // Создаем ключи для инструкции
    const keys = [
      { pubkey: this.marginfiAccount.address, isSigner: false, isWritable: true },
      { pubkey: this.marginfiClient.wallet.publicKey, isSigner: true, isWritable: true }
    ];

    // Добавляем projected active balances как readonly ключи
    for (const balance of projectedActiveBalances) {
      keys.push({ pubkey: balance, isSigner: false, isWritable: false });
    }

    const endFlashLoanIx = new TransactionInstruction({
      keys: keys,
      programId: MARGINFI_PROGRAM_ID,
      data: instructionData
    });

    console.log(`✅ End flash loan инструкция создана с ${keys.length} ключами`);

    return {
      instructions: [endFlashLoanIx],
      keys: []
    };
  }

  /**
   * ⚡ БЫСТРОЕ СОЗДАНИЕ FLASH LOAN (ОПТИМИЗИРОВАНО ДЛЯ < 14МС!) - ОБНОВЛЕННАЯ ВЕРСИЯ
   * ПОДДЕРЖКА VersionedTransaction И ADDRESS LOOKUP TABLES
   */
  async createFlashLoan(tokenMint, amount, arbitrageInstructions = [], versionedTransactionOptions = null) {
    const startTime = Date.now();

    try {
      // 🔥 ИСПРАВЛЕНО: Объявляем tokenMintStr ОДИН РАЗ в начале метода
      const tokenMintStr = typeof tokenMint === 'string' ? tokenMint : tokenMint.toString();

      // 🔧 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ ПО ОФИЦИАЛЬНОЙ ДОКУМЕНТАЦИИ MARGINFI:
      // repayAll: true НЕ РАБОТАЕТ в последовательных операциях в новых версиях MarginFi V2
      console.log(`\n🔧 ИСПРАВЛЕНИЕ ПО ОФИЦИАЛЬНОЙ ДОКУМЕНТАЦИИ MARGINFI V2...`);
      console.log(`💡 ПРОБЛЕМА: repayAll=true вызывает "Unknown instruction error" в последовательных операциях`);
      console.log(`✅ РЕШЕНИЕ: Переопределяем buildFlashLoanTx для использования точной суммы вместо repayAll=true`);
      console.log(`🏦 Используем единственный реальный аккаунт: ${this.marginfiAccount.address.toString()}`);

      // 🔧 ПЕРЕОПРЕДЕЛЯЕМ makeRepayIx ДЛЯ ИСПРАВЛЕНИЯ repayAll: true
      if (this.marginfiAccount && typeof this.marginfiAccount.makeRepayIx === 'function') {
        console.log(`🔧 Переопределяем makeRepayIx для исправления repayAll: true...`);

        // Сохраняем оригинальный метод
        const originalMakeRepayIx = this.marginfiAccount.makeRepayIx.bind(this.marginfiAccount);

        // Переопределяем метод с исправлением
        this.marginfiAccount.makeRepayIx = async (amount, bankAddress, repayAll, opt) => {
          if (repayAll === true) {
            console.log(`🔧 ИСПРАВЛЕНИЕ: заменяем repayAll=true на точную сумму ${amount}`);
            console.log(`💡 ПРИЧИНА: repayAll=true вызывает "Unknown instruction error" в последовательных операциях`);

            // Используем точную сумму вместо repayAll=true
            return await originalMakeRepayIx(amount, bankAddress, false, opt);
          } else {
            // Обычный вызов без изменений
            return await originalMakeRepayIx(amount, bankAddress, repayAll, opt);
          }
        };

        console.log(`✅ makeRepayIx переопределен: repayAll=true → точная сумма`);
      } else {
        console.log(`⚠️ makeRepayIx недоступен - используем альтернативный подход`);
      }

      this.stats.totalLoans++;
      console.log(`⚡ БЫСТРОЕ создание MarginFi flash loan: ${amount} ${tokenMintStr}`);

      // 🔥 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: ПОДДЕРЖКА VersionedTransaction ДАННЫХ
      if (versionedTransactionOptions) {
        console.log('🔥 ИСПОЛЬЗУЕМ VersionedTransaction данные (ПРАВИЛЬНЫЙ ПО ДОКУМЕНТАЦИИ SOLANA)');
        console.log(`   Address Lookup Tables: ${versionedTransactionOptions.addressLookupTableAddresses?.length || 0}`);
        console.log(`   Official Solana Method: ${versionedTransactionOptions.officialSolanaMethod || false}`);

        // ✅ ИСПОЛЬЗУЕМ УЖЕ ПОДГОТОВЛЕННЫЕ ИНСТРУКЦИИ ИЗ VersionedTransaction
        if (versionedTransactionOptions.versionedTransactionData?.arbitrageInstructions?.length > 0) {
          arbitrageInstructions = versionedTransactionOptions.versionedTransactionData.arbitrageInstructions;
          console.log(`✅ Получены арбитражные инструкции из VersionedTransaction: ${arbitrageInstructions.length}`);
        }
      }

      // 🚨 СТРОГАЯ ПРОВЕРКА: ТОЛЬКО MARGINFI!
      if (!this.client || !this.marginfiAccount) {
        throw new Error('MarginFi недоступен - торговля ЗАПРЕЩЕНА!');
      }

      // 🔥 FLASH LOAN НЕ ТРЕБУЕТ ПРОВЕРОК БАЛАНСА - АТОМАРНАЯ ТРАНЗАКЦИЯ!
      console.log(`🔥 FLASH LOAN БЕЗ ПРОВЕРОК БАЛАНСА - АТОМАРНОСТЬ ГАРАНТИРУЕТ БЕЗОПАСНОСТЬ!`);

      // 🔥 TOKEN ACCOUNTS СОЗДАЮТСЯ АВТОМАТИЧЕСКИ - НЕ НУЖНЫ ПРОВЕРКИ!
      console.log(`🔥 TOKEN ACCOUNTS СОЗДАЮТСЯ АВТОМАТИЧЕСКИ В FLASH LOAN!`);

      // 🔥 МИНИМАЛЬНЫЕ СУММЫ НЕ ВАЖНЫ - FLASH LOAN РАБОТАЕТ С ЛЮБОЙ СУММОЙ!
      console.log(`🔥 FLASH LOAN БЕЗ ОГРАНИЧЕНИЙ СУММЫ - ИСПОЛЬЗУЕМ ТОЧНУЮ СУММУ!`);

      // ✅ ИСПОЛЬЗУЕМ КЭШИРОВАННЫЙ БАНК ДЛЯ СКОРОСТИ
      let bank;

      if (tokenMintStr.includes('USDC') && this.cachedUsdcBank) {
        bank = this.cachedUsdcBank;
        console.log(`⚡ Используем кэшированный USDC банк`);
      } else if (tokenMintStr.includes('SOL') && this.cachedSolBank) {
        bank = this.cachedSolBank;
        console.log(`⚡ Используем кэшированный SOL банк`);
      } else {
        // Fallback: ищем банк по mint
        const mintPubkey = typeof tokenMint === 'string' ? new PublicKey(tokenMint) : tokenMint;
        bank = this.client.getBankByMint(mintPubkey);
      }

      if (!bank) {
        throw new Error(`Банк не найден для токена: ${tokenMint}`);
      }

      console.log(`🏦 Банк: ${bank.address.toString().slice(0, 8)}...`);

      // 🎯 ОФИЦИАЛЬНОЕ РЕШЕНИЕ: ИСПОЛЬЗУЕМ BN.JS ДЛЯ БОЛЬШИХ ЧИСЕЛ!
      // ПРОБЛЕМА: 76,923,076,923 lamports > Number.MAX_SAFE_INTEGER (53 bits)
      // РЕШЕНИЕ: MarginFi SDK ожидает BN.js объекты, НЕ BigInt и НЕ number!

      // Удален неиспользуемый импорт BN

      console.log(`🔢 АНАЛИЗ СУММЫ ЗАЙМА $10,000:`);
      console.log(`   Сумма в токенах: ${amount}`);
      console.log(`   ТРЕБОВАНИЕ: ТОЛЬКО $10,000 - ВСЕ ОСТАЛЬНЫЕ СУММЫ УДАЛЕНЫ!`);

      // 🎯 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: MARGINFI ОЖИДАЕТ ОБЫЧНЫЕ ЧИСЛА!
      // ПРОБЛЕМА: Мы передавали BN объекты, но MarginFi SDK ожидает обычные числа
      // РЕШЕНИЕ: Используем обычные числа - MarginFi SDK сам конвертирует их правильно

      // 🔥 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: ПРИНУДИТЕЛЬНАЯ КОНВЕРТАЦИЯ В ЧИСЛО!
      console.log(`🔍 ДИАГНОСТИКА amount ПЕРЕД КОНВЕРТАЦИЕЙ:`);
      console.log(`   amount: ${amount}`);
      console.log(`   amount тип: ${typeof amount}`);
      console.log(`   amount.toString(): ${amount.toString()}`);

      // 🔥 ПРИНУДИТЕЛЬНАЯ КОНВЕРТАЦИЯ В ЧИСЛО
      let numericAmount;
      if (typeof amount === 'object' && amount !== null) {
        // Если это BN объект или другой объект с toString()
        numericAmount = parseFloat(amount.toString());
        console.log(`🔧 ИСПРАВЛЕНО: Объект конвертирован в число: ${numericAmount}`);
      } else if (typeof amount === 'string') {
        numericAmount = parseFloat(amount);
        console.log(`🔧 ИСПРАВЛЕНО: Строка конвертирована в число: ${numericAmount}`);
      } else {
        numericAmount = Number(amount);
        console.log(`🔧 ИСПРАВЛЕНО: Значение конвертировано в число: ${numericAmount}`);
      }

      // 🔥 ПРОВЕРКА НА NaN
      if (isNaN(numericAmount) || numericAmount <= 0) {
        throw new Error(`КРИТИЧЕСКАЯ ОШИБКА: amount не является валидным числом! amount=${amount}, numericAmount=${numericAmount}`);
      }

      // 🔥 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: ПРАВИЛЬНАЯ КОНВЕРТАЦИЯ ПО ТИПУ ТОКЕНА!
      let amountUI;
      let tokenSymbol;

      // 🚨 КРИТИЧЕСКАЯ ПРОВЕРКА: ПРЕДОТВРАЩАЕМ ОГРОМНЫЕ СУММЫ!
      // ✅ ИСПРАВЛЕНО: Увеличен лимит для flash loan арбитража!
      if (numericAmount > 10000000000000) { // Больше 10 триллионов ($10,000,000)
        console.log(`🚨 КРИТИЧЕСКАЯ ОШИБКА: Сумма слишком большая! ${numericAmount}`);
        console.log(`🚨 Максимально допустимая сумма: 10,000,000,000,000 (10 триллионов микроюнитов = $10,000,000)`);
        console.log(`🚨 ПОЛУЧЕННАЯ СУММА: ${numericAmount} (${(numericAmount/1000000).toLocaleString()} USD)`);
        throw new Error(`КРИТИЧЕСКАЯ ОШИБКА: Сумма ${numericAmount} превышает максимально допустимую ($10,000,000)!`);
      }

      // 🔥 ИСПРАВЛЕНО: Определяем формат суммы (UI amount vs micro units)
      let isUIAmount = false;

      if (tokenMintStr === 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v') {
        // USDC: 6 decimals
        tokenSymbol = 'USDC';
        // Если сумма меньше 1 миллиона, скорее всего это UI amount ($10,000)
        isUIAmount = numericAmount < 1000000;
        amountUI = isUIAmount ? numericAmount : numericAmount / Math.pow(10, 6);
      } else if (tokenMintStr === 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB') {
        // USDT: 6 decimals
        tokenSymbol = 'USDT';
        isUIAmount = numericAmount < 1000000;
        amountUI = isUIAmount ? numericAmount : numericAmount / Math.pow(10, 6);
      } else if (tokenMintStr === 'So********************************111111112') {
        // SOL: 9 decimals
        tokenSymbol = 'SOL';
        // Для SOL суммы больше 1000 скорее всего в lamports
        isUIAmount = numericAmount < 1000;
        amountUI = isUIAmount ? numericAmount : numericAmount / Math.pow(10, 9);
      } else {
        // По умолчанию 6 decimals для большинства токенов
        tokenSymbol = 'TOKEN';
        isUIAmount = numericAmount < 1000000;
        amountUI = isUIAmount ? numericAmount : numericAmount / Math.pow(10, 6);
      }

      // 🔥 ДОПОЛНИТЕЛЬНАЯ ПРОВЕРКА amountUI
      if (isNaN(amountUI) || amountUI <= 0) {
        throw new Error(`КРИТИЧЕСКАЯ ОШИБКА: amountUI не является валидным числом! amountUI=${amountUI}`);
      }

      // 🚨 КРИТИЧЕСКАЯ ПРОВЕРКА: МАКСИМАЛЬНАЯ СУММА ЗАЙМА
      // ✅ ИСПРАВЛЕНО: Увеличены лимиты для flash loan арбитража!
      const MAX_LOAN_AMOUNT = {
        'USDC': 10000000,  // $10,000,000 максимум (для flash loan арбитража)
        'USDT': 10000000,  // $10,000,000 максимум (для flash loan арбитража)
        'SOL': 70000,      // 70,000 SOL максимум (~$10,000,000)
        'TOKEN': 10000000  // $10,000,000 по умолчанию
      };

      const maxAllowed = MAX_LOAN_AMOUNT[tokenSymbol] || MAX_LOAN_AMOUNT['TOKEN'];
      if (amountUI > maxAllowed) {
        console.log(`🚨 КРИТИЧЕСКАЯ ОШИБКА: Сумма займа превышает максимум!`);
        console.log(`🚨 Запрошено: ${amountUI} ${tokenSymbol}`);
        console.log(`🚨 Максимум: ${maxAllowed} ${tokenSymbol}`);
        throw new Error(`КРИТИЧЕСКАЯ ОШИБКА: Сумма займа ${amountUI} ${tokenSymbol} превышает максимум ${maxAllowed} ${tokenSymbol}!`);
      }

      console.log(`🔧 MARGINFI ИСПРАВЛЕНИЕ: Правильная конвертация по типу токена:`);
      console.log(`   Исходная сумма: ${amount}`);
      console.log(`   Определен формат: ${isUIAmount ? 'UI Amount' : 'Micro Units'}`);
      console.log(`   UI Amount: ${amountUI} ${tokenSymbol}`);
      // 🔥 ИСПРАВЛЕНО: Используем уже объявленную переменную tokenMintStr
      console.log(`   Токен: ${tokenSymbol} (${tokenMintStr.slice(0, 8)}...)`);
      console.log(`🔥 MARGINFI SDK ОЖИДАЕТ UI AMOUNT, НЕ МИКРОЮНИТЫ!`);

      // 🚨 КРИТИЧЕСКАЯ ПРОВЕРКА: ЛИКВИДНОСТЬ БАНКА
      console.log(`🔍 ПРОВЕРКА ЛИКВИДНОСТИ БАНКА...`);
      try {
        const bankCapacity = bank.computeRemainingCapacity();
        const borrowCapacityUI = bankCapacity.borrowCapacity.div(Math.pow(10, tokenSymbol === 'SOL' ? 9 : 6));

        console.log(`💰 Доступная ликвидность: ${borrowCapacityUI.toString()} ${tokenSymbol}`);
        console.log(`💰 Запрашиваемая сумма: ${amountUI} ${tokenSymbol}`);

        if (borrowCapacityUI.lt(amountUI)) {
          throw new Error(`НЕДОСТАТОЧНАЯ ЛИКВИДНОСТЬ: требуется ${amountUI} ${tokenSymbol}, доступно ${borrowCapacityUI.toString()} ${tokenSymbol}`);
        }

        console.log(`✅ Ликвидность достаточна для займа`);
      } catch (capacityError) {
        console.log(`⚠️ Ошибка проверки ликвидности: ${capacityError.message}`);
        // Продолжаем, но с предупреждением
      }

      // 🎯 КРИТИЧЕСКАЯ ДИАГНОСТИКА ПЕРЕД MARGINFI ВЫЗОВОМ!
      console.log(`🔍 ДЕТАЛЬНАЯ ДИАГНОСТИКА ПЕРЕД MARGINFI:`);
      console.log(`   amount (исходное): ${amount}`);
      console.log(`   amountUI (UI формат): ${amountUI} ${tokenSymbol}`);
      console.log(`   bank.address: ${bank.address.toString()}`);
      console.log(`   bank.address тип: ${typeof bank.address}`);
      console.log(`   this.marginfiAccount тип: ${typeof this.marginfiAccount}`);
      console.log(`   🔥 makeBorrowIx УДАЛЕН! Используем ТОЛЬКО buildFlashLoanTx!`);

      // 🔥 ФИНАЛЬНАЯ ПРОВЕРКА ПЕРЕД MARGINFI ВЫЗОВОМ
      if (typeof amountUI !== 'number' || isNaN(amountUI) || amountUI <= 0) {
        throw new Error(`ASSERTION FAILED: amountUI должен быть положительным числом! Получено: ${amountUI} (тип: ${typeof amountUI})`);
      }

      if (!bank || !bank.address) {
        throw new Error(`ASSERTION FAILED: bank или bank.address не определен! bank=${bank}`);
      }

      console.log(`✅ ВСЕ ПРОВЕРКИ ПРОЙДЕНЫ - ИСПОЛЬЗУЕМ buildFlashLoanTx (makeBorrowIx УДАЛЕН!)`);

      // Удалены неиспользуемые переменные borrowIxPromise, repayIxPromise

      // 🔥 УБИРАЕМ TIMEOUT - ПУСТЬ MARGINFI РАБОТАЕТ НОРМАЛЬНО!

      // 🔥 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: НЕ СОЗДАЕМ BORROW/REPAY ИНСТРУКЦИИ!
      // buildFlashLoanTx создает их автоматически согласно официальной документации
      console.log(`🔥 ИСПРАВЛЕНО: buildFlashLoanTx создает borrow/repay автоматически`);
      console.log(`📚 ОФИЦИАЛЬНАЯ ДОКУМЕНТАЦИЯ: передаем только арбитражные инструкции`);

      // Убираем создание borrow/repay инструкций - они больше не нужны!
      // try {
      //   console.log(`🔥 СОЗДАЕМ BORROW ИНСТРУКЦИИ ДЛЯ FLASH LOAN...`);
      //   const borrowPromise = this.marginfiAccount.makeBorrowIx(amountUI, bank.address);
      //   borrowIxPromise = await Promise.race([borrowPromise, marginfiTimeout]);
      // } catch (borrowError) {
      //   throw new Error(`MarginFi makeBorrowIx failed: ${borrowError.message}`);
      // }
      //
      // try {
      //   const repayPromise = this.marginfiAccount.makeRepayIx(amountUI, bank.address, true);
      //   repayIxPromise = await Promise.race([repayPromise, marginfiTimeout]);
      // } catch (repayError) {
      //   throw new Error(`MarginFi makeRepayIx failed: ${repayError.message}`);
      // }

      // 🔥 FLASH LOAN КОМИССИЯ 0.09% - АТОМАРНОСТЬ ГАРАНТИРУЕТ ВОЗВРАТ!
      const feeAmount = Math.floor(amount * 0.0009); // 0.09% комиссия MarginFi Flash Loan
      const repayAmount = amount + feeAmount; // Точная сумма для возврата

      if (arbitrageInstructions.length > 0) {
        console.log(`💰 Арбитражные инструкции должны создать минимум ${feeAmount / Math.pow(10, tokenSymbol === 'SOL' ? 9 : 6)} ${tokenSymbol} прибыли для покрытия комиссии`);
        console.log(`🎯 Цель: Занять ${amountUI} ${tokenSymbol} → Заработать ${(repayAmount / Math.pow(10, tokenSymbol === 'SOL' ? 9 : 6)).toFixed(6)} ${tokenSymbol} → Прибыль ${(feeAmount / Math.pow(10, tokenSymbol === 'SOL' ? 9 : 6)).toFixed(6)}+ ${tokenSymbol}`);
      } else {
        console.log(`⚠️ ВНИМАНИЕ: Нет арбитражных инструкций - flash loan провалится!`);
        console.log(`💡 Нужны инструкции для создания ${(feeAmount / Math.pow(10, tokenSymbol === 'SOL' ? 9 : 6)).toFixed(6)} ${tokenSymbol} прибыли`);
      }

      // 🔥 ИСПРАВЛЕНО: buildFlashLoanTx создает borrow/repay автоматически
      console.log(`🔧 Создание flash loan с ${arbitrageInstructions.length} арбитражными инструкциями`);
      console.log(`📚 ОФИЦИАЛЬНАЯ ДОКУМЕНТАЦИЯ: borrow/repay создаются автоматически`);

      // ✅ ПРОВЕРЯЕМ ЧТО У НАС ЕСТЬ АРБИТРАЖНЫЕ ИНСТРУКЦИИ
      if (arbitrageInstructions.length === 0) {
        console.log('⚠️ ВНИМАНИЕ: Нет арбитражных инструкций - flash loan будет убыточным!');
      } else {
        console.log(`✅ Арбитражные инструкции: ${arbitrageInstructions.length} шт.`);
      }

      // ✅ ОБНОВЛЯЕМ BLOCKHASH ТОЛЬКО ПРИ НЕОБХОДИМОСТИ
      await this.updateBlockhashIfNeeded();

      // ✅ ИСПРАВЛЕНО: СОЗДАЕМ FLASH LOAN С АРБИТРАЖНЫМИ ИНСТРУКЦИЯМИ
      console.log(`🔧 Создаем flash loan с ${arbitrageInstructions.length} арбитражными инструкциями...`);
      console.log(`💡 Арбитражные инструкции обеспечат прибыльность flash loan`);

      // 🔧 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Нормализуем ТОЛЬКО арбитражные инструкции
      // Borrow/Repay инструкции создаются автоматически в buildFlashLoanTx
      const normalizedArbitrageInstructions = normalizeInstructions(arbitrageInstructions);

      console.log(`🔍 ДИАГНОСТИКА НОРМАЛИЗОВАННЫХ АРБИТРАЖНЫХ ИНСТРУКЦИЙ:`);
      console.log(`   Arbitrage инструкций: ${normalizedArbitrageInstructions.length}`);
      console.log(`   Borrow/Repay: создаются автоматически в buildFlashLoanTx`);

      // Проверяем что у нас есть хотя бы арбитражные инструкции или разрешаем пустые для тестирования
      if (normalizedArbitrageInstructions.length === 0) {
        console.log('⚠️ ВНИМАНИЕ: Flash loan без арбитражных инструкций (только тестирование)');
      }

      // Проверяем каждую нормализованную арбитражную инструкцию
      normalizedArbitrageInstructions.forEach((ix, index) => {
        if (!ix || !ix.programId || !ix.keys) {
          console.error(`❌ НОРМАЛИЗОВАННАЯ АРБИТРАЖНАЯ ИНСТРУКЦИЯ ${index + 1} НЕВАЛИДНА:`, ix);
          throw new Error(`Нормализованная арбитражная инструкция ${index + 1} невалидна`);
        }
        // Дополнительная проверка что programId является PublicKey
        if (!(ix.programId instanceof PublicKey)) {
          console.error(`❌ АРБИТРАЖНАЯ ИНСТРУКЦИЯ ${index + 1}: programId не является PublicKey:`, ix.programId);
          throw new Error(`Арбитражная инструкция ${index + 1}: programId должен быть PublicKey`);
        }
      });

      console.log(`✅ Все ${normalizedArbitrageInstructions.length} нормализованных арбитражных инструкций валидны`);

      // 🔥 ПРОВЕРЯЕМ ALT ТАБЛИЦЫ (НУЖНЫ ТОЛЬКО ДЛЯ JUPITER)
      console.log(`🔧 Проверяем ALT таблицы для buildFlashLoanTx...`);

      // Определяем ALT таблицы из параметров или используем пустой массив
      let altAccounts = [];

      if (typeof jupiterALTAccounts !== 'undefined' && jupiterALTAccounts) {
        altAccounts = jupiterALTAccounts;
        console.log(`✅ Используем Jupiter ALT таблицы: ${altAccounts.length} шт.`);
      } else if (versionedTransactionOptions && versionedTransactionOptions.addressLookupTableAccounts) {
        altAccounts = versionedTransactionOptions.addressLookupTableAccounts;
        console.log(`✅ Используем ALT из versionedTransactionOptions: ${altAccounts.length} шт.`);
      } else {
        console.log(`⚠️ ALT таблицы не предоставлены - используем пустой массив`);
        console.log(`💡 Для простых Flash Loans без Jupiter ALT не обязательны`);
        altAccounts = [];
      }

      console.log(`📊 Итого ALT таблиц: ${altAccounts.length}`);

      // 🔥 ИСПОЛЬЗУЕМ ОФИЦИАЛЬНЫЙ buildFlashLoanTx ПО ДОКУМЕНТАЦИИ!
      console.log(`🔥 ИСПОЛЬЗУЕМ ОФИЦИАЛЬНЫЙ buildFlashLoanTx:`);
      console.log(`   📋 Arbitrage инструкций: ${normalizedArbitrageInstructions.length}`);
      console.log(`   📋 Borrow/Repay: создаются автоматически в buildFlashLoanTx`);

      // 🔥 ИСПРАВЛЯЕМ buildFlashLoanTx ЧТОБЫ ОН СОЗДАВАЛ ВСЕ 4 ИНСТРУКЦИИ!
      console.log(`🔥 ИСПРАВЛЯЕМ buildFlashLoanTx ПО ОБРАЗЦУ ИЗ БЭКАПА!`);
      console.log(`🎯 ЦЕЛЬ: ВСЕ 4 MarginFi инструкции как в успешной сделке!`);

      // 🔥 ПРАВИЛЬНАЯ РЕАЛИЗАЦИЯ ИЗ БЭКАПА!
      const endIndex = normalizedArbitrageInstructions.length + 1;
      console.log(`📊 Вычисленный endIndex: ${endIndex}`);

      const projectedActiveBalances = this.marginfiAccount.projectActiveBalancesNoCpi(
        this.marginfiAccount._program,
        normalizedArbitrageInstructions
      );
      console.log(`📊 Projected balances: ${projectedActiveBalances.length}`);

      // ✅ СОЗДАЕМ start_flashloan ИНСТРУКЦИЮ
      const beginFlashLoanIx = await this.marginfiAccount.makeBeginFlashLoanIx(endIndex);
      console.log('✅ Begin flash loan инструкция создана');

      // ✅ СОЗДАЕМ end_flashloan ИНСТРУКЦИЮ
      const endFlashLoanIx = await this.createOfficialEndFlashLoanIx(projectedActiveBalances, endIndex);
      console.log('✅ End flash loan инструкция создана С ПРАВИЛЬНЫМ endIndex!');

      // ✅ СОБИРАЕМ ВСЕ ИНСТРУКЦИИ КАК В БЭКАПЕ!
      const flashloanIxs = [
        ...beginFlashLoanIx.instructions,        // 1. start_flashloan
        ...normalizedArbitrageInstructions,      // 2. borrow + arbitrage + repay
        ...endFlashLoanIx.instructions           // 3. end_flashloan
      ];
      console.log(`📊 Всего flash loan инструкций: ${flashloanIxs.length}`);

      // ✅ СОЗДАЕМ VERSIONED TRANSACTION КАК В БЭКАПЕ!
      const { TransactionMessage, VersionedTransaction } = require('@solana/web3.js');

      const blockhash = (await this.marginfiAccount._program.provider.connection.getLatestBlockhash("confirmed")).blockhash;

      const message = new TransactionMessage({
        payerKey: this.marginfiClient.wallet.publicKey,
        recentBlockhash: blockhash,
        instructions: flashloanIxs,
      }).compileToV0Message(altAccounts);

      const flashLoanTx = new VersionedTransaction(message);
      console.log('🎯 ПРАВИЛЬНАЯ flash loan транзакция создана ПО ОБРАЗЦУ ИЗ БЭКАПА!');

      // ✅ КРИТИЧЕСКАЯ ПРОВЕРКА РЕЗУЛЬТАТА buildFlashLoanTx
      console.log(`🔍 ДИАГНОСТИКА РЕЗУЛЬТАТА buildFlashLoanTx:`);
      console.log(`   flashLoanTx: ${flashLoanTx ? 'есть' : 'undefined'}`);
      console.log(`   flashLoanTx.constructor.name: ${flashLoanTx?.constructor?.name}`);

      if (flashLoanTx.message) {
        console.log(`   flashLoanTx.message: есть`);
        console.log(`   flashLoanTx.message.instructions: ${flashLoanTx.message.instructions?.length || 0}`);
        console.log(`   flashLoanTx.message.compiledInstructions: ${flashLoanTx.message.compiledInstructions?.length || 0}`);
      } else {
        console.log(`   flashLoanTx.message: НЕТ`);
      }

      if (flashLoanTx.instructions) {
        console.log(`   flashLoanTx.instructions: ${flashLoanTx.instructions.length}`);
      } else {
        console.log(`   flashLoanTx.instructions: НЕТ`);
      }

      // Проверяем что транзакция содержит инструкции
      const hasInstructions = (flashLoanTx.message?.compiledInstructions?.length > 0) ||
                             (flashLoanTx.message?.instructions?.length > 0) ||
                             (flashLoanTx.instructions?.length > 0);

      if (!hasInstructions) {
        console.error(`❌ КРИТИЧЕСКАЯ ОШИБКА: buildFlashLoanTx вернул транзакцию БЕЗ ИНСТРУКЦИЙ!`);
        console.error(`   Входные арбитражные инструкции: ${normalizedArbitrageInstructions.length}`);
        console.error(`   Результат: пустая транзакция`);
        throw new Error('buildFlashLoanTx вернул транзакцию без инструкций');
      }

      console.log(`✅ Flash loan транзакция содержит инструкции`);

      // 🔧 ДЕКОМПИЛИРУЕМ VERSIONED TRANSACTION ДЛЯ ИЗВЛЕЧЕНИЯ ВСЕХ ИНСТРУКЦИЙ
      console.log(`\n🔧 ДЕКОМПИЛЯЦИЯ VERSIONED TRANSACTION...`);
      const decompiled = this.decompileVersionedTransaction(flashLoanTx, altAccounts);

      console.log(`✅ Декомпиляция завершена:`);
      console.log(`   📊 Всего инструкций: ${decompiled.instructions.length}`);
      console.log(`   🏦 MarginFi инструкций: ${decompiled.marginfiInstructions}`);
      console.log(`   🔄 Арбитражных инструкций: ${decompiled.arbitrageInstructions}`);
      console.log(`   🗜️ ALT таблиц: ${decompiled.addressLookupTableAccounts.length}`);

      this.stats.successfulLoans++;
      this.stats.totalVolume += amount; // Используем исходное число для статистики

      const elapsedTime = Date.now() - startTime;
      console.log(`✅ Flash loan создан за ${elapsedTime}мс`);

      if (elapsedTime > 14) {
        console.log(`⚠️ МЕДЛЕННО! Цель: < 14мс, факт: ${elapsedTime}мс`);
      } else {
        console.log(`🚀 БЫСТРО! Создано за ${elapsedTime}мс (цель: < 14мс)`);
      }

      return {
        success: true,
        transaction: flashLoanTx,
        instructions: decompiled.instructions, // 🔥 ДЕКОМПИЛИРОВАННЫЕ ИНСТРУКЦИИ!
        addressLookupTableAccounts: decompiled.addressLookupTableAccounts, // 🔥 ALT ТАБЛИЦЫ!
        amount: amount, // Используем исходное число для совместимости
        bank: bank.address.toString(),
        fee: feeAmount, // Обычное число для совместимости
        marginfiAccount: this.marginfiAccount,
        stats: this.stats,
        creationTime: elapsedTime,

        // 🔥 СТРУКТУРА FLASH LOAN
        marginfiStructure: {
          totalInstructions: decompiled.instructions.length,
          marginfiInstructions: decompiled.marginfiInstructions,
          arbitrageInstructions: decompiled.arbitrageInstructions,
          hasFlashLoanStart: decompiled.hasFlashLoanStart,
          hasFlashLoanEnd: decompiled.hasFlashLoanEnd
        },

        // 🔥 НОВЫЕ ПОЛЯ: VersionedTransaction данные
        arbitrageInstructions: arbitrageInstructions,
        addressLookupTableAddresses: versionedTransactionOptions?.addressLookupTableAddresses || [],
        officialSolanaMethod: versionedTransactionOptions?.officialSolanaMethod || false,
        versionedTransactionData: versionedTransactionOptions?.versionedTransactionData || null,
        method: versionedTransactionOptions?.officialSolanaMethod ? 'VERSIONED_TRANSACTION' : 'LEGACY_INSTRUCTIONS'
      };

    } catch (error) {
      this.stats.failedLoans++;
      const elapsedTime = Date.now() - startTime;
      console.error(`❌ Ошибка создания flash loan за ${elapsedTime}мс: ${error.message}`);

      // 🚨 НЕ ВОЗВРАЩАЕМ FALLBACK - ТОЛЬКО MARGINFI!
      throw error;
    }
  }

  /**
   * 🔧 ДЕКОМПИЛЯЦИЯ VERSIONED TRANSACTION
   * Извлекает все инструкции из VersionedTransaction включая MarginFi
   */
  decompileVersionedTransaction(versionedTx, addressLookupTableAccounts) {
    try {
      console.log(`🔧 ДЕКОМПИЛЯЦИЯ VERSIONED TRANSACTION...`);

      // Импортируем TransactionMessage для декомпиляции
      const { TransactionMessage } = require('@solana/web3.js');

      console.log(`🔍 Декомпилируем с ${addressLookupTableAccounts.length} ALT таблицами...`);

      // Официальная декомпиляция по документации Solana
      const decompiled = TransactionMessage.decompile(versionedTx.message, {
        addressLookupTableAccounts: addressLookupTableAccounts
      });

      const instructions = decompiled.instructions;

      console.log(`✅ ДЕКОМПИЛЯЦИЯ УСПЕШНА:`);
      console.log(`   📊 Всего инструкций: ${instructions.length}`);

      // Анализируем типы инструкций
      let marginfiInstructions = 0;
      let arbitrageInstructions = 0;
      let hasFlashLoanStart = false;
      let hasFlashLoanEnd = false;

      instructions.forEach((instruction, index) => {
        const programId = instruction.programId.toString();

        if (programId === 'MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA') {
          marginfiInstructions++;

          // Проверяем тип MarginFi инструкции по data
          const data = instruction.data;
          if (data && data.length > 0) {
            const discriminatorHex = Buffer.from(data.slice(0, 8)).toString('hex');

            // Проверяем на start/end flash loan (примерные discriminators)
            if (discriminatorHex.includes('start') || discriminatorHex.includes('begin')) {
              hasFlashLoanStart = true;
            } else if (discriminatorHex.includes('end')) {
              hasFlashLoanEnd = true;
            }
          }
        } else {
          arbitrageInstructions++;
        }
      });

      console.log(`🔍 АНАЛИЗ ДЕКОМПИЛИРОВАННЫХ ИНСТРУКЦИЙ:`);
      console.log(`   🏦 MarginFi инструкций: ${marginfiInstructions}`);
      console.log(`   🔄 Арбитражных инструкций: ${arbitrageInstructions}`);
      console.log(`   🚀 Flash Loan Start: ${hasFlashLoanStart ? '✅' : '❌'}`);
      console.log(`   🏁 Flash Loan End: ${hasFlashLoanEnd ? '✅' : '❌'}`);

      if (marginfiInstructions > 0) {
        console.log(`🎉 УСПЕХ! MarginFi инструкции найдены в декомпилированной транзакции!`);
      } else {
        console.log(`⚠️ MarginFi инструкции не найдены - возможно проблема с Flash Loan`);
      }

      return {
        instructions: instructions,
        addressLookupTableAccounts: addressLookupTableAccounts,
        marginfiInstructions: marginfiInstructions,
        arbitrageInstructions: arbitrageInstructions,
        hasFlashLoanStart: hasFlashLoanStart,
        hasFlashLoanEnd: hasFlashLoanEnd
      };

    } catch (decompileError) {
      console.error(`❌ Ошибка декомпиляции VersionedTransaction: ${decompileError.message}`);
      console.log(`📚 Проверьте: addressLookupTableAccounts, VersionedTransaction format`);

      // Возвращаем пустой результат в случае ошибки
      return {
        instructions: [],
        addressLookupTableAccounts: addressLookupTableAccounts,
        marginfiInstructions: 0,
        arbitrageInstructions: 0,
        hasFlashLoanStart: false,
        hasFlashLoanEnd: false
      };
    }
  }

  /**
   * 🔧 СОЗДАТЬ РЕАЛЬНЫЕ АРБИТРАЖНЫЕ ИНСТРУКЦИИ
   */
  async createRealArbitrageInstructions(inputMint, intermediateMint, finalMint, amount, arbitrageType = 'simple') {
    try {
      console.log(`🔧 Создание РЕАЛЬНЫХ арбитражных инструкций: ${arbitrageType}`);
      console.log(`   Путь: ${inputMint} → ${intermediateMint} → ${finalMint || 'same'}`);
      console.log(`   Сумма: ${amount}`);

      let instructions = [];

      if (arbitrageType === 'simple') {
        // Простой swap: input → intermediate
        const swapData = await this.jupiterSwapInstructions.createSimpleSwapInstructions(
          inputMint,
          intermediateMint,
          amount
        );
        instructions = swapData.instructions;
        console.log(`✅ Создано ${instructions.length} простых swap инструкций`);

      } else if (arbitrageType === 'triangular') {
        // ✅ КРУГОВОЙ АРБИТРАЖ: input → intermediate → final (должен быть тот же что input)
        console.log(`🔄 Круговой арбитраж: ${inputMint.slice(0, 8)}... → ${intermediateMint.slice(0, 8)}... → ${finalMint.slice(0, 8)}...`);

        const arbitrageData = await this.jupiterSwapInstructions.createCircularArbitrageInstructions(
          inputMint,
          intermediateMint,
          finalMint,
          amount
        );
        instructions = arbitrageData.instructions;
        console.log(`✅ Создано ${instructions.length} круговых арбитражных инструкций`);
        console.log(`💰 Ожидаемая прибыль: ${arbitrageData.expectedProfit || 'неизвестно'} (${arbitrageData.profitPercent || 0}%)`);

        // 🔥 ИСПРАВЛЕНО: ИЗВЛЕКАЕМ ALT ИЗ ARBITRAGE DATA!
        if (arbitrageData.addressLookupTableAccounts) {
          jupiterALTAccounts = arbitrageData.addressLookupTableAccounts;
          console.log(`✅ Извлечено ${jupiterALTAccounts.length} ALT из arbitrageData`);
        }
        if (arbitrageData.addressLookupTableAddresses) {
          jupiterALTAddresses = arbitrageData.addressLookupTableAddresses;
          console.log(`✅ Извлечено ${jupiterALTAddresses.length} ALT адресов из arbitrageData`);
        }

        // ✅ ПРОВЕРЯЕМ ЧТО ПРИБЫЛЬ ПОКРЫВАЕТ КОМИССИЮ FLASH LOAN
        const feeAmount = Math.floor(amount * 0.0009); // 0.09% комиссия MarginFi
        if (arbitrageData.expectedProfit && arbitrageData.expectedProfit < feeAmount) {
          console.log(`⚠️ ВНИМАНИЕ: Прибыль ${arbitrageData.expectedProfit} < комиссия ${feeAmount}`);
          console.log(`💡 Арбитраж может быть убыточным!`);
        } else if (arbitrageData.expectedProfit) {
          console.log(`✅ Прибыль ${arbitrageData.expectedProfit} > комиссия ${feeAmount} - арбитраж прибыльный!`);
        } else {
          console.log(`💡 Прибыль не рассчитана - продолжаем с арбитражем`);
        }

      } else {
        throw new Error(`Неподдерживаемый тип арбитража: ${arbitrageType}`);
      }

      // Валидируем инструкции
      this.jupiterSwapInstructions.validateInstructions(instructions);

      // Анализируем инструкции
      const analysis = this.jupiterSwapInstructions.analyzeInstructions(instructions);
      console.log(`📊 Анализ: ${analysis.instructionCount} инструкций, ${analysis.uniquePrograms} программ`);

      return instructions;

    } catch (error) {
      console.error(`❌ Ошибка создания арбитражных инструкций: ${error.message}`);
      throw error;
    }
  }

  /**
   * 🚀 ВЫПОЛНИТЬ FLASH LOAN ЧЕРЕЗ QUICKNODE
   */
  async executeFlashLoan(tokenMint, amount, arbitrageCallback) {
    try {
      console.log(`🚀 Выполнение MarginFi flash loan: ${amount} ${tokenMint}`);
      console.log(`🌐 RPC: QuickNode (платный, поддерживает MarginFi)`);

      // ✅ СОЗДАЕМ РЕАЛЬНЫЕ АРБИТРАЖНЫЕ ИНСТРУКЦИИ
      let arbitrageInstructions = [];

      if (arbitrageCallback && typeof arbitrageCallback === 'function') {
        // Используем callback если предоставлен
        arbitrageInstructions = await arbitrageCallback(amount);
        console.log(`🔧 Получено ${arbitrageInstructions.length} инструкций от callback`);
      } else {
        // ✅ СОЗДАЕМ РЕАЛЬНЫЕ JUPITER КРУГОВОЙ АРБИТРАЖ ИНСТРУКЦИИ
        console.log('🪐 Создаем реальные Jupiter круговой арбитраж инструкции...');

        // 🔥 ИСПРАВЛЕНО: Конвертируем tokenMint в строку для сравнения
        const tokenMintStr = typeof tokenMint === 'string' ? tokenMint : tokenMint.toString();
        console.log(`💰 Flash loan: ${amount} lamports ${tokenMintStr === 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v' ? 'USDC' : 'SOL'}`);

        // ✅ ИСПОЛЬЗУЕМ СПЕЦИАЛЬНЫЙ FLASH LOAN АРБИТРАЖ
        console.log('🔄 Создаем СПЕЦИАЛЬНЫЕ flash loan арбитражные инструкции...');

        if (tokenMintStr === 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v') {
          console.log('💰 Flash loan арбитраж: USDC → SOL → USDC');

          // 🔥 ИСПОЛЬЗУЕМ НОВЫЙ МУЛЬТИХОП МЕТОД!
          if (this.enableMultihop) {
            console.log('🔥 ИСПОЛЬЗУЕМ МУЛЬТИХОП АРХИТЕКТУРУ!');
            const arbitrageData = await this.jupiterSwapInstructions.createCircularArbitrageInstructions(
              tokenMint, // baseMint: USDC (flash loan токен)
              'So********************************111111112', // intermediateMint: SOL
              amount, // Сумма займа
              100 // 1% slippage для мультихоп
            );

            // 🔥 ОБНОВЛЯЕМ СТАТИСТИКУ
            this.stats.multihopLoans++;

            // 🔥 ИСПРАВЛЕНО: ИСПОЛЬЗУЕМ arbitrageData
            arbitrageInstructions = arbitrageData.instructions;

          } else {
            // 🔥 FALLBACK УДАЛЕН! ТОЛЬКО FLASH LOAN ТОРГОВЛЯ!
            throw new Error('❌ МУЛЬТИХОП НЕДОСТУПЕН - FALLBACK УДАЛЕН! Используйте только Flash Loan торговлю!');
          }

          // 🔥 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: ИЗВЛЕКАЕМ ALT ИЗ JUPITER РЕЗУЛЬТАТА!
          jupiterALTAccounts = arbitrageData.addressLookupTableAccounts || [];
          jupiterALTAddresses = arbitrageData.addressLookupTableAddresses || [];

          console.log(`🔥 ИЗВЛЕЧЕНЫ JUPITER ALT:`);
          console.log(`   📊 ALT аккаунтов: ${jupiterALTAccounts.length}`);
          console.log(`   📊 ALT адресов: ${jupiterALTAddresses.length}`);

          console.log(`💰 Flash loan арбитраж результат:`);
          console.log(`   Занимаем: ${arbitrageData.borrowedAmount} USDC`);
          console.log(`   Нужно вернуть: ${arbitrageData.requiredReturn} USDC`);
          console.log(`   Получим: ${arbitrageData.finalAmount} USDC`);
          console.log(`   Прибыль: ${arbitrageData.profit} USDC (${arbitrageData.isProfitable ? '✅' : '❌'})`);

        } else {
          console.log('💰 Flash loan арбитраж: SOL → USDC → SOL');

          // 🔥 ИСПОЛЬЗУЕМ НОВЫЙ МУЛЬТИХОП МЕТОД!
          if (this.enableMultihop) {
            console.log('🔥 ИСПОЛЬЗУЕМ МУЛЬТИХОП АРХИТЕКТУРУ!');
            const arbitrageData = await this.jupiterSwapInstructions.createCircularArbitrageInstructions(
              tokenMint, // baseMint: SOL (flash loan токен)
              'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // intermediateMint: USDC
              amount, // Сумма займа
              100 // 1% slippage для мультихоп
            );

            // 🔥 ОБНОВЛЯЕМ СТАТИСТИКУ
            this.stats.multihopLoans++;

            // 🔥 ИСПРАВЛЕНО: ИСПОЛЬЗУЕМ arbitrageData
            arbitrageInstructions = arbitrageData.instructions;

          } else {
            // 🔥 FALLBACK УДАЛЕН! ТОЛЬКО FLASH LOAN ТОРГОВЛЯ!
            throw new Error('❌ МУЛЬТИХОП НЕДОСТУПЕН - FALLBACK УДАЛЕН! Используйте только Flash Loan торговлю!');
          }

          // 🔥 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: ИЗВЛЕКАЕМ ALT ИЗ JUPITER РЕЗУЛЬТАТА!
          jupiterALTAccounts = arbitrageData.addressLookupTableAccounts || [];
          jupiterALTAddresses = arbitrageData.addressLookupTableAddresses || [];

          console.log(`🔥 ИЗВЛЕЧЕНЫ JUPITER ALT:`);
          console.log(`   📊 ALT аккаунтов: ${jupiterALTAccounts.length}`);
          console.log(`   📊 ALT адресов: ${jupiterALTAddresses.length}`);

          console.log(`💰 Flash loan арбитраж результат:`);
          console.log(`   Занимаем: ${arbitrageData.borrowedAmount} SOL`);
          console.log(`   Нужно вернуть: ${arbitrageData.requiredReturn} SOL`);
          console.log(`   Получим: ${arbitrageData.finalAmount} SOL`);
          console.log(`   Прибыль: ${arbitrageData.profit} SOL (${arbitrageData.isProfitable ? '✅' : '❌'})`);
        }
      }

      if (arbitrageInstructions.length === 0) {
        console.log('⚠️ ВНИМАНИЕ: Создаем flash loan БЕЗ арбитражных инструкций (только тест)');
      }

      // 🚀 ИСПОЛЬЗУЕМ БЫСТРОЕ СОЗДАНИЕ ИЗ ШАБЛОНА
      console.log(`🚀 Используем БЫСТРОЕ создание flash loan из предподготовленного шаблона...`);
      const result = await this.createFastFlashLoan(tokenMint, amount, arbitrageInstructions);

      if (!result.success) {
        throw new Error(result.error);
      }

      // 🔥 УДАЛЕНО: sendTransactionWithRetry - ИСТОЧНИК ФЕЙКОВЫХ ПОДПИСЕЙ!
      // ✅ ТРАНЗАКЦИИ ОТПРАВЛЯЮТСЯ ТОЛЬКО ЧЕРЕЗ real-trading-executor.js
      throw new Error('ТРАНЗАКЦИИ НЕ ОТПРАВЛЯЮТСЯ ИЗ MARGINFI! Используйте real-trading-executor.js');

    } catch (error) {
      console.error('❌ Ошибка выполнения flash loan:', error.message);
      return {
        success: false,
        error: error.message,
        stats: this.stats
      };
    }
  }

  /**
   * 🚫 ОТПРАВКА ТРАНЗАКЦИЙ УДАЛЕНА ИЗ MARGINFI-FLASH-LOAN.JS!
   *
   * ✅ ПРАВИЛЬНАЯ АРХИТЕКТУРА:
   * - marginfi-flash-loan.js: ТОЛЬКО создание инструкций flash loan
   * - real-trading-executor.js: отправка транзакций
   */

  /**
   * 📊 ПОЛУЧИТЬ СТАТИСТИКУ
   */
  getStats() {
    return {
      ...this.stats,
      successRate: this.stats.totalLoans > 0 ? (this.stats.successfulLoans / this.stats.totalLoans * 100).toFixed(2) + '%' : '0%',
      averageVolume: this.stats.successfulLoans > 0 ? (this.stats.totalVolume / this.stats.successfulLoans).toFixed(2) : '0',
      totalFeesUSD: 0 // ВСЕГДА 0 для MarginFi!
    };
  }

  /**
   * 💡 ПРОВЕРКА ГОТОВНОСТИ К FLASH LOANS
   */
  async checkFlashLoanReadiness() {
    try {
      console.log('💡 Проверяем готовность к flash loans...');

      // Проверяем инициализацию
      if (!this.isInitialized) {
        console.log('❌ MarginFi не инициализирован');
        return false;
      }

      // Проверяем client
      if (!this.client) {
        console.log('❌ MarginFi client не создан');
        return false;
      }

      // Проверяем account
      if (!this.marginfiAccount) {
        console.log('❌ MarginFi account не создан');
        return false;
      }

      // Проверяем доступность банков
      const banks = await this.getAvailableBanks();
      if (banks.length === 0) {
        console.log('❌ Нет доступных банков для flash loans');
        return false;
      }

      console.log(`✅ Система готова к flash loans (${banks.length} банков доступно)`);
      return true;

    } catch (error) {
      console.error(`❌ Ошибка проверки готовности: ${error.message}`);
      return false;
    }
  }



  /**
   * 🏦 ПОЛУЧИТЬ ДОСТУПНЫЕ БАНКИ
   */
  async getAvailableBanks() {
    try {
      if (!this.client) {
        throw new Error('MarginFi client недоступен - банки недоступны');
      }

      console.log('🏦 Получение ТОЛЬКО 15 РЕАЛЬНЫХ банков с ликвидностью...');

      // ✅ РЕАЛЬНЫЕ БАНКИ С ПРОВЕРЕННЫМИ АДРЕСАМИ ИЗ MARGINFI SDK
      // 🔧 ТОЛЬКО ОСНОВНЫЕ ТОКЕНЫ ДЛЯ АРБИТРАЖА - УБРАЛИ JUP И BONK
      const REAL_BANKS_WITH_ADDRESSES = {
        'USDC': '2s37akK2eyBbp8DZgCm7RtsaEz8eJP3Nxd4urLHQv7yB',
        'SOL': 'CCKtUs6Cgwo4aaQUmBPmyoApH2gUDErxNZCAntD6LYGh',
        'USDT': 'HmpMfL8942u22htC4EMiWgLX931g3sacXFR6KjuLgKLV',
        'WBTC': 'BKsfDJCMbYep6gr9pq8PsmJbb5XGLHbAJzUV8vmorz7a',
        'ETH': 'BkUyfXjbBBALcfZvw76WAFRvYQ21xxMWWeoPtJrUqG3z',
        'mSOL': '22DcjMZrMwC5Bpa5AGBsmjc5V9VuQrXG6N9ZtdUNyYGE',
        'stSOL': '7TZABdVVzqtGwgtqHM6VS8E34LtFq4dogNvTWEH9QaaM'
        // ❌ УДАЛЕНЫ: JUP и BONK (нет возможности займов)
      };

      const bankInfo = [];

      for (const [tokenSymbol, bankAddress] of Object.entries(REAL_BANKS_WITH_ADDRESSES)) {
        try {
          // 🔇 ПОКАЗЫВАЕМ ЛОГИ ТОЛЬКО ДЛЯ ОСНОВНЫХ ТОКЕНОВ
          if (['SOL', 'USDC', 'USDT'].includes(tokenSymbol)) {
            console.log(`🔍 Проверяем банк: ${tokenSymbol} (${bankAddress.slice(0, 8)}...)...`);
          }

          // Получаем банк по адресу вместо символа
          const bankPubkey = new PublicKey(bankAddress);
          const bank = this.client.banks.get(bankPubkey.toString());

          if (bank) {
            // Проверяем ликвидность через правильные методы
            try {
              const remainingCapacity = bank.computeRemainingCapacity();
              const borrowCapacity = remainingCapacity.borrowCapacity;

              // Только банки с возможностью займов
              if (borrowCapacity > 0) {
                bankInfo.push({
                  address: bankAddress,
                  mint: bank.mint.toString(),
                  symbol: tokenSymbol,
                  borrowCapacity: borrowCapacity.toString(),
                  depositCapacity: remainingCapacity.depositCapacity.toString(),
                  borrowLimit: bank.config?.borrowLimit?.toString() || '0',
                  canBorrow: true
                });

                // 🔇 ПОКАЗЫВАЕМ ТОЛЬКО ОСНОВНЫЕ ТОКЕНЫ (SOL, USDC, USDT)
                if (['SOL', 'USDC', 'USDT'].includes(tokenSymbol)) {
                  console.log(`✅ ${tokenSymbol}: Borrow Capacity ${borrowCapacity.toString()}`);
                }
              } else {
                // 🔇 УБИРАЕМ ЛОГИ ДЛЯ НЕПОДДЕРЖИВАЕМЫХ ТОКЕНОВ (JUP, BONK и др.)
                if (['SOL', 'USDC', 'USDT'].includes(tokenSymbol)) {
                  console.log(`❌ ${tokenSymbol}: нет возможности займов`);
                }
              }
            } catch (capacityError) {
              console.log(`⚠️ ${tokenSymbol}: ошибка получения capacity - ${capacityError.message}`);
              // Добавляем банк даже без capacity данных
              bankInfo.push({
                address: bankAddress,
                mint: bank.mint.toString(),
                symbol: tokenSymbol,
                borrowCapacity: 'Unknown',
                depositCapacity: 'Unknown',
                borrowLimit: bank.config?.borrowLimit?.toString() || '0',
                canBorrow: true
              });
            }
          } else {
            console.log(`❌ ${tokenSymbol}: банк не найден по адресу ${bankAddress}`);
          }
        } catch (bankError) {
          console.log(`⚠️ Ошибка банка ${tokenSymbol}: ${bankError.message}`);
          continue;
        }
      }

      console.log(`✅ Найдено РЕАЛЬНЫХ банков с ликвидностью: ${bankInfo.length}/15`);
      return bankInfo;
    } catch (error) {
      console.error('❌ Ошибка получения банков:', error.message);
      return [];
    }
  }

  /**
   * ✅ ПРОВЕРИТЬ И СОЗДАТЬ ТОКЕН АККАУНТ ЕСЛИ НУЖНО
   */
  async ensureTokenAccountExists(tokenMint) {
    try {
      const { getAssociatedTokenAddress, createAssociatedTokenAccountInstruction } = require('@solana/spl-token');

      console.log(`🔍 Проверяем токен аккаунт для ${tokenMint}...`);

      const mintPubkey = new PublicKey(tokenMint);
      const walletPubkey = this.wallet.publicKey;

      // Получаем адрес Associated Token Account
      const associatedTokenAddress = await getAssociatedTokenAddress(
        mintPubkey,
        walletPubkey
      );

      // Проверяем существует ли аккаунт
      const accountInfo = await this.connection.getAccountInfo(associatedTokenAddress);

      if (!accountInfo) {
        console.log(`⚠️ Токен аккаунт не существует, создаем: ${associatedTokenAddress.toString()}`);

        // Создаем инструкцию для создания Associated Token Account
        const createATAInstruction = createAssociatedTokenAccountInstruction(
          walletPubkey, // payer
          associatedTokenAddress, // ata
          walletPubkey, // owner
          mintPubkey // mint
        );

        // Создаем и отправляем транзакцию
        const { Transaction } = require('@solana/web3.js');
        const transaction = new Transaction().add(createATAInstruction);

        const signature = await this.connection.sendTransaction(transaction, [this.wallet], {
          skipPreflight: true,  // 🔥 СИМУЛЯЦИЯ ПОЛНОСТЬЮ УДАЛЕНА!
          preflightCommitment: 'processed',
          maxRetries: 0  // 🔥 БЕЗ РЕТРАЕВ - ПРЯМАЯ ОТПРАВКА!
        });

        console.log(`✅ Токен аккаунт создан: ${signature}`);

        // Ждем подтверждения
        // 🔥 УДАЛЕНО: confirmTransaction - НЕ ЖДЕМ ПОДТВЕРЖДЕНИЯ!
      } else {
        console.log(`✅ Токен аккаунт уже существует: ${associatedTokenAddress.toString()}`);
      }

      return associatedTokenAddress;
    } catch (error) {
      console.error(`❌ Ошибка создания токен аккаунта: ${error.message}`);
      // Не выбрасываем ошибку, так как аккаунт может быть создан автоматически
      return null;
    }
  }

  /**
   * 💰 ПРОВЕРИТЬ БАЛАНС КОШЕЛЬКА
   */
  async checkWalletBalance() {
    try {
      // 🔥 ИСПОЛЬЗУЕМ ТЕКУЩИЙ КОШЕЛЕК ИЗ СИСТЕМЫ!
      const CURRENT_WALLET = this.wallet.publicKey;

      console.log(`🎯 checkWalletBalance: Проверяем ТЕКУЩИЙ кошелек: ${CURRENT_WALLET.toString()}`);
      console.log(`🔍 this.wallet.publicKey: ${this.wallet.publicKey.toString()}`);

      // Проверяем SOL баланс ТЕКУЩЕГО кошелька
      const solBalance = await this.connection.getBalance(CURRENT_WALLET);
      const solBalanceInSol = solBalance / 1e9; // Конвертируем lamports в SOL

      // Проверяем USDC баланс ПРАВИЛЬНОГО кошелька
      let usdcBalance = 0;
      try {
        const { getAssociatedTokenAddress } = require('@solana/spl-token');
        const usdcMint = new PublicKey('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v');
        const usdcTokenAccount = await getAssociatedTokenAddress(usdcMint, CORRECT_WALLET);

        const usdcAccountInfo = await this.connection.getTokenAccountBalance(usdcTokenAccount);
        if (usdcAccountInfo && usdcAccountInfo.value) {
          usdcBalance = parseFloat(usdcAccountInfo.value.uiAmount) || 0;
        }
      } catch (usdcError) {
        console.log(`⚠️ USDC аккаунт не найден или пустой: ${usdcError.message}`);
        usdcBalance = 0;
      }

      console.log(`💰 Баланс кошелька: ${solBalanceInSol.toFixed(6)} SOL, ${usdcBalance.toFixed(2)} USDC`);

      return {
        sol: solBalanceInSol,
        usdc: usdcBalance,
        solLamports: solBalance
      };
    } catch (error) {
      console.error(`❌ Ошибка проверки баланса: ${error.message}`);
      return {
        sol: 0,
        usdc: 0,
        solLamports: 0
      };
    }
  }

  /**
   * 🚀 БЫСТРЫЙ РЕЖИМ ВКЛЮЧЕН - ПРЯМАЯ ТОРГОВЛЯ БЕЗ BUNDLE!
   */
  enableFastMode() {
    this.fastMode = true;
    this.enableBundleSimulation = false;
    console.log('🚀 БЫСТРЫЙ РЕЖИМ ВКЛЮЧЕН - прямая торговля без Bundle simulation!');
    console.log('⚡ Транзакции будут выполняться напрямую для максимальной скорости');
    console.log('⚠️ ВНИМАНИЕ: Повышенный риск, но максимальная скорость!');
  }

  // 🔥 БЕЗОПАСНЫЙ РЕЖИМ УДАЛЕН - ТОЛЬКО БЫСТРАЯ ТОРГОВЛЯ!

  /**
   * 📊 ПОЛУЧИТЬ НАСТРОЙКИ СКОРОСТИ
   */
  getSpeedSettings() {
    return {
      fastMode: this.fastMode,
      enableBundleSimulation: this.enableBundleSimulation,
      expectedSpeed: this.fastMode ? '21-53мс' : '100-200мс',
      safety: this.enableBundleSimulation ? 'высокая' : 'низкая'
    };
  }

  /**
   * 🧹 ОЧИСТКА РЕСУРСОВ
   */
  cleanup() {
    console.log('🧹 Очистка MarginFi Flash Loan ресурсов...');

    // Останавливаем автоматическое обновление blockhash если оно было запущено
    if (this.blockhashUpdateInterval) {
      clearInterval(this.blockhashUpdateInterval);
      this.blockhashUpdateInterval = null;
      console.log('🚫 Автоматическое обновление blockhash остановлено');
    }

    this.templateInstructions.clear();
    this.preparationInProgress.clear();
  }

  /**
   * 🚨 ОФИЦИАЛЬНЫЕ МЕТОДЫ FLASH LOAN ПО ОБРАЗЦУ MARGINFI
   * Используют lending_account_start_flashloan и lending_account_end_flashloan
   */

  /**
   * 🔧 СОЗДАНИЕ ОФИЦИАЛЬНОЙ lending_account_start_flashloan ИНСТРУКЦИИ
   */
  async makeBeginFlashLoanIx(endIndex) {
    try {
      console.log(`🔧 Создаем ОФИЦИАЛЬНУЮ lending_account_start_flashloan инструкцию через MarginFi SDK 6.1.0...`);
      console.log(`   End Index: ${endIndex}`);

      if (!this.marginfiAccount) {
        throw new Error('MarginFi account не инициализирован');
      }

      // 🚨 ПРОВЕРЯЕМ ЧТО ОФИЦИАЛЬНЫЙ МЕТОД СУЩЕСТВУЕТ (ПО ДОКУМЕНТАЦИИ)
      if (typeof this.marginfiAccount.makeBeginFlashLoanIx !== 'function') {
        console.log(`❌ makeBeginFlashLoanIx метод не найден в MarginFi SDK 6.1.0`);
        console.log(`💡 Возможно изменился API в новой версии`);
        console.log(`📋 Доступные методы:`, Object.getOwnPropertyNames(this.marginfiAccount).filter(name => typeof this.marginfiAccount[name] === 'function'));
        throw new Error('makeBeginFlashLoanIx метод не найден в MarginFi SDK 6.1.0 - возможно изменился API');
      }

      // 🚨 FALLBACK: СОЗДАЕМ ИНСТРУКЦИЮ С ТОЧНЫМ DISCRIMINATOR!
      console.log(`🔧 СОЗДАЕМ ИНСТРУКЦИЮ С ТОЧНЫМ DISCRIMINATOR ИЗ ОШИБКИ...`);
      console.log(`📋 Ожидаемый discriminator: [105, 124, 201, 106, 153, 2, 8, 156]`);

      const { TransactionInstruction, PublicKey } = require('@solana/web3.js');

      // MarginFi V2 Program ID
      const MARGINFI_PROGRAM_ID = new PublicKey('MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA');

      // 🚨 ИСПОЛЬЗУЕМ ТОЧНЫЙ DISCRIMINATOR ИЗ ОШИБКИ
      const exactDiscriminator = [105, 124, 201, 106, 153, 2, 8, 156];

      // Создаем instruction data с ТОЧНЫМ discriminator
      const instructionData = Buffer.alloc(16);
      Buffer.from(exactDiscriminator).copy(instructionData, 0);
      instructionData.writeBigUInt64LE(BigInt(endIndex), 8);

      console.log(`   📊 ТОЧНЫЙ discriminator: [${exactDiscriminator.join(', ')}]`);
      console.log(`   📊 End Index: ${endIndex}`);
      console.log(`   📊 Instruction Data: ${instructionData.toString('hex')}`);

      // 🚨 БЕЗОПАСНОЕ СОЗДАНИЕ PUBKEY ОБЪЕКТОВ С ДИАГНОСТИКОЙ
      console.log(`🔍 ДИАГНОСТИКА PUBKEY ОБЪЕКТОВ:`);
      console.log(`   marginfiAccount.address тип: ${typeof this.marginfiAccount.address}`);
      console.log(`   marginfiAccount.address значение: ${this.marginfiAccount.address}`);
      console.log(`   wallet.publicKey тип: ${typeof this.wallet.publicKey}`);
      console.log(`   wallet.publicKey значение: ${this.wallet.publicKey}`);

      let marginfiAccountPubkey;
      if (typeof this.marginfiAccount.address === 'string') {
        marginfiAccountPubkey = new PublicKey(this.marginfiAccount.address);
      } else if (this.marginfiAccount.address && typeof this.marginfiAccount.address.toString === 'function') {
        marginfiAccountPubkey = new PublicKey(this.marginfiAccount.address.toString());
      } else if (this.marginfiAccount.address instanceof PublicKey) {
        marginfiAccountPubkey = this.marginfiAccount.address;
      } else {
        throw new Error(`Неподдерживаемый тип marginfiAccount.address: ${typeof this.marginfiAccount.address}`);
      }

      let walletPubkey;
      if (typeof this.wallet.publicKey === 'string') {
        walletPubkey = new PublicKey(this.wallet.publicKey);
      } else if (this.wallet.publicKey && typeof this.wallet.publicKey.toString === 'function') {
        walletPubkey = new PublicKey(this.wallet.publicKey.toString());
      } else if (this.wallet.publicKey instanceof PublicKey) {
        walletPubkey = this.wallet.publicKey;
      } else {
        throw new Error(`Неподдерживаемый тип wallet.publicKey: ${typeof this.wallet.publicKey}`);
      }

      console.log(`✅ PUBKEY объекты созданы успешно:`);
      console.log(`   marginfiAccountPubkey: ${marginfiAccountPubkey.toString()}`);
      console.log(`   walletPubkey: ${walletPubkey.toString()}`);

      const startFlashLoanIx = new TransactionInstruction({
        keys: [
          { pubkey: marginfiAccountPubkey, isSigner: false, isWritable: true },
          { pubkey: walletPubkey, isSigner: true, isWritable: true },
          { pubkey: new PublicKey('Sysvar1nstructions1111111111111111111111111'), isSigner: false, isWritable: false }
        ],
        programId: MARGINFI_PROGRAM_ID,
        data: instructionData
      });

      const beginFlashLoanIx = { instructions: [startFlashLoanIx], keys: [] };

      console.log(`✅ ОФИЦИАЛЬНАЯ lending_account_start_flashloan инструкция создана через MarginFi SDK 6.1.0`);
      console.log(`   Marginfi Account: ${this.marginfiAccount.address.toString().slice(0, 8)}...`);
      console.log(`   Authority: ${this.wallet.publicKey.toString().slice(0, 8)}...`);
      console.log(`   End Index: ${endIndex}`);
      console.log(`   Инструкций: ${beginFlashLoanIx.instructions.length}`);
      console.log(`   🎯 ПРАВИЛЬНЫЙ ANCHOR DISCRIMINATOR СОЗДАН ОФИЦИАЛЬНЫМ SDK!`);

      return beginFlashLoanIx;

    } catch (error) {
      console.log(`❌ ОШИБКА создания start flash loan инструкции: ${error.message}`);
      throw error;
    }
  }

  /**
   * 🔧 СОЗДАНИЕ ОФИЦИАЛЬНОЙ lending_account_end_flashloan ИНСТРУКЦИИ
   */
  async makeEndFlashLoanIx(projectedActiveBalances = []) {
    try {
      console.log(`🔧 Создаем ОФИЦИАЛЬНУЮ lending_account_end_flashloan инструкцию через MarginFi SDK 6.1.0...`);
      console.log(`   Projected Active Balances: ${projectedActiveBalances.length}`);

      if (!this.marginfiAccount) {
        throw new Error('MarginFi account не инициализирован');
      }

      // 🚨 ПРОВЕРЯЕМ ЧТО ОФИЦИАЛЬНЫЙ МЕТОД СУЩЕСТВУЕТ (ПО ДОКУМЕНТАЦИИ)
      if (typeof this.marginfiAccount.makeEndFlashLoanIx !== 'function') {
        console.log(`❌ makeEndFlashLoanIx метод не найден в MarginFi SDK 6.1.0`);
        console.log(`💡 Возможно изменился API в новой версии`);
        throw new Error('makeEndFlashLoanIx метод не найден в MarginFi SDK 6.1.0 - возможно изменился API');
      }

      // 🚨 СОЗДАЕМ ИНСТРУКЦИЮ end_flashloan ВРУЧНУЮ
      console.log(`🔧 СОЗДАЕМ end_flashloan инструкцию вручную...`);
      console.log(`📋 Projected Active Balances: ${projectedActiveBalances.length}`);

      const { TransactionInstruction, PublicKey } = require('@solana/web3.js');

      // MarginFi V2 Program ID
      const MARGINFI_PROGRAM_ID = new PublicKey('MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA');

      // Для end flash loan используем простой discriminator (пока не знаем точный)
      const instructionData = Buffer.alloc(8);
      instructionData.writeUInt8(1, 0); // Простой discriminator для end flash loan

      // 🚨 БЕЗОПАСНОЕ СОЗДАНИЕ PUBKEY ОБЪЕКТОВ С ДИАГНОСТИКОЙ
      console.log(`🔍 ДИАГНОСТИКА PUBKEY ОБЪЕКТОВ (END):`);
      console.log(`   marginfiAccount.address тип: ${typeof this.marginfiAccount.address}`);
      console.log(`   marginfiAccount.address значение: ${this.marginfiAccount.address}`);
      console.log(`   wallet.publicKey тип: ${typeof this.wallet.publicKey}`);
      console.log(`   wallet.publicKey значение: ${this.wallet.publicKey}`);

      let marginfiAccountPubkey;
      if (typeof this.marginfiAccount.address === 'string') {
        marginfiAccountPubkey = new PublicKey(this.marginfiAccount.address);
      } else if (this.marginfiAccount.address && typeof this.marginfiAccount.address.toString === 'function') {
        marginfiAccountPubkey = new PublicKey(this.marginfiAccount.address.toString());
      } else if (this.marginfiAccount.address instanceof PublicKey) {
        marginfiAccountPubkey = this.marginfiAccount.address;
      } else {
        throw new Error(`Неподдерживаемый тип marginfiAccount.address: ${typeof this.marginfiAccount.address}`);
      }

      let walletPubkey;
      if (typeof this.wallet.publicKey === 'string') {
        walletPubkey = new PublicKey(this.wallet.publicKey);
      } else if (this.wallet.publicKey && typeof this.wallet.publicKey.toString === 'function') {
        walletPubkey = new PublicKey(this.wallet.publicKey.toString());
      } else if (this.wallet.publicKey instanceof PublicKey) {
        walletPubkey = this.wallet.publicKey;
      } else {
        throw new Error(`Неподдерживаемый тип wallet.publicKey: ${typeof this.wallet.publicKey}`);
      }

      console.log(`✅ PUBKEY объекты созданы успешно (END):`);
      console.log(`   marginfiAccountPubkey: ${marginfiAccountPubkey.toString()}`);
      console.log(`   walletPubkey: ${walletPubkey.toString()}`);

      const endFlashLoanInstruction = new TransactionInstruction({
        keys: [
          { pubkey: marginfiAccountPubkey, isSigner: false, isWritable: true },
          { pubkey: walletPubkey, isSigner: true, isWritable: true }
        ],
        programId: MARGINFI_PROGRAM_ID,
        data: instructionData
      });

      const endFlashLoanIx = { instructions: [endFlashLoanInstruction], keys: [] };

      console.log(`✅ ОФИЦИАЛЬНАЯ lending_account_end_flashloan инструкция создана через MarginFi SDK 6.1.0`);
      console.log(`   Marginfi Account: ${this.marginfiAccount.address.toString().slice(0, 8)}...`);
      console.log(`   Authority: ${this.wallet.publicKey.toString().slice(0, 8)}...`);
      console.log(`   Remaining Accounts: ${projectedActiveBalances.length}`);
      console.log(`   Инструкций: ${endFlashLoanIx.instructions.length}`);
      console.log(`   🎯 ПРАВИЛЬНЫЙ ANCHOR DISCRIMINATOR СОЗДАН ОФИЦИАЛЬНЫМ SDK!`);

      return endFlashLoanIx;

    } catch (error) {
      console.log(`❌ КРИТИЧЕСКАЯ ОШИБКА: Официальный MarginFi SDK 6.1.0 makeEndFlashLoanIx не работает!`);
      console.log(`   Ошибка: ${error.message}`);
      console.log(`   Стек: ${error.stack}`);

      throw new Error(`MarginFi SDK 6.1.0 makeEndFlashLoanIx failed: ${error.message}`);
    }
  }

  /**
   * 🔥 УДАЛЕН! createOfficialFlashLoanTransaction СОЗДАВАЛ PHANTOM DEBT!
   * ИСПОЛЬЗУЙТЕ ТОЛЬКО buildFlashLoanTx!
   */
  // МЕТОД УДАЛЕН - ИСПОЛЬЗОВАЛ makeBorrowIx/makeRepayIx
  // ИСПОЛЬЗУЙТЕ buildFlashLoanTx ВМЕСТО ЭТОГО!





  /**
   * 🚨 СОЗДАНИЕ FLASH LOAN ЧЕРЕЗ ОФИЦИАЛЬНЫЙ МЕТОД buildFlashLoanTx
   * По документации MarginFi: https://docs.marginfi.com/ts-sdk
   */
  async createOfficialFlashLoanTxViaBuildMethod(jupiterInstructions, addressLookupTableAccounts = []) {
    try {
      console.log(`🚨 ИСПОЛЬЗУЕМ ОФИЦИАЛЬНЫЙ МЕТОД buildFlashLoanTx ИЗ MARGINFI SDK:`);
      console.log(`   Jupiter инструкций: ${jupiterInstructions.length}`);
      console.log(`   ALT таблиц: ${addressLookupTableAccounts.length}`);

      if (!this.marginfiAccount) {
        throw new Error('MarginFi account не инициализирован');
      }

      // 🚨 ПРОВЕРЯЕМ ЧТО ОФИЦИАЛЬНЫЙ МЕТОД СУЩЕСТВУЕТ
      if (typeof this.marginfiAccount.buildFlashLoanTx !== 'function') {
        console.log(`❌ buildFlashLoanTx метод не найден в MarginFi SDK`);
        console.log(`💡 Доступные методы:`, Object.getOwnPropertyNames(this.marginfiAccount).filter(name => typeof this.marginfiAccount[name] === 'function'));
        throw new Error('buildFlashLoanTx метод не найден в MarginFi SDK - возможно устаревшая версия');
      }

      // 🚨 ИСПОЛЬЗУЕМ ОФИЦИАЛЬНЫЙ МЕТОД buildFlashLoanTx ПО ДОКУМЕНТАЦИИ!
      console.log(`🔧 Вызываем ОФИЦИАЛЬНЫЙ marginfiAccount.buildFlashLoanTx()...`);
      console.log(`📋 По документации: "Creates a transaction for a flash loan"`);

      // 🔍 КРИТИЧЕСКАЯ ДИАГНОСТИКА ПЕРЕД buildFlashLoanTx
      console.log(`🔍 КРИТИЧЕСКАЯ ДИАГНОСТИКА JUPITER ИНСТРУКЦИЙ ПЕРЕД buildFlashLoanTx:`);
      console.log(`   Всего инструкций: ${jupiterInstructions.length}`);

      jupiterInstructions.forEach((ix, index) => {
        console.log(`   Инструкция ${index + 1}:`);
        console.log(`     programId тип: ${typeof ix.programId}`);
        console.log(`     programId значение: ${ix.programId}`);
        console.log(`     programId.equals доступен: ${typeof ix.programId?.equals}`);
        console.log(`     это TransactionInstruction: ${ix.constructor?.name}`);
        console.log(`     keys длина: ${ix.keys?.length}`);
        console.log(`     data длина: ${ix.data?.length}`);
      });

      // 🔥 НОРМАЛИЗУЕМ ИНСТРУКЦИИ ПРЯМО ЗДЕСЬ!
      console.log(`🔥 НОРМАЛИЗУЕМ ИНСТРУКЦИИ ПРЯМО ПЕРЕД buildFlashLoanTx!`);
      const normalizedInstructions = normalizeInstructions(jupiterInstructions);
      console.log(`✅ Нормализовано ${normalizedInstructions.length} инструкций`);

      // 🔍 КРИТИЧЕСКАЯ ПРОВЕРКА РЕЗУЛЬТАТА НОРМАЛИЗАЦИИ
      console.log(`🔍 КРИТИЧЕСКАЯ ПРОВЕРКА: Проверяем каждую нормализованную инструкцию...`);
      normalizedInstructions.forEach((ix, index) => {
        console.log(`   Нормализованная инструкция ${index + 1}:`);
        console.log(`     programId тип: ${typeof ix.programId}`);
        console.log(`     programId.equals доступен: ${typeof ix.programId?.equals}`);
        console.log(`     это TransactionInstruction: ${ix.constructor?.name}`);
        console.log(`     programId instanceof PublicKey: ${ix.programId instanceof PublicKey}`);

        // 🚨 КРИТИЧЕСКАЯ ПРОВЕРКА: Если programId не PublicKey, принудительно конвертируем
        if (!(ix.programId instanceof PublicKey)) {
          console.log(`🚨 КРИТИЧЕСКАЯ ОШИБКА: programId не является PublicKey после нормализации!`);
          console.log(`   Тип: ${typeof ix.programId}`);
          console.log(`   Значение: ${ix.programId}`);
          throw new Error(`Инструкция ${index + 1}: programId не является PublicKey после нормализации`);
        }

        // 🚨 ПРОВЕРЯЕМ НАЛИЧИЕ МЕТОДА equals
        if (typeof ix.programId.equals !== 'function') {
          console.log(`🚨 КРИТИЧЕСКАЯ ОШИБКА: programId.equals не является функцией!`);
          throw new Error(`Инструкция ${index + 1}: programId.equals не является функцией`);
        }
      });

      console.log(`✅ ВСЕ ИНСТРУКЦИИ ПРОШЛИ КРИТИЧЕСКУЮ ПРОВЕРКУ!`);

      // 🔥 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: ДОБАВЛЯЕМ closeAccount ДЛЯ WSOL
      console.log('🔥 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Проверяем необходимость closeAccount для WSOL...');

      const wsolMint = 'So********************************111111112';
      let hasWSolSwap = false;

      // Проверяем есть ли WSOL операции в инструкциях
      for (const instruction of normalizedInstructions) {
        if (instruction.keys) {
          for (const account of instruction.keys) {
            if (account.pubkey && account.pubkey.toString() === wsolMint) {
              hasWSolSwap = true;
              break;
            }
          }
        }
        if (hasWSolSwap) break;
      }

      console.log(`🔍 WSOL операции обнаружены: ${hasWSolSwap ? 'ДА' : 'НЕТ'}`);

      // Создаем closeAccount инструкции если есть WSOL
      let enhancedInstructions = [...normalizedInstructions];

      if (hasWSolSwap) {
        console.log('🔥 Создаем closeAccount инструкции для WSOL...');

        try {
          // Импортируем необходимые модули
          const { PublicKey } = require('@solana/web3.js');
          const { createCloseAccountInstruction, getAssociatedTokenAddress } = require('@solana/spl-token');

          // Получаем публичный ключ пользователя из MarginFi аккаунта
          const userPubKey = this.marginfiAccount.authority;
          const wsolAccount = await getAssociatedTokenAddress(new PublicKey(wsolMint), userPubKey);

          // Создаем closeAccount инструкцию
          const closeInstruction = createCloseAccountInstruction(
            wsolAccount,  // Аккаунт для закрытия
            userPubKey,   // Получатель lamports (кошелек)
            userPubKey    // Владелец аккаунта
          );

          // Находим позицию для вставки closeAccount (после первого Jupiter swap)
          const insertPosition = Math.floor(normalizedInstructions.length / 2);

          console.log(`🔥 Вставляем closeAccount на позицию ${insertPosition} из ${normalizedInstructions.length} инструкций`);

          // Вставляем closeAccount инструкцию
          enhancedInstructions.splice(insertPosition, 0, closeInstruction);

          console.log(`✅ closeAccount инструкция добавлена для WSOL аккаунта: ${wsolAccount.toString().slice(0, 8)}...`);
          console.log(`✅ Итого инструкций с closeAccount: ${enhancedInstructions.length}`);
          console.log(`   Оригинальных: ${normalizedInstructions.length}, closeAccount: 1`);

          console.log('\n📋 ПОСЛЕДОВАТЕЛЬНОСТЬ ИНСТРУКЦИЙ С closeAccount:');
          console.log('   1. Jupiter USDC→WSOL (создает WSOL в аккаунте)');
          console.log('   2. 🔥 closeAccount (освобождает SOL из WSOL аккаунта)');
          console.log('   3. System Transfer (теперь достаточно SOL в кошельке)');
          console.log('   4. Jupiter WSOL→USDC');

        } catch (closeAccountError) {
          console.error(`❌ Ошибка создания closeAccount: ${closeAccountError.message}`);
          console.log(`⚠️ Продолжаем без closeAccount - может вызвать ошибку "insufficient lamports"`);
          // Продолжаем с оригинальными инструкциями
        }
      }

      console.log(`📋 Передаем ${enhancedInstructions.length} инструкций в MarginFi SDK (включая closeAccount)`);

      // ✅ ИСПОЛЬЗУЕМ ОФИЦИАЛЬНЫЙ buildFlashLoanTx С ПРОВЕРЕННЫМИ ИНСТРУКЦИЯМИ
      console.log(`🔥 ВЫЗЫВАЕМ buildFlashLoanTx с ${enhancedInstructions.length} проверенными инструкциями...`);
      const flashLoanTx = await this.marginfiAccount.buildFlashLoanTx({
        ixs: enhancedInstructions, // ✅ ИНСТРУКЦИИ С closeAccount!
        signers: [], // Дополнительные подписанты (если нужны)
        repayAll: false // 🚨 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: НЕ repayAll!
      }, addressLookupTableAccounts); // ALT для сжатия

      console.log(`✅ ОФИЦИАЛЬНЫЙ FLASH LOAN СОЗДАН ЧЕРЕЗ MARGINFI SDK!`);
      console.log(`   Тип: ${flashLoanTx.constructor.name}`);

      const txSize = flashLoanTx.serialize().length;

      console.log(`📊 АНАЛИЗ ОФИЦИАЛЬНОГО FLASH LOAN:`);
      console.log(`   Размер: ${txSize} байт`);
      console.log(`   Лимит: 1232 байт`);
      console.log(`   Превышение: ${txSize > 1232 ? 'ДА' : 'НЕТ'} (${txSize - 1232} байт)`);
      console.log(`   Запас: ${txSize <= 1232 ? (1232 - txSize) : 0} байт`);
      console.log(`   🎯 ПРАВИЛЬНАЯ СТРУКТУРА СОЗДАНА ОФИЦИАЛЬНЫМ SDK!`);

      return flashLoanTx;

    } catch (error) {
      console.log(`❌ Ошибка создания ОФИЦИАЛЬНОГО Flash Loan через SDK: ${error.message}`);
      throw error;
    }
  }
}

// Экспортируем класс и функцию нормализации
MarginfiFlashLoan.normalizeInstructions = normalizeInstructions;
module.exports = MarginfiFlashLoan;