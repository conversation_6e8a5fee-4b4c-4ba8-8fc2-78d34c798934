/*!
 * 🌐 RPC CLIENT FOR SOLANA
 * Высокопроизводительный клиент для взаимодействия с Solana RPC
 */

use super::{EpochInfo, EpochSchedule, LeaderSchedule, LeaderScheduleError, LeaderScheduleResult};
use reqwest::{Client, ClientBuilder};
use serde_json::{json, Value};
use std::collections::HashMap;
use std::time::{Duration, Instant};
use tokio::time::timeout;
use tracing::{debug, error, info, warn};

/// 🌐 RPC клиент для Solana
pub struct SolanaRpcClient {
    client: Client,
    rpc_url: String,
    timeout: Duration,
    max_retries: u32,
    retry_delay: Duration,
}

impl SolanaRpcClient {
    /// Создание нового RPC клиента
    pub fn new(
        rpc_url: String,
        timeout_ms: u64,
        max_retries: u32,
        retry_delay_ms: u64,
    ) -> LeaderScheduleResult<Self> {
        let client = ClientBuilder::new()
            .timeout(Duration::from_millis(timeout_ms))
            .tcp_keepalive(Duration::from_secs(30))
            .tcp_nodelay(true)
            .pool_max_idle_per_host(10)
            .pool_idle_timeout(Duration::from_secs(30))
            .build()
            .map_err(|e| LeaderScheduleError::NetworkError(e.to_string()))?;

        Ok(Self {
            client,
            rpc_url,
            timeout: Duration::from_millis(timeout_ms),
            max_retries,
            retry_delay: Duration::from_millis(retry_delay_ms),
        })
    }

    /// Выполнение RPC запроса с повторными попытками
    async fn rpc_call(&self, method: &str, params: Value) -> LeaderScheduleResult<Value> {
        let request_body = json!({
            "jsonrpc": "2.0",
            "id": 1,
            "method": method,
            "params": params
        });

        let mut last_error = None;

        for attempt in 0..=self.max_retries {
            let start = Instant::now();
            
            let result = timeout(
                self.timeout,
                self.client
                    .post(&self.rpc_url)
                    .header("Content-Type", "application/json")
                    .json(&request_body)
                    .send(),
            )
            .await;

            let duration = start.elapsed();

            match result {
                Ok(Ok(response)) => {
                    debug!(
                        "RPC call {} completed in {:?} (attempt {})",
                        method, duration, attempt + 1
                    );

                    if response.status().is_success() {
                        match response.json::<Value>().await {
                            Ok(json_response) => {
                                if let Some(error) = json_response.get("error") {
                                    let error_msg = format!("RPC error: {}", error);
                                    error!("{}", error_msg);
                                    last_error = Some(LeaderScheduleError::RpcError(error_msg));
                                } else if let Some(result) = json_response.get("result") {
                                    return Ok(result.clone());
                                } else {
                                    let error_msg = "Invalid RPC response format".to_string();
                                    error!("{}", error_msg);
                                    last_error = Some(LeaderScheduleError::RpcError(error_msg));
                                }
                            }
                            Err(e) => {
                                let error_msg = format!("Failed to parse JSON response: {}", e);
                                error!("{}", error_msg);
                                last_error = Some(LeaderScheduleError::SerializationError(error_msg));
                            }
                        }
                    } else {
                        let error_msg = format!("HTTP error: {}", response.status());
                        error!("{}", error_msg);
                        last_error = Some(LeaderScheduleError::NetworkError(error_msg));
                    }
                }
                Ok(Err(e)) => {
                    let error_msg = format!("Request failed: {}", e);
                    error!("{}", error_msg);
                    last_error = Some(LeaderScheduleError::NetworkError(error_msg));
                }
                Err(_) => {
                    let error_msg = format!("Request timeout after {:?}", self.timeout);
                    error!("{}", error_msg);
                    last_error = Some(LeaderScheduleError::TimeoutError);
                }
            }

            if attempt < self.max_retries {
                warn!(
                    "RPC call {} failed (attempt {}), retrying in {:?}",
                    method,
                    attempt + 1,
                    self.retry_delay
                );
                tokio::time::sleep(self.retry_delay).await;
            }
        }

        Err(last_error.unwrap_or_else(|| {
            LeaderScheduleError::RpcError("Unknown error".to_string())
        }))
    }

    /// Получение текущего слота
    pub async fn get_slot(&self) -> LeaderScheduleResult<u64> {
        debug!("Getting current slot");
        
        let result = self.rpc_call("getSlot", json!([])).await?;
        
        result
            .as_u64()
            .ok_or_else(|| LeaderScheduleError::SerializationError("Invalid slot format".to_string()))
    }

    /// Получение информации об эпохе
    pub async fn get_epoch_info(&self) -> LeaderScheduleResult<EpochInfo> {
        debug!("Getting epoch info");
        
        let result = self.rpc_call("getEpochInfo", json!([])).await?;
        
        let absolute_slot = result["absoluteSlot"]
            .as_u64()
            .ok_or_else(|| LeaderScheduleError::SerializationError("Invalid absoluteSlot".to_string()))?;
        
        let block_height = result["blockHeight"]
            .as_u64()
            .ok_or_else(|| LeaderScheduleError::SerializationError("Invalid blockHeight".to_string()))?;
        
        let epoch = result["epoch"]
            .as_u64()
            .ok_or_else(|| LeaderScheduleError::SerializationError("Invalid epoch".to_string()))?;
        
        let slot_index = result["slotIndex"]
            .as_u64()
            .ok_or_else(|| LeaderScheduleError::SerializationError("Invalid slotIndex".to_string()))?;
        
        let slots_in_epoch = result["slotsInEpoch"]
            .as_u64()
            .ok_or_else(|| LeaderScheduleError::SerializationError("Invalid slotsInEpoch".to_string()))?;
        
        let transaction_count = result["transactionCount"].as_u64();

        Ok(EpochInfo {
            absolute_slot,
            block_height,
            epoch,
            slot_index,
            slots_in_epoch,
            transaction_count,
        })
    }

    /// Получение расписания эпох
    pub async fn get_epoch_schedule(&self) -> LeaderScheduleResult<EpochSchedule> {
        debug!("Getting epoch schedule");
        
        let result = self.rpc_call("getEpochSchedule", json!([])).await?;
        
        let slots_per_epoch = result["slotsPerEpoch"]
            .as_u64()
            .ok_or_else(|| LeaderScheduleError::SerializationError("Invalid slotsPerEpoch".to_string()))?;
        
        let leader_schedule_slot_offset = result["leaderScheduleSlotOffset"]
            .as_u64()
            .ok_or_else(|| LeaderScheduleError::SerializationError("Invalid leaderScheduleSlotOffset".to_string()))?;
        
        let warmup = result["warmup"]
            .as_bool()
            .ok_or_else(|| LeaderScheduleError::SerializationError("Invalid warmup".to_string()))?;
        
        let first_normal_epoch = result["firstNormalEpoch"]
            .as_u64()
            .ok_or_else(|| LeaderScheduleError::SerializationError("Invalid firstNormalEpoch".to_string()))?;
        
        let first_normal_slot = result["firstNormalSlot"]
            .as_u64()
            .ok_or_else(|| LeaderScheduleError::SerializationError("Invalid firstNormalSlot".to_string()))?;

        Ok(EpochSchedule {
            slots_per_epoch,
            leader_schedule_slot_offset,
            warmup,
            first_normal_epoch,
            first_normal_slot,
        })
    }

    /// Получение расписания лидеров для эпохи
    pub async fn get_leader_schedule(&self, epoch_slot: Option<u64>) -> LeaderScheduleResult<LeaderSchedule> {
        debug!("Getting leader schedule for epoch slot: {:?}", epoch_slot);
        
        let params = if let Some(slot) = epoch_slot {
            json!([slot])
        } else {
            json!([])
        };
        
        let result = self.rpc_call("getLeaderSchedule", params).await?;
        
        if result.is_null() {
            return Err(LeaderScheduleError::ScheduleNotFound(
                epoch_slot.unwrap_or(0)
            ));
        }
        
        let schedule_obj = result
            .as_object()
            .ok_or_else(|| LeaderScheduleError::SerializationError("Invalid schedule format".to_string()))?;
        
        let mut schedule = HashMap::new();
        
        for (validator, slots) in schedule_obj {
            let slot_indices = slots
                .as_array()
                .ok_or_else(|| LeaderScheduleError::SerializationError("Invalid slot indices".to_string()))?
                .iter()
                .map(|v| v.as_u64().ok_or_else(|| LeaderScheduleError::SerializationError("Invalid slot index".to_string())))
                .collect::<Result<Vec<_>, _>>()?;
            
            schedule.insert(validator.clone(), slot_indices);
        }
        
        // Получаем информацию об эпохе для расчета границ
        let epoch_info = self.get_epoch_info().await?;
        let epoch_schedule = self.get_epoch_schedule().await?;
        
        let epoch = if epoch_slot.is_some() {
            super::utils::slot_to_epoch(epoch_slot.unwrap(), epoch_schedule.slots_per_epoch)
        } else {
            epoch_info.epoch
        };
        
        let first_slot = super::utils::epoch_to_first_slot(epoch, epoch_schedule.slots_per_epoch);
        let last_slot = super::utils::epoch_to_last_slot(epoch, epoch_schedule.slots_per_epoch);
        
        Ok(LeaderSchedule {
            epoch,
            schedule,
            created_at: chrono::Utc::now().timestamp() as u64,
            first_slot,
            last_slot,
        })
    }

    /// Получение лидера конкретного слота
    pub async fn get_slot_leader(&self, slot: Option<u64>) -> LeaderScheduleResult<String> {
        debug!("Getting slot leader for slot: {:?}", slot);
        
        let params = if let Some(s) = slot {
            json!([s])
        } else {
            json!([])
        };
        
        let result = self.rpc_call("getSlotLeader", params).await?;
        
        result
            .as_str()
            .map(|s| s.to_string())
            .ok_or_else(|| LeaderScheduleError::SerializationError("Invalid leader format".to_string()))
    }

    /// Получение лидеров для диапазона слотов
    pub async fn get_slot_leaders(&self, start_slot: u64, limit: u64) -> LeaderScheduleResult<Vec<String>> {
        debug!("Getting slot leaders from {} with limit {}", start_slot, limit);
        
        let result = self.rpc_call("getSlotLeaders", json!([start_slot, limit])).await?;
        
        let leaders_array = result
            .as_array()
            .ok_or_else(|| LeaderScheduleError::SerializationError("Invalid leaders format".to_string()))?;
        
        leaders_array
            .iter()
            .map(|v| {
                v.as_str()
                    .map(|s| s.to_string())
                    .ok_or_else(|| LeaderScheduleError::SerializationError("Invalid leader string".to_string()))
            })
            .collect()
    }

    /// Проверка здоровья RPC
    pub async fn health_check(&self) -> bool {
        match timeout(Duration::from_secs(5), self.get_slot()).await {
            Ok(Ok(_)) => {
                info!("RPC health check passed");
                true
            }
            Ok(Err(e)) => {
                warn!("RPC health check failed: {}", e);
                false
            }
            Err(_) => {
                warn!("RPC health check timed out");
                false
            }
        }
    }

    /// Получение статистики RPC
    pub fn get_stats(&self) -> HashMap<String, String> {
        HashMap::from([
            ("rpc_url".to_string(), self.rpc_url.clone()),
            ("timeout".to_string(), format!("{:?}", self.timeout)),
            ("max_retries".to_string(), self.max_retries.to_string()),
            ("retry_delay".to_string(), format!("{:?}", self.retry_delay)),
        ])
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_rpc_client_creation() {
        let client = SolanaRpcClient::new(
            "https://api.devnet.solana.com".to_string(),
            5000,
            3,
            100,
        );
        
        assert!(client.is_ok());
    }
    
    #[tokio::test]
    async fn test_invalid_url() {
        let client = SolanaRpcClient::new(
            "invalid-url".to_string(),
            1000,
            1,
            100,
        );
        
        assert!(client.is_ok()); // Клиент создается, но запросы будут падать
        
        if let Ok(client) = client {
            let result = client.get_slot().await;
            assert!(result.is_err());
        }
    }
    
    #[test]
    fn test_rpc_stats() {
        let client = SolanaRpcClient::new(
            "https://api.devnet.solana.com".to_string(),
            5000,
            3,
            100,
        ).unwrap();
        
        let stats = client.get_stats();
        assert!(stats.contains_key("rpc_url"));
        assert!(stats.contains_key("timeout"));
        assert!(stats.contains_key("max_retries"));
    }
}
