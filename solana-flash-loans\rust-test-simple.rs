// 🦀 ПРОСТОЙ ТЕСТ RUST КОМПИЛЯЦИИ
// Проверяем, что Rust работает корректно

fn main() {
    println!("🦀 Rust is working!");
    println!("📊 Testing basic functionality...");
    
    // Тест базовых типов
    let numbers: Vec<u64> = (1..=10).collect();
    let sum: u64 = numbers.iter().sum();
    println!("✅ Sum of 1-10: {}", sum);
    
    // Тест async
    let rt = tokio::runtime::Runtime::new().unwrap();
    rt.block_on(async {
        println!("✅ Async runtime working");
        
        // Тест HTTP клиента
        match reqwest::get("https://httpbin.org/get").await {
            Ok(response) => {
                println!("✅ HTTP request successful: {}", response.status());
            }
            Err(e) => {
                println!("⚠️  HTTP request failed: {}", e);
            }
        }
    });
    
    println!("🎉 All tests completed!");
}

// Добавляем зависимости для теста
// В Cargo.toml должно быть:
// [dependencies]
// tokio = { version = "1.0", features = ["full"] }
// reqwest = "0.11"
