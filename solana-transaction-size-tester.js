#!/usr/bin/env node

/**
 * 🚀 SOLANA TRANSACTION SIZE TESTER
 * 
 * Проверяет РЕАЛЬНЫЙ размер транзакций через запросы к сети Solana:
 * 1. Создает полную транзакцию с 4 ALT таблицами
 * 2. Сериализует в VersionedTransaction
 * 3. Отправляет simulate запрос к Solana RPC
 * 4. Измеряет точный размер в байтах
 * 5. Сравнивает с лимитами Solana (1232 байт)
 */

const { 
    Connection, 
    Keypair, 
    VersionedTransaction,
    TransactionMessage,
    AddressLookupTableAccount
} = require('@solana/web3.js');
const bs58 = require('bs58');

// 🔥 ИМПОРТИРУЕМ ПОЛНУЮ СИСТЕМУ
const CompleteFlashLoanWithLiquidity = require('./complete-flash-loan-with-liquidity.js');

require('dotenv').config();

class SolanaTransactionSizeTester {
    constructor() {
        // 🌐 CONNECTION К SOLANA MAINNET
        this.connection = new Connection(
            process.env.QUICKNODE_RPC_URL || process.env.SOLANA_RPC_URL,
            'confirmed'
        );
        
        // 🔑 WALLET
        this.wallet = null;
        
        // 🔥 ПОЛНАЯ СИСТЕМА
        this.flashLoanSystem = null;
        
        console.log('🚀 SOLANA TRANSACTION SIZE TESTER ИНИЦИАЛИЗИРОВАН');
    }

    /**
     * 🔑 ИНИЦИАЛИЗАЦИЯ
     */
    async initialize() {
        console.log('\n🔑 ИНИЦИАЛИЗАЦИЯ ТЕСТЕРА...');
        
        const privateKeyBytes = bs58.default.decode(process.env.WALLET_PRIVATE_KEY);
        this.wallet = Keypair.fromSecretKey(privateKeyBytes);
        
        console.log(`   Кошелек: ${this.wallet.publicKey.toString()}`);
        
        // 🔥 ИНИЦИАЛИЗАЦИЯ ПОЛНОЙ СИСТЕМЫ
        console.log('🔥 ИНИЦИАЛИЗАЦИЯ ПОЛНОЙ СИСТЕМЫ...');
        this.flashLoanSystem = new CompleteFlashLoanWithLiquidity();
        await this.flashLoanSystem.initialize();
        
        console.log('   ✅ Готов к тестированию размера транзакций');
    }

    /**
     * 📏 СОЗДАНИЕ И ИЗМЕРЕНИЕ РЕАЛЬНОГО РАЗМЕРА ТРАНЗАКЦИИ
     */
    async testRealTransactionSize() {
        console.log('\n🚀 ТЕСТИРОВАНИЕ РЕАЛЬНОГО РАЗМЕРА ТРАНЗАКЦИИ...');
        console.log('=' .repeat(80));
        
        try {
            // 🔥 СОЗДАЕМ ПОЛНУЮ ТРАНЗАКЦИЮ С 4 ALT ТАБЛИЦАМИ
            console.log('🔥 СОЗДАНИЕ ПОЛНОЙ ТРАНЗАКЦИИ...');
            const result = await this.flashLoanSystem.createCompleteFlashLoanTransaction();
            
            if (!result.success) {
                throw new Error('Не удалось создать транзакцию для тестирования');
            }
            
            const instructions = result.instructions;
            const altTables = result.altTables || [];
            
            console.log(`✅ Транзакция создана:`);
            console.log(`   📊 Инструкций: ${instructions.length}`);
            console.log(`   🗜️ ALT таблиц: ${altTables.length}`);
            
            // 📊 АНАЛИЗ ALT ТАБЛИЦ
            if (altTables.length > 0) {
                console.log('\n🗜️ ДЕТАЛИ ALT ТАБЛИЦ:');
                altTables.forEach((alt, index) => {
                    const addressCount = alt.state?.addresses?.length || 0;
                    const keyStr = alt.key ? alt.key.toString() : 'Unknown';
                    console.log(`   ${index + 1}. ${keyStr.slice(0, 8)}... (${addressCount} адресов)`);
                });
            }
            
            // 🔧 ПОЛУЧАЕМ СВЕЖИЙ BLOCKHASH
            console.log('\n🔧 ПОЛУЧЕНИЕ СВЕЖЕГО BLOCKHASH...');
            const { blockhash } = await this.connection.getLatestBlockhash('confirmed');
            console.log(`   Blockhash: ${blockhash.slice(0, 8)}...`);
            
            // 🏗️ СОЗДАЕМ VERSIONED TRANSACTION
            console.log('\n🏗️ СОЗДАНИЕ VERSIONED TRANSACTION...');
            
            const messageV0 = new TransactionMessage({
                payerKey: this.wallet.publicKey,
                recentBlockhash: blockhash,
                instructions: instructions
            }).compileToV0Message(altTables);
            
            const versionedTx = new VersionedTransaction(messageV0);
            versionedTx.sign([this.wallet]);
            
            console.log(`✅ VersionedTransaction создана и подписана`);
            
            // 📏 ИЗМЕРЯЕМ РАЗМЕР СЕРИАЛИЗОВАННОЙ ТРАНЗАКЦИИ
            console.log('\n📏 ИЗМЕРЕНИЕ РАЗМЕРА СЕРИАЛИЗОВАННОЙ ТРАНЗАКЦИИ...');
            
            const serializedTx = versionedTx.serialize();
            const realSize = serializedTx.length;
            
            console.log(`📊 РЕАЛЬНЫЙ РАЗМЕР ТРАНЗАКЦИИ: ${realSize} байт`);
            console.log(`🎯 Лимит Solana: 1232 байт`);
            console.log(`📈 Запас: ${1232 - realSize} байт`);
            console.log(`📊 Использовано: ${((realSize / 1232) * 100).toFixed(1)}%`);
            
            // 🚨 ПРОВЕРКА ЛИМИТОВ
            if (realSize > 1232) {
                console.log(`🚨 КРИТИЧЕСКАЯ ОШИБКА: Транзакция превышает лимит на ${realSize - 1232} байт!`);
                return { success: false, error: 'Transaction size exceeds limit', realSize, limit: 1232 };
            } else if (realSize > 1200) {
                console.log(`⚠️ ПРЕДУПРЕЖДЕНИЕ: Транзакция близка к лимиту (${1232 - realSize} байт запас)`);
            } else {
                console.log(`✅ ОТЛИЧНО: Транзакция в пределах лимита с запасом ${1232 - realSize} байт`);
            }
            
            // 🌐 ТЕСТИРОВАНИЕ ЧЕРЕЗ SOLANA RPC SIMULATE
            console.log('\n🌐 ТЕСТИРОВАНИЕ ЧЕРЕЗ SOLANA RPC SIMULATE...');
            
            try {
                const simulationResult = await this.connection.simulateTransaction(versionedTx, {
                    sigVerify: false,
                    replaceRecentBlockhash: true
                });
                
                console.log(`✅ RPC SIMULATE УСПЕШНО:`);
                console.log(`   🔧 Compute Units: ${simulationResult.value.unitsConsumed || 'N/A'}`);
                console.log(`   📊 Logs: ${simulationResult.value.logs?.length || 0} записей`);
                
                if (simulationResult.value.err) {
                    console.log(`   ⚠️ Ошибка симуляции: ${JSON.stringify(simulationResult.value.err)}`);
                } else {
                    console.log(`   ✅ Симуляция прошла успешно`);
                }
                
            } catch (simulateError) {
                console.log(`❌ Ошибка RPC simulate: ${simulateError.message}`);
                console.log(`💡 Это может быть из-за недостатка средств или других условий`);
            }
            
            // 📊 ДЕТАЛЬНЫЙ АНАЛИЗ РАЗМЕРА
            console.log('\n📊 ДЕТАЛЬНЫЙ АНАЛИЗ РАЗМЕРА:');
            console.log('=' .repeat(50));
            
            // Анализ компонентов транзакции
            const messageSize = messageV0.serialize().length;
            const signatureSize = 64; // Одна подпись
            const versionSize = 1; // Версия транзакции
            
            console.log(`📋 Компоненты размера:`);
            console.log(`   🔢 Версия: ${versionSize} байт`);
            console.log(`   ✍️ Подписи: ${signatureSize} байт`);
            console.log(`   📨 Сообщение: ${messageSize} байт`);
            console.log(`   📊 ИТОГО: ${realSize} байт`);
            
            // Анализ инструкций
            let instructionSizes = 0;
            instructions.forEach((ix, index) => {
                const ixSize = 1 + 1 + (ix.keys?.length || 0) + 4 + (ix.data?.length || 0);
                instructionSizes += ixSize;
            });
            
            console.log(`\n📋 Анализ инструкций:`);
            console.log(`   📊 Количество: ${instructions.length}`);
            console.log(`   📏 Размер инструкций: ~${instructionSizes} байт`);
            console.log(`   🗜️ ALT сжатие: ${altTables.length} таблиц`);
            
            return {
                success: true,
                realSize: realSize,
                limit: 1232,
                margin: 1232 - realSize,
                percentage: (realSize / 1232) * 100,
                instructions: instructions.length,
                altTables: altTables.length,
                components: {
                    version: versionSize,
                    signatures: signatureSize,
                    message: messageSize
                }
            };
            
        } catch (error) {
            console.error(`❌ ОШИБКА ТЕСТИРОВАНИЯ: ${error.message}`);
            console.error(`📚 Stack trace:`, error.stack);
            return { success: false, error: error.message };
        }
    }

    /**
     * 📊 СРАВНЕНИЕ ОЦЕНОЧНОГО И РЕАЛЬНОГО РАЗМЕРА
     */
    async compareEstimatedVsReal() {
        console.log('\n📊 СРАВНЕНИЕ ОЦЕНОЧНОГО И РЕАЛЬНОГО РАЗМЕРА...');
        console.log('=' .repeat(80));
        
        try {
            // Получаем результат реального тестирования
            const realResult = await this.testRealTransactionSize();
            
            if (!realResult.success) {
                throw new Error('Не удалось получить реальный размер');
            }
            
            // Получаем оценочный размер из системы
            const result = await this.flashLoanSystem.createCompleteFlashLoanTransaction();
            const estimatedSize = result.optimization?.finalSize || 'N/A';
            
            console.log('\n📊 СРАВНЕНИЕ РЕЗУЛЬТАТОВ:');
            console.log('=' .repeat(50));
            console.log(`🔍 Оценочный размер: ${estimatedSize} байт`);
            console.log(`🌐 Реальный размер: ${realResult.realSize} байт`);
            
            if (typeof estimatedSize === 'number') {
                const difference = Math.abs(realResult.realSize - estimatedSize);
                const accuracy = 100 - ((difference / realResult.realSize) * 100);
                
                console.log(`📏 Разница: ${difference} байт`);
                console.log(`🎯 Точность оценки: ${accuracy.toFixed(1)}%`);
                
                if (difference <= 50) {
                    console.log(`✅ ОТЛИЧНАЯ ТОЧНОСТЬ: Разница менее 50 байт`);
                } else if (difference <= 100) {
                    console.log(`⚠️ ХОРОШАЯ ТОЧНОСТЬ: Разница менее 100 байт`);
                } else {
                    console.log(`🚨 НИЗКАЯ ТОЧНОСТЬ: Разница более 100 байт`);
                }
            }
            
            return {
                estimated: estimatedSize,
                real: realResult.realSize,
                difference: typeof estimatedSize === 'number' ? Math.abs(realResult.realSize - estimatedSize) : null,
                accuracy: typeof estimatedSize === 'number' ? 100 - ((Math.abs(realResult.realSize - estimatedSize) / realResult.realSize) * 100) : null
            };
            
        } catch (error) {
            console.error(`❌ ОШИБКА СРАВНЕНИЯ: ${error.message}`);
            return { success: false, error: error.message };
        }
    }
}

// 🚀 ЗАПУСК ТЕСТИРОВАНИЯ
if (require.main === module) {
    async function main() {
        const tester = new SolanaTransactionSizeTester();
        
        try {
            await tester.initialize();
            
            // Тестируем реальный размер
            const realSizeResult = await tester.testRealTransactionSize();
            
            if (realSizeResult.success) {
                console.log('\n🎉 ТЕСТИРОВАНИЕ РЕАЛЬНОГО РАЗМЕРА ЗАВЕРШЕНО УСПЕШНО!');
                
                // Сравниваем с оценочным размером
                await tester.compareEstimatedVsReal();
                
                console.log('\n🎯 ИТОГОВЫЕ РЕЗУЛЬТАТЫ:');
                console.log(`   📊 Реальный размер: ${realSizeResult.realSize} байт`);
                console.log(`   🎯 Лимит: ${realSizeResult.limit} байт`);
                console.log(`   📈 Запас: ${realSizeResult.margin} байт`);
                console.log(`   📊 Использовано: ${realSizeResult.percentage.toFixed(1)}%`);
                console.log(`   📋 Инструкций: ${realSizeResult.instructions}`);
                console.log(`   🗜️ ALT таблиц: ${realSizeResult.altTables}`);
                
                process.exit(0);
            } else {
                console.log('\n❌ ТЕСТИРОВАНИЕ ЗАВЕРШЕНО С ОШИБКАМИ');
                process.exit(1);
            }
            
        } catch (error) {
            console.error('💥 КРИТИЧЕСКАЯ ОШИБКА:', error.message);
            process.exit(1);
        }
    }
    
    main().catch(console.error);
}

module.exports = SolanaTransactionSizeTester;
