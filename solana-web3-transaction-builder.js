#!/usr/bin/env node

/**
 * 🚀 SOLANA WEB3.JS TRANSACTION BUILDER
 * 
 * Создает реальные транзакции Solana с использованием @solana/web3.js
 * Интегрирован с Meteora DLMM PDA калькулятором
 */

const { 
    Connection, 
    PublicKey, 
    Transaction, 
    TransactionInstruction,
    Keypair,
    SystemProgram,
    SYSVAR_RENT_PUBKEY
} = require('@solana/web3.js');

const { 
    getAssociatedTokenAddressSync,
    TOKEN_PROGRAM_ID,
    ASSOCIATED_TOKEN_PROGRAM_ID
} = require('@solana/spl-token');

const MeteoraLBPDACalculator = require('./pda-calculator.js');

class SolanaWeb3TransactionBuilder {
    constructor() {
        // 🌐 CONNECTION
        this.connection = new Connection('https://api.mainnet-beta.solana.com', 'confirmed');
        
        // 🔑 PROGRAM IDS
        this.PROGRAMS = {
            MARGINFI: new PublicKey('MFv2hWf31Z9kbCa1snEPYctwafyhdvnV7FZnsebVacA'),
            METEORA_DLMM: new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo'),
            JUPITER_V6: new PublicKey('JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4'),
            TOKEN_PROGRAM: TOKEN_PROGRAM_ID,
            ASSOCIATED_TOKEN: ASSOCIATED_TOKEN_PROGRAM_ID,
            SYSTEM_PROGRAM: SystemProgram.programId
        };

        // 🪙 TOKEN MINTS
        this.TOKENS = {
            USDC: new PublicKey('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'),
            SOL: new PublicKey('So11111111111111111111111111111111111111112'),
            WSOL: new PublicKey('So11111111111111111111111111111111111111112')
        };

        // 🧮 PDA CALCULATOR
        this.pdaCalculator = new MeteoraLBPDACalculator();
        
        console.log('🚀 SOLANA WEB3 TRANSACTION BUILDER ИНИЦИАЛИЗИРОВАН');
    }

    /**
     * 🔑 ГЕНЕРАЦИЯ ASSOCIATED TOKEN ACCOUNTS
     */
    generateATAs(userWallet) {
        const userPubkey = new PublicKey(userWallet);
        
        return {
            userUSDC: getAssociatedTokenAddressSync(this.TOKENS.USDC, userPubkey),
            userSOL: getAssociatedTokenAddressSync(this.TOKENS.WSOL, userPubkey)
        };
    }

    /**
     * 🏗️ СОЗДАНИЕ ИНСТРУКЦИИ INITIALIZE POSITION
     */
    createInitializePositionInstruction(accounts, data) {
        const keys = [
            { pubkey: new PublicKey(accounts.user), isSigner: true, isWritable: true },
            { pubkey: new PublicKey(accounts.position), isSigner: true, isWritable: true },
            { pubkey: new PublicKey(accounts.lbPair), isSigner: false, isWritable: false },
            { pubkey: this.PROGRAMS.SYSTEM_PROGRAM, isSigner: false, isWritable: false },
            { pubkey: SYSVAR_RENT_PUBKEY, isSigner: false, isWritable: false }
        ];

        // Сериализация данных (упрощенная)
        const instructionData = Buffer.alloc(16);
        instructionData.writeInt32LE(data.lowerBinId, 0);
        instructionData.writeInt32LE(data.width, 4);

        return new TransactionInstruction({
            keys,
            programId: this.PROGRAMS.METEORA_DLMM,
            data: instructionData
        });
    }

    /**
     * 🏊 СОЗДАНИЕ ИНСТРУКЦИИ ADD LIQUIDITY BY STRATEGY
     */
    createAddLiquidityInstruction(accounts, data) {
        const keys = [
            { pubkey: new PublicKey(accounts.user), isSigner: true, isWritable: true },
            { pubkey: new PublicKey(accounts.position), isSigner: false, isWritable: true },
            { pubkey: new PublicKey(accounts.lbPair), isSigner: false, isWritable: true },
            { pubkey: new PublicKey(accounts.binArrayLower), isSigner: false, isWritable: true },
            { pubkey: new PublicKey(accounts.binArrayUpper), isSigner: false, isWritable: true },
            { pubkey: accounts.userUSDC, isSigner: false, isWritable: true },
            { pubkey: accounts.userSOL, isSigner: false, isWritable: true },
            { pubkey: new PublicKey(accounts.reserveX), isSigner: false, isWritable: true },
            { pubkey: new PublicKey(accounts.reserveY), isSigner: false, isWritable: true },
            { pubkey: this.PROGRAMS.TOKEN_PROGRAM, isSigner: false, isWritable: false },
            { pubkey: new PublicKey(accounts.eventAuthority), isSigner: false, isWritable: false },
            { pubkey: this.PROGRAMS.METEORA_DLMM, isSigner: false, isWritable: false }
        ];

        // Сериализация данных (упрощенная)
        const instructionData = Buffer.alloc(1024); // Достаточно места для данных
        // В реальности нужно правильно сериализовать liquidityParameter
        
        return new TransactionInstruction({
            keys,
            programId: this.PROGRAMS.METEORA_DLMM,
            data: instructionData
        });
    }

    /**
     * 🏊 СОЗДАНИЕ ИНСТРУКЦИИ REMOVE LIQUIDITY
     */
    createRemoveLiquidityInstruction(accounts, data) {
        const keys = [
            { pubkey: new PublicKey(accounts.user), isSigner: true, isWritable: true },
            { pubkey: new PublicKey(accounts.position), isSigner: false, isWritable: true },
            { pubkey: new PublicKey(accounts.lbPair), isSigner: false, isWritable: true },
            { pubkey: new PublicKey(accounts.binArrayLower), isSigner: false, isWritable: true },
            { pubkey: new PublicKey(accounts.binArrayUpper), isSigner: false, isWritable: true },
            { pubkey: accounts.userUSDC, isSigner: false, isWritable: true },
            { pubkey: accounts.userSOL, isSigner: false, isWritable: true },
            { pubkey: new PublicKey(accounts.reserveX), isSigner: false, isWritable: true },
            { pubkey: new PublicKey(accounts.reserveY), isSigner: false, isWritable: true },
            { pubkey: this.PROGRAMS.TOKEN_PROGRAM, isSigner: false, isWritable: false },
            { pubkey: new PublicKey(accounts.eventAuthority), isSigner: false, isWritable: false },
            { pubkey: this.PROGRAMS.METEORA_DLMM, isSigner: false, isWritable: false }
        ];

        const instructionData = Buffer.alloc(512);
        // Сериализация binLiquidityRemoval данных
        
        return new TransactionInstruction({
            keys,
            programId: this.PROGRAMS.METEORA_DLMM,
            data: instructionData
        });
    }

    /**
     * 🔒 СОЗДАНИЕ ИНСТРУКЦИИ CLOSE POSITION
     */
    createClosePositionInstruction(accounts) {
        const keys = [
            { pubkey: new PublicKey(accounts.user), isSigner: true, isWritable: true },
            { pubkey: new PublicKey(accounts.position), isSigner: false, isWritable: true },
            { pubkey: new PublicKey(accounts.lbPair), isSigner: false, isWritable: false },
            { pubkey: this.PROGRAMS.SYSTEM_PROGRAM, isSigner: false, isWritable: false }
        ];

        return new TransactionInstruction({
            keys,
            programId: this.PROGRAMS.METEORA_DLMM,
            data: Buffer.alloc(0) // Пустые данные
        });
    }

    /**
     * 🚀 СОЗДАНИЕ ПОЛНОЙ ТРАНЗАКЦИИ
     */
    async buildCompleteTransaction(params, userWallet) {
        console.log('\n🚀 СОЗДАНИЕ ПОЛНОЙ WEB3.JS ТРАНЗАКЦИИ...');
        
        try {
            // 1. Генерируем keypair для позиции
            const positionKeypair = Keypair.generate();
            console.log(`   🔑 Position Keypair: ${positionKeypair.publicKey.toString()}`);

            // 2. Рассчитываем PDA адреса
            const bins = params.addLiquidity.bins.map(b => b.binId);
            const poolConfig = {
                lbPairAddress: params.addLiquidity.poolAddress,
                tokenX: this.TOKENS.USDC.toString(),
                tokenY: this.TOKENS.SOL.toString(),
                bins: bins
            };

            const pdaResults = this.pdaCalculator.calculateAllPDAsForStrategy(poolConfig);
            const pdaAccounts = this.pdaCalculator.generateInstructionAccounts(
                pdaResults, 
                userWallet, 
                positionKeypair.publicKey.toString()
            );

            // 3. Генерируем ATA адреса
            const ataAccounts = this.generateATAs(userWallet);

            // 4. Объединяем все аккаунты
            const allAccounts = {
                ...pdaAccounts,
                userUSDC: ataAccounts.userUSDC,
                userSOL: ataAccounts.userSOL
            };

            // 5. Создаем инструкции
            const transaction = new Transaction();

            // Initialize Position
            const initPositionIx = this.createInitializePositionInstruction(
                allAccounts,
                {
                    lowerBinId: Math.min(...bins),
                    width: bins.length
                }
            );
            transaction.add(initPositionIx);

            // Add Liquidity
            const addLiquidityIx = this.createAddLiquidityInstruction(
                allAccounts,
                params.addLiquidity
            );
            transaction.add(addLiquidityIx);

            // Remove Liquidity (для завершения стратегии)
            const removeLiquidityIx = this.createRemoveLiquidityInstruction(
                allAccounts,
                params.removeLiquidity
            );
            transaction.add(removeLiquidityIx);

            // Close Position
            const closePositionIx = this.createClosePositionInstruction(allAccounts);
            transaction.add(closePositionIx);

            // 6. Получаем recent blockhash
            const { blockhash } = await this.connection.getLatestBlockhash();
            transaction.recentBlockhash = blockhash;
            transaction.feePayer = new PublicKey(userWallet);

            console.log(`   ✅ Транзакция создана с ${transaction.instructions.length} инструкциями`);
            console.log(`   🔑 Position: ${positionKeypair.publicKey.toString()}`);
            console.log(`   📊 Bin Array: ${allAccounts.binArrayLower}`);
            console.log(`   🏦 Reserve X: ${allAccounts.reserveX}`);
            console.log(`   🏦 Reserve Y: ${allAccounts.reserveY}`);

            return {
                transaction,
                positionKeypair,
                accounts: allAccounts,
                success: true
            };

        } catch (error) {
            console.error('❌ ОШИБКА СОЗДАНИЯ ТРАНЗАКЦИИ:', error.message);
            return {
                error: error.message,
                success: false
            };
        }
    }

    /**
     * 📊 СИМУЛЯЦИЯ ТРАНЗАКЦИИ
     */
    async simulateTransaction(transaction, signers) {
        console.log('\n📊 СИМУЛЯЦИЯ ТРАНЗАКЦИИ...');
        
        try {
            const simulation = await this.connection.simulateTransaction(transaction, signers);
            
            if (simulation.value.err) {
                console.error('❌ Симуляция провалена:', simulation.value.err);
                return { success: false, error: simulation.value.err };
            }

            console.log(`   ✅ Симуляция успешна`);
            console.log(`   ⚡ Compute Units: ${simulation.value.unitsConsumed}`);
            console.log(`   📋 Logs: ${simulation.value.logs?.length || 0} записей`);

            return {
                success: true,
                unitsConsumed: simulation.value.unitsConsumed,
                logs: simulation.value.logs
            };

        } catch (error) {
            console.error('❌ ОШИБКА СИМУЛЯЦИИ:', error.message);
            return { success: false, error: error.message };
        }
    }
}

// 🧪 ТЕСТОВЫЙ ЗАПУСК
if (require.main === module) {
    async function testTransactionBuilder() {
        const builder = new SolanaWeb3TransactionBuilder();
        
        // Тестовые параметры
        const params = {
            addLiquidity: {
                poolAddress: 'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y',
                totalAmount: 1400000,
                bins: [
                    { binId: -1, amountX: 175000 },
                    { binId: -2, amountX: 175000 },
                    { binId: -3, amountX: 175000 },
                    { binId: -4, amountX: 175000 },
                    { binId: -5, amountX: 175000 },
                    { binId: -6, amountX: 175000 },
                    { binId: -7, amountX: 175000 },
                    { binId: -8, amountX: 175000 }
                ]
            },
            removeLiquidity: {
                poolAddress: 'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y',
                bins: [-1, -2, -3, -4, -5, -6, -7, -8],
                expectedAmount: 1400000
            }
        };

        const userWallet = 'USER_WALLET_PLACEHOLDER';
        
        const result = await builder.buildCompleteTransaction(params, userWallet);
        
        if (result.success) {
            console.log('\n🎉 WEB3.JS ТРАНЗАКЦИЯ СОЗДАНА УСПЕШНО!');
            console.log(`   📋 Инструкций: ${result.transaction.instructions.length}`);
            console.log(`   🔑 Position: ${result.positionKeypair.publicKey.toString()}`);
        }
    }
    
    testTransactionBuilder().catch(console.error);
}

module.exports = SolanaWeb3TransactionBuilder;
