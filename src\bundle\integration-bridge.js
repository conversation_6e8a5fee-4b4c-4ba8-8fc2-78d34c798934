/**
 * 🌉 INTEGRATION BRIDGE
 * Мост между основной торговой системой и Bundle системой
 * Устраняет дублирование функций и обеспечивает бесшовную интеграцию
 */

const MainSystemIntegration = require('./main-system-integration');

class IntegrationBridge {
  constructor(mainSystem) {
    this.mainSystem = mainSystem;
    this.bundleIntegration = null;
    this.isIntegrated = false;

    // Маппинг функций для устранения дублирования
    this.functionMapping = {
      // Jupiter функции - используем Bundle систему
      'createSwapTransaction': 'bundle',
      'executeSwapTransaction': 'bundle',
      'getJupiterQuote': 'bundle',

      // Flash Loan функции - используем Bundle систему
      'createFlashLoan': 'bundle',
      'prepareFlashLoan': 'bundle',

      // Симуляция - используем Bundle систему
      'simulateTransaction': 'bundle',
      'fastSimulation': 'bundle',

      // Цены и анализ - используем основную систему
      'getAllDexPricesForPair': 'main',
      'findArbitrageOpportunities': 'main',
      'fetchOrcaPrices': 'main',
      'fetchRaydiumPrices': 'main',
      'updateMeteoraLivePrices': 'main',

      // WebSocket - используем основную систему
      'initializeWebSocket': 'main',
      'subscribeToRealPools': 'main',
      'handleRealPoolUpdate': 'main'
    };

    console.log('🌉 Integration Bridge инициализирован');
  }

  // Интеграция систем
  async integrate() {
    try {
      console.log('🌉 Начало интеграции систем...');

      // 1. ПРИНУДИТЕЛЬНО СОЗДАЕМ Bundle интеграцию
      console.log('🔧 Создаем MainSystemIntegration...');
      console.log(`🔧 mainSystem: ${!!this.mainSystem}`);
      console.log(`🔧 mainSystem.wallet: ${!!this.mainSystem?.wallet}`);
      console.log(`🔧 mainSystem.marginfiFlashLoan: ${!!this.mainSystem?.marginfiFlashLoan}`);

      this.bundleIntegration = new MainSystemIntegration(this.mainSystem);
      console.log('✅ Bundle интеграция СОЗДАНА!');
      console.log(`✅ bundleIntegration: ${!!this.bundleIntegration}`);

      try {
        await this.bundleIntegration.integrate();
        console.log('✅ Bundle интеграция УСПЕШНА!');
      } catch (integrationError) {
        console.log(`⚠️ Bundle интеграция с ошибкой: ${integrationError.message}`);
        console.log('🔧 Продолжаем с частичной интеграцией...');
      }

      // 2. Перенаправляем функции согласно маппингу
      try {
        this.redirectFunctions();
      } catch (redirectError) {
        console.log(`⚠️ Ошибка перенаправления: ${redirectError.message}`);
      }

      // 3. Устанавливаем обработчики событий
      try {
        this.setupEventHandlers();
      } catch (handlerError) {
        console.log(`⚠️ Ошибка обработчиков: ${handlerError.message}`);
      }

      this.isIntegrated = true;
      console.log('✅ Системы интегрированы (принудительно)!');

      return true;

    } catch (error) {
      console.error(`❌ КРИТИЧЕСКАЯ ошибка интеграции: ${error.message}`);
      console.error(`❌ Stack trace: ${error.stack}`);

      // 🔥 ПРИНУДИТЕЛЬНО СОЗДАЕМ ДАЖЕ ПРИ КРИТИЧЕСКИХ ОШИБКАХ!
      try {
        console.log('🔥 ПРИНУДИТЕЛЬНОЕ создание MainSystemIntegration...');
        this.bundleIntegration = new MainSystemIntegration(this.mainSystem);
        this.isIntegrated = true;
        console.log('🔥 Bundle интеграция ПРИНУДИТЕЛЬНО СОЗДАНА!');
        console.log(`🔥 bundleIntegration после принудительного создания: ${!!this.bundleIntegration}`);
        return true;
      } catch (forceError) {
        console.error(`❌ Даже принудительное создание провалено: ${forceError.message}`);
        console.error(`❌ Force error stack: ${forceError.stack}`);
        return false;
      }
    }
  }

  // Перенаправление функций для устранения дублирования
  redirectFunctions() {
    console.log('🔄 Перенаправление функций для устранения дублирования...');

    for (const [functionName, targetSystem] of Object.entries(this.functionMapping)) {
      if (targetSystem === 'bundle') {
        this.redirectToBundle(functionName);
      } else if (targetSystem === 'main') {
        this.redirectToMain(functionName);
      }
    }

    console.log('✅ Функции перенаправлены');
  }

  // Перенаправление к Bundle системе
  redirectToBundle(functionName) {
    if (this.mainSystem[functionName]) {
      const originalFunction = this.mainSystem[functionName].bind(this.mainSystem);

      this.mainSystem[functionName] = (...args) => {
        console.log(`🚀 [REDIRECT] ${functionName} → Bundle система`);

        // Перенаправляем к Bundle системе
        if (this.bundleIntegration && this.bundleIntegration.bundleOrchestrator) {
          return this.handleBundleRedirect(functionName, args);
        } else {
          console.log(`⚠️ Bundle система недоступна, используем оригинальную функцию`);
          return originalFunction(...args);
        }
      };
    }
  }

  // Перенаправление к основной системе
  redirectToMain(functionName) {
    // Основная система остается как есть
    console.log(`✅ ${functionName} остается в основной системе`);
  }

  // Обработка перенаправления к Bundle
  async handleBundleRedirect(functionName, args) {
    try {
      switch (functionName) {
        case 'createSwapTransaction':
          // Создаем возможность из аргументов
          const swapOpportunity = this.createOpportunityFromArgs(args);
          return await this.bundleIntegration.bundleOrchestrator.bundleManager.jupiterBundle.getJupiterTransaction(swapOpportunity);

        case 'executeSwapTransaction':
          // Создаем возможность для Bundle системы
          const opportunity = this.createOpportunityFromArgs(args);
          return await this.bundleIntegration.bundleOrchestrator.addOpportunity(opportunity);

        case 'createFlashLoan':
          const bundleOpportunity = this.createOpportunityFromArgs(args);
          return await this.bundleIntegration.bundleOrchestrator.bundleManager.jupiterBundle.prepareFlashLoan(bundleOpportunity);

        case 'simulateTransaction':
          const bundle = { transaction: args[0] };
          return await this.bundleIntegration.bundleOrchestrator.bundleManager.fastSimulation(bundle);

        default:
          console.log(`⚠️ Неизвестная функция для перенаправления: ${functionName}`);
          return null;
      }

    } catch (error) {
      console.error(`❌ Ошибка перенаправления ${functionName}: ${error.message}`);
      throw error;
    }
  }

  // Создание возможности из аргументов
  createOpportunityFromArgs(args) {
    // Базовая структура возможности
    const opportunity = {
      tokenSymbol: args[0] || 'SOL/USDC',
      amount: args[1] || 1000,
      spread: 0.01, // 1%
      buyDex: 'Jupiter',
      sellDex: 'Orca',
      timestamp: Date.now(),
      source: 'integration_bridge'
    };

    // Дополняем mint адресами
    opportunity.inputMint = this.getMintAddress(opportunity.tokenSymbol, 'input');
    opportunity.outputMint = this.getMintAddress(opportunity.tokenSymbol, 'output');

    return opportunity;
  }

  // Получение mint адреса
  getMintAddress(tokenSymbol, type) {
    const mints = {
      'SOL': 'So11111111111111111111111111111111111111112',
      'USDC': 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
      'USDT': 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB',
      'WBTC': '********************************************',
      'JitoSOL': 'J1toso1uCk3RLmjorhTtrVwY9HJ7X8V9yYac6Y7kGCPn',
      'JUP': 'JUPyiwrYJFskUPiHa7hkeR8VUtAeFoSYbKedZNsDvCN'
    };

    if (tokenSymbol.includes('/')) {
      const [base, quote] = tokenSymbol.split('/');
      return type === 'input' ? mints[base] : mints[quote];
    }

    return mints[tokenSymbol] || mints['USDC'];
  }

  // Настройка обработчиков событий
  setupEventHandlers() {
    console.log('🔧 Настройка обработчиков событий...');

    // Перехватываем арбитражные возможности
    if (this.mainSystem.findArbitrageOpportunities) {
      const originalFindArbitrage = this.mainSystem.findArbitrageOpportunities.bind(this.mainSystem);

      this.mainSystem.findArbitrageOpportunities = async (...args) => {
        const opportunities = await originalFindArbitrage(...args);

        // 🔥 ВКЛЮЧЕНО: Bundle система использует Jupiter API!
        console.log(`🔥 Bundle система ВКЛЮЧЕНА - используем Jupiter API!`);

        // Обрабатываем каждую возможность через Bundle систему
        for (const opportunity of opportunities) {
          if (this.shouldUseBundleProcessing(opportunity)) {
            console.log(`🚀 Отправляем в Bundle систему: ${opportunity.token}`);

            try {
              await this.bundleIntegration.handleArbitrageOpportunity(opportunity);
            } catch (bundleError) {
              console.error(`❌ Ошибка Bundle обработки: ${bundleError.message}`);
            }
          }
        }

        return opportunities;
      };
    }

    console.log('✅ Обработчики событий настроены');
  }

  // Проверка подходит ли возможность для Bundle
  shouldUseBundleProcessing(opportunity) {
    console.log(`🔍 Bundle проверка: спред=${opportunity.spread}, сумма=${opportunity.tradeAmount}`);

    // 🔧 ИСПОЛЬЗУЕМ ЦЕНТРАЛИЗОВАННЫЙ КОНФИГ!
    const { TRADING_CONFIG } = require('../../trading-config.js');
    return (
      opportunity.spread > TRADING_CONFIG.MIN_SPREAD_PERCENT && // ЦЕНТРАЛИЗОВАННЫЙ КОНФИГ!
      opportunity.tradeAmount > 100 && // Минимум $100 (было $50)
      (opportunity.buyDex === 'Jupiter' || opportunity.sellDex === 'Jupiter') // Включает Jupiter
    );
  }

  // Получение статистики интеграции
  getIntegrationStats() {
    if (!this.bundleIntegration) {
      return { integrated: false };
    }

    return {
      integrated: this.isIntegrated,
      bundleStats: this.bundleIntegration.getIntegrationStats(),
      functionMappings: Object.keys(this.functionMapping).length,
      redirectedFunctions: Object.entries(this.functionMapping).filter(([, target]) => target === 'bundle').length
    };
  }

  // Отображение статистики
  displayIntegrationStatus() {
    const stats = this.getIntegrationStats();

    console.log('\n🌉 СТАТУС ИНТЕГРАЦИИ СИСТЕМ');
    console.log('═'.repeat(40));
    console.log(`🔗 Интегрировано: ${stats.integrated ? 'ДА' : 'НЕТ'}`);

    if (stats.integrated) {
      console.log(`📊 Функций перенаправлено: ${stats.redirectedFunctions}/${stats.functionMappings}`);
      console.log(`🚀 Bundle возможностей: ${stats.bundleStats?.opportunitiesFromMain || 0}`);
      console.log(`✅ Bundle успешно: ${stats.bundleStats?.bundleSuccessRate || 0}%`);
    }

    console.log('═'.repeat(40));
  }

  // Остановка интеграции
  async stop() {
    console.log('🛑 Остановка интеграции...');

    if (this.bundleIntegration) {
      await this.bundleIntegration.stop();
    }

    this.isIntegrated = false;
    console.log('✅ Интеграция остановлена');
  }

  // Принудительная синхронизация
  async forceSynchronization() {
    if (!this.isIntegrated) {
      throw new Error('Системы не интегрированы');
    }

    console.log('🔄 Принудительная синхронизация систем...');

    // Синхронизируем данные между системами
    const mainPrices = this.mainSystem.getAllDexPricesForPair?.('SOL/USDC') || {};
    const bundleQueue = this.bundleIntegration.getBundleQueueInfo();

    console.log(`📊 Основная система: ${Object.keys(mainPrices).length} DEX цен`);
    console.log(`📥 Bundle очередь: ${bundleQueue.length} возможностей`);

    return {
      mainPrices: Object.keys(mainPrices).length,
      bundleQueue: bundleQueue.length,
      synchronized: true
    };
  }

  // Тестирование интеграции
  async testIntegration() {
    console.log('🧪 Тестирование интеграции...');

    try {
      // Тест 1: Создание тестовой возможности
      const testOpportunity = {
        tokenSymbol: 'SOL/USDC',
        spread: 0.01,
        amount: 1000,
        buyDex: 'Jupiter',
        sellDex: 'Orca',
        timestamp: Date.now()
      };

      // Тест 2: Отправка в Bundle систему
      const result = await this.bundleIntegration.forceProcessOpportunity(testOpportunity);

      console.log(`✅ Тест интеграции: ${result ? 'УСПЕХ' : 'ПРОВАЛ'}`);
      return result;

    } catch (error) {
      console.error(`❌ Ошибка тестирования: ${error.message}`);
      return false;
    }
  }
}

module.exports = IntegrationBridge;
