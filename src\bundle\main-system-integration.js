/**
 * 🔗 MAIN SYSTEM INTEGRATION
 * Интеграция Bundle системы с основной торговой системой
 */

const BundleOrchestrator = require('./bundle-orchestrator');

class MainSystemIntegration {
  constructor(mainSystem) {
    this.mainSystem = mainSystem;
    this.bundleOrchestrator = null;
    this.isIntegrated = false;

    // Статистика интеграции
    this.integrationStats = {
      opportunitiesFromMain: 0,
      bundleProcessed: 0,
      bundleSuccessful: 0,
      totalProfitFromBundles: 0,
      startTime: Date.now()
    };

    console.log('🔗 Main System Integration инициализирован');

    // 🔥 УБИРАЕМ ДУБЛИРОВАНИЕ: Bundle Orchestrator создается ТОЛЬКО в integrate()!
    // this.createBundleOrchestratorSync(); // УБРАНО - создается в integrate()
  }

  // 🔥 СИНХРОННОЕ СОЗДАНИЕ bundleOrchestrator
  createBundleOrchestratorSync() {
    try {
      console.log('🔥 ПРИНУДИТЕЛЬНОЕ создание Bundle Orchestrator...');

      // Создаем Bundle Orchestrator с доступными компонентами
      this.bundleOrchestrator = new BundleOrchestrator(
        this.mainSystem.connection || null,
        this.mainSystem.wallet || null,
        null, // НЕТ MEV интеграции
        this.mainSystem.marginfiFlashLoan || null,
        this.mainSystem,
        this.mainSystem.atomicTransactionBuilder || null
      );

      console.log('✅ Bundle Orchestrator ПРИНУДИТЕЛЬНО СОЗДАН!');
      console.log(`✅ bundleOrchestrator: ${!!this.bundleOrchestrator}`);

    } catch (error) {
      console.error(`❌ Ошибка принудительного создания Bundle Orchestrator: ${error.message}`);
      // Создаем минимальный mock если не получается
      this.bundleOrchestrator = {
        addOpportunity: () => {
          console.log('🔧 Mock addOpportunity вызван');
          return true;
        }
      };
      console.log('🔧 Создан mock Bundle Orchestrator');
    }
  }

  // Интеграция с основной системой
  async integrate() {
    try {
      console.log('🔗 Интеграция Bundle системы с основной торговой системой...');

      // Проверяем наличие необходимых компонентов в основной системе
      this.validateMainSystem();

      // Инициализируем Bundle Orchestrator
      await this.initializeBundleOrchestrator();

      // Подключаем обработчики событий
      this.setupEventHandlers();

      // Запускаем Bundle Orchestrator
      await this.bundleOrchestrator.start();

      this.isIntegrated = true;
      console.log('✅ Bundle система успешно интегрирована!');

      return true;
    } catch (error) {
      console.error(`❌ Ошибка интеграции: ${error.message}`);
      return false;
    }
  }

  // Валидация основной системы
  validateMainSystem() {
    const requiredComponents = [
      'connection',
      'tradingExecutor',
      'wallet'
    ];

    for (const component of requiredComponents) {
      if (!this.mainSystem[component]) {
        throw new Error(`Отсутствует компонент: ${component}`);
      }
    }

    console.log('✅ Основная система валидирована');
  }

  // Инициализация Bundle Orchestrator
  async initializeBundleOrchestrator() {
    try {
      // ✅ УДАЛЯЕМ MOCK MEV - ИСПОЛЬЗУЕМ ТОЛЬКО РЕАЛЬНУЮ ТОРГОВЛЮ
      console.log('🚀 Инициализация Bundle Orchestrator БЕЗ MOCK MEV интеграции');

      // 🔥 ПРОВЕРЯЕМ ЧТО Bundle Orchestrator ЕЩЕ НЕ СОЗДАН!
      if (this.bundleOrchestrator) {
        console.log('⚠️ Bundle Orchestrator уже создан - пропускаем дублирование');
        return;
      }

      // 🔥 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Передаем атомарный строитель в Bundle Orchestrator
      console.log('🔥 ПЕРЕДАЕМ АТОМАРНЫЙ СТРОИТЕЛЬ В BUNDLE ORCHESTRATOR...');
      console.log(`   atomicTransactionBuilder: ${!!this.mainSystem.atomicTransactionBuilder}`);

      // 🔥 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: ПРОВЕРЯЕМ MARGINFI ПЕРЕД ПЕРЕДАЧЕЙ
      const marginfiToPass = this.mainSystem.marginfiFlashLoan || this.mainSystem.ultraFastMarginFi || null;
      console.log(`🔥 MARGINFI ДЛЯ BUNDLE: ${marginfiToPass ? 'НАЙДЕН' : 'НЕ НАЙДЕН'}`);
      if (marginfiToPass) {
        console.log(`   🏦 MarginFi тип: ${marginfiToPass.constructor.name}`);
        console.log(`   🏦 MarginFi инициализирован: ${!!marginfiToPass.client}`);
      }

      // Создаем Bundle Orchestrator с атомарным строителем
      this.bundleOrchestrator = new BundleOrchestrator(
        this.mainSystem.connection,
        this.mainSystem.wallet,
        null, // НЕТ MEV интеграции - только реальная торговля
        marginfiToPass,  // ✅ ИСПРАВЛЕНО: ПЕРЕДАЕМ ПРАВИЛЬНЫЙ MARGINFI!
        this.mainSystem,
        this.mainSystem.atomicTransactionBuilder // ✅ ПЕРЕДАЕМ АТОМАРНЫЙ СТРОИТЕЛЬ!
      );

      console.log('✅ Bundle Orchestrator инициализирован БЕЗ MOCK MEV - только реальная торговля');

    } catch (error) {
      console.error(`❌ Ошибка инициализации Bundle Orchestrator: ${error.message}`);
      throw error;
    }
  }

  // Настройка обработчиков событий
  setupEventHandlers() {
    console.log('🔧 Настройка обработчиков событий...');

    // Перехватываем арбитражные возможности из основной системы
    if (this.mainSystem.on) {
      this.mainSystem.on('arbitrageOpportunity', this.handleArbitrageOpportunity.bind(this));
      console.log('✅ Подключен к событию arbitrageOpportunity');
    } else {
      // Если основная система не поддерживает события, используем polling
      this.startOpportunityPolling();
    }

    // Подключаемся к событиям обновления цен
    if (this.mainSystem.on) {
      this.mainSystem.on('priceUpdate', this.handlePriceUpdate.bind(this));
      console.log('✅ Подключен к событию priceUpdate');
    }
  }

  // Обработка арбитражной возможности
  async handleArbitrageOpportunity(opportunity) {
    try {
      console.log(`🎯 Получена арбитражная возможность: ${opportunity.token || opportunity.tokenSymbol}`);

      // 🔥 КРИТИЧЕСКАЯ ПРОВЕРКА: bundleOrchestrator НЕ ДОЛЖЕН БЫТЬ NULL!
      if (!this.bundleOrchestrator) {
        console.error('❌ КРИТИЧЕСКАЯ ОШИБКА: bundleOrchestrator равен null!');
        console.log('🔧 Принудительно создаем bundleOrchestrator...');
        this.createBundleOrchestratorSync();

        if (!this.bundleOrchestrator) {
          throw new Error('Не удалось создать bundleOrchestrator');
        }
      }

      // Преобразуем возможность в формат Bundle системы
      const bundleOpportunity = this.transformOpportunity(opportunity);

      // Проверяем подходит ли для Bundle обработки
      if (this.shouldUseBundleProcessing(bundleOpportunity)) {
        console.log(`🚀 Отправляем в Bundle систему: ${bundleOpportunity.tokenSymbol}`);

        // Добавляем в Bundle очередь
        const added = this.bundleOrchestrator.addOpportunity(bundleOpportunity);

        if (added) {
          this.integrationStats.opportunitiesFromMain++;
          console.log(`✅ Возможность добавлена в Bundle очередь`);

          // ✅ КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: Ждем результата обработки
          const result = await this.waitForOpportunityResult(bundleOpportunity.id);

          if (result && result.success) {
            return {
              success: true,
              signature: result.signature,
              profit: result.profit,
              executionTime: result.executionTime,
              method: 'BUNDLE_SYSTEM'
            };
          } else {
            throw new Error(result?.error || result?.reason || 'Bundle обработка провалена');
          }
        }
      }

      return {
        success: false,
        error: 'Возможность не подходит для Bundle обработки'
      };

    } catch (error) {
      console.error(`❌ Ошибка обработки арбитражной возможности: ${error.message}`);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Ожидание результата обработки возможности
  async waitForOpportunityResult(opportunityId, timeout = 60000) {
    const startTime = Date.now();
    let checkCount = 0;

    console.log(`⏳ Ожидание результата для ${opportunityId}...`);

    while (Date.now() - startTime < timeout) {
      checkCount++;

      // Проверяем есть ли результат в Bundle Manager
      const result = this.bundleOrchestrator.getOpportunityResult(opportunityId);

      if (result) {
        console.log(`✅ Получен результат для ${opportunityId} после ${checkCount} проверок: ${result.success ? 'успех' : 'провал'}`);
        return result;
      }

      // Показываем прогресс каждые 5 секунд
      if (checkCount % 50 === 0) {
        const elapsed = Date.now() - startTime;
        console.log(`⏳ Ожидание ${opportunityId}: ${elapsed}ms / ${timeout}ms (проверка ${checkCount})`);

        // Показываем состояние очереди
        const queueInfo = this.bundleOrchestrator.getQueueInfo();
        console.log(`📊 Очередь Bundle: ${queueInfo.length} возможностей`);
      }

      // Ждем 100ms перед следующей проверкой
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    console.error(`⏰ Timeout ожидания результата для ${opportunityId} после ${checkCount} проверок`);

    // Показываем финальное состояние очереди
    const queueInfo = this.bundleOrchestrator.getQueueInfo();
    console.log(`📊 Финальное состояние очереди: ${queueInfo.length} возможностей`);

    return {
      success: false,
      error: `Timeout ожидания результата Bundle обработки (${checkCount} проверок)`
    };
  }

  // Преобразование возможности в формат Bundle системы
  transformOpportunity(opportunity) {
    const id = this.generateOpportunityId(opportunity);
    const tokenSymbol = opportunity.token || opportunity.tokenSymbol || 'UNKNOWN';

    return {
      id: id,
      tokenSymbol: tokenSymbol,
      spread: opportunity.spread || 0,
      amount: opportunity.tradeAmount || opportunity.amount || 1000,
      buyDex: opportunity.buyDex || 'Unknown',
      sellDex: opportunity.sellDex || 'Unknown',
      buyPrice: opportunity.buyPrice || 0,
      sellPrice: opportunity.sellPrice || 0,

      // 🔥 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: ДОБАВЛЯЕМ inputMint И outputMint!
      inputMint: opportunity.inputMint || this.getMintAddress(tokenSymbol, 'input'),
      outputMint: opportunity.outputMint || this.getMintAddress(tokenSymbol, 'output'),

      timestamp: opportunity.timestamp || Date.now(),
      source: 'main_system',
      originalOpportunity: opportunity
    };
  }

  // Генерация уникального ID для возможности
  generateOpportunityId(opportunity) {
    const tokenSymbol = opportunity.token || opportunity.tokenSymbol || 'UNKNOWN';
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);

    return `${tokenSymbol}_${timestamp}_${random}`;
  }

  // 🔥 КРИТИЧЕСКОЕ ИСПРАВЛЕНИЕ: ДОБАВЛЯЕМ getMintAddress МЕТОД!
  getMintAddress(tokenSymbol, type) {
    const mints = {
      'SOL': 'So11111111111111111111111111111111111111112',
      'USDC': 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
      'USDT': 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB',
      'WBTC': '********************************************',
      'JitoSOL': 'J1toso1uCk3RLmjorhTtrVwY9HJ7X8V9yYac6Y7kGCPn',
      'JUP': 'JUPyiwrYJFskUPiHa7hkeR8VUtAeFoSYbKedZNsDvCN'
    };

    if (tokenSymbol.includes('/')) {
      const [base, quote] = tokenSymbol.split('/');
      return type === 'input' ? mints[base] : mints[quote];
    }

    return mints[tokenSymbol] || mints['USDC'];
  }

  // Проверка подходит ли возможность для Bundle обработки
  shouldUseBundleProcessing(opportunity) {
    console.log(`🔍 Bundle проверка: спред=${opportunity.spread}, сумма=${opportunity.amount}, buyDex=${opportunity.buyDex}, sellDex=${opportunity.sellDex}, tokenSymbol=${opportunity.tokenSymbol}`);

    // 🔧 ИСПОЛЬЗУЕМ ЦЕНТРАЛИЗОВАННЫЙ КОНФИГ!
    const { TRADING_CONFIG } = require('../../trading-config.js');
    if (opportunity.spread < TRADING_CONFIG.MIN_SPREAD_PERCENT) {
      console.log(`❌ Bundle: спред слишком мал: ${opportunity.spread.toFixed(3)}% < ${TRADING_CONFIG.MIN_SPREAD_PERCENT}%`);
      return false;
    }

    // Минимальная сумма $100 (было $500)
    if (opportunity.amount < 100) {
      console.log(`❌ Bundle: сумма слишком мала: $${opportunity.amount} < $100`);
      return false;
    }

    // Поддерживаемые DEX
    const supportedDex = ['Jupiter', 'Raydium', 'Orca', 'Meteora'];
    if (!supportedDex.includes(opportunity.buyDex) && !supportedDex.includes(opportunity.sellDex)) {
      return false;
    }

    // Поддерживаемые токены
    const supportedTokens = ['SOL/USDC', 'SOL/USDT', 'WBTC/USDC', 'WBTC/SOL', 'SOL/JitoSOL', 'JUP/USDC'];
    if (!supportedTokens.includes(opportunity.tokenSymbol)) {
      return false;
    }

    console.log(`✅ Возможность подходит для Bundle обработки: ${opportunity.tokenSymbol}`);
    return true;
  }

  // Обработка обновления цен
  handlePriceUpdate(priceData) {
    // Можно использовать для дополнительной валидации возможностей
  }

  // Polling возможностей если события недоступны
  startOpportunityPolling() {
    console.log('🔄 Запуск polling арбитражных возможностей...');

    this.pollingInterval = setInterval(async () => {
      try {
        if (this.mainSystem.getArbitrageOpportunities) {
          const opportunities = await this.mainSystem.getArbitrageOpportunities();

          for (const opportunity of opportunities) {
            await this.handleArbitrageOpportunity(opportunity);
          }
        }
      } catch (error) {
        console.error(`❌ Ошибка polling: ${error.message}`);
      }
    }, 1000);
  }

  // ✅ MOCK MEV ИНТЕГРАЦИЯ УДАЛЕНА - ИСПОЛЬЗУЕМ ТОЛЬКО РЕАЛЬНУЮ ТОРГОВЛЮ

  // Получение статистики интеграции
  getIntegrationStats() {
    const bundleStats = this.bundleOrchestrator ? this.bundleOrchestrator.getStats() : {};
    const runtime = (Date.now() - this.integrationStats.startTime) / 1000 / 60;

    return {
      ...this.integrationStats,
      runtime: runtime.toFixed(1),
      isIntegrated: this.isIntegrated,
      bundleStats
    };
  }

  // Остановка интеграции
  async stop() {
    console.log('🛑 Остановка интеграции Bundle системы...');

    if (this.pollingInterval) {
      clearInterval(this.pollingInterval);
    }

    if (this.bundleOrchestrator) {
      await this.bundleOrchestrator.stop();
    }

    this.isIntegrated = false;
    console.log('✅ Интеграция Bundle системы остановлена');
  }

  // Получение информации о Bundle очереди
  getBundleQueueInfo() {
    if (!this.bundleOrchestrator) {
      return { length: 0, opportunities: [] };
    }

    return this.bundleOrchestrator.getQueueInfo();
  }
}

module.exports = MainSystemIntegration;
