class TimeoutManager {
  constructor() {
    this.timeouts = new Set();
    this.intervals = new Set();
  }
  
  setTimeout(callback, delay) {
    const timeoutId = setTimeout(() => {
      this.timeouts.delete(timeoutId);
      try {
        callback();
      } catch (error) {
        console.error('Timeout callback error:', error);
      }
    }, delay);
    this.timeouts.add(timeoutId);
    return timeoutId;
  }
  
  setInterval(callback, delay) {
    const intervalId = setInterval(() => {
      try {
        callback();
      } catch (error) {
        console.error('Interval callback error:', error);
      }
    }, delay);
    this.intervals.add(intervalId);
    return intervalId;
  }
  
  clearTimeout(timeoutId) {
    clearTimeout(timeoutId);
    this.timeouts.delete(timeoutId);
  }
  
  clearInterval(intervalId) {
    clearInterval(intervalId);
    this.intervals.delete(intervalId);
  }
  
  clearAll() {
    console.log(`🧹 TimeoutManager: очищаем ${this.timeouts.size} timeouts и ${this.intervals.size} intervals`);
    for (const timeoutId of this.timeouts) {
      clearTimeout(timeoutId);
    }
    for (const intervalId of this.intervals) {
      clearInterval(intervalId);
    }
    this.timeouts.clear();
    this.intervals.clear();
  }
  
  getStats() {
    return {
      activeTimeouts: this.timeouts.size,
      activeIntervals: this.intervals.size
    };
  }
}

module.exports = TimeoutManager;