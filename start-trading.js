#!/usr/bin/env node

/**
 * 🚀 ЗАПУСК РЕАЛЬНОЙ ТОРГОВЛИ
 * 
 * Простой скрипт для запуска production DLMM бота
 */

const ProductionDLMMBot = require('./production-dlmm-bot.js');

async function startTrading() {
    console.log('🚀 ЗАПУСК РЕАЛЬНОЙ DLMM ТОРГОВЛИ');
    console.log('=' .repeat(80));
    
    console.log('⚠️ ВНИМАНИЕ: ЭТО РЕАЛЬНАЯ ТОРГОВЛЯ!');
    console.log('⚠️ МАКСИМАЛЬНЫЙ РИСК: $0.01 (gas fee)');
    console.log('⚠️ ОЖИДАЕМАЯ ПРИБЫЛЬ: $22,239 (ROI 1.22%)');
    console.log('');
    
    // Показываем параметры стратегии
    console.log('🎯 ПАРАМЕТРЫ СТРАТЕГИИ:');
    console.log('   Flash Loan: $1,820,000 USDC');
    console.log('   Ликвидность: $1,400,000 USDC');
    console.log('   Торговля: $420,000 USDC (30% правило)');
    console.log('   Целевой ROI: 1.22%');
    console.log('');
    
    // Подтверждение от пользователя
    const readline = require('readline');
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });
    
    const answer = await new Promise((resolve) => {
        rl.question('🤔 Вы уверены что хотите запустить реальную торговлю? (yes/no): ', resolve);
    });
    
    rl.close();
    
    if (answer.toLowerCase() !== 'yes' && answer.toLowerCase() !== 'y') {
        console.log('❌ Торговля отменена пользователем');
        process.exit(0);
    }
    
    console.log('✅ Подтверждение получено. Запускаем торговлю...');
    console.log('');
    
    try {
        // Создаем и запускаем бота
        const bot = new ProductionDLMMBot();
        const result = await bot.run();
        
        if (result.success) {
            console.log('\n🎉 ТОРГОВЛЯ ЗАВЕРШЕНА УСПЕШНО!');
            console.log(`💰 Прибыль: $${result.profit?.toFixed(2) || 'N/A'}`);
            console.log(`⏱️ Время выполнения: ${result.executionTime || 'N/A'}ms`);
            console.log(`🔗 Signature: ${result.signature || 'N/A'}`);
        } else {
            console.log('\n❌ ТОРГОВЛЯ ЗАВЕРШЕНА С ОШИБКОЙ!');
            console.log(`💸 Потеря: $${result.loss || 0.01} (gas fee)`);
            console.log(`❌ Ошибка: ${result.error || 'Неизвестная ошибка'}`);
        }
        
    } catch (error) {
        console.error('\n💥 КРИТИЧЕСКАЯ ОШИБКА:', error.message);
        console.log('💸 Максимальная потеря: $0.01 (gas fee)');
    }
    
    console.log('\n📊 Для просмотра статистики запустите:');
    console.log('node production-dlmm-bot.js stats');
}

// Запуск
if (require.main === module) {
    startTrading().catch(console.error);
}

module.exports = { startTrading };
