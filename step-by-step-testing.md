# 🧪 **ПОШАГОВОЕ РУКОВОДСТВО ПО ТЕСТИРОВАНИЮ**

## 🚀 **БЫСТРЫЙ СТАРТ (5 МИНУТ)**

### **1️⃣ АВТОМАТИЧЕСКИЙ ТЕСТ ВСЕЙ СИСТЕМЫ**
```bash
# Запустить все тесты автоматически
node test-runner.js run
```

**Ожидаемый результат:**
```
🎯 Всего тестов: 6
✅ Пройдено: 6
❌ Провалено: 0
📊 Успешность: 100.0%
🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ! Система готова к использованию.
```

---

## 📋 **ДЕТАЛЬНОЕ ТЕСТИРОВАНИЕ (ПОШАГОВО)**

### **ШАГ 1: ПРОВЕРКА ФАЙЛОВ**
```bash
# Убедитесь что все файлы на месте
ls -la *.js | grep -E "(final-strategy|test-runner|error-handler|pda-calculator)"
```

**Должны быть:**
- ✅ `final-strategy-executor.js`
- ✅ `test-runner.js`
- ✅ `error-handler-validator.js`
- ✅ `pda-calculator.js`

### **ШАГ 2: БАЗОВАЯ ПРОВЕРКА СИСТЕМЫ**
```bash
node final-strategy-executor.js check
```

**Что проверяется:**
- 📦 Версии SDK
- 🛡️ Валидация компонентов
- 🔑 Program IDs

**Ожидаемый результат:**
```
📊 РЕЗУЛЬТАТЫ СИСТЕМНОЙ ПРОВЕРКИ:
   📦 SDK версии: ✅
   🧪 Devnet тест: ✅
   🛡️ Валидация: ✅
   🎯 ОБЩИЙ РЕЗУЛЬТАТ: ✅ ГОТОВО
```

### **ШАГ 3: ТЕСТ РАСЧЕТОВ**
```bash
node final-strategy-executor.js simulate
```

**Что проверяется:**
- 🧮 Расчет арбитражной прибыли
- 📊 Влияние на цену
- 🎯 Оптимальные bins
- 💰 Прибыльность

**Ожидаемый результат:**
```
🎉 ВЫПОЛНЕНИЕ ЗАВЕРШЕНО!
   ✅ Статус: УСПЕШНО
   💰 Прибыль: $58334
   📈 ROI: 3.24%
   ⏱️ Время: <5000ms
```

### **ШАГ 4: ТЕСТ PDA АДРЕСОВ**
```bash
node pda-calculator.js
```

**Что проверяется:**
- 🔑 Расчет Bin Array PDA
- 🏦 Расчет Reserve PDA
- 📊 Расчет Oracle PDA

**Ожидаемый результат:**
```
📊 Bin Array -1: 6K84Zmy8ZnihPCdoHona2iT25dFHsMAHzZtdjhtw5XG9
🏦 Reserve X: 4N22J4vW2juHocTntJNmXywSonYjkndCwahjZ2cYLDgb
🏦 Reserve Y: DwZz4S1Z1LBXomzmncQRVKCYhjCqSAMQ6RPKbUAadr7H
```

### **ШАГ 5: ТЕСТ ВАЛИДАЦИИ**
```bash
node error-handler-validator.js
```

**Что проверяется:**
- ✅ Параметры стратегии
- 🏊 Данные пулов
- 📊 Результаты расчетов
- 🔑 Адреса

**Ожидаемый результат:**
```
📊 ИТОГИ ВАЛИДАЦИИ:
   ✅ Статус: ВАЛИДНА
   ❌ Ошибок: 0
   ⚠️ Предупреждений: 0
```

---

## 🔧 **ДИАГНОСТИКА ПРОБЛЕМ**

### **❌ ЕСЛИ ТЕСТЫ ПРОВАЛИЛИСЬ:**

#### **Проблема 1: Ошибка модулей**
```bash
# Ошибка: Cannot find module
npm install @solana/web3.js @solana/spl-token
```

#### **Проблема 2: Ошибка сети**
```bash
# Ошибка: Network timeout
# Проверьте интернет соединение
ping google.com
```

#### **Проблема 3: Неправильные расчеты**
```bash
# Запустить детальную диагностику
node test-runner.js single "Симуляция"
```

#### **Проблема 4: PDA ошибки**
```bash
# Проверить отдельно PDA расчеты
node pda-calculator.js
```

---

## 🧪 **DEVNET ТЕСТИРОВАНИЕ**

### **ПОДГОТОВКА:**
```bash
# 1. Создать devnet кошелек
node devnet-tester.js

# 2. Проверить баланс (должно быть 2+ SOL)
# Адрес будет показан в выводе

# 3. Запросить дополнительный SOL если нужно
# Используйте Solana faucet: https://faucet.solana.com/
```

### **DEVNET ТЕСТЫ:**
```bash
# Полный devnet тест
node devnet-tester.js

# Ожидаемый результат:
# ✅ Кошелек создан
# ✅ SOL получен
# ⚠️ Пулы не настроены (нормально)
# ✅ Транзакция создана
# ✅ Валидация пройдена
```

---

## 📊 **МОНИТОРИНГ И ОТЧЕТЫ**

### **АВТОМАТИЧЕСКИЕ ОТЧЕТЫ:**
```bash
# Запуск с сохранением отчета
node test-runner.js run

# Просмотр отчета
cat test-report.json
```

### **НЕПРЕРЫВНОЕ ТЕСТИРОВАНИЕ:**
```bash
# Тестирование каждые 30 минут
node test-runner.js continuous 30
```

### **МОНИТОРИНГ SDK:**
```bash
# Проверка обновлений SDK
node sdk-version-monitor.js

# Просмотр истории
cat sdk-version-history.json
```

---

## 🎯 **КРИТЕРИИ УСПЕШНОСТИ**

### **✅ СИСТЕМА ГОТОВА ЕСЛИ:**
1. **Все тесты пройдены** (6/6)
2. **ROI > 2%** (цель: 3.24%)
3. **Прибыль > $50K** (цель: $58K)
4. **Время выполнения < 10 сек**
5. **Валидация без ошибок**
6. **PDA адреса рассчитываются**

### **⚠️ ТРЕБУЕТ ВНИМАНИЯ ЕСЛИ:**
- Тесты проходят но с предупреждениями
- ROI 1-2% (низкий но рабочий)
- Время выполнения > 10 сек
- Есть некритические ошибки

### **❌ НЕ ГОТОВО ЕСЛИ:**
- Критические тесты провалены
- ROI < 1%
- Ошибки валидации
- PDA не рассчитываются

---

## 🚀 **ПЕРЕХОД К PRODUCTION**

### **ПОСЛЕ УСПЕШНЫХ ТЕСТОВ:**

1. **Создайте production кошелек**
2. **Измените параметры на реальные**
3. **Запустите финальную проверку:**
   ```bash
   node final-strategy-executor.js check
   node final-strategy-executor.js simulate
   ```
4. **Начните с малых сумм**
5. **Мониторьте результаты**

---

## 📞 **ПОДДЕРЖКА**

### **ЕСЛИ НУЖНА ПОМОЩЬ:**
1. **Запустите диагностику:**
   ```bash
   node test-runner.js run > test-output.log 2>&1
   ```
2. **Проверьте логи в `test-output.log`**
3. **Найдите конкретную ошибку**
4. **Используйте отладочные команды**

### **ПОЛЕЗНЫЕ КОМАНДЫ:**
```bash
# Показать статистику
node final-strategy-executor.js stats

# Проверить только валидацию
node error-handler-validator.js

# Проверить только PDA
node pda-calculator.js

# Один конкретный тест
node test-runner.js single "Системная"
```

---

## 🎉 **ГОТОВО К ТЕСТИРОВАНИЮ!**

**Начните с команды:**
```bash
node test-runner.js run
```

**Если все тесты пройдены - система готова к использованию!** 🚀
