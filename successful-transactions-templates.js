#!/usr/bin/env node

/**
 * 🔥 ШАБЛОНЫ УСПЕШНЫХ ТРАНЗАКЦИЙ
 * 
 * 🎯 ЦЕЛЬ: Сохранить успешные транзакции и модифицировать их для мгновенной торговли
 * ✅ 3 пула - провести по 1 успешной транзакции на каждом
 * ✅ Сохранить точную структуру аккаунтов
 * ✅ Модифицировать только amount_in и min_amount_out
 * ✅ Торговать за миллисекунды
 */

const { PublicKey, TransactionInstruction } = require('@solana/web3.js');

class SuccessfulTransactionsTemplates {
    constructor(connection, wallet) {
        this.connection = connection;
        this.wallet = wallet;
        
        // 🔥 2 РАБОЧИХ ПУЛА ДЛЯ БЫСТРОЙ ТОРГОВЛИ (ТОЛЬКО ПРОВЕРЕННЫЕ)
        this.TARGET_POOLS = {
            pool1: '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6', // SOL/USDC
            pool2: 'BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y'   // SOL/USDC
        };
        
        // 🔥 ШАБЛОНЫ УСПЕШНЫХ ТРАНЗАКЦИЙ (БУДУТ ЗАПОЛНЕНЫ)
        this.successfulTemplates = {};
        
        console.log('🔥 СИСТЕМА ШАБЛОНОВ УСПЕШНЫХ ТРАНЗАКЦИЙ');
        console.log('✅ 3 целевых пула для быстрой торговли');
        console.log('✅ Готов сохранять и модифицировать успешные транзакции');
    }
    
    /**
     * 🎯 ЭТАП 1: ПРОВЕСТИ ТЕСТОВЫЕ ТРАНЗАКЦИИ НА КАЖДОМ ПУЛЕ
     */
    async conductTestTransactions() {
        console.log('\n🎯 ЭТАП 1: ПРОВЕДЕНИЕ ТЕСТОВЫХ ТРАНЗАКЦИЙ...');
        
        const MeteoraHybridImplementation = require('./meteora-hybrid-implementation.js');
        const meteoraSwap = new MeteoraHybridImplementation(this.connection, this.wallet);
        
        for (const [poolName, poolAddress] of Object.entries(this.TARGET_POOLS)) {
            console.log(`\n📋 Тестовая транзакция для ${poolName} (${poolAddress})...`);
            
            try {
                // Создаем минимальную swap инструкцию
                const result = await meteoraSwap.createStableSwapInstruction(
                    poolAddress,
                    1000000, // 0.001 SOL - минимальная сумма
                    1        // минимальный выход
                );
                
                if (result.success) {
                    console.log(`✅ ${poolName}: Инструкция создана (${result.accountsCount} аккаунтов)`);
                    
                    // Сохраняем шаблон
                    this.successfulTemplates[poolName] = {
                        instruction: result.instruction,
                        accountsCount: result.accountsCount,
                        poolData: result.poolData,
                        timestamp: Date.now()
                    };
                    
                } else {
                    console.log(`❌ ${poolName}: Ошибка создания инструкции`);
                }
                
            } catch (error) {
                console.log(`❌ ${poolName}: Ошибка - ${error.message}`);
            }
        }
        
        console.log('\n📊 РЕЗУЛЬТАТЫ ТЕСТОВЫХ ТРАНЗАКЦИЙ:');
        Object.keys(this.successfulTemplates).forEach(poolName => {
            const template = this.successfulTemplates[poolName];
            console.log(`✅ ${poolName}: ${template.accountsCount} аккаунтов, создан ${new Date(template.timestamp).toLocaleTimeString()}`);
        });
    }
    
    /**
     * 🔥 ЭТАП 2: СОХРАНИТЬ ШАБЛОНЫ В ФАЙЛ
     */
    async saveTemplatesToFile() {
        console.log('\n🔥 ЭТАП 2: СОХРАНЕНИЕ ШАБЛОНОВ В ФАЙЛ...');
        
        const fs = require('fs');
        
        // Подготавливаем данные для сохранения
        const templatesData = {};
        
        for (const [poolName, template] of Object.entries(this.successfulTemplates)) {
            templatesData[poolName] = {
                poolAddress: this.TARGET_POOLS[poolName],
                accountsCount: template.accountsCount,
                timestamp: template.timestamp,
                
                // Сохраняем структуру аккаунтов
                accounts: template.instruction.keys.map(key => ({
                    pubkey: key.pubkey.toString(),
                    isSigner: key.isSigner,
                    isWritable: key.isWritable
                })),
                
                // Сохраняем program ID
                programId: template.instruction.programId.toString(),
                
                // Сохраняем базовые данные (без amount)
                baseData: template.instruction.data.toString('hex'),
                
                // Метаданные
                poolData: template.poolData
            };
        }
        
        // Сохраняем в файл
        const filename = 'successful-transactions-templates.json';
        fs.writeFileSync(filename, JSON.stringify(templatesData, null, 2));
        
        console.log(`✅ Шаблоны сохранены в ${filename}`);
        console.log(`📊 Сохранено шаблонов: ${Object.keys(templatesData).length}`);
        
        return filename;
    }
    
    /**
     * ⚡ ЭТАП 3: ЗАГРУЗИТЬ ШАБЛОНЫ ИЗ ФАЙЛА
     */
    loadTemplatesFromFile(filename = 'successful-transactions-templates.json') {
        console.log('\n⚡ ЭТАП 3: ЗАГРУЗКА ШАБЛОНОВ ИЗ ФАЙЛА...');
        
        const fs = require('fs');
        
        if (!fs.existsSync(filename)) {
            throw new Error(`Файл шаблонов ${filename} не найден!`);
        }
        
        const templatesData = JSON.parse(fs.readFileSync(filename, 'utf8'));
        
        // Восстанавливаем шаблоны
        this.successfulTemplates = {};
        
        for (const [poolName, data] of Object.entries(templatesData)) {
            this.successfulTemplates[poolName] = {
                poolAddress: data.poolAddress,
                accountsCount: data.accountsCount,
                timestamp: data.timestamp,
                
                // Восстанавливаем аккаунты
                accounts: data.accounts.map(acc => ({
                    pubkey: new PublicKey(acc.pubkey),
                    isSigner: acc.isSigner,
                    isWritable: acc.isWritable
                })),
                
                programId: new PublicKey(data.programId),
                baseData: Buffer.from(data.baseData, 'hex'),
                poolData: data.poolData
            };
        }
        
        console.log(`✅ Загружено шаблонов: ${Object.keys(this.successfulTemplates).length}`);
        Object.keys(this.successfulTemplates).forEach(poolName => {
            const template = this.successfulTemplates[poolName];
            console.log(`   ${poolName}: ${template.accountsCount} аккаунтов`);
        });
    }
    
    /**
     * 🚀 ЭТАП 4: МГНОВЕННАЯ МОДИФИКАЦИЯ ДЛЯ ТОРГОВЛИ
     */
    createInstantSwapInstruction(poolName, amountIn, minAmountOut) {
        const template = this.successfulTemplates[poolName];
        if (!template) {
            throw new Error(`Шаблон для ${poolName} не найден!`);
        }
        
        // Клонируем базовые данные
        const data = Buffer.from(template.baseData);
        
        // Модифицируем только amount_in и min_amount_out
        data.writeBigUInt64LE(BigInt(amountIn), 8);      // позиция 8: amount_in
        data.writeBigUInt64LE(BigInt(minAmountOut), 16); // позиция 16: min_amount_out
        
        // Создаем инструкцию
        return new TransactionInstruction({
            keys: [...template.accounts], // клонируем аккаунты
            programId: template.programId,
            data: data
        });
    }
    
    /**
     * 🧪 ПОЛНЫЙ ТЕСТ СИСТЕМЫ
     */
    async runFullTest() {
        console.log('\n🧪 ПОЛНЫЙ ТЕСТ СИСТЕМЫ ШАБЛОНОВ...');
        
        try {
            // Этап 1: Провести тестовые транзакции
            await this.conductTestTransactions();
            
            if (Object.keys(this.successfulTemplates).length === 0) {
                throw new Error('Не удалось создать ни одного шаблона!');
            }
            
            // Этап 2: Сохранить в файл
            const filename = await this.saveTemplatesToFile();
            
            // Этап 3: Загрузить из файла (тест)
            this.successfulTemplates = {}; // очищаем
            this.loadTemplatesFromFile(filename);
            
            // Этап 4: Тест мгновенной модификации
            console.log('\n🚀 ТЕСТ МГНОВЕННОЙ МОДИФИКАЦИИ:');
            
            const startTime = Date.now();
            
            for (const poolName of Object.keys(this.successfulTemplates)) {
                const instruction = this.createInstantSwapInstruction(
                    poolName,
                    50000000, // 0.05 SOL
                    1         // min out
                );
                
                console.log(`⚡ ${poolName}: ${instruction.keys.length} аккаунтов, ${instruction.data.length} bytes`);
            }
            
            const endTime = Date.now();
            console.log(`⚡ Все инструкции созданы за ${endTime - startTime}ms`);
            
            console.log('\n🎉 СИСТЕМА ШАБЛОНОВ РАБОТАЕТ ИДЕАЛЬНО!');
            console.log('✅ Готов к мгновенной торговле между 3 пулами');
            
        } catch (error) {
            console.error('❌ Ошибка теста системы:', error.message);
            throw error;
        }
    }
}

module.exports = SuccessfulTransactionsTemplates;

// 🧪 ТЕСТ ЕСЛИ ФАЙЛ ВЫЗВАН НАПРЯМУЮ
if (require.main === module) {
    const { Connection, Keypair } = require('@solana/web3.js');
    const fs = require('fs');
    
    async function runTest() {
        try {
            const connection = new Connection('https://api.mainnet-beta.solana.com', 'confirmed');
            const walletData = JSON.parse(fs.readFileSync('./wallet.json', 'utf8'));
            const wallet = Keypair.fromSecretKey(new Uint8Array(walletData));
            
            const templates = new SuccessfulTransactionsTemplates(connection, wallet);
            await templates.runFullTest();
            
        } catch (error) {
            console.error('❌ Ошибка теста:', error.message);
        }
    }
    
    runTest();
}
