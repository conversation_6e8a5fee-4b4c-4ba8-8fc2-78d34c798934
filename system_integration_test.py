#!/usr/bin/env python3
"""
🧪 SYSTEM INTEGRATION TEST
Полная проверка интеграции всех компонентов системы
"""

import asyncio
import json
import time
from datetime import datetime
import logging
import sys
from pathlib import Path

# Настройка логирования
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SystemIntegrationTest:
    """Тестирование интеграции всех компонентов"""
    
    def __init__(self):
        self.test_results = {
            'start_time': datetime.now(),
            'tests_passed': 0,
            'tests_failed': 0,
            'component_status': {},
            'integration_status': {},
            'performance_metrics': {}
        }
    
    async def run_full_integration_test(self):
        """Запуск полного теста интеграции"""
        print("🧪 SYSTEM INTEGRATION TEST")
        print("=" * 60)
        
        try:
            # Тест 1: Проверка компонентов
            await self._test_individual_components()
            
            # Тест 2: Проверка интеграции
            await self._test_component_integration()
            
            # Тест 3: Проверка производительности
            await self._test_system_performance()
            
            # Тест 4: Проверка данных
            await self._test_data_consistency()
            
            # Генерация отчета
            await self._generate_test_report()
            
        except Exception as e:
            logger.error(f"❌ Критическая ошибка теста: {e}")
            self.test_results['critical_error'] = str(e)
    
    async def _test_individual_components(self):
        """Тест отдельных компонентов"""
        print("\n🔧 ТЕСТ КОМПОНЕНТОВ")
        print("-" * 40)
        
        # Тест 1.1: Unified Data Management
        try:
            from unified_data_management_system import UnifiedDataManager
            
            async with UnifiedDataManager("test_integration.db") as dm:
                # Тест создания таблиц
                await dm.initialize_database()
                
                # Тест импорта данных
                test_data = [{
                    'name': 'Test Program',
                    'url': 'https://test.com',
                    'contracts': ['0x123'],
                    'endpoints': ['https://api.test.com'],
                    'priority_score': 0.8
                }]
                
                imported = await dm.import_immunefi_data(test_data)
                assert imported == 1, "Импорт данных не работает"
                
                # Тест получения целей
                targets = await dm.get_targets_for_testing(limit=1)
                assert len(targets) == 1, "Получение целей не работает"
                
                self.test_results['component_status']['data_manager'] = '✅ PASSED'
                self.test_results['tests_passed'] += 1
                print("✅ Unified Data Management: PASSED")
                
        except Exception as e:
            self.test_results['component_status']['data_manager'] = f'❌ FAILED: {e}'
            self.test_results['tests_failed'] += 1
            print(f"❌ Unified Data Management: FAILED - {e}")
        
        # Тест 1.2: Strategy Integration Engine
        try:
            from strategy_integration_engine import StrategyIntegrationEngine
            from unified_data_management_system import UnifiedDataManager, TestTarget
            
            async with UnifiedDataManager("test_integration.db") as dm:
                engine = StrategyIntegrationEngine(dm)
                await engine.initialize_strategies()
                
                assert len(engine.loaded_strategies) > 0, "Стратегии не загружены"
                
                # Тест выполнения стратегии
                test_target = TestTarget(
                    target_id="test_001",
                    target_type="test",
                    name="Test Target",
                    url="https://test.com",
                    contracts=["0x123"],
                    endpoints=["https://api.test.com"],
                    metadata={},
                    priority_score=0.8
                )
                
                # Выполняем базовую стратегию
                if 'basic_contract_analysis' in engine.loaded_strategies:
                    result = await engine.execute_strategy('basic_contract_analysis', test_target)
                    assert result.success, "Выполнение стратегии не работает"
                
                self.test_results['component_status']['strategy_engine'] = '✅ PASSED'
                self.test_results['tests_passed'] += 1
                print("✅ Strategy Integration Engine: PASSED")
                
        except Exception as e:
            self.test_results['component_status']['strategy_engine'] = f'❌ FAILED: {e}'
            self.test_results['tests_failed'] += 1
            print(f"❌ Strategy Integration Engine: FAILED - {e}")
        
        # Тест 1.3: Intelligent Retest System
        try:
            from intelligent_retest_system import IntelligentRetestSystem
            from strategy_integration_engine import StrategyIntegrationEngine
            from unified_data_management_system import UnifiedDataManager
            
            async with UnifiedDataManager("test_integration.db") as dm:
                engine = StrategyIntegrationEngine(dm)
                await engine.initialize_strategies()
                
                retest_system = IntelligentRetestSystem(dm, engine)
                
                # Тест анализа кандидатов
                candidates = await retest_system.analyze_retest_candidates()
                assert isinstance(candidates, list), "Анализ кандидатов не работает"
                
                # Тест статистики
                stats = await retest_system.get_retest_statistics()
                assert 'total_queued_tasks' in stats, "Статистика не работает"
                
                self.test_results['component_status']['retest_system'] = '✅ PASSED'
                self.test_results['tests_passed'] += 1
                print("✅ Intelligent Retest System: PASSED")
                
        except Exception as e:
            self.test_results['component_status']['retest_system'] = f'❌ FAILED: {e}'
            self.test_results['tests_failed'] += 1
            print(f"❌ Intelligent Retest System: FAILED - {e}")
        
        # Тест 1.4: Immunefi Integration
        try:
            from immunefi_mass_scanner import ImmunefiBountyParser
            
            async with ImmunefiBountyParser() as parser:
                # Тест подключения (без реальных запросов)
                assert hasattr(parser, 'fetch_bounty_list'), "Парсер не имеет нужных методов"
                
                self.test_results['component_status']['immunefi_parser'] = '✅ PASSED'
                self.test_results['tests_passed'] += 1
                print("✅ Immunefi Integration: PASSED")
                
        except Exception as e:
            self.test_results['component_status']['immunefi_parser'] = f'❌ FAILED: {e}'
            self.test_results['tests_failed'] += 1
            print(f"❌ Immunefi Integration: FAILED - {e}")
    
    async def _test_component_integration(self):
        """Тест интеграции компонентов"""
        print("\n🔗 ТЕСТ ИНТЕГРАЦИИ")
        print("-" * 40)
        
        # Тест 2.1: Master Coordinator
        try:
            from master_bug_hunting_coordinator import MasterBugHuntingCoordinator
            
            # Создаем координатор (без полного запуска)
            coordinator = MasterBugHuntingCoordinator()
            
            # Проверяем инициализацию
            assert hasattr(coordinator, 'data_manager'), "Координатор не имеет data_manager"
            assert hasattr(coordinator, 'run_full_hunting_cycle'), "Координатор не имеет основного метода"
            
            self.test_results['integration_status']['master_coordinator'] = '✅ PASSED'
            self.test_results['tests_passed'] += 1
            print("✅ Master Coordinator Integration: PASSED")
            
        except Exception as e:
            self.test_results['integration_status']['master_coordinator'] = f'❌ FAILED: {e}'
            self.test_results['tests_failed'] += 1
            print(f"❌ Master Coordinator Integration: FAILED - {e}")
        
        # Тест 2.2: Data Flow
        try:
            from unified_data_management_system import UnifiedDataManager, TestTarget, TestResult
            from strategy_integration_engine import StrategyIntegrationEngine
            from datetime import datetime
            import hashlib
            
            async with UnifiedDataManager("test_integration.db") as dm:
                engine = StrategyIntegrationEngine(dm)
                await engine.initialize_strategies()
                
                # Создаем тестовую цель
                test_target = TestTarget(
                    target_id="integration_test_001",
                    target_type="test",
                    name="Integration Test Target",
                    url="https://test.com",
                    contracts=["0x123"],
                    endpoints=["https://api.test.com"],
                    metadata={'test': True},
                    priority_score=0.9
                )
                
                # Сохраняем цель
                await dm.save_target(test_target)
                
                # Выполняем стратегию
                if engine.loaded_strategies:
                    strategy_name = list(engine.loaded_strategies.keys())[0]
                    result = await engine.execute_strategy(strategy_name, test_target)
                    
                    # Проверяем, что результат сохранен в базе
                    report = await dm.generate_testing_report()
                    assert report['summary']['total_test_results'] > 0, "Результаты не сохраняются"
                
                self.test_results['integration_status']['data_flow'] = '✅ PASSED'
                self.test_results['tests_passed'] += 1
                print("✅ Data Flow Integration: PASSED")
                
        except Exception as e:
            self.test_results['integration_status']['data_flow'] = f'❌ FAILED: {e}'
            self.test_results['tests_failed'] += 1
            print(f"❌ Data Flow Integration: FAILED - {e}")
    
    async def _test_system_performance(self):
        """Тест производительности системы"""
        print("\n⚡ ТЕСТ ПРОИЗВОДИТЕЛЬНОСТИ")
        print("-" * 40)
        
        try:
            from unified_data_management_system import UnifiedDataManager, TestTarget
            from strategy_integration_engine import StrategyIntegrationEngine
            
            start_time = time.time()
            
            async with UnifiedDataManager("test_integration.db") as dm:
                engine = StrategyIntegrationEngine(dm)
                await engine.initialize_strategies()
                
                # Создаем несколько тестовых целей
                targets = []
                for i in range(3):
                    target = TestTarget(
                        target_id=f"perf_test_{i}",
                        target_type="test",
                        name=f"Performance Test {i}",
                        url=f"https://test{i}.com",
                        contracts=[f"0x{i}23"],
                        endpoints=[f"https://api.test{i}.com"],
                        metadata={'performance_test': True},
                        priority_score=0.7
                    )
                    targets.append(target)
                    await dm.save_target(target)
                
                # Тестируем параллельное выполнение
                tasks = []
                for target in targets:
                    if engine.loaded_strategies:
                        strategy_name = list(engine.loaded_strategies.keys())[0]
                        task = engine.execute_strategy(strategy_name, target)
                        tasks.append(task)
                
                if tasks:
                    results = await asyncio.gather(*tasks, return_exceptions=True)
                    successful_results = [r for r in results if not isinstance(r, Exception)]
                    
                    execution_time = time.time() - start_time
                    
                    self.test_results['performance_metrics'] = {
                        'targets_tested': len(targets),
                        'successful_executions': len(successful_results),
                        'total_execution_time': execution_time,
                        'avg_time_per_target': execution_time / len(targets) if targets else 0,
                        'strategies_loaded': len(engine.loaded_strategies)
                    }
                    
                    # Проверяем производительность
                    assert execution_time < 30, f"Слишком медленное выполнение: {execution_time:.2f}с"
                    assert len(successful_results) > 0, "Нет успешных выполнений"
                    
                    self.test_results['integration_status']['performance'] = '✅ PASSED'
                    self.test_results['tests_passed'] += 1
                    print(f"✅ Performance Test: PASSED ({execution_time:.2f}s for {len(targets)} targets)")
                else:
                    print("⚠️ Performance Test: SKIPPED (no strategies available)")
                
        except Exception as e:
            self.test_results['integration_status']['performance'] = f'❌ FAILED: {e}'
            self.test_results['tests_failed'] += 1
            print(f"❌ Performance Test: FAILED - {e}")
    
    async def _test_data_consistency(self):
        """Тест консистентности данных"""
        print("\n💾 ТЕСТ ДАННЫХ")
        print("-" * 40)
        
        try:
            from unified_data_management_system import UnifiedDataManager
            
            async with UnifiedDataManager("test_integration.db") as dm:
                # Проверяем структуру базы данных
                cursor = dm.conn.cursor()
                
                # Проверяем таблицы
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]
                
                required_tables = ['test_targets', 'test_results', 'strategies', 'test_sessions']
                for table in required_tables:
                    assert table in tables, f"Таблица {table} не найдена"
                
                # Проверяем данные
                cursor.execute("SELECT COUNT(*) FROM test_targets")
                target_count = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM test_results")
                result_count = cursor.fetchone()[0]
                
                # Генерируем отчет
                report = await dm.generate_testing_report()
                assert 'summary' in report, "Отчет не содержит summary"
                assert 'timestamp' in report, "Отчет не содержит timestamp"
                
                self.test_results['integration_status']['data_consistency'] = '✅ PASSED'
                self.test_results['tests_passed'] += 1
                print(f"✅ Data Consistency: PASSED ({target_count} targets, {result_count} results)")
                
        except Exception as e:
            self.test_results['integration_status']['data_consistency'] = f'❌ FAILED: {e}'
            self.test_results['tests_failed'] += 1
            print(f"❌ Data Consistency: FAILED - {e}")
    
    async def _generate_test_report(self):
        """Генерация отчета о тестировании"""
        print("\n📄 ОТЧЕТ О ТЕСТИРОВАНИИ")
        print("=" * 60)
        
        end_time = datetime.now()
        duration = (end_time - self.test_results['start_time']).total_seconds()
        
        self.test_results.update({
            'end_time': end_time,
            'duration_seconds': duration,
            'total_tests': self.test_results['tests_passed'] + self.test_results['tests_failed'],
            'success_rate': self.test_results['tests_passed'] / (self.test_results['tests_passed'] + self.test_results['tests_failed']) if (self.test_results['tests_passed'] + self.test_results['tests_failed']) > 0 else 0
        })
        
        print(f"⏱️  Время выполнения: {duration:.2f} секунд")
        print(f"✅ Тестов пройдено: {self.test_results['tests_passed']}")
        print(f"❌ Тестов провалено: {self.test_results['tests_failed']}")
        print(f"📊 Успешность: {self.test_results['success_rate']:.1%}")
        
        print(f"\n🔧 СТАТУС КОМПОНЕНТОВ:")
        for component, status in self.test_results['component_status'].items():
            print(f"   {component}: {status}")
        
        print(f"\n🔗 СТАТУС ИНТЕГРАЦИИ:")
        for integration, status in self.test_results['integration_status'].items():
            print(f"   {integration}: {status}")
        
        if self.test_results['performance_metrics']:
            print(f"\n⚡ МЕТРИКИ ПРОИЗВОДИТЕЛЬНОСТИ:")
            metrics = self.test_results['performance_metrics']
            print(f"   Стратегий загружено: {metrics.get('strategies_loaded', 0)}")
            print(f"   Целей протестировано: {metrics.get('targets_tested', 0)}")
            print(f"   Успешных выполнений: {metrics.get('successful_executions', 0)}")
            print(f"   Среднее время на цель: {metrics.get('avg_time_per_target', 0):.2f}с")
        
        # Сохранение отчета
        report_filename = f"integration_test_report_{int(time.time())}.json"
        with open(report_filename, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n💾 Отчет сохранен: {report_filename}")
        
        # Итоговый статус
        if self.test_results['tests_failed'] == 0:
            print(f"\n🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ УСПЕШНО!")
            print(f"✅ Система готова к работе!")
        else:
            print(f"\n⚠️  ЕСТЬ ПРОБЛЕМЫ В СИСТЕМЕ")
            print(f"❌ Требуется исправление ошибок")
        
        return self.test_results

async def main():
    """Запуск интеграционного теста"""
    tester = SystemIntegrationTest()
    await tester.run_full_integration_test()

if __name__ == "__main__":
    asyncio.run(main())
