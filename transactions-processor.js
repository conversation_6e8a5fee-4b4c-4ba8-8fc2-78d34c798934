/**
 * 🔄 TRANSACTIONS PROCESSOR - ОБРАБОТКА И ОТПРАВКА ТРАНЗАКЦИЙ
 * ═══════════════════════════════════════════════════════════════
 * 🎯 ЦЕЛЬ: Правильная обработка, подпись и отправка транзакций Solana
 * 🔧 ФУНКЦИИ: Подпись, отправка, подтверждение, обработка ошибок
 * 📋 АРХИТЕКТУРА: Atomic Builder → Processor → Solana Network
 */

const { Connection, Transaction, VersionedTransaction, TransactionMessage } = require('@solana/web3.js');

class TransactionProcessor {
  constructor(connection, wallet, commitment = "confirmed") {
    this.connection = connection;
    this.wallet = wallet;
    this.commitment = commitment;
  }

  /**
   * 🔐 ПОДПИСЬ ОДНОЙ ТРАНЗАКЦИИ
   */
  async signTransaction(txRequest) {
    const { transactions, lastValidBlockHeight, blockhash } = await this.signTransactions([txRequest]);
    return { transaction: transactions[0], lastValidBlockHeight, blockhash };
  }

  /**
   * 🔐 ПОДПИСЬ НЕСКОЛЬКИХ ТРАНЗАКЦИЙ
   */
  async signTransactions(txRequests) {
    const { blockhash, lastValidBlockHeight } = await this.connection.getLatestBlockhash(this.commitment);
    const feePayer = this.wallet.publicKey;
    
    const pSignedTxs = txRequests.map((txRequest) => {
      return this.rewriteTransaction(txRequest, feePayer, blockhash);
    });
    
    const transactions = await this.wallet.signAllTransactions(pSignedTxs);
    
    return {
      transactions,
      lastValidBlockHeight,
      blockhash,
    };
  }

  /**
   * 📤 ОТПРАВКА ТРАНЗАКЦИИ
   */
  async sendTransaction(transaction, lastValidBlockHeight, blockhash) {
    const execute = this.constructSendTransactions([transaction], lastValidBlockHeight, blockhash);
    const txs = await execute();
    const ex = txs[0];
    
    if (ex.status === "fulfilled") {
      return ex.value;
    } else {
      throw ex.reason;
    }
  }

  /**
   * 🔧 КОНСТРУКТОР ОТПРАВКИ ТРАНЗАКЦИЙ
   */
  constructSendTransactions(transactions, lastValidBlockHeight, blockhash, parallel = true) {
    const executeTx = async (tx) => {
      const rawTxs = tx.serialize();
      return this.connection.sendRawTransaction(rawTxs, {
        preflightCommitment: this.commitment,
      });
    };
    
    const confirmTx = async (txId) => {
      const result = await this.connection.confirmTransaction({
        signature: txId,
        lastValidBlockHeight: lastValidBlockHeight,
        blockhash,
      }, this.commitment);
      
      if (result.value.err) {
        throw new Error(`Transaction failed: ${JSON.stringify(result.value)}`);
      }
    };

    return async () => {
      if (parallel) {
        const promises = transactions.map(async (tx) => {
          const txId = await executeTx(tx);
          await confirmTx(txId);
          return txId;
        });
        return Promise.allSettled(promises);
      } else {
        const results = [];
        for (const tx of transactions) {
          try {
            const txId = await executeTx(tx);
            await confirmTx(txId);
            results.push({ status: "fulfilled", value: txId });
          } catch (error) {
            results.push({ status: "rejected", reason: error });
          }
        }
        return results;
      }
    };
  }

  /**
   * 🔄 ПЕРЕПИСЫВАНИЕ ТРАНЗАКЦИИ С НОВЫМ BLOCKHASH
   */
  rewriteTransaction(txRequest, feePayer, blockhash) {
    if (txRequest instanceof VersionedTransaction) {
      // 🔥 ПРОСТОЕ РЕШЕНИЕ: НЕ ПЕРЕПИСЫВАЕМ VersionedTransaction!
      // Транзакция уже создана с правильным blockhash
      console.log('🔥 VersionedTransaction: используем как есть (blockhash уже правильный)');
      return txRequest;
    } else if (txRequest instanceof Transaction) {
      // Legacy транзакция
      const newTx = new Transaction(txRequest);
      newTx.recentBlockhash = blockhash;
      newTx.feePayer = feePayer;
      return newTx;
    } else {
      throw new Error('Неподдерживаемый тип транзакции');
    }
  }

  /**
   * 🔥 НИЗКОУРОВНЕВЫЙ ПОДХОД: НЕ СИМУЛИРУЕМ!
   */
  async simulateTransaction(transaction) {
    console.log('🔥 SIMULATION ОТКЛЮЧЕНА! НИЗКОУРОВНЕВЫЙ РЕЖИМ!');
    return {
      success: true,
      error: null,
      logs: ['🔥 SIMULATION DISABLED - LOW LEVEL MODE'],
      unitsConsumed: 0,
    };
  }

  /**
   * 🔍 ПОЛУЧЕНИЕ СТАТУСА ТРАНЗАКЦИИ
   */
  async getTransactionStatus(signature) {
    try {
      const status = await this.connection.getSignatureStatus(signature);
      return {
        confirmed: status.value?.confirmationStatus === 'confirmed' || status.value?.confirmationStatus === 'finalized',
        finalized: status.value?.confirmationStatus === 'finalized',
        error: status.value?.err,
        slot: status.value?.slot,
      };
    } catch (error) {
      return {
        confirmed: false,
        finalized: false,
        error: error.message,
        slot: null,
      };
    }
  }

  /**
   * ⏱️ ОЖИДАНИЕ ПОДТВЕРЖДЕНИЯ ТРАНЗАКЦИИ
   */
  async waitForConfirmation(signature, timeout = 30000) {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      const status = await this.getTransactionStatus(signature);
      
      if (status.confirmed) {
        return {
          success: true,
          confirmed: true,
          finalized: status.finalized,
          error: status.error,
        };
      }
      
      if (status.error) {
        return {
          success: false,
          confirmed: false,
          finalized: false,
          error: status.error,
        };
      }
      
      // Ждем 1 секунду перед следующей проверкой
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    return {
      success: false,
      confirmed: false,
      finalized: false,
      error: 'Timeout waiting for confirmation',
    };
  }

  /**
   * 🔥 БЫСТРАЯ ОТПРАВКА С ПОДТВЕРЖДЕНИЕМ
   */
  async sendAndConfirm(transaction, options = {}) {
    const {
      timeout = 120000, // 🔥 УВЕЛИЧЕН TIMEOUT ДО 2 МИНУТ ДЛЯ МЕДЛЕННОЙ СЕТИ!
      skipPreflight = true,  // 🔥 НИЗКОУРОВНЕВЫЙ РЕЖИМ!
      maxRetries = 3,
    } = options;

    let lastError;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`📤 Попытка отправки ${attempt}/${maxRetries}...`);
        
        // Получаем свежий blockhash
        const { blockhash, lastValidBlockHeight } = await this.connection.getLatestBlockhash(this.commitment);
        
        // Переписываем транзакцию с новым blockhash
        const rewrittenTx = this.rewriteTransaction(transaction, this.wallet.publicKey, blockhash);
        
        // Подписываем
        await this.wallet.signTransaction(rewrittenTx);

        // Отправляем
        const signature = await this.connection.sendRawTransaction(rewrittenTx.serialize(), {
          skipPreflight,
          preflightCommitment: this.commitment,
        });

        console.log(`⚡ Транзакция отправлена: ${signature}`);
        
        // Ждем подтверждения
        const confirmation = await this.waitForConfirmation(signature, timeout);
        
        if (confirmation.success) {
          console.log(`✅ Транзакция подтверждена: ${signature}`);
          return {
            success: true,
            signature,
            confirmed: confirmation.confirmed,
            finalized: confirmation.finalized,
          };
        } else {
          lastError = confirmation.error;
          console.log(`❌ Транзакция не подтверждена: ${confirmation.error}`);
        }
        
      } catch (error) {
        lastError = error.message;
        console.log(`❌ Ошибка отправки (попытка ${attempt}): ${error.message}`);
        
        if (attempt < maxRetries) {
          // Ждем перед повторной попыткой
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
        }
      }
    }
    
    return {
      success: false,
      error: lastError || 'Все попытки отправки провалились',
      signature: null,
    };
  }
}

module.exports = TransactionProcessor;
