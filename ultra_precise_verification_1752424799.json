{"verification_date": "2025-07-14T00:39:59.354935", "total_verified": 45, "high_confidence_count": 3, "real_vulnerabilities_count": 3, "top_findings": [{"index": 16, "vulnerability_id": "37bb3520084fb2ca901ca234fe965c00", "target": "Polygon", "entropy": 4.822784810126583, "methods": {"entropy_math": {"score": 95, "entropy_value": 4.822784810126583, "is_mathematically_valid": true, "z_score": 3.4092827004219446, "statistical_significance": 95, "interpretation": "КРИТИЧЕСКИ ВЫСОКАЯ - аномальная сложность", "evidence": "Энтропия 4.822785, Z-score: 3.41"}, "contract_analysis": {"score": 90, "contracts_found": 1, "total_contracts": 1, "valid_format_count": 1, "evidence": "Найдено 1/3 реальных контрактов"}, "network_verification": {"score": 50, "accessible_endpoints": 1, "total_endpoints": 1, "evidence": "Доступно 1/1 endpoints"}, "pattern_analysis": {"score": 100, "pattern_matches": 4, "project_bonus": 20, "evidence": "Найдено 4 паттернов сложности"}, "statistical_analysis": {"score": 70, "z_score": 1.5507237323609935, "percentile": 95.55555555555556, "significance": "СРЕДНЯЯ", "evidence": "Z-score: 1.55, 95.6 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 3, "avg_target_entropy": 4.822784810126583, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 96, "assessment": "В ОЖИДАЕМОМ ДИАПАЗОНЕ", "expected_range": [4.2, 4.9], "complexity_reasons": ["Layer 2 scaling", "Bridge mechanisms", "Validator logic"], "credibility_multiplier": 1.2, "evidence": "В ОЖИДАЕМОМ ДИАПАЗОНЕ для Polygon (ожидается 4.2-4.9)"}}, "final_score": 84, "confidence_level": "ВЫСОКАЯ", "is_real_vulnerability": true, "evidence_strength": "УБЕДИТЕЛЬНЫЕ", "recommended_action": "ОТПРАВИТЬ ОТЧЕТ"}, {"index": 29, "vulnerability_id": "22a9c43255a6d2e86f28f1e859cb4f67", "target": "Polygon", "entropy": 4.822784810126583, "methods": {"entropy_math": {"score": 95, "entropy_value": 4.822784810126583, "is_mathematically_valid": true, "z_score": 3.4092827004219446, "statistical_significance": 95, "interpretation": "КРИТИЧЕСКИ ВЫСОКАЯ - аномальная сложность", "evidence": "Энтропия 4.822785, Z-score: 3.41"}, "contract_analysis": {"score": 90, "contracts_found": 1, "total_contracts": 1, "valid_format_count": 1, "evidence": "Найдено 1/3 реальных контрактов"}, "network_verification": {"score": 50, "accessible_endpoints": 1, "total_endpoints": 1, "evidence": "Доступно 1/1 endpoints"}, "pattern_analysis": {"score": 100, "pattern_matches": 4, "project_bonus": 20, "evidence": "Найдено 4 паттернов сложности"}, "statistical_analysis": {"score": 70, "z_score": 1.5507237323609935, "percentile": 95.55555555555556, "significance": "СРЕДНЯЯ", "evidence": "Z-score: 1.55, 95.6 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 3, "avg_target_entropy": 4.822784810126583, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 96, "assessment": "В ОЖИДАЕМОМ ДИАПАЗОНЕ", "expected_range": [4.2, 4.9], "complexity_reasons": ["Layer 2 scaling", "Bridge mechanisms", "Validator logic"], "credibility_multiplier": 1.2, "evidence": "В ОЖИДАЕМОМ ДИАПАЗОНЕ для Polygon (ожидается 4.2-4.9)"}}, "final_score": 84, "confidence_level": "ВЫСОКАЯ", "is_real_vulnerability": true, "evidence_strength": "УБЕДИТЕЛЬНЫЕ", "recommended_action": "ОТПРАВИТЬ ОТЧЕТ"}, {"index": 42, "vulnerability_id": "9b74ff06363610a48167c1a57cafd2e7", "target": "Polygon", "entropy": 4.822784810126583, "methods": {"entropy_math": {"score": 95, "entropy_value": 4.822784810126583, "is_mathematically_valid": true, "z_score": 3.4092827004219446, "statistical_significance": 95, "interpretation": "КРИТИЧЕСКИ ВЫСОКАЯ - аномальная сложность", "evidence": "Энтропия 4.822785, Z-score: 3.41"}, "contract_analysis": {"score": 90, "contracts_found": 1, "total_contracts": 1, "valid_format_count": 1, "evidence": "Найдено 1/3 реальных контрактов"}, "network_verification": {"score": 50, "accessible_endpoints": 1, "total_endpoints": 1, "evidence": "Доступно 1/1 endpoints"}, "pattern_analysis": {"score": 100, "pattern_matches": 4, "project_bonus": 20, "evidence": "Найдено 4 паттернов сложности"}, "statistical_analysis": {"score": 70, "z_score": 1.5507237323609935, "percentile": 95.55555555555556, "significance": "СРЕДНЯЯ", "evidence": "Z-score: 1.55, 95.6 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 3, "avg_target_entropy": 4.822784810126583, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 96, "assessment": "В ОЖИДАЕМОМ ДИАПАЗОНЕ", "expected_range": [4.2, 4.9], "complexity_reasons": ["Layer 2 scaling", "Bridge mechanisms", "Validator logic"], "credibility_multiplier": 1.2, "evidence": "В ОЖИДАЕМОМ ДИАПАЗОНЕ для Polygon (ожидается 4.2-4.9)"}}, "final_score": 84, "confidence_level": "ВЫСОКАЯ", "is_real_vulnerability": true, "evidence_strength": "УБЕДИТЕЛЬНЫЕ", "recommended_action": "ОТПРАВИТЬ ОТЧЕТ"}, {"index": 1, "vulnerability_id": "d6f775781b6b7945748597294ea3243d", "target": "Chainlink", "entropy": 4.654320987654319, "methods": {"entropy_math": {"score": 80, "entropy_value": 4.654320987654319, "is_mathematically_valid": true, "z_score": 2.847736625514396, "statistical_significance": 80, "interpretation": "ВЫСОКАЯ - подозрительная сложность", "evidence": "Энтропия 4.654321, Z-score: 2.85"}, "contract_analysis": {"score": 90, "contracts_found": 1, "total_contracts": 1, "valid_format_count": 1, "evidence": "Найдено 1/3 реальных контрактов"}, "network_verification": {"score": 30, "accessible_endpoints": 0, "total_endpoints": 1, "evidence": "Доступно 0/1 endpoints"}, "pattern_analysis": {"score": 55, "pattern_matches": 2, "project_bonus": 15, "evidence": "Найдено 2 паттернов сложности"}, "statistical_analysis": {"score": 40, "z_score": -0.2081903344817694, "percentile": 37.77777777777778, "significance": "НИЗКАЯ", "evidence": "Z-score: -0.21, 37.8 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 4, "avg_target_entropy": 4.654320987654319, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 88, "assessment": "В ОЖИДАЕМОМ ДИАПАЗОНЕ", "expected_range": [4.0, 4.7], "complexity_reasons": ["Oracle aggregation", "Price feeds", "Decentralized network"], "credibility_multiplier": 1.1, "evidence": "В ОЖИДАЕМОМ ДИАПАЗОНЕ для Chainlink (ожидается 4.0-4.7)"}}, "final_score": 67, "confidence_level": "СРЕДНЯЯ", "is_real_vulnerability": false, "evidence_strength": "СИЛЬНЫЕ", "recommended_action": "РАССМОТРЕТЬ ОТПРАВКУ"}, {"index": 2, "vulnerability_id": "1e4e75fb32194fb1162dbeaa0421006a", "target": "SushiSwap", "entropy": 4.687242798353906, "methods": {"entropy_math": {"score": 80, "entropy_value": 4.687242798353906, "is_mathematically_valid": true, "z_score": 2.957475994513021, "statistical_significance": 80, "interpretation": "ВЫСОКАЯ - подозрительная сложность", "evidence": "Энтропия 4.687243, Z-score: 2.96"}, "contract_analysis": {"score": 90, "contracts_found": 1, "total_contracts": 1, "valid_format_count": 1, "evidence": "Найдено 1/3 реальных контрактов"}, "network_verification": {"score": 50, "accessible_endpoints": 1, "total_endpoints": 1, "evidence": "Доступно 1/1 endpoints"}, "pattern_analysis": {"score": 40, "pattern_matches": 2, "project_bonus": 0, "evidence": "Найдено 2 паттернов сложности"}, "statistical_analysis": {"score": 40, "z_score": 0.13554302675650198, "percentile": 62.22222222222222, "significance": "НИЗКАЯ", "evidence": "Z-score: 0.14, 62.2 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 4, "avg_target_entropy": 4.687242798353906, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 81, "assessment": "ВЫШЕ ОЖИДАЕМОГО", "expected_range": [3.8, 4.4], "complexity_reasons": ["Standard AMM", "Well-audited"], "credibility_multiplier": 0.9, "evidence": "ВЫШЕ ОЖИДАЕМОГО для SushiSwap (ожидается 3.8-4.4)"}}, "final_score": 67, "confidence_level": "СРЕДНЯЯ", "is_real_vulnerability": false, "evidence_strength": "СИЛЬНЫЕ", "recommended_action": "РАССМОТРЕТЬ ОТПРАВКУ"}, {"index": 7, "vulnerability_id": "777d3f134d38c2ead65773e2d50c7c02", "target": "Chainlink", "entropy": 4.654320987654319, "methods": {"entropy_math": {"score": 80, "entropy_value": 4.654320987654319, "is_mathematically_valid": true, "z_score": 2.847736625514396, "statistical_significance": 80, "interpretation": "ВЫСОКАЯ - подозрительная сложность", "evidence": "Энтропия 4.654321, Z-score: 2.85"}, "contract_analysis": {"score": 90, "contracts_found": 1, "total_contracts": 1, "valid_format_count": 1, "evidence": "Найдено 1/3 реальных контрактов"}, "network_verification": {"score": 30, "accessible_endpoints": 0, "total_endpoints": 1, "evidence": "Доступно 0/1 endpoints"}, "pattern_analysis": {"score": 55, "pattern_matches": 2, "project_bonus": 15, "evidence": "Найдено 2 паттернов сложности"}, "statistical_analysis": {"score": 40, "z_score": -0.2081903344817694, "percentile": 37.77777777777778, "significance": "НИЗКАЯ", "evidence": "Z-score: -0.21, 37.8 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 4, "avg_target_entropy": 4.654320987654319, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 88, "assessment": "В ОЖИДАЕМОМ ДИАПАЗОНЕ", "expected_range": [4.0, 4.7], "complexity_reasons": ["Oracle aggregation", "Price feeds", "Decentralized network"], "credibility_multiplier": 1.1, "evidence": "В ОЖИДАЕМОМ ДИАПАЗОНЕ для Chainlink (ожидается 4.0-4.7)"}}, "final_score": 67, "confidence_level": "СРЕДНЯЯ", "is_real_vulnerability": false, "evidence_strength": "СИЛЬНЫЕ", "recommended_action": "РАССМОТРЕТЬ ОТПРАВКУ"}, {"index": 8, "vulnerability_id": "f815ebb7ed83a2167c7c9ae81d7edc70", "target": "SushiSwap", "entropy": 4.687242798353906, "methods": {"entropy_math": {"score": 80, "entropy_value": 4.687242798353906, "is_mathematically_valid": true, "z_score": 2.957475994513021, "statistical_significance": 80, "interpretation": "ВЫСОКАЯ - подозрительная сложность", "evidence": "Энтропия 4.687243, Z-score: 2.96"}, "contract_analysis": {"score": 90, "contracts_found": 1, "total_contracts": 1, "valid_format_count": 1, "evidence": "Найдено 1/3 реальных контрактов"}, "network_verification": {"score": 50, "accessible_endpoints": 1, "total_endpoints": 1, "evidence": "Доступно 1/1 endpoints"}, "pattern_analysis": {"score": 40, "pattern_matches": 2, "project_bonus": 0, "evidence": "Найдено 2 паттернов сложности"}, "statistical_analysis": {"score": 40, "z_score": 0.13554302675650198, "percentile": 62.22222222222222, "significance": "НИЗКАЯ", "evidence": "Z-score: 0.14, 62.2 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 4, "avg_target_entropy": 4.687242798353906, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 81, "assessment": "ВЫШЕ ОЖИДАЕМОГО", "expected_range": [3.8, 4.4], "complexity_reasons": ["Standard AMM", "Well-audited"], "credibility_multiplier": 0.9, "evidence": "ВЫШЕ ОЖИДАЕМОГО для SushiSwap (ожидается 3.8-4.4)"}}, "final_score": 67, "confidence_level": "СРЕДНЯЯ", "is_real_vulnerability": false, "evidence_strength": "СИЛЬНЫЕ", "recommended_action": "РАССМОТРЕТЬ ОТПРАВКУ"}, {"index": 20, "vulnerability_id": "37dde28c3dc168502fe1a50eb4f40f97", "target": "Chainlink", "entropy": 4.654320987654319, "methods": {"entropy_math": {"score": 80, "entropy_value": 4.654320987654319, "is_mathematically_valid": true, "z_score": 2.847736625514396, "statistical_significance": 80, "interpretation": "ВЫСОКАЯ - подозрительная сложность", "evidence": "Энтропия 4.654321, Z-score: 2.85"}, "contract_analysis": {"score": 90, "contracts_found": 1, "total_contracts": 1, "valid_format_count": 1, "evidence": "Найдено 1/3 реальных контрактов"}, "network_verification": {"score": 30, "accessible_endpoints": 0, "total_endpoints": 1, "evidence": "Доступно 0/1 endpoints"}, "pattern_analysis": {"score": 55, "pattern_matches": 2, "project_bonus": 15, "evidence": "Найдено 2 паттернов сложности"}, "statistical_analysis": {"score": 40, "z_score": -0.2081903344817694, "percentile": 37.77777777777778, "significance": "НИЗКАЯ", "evidence": "Z-score: -0.21, 37.8 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 4, "avg_target_entropy": 4.654320987654319, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 88, "assessment": "В ОЖИДАЕМОМ ДИАПАЗОНЕ", "expected_range": [4.0, 4.7], "complexity_reasons": ["Oracle aggregation", "Price feeds", "Decentralized network"], "credibility_multiplier": 1.1, "evidence": "В ОЖИДАЕМОМ ДИАПАЗОНЕ для Chainlink (ожидается 4.0-4.7)"}}, "final_score": 67, "confidence_level": "СРЕДНЯЯ", "is_real_vulnerability": false, "evidence_strength": "СИЛЬНЫЕ", "recommended_action": "РАССМОТРЕТЬ ОТПРАВКУ"}, {"index": 21, "vulnerability_id": "734000eb49e578becb1ea8b2a2dc8f4e", "target": "SushiSwap", "entropy": 4.687242798353906, "methods": {"entropy_math": {"score": 80, "entropy_value": 4.687242798353906, "is_mathematically_valid": true, "z_score": 2.957475994513021, "statistical_significance": 80, "interpretation": "ВЫСОКАЯ - подозрительная сложность", "evidence": "Энтропия 4.687243, Z-score: 2.96"}, "contract_analysis": {"score": 90, "contracts_found": 1, "total_contracts": 1, "valid_format_count": 1, "evidence": "Найдено 1/3 реальных контрактов"}, "network_verification": {"score": 50, "accessible_endpoints": 1, "total_endpoints": 1, "evidence": "Доступно 1/1 endpoints"}, "pattern_analysis": {"score": 40, "pattern_matches": 2, "project_bonus": 0, "evidence": "Найдено 2 паттернов сложности"}, "statistical_analysis": {"score": 40, "z_score": 0.13554302675650198, "percentile": 62.22222222222222, "significance": "НИЗКАЯ", "evidence": "Z-score: 0.14, 62.2 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 4, "avg_target_entropy": 4.687242798353906, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 81, "assessment": "ВЫШЕ ОЖИДАЕМОГО", "expected_range": [3.8, 4.4], "complexity_reasons": ["Standard AMM", "Well-audited"], "credibility_multiplier": 0.9, "evidence": "ВЫШЕ ОЖИДАЕМОГО для SushiSwap (ожидается 3.8-4.4)"}}, "final_score": 67, "confidence_level": "СРЕДНЯЯ", "is_real_vulnerability": false, "evidence_strength": "СИЛЬНЫЕ", "recommended_action": "РАССМОТРЕТЬ ОТПРАВКУ"}, {"index": 33, "vulnerability_id": "4fb6cece61b01365473ed42f3d8b258e", "target": "Chainlink", "entropy": 4.654320987654319, "methods": {"entropy_math": {"score": 80, "entropy_value": 4.654320987654319, "is_mathematically_valid": true, "z_score": 2.847736625514396, "statistical_significance": 80, "interpretation": "ВЫСОКАЯ - подозрительная сложность", "evidence": "Энтропия 4.654321, Z-score: 2.85"}, "contract_analysis": {"score": 90, "contracts_found": 1, "total_contracts": 1, "valid_format_count": 1, "evidence": "Найдено 1/3 реальных контрактов"}, "network_verification": {"score": 30, "accessible_endpoints": 0, "total_endpoints": 1, "evidence": "Доступно 0/1 endpoints"}, "pattern_analysis": {"score": 55, "pattern_matches": 2, "project_bonus": 15, "evidence": "Найдено 2 паттернов сложности"}, "statistical_analysis": {"score": 40, "z_score": -0.2081903344817694, "percentile": 37.77777777777778, "significance": "НИЗКАЯ", "evidence": "Z-score: -0.21, 37.8 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 4, "avg_target_entropy": 4.654320987654319, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 88, "assessment": "В ОЖИДАЕМОМ ДИАПАЗОНЕ", "expected_range": [4.0, 4.7], "complexity_reasons": ["Oracle aggregation", "Price feeds", "Decentralized network"], "credibility_multiplier": 1.1, "evidence": "В ОЖИДАЕМОМ ДИАПАЗОНЕ для Chainlink (ожидается 4.0-4.7)"}}, "final_score": 67, "confidence_level": "СРЕДНЯЯ", "is_real_vulnerability": false, "evidence_strength": "СИЛЬНЫЕ", "recommended_action": "РАССМОТРЕТЬ ОТПРАВКУ"}], "send_immediately": ["Polygon", "Polygon", "Polygon"], "consider_sending": ["Chainlink", "SushiSwap", "Chainlink", "SushiSwap", "Chainlink", "SushiSwap", "Chainlink", "SushiSwap"], "detailed_results": [{"index": 1, "vulnerability_id": "d6f775781b6b7945748597294ea3243d", "target": "Chainlink", "entropy": 4.654320987654319, "methods": {"entropy_math": {"score": 80, "entropy_value": 4.654320987654319, "is_mathematically_valid": true, "z_score": 2.847736625514396, "statistical_significance": 80, "interpretation": "ВЫСОКАЯ - подозрительная сложность", "evidence": "Энтропия 4.654321, Z-score: 2.85"}, "contract_analysis": {"score": 90, "contracts_found": 1, "total_contracts": 1, "valid_format_count": 1, "evidence": "Найдено 1/3 реальных контрактов"}, "network_verification": {"score": 30, "accessible_endpoints": 0, "total_endpoints": 1, "evidence": "Доступно 0/1 endpoints"}, "pattern_analysis": {"score": 55, "pattern_matches": 2, "project_bonus": 15, "evidence": "Найдено 2 паттернов сложности"}, "statistical_analysis": {"score": 40, "z_score": -0.2081903344817694, "percentile": 37.77777777777778, "significance": "НИЗКАЯ", "evidence": "Z-score: -0.21, 37.8 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 4, "avg_target_entropy": 4.654320987654319, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 88, "assessment": "В ОЖИДАЕМОМ ДИАПАЗОНЕ", "expected_range": [4.0, 4.7], "complexity_reasons": ["Oracle aggregation", "Price feeds", "Decentralized network"], "credibility_multiplier": 1.1, "evidence": "В ОЖИДАЕМОМ ДИАПАЗОНЕ для Chainlink (ожидается 4.0-4.7)"}}, "final_score": 67, "confidence_level": "СРЕДНЯЯ", "is_real_vulnerability": false, "evidence_strength": "СИЛЬНЫЕ", "recommended_action": "РАССМОТРЕТЬ ОТПРАВКУ"}, {"index": 2, "vulnerability_id": "1e4e75fb32194fb1162dbeaa0421006a", "target": "SushiSwap", "entropy": 4.687242798353906, "methods": {"entropy_math": {"score": 80, "entropy_value": 4.687242798353906, "is_mathematically_valid": true, "z_score": 2.957475994513021, "statistical_significance": 80, "interpretation": "ВЫСОКАЯ - подозрительная сложность", "evidence": "Энтропия 4.687243, Z-score: 2.96"}, "contract_analysis": {"score": 90, "contracts_found": 1, "total_contracts": 1, "valid_format_count": 1, "evidence": "Найдено 1/3 реальных контрактов"}, "network_verification": {"score": 50, "accessible_endpoints": 1, "total_endpoints": 1, "evidence": "Доступно 1/1 endpoints"}, "pattern_analysis": {"score": 40, "pattern_matches": 2, "project_bonus": 0, "evidence": "Найдено 2 паттернов сложности"}, "statistical_analysis": {"score": 40, "z_score": 0.13554302675650198, "percentile": 62.22222222222222, "significance": "НИЗКАЯ", "evidence": "Z-score: 0.14, 62.2 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 4, "avg_target_entropy": 4.687242798353906, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 81, "assessment": "ВЫШЕ ОЖИДАЕМОГО", "expected_range": [3.8, 4.4], "complexity_reasons": ["Standard AMM", "Well-audited"], "credibility_multiplier": 0.9, "evidence": "ВЫШЕ ОЖИДАЕМОГО для SushiSwap (ожидается 3.8-4.4)"}}, "final_score": 67, "confidence_level": "СРЕДНЯЯ", "is_real_vulnerability": false, "evidence_strength": "СИЛЬНЫЕ", "recommended_action": "РАССМОТРЕТЬ ОТПРАВКУ"}, {"index": 3, "vulnerability_id": "d90b8c7eb5f691a030f006a25bb0564b", "target": "PancakeSwap", "entropy": 4.793248945147679, "methods": {"entropy_math": {"score": 80, "entropy_value": 4.793248945147679, "is_mathematically_valid": true, "z_score": 3.310829817158932, "statistical_significance": 80, "interpretation": "ВЫСОКАЯ - подозрительная сложность", "evidence": "Энтропия 4.793249, Z-score: 3.31"}, "contract_analysis": {"score": 0, "contracts_found": 0, "total_contracts": 1, "evidence": "Проект PancakeSwap не найден в базе реальных контрактов"}, "network_verification": {"score": 50, "accessible_endpoints": 1, "total_endpoints": 1, "evidence": "Доступно 1/1 endpoints"}, "pattern_analysis": {"score": 60, "pattern_matches": 3, "project_bonus": 0, "evidence": "Найдено 3 паттернов сложности"}, "statistical_analysis": {"score": 40, "z_score": 1.24234269466778, "percentile": 77.77777777777779, "significance": "НИЗКАЯ", "evidence": "Z-score: 1.24, 77.8 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 4, "avg_target_entropy": 4.793248945147679, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 40, "evidence": "Нет экспертных знаний о проекте PancakeSwap"}}, "final_score": 50, "confidence_level": "НИЗКАЯ", "is_real_vulnerability": false, "evidence_strength": "УМЕРЕННЫЕ", "recommended_action": "ДОПОЛНИТЕЛЬНАЯ ПРОВЕРКА"}, {"index": 4, "vulnerability_id": "faad5955017b013351f3e4e619bce088", "target": "GMX", "entropy": 4.816143497757849, "methods": {"entropy_math": {"score": 95, "entropy_value": 4.816143497757849, "is_mathematically_valid": true, "z_score": 3.3871449925261627, "statistical_significance": 95, "interpretation": "КРИТИЧЕСКИ ВЫСОКАЯ - аномальная сложность", "evidence": "Энтропия 4.816143, Z-score: 3.39"}, "contract_analysis": {"score": 0, "contracts_found": 0, "total_contracts": 1, "evidence": "Проект GMX не найден в базе реальных контрактов"}, "network_verification": {"score": 30, "accessible_endpoints": 0, "total_endpoints": 1, "evidence": "Доступно 0/1 endpoints"}, "pattern_analysis": {"score": 80, "pattern_matches": 4, "project_bonus": 0, "evidence": "Найдено 4 паттернов сложности"}, "statistical_analysis": {"score": 40, "z_score": 1.4813824484210114, "percentile": 86.66666666666667, "significance": "НИЗКАЯ", "evidence": "Z-score: 1.48, 86.7 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 4, "avg_target_entropy": 4.816143497757849, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 90, "assessment": "ВЫШЕ ОЖИДАЕМОГО", "expected_range": [4.1, 4.8], "complexity_reasons": ["Perpetual trading", "Liquidity pools", "Price impact"], "credibility_multiplier": 1.0, "evidence": "ВЫШЕ ОЖИДАЕМОГО для GMX (ожидается 4.1-4.8)"}}, "final_score": 56, "confidence_level": "СРЕДНЯЯ", "is_real_vulnerability": false, "evidence_strength": "УМЕРЕННЫЕ", "recommended_action": "ДОПОЛНИТЕЛЬНАЯ ПРОВЕРКА"}, {"index": 5, "vulnerability_id": "79c8bab7e22bb799862d41ce590532a5", "target": "Trader <PERSON>", "entropy": 4.597402597402597, "methods": {"entropy_math": {"score": 80, "entropy_value": 4.597402597402597, "is_mathematically_valid": true, "z_score": 2.658008658008659, "statistical_significance": 80, "interpretation": "ВЫСОКАЯ - подозрительная сложность", "evidence": "Энтропия 4.597403, Z-score: 2.66"}, "contract_analysis": {"score": 0, "contracts_found": 0, "total_contracts": 1, "evidence": "Проект Trader Joe не найден в базе реальных контрактов"}, "network_verification": {"score": 0, "accessible_endpoints": 0, "total_endpoints": 1, "evidence": "Доступно 0/1 endpoints"}, "pattern_analysis": {"score": 40, "pattern_matches": 2, "project_bonus": 0, "evidence": "Найдено 2 паттернов сложности"}, "statistical_analysis": {"score": 40, "z_score": -0.8024696035057225, "percentile": 15.555555555555555, "significance": "НИЗКАЯ", "evidence": "Z-score: -0.80, 15.6 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 4, "avg_target_entropy": 4.597402597402597, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 40, "evidence": "Нет экспертных знаний о проекте Trader Joe"}}, "final_score": 40, "confidence_level": "НИЗКАЯ", "is_real_vulnerability": false, "evidence_strength": "СЛАБЫЕ", "recommended_action": "НЕ ОТПРАВЛЯТЬ"}, {"index": 6, "vulnerability_id": "1214c421cd1d71149a086ae66f1708ca", "target": "El<PERSON><PERSON>", "entropy": 4.6651583710407225, "methods": {"entropy_math": {"score": 80, "entropy_value": 4.6651583710407225, "is_mathematically_valid": true, "z_score": 2.8838612368024092, "statistical_significance": 80, "interpretation": "ВЫСОКАЯ - подозрительная сложность", "evidence": "Энтропия 4.665158, Z-score: 2.88"}, "contract_analysis": {"score": 0, "contracts_found": 0, "total_contracts": 1, "evidence": "Проект Ellipsis не найден в базе реальных контрактов"}, "network_verification": {"score": 0, "accessible_endpoints": 0, "total_endpoints": 1, "evidence": "Доступно 0/1 endpoints"}, "pattern_analysis": {"score": 40, "pattern_matches": 2, "project_bonus": 0, "evidence": "Найдено 2 паттернов сложности"}, "statistical_analysis": {"score": 40, "z_score": -0.09503828909675936, "percentile": 46.666666666666664, "significance": "НИЗКАЯ", "evidence": "Z-score: -0.10, 46.7 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 4, "avg_target_entropy": 4.6651583710407225, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 40, "evidence": "Нет экспертных знаний о проекте Ellipsis"}}, "final_score": 40, "confidence_level": "НИЗКАЯ", "is_real_vulnerability": false, "evidence_strength": "СЛАБЫЕ", "recommended_action": "НЕ ОТПРАВЛЯТЬ"}, {"index": 7, "vulnerability_id": "777d3f134d38c2ead65773e2d50c7c02", "target": "Chainlink", "entropy": 4.654320987654319, "methods": {"entropy_math": {"score": 80, "entropy_value": 4.654320987654319, "is_mathematically_valid": true, "z_score": 2.847736625514396, "statistical_significance": 80, "interpretation": "ВЫСОКАЯ - подозрительная сложность", "evidence": "Энтропия 4.654321, Z-score: 2.85"}, "contract_analysis": {"score": 90, "contracts_found": 1, "total_contracts": 1, "valid_format_count": 1, "evidence": "Найдено 1/3 реальных контрактов"}, "network_verification": {"score": 30, "accessible_endpoints": 0, "total_endpoints": 1, "evidence": "Доступно 0/1 endpoints"}, "pattern_analysis": {"score": 55, "pattern_matches": 2, "project_bonus": 15, "evidence": "Найдено 2 паттернов сложности"}, "statistical_analysis": {"score": 40, "z_score": -0.2081903344817694, "percentile": 37.77777777777778, "significance": "НИЗКАЯ", "evidence": "Z-score: -0.21, 37.8 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 4, "avg_target_entropy": 4.654320987654319, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 88, "assessment": "В ОЖИДАЕМОМ ДИАПАЗОНЕ", "expected_range": [4.0, 4.7], "complexity_reasons": ["Oracle aggregation", "Price feeds", "Decentralized network"], "credibility_multiplier": 1.1, "evidence": "В ОЖИДАЕМОМ ДИАПАЗОНЕ для Chainlink (ожидается 4.0-4.7)"}}, "final_score": 67, "confidence_level": "СРЕДНЯЯ", "is_real_vulnerability": false, "evidence_strength": "СИЛЬНЫЕ", "recommended_action": "РАССМОТРЕТЬ ОТПРАВКУ"}, {"index": 8, "vulnerability_id": "f815ebb7ed83a2167c7c9ae81d7edc70", "target": "SushiSwap", "entropy": 4.687242798353906, "methods": {"entropy_math": {"score": 80, "entropy_value": 4.687242798353906, "is_mathematically_valid": true, "z_score": 2.957475994513021, "statistical_significance": 80, "interpretation": "ВЫСОКАЯ - подозрительная сложность", "evidence": "Энтропия 4.687243, Z-score: 2.96"}, "contract_analysis": {"score": 90, "contracts_found": 1, "total_contracts": 1, "valid_format_count": 1, "evidence": "Найдено 1/3 реальных контрактов"}, "network_verification": {"score": 50, "accessible_endpoints": 1, "total_endpoints": 1, "evidence": "Доступно 1/1 endpoints"}, "pattern_analysis": {"score": 40, "pattern_matches": 2, "project_bonus": 0, "evidence": "Найдено 2 паттернов сложности"}, "statistical_analysis": {"score": 40, "z_score": 0.13554302675650198, "percentile": 62.22222222222222, "significance": "НИЗКАЯ", "evidence": "Z-score: 0.14, 62.2 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 4, "avg_target_entropy": 4.687242798353906, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 81, "assessment": "ВЫШЕ ОЖИДАЕМОГО", "expected_range": [3.8, 4.4], "complexity_reasons": ["Standard AMM", "Well-audited"], "credibility_multiplier": 0.9, "evidence": "ВЫШЕ ОЖИДАЕМОГО для SushiSwap (ожидается 3.8-4.4)"}}, "final_score": 67, "confidence_level": "СРЕДНЯЯ", "is_real_vulnerability": false, "evidence_strength": "СИЛЬНЫЕ", "recommended_action": "РАССМОТРЕТЬ ОТПРАВКУ"}, {"index": 9, "vulnerability_id": "c4fb701b2333059061be60477b512cb5", "target": "PancakeSwap", "entropy": 4.793248945147679, "methods": {"entropy_math": {"score": 80, "entropy_value": 4.793248945147679, "is_mathematically_valid": true, "z_score": 3.310829817158932, "statistical_significance": 80, "interpretation": "ВЫСОКАЯ - подозрительная сложность", "evidence": "Энтропия 4.793249, Z-score: 3.31"}, "contract_analysis": {"score": 0, "contracts_found": 0, "total_contracts": 1, "evidence": "Проект PancakeSwap не найден в базе реальных контрактов"}, "network_verification": {"score": 50, "accessible_endpoints": 1, "total_endpoints": 1, "evidence": "Доступно 1/1 endpoints"}, "pattern_analysis": {"score": 60, "pattern_matches": 3, "project_bonus": 0, "evidence": "Найдено 3 паттернов сложности"}, "statistical_analysis": {"score": 40, "z_score": 1.24234269466778, "percentile": 77.77777777777779, "significance": "НИЗКАЯ", "evidence": "Z-score: 1.24, 77.8 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 4, "avg_target_entropy": 4.793248945147679, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 40, "evidence": "Нет экспертных знаний о проекте PancakeSwap"}}, "final_score": 50, "confidence_level": "НИЗКАЯ", "is_real_vulnerability": false, "evidence_strength": "УМЕРЕННЫЕ", "recommended_action": "ДОПОЛНИТЕЛЬНАЯ ПРОВЕРКА"}, {"index": 10, "vulnerability_id": "********************************", "target": "Trader <PERSON>", "entropy": 4.597402597402597, "methods": {"entropy_math": {"score": 80, "entropy_value": 4.597402597402597, "is_mathematically_valid": true, "z_score": 2.658008658008659, "statistical_significance": 80, "interpretation": "ВЫСОКАЯ - подозрительная сложность", "evidence": "Энтропия 4.597403, Z-score: 2.66"}, "contract_analysis": {"score": 0, "contracts_found": 0, "total_contracts": 1, "evidence": "Проект Trader Joe не найден в базе реальных контрактов"}, "network_verification": {"score": 0, "accessible_endpoints": 0, "total_endpoints": 1, "evidence": "Доступно 0/1 endpoints"}, "pattern_analysis": {"score": 40, "pattern_matches": 2, "project_bonus": 0, "evidence": "Найдено 2 паттернов сложности"}, "statistical_analysis": {"score": 40, "z_score": -0.8024696035057225, "percentile": 15.555555555555555, "significance": "НИЗКАЯ", "evidence": "Z-score: -0.80, 15.6 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 4, "avg_target_entropy": 4.597402597402597, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 40, "evidence": "Нет экспертных знаний о проекте Trader Joe"}}, "final_score": 40, "confidence_level": "НИЗКАЯ", "is_real_vulnerability": false, "evidence_strength": "СЛАБЫЕ", "recommended_action": "НЕ ОТПРАВЛЯТЬ"}, {"index": 11, "vulnerability_id": "d1da05e8b22dd0972df9e6838125406e", "target": "GMX", "entropy": 4.816143497757849, "methods": {"entropy_math": {"score": 95, "entropy_value": 4.816143497757849, "is_mathematically_valid": true, "z_score": 3.3871449925261627, "statistical_significance": 95, "interpretation": "КРИТИЧЕСКИ ВЫСОКАЯ - аномальная сложность", "evidence": "Энтропия 4.816143, Z-score: 3.39"}, "contract_analysis": {"score": 0, "contracts_found": 0, "total_contracts": 1, "evidence": "Проект GMX не найден в базе реальных контрактов"}, "network_verification": {"score": 30, "accessible_endpoints": 0, "total_endpoints": 1, "evidence": "Доступно 0/1 endpoints"}, "pattern_analysis": {"score": 80, "pattern_matches": 4, "project_bonus": 0, "evidence": "Найдено 4 паттернов сложности"}, "statistical_analysis": {"score": 40, "z_score": 1.4813824484210114, "percentile": 86.66666666666667, "significance": "НИЗКАЯ", "evidence": "Z-score: 1.48, 86.7 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 4, "avg_target_entropy": 4.816143497757849, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 90, "assessment": "ВЫШЕ ОЖИДАЕМОГО", "expected_range": [4.1, 4.8], "complexity_reasons": ["Perpetual trading", "Liquidity pools", "Price impact"], "credibility_multiplier": 1.0, "evidence": "ВЫШЕ ОЖИДАЕМОГО для GMX (ожидается 4.1-4.8)"}}, "final_score": 56, "confidence_level": "СРЕДНЯЯ", "is_real_vulnerability": false, "evidence_strength": "УМЕРЕННЫЕ", "recommended_action": "ДОПОЛНИТЕЛЬНАЯ ПРОВЕРКА"}, {"index": 12, "vulnerability_id": "2bba41dddf6312cd9cbd3a02c2cd29c0", "target": "Venus", "entropy": 4.4862385321100895, "methods": {"entropy_math": {"score": 60, "entropy_value": 4.4862385321100895, "is_mathematically_valid": true, "z_score": 2.287461773700299, "statistical_significance": 60, "interpretation": "ПОВЫШЕННАЯ - требует внимания", "evidence": "Энтропия 4.486239, Z-score: 2.29"}, "contract_analysis": {"score": 0, "contracts_found": 0, "total_contracts": 1, "evidence": "Проект Venus не найден в базе реальных контрактов"}, "network_verification": {"score": 0, "accessible_endpoints": 0, "total_endpoints": 1, "evidence": "Доступно 0/1 endpoints"}, "pattern_analysis": {"score": 20, "pattern_matches": 1, "project_bonus": 0, "evidence": "Найдено 1 паттернов сложности"}, "statistical_analysis": {"score": 70, "z_score": -1.9631225870423552, "percentile": 2.2222222222222223, "significance": "СРЕДНЯЯ", "evidence": "Z-score: -1.96, 2.2 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 3, "avg_target_entropy": 4.4862385321100895, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 40, "evidence": "Нет экспертных знаний о проекте Venus"}}, "final_score": 35, "confidence_level": "ОЧЕНЬ НИЗКАЯ", "is_real_vulnerability": false, "evidence_strength": "СЛАБЫЕ", "recommended_action": "НЕ ОТПРАВЛЯТЬ"}, {"index": 13, "vulnerability_id": "d318eef17812c3471526a69bcbfa3329", "target": "Alpaca Finance", "entropy": 4.629955947136561, "methods": {"entropy_math": {"score": 80, "entropy_value": 4.629955947136561, "is_mathematically_valid": true, "z_score": 2.7665198237885376, "statistical_significance": 80, "interpretation": "ВЫСОКАЯ - подозрительная сложность", "evidence": "Энтропия 4.629956, Z-score: 2.77"}, "contract_analysis": {"score": 0, "contracts_found": 0, "total_contracts": 1, "evidence": "Проект Alpaca Finance не найден в базе реальных контрактов"}, "network_verification": {"score": 0, "accessible_endpoints": 0, "total_endpoints": 1, "evidence": "Доступно 0/1 endpoints"}, "pattern_analysis": {"score": 40, "pattern_matches": 2, "project_bonus": 0, "evidence": "Найдено 2 паттернов сложности"}, "statistical_analysis": {"score": 40, "z_score": -0.4625833066757451, "percentile": 31.11111111111111, "significance": "НИЗКАЯ", "evidence": "Z-score: -0.46, 31.1 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 3, "avg_target_entropy": 4.629955947136561, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 40, "evidence": "Нет экспертных знаний о проекте Alpaca Finance"}}, "final_score": 40, "confidence_level": "НИЗКАЯ", "is_real_vulnerability": false, "evidence_strength": "СЛАБЫЕ", "recommended_action": "НЕ ОТПРАВЛЯТЬ"}, {"index": 14, "vulnerability_id": "bdadbccd4ef219cf0ad9ee9976c58ce7", "target": "Biswap", "entropy": 4.716894977168951, "methods": {"entropy_math": {"score": 80, "entropy_value": 4.716894977168951, "is_mathematically_valid": true, "z_score": 3.0563165905631706, "statistical_significance": 80, "interpretation": "ВЫСОКАЯ - подозрительная сложность", "evidence": "Энтропия 4.716895, Z-score: 3.06"}, "contract_analysis": {"score": 0, "contracts_found": 0, "total_contracts": 1, "evidence": "Проект Biswap не найден в базе реальных контрактов"}, "network_verification": {"score": 0, "accessible_endpoints": 0, "total_endpoints": 1, "evidence": "Доступно 0/1 endpoints"}, "pattern_analysis": {"score": 60, "pattern_matches": 3, "project_bonus": 0, "evidence": "Найдено 3 паттернов сложности"}, "statistical_analysis": {"score": 40, "z_score": 0.44513848568007236, "percentile": 71.11111111111111, "significance": "НИЗКАЯ", "evidence": "Z-score: 0.45, 71.1 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 3, "avg_target_entropy": 4.716894977168951, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 40, "evidence": "Нет экспертных знаний о проекте Biswap"}}, "final_score": 43, "confidence_level": "НИЗКАЯ", "is_real_vulnerability": false, "evidence_strength": "СЛАБЫЕ", "recommended_action": "НЕ ОТПРАВЛЯТЬ"}, {"index": 15, "vulnerability_id": "b93018bdb5abfcd45953f0cb32ee942f", "target": "El<PERSON><PERSON>", "entropy": 4.6651583710407225, "methods": {"entropy_math": {"score": 80, "entropy_value": 4.6651583710407225, "is_mathematically_valid": true, "z_score": 2.8838612368024092, "statistical_significance": 80, "interpretation": "ВЫСОКАЯ - подозрительная сложность", "evidence": "Энтропия 4.665158, Z-score: 2.88"}, "contract_analysis": {"score": 0, "contracts_found": 0, "total_contracts": 1, "evidence": "Проект Ellipsis не найден в базе реальных контрактов"}, "network_verification": {"score": 0, "accessible_endpoints": 0, "total_endpoints": 1, "evidence": "Доступно 0/1 endpoints"}, "pattern_analysis": {"score": 40, "pattern_matches": 2, "project_bonus": 0, "evidence": "Найдено 2 паттернов сложности"}, "statistical_analysis": {"score": 40, "z_score": -0.09503828909675936, "percentile": 46.666666666666664, "significance": "НИЗКАЯ", "evidence": "Z-score: -0.10, 46.7 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 4, "avg_target_entropy": 4.6651583710407225, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 40, "evidence": "Нет экспертных знаний о проекте Ellipsis"}}, "final_score": 40, "confidence_level": "НИЗКАЯ", "is_real_vulnerability": false, "evidence_strength": "СЛАБЫЕ", "recommended_action": "НЕ ОТПРАВЛЯТЬ"}, {"index": 16, "vulnerability_id": "37bb3520084fb2ca901ca234fe965c00", "target": "Polygon", "entropy": 4.822784810126583, "methods": {"entropy_math": {"score": 95, "entropy_value": 4.822784810126583, "is_mathematically_valid": true, "z_score": 3.4092827004219446, "statistical_significance": 95, "interpretation": "КРИТИЧЕСКИ ВЫСОКАЯ - аномальная сложность", "evidence": "Энтропия 4.822785, Z-score: 3.41"}, "contract_analysis": {"score": 90, "contracts_found": 1, "total_contracts": 1, "valid_format_count": 1, "evidence": "Найдено 1/3 реальных контрактов"}, "network_verification": {"score": 50, "accessible_endpoints": 1, "total_endpoints": 1, "evidence": "Доступно 1/1 endpoints"}, "pattern_analysis": {"score": 100, "pattern_matches": 4, "project_bonus": 20, "evidence": "Найдено 4 паттернов сложности"}, "statistical_analysis": {"score": 70, "z_score": 1.5507237323609935, "percentile": 95.55555555555556, "significance": "СРЕДНЯЯ", "evidence": "Z-score: 1.55, 95.6 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 3, "avg_target_entropy": 4.822784810126583, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 96, "assessment": "В ОЖИДАЕМОМ ДИАПАЗОНЕ", "expected_range": [4.2, 4.9], "complexity_reasons": ["Layer 2 scaling", "Bridge mechanisms", "Validator logic"], "credibility_multiplier": 1.2, "evidence": "В ОЖИДАЕМОМ ДИАПАЗОНЕ для Polygon (ожидается 4.2-4.9)"}}, "final_score": 84, "confidence_level": "ВЫСОКАЯ", "is_real_vulnerability": true, "evidence_strength": "УБЕДИТЕЛЬНЫЕ", "recommended_action": "ОТПРАВИТЬ ОТЧЕТ"}, {"index": 17, "vulnerability_id": "a65febf1bc077e9adee08741e95bf8c2", "target": "Lyra", "entropy": 4.612612612612613, "methods": {"entropy_math": {"score": 80, "entropy_value": 4.612612612612613, "is_mathematically_valid": true, "z_score": 2.708708708708709, "statistical_significance": 80, "interpretation": "ВЫСОКАЯ - подозрительная сложность", "evidence": "Энтропия 4.612613, Z-score: 2.71"}, "contract_analysis": {"score": 0, "contracts_found": 0, "total_contracts": 1, "evidence": "Проект Lyra не найден в базе реальных контрактов"}, "network_verification": {"score": 20, "accessible_endpoints": 1, "total_endpoints": 1, "evidence": "Доступно 1/1 endpoints"}, "pattern_analysis": {"score": 40, "pattern_matches": 2, "project_bonus": 0, "evidence": "Найдено 2 паттернов сложности"}, "statistical_analysis": {"score": 40, "z_score": -0.643663342807271, "percentile": 24.444444444444443, "significance": "НИЗКАЯ", "evidence": "Z-score: -0.64, 24.4 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 3, "avg_target_entropy": 4.612612612612613, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 40, "evidence": "Нет экспертных знаний о проекте Lyra"}}, "final_score": 43, "confidence_level": "НИЗКАЯ", "is_real_vulnerability": false, "evidence_strength": "СЛАБЫЕ", "recommended_action": "НЕ ОТПРАВЛЯТЬ"}, {"index": 18, "vulnerability_id": "a4627792cb985370753ac1a7665dd3a8", "target": "K<PERSON><PERSON>", "entropy": 4.54017857142857, "methods": {"entropy_math": {"score": 80, "entropy_value": 4.54017857142857, "is_mathematically_valid": true, "z_score": 2.4672619047619015, "statistical_significance": 80, "interpretation": "ВЫСОКАЯ - подозрительная сложность", "evidence": "Энтропия 4.540179, Z-score: 2.47"}, "contract_analysis": {"score": 0, "contracts_found": 0, "total_contracts": 1, "evidence": "Проект Kwenta не найден в базе реальных контрактов"}, "network_verification": {"score": 0, "accessible_endpoints": 0, "total_endpoints": 1, "evidence": "Доступно 0/1 endpoints"}, "pattern_analysis": {"score": 40, "pattern_matches": 2, "project_bonus": 0, "evidence": "Найдено 2 паттернов сложности"}, "statistical_analysis": {"score": 40, "z_score": -1.3999399848017602, "percentile": 8.88888888888889, "significance": "НИЗКАЯ", "evidence": "Z-score: -1.40, 8.9 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 3, "avg_target_entropy": 4.54017857142857, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 40, "evidence": "Нет экспертных знаний о проекте Kwenta"}}, "final_score": 40, "confidence_level": "НИЗКАЯ", "is_real_vulnerability": false, "evidence_strength": "СЛАБЫЕ", "recommended_action": "НЕ ОТПРАВЛЯТЬ"}, {"index": 19, "vulnerability_id": "e2e13af233a78699e8cf0487cb2dc179", "target": "Velodrome", "entropy": 4.6872246696035225, "methods": {"entropy_math": {"score": 80, "entropy_value": 4.6872246696035225, "is_mathematically_valid": true, "z_score": 2.9574155653450758, "statistical_significance": 80, "interpretation": "ВЫСОКАЯ - подозрительная сложность", "evidence": "Энтропия 4.687225, Z-score: 2.96"}, "contract_analysis": {"score": 0, "contracts_found": 0, "total_contracts": 1, "evidence": "Проект Velodrome не найден в базе реальных контрактов"}, "network_verification": {"score": 0, "accessible_endpoints": 0, "total_endpoints": 1, "evidence": "Доступно 0/1 endpoints"}, "pattern_analysis": {"score": 40, "pattern_matches": 2, "project_bonus": 0, "evidence": "Найдено 2 паттернов сложности"}, "statistical_analysis": {"score": 40, "z_score": 0.13535374627125632, "percentile": 55.55555555555556, "significance": "НИЗКАЯ", "evidence": "Z-score: 0.14, 55.6 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 3, "avg_target_entropy": 4.6872246696035225, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 40, "evidence": "Нет экспертных знаний о проекте Velodrome"}}, "final_score": 40, "confidence_level": "НИЗКАЯ", "is_real_vulnerability": false, "evidence_strength": "СЛАБЫЕ", "recommended_action": "НЕ ОТПРАВЛЯТЬ"}, {"index": 20, "vulnerability_id": "37dde28c3dc168502fe1a50eb4f40f97", "target": "Chainlink", "entropy": 4.654320987654319, "methods": {"entropy_math": {"score": 80, "entropy_value": 4.654320987654319, "is_mathematically_valid": true, "z_score": 2.847736625514396, "statistical_significance": 80, "interpretation": "ВЫСОКАЯ - подозрительная сложность", "evidence": "Энтропия 4.654321, Z-score: 2.85"}, "contract_analysis": {"score": 90, "contracts_found": 1, "total_contracts": 1, "valid_format_count": 1, "evidence": "Найдено 1/3 реальных контрактов"}, "network_verification": {"score": 30, "accessible_endpoints": 0, "total_endpoints": 1, "evidence": "Доступно 0/1 endpoints"}, "pattern_analysis": {"score": 55, "pattern_matches": 2, "project_bonus": 15, "evidence": "Найдено 2 паттернов сложности"}, "statistical_analysis": {"score": 40, "z_score": -0.2081903344817694, "percentile": 37.77777777777778, "significance": "НИЗКАЯ", "evidence": "Z-score: -0.21, 37.8 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 4, "avg_target_entropy": 4.654320987654319, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 88, "assessment": "В ОЖИДАЕМОМ ДИАПАЗОНЕ", "expected_range": [4.0, 4.7], "complexity_reasons": ["Oracle aggregation", "Price feeds", "Decentralized network"], "credibility_multiplier": 1.1, "evidence": "В ОЖИДАЕМОМ ДИАПАЗОНЕ для Chainlink (ожидается 4.0-4.7)"}}, "final_score": 67, "confidence_level": "СРЕДНЯЯ", "is_real_vulnerability": false, "evidence_strength": "СИЛЬНЫЕ", "recommended_action": "РАССМОТРЕТЬ ОТПРАВКУ"}, {"index": 21, "vulnerability_id": "734000eb49e578becb1ea8b2a2dc8f4e", "target": "SushiSwap", "entropy": 4.687242798353906, "methods": {"entropy_math": {"score": 80, "entropy_value": 4.687242798353906, "is_mathematically_valid": true, "z_score": 2.957475994513021, "statistical_significance": 80, "interpretation": "ВЫСОКАЯ - подозрительная сложность", "evidence": "Энтропия 4.687243, Z-score: 2.96"}, "contract_analysis": {"score": 90, "contracts_found": 1, "total_contracts": 1, "valid_format_count": 1, "evidence": "Найдено 1/3 реальных контрактов"}, "network_verification": {"score": 50, "accessible_endpoints": 1, "total_endpoints": 1, "evidence": "Доступно 1/1 endpoints"}, "pattern_analysis": {"score": 40, "pattern_matches": 2, "project_bonus": 0, "evidence": "Найдено 2 паттернов сложности"}, "statistical_analysis": {"score": 40, "z_score": 0.13554302675650198, "percentile": 62.22222222222222, "significance": "НИЗКАЯ", "evidence": "Z-score: 0.14, 62.2 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 4, "avg_target_entropy": 4.687242798353906, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 81, "assessment": "ВЫШЕ ОЖИДАЕМОГО", "expected_range": [3.8, 4.4], "complexity_reasons": ["Standard AMM", "Well-audited"], "credibility_multiplier": 0.9, "evidence": "ВЫШЕ ОЖИДАЕМОГО для SushiSwap (ожидается 3.8-4.4)"}}, "final_score": 67, "confidence_level": "СРЕДНЯЯ", "is_real_vulnerability": false, "evidence_strength": "СИЛЬНЫЕ", "recommended_action": "РАССМОТРЕТЬ ОТПРАВКУ"}, {"index": 22, "vulnerability_id": "3dca2ee661ce06ec64e9f4145d4d1717", "target": "PancakeSwap", "entropy": 4.793248945147679, "methods": {"entropy_math": {"score": 80, "entropy_value": 4.793248945147679, "is_mathematically_valid": true, "z_score": 3.310829817158932, "statistical_significance": 80, "interpretation": "ВЫСОКАЯ - подозрительная сложность", "evidence": "Энтропия 4.793249, Z-score: 3.31"}, "contract_analysis": {"score": 0, "contracts_found": 0, "total_contracts": 1, "evidence": "Проект PancakeSwap не найден в базе реальных контрактов"}, "network_verification": {"score": 50, "accessible_endpoints": 1, "total_endpoints": 1, "evidence": "Доступно 1/1 endpoints"}, "pattern_analysis": {"score": 60, "pattern_matches": 3, "project_bonus": 0, "evidence": "Найдено 3 паттернов сложности"}, "statistical_analysis": {"score": 40, "z_score": 1.24234269466778, "percentile": 77.77777777777779, "significance": "НИЗКАЯ", "evidence": "Z-score: 1.24, 77.8 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 4, "avg_target_entropy": 4.793248945147679, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 40, "evidence": "Нет экспертных знаний о проекте PancakeSwap"}}, "final_score": 50, "confidence_level": "НИЗКАЯ", "is_real_vulnerability": false, "evidence_strength": "УМЕРЕННЫЕ", "recommended_action": "ДОПОЛНИТЕЛЬНАЯ ПРОВЕРКА"}, {"index": 23, "vulnerability_id": "49843082fca24bce0849715249b006b8", "target": "Trader <PERSON>", "entropy": 4.597402597402597, "methods": {"entropy_math": {"score": 80, "entropy_value": 4.597402597402597, "is_mathematically_valid": true, "z_score": 2.658008658008659, "statistical_significance": 80, "interpretation": "ВЫСОКАЯ - подозрительная сложность", "evidence": "Энтропия 4.597403, Z-score: 2.66"}, "contract_analysis": {"score": 0, "contracts_found": 0, "total_contracts": 1, "evidence": "Проект Trader Joe не найден в базе реальных контрактов"}, "network_verification": {"score": 0, "accessible_endpoints": 0, "total_endpoints": 1, "evidence": "Доступно 0/1 endpoints"}, "pattern_analysis": {"score": 40, "pattern_matches": 2, "project_bonus": 0, "evidence": "Найдено 2 паттернов сложности"}, "statistical_analysis": {"score": 40, "z_score": -0.8024696035057225, "percentile": 15.555555555555555, "significance": "НИЗКАЯ", "evidence": "Z-score: -0.80, 15.6 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 4, "avg_target_entropy": 4.597402597402597, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 40, "evidence": "Нет экспертных знаний о проекте Trader Joe"}}, "final_score": 40, "confidence_level": "НИЗКАЯ", "is_real_vulnerability": false, "evidence_strength": "СЛАБЫЕ", "recommended_action": "НЕ ОТПРАВЛЯТЬ"}, {"index": 24, "vulnerability_id": "c8f8f36b56c97c71c04deeceabda165b", "target": "GMX", "entropy": 4.816143497757849, "methods": {"entropy_math": {"score": 95, "entropy_value": 4.816143497757849, "is_mathematically_valid": true, "z_score": 3.3871449925261627, "statistical_significance": 95, "interpretation": "КРИТИЧЕСКИ ВЫСОКАЯ - аномальная сложность", "evidence": "Энтропия 4.816143, Z-score: 3.39"}, "contract_analysis": {"score": 0, "contracts_found": 0, "total_contracts": 1, "evidence": "Проект GMX не найден в базе реальных контрактов"}, "network_verification": {"score": 30, "accessible_endpoints": 0, "total_endpoints": 1, "evidence": "Доступно 0/1 endpoints"}, "pattern_analysis": {"score": 80, "pattern_matches": 4, "project_bonus": 0, "evidence": "Найдено 4 паттернов сложности"}, "statistical_analysis": {"score": 40, "z_score": 1.4813824484210114, "percentile": 86.66666666666667, "significance": "НИЗКАЯ", "evidence": "Z-score: 1.48, 86.7 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 4, "avg_target_entropy": 4.816143497757849, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 90, "assessment": "ВЫШЕ ОЖИДАЕМОГО", "expected_range": [4.1, 4.8], "complexity_reasons": ["Perpetual trading", "Liquidity pools", "Price impact"], "credibility_multiplier": 1.0, "evidence": "ВЫШЕ ОЖИДАЕМОГО для GMX (ожидается 4.1-4.8)"}}, "final_score": 56, "confidence_level": "СРЕДНЯЯ", "is_real_vulnerability": false, "evidence_strength": "УМЕРЕННЫЕ", "recommended_action": "ДОПОЛНИТЕЛЬНАЯ ПРОВЕРКА"}, {"index": 25, "vulnerability_id": "d3adde0f8c0073a9ac7cb916715eb547", "target": "Venus", "entropy": 4.4862385321100895, "methods": {"entropy_math": {"score": 60, "entropy_value": 4.4862385321100895, "is_mathematically_valid": true, "z_score": 2.287461773700299, "statistical_significance": 60, "interpretation": "ПОВЫШЕННАЯ - требует внимания", "evidence": "Энтропия 4.486239, Z-score: 2.29"}, "contract_analysis": {"score": 0, "contracts_found": 0, "total_contracts": 1, "evidence": "Проект Venus не найден в базе реальных контрактов"}, "network_verification": {"score": 0, "accessible_endpoints": 0, "total_endpoints": 1, "evidence": "Доступно 0/1 endpoints"}, "pattern_analysis": {"score": 20, "pattern_matches": 1, "project_bonus": 0, "evidence": "Найдено 1 паттернов сложности"}, "statistical_analysis": {"score": 70, "z_score": -1.9631225870423552, "percentile": 2.2222222222222223, "significance": "СРЕДНЯЯ", "evidence": "Z-score: -1.96, 2.2 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 3, "avg_target_entropy": 4.4862385321100895, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 40, "evidence": "Нет экспертных знаний о проекте Venus"}}, "final_score": 35, "confidence_level": "ОЧЕНЬ НИЗКАЯ", "is_real_vulnerability": false, "evidence_strength": "СЛАБЫЕ", "recommended_action": "НЕ ОТПРАВЛЯТЬ"}, {"index": 26, "vulnerability_id": "5295f899c577c304ce3564c5787a2434", "target": "Alpaca Finance", "entropy": 4.629955947136561, "methods": {"entropy_math": {"score": 80, "entropy_value": 4.629955947136561, "is_mathematically_valid": true, "z_score": 2.7665198237885376, "statistical_significance": 80, "interpretation": "ВЫСОКАЯ - подозрительная сложность", "evidence": "Энтропия 4.629956, Z-score: 2.77"}, "contract_analysis": {"score": 0, "contracts_found": 0, "total_contracts": 1, "evidence": "Проект Alpaca Finance не найден в базе реальных контрактов"}, "network_verification": {"score": 0, "accessible_endpoints": 0, "total_endpoints": 1, "evidence": "Доступно 0/1 endpoints"}, "pattern_analysis": {"score": 40, "pattern_matches": 2, "project_bonus": 0, "evidence": "Найдено 2 паттернов сложности"}, "statistical_analysis": {"score": 40, "z_score": -0.4625833066757451, "percentile": 31.11111111111111, "significance": "НИЗКАЯ", "evidence": "Z-score: -0.46, 31.1 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 3, "avg_target_entropy": 4.629955947136561, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 40, "evidence": "Нет экспертных знаний о проекте Alpaca Finance"}}, "final_score": 40, "confidence_level": "НИЗКАЯ", "is_real_vulnerability": false, "evidence_strength": "СЛАБЫЕ", "recommended_action": "НЕ ОТПРАВЛЯТЬ"}, {"index": 27, "vulnerability_id": "882816f590682528c253bc003d139896", "target": "Biswap", "entropy": 4.716894977168951, "methods": {"entropy_math": {"score": 80, "entropy_value": 4.716894977168951, "is_mathematically_valid": true, "z_score": 3.0563165905631706, "statistical_significance": 80, "interpretation": "ВЫСОКАЯ - подозрительная сложность", "evidence": "Энтропия 4.716895, Z-score: 3.06"}, "contract_analysis": {"score": 0, "contracts_found": 0, "total_contracts": 1, "evidence": "Проект Biswap не найден в базе реальных контрактов"}, "network_verification": {"score": 0, "accessible_endpoints": 0, "total_endpoints": 1, "evidence": "Доступно 0/1 endpoints"}, "pattern_analysis": {"score": 60, "pattern_matches": 3, "project_bonus": 0, "evidence": "Найдено 3 паттернов сложности"}, "statistical_analysis": {"score": 40, "z_score": 0.44513848568007236, "percentile": 71.11111111111111, "significance": "НИЗКАЯ", "evidence": "Z-score: 0.45, 71.1 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 3, "avg_target_entropy": 4.716894977168951, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 40, "evidence": "Нет экспертных знаний о проекте Biswap"}}, "final_score": 43, "confidence_level": "НИЗКАЯ", "is_real_vulnerability": false, "evidence_strength": "СЛАБЫЕ", "recommended_action": "НЕ ОТПРАВЛЯТЬ"}, {"index": 28, "vulnerability_id": "6ed4a4731b882a51abfe232b9e45c4b2", "target": "El<PERSON><PERSON>", "entropy": 4.6651583710407225, "methods": {"entropy_math": {"score": 80, "entropy_value": 4.6651583710407225, "is_mathematically_valid": true, "z_score": 2.8838612368024092, "statistical_significance": 80, "interpretation": "ВЫСОКАЯ - подозрительная сложность", "evidence": "Энтропия 4.665158, Z-score: 2.88"}, "contract_analysis": {"score": 0, "contracts_found": 0, "total_contracts": 1, "evidence": "Проект Ellipsis не найден в базе реальных контрактов"}, "network_verification": {"score": 0, "accessible_endpoints": 0, "total_endpoints": 1, "evidence": "Доступно 0/1 endpoints"}, "pattern_analysis": {"score": 40, "pattern_matches": 2, "project_bonus": 0, "evidence": "Найдено 2 паттернов сложности"}, "statistical_analysis": {"score": 40, "z_score": -0.09503828909675936, "percentile": 46.666666666666664, "significance": "НИЗКАЯ", "evidence": "Z-score: -0.10, 46.7 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 4, "avg_target_entropy": 4.6651583710407225, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 40, "evidence": "Нет экспертных знаний о проекте Ellipsis"}}, "final_score": 40, "confidence_level": "НИЗКАЯ", "is_real_vulnerability": false, "evidence_strength": "СЛАБЫЕ", "recommended_action": "НЕ ОТПРАВЛЯТЬ"}, {"index": 29, "vulnerability_id": "22a9c43255a6d2e86f28f1e859cb4f67", "target": "Polygon", "entropy": 4.822784810126583, "methods": {"entropy_math": {"score": 95, "entropy_value": 4.822784810126583, "is_mathematically_valid": true, "z_score": 3.4092827004219446, "statistical_significance": 95, "interpretation": "КРИТИЧЕСКИ ВЫСОКАЯ - аномальная сложность", "evidence": "Энтропия 4.822785, Z-score: 3.41"}, "contract_analysis": {"score": 90, "contracts_found": 1, "total_contracts": 1, "valid_format_count": 1, "evidence": "Найдено 1/3 реальных контрактов"}, "network_verification": {"score": 50, "accessible_endpoints": 1, "total_endpoints": 1, "evidence": "Доступно 1/1 endpoints"}, "pattern_analysis": {"score": 100, "pattern_matches": 4, "project_bonus": 20, "evidence": "Найдено 4 паттернов сложности"}, "statistical_analysis": {"score": 70, "z_score": 1.5507237323609935, "percentile": 95.55555555555556, "significance": "СРЕДНЯЯ", "evidence": "Z-score: 1.55, 95.6 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 3, "avg_target_entropy": 4.822784810126583, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 96, "assessment": "В ОЖИДАЕМОМ ДИАПАЗОНЕ", "expected_range": [4.2, 4.9], "complexity_reasons": ["Layer 2 scaling", "Bridge mechanisms", "Validator logic"], "credibility_multiplier": 1.2, "evidence": "В ОЖИДАЕМОМ ДИАПАЗОНЕ для Polygon (ожидается 4.2-4.9)"}}, "final_score": 84, "confidence_level": "ВЫСОКАЯ", "is_real_vulnerability": true, "evidence_strength": "УБЕДИТЕЛЬНЫЕ", "recommended_action": "ОТПРАВИТЬ ОТЧЕТ"}, {"index": 30, "vulnerability_id": "e65781f3ced171816f3e15a8d1888570", "target": "Lyra", "entropy": 4.612612612612613, "methods": {"entropy_math": {"score": 80, "entropy_value": 4.612612612612613, "is_mathematically_valid": true, "z_score": 2.708708708708709, "statistical_significance": 80, "interpretation": "ВЫСОКАЯ - подозрительная сложность", "evidence": "Энтропия 4.612613, Z-score: 2.71"}, "contract_analysis": {"score": 0, "contracts_found": 0, "total_contracts": 1, "evidence": "Проект Lyra не найден в базе реальных контрактов"}, "network_verification": {"score": 20, "accessible_endpoints": 1, "total_endpoints": 1, "evidence": "Доступно 1/1 endpoints"}, "pattern_analysis": {"score": 40, "pattern_matches": 2, "project_bonus": 0, "evidence": "Найдено 2 паттернов сложности"}, "statistical_analysis": {"score": 40, "z_score": -0.643663342807271, "percentile": 24.444444444444443, "significance": "НИЗКАЯ", "evidence": "Z-score: -0.64, 24.4 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 3, "avg_target_entropy": 4.612612612612613, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 40, "evidence": "Нет экспертных знаний о проекте Lyra"}}, "final_score": 43, "confidence_level": "НИЗКАЯ", "is_real_vulnerability": false, "evidence_strength": "СЛАБЫЕ", "recommended_action": "НЕ ОТПРАВЛЯТЬ"}, {"index": 31, "vulnerability_id": "529593efa538469491d0a490bc9d9703", "target": "K<PERSON><PERSON>", "entropy": 4.54017857142857, "methods": {"entropy_math": {"score": 80, "entropy_value": 4.54017857142857, "is_mathematically_valid": true, "z_score": 2.4672619047619015, "statistical_significance": 80, "interpretation": "ВЫСОКАЯ - подозрительная сложность", "evidence": "Энтропия 4.540179, Z-score: 2.47"}, "contract_analysis": {"score": 0, "contracts_found": 0, "total_contracts": 1, "evidence": "Проект Kwenta не найден в базе реальных контрактов"}, "network_verification": {"score": 0, "accessible_endpoints": 0, "total_endpoints": 1, "evidence": "Доступно 0/1 endpoints"}, "pattern_analysis": {"score": 40, "pattern_matches": 2, "project_bonus": 0, "evidence": "Найдено 2 паттернов сложности"}, "statistical_analysis": {"score": 40, "z_score": -1.3999399848017602, "percentile": 8.88888888888889, "significance": "НИЗКАЯ", "evidence": "Z-score: -1.40, 8.9 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 3, "avg_target_entropy": 4.54017857142857, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 40, "evidence": "Нет экспертных знаний о проекте Kwenta"}}, "final_score": 40, "confidence_level": "НИЗКАЯ", "is_real_vulnerability": false, "evidence_strength": "СЛАБЫЕ", "recommended_action": "НЕ ОТПРАВЛЯТЬ"}, {"index": 32, "vulnerability_id": "35dc4361df5944f4f1e70671a193ae67", "target": "Velodrome", "entropy": 4.6872246696035225, "methods": {"entropy_math": {"score": 80, "entropy_value": 4.6872246696035225, "is_mathematically_valid": true, "z_score": 2.9574155653450758, "statistical_significance": 80, "interpretation": "ВЫСОКАЯ - подозрительная сложность", "evidence": "Энтропия 4.687225, Z-score: 2.96"}, "contract_analysis": {"score": 0, "contracts_found": 0, "total_contracts": 1, "evidence": "Проект Velodrome не найден в базе реальных контрактов"}, "network_verification": {"score": 0, "accessible_endpoints": 0, "total_endpoints": 1, "evidence": "Доступно 0/1 endpoints"}, "pattern_analysis": {"score": 40, "pattern_matches": 2, "project_bonus": 0, "evidence": "Найдено 2 паттернов сложности"}, "statistical_analysis": {"score": 40, "z_score": 0.13535374627125632, "percentile": 55.55555555555556, "significance": "НИЗКАЯ", "evidence": "Z-score: 0.14, 55.6 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 3, "avg_target_entropy": 4.6872246696035225, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 40, "evidence": "Нет экспертных знаний о проекте Velodrome"}}, "final_score": 40, "confidence_level": "НИЗКАЯ", "is_real_vulnerability": false, "evidence_strength": "СЛАБЫЕ", "recommended_action": "НЕ ОТПРАВЛЯТЬ"}, {"index": 33, "vulnerability_id": "4fb6cece61b01365473ed42f3d8b258e", "target": "Chainlink", "entropy": 4.654320987654319, "methods": {"entropy_math": {"score": 80, "entropy_value": 4.654320987654319, "is_mathematically_valid": true, "z_score": 2.847736625514396, "statistical_significance": 80, "interpretation": "ВЫСОКАЯ - подозрительная сложность", "evidence": "Энтропия 4.654321, Z-score: 2.85"}, "contract_analysis": {"score": 90, "contracts_found": 1, "total_contracts": 1, "valid_format_count": 1, "evidence": "Найдено 1/3 реальных контрактов"}, "network_verification": {"score": 30, "accessible_endpoints": 0, "total_endpoints": 1, "evidence": "Доступно 0/1 endpoints"}, "pattern_analysis": {"score": 55, "pattern_matches": 2, "project_bonus": 15, "evidence": "Найдено 2 паттернов сложности"}, "statistical_analysis": {"score": 40, "z_score": -0.2081903344817694, "percentile": 37.77777777777778, "significance": "НИЗКАЯ", "evidence": "Z-score: -0.21, 37.8 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 4, "avg_target_entropy": 4.654320987654319, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 88, "assessment": "В ОЖИДАЕМОМ ДИАПАЗОНЕ", "expected_range": [4.0, 4.7], "complexity_reasons": ["Oracle aggregation", "Price feeds", "Decentralized network"], "credibility_multiplier": 1.1, "evidence": "В ОЖИДАЕМОМ ДИАПАЗОНЕ для Chainlink (ожидается 4.0-4.7)"}}, "final_score": 67, "confidence_level": "СРЕДНЯЯ", "is_real_vulnerability": false, "evidence_strength": "СИЛЬНЫЕ", "recommended_action": "РАССМОТРЕТЬ ОТПРАВКУ"}, {"index": 34, "vulnerability_id": "bda58cb6f01d9bf4cb5580e9c93c4561", "target": "SushiSwap", "entropy": 4.687242798353906, "methods": {"entropy_math": {"score": 80, "entropy_value": 4.687242798353906, "is_mathematically_valid": true, "z_score": 2.957475994513021, "statistical_significance": 80, "interpretation": "ВЫСОКАЯ - подозрительная сложность", "evidence": "Энтропия 4.687243, Z-score: 2.96"}, "contract_analysis": {"score": 90, "contracts_found": 1, "total_contracts": 1, "valid_format_count": 1, "evidence": "Найдено 1/3 реальных контрактов"}, "network_verification": {"score": 50, "accessible_endpoints": 1, "total_endpoints": 1, "evidence": "Доступно 1/1 endpoints"}, "pattern_analysis": {"score": 40, "pattern_matches": 2, "project_bonus": 0, "evidence": "Найдено 2 паттернов сложности"}, "statistical_analysis": {"score": 40, "z_score": 0.13554302675650198, "percentile": 62.22222222222222, "significance": "НИЗКАЯ", "evidence": "Z-score: 0.14, 62.2 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 4, "avg_target_entropy": 4.687242798353906, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 81, "assessment": "ВЫШЕ ОЖИДАЕМОГО", "expected_range": [3.8, 4.4], "complexity_reasons": ["Standard AMM", "Well-audited"], "credibility_multiplier": 0.9, "evidence": "ВЫШЕ ОЖИДАЕМОГО для SushiSwap (ожидается 3.8-4.4)"}}, "final_score": 67, "confidence_level": "СРЕДНЯЯ", "is_real_vulnerability": false, "evidence_strength": "СИЛЬНЫЕ", "recommended_action": "РАССМОТРЕТЬ ОТПРАВКУ"}, {"index": 35, "vulnerability_id": "c7782d1ad2ceb42bca8dff49f6da5816", "target": "PancakeSwap", "entropy": 4.793248945147679, "methods": {"entropy_math": {"score": 80, "entropy_value": 4.793248945147679, "is_mathematically_valid": true, "z_score": 3.310829817158932, "statistical_significance": 80, "interpretation": "ВЫСОКАЯ - подозрительная сложность", "evidence": "Энтропия 4.793249, Z-score: 3.31"}, "contract_analysis": {"score": 0, "contracts_found": 0, "total_contracts": 1, "evidence": "Проект PancakeSwap не найден в базе реальных контрактов"}, "network_verification": {"score": 50, "accessible_endpoints": 1, "total_endpoints": 1, "evidence": "Доступно 1/1 endpoints"}, "pattern_analysis": {"score": 60, "pattern_matches": 3, "project_bonus": 0, "evidence": "Найдено 3 паттернов сложности"}, "statistical_analysis": {"score": 40, "z_score": 1.24234269466778, "percentile": 77.77777777777779, "significance": "НИЗКАЯ", "evidence": "Z-score: 1.24, 77.8 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 4, "avg_target_entropy": 4.793248945147679, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 40, "evidence": "Нет экспертных знаний о проекте PancakeSwap"}}, "final_score": 50, "confidence_level": "НИЗКАЯ", "is_real_vulnerability": false, "evidence_strength": "УМЕРЕННЫЕ", "recommended_action": "ДОПОЛНИТЕЛЬНАЯ ПРОВЕРКА"}, {"index": 36, "vulnerability_id": "aaaba393ccc2a88347c3b3744768421a", "target": "Trader <PERSON>", "entropy": 4.597402597402597, "methods": {"entropy_math": {"score": 80, "entropy_value": 4.597402597402597, "is_mathematically_valid": true, "z_score": 2.658008658008659, "statistical_significance": 80, "interpretation": "ВЫСОКАЯ - подозрительная сложность", "evidence": "Энтропия 4.597403, Z-score: 2.66"}, "contract_analysis": {"score": 0, "contracts_found": 0, "total_contracts": 1, "evidence": "Проект Trader Joe не найден в базе реальных контрактов"}, "network_verification": {"score": 0, "accessible_endpoints": 0, "total_endpoints": 1, "evidence": "Доступно 0/1 endpoints"}, "pattern_analysis": {"score": 40, "pattern_matches": 2, "project_bonus": 0, "evidence": "Найдено 2 паттернов сложности"}, "statistical_analysis": {"score": 40, "z_score": -0.8024696035057225, "percentile": 15.555555555555555, "significance": "НИЗКАЯ", "evidence": "Z-score: -0.80, 15.6 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 4, "avg_target_entropy": 4.597402597402597, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 40, "evidence": "Нет экспертных знаний о проекте Trader Joe"}}, "final_score": 40, "confidence_level": "НИЗКАЯ", "is_real_vulnerability": false, "evidence_strength": "СЛАБЫЕ", "recommended_action": "НЕ ОТПРАВЛЯТЬ"}, {"index": 37, "vulnerability_id": "e6b0dadf9706d4ff524fa4216606501f", "target": "GMX", "entropy": 4.816143497757849, "methods": {"entropy_math": {"score": 95, "entropy_value": 4.816143497757849, "is_mathematically_valid": true, "z_score": 3.3871449925261627, "statistical_significance": 95, "interpretation": "КРИТИЧЕСКИ ВЫСОКАЯ - аномальная сложность", "evidence": "Энтропия 4.816143, Z-score: 3.39"}, "contract_analysis": {"score": 0, "contracts_found": 0, "total_contracts": 1, "evidence": "Проект GMX не найден в базе реальных контрактов"}, "network_verification": {"score": 30, "accessible_endpoints": 0, "total_endpoints": 1, "evidence": "Доступно 0/1 endpoints"}, "pattern_analysis": {"score": 80, "pattern_matches": 4, "project_bonus": 0, "evidence": "Найдено 4 паттернов сложности"}, "statistical_analysis": {"score": 40, "z_score": 1.4813824484210114, "percentile": 86.66666666666667, "significance": "НИЗКАЯ", "evidence": "Z-score: 1.48, 86.7 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 4, "avg_target_entropy": 4.816143497757849, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 90, "assessment": "ВЫШЕ ОЖИДАЕМОГО", "expected_range": [4.1, 4.8], "complexity_reasons": ["Perpetual trading", "Liquidity pools", "Price impact"], "credibility_multiplier": 1.0, "evidence": "ВЫШЕ ОЖИДАЕМОГО для GMX (ожидается 4.1-4.8)"}}, "final_score": 56, "confidence_level": "СРЕДНЯЯ", "is_real_vulnerability": false, "evidence_strength": "УМЕРЕННЫЕ", "recommended_action": "ДОПОЛНИТЕЛЬНАЯ ПРОВЕРКА"}, {"index": 38, "vulnerability_id": "ae1a1b61eb2aff90bbf963e51d41d7ff", "target": "Venus", "entropy": 4.4862385321100895, "methods": {"entropy_math": {"score": 60, "entropy_value": 4.4862385321100895, "is_mathematically_valid": true, "z_score": 2.287461773700299, "statistical_significance": 60, "interpretation": "ПОВЫШЕННАЯ - требует внимания", "evidence": "Энтропия 4.486239, Z-score: 2.29"}, "contract_analysis": {"score": 0, "contracts_found": 0, "total_contracts": 1, "evidence": "Проект Venus не найден в базе реальных контрактов"}, "network_verification": {"score": 0, "accessible_endpoints": 0, "total_endpoints": 1, "evidence": "Доступно 0/1 endpoints"}, "pattern_analysis": {"score": 20, "pattern_matches": 1, "project_bonus": 0, "evidence": "Найдено 1 паттернов сложности"}, "statistical_analysis": {"score": 70, "z_score": -1.9631225870423552, "percentile": 2.2222222222222223, "significance": "СРЕДНЯЯ", "evidence": "Z-score: -1.96, 2.2 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 3, "avg_target_entropy": 4.4862385321100895, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 40, "evidence": "Нет экспертных знаний о проекте Venus"}}, "final_score": 35, "confidence_level": "ОЧЕНЬ НИЗКАЯ", "is_real_vulnerability": false, "evidence_strength": "СЛАБЫЕ", "recommended_action": "НЕ ОТПРАВЛЯТЬ"}, {"index": 39, "vulnerability_id": "73075581e4aa5913a4b12128074e847b", "target": "Alpaca Finance", "entropy": 4.629955947136561, "methods": {"entropy_math": {"score": 80, "entropy_value": 4.629955947136561, "is_mathematically_valid": true, "z_score": 2.7665198237885376, "statistical_significance": 80, "interpretation": "ВЫСОКАЯ - подозрительная сложность", "evidence": "Энтропия 4.629956, Z-score: 2.77"}, "contract_analysis": {"score": 0, "contracts_found": 0, "total_contracts": 1, "evidence": "Проект Alpaca Finance не найден в базе реальных контрактов"}, "network_verification": {"score": 0, "accessible_endpoints": 0, "total_endpoints": 1, "evidence": "Доступно 0/1 endpoints"}, "pattern_analysis": {"score": 40, "pattern_matches": 2, "project_bonus": 0, "evidence": "Найдено 2 паттернов сложности"}, "statistical_analysis": {"score": 40, "z_score": -0.4625833066757451, "percentile": 31.11111111111111, "significance": "НИЗКАЯ", "evidence": "Z-score: -0.46, 31.1 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 3, "avg_target_entropy": 4.629955947136561, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 40, "evidence": "Нет экспертных знаний о проекте Alpaca Finance"}}, "final_score": 40, "confidence_level": "НИЗКАЯ", "is_real_vulnerability": false, "evidence_strength": "СЛАБЫЕ", "recommended_action": "НЕ ОТПРАВЛЯТЬ"}, {"index": 40, "vulnerability_id": "1702ff80c40b8404ba280a15f84acfaf", "target": "Biswap", "entropy": 4.716894977168951, "methods": {"entropy_math": {"score": 80, "entropy_value": 4.716894977168951, "is_mathematically_valid": true, "z_score": 3.0563165905631706, "statistical_significance": 80, "interpretation": "ВЫСОКАЯ - подозрительная сложность", "evidence": "Энтропия 4.716895, Z-score: 3.06"}, "contract_analysis": {"score": 0, "contracts_found": 0, "total_contracts": 1, "evidence": "Проект Biswap не найден в базе реальных контрактов"}, "network_verification": {"score": 0, "accessible_endpoints": 0, "total_endpoints": 1, "evidence": "Доступно 0/1 endpoints"}, "pattern_analysis": {"score": 60, "pattern_matches": 3, "project_bonus": 0, "evidence": "Найдено 3 паттернов сложности"}, "statistical_analysis": {"score": 40, "z_score": 0.44513848568007236, "percentile": 71.11111111111111, "significance": "НИЗКАЯ", "evidence": "Z-score: 0.45, 71.1 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 3, "avg_target_entropy": 4.716894977168951, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 40, "evidence": "Нет экспертных знаний о проекте Biswap"}}, "final_score": 43, "confidence_level": "НИЗКАЯ", "is_real_vulnerability": false, "evidence_strength": "СЛАБЫЕ", "recommended_action": "НЕ ОТПРАВЛЯТЬ"}, {"index": 41, "vulnerability_id": "84ea03894dcb7c2f1dddb5d8aab560d1", "target": "El<PERSON><PERSON>", "entropy": 4.6651583710407225, "methods": {"entropy_math": {"score": 80, "entropy_value": 4.6651583710407225, "is_mathematically_valid": true, "z_score": 2.8838612368024092, "statistical_significance": 80, "interpretation": "ВЫСОКАЯ - подозрительная сложность", "evidence": "Энтропия 4.665158, Z-score: 2.88"}, "contract_analysis": {"score": 0, "contracts_found": 0, "total_contracts": 1, "evidence": "Проект Ellipsis не найден в базе реальных контрактов"}, "network_verification": {"score": 0, "accessible_endpoints": 0, "total_endpoints": 1, "evidence": "Доступно 0/1 endpoints"}, "pattern_analysis": {"score": 40, "pattern_matches": 2, "project_bonus": 0, "evidence": "Найдено 2 паттернов сложности"}, "statistical_analysis": {"score": 40, "z_score": -0.09503828909675936, "percentile": 46.666666666666664, "significance": "НИЗКАЯ", "evidence": "Z-score: -0.10, 46.7 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 4, "avg_target_entropy": 4.6651583710407225, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 40, "evidence": "Нет экспертных знаний о проекте Ellipsis"}}, "final_score": 40, "confidence_level": "НИЗКАЯ", "is_real_vulnerability": false, "evidence_strength": "СЛАБЫЕ", "recommended_action": "НЕ ОТПРАВЛЯТЬ"}, {"index": 42, "vulnerability_id": "9b74ff06363610a48167c1a57cafd2e7", "target": "Polygon", "entropy": 4.822784810126583, "methods": {"entropy_math": {"score": 95, "entropy_value": 4.822784810126583, "is_mathematically_valid": true, "z_score": 3.4092827004219446, "statistical_significance": 95, "interpretation": "КРИТИЧЕСКИ ВЫСОКАЯ - аномальная сложность", "evidence": "Энтропия 4.822785, Z-score: 3.41"}, "contract_analysis": {"score": 90, "contracts_found": 1, "total_contracts": 1, "valid_format_count": 1, "evidence": "Найдено 1/3 реальных контрактов"}, "network_verification": {"score": 50, "accessible_endpoints": 1, "total_endpoints": 1, "evidence": "Доступно 1/1 endpoints"}, "pattern_analysis": {"score": 100, "pattern_matches": 4, "project_bonus": 20, "evidence": "Найдено 4 паттернов сложности"}, "statistical_analysis": {"score": 70, "z_score": 1.5507237323609935, "percentile": 95.55555555555556, "significance": "СРЕДНЯЯ", "evidence": "Z-score: 1.55, 95.6 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 3, "avg_target_entropy": 4.822784810126583, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 96, "assessment": "В ОЖИДАЕМОМ ДИАПАЗОНЕ", "expected_range": [4.2, 4.9], "complexity_reasons": ["Layer 2 scaling", "Bridge mechanisms", "Validator logic"], "credibility_multiplier": 1.2, "evidence": "В ОЖИДАЕМОМ ДИАПАЗОНЕ для Polygon (ожидается 4.2-4.9)"}}, "final_score": 84, "confidence_level": "ВЫСОКАЯ", "is_real_vulnerability": true, "evidence_strength": "УБЕДИТЕЛЬНЫЕ", "recommended_action": "ОТПРАВИТЬ ОТЧЕТ"}, {"index": 43, "vulnerability_id": "e67607a3670e1ba02b9dbeec58a16812", "target": "Lyra", "entropy": 4.612612612612613, "methods": {"entropy_math": {"score": 80, "entropy_value": 4.612612612612613, "is_mathematically_valid": true, "z_score": 2.708708708708709, "statistical_significance": 80, "interpretation": "ВЫСОКАЯ - подозрительная сложность", "evidence": "Энтропия 4.612613, Z-score: 2.71"}, "contract_analysis": {"score": 0, "contracts_found": 0, "total_contracts": 1, "evidence": "Проект Lyra не найден в базе реальных контрактов"}, "network_verification": {"score": 20, "accessible_endpoints": 1, "total_endpoints": 1, "evidence": "Доступно 1/1 endpoints"}, "pattern_analysis": {"score": 40, "pattern_matches": 2, "project_bonus": 0, "evidence": "Найдено 2 паттернов сложности"}, "statistical_analysis": {"score": 40, "z_score": -0.643663342807271, "percentile": 24.444444444444443, "significance": "НИЗКАЯ", "evidence": "Z-score: -0.64, 24.4 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 3, "avg_target_entropy": 4.612612612612613, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 40, "evidence": "Нет экспертных знаний о проекте Lyra"}}, "final_score": 43, "confidence_level": "НИЗКАЯ", "is_real_vulnerability": false, "evidence_strength": "СЛАБЫЕ", "recommended_action": "НЕ ОТПРАВЛЯТЬ"}, {"index": 44, "vulnerability_id": "dd57d096e9c91332ad4c4560ae2172cb", "target": "K<PERSON><PERSON>", "entropy": 4.54017857142857, "methods": {"entropy_math": {"score": 80, "entropy_value": 4.54017857142857, "is_mathematically_valid": true, "z_score": 2.4672619047619015, "statistical_significance": 80, "interpretation": "ВЫСОКАЯ - подозрительная сложность", "evidence": "Энтропия 4.540179, Z-score: 2.47"}, "contract_analysis": {"score": 0, "contracts_found": 0, "total_contracts": 1, "evidence": "Проект Kwenta не найден в базе реальных контрактов"}, "network_verification": {"score": 0, "accessible_endpoints": 0, "total_endpoints": 1, "evidence": "Доступно 0/1 endpoints"}, "pattern_analysis": {"score": 40, "pattern_matches": 2, "project_bonus": 0, "evidence": "Найдено 2 паттернов сложности"}, "statistical_analysis": {"score": 40, "z_score": -1.3999399848017602, "percentile": 8.88888888888889, "significance": "НИЗКАЯ", "evidence": "Z-score: -1.40, 8.9 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 3, "avg_target_entropy": 4.54017857142857, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 40, "evidence": "Нет экспертных знаний о проекте Kwenta"}}, "final_score": 40, "confidence_level": "НИЗКАЯ", "is_real_vulnerability": false, "evidence_strength": "СЛАБЫЕ", "recommended_action": "НЕ ОТПРАВЛЯТЬ"}, {"index": 45, "vulnerability_id": "42aa609e293aad8472bbb152907cd6af", "target": "Velodrome", "entropy": 4.6872246696035225, "methods": {"entropy_math": {"score": 80, "entropy_value": 4.6872246696035225, "is_mathematically_valid": true, "z_score": 2.9574155653450758, "statistical_significance": 80, "interpretation": "ВЫСОКАЯ - подозрительная сложность", "evidence": "Энтропия 4.687225, Z-score: 2.96"}, "contract_analysis": {"score": 0, "contracts_found": 0, "total_contracts": 1, "evidence": "Проект Velodrome не найден в базе реальных контрактов"}, "network_verification": {"score": 0, "accessible_endpoints": 0, "total_endpoints": 1, "evidence": "Доступно 0/1 endpoints"}, "pattern_analysis": {"score": 40, "pattern_matches": 2, "project_bonus": 0, "evidence": "Найдено 2 паттернов сложности"}, "statistical_analysis": {"score": 40, "z_score": 0.13535374627125632, "percentile": 55.55555555555556, "significance": "НИЗКАЯ", "evidence": "Z-score: 0.14, 55.6 процентиль"}, "cross_validation": {"score": 80, "same_target_count": 3, "avg_target_entropy": 4.6872246696035225, "deviation": 0.0, "evidence": "Консистентно с другими находками (отклонение: 0.000)"}, "expert_assessment": {"score": 40, "evidence": "Нет экспертных знаний о проекте Velodrome"}}, "final_score": 40, "confidence_level": "НИЗКАЯ", "is_real_vulnerability": false, "evidence_strength": "СЛАБЫЕ", "recommended_action": "НЕ ОТПРАВЛЯТЬ"}]}