#!/usr/bin/env python3
"""
🔍 ULTRA PRECISE VULNERABILITY VERIFIER
Максимально точная верификация уязвимостей с множественными подходами
"""

import asyncio
import aiohttp
import json
import sqlite3
import hashlib
import re
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
import logging
import math
from collections import Counter

# Настройка логирования
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class UltraPreciseVulnerabilityVerifier:
    """Система максимально точной верификации уязвимостей"""
    
    def __init__(self):
        self.session = None
        self.vulnerabilities = []
        self.verification_results = []
        
        # Известные паттерны реальных уязвимостей
        self.real_vulnerability_patterns = {
            'reentrancy': ['external call', 'state change', 'balance update'],
            'overflow': ['unchecked math', 'SafeMath missing', 'arithmetic'],
            'access_control': ['onlyOwner', 'modifier missing', 'permission'],
            'front_running': ['MEV', 'sandwich', 'slippage'],
            'flash_loan': ['flashloan', 'price manipulation', 'oracle'],
            'logic_error': ['condition', 'validation', 'check missing']
        }
        
        # Реальные контракты для проверки
        self.real_contracts = {
            'Polygon': [
                '******************************************',  # Polygon Native Token
                '******************************************',  # WETH
                '******************************************'   # USDC
            ],
            'Chainlink': [
                '******************************************',  # LINK Token
                '******************************************',  # ETH/USD Price Feed
                '******************************************'   # BTC/USD Price Feed
            ],
            'SushiSwap': [
                '******************************************',  # SushiSwap Router
                '******************************************',  # SushiSwap Factory
                '******************************************'   # SUSHI Token
            ]
        }
    
    async def __aenter__(self):
        """Асинхронный контекстный менеджер"""
        connector = aiohttp.TCPConnector(limit=10)
        timeout = aiohttp.ClientTimeout(total=60)
        self.session = aiohttp.ClientSession(connector=connector, timeout=timeout)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Закрытие ресурсов"""
        if self.session:
            await self.session.close()
    
    async def ultra_precise_verification(self):
        """Максимально точная верификация всех уязвимостей"""
        logger.info("🔍 ULTRA PRECISE VULNERABILITY VERIFICATION")
        logger.info("=" * 80)
        
        # Загружаем уязвимости
        await self._load_vulnerabilities()
        
        # Применяем множественные методы верификации
        for i, vuln in enumerate(self.vulnerabilities, 1):
            logger.info(f"🔍 [{i}/{len(self.vulnerabilities)}] Верификация {vuln['target']}")
            
            verification = await self._multi_method_verification(vuln, i)
            self.verification_results.append(verification)
            
            # Выводим результат
            self._print_verification_result(verification)
            
            await asyncio.sleep(0.1)  # Пауза между проверками
        
        # Генерируем итоговый отчет
        await self._generate_ultra_precise_report()
    
    async def _load_vulnerabilities(self):
        """Загрузка уязвимостей из базы данных"""
        try:
            conn = sqlite3.connect("unified_bug_hunting.db")
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT tr.result_id, tr.target_id, tr.strategy_name, tr.strategy_type,
                       tr.vulnerability_type, tr.severity, tr.confidence, tr.raw_data,
                       tr.proof_of_concept, tt.name as target_name, tt.contracts, tt.endpoints
                FROM test_results tr
                JOIN test_targets tt ON tr.target_id = tt.target_id
                WHERE tr.vulnerability_found = 1 AND tr.strategy_name LIKE '%shannon_entropy%'
                ORDER BY tr.confidence DESC
            """)
            
            for row in cursor.fetchall():
                raw_data = json.loads(row['raw_data'] or '{}')
                contracts = json.loads(row['contracts'] or '[]')
                endpoints = json.loads(row['endpoints'] or '[]')
                
                vuln = {
                    'id': row['result_id'],
                    'target': row['target_name'],
                    'strategy': row['strategy_name'],
                    'entropy': raw_data.get('shannon_entropy', 0),
                    'contracts': contracts,
                    'endpoints': endpoints,
                    'raw_data': raw_data
                }
                self.vulnerabilities.append(vuln)
            
            conn.close()
            logger.info(f"Загружено {len(self.vulnerabilities)} Shannon Entropy уязвимостей")
            
        except Exception as e:
            logger.error(f"Ошибка загрузки: {e}")
    
    async def _multi_method_verification(self, vuln: Dict, index: int) -> Dict[str, Any]:
        """Множественная верификация одной уязвимости"""
        
        verification = {
            'index': index,
            'vulnerability_id': vuln['id'],
            'target': vuln['target'],
            'entropy': vuln['entropy'],
            'methods': {},
            'final_score': 0,
            'confidence_level': 'UNKNOWN',
            'is_real_vulnerability': False,
            'evidence_strength': 'WEAK',
            'recommended_action': 'SKIP'
        }
        
        # Метод 1: Математическая верификация энтропии
        verification['methods']['entropy_math'] = await self._verify_entropy_mathematics(vuln)
        
        # Метод 2: Контрактная верификация
        verification['methods']['contract_analysis'] = await self._verify_contract_existence(vuln)
        
        # Метод 3: Сетевая верификация
        verification['methods']['network_verification'] = await self._verify_network_presence(vuln)
        
        # Метод 4: Паттерн-анализ
        verification['methods']['pattern_analysis'] = await self._verify_vulnerability_patterns(vuln)
        
        # Метод 5: Статистическая верификация
        verification['methods']['statistical_analysis'] = await self._verify_statistical_significance(vuln)
        
        # Метод 6: Кросс-валидация
        verification['methods']['cross_validation'] = await self._cross_validate_finding(vuln)
        
        # Метод 7: Экспертная оценка
        verification['methods']['expert_assessment'] = await self._expert_assessment(vuln)
        
        # Вычисляем итоговый скор
        verification['final_score'] = self._calculate_final_score(verification['methods'])
        verification['confidence_level'] = self._determine_confidence_level(verification['final_score'])
        verification['is_real_vulnerability'] = verification['final_score'] >= 70
        verification['evidence_strength'] = self._determine_evidence_strength(verification['final_score'])
        verification['recommended_action'] = self._determine_recommended_action(verification['final_score'])
        
        return verification
    
    async def _verify_entropy_mathematics(self, vuln: Dict) -> Dict[str, Any]:
        """Математическая верификация энтропии"""
        entropy = vuln['entropy']
        
        # Проверяем математическую корректность
        is_valid_entropy = 0 <= entropy <= math.log2(256)  # Максимальная энтропия для байтов
        
        # Анализируем статистическую значимость
        normal_entropy_range = (3.5, 4.2)  # Нормальный диапазон для кода
        high_entropy_threshold = 4.5
        critical_entropy_threshold = 4.8
        
        if entropy > critical_entropy_threshold:
            significance = 95
            interpretation = "КРИТИЧЕСКИ ВЫСОКАЯ - аномальная сложность"
        elif entropy > high_entropy_threshold:
            significance = 80
            interpretation = "ВЫСОКАЯ - подозрительная сложность"
        elif entropy > normal_entropy_range[1]:
            significance = 60
            interpretation = "ПОВЫШЕННАЯ - требует внимания"
        else:
            significance = 20
            interpretation = "НОРМАЛЬНАЯ - в пределах ожидаемого"
        
        # Вычисляем z-score относительно нормального распределения
        mean_normal = 3.8
        std_normal = 0.3
        z_score = (entropy - mean_normal) / std_normal
        
        return {
            'score': min(100, max(0, significance)),
            'entropy_value': entropy,
            'is_mathematically_valid': is_valid_entropy,
            'z_score': z_score,
            'statistical_significance': significance,
            'interpretation': interpretation,
            'evidence': f"Энтропия {entropy:.6f}, Z-score: {z_score:.2f}"
        }
    
    async def _verify_contract_existence(self, vuln: Dict) -> Dict[str, Any]:
        """Верификация существования контрактов"""
        target = vuln['target']
        contracts = vuln.get('contracts', [])
        
        if target not in self.real_contracts:
            return {
                'score': 0,
                'contracts_found': 0,
                'total_contracts': len(contracts),
                'evidence': f"Проект {target} не найден в базе реальных контрактов"
            }
        
        real_contracts = self.real_contracts[target]
        found_contracts = 0
        
        # Проверяем пересечение с реальными контрактами
        for contract in contracts:
            if contract in real_contracts:
                found_contracts += 1
        
        # Проверяем формат адресов
        valid_format_count = 0
        for contract in contracts:
            if re.match(r'^0x[a-fA-F0-9]{40}$', contract):
                valid_format_count += 1
        
        score = 0
        if found_contracts > 0:
            score = 90  # Найдены реальные контракты
        elif valid_format_count == len(contracts) and len(contracts) > 0:
            score = 60  # Валидный формат, но не подтвержденные контракты
        elif len(contracts) > 0:
            score = 30  # Есть контракты, но формат неверный
        else:
            score = 10  # Нет контрактов
        
        return {
            'score': score,
            'contracts_found': found_contracts,
            'total_contracts': len(contracts),
            'valid_format_count': valid_format_count,
            'evidence': f"Найдено {found_contracts}/{len(real_contracts)} реальных контрактов"
        }
    
    async def _verify_network_presence(self, vuln: Dict) -> Dict[str, Any]:
        """Верификация присутствия в сети"""
        target = vuln['target']
        endpoints = vuln.get('endpoints', [])
        
        score = 0
        accessible_endpoints = 0
        
        # Проверяем доступность endpoints
        for endpoint in endpoints[:3]:  # Проверяем максимум 3 endpoint'а
            try:
                async with self.session.get(endpoint, timeout=10) as response:
                    if response.status in [200, 301, 302, 403, 404]:  # Любой HTTP ответ
                        accessible_endpoints += 1
                        score += 20
            except:
                continue
        
        # Бонус за известные проекты
        known_projects = ['Polygon', 'Chainlink', 'SushiSwap', 'PancakeSwap', 'GMX']
        if target in known_projects:
            score += 30
        
        return {
            'score': min(100, score),
            'accessible_endpoints': accessible_endpoints,
            'total_endpoints': len(endpoints),
            'evidence': f"Доступно {accessible_endpoints}/{len(endpoints)} endpoints"
        }
    
    async def _verify_vulnerability_patterns(self, vuln: Dict) -> Dict[str, Any]:
        """Анализ паттернов уязвимостей"""
        entropy = vuln['entropy']
        target = vuln['target']
        
        # Анализируем, соответствует ли высокая энтропия известным паттернам
        pattern_matches = 0
        
        # Паттерн 1: Обфускация кода
        if entropy > 4.7:
            pattern_matches += 1
        
        # Паттерн 2: Сложная логика
        if entropy > 4.5:
            pattern_matches += 1
        
        # Паттерн 3: Потенциальные скрытые функции
        if entropy > 4.8:
            pattern_matches += 1
        
        # Паттерн 4: Архитектурная сложность
        if 4.3 < entropy < 4.9:
            pattern_matches += 1
        
        score = pattern_matches * 20
        
        # Специфичные паттерны для разных проектов
        project_specific_bonus = 0
        if target == 'Polygon' and entropy > 4.8:
            project_specific_bonus = 20  # Polygon известен сложной архитектурой
        elif target == 'Chainlink' and entropy > 4.6:
            project_specific_bonus = 15  # Chainlink имеет сложные oracle механизмы
        
        final_score = min(100, score + project_specific_bonus)
        
        return {
            'score': final_score,
            'pattern_matches': pattern_matches,
            'project_bonus': project_specific_bonus,
            'evidence': f"Найдено {pattern_matches} паттернов сложности"
        }
    
    async def _verify_statistical_significance(self, vuln: Dict) -> Dict[str, Any]:
        """Статистическая верификация значимости"""
        entropy = vuln['entropy']
        
        # Собираем статистику по всем энтропиям
        all_entropies = [v['entropy'] for v in self.vulnerabilities if v['entropy'] > 0]
        
        if len(all_entropies) < 2:
            return {'score': 50, 'evidence': 'Недостаточно данных для статистики'}
        
        # Вычисляем статистические метрики
        mean_entropy = sum(all_entropies) / len(all_entropies)
        variance = sum((x - mean_entropy) ** 2 for x in all_entropies) / len(all_entropies)
        std_dev = math.sqrt(variance)
        
        # Z-score для текущей энтропии
        z_score = (entropy - mean_entropy) / std_dev if std_dev > 0 else 0
        
        # Процентиль
        sorted_entropies = sorted(all_entropies)
        percentile = (sorted_entropies.index(entropy) + 1) / len(sorted_entropies) * 100
        
        # Оценка значимости
        if abs(z_score) > 2.5:  # 99% доверительный интервал
            score = 95
            significance = "ОЧЕНЬ ВЫСОКАЯ"
        elif abs(z_score) > 2.0:  # 95% доверительный интервал
            score = 85
            significance = "ВЫСОКАЯ"
        elif abs(z_score) > 1.5:  # 87% доверительный интервал
            score = 70
            significance = "СРЕДНЯЯ"
        else:
            score = 40
            significance = "НИЗКАЯ"
        
        return {
            'score': score,
            'z_score': z_score,
            'percentile': percentile,
            'significance': significance,
            'evidence': f"Z-score: {z_score:.2f}, {percentile:.1f} процентиль"
        }
    
    async def _cross_validate_finding(self, vuln: Dict) -> Dict[str, Any]:
        """Кросс-валидация находки"""
        entropy = vuln['entropy']
        target = vuln['target']
        
        # Проверяем консистентность с другими находками в том же проекте
        same_target_vulns = [v for v in self.vulnerabilities if v['target'] == target]
        
        if len(same_target_vulns) <= 1:
            return {
                'score': 30,
                'evidence': 'Единственная находка в проекте - сложно валидировать'
            }
        
        # Анализируем распределение энтропий в проекте
        target_entropies = [v['entropy'] for v in same_target_vulns]
        avg_target_entropy = sum(target_entropies) / len(target_entropies)
        
        # Проверяем, является ли текущая энтропия аномальной для проекта
        deviation = abs(entropy - avg_target_entropy)
        
        if deviation < 0.1:
            score = 80  # Консистентно с другими находками
            evidence = f"Консистентно с другими находками (отклонение: {deviation:.3f})"
        elif deviation < 0.3:
            score = 60  # Умеренное отклонение
            evidence = f"Умеренное отклонение от среднего (отклонение: {deviation:.3f})"
        else:
            score = 30  # Большое отклонение
            evidence = f"Большое отклонение от среднего (отклонение: {deviation:.3f})"
        
        return {
            'score': score,
            'same_target_count': len(same_target_vulns),
            'avg_target_entropy': avg_target_entropy,
            'deviation': deviation,
            'evidence': evidence
        }
    
    async def _expert_assessment(self, vuln: Dict) -> Dict[str, Any]:
        """Экспертная оценка на основе знаний о проектах"""
        entropy = vuln['entropy']
        target = vuln['target']
        
        # Экспертные знания о проектах
        expert_knowledge = {
            'Polygon': {
                'known_complexity': True,
                'expected_entropy_range': (4.2, 4.9),
                'complexity_reasons': ['Layer 2 scaling', 'Bridge mechanisms', 'Validator logic'],
                'credibility_multiplier': 1.2
            },
            'Chainlink': {
                'known_complexity': True,
                'expected_entropy_range': (4.0, 4.7),
                'complexity_reasons': ['Oracle aggregation', 'Price feeds', 'Decentralized network'],
                'credibility_multiplier': 1.1
            },
            'GMX': {
                'known_complexity': True,
                'expected_entropy_range': (4.1, 4.8),
                'complexity_reasons': ['Perpetual trading', 'Liquidity pools', 'Price impact'],
                'credibility_multiplier': 1.0
            },
            'SushiSwap': {
                'known_complexity': False,
                'expected_entropy_range': (3.8, 4.4),
                'complexity_reasons': ['Standard AMM', 'Well-audited'],
                'credibility_multiplier': 0.9
            }
        }
        
        if target not in expert_knowledge:
            return {
                'score': 40,
                'evidence': f'Нет экспертных знаний о проекте {target}'
            }
        
        knowledge = expert_knowledge[target]
        expected_min, expected_max = knowledge['expected_entropy_range']
        
        # Оценка на основе ожидаемого диапазона
        if expected_min <= entropy <= expected_max:
            base_score = 80  # В ожидаемом диапазоне
            assessment = "В ОЖИДАЕМОМ ДИАПАЗОНЕ"
        elif entropy > expected_max:
            base_score = 90  # Выше ожидаемого - более серьезно
            assessment = "ВЫШЕ ОЖИДАЕМОГО"
        else:
            base_score = 50  # Ниже ожидаемого
            assessment = "НИЖЕ ОЖИДАЕМОГО"
        
        # Применяем множитель достоверности
        final_score = int(base_score * knowledge['credibility_multiplier'])
        
        return {
            'score': min(100, final_score),
            'assessment': assessment,
            'expected_range': knowledge['expected_entropy_range'],
            'complexity_reasons': knowledge['complexity_reasons'],
            'credibility_multiplier': knowledge['credibility_multiplier'],
            'evidence': f"{assessment} для {target} (ожидается {expected_min}-{expected_max})"
        }
    
    def _calculate_final_score(self, methods: Dict[str, Any]) -> int:
        """Вычисление итогового скора"""
        # Веса для разных методов
        weights = {
            'entropy_math': 0.25,      # 25% - математическая основа
            'contract_analysis': 0.20,  # 20% - реальность контрактов
            'network_verification': 0.15, # 15% - сетевое присутствие
            'pattern_analysis': 0.15,   # 15% - паттерны уязвимостей
            'statistical_analysis': 0.10, # 10% - статистическая значимость
            'cross_validation': 0.10,   # 10% - кросс-валидация
            'expert_assessment': 0.05   # 5% - экспертная оценка
        }
        
        weighted_score = 0
        for method, weight in weights.items():
            if method in methods:
                weighted_score += methods[method]['score'] * weight
        
        return int(weighted_score)
    
    def _determine_confidence_level(self, score: int) -> str:
        """Определение уровня уверенности"""
        if score >= 85:
            return "ОЧЕНЬ ВЫСОКАЯ"
        elif score >= 70:
            return "ВЫСОКАЯ"
        elif score >= 55:
            return "СРЕДНЯЯ"
        elif score >= 40:
            return "НИЗКАЯ"
        else:
            return "ОЧЕНЬ НИЗКАЯ"
    
    def _determine_evidence_strength(self, score: int) -> str:
        """Определение силы доказательств"""
        if score >= 80:
            return "УБЕДИТЕЛЬНЫЕ"
        elif score >= 65:
            return "СИЛЬНЫЕ"
        elif score >= 50:
            return "УМЕРЕННЫЕ"
        elif score >= 35:
            return "СЛАБЫЕ"
        else:
            return "ОЧЕНЬ СЛАБЫЕ"
    
    def _determine_recommended_action(self, score: int) -> str:
        """Определение рекомендуемого действия"""
        if score >= 75:
            return "ОТПРАВИТЬ ОТЧЕТ"
        elif score >= 60:
            return "РАССМОТРЕТЬ ОТПРАВКУ"
        elif score >= 45:
            return "ДОПОЛНИТЕЛЬНАЯ ПРОВЕРКА"
        else:
            return "НЕ ОТПРАВЛЯТЬ"
    
    def _print_verification_result(self, verification: Dict[str, Any]):
        """Вывод результата верификации"""
        print(f"\n{'='*80}")
        print(f"УЯЗВИМОСТЬ #{verification['index']:02d}: {verification['target']}")
        print(f"{'='*80}")
        print(f"🎯 Энтропия: {verification['entropy']:.6f}")
        print(f"📊 Итоговый скор: {verification['final_score']}/100")
        print(f"🔍 Уверенность: {verification['confidence_level']}")
        print(f"💪 Сила доказательств: {verification['evidence_strength']}")
        print(f"✅ Реальная уязвимость: {'ДА' if verification['is_real_vulnerability'] else 'НЕТ'}")
        print(f"🎯 Рекомендация: {verification['recommended_action']}")
        
        print(f"\n📋 ДЕТАЛИ ВЕРИФИКАЦИИ:")
        for method, result in verification['methods'].items():
            print(f"   {method}: {result['score']}/100 - {result['evidence']}")
    
    async def _generate_ultra_precise_report(self):
        """Генерация максимально точного отчета"""
        logger.info("\n📊 ГЕНЕРАЦИЯ МАКСИМАЛЬНО ТОЧНОГО ОТЧЕТА")
        
        # Статистика
        total = len(self.verification_results)
        high_confidence = sum(1 for v in self.verification_results if v['final_score'] >= 75)
        medium_confidence = sum(1 for v in self.verification_results if 60 <= v['final_score'] < 75)
        low_confidence = sum(1 for v in self.verification_results if v['final_score'] < 60)
        
        real_vulnerabilities = sum(1 for v in self.verification_results if v['is_real_vulnerability'])
        
        print(f"\n{'='*80}")
        print("🔍 МАКСИМАЛЬНО ТОЧНЫЙ ИТОГОВЫЙ АНАЛИЗ")
        print(f"{'='*80}")
        print(f"📊 СТАТИСТИКА ВЕРИФИКАЦИИ:")
        print(f"   Всего проверено: {total}")
        print(f"   Высокая уверенность (75+): {high_confidence} ({high_confidence/total*100:.1f}%)")
        print(f"   Средняя уверенность (60-74): {medium_confidence} ({medium_confidence/total*100:.1f}%)")
        print(f"   Низкая уверенность (<60): {low_confidence} ({low_confidence/total*100:.1f}%)")
        print(f"   Реальных уязвимостей: {real_vulnerabilities} ({real_vulnerabilities/total*100:.1f}%)")
        
        # Топ находки
        top_findings = sorted(self.verification_results, key=lambda x: x['final_score'], reverse=True)[:10]
        
        print(f"\n🏆 ТОП-10 НАИБОЛЕЕ ДОСТОВЕРНЫХ НАХОДОК:")
        for i, finding in enumerate(top_findings, 1):
            print(f"   {i}. {finding['target']} - Скор: {finding['final_score']}/100")
            print(f"      Энтропия: {finding['entropy']:.6f}, Действие: {finding['recommended_action']}")
        
        # Рекомендации по отправке
        send_reports = [v for v in self.verification_results if v['final_score'] >= 75]
        consider_reports = [v for v in self.verification_results if 60 <= v['final_score'] < 75]
        
        print(f"\n📧 РЕКОМЕНДАЦИИ ПО ОТПРАВКЕ:")
        print(f"   ОТПРАВИТЬ НЕМЕДЛЕННО: {len(send_reports)} отчетов")
        print(f"   РАССМОТРЕТЬ ОТПРАВКУ: {len(consider_reports)} отчетов")
        print(f"   НЕ ОТПРАВЛЯТЬ: {total - len(send_reports) - len(consider_reports)} отчетов")
        
        # Сохранение отчета
        report_data = {
            'verification_date': datetime.now().isoformat(),
            'total_verified': total,
            'high_confidence_count': high_confidence,
            'real_vulnerabilities_count': real_vulnerabilities,
            'top_findings': top_findings[:10],
            'send_immediately': [v['target'] for v in send_reports],
            'consider_sending': [v['target'] for v in consider_reports],
            'detailed_results': self.verification_results
        }
        
        filename = f"ultra_precise_verification_{int(time.time())}.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n💾 Детальный отчет сохранен: {filename}")

async def main():
    """Главная функция"""
    print("🔍 ULTRA PRECISE VULNERABILITY VERIFIER")
    print("=" * 80)
    
    async with UltraPreciseVulnerabilityVerifier() as verifier:
        await verifier.ultra_precise_verification()

if __name__ == "__main__":
    asyncio.run(main())
