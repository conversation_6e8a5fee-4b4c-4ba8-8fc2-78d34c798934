/**
 * 🔥 СКРИПТ ОБНОВЛЕНИЯ CUSTOM ALT ТАБЛИЦЫ
 * Загружает актуальные данные Custom ALT и обновляет локальный файл
 */

const { Connection, PublicKey } = require('@solana/web3.js');
const fs = require('fs');

class CustomALTUpdater {
    constructor() {
        this.connection = new Connection('https://api.mainnet-beta.solana.com', 'confirmed');
        this.customALTAddress = 'FAeyUf4AdG7vTQ2Q89r9hWBkPdYkykJKG4CxgqArBGXe';
        this.outputFile = 'custom-alt-data.json';
    }

    /**
     * 🔥 ЗАГРУЗИТЬ АКТУАЛЬНЫЕ ДАННЫЕ CUSTOM ALT
     */
    async loadCustomALT() {
        console.log(`🔥 ЗАГРУЗКА CUSTOM ALT: ${this.customALTAddress}`);
        
        try {
            const altPubkey = new PublicKey(this.customALTAddress);
            
            // Загружаем ALT таблицу с повторными попытками
            let altAccount = null;
            for (let attempt = 1; attempt <= 3; attempt++) {
                try {
                    console.log(`   Попытка ${attempt}/3...`);
                    altAccount = await this.connection.getAddressLookupTable(altPubkey);
                    if (altAccount && altAccount.value) break;
                } catch (error) {
                    console.log(`   ❌ Попытка ${attempt} неудачна: ${error.message}`);
                    if (attempt < 3) {
                        await new Promise(resolve => setTimeout(resolve, 2000));
                    }
                }
            }
            
            if (!altAccount || !altAccount.value) {
                throw new Error('Custom ALT таблица не найдена после 3 попыток!');
            }
            
            const addresses = altAccount.value.state.addresses;
            console.log(`✅ ЗАГРУЖЕНО ${addresses.length} АДРЕСОВ из Custom ALT`);
            
            // Показываем статистику
            console.log(`📊 СТАТИСТИКА CUSTOM ALT:`);
            console.log(`   Всего адресов: ${addresses.length}`);
            console.log(`   Первый адрес: ${addresses[0]?.toString() || 'НЕТ'}`);
            console.log(`   Последний адрес: ${addresses[addresses.length - 1]?.toString() || 'НЕТ'}`);
            
            return {
                address: this.customALTAddress,
                totalAddresses: addresses.length,
                addresses: addresses.map(addr => addr.toString()),
                updatedAt: new Date().toISOString(),
                loadedFromBlockchain: true
            };
            
        } catch (error) {
            console.log(`❌ ОШИБКА ЗАГРУЗКИ CUSTOM ALT: ${error.message}`);
            throw error;
        }
    }

    /**
     * 🔥 СРАВНИТЬ С ТЕКУЩИМ ФАЙЛОМ
     */
    compareWithCurrentFile(newData) {
        console.log(`🔍 СРАВНЕНИЕ С ТЕКУЩИМ ФАЙЛОМ: ${this.outputFile}`);
        
        try {
            if (!fs.existsSync(this.outputFile)) {
                console.log(`   ⚠️ Файл ${this.outputFile} не существует - будет создан новый`);
                return { isNew: true, differences: [] };
            }
            
            const currentData = JSON.parse(fs.readFileSync(this.outputFile, 'utf8'));
            
            console.log(`   📊 СРАВНЕНИЕ:`);
            console.log(`      Текущий файл: ${currentData.totalAddresses || 0} адресов`);
            console.log(`      Новые данные: ${newData.totalAddresses} адресов`);
            
            const differences = [];
            
            // Сравниваем количество адресов
            if (currentData.totalAddresses !== newData.totalAddresses) {
                differences.push(`Количество адресов: ${currentData.totalAddresses} → ${newData.totalAddresses}`);
            }
            
            // Сравниваем первые 10 адресов
            const currentAddresses = currentData.addresses || [];
            const newAddresses = newData.addresses || [];
            
            for (let i = 0; i < Math.min(10, Math.max(currentAddresses.length, newAddresses.length)); i++) {
                if (currentAddresses[i] !== newAddresses[i]) {
                    differences.push(`Адрес ${i}: ${currentAddresses[i] || 'НЕТ'} → ${newAddresses[i] || 'НЕТ'}`);
                }
            }
            
            if (differences.length > 0) {
                console.log(`   🔄 НАЙДЕНО ${differences.length} РАЗЛИЧИЙ:`);
                differences.forEach(diff => console.log(`      - ${diff}`));
            } else {
                console.log(`   ✅ РАЗЛИЧИЙ НЕ НАЙДЕНО - файл актуален`);
            }
            
            return { isNew: false, differences };
            
        } catch (error) {
            console.log(`   ❌ ОШИБКА СРАВНЕНИЯ: ${error.message}`);
            return { isNew: true, differences: [] };
        }
    }

    /**
     * 🔥 СОХРАНИТЬ ОБНОВЛЕННЫЕ ДАННЫЕ
     */
    saveUpdatedData(altData) {
        console.log(`💾 СОХРАНЕНИЕ ОБНОВЛЕННЫХ ДАННЫХ: ${this.outputFile}`);
        
        try {
            // Создаем резервную копию
            if (fs.existsSync(this.outputFile)) {
                const backupFile = `${this.outputFile}.backup.${Date.now()}`;
                fs.copyFileSync(this.outputFile, backupFile);
                console.log(`   📋 Резервная копия: ${backupFile}`);
            }
            
            // Сохраняем новые данные
            const jsonData = JSON.stringify(altData, null, 2);
            fs.writeFileSync(this.outputFile, jsonData);
            
            console.log(`✅ ДАННЫЕ СОХРАНЕНЫ:`);
            console.log(`   Файл: ${this.outputFile}`);
            console.log(`   Адресов: ${altData.totalAddresses}`);
            console.log(`   Размер: ${jsonData.length} байт`);
            console.log(`   Обновлено: ${altData.updatedAt}`);
            
        } catch (error) {
            console.log(`❌ ОШИБКА СОХРАНЕНИЯ: ${error.message}`);
            throw error;
        }
    }

    /**
     * 🔥 ПОЛНОЕ ОБНОВЛЕНИЕ CUSTOM ALT
     */
    async updateCustomALT() {
        console.log(`🚀 ОБНОВЛЕНИЕ CUSTOM ALT ТАБЛИЦЫ...`);
        console.log(`   ALT Address: ${this.customALTAddress}`);
        console.log(`   Output File: ${this.outputFile}`);
        
        try {
            // 1. Загружаем актуальные данные
            const newData = await this.loadCustomALT();
            
            // 2. Сравниваем с текущим файлом
            const comparison = this.compareWithCurrentFile(newData);
            
            // 3. Сохраняем если есть изменения или файл новый
            if (comparison.isNew || comparison.differences.length > 0) {
                this.saveUpdatedData(newData);
                console.log(`🎉 CUSTOM ALT УСПЕШНО ОБНОВЛЕН!`);
            } else {
                console.log(`✅ CUSTOM ALT УЖЕ АКТУАЛЕН - обновление не требуется`);
            }
            
            return newData;
            
        } catch (error) {
            console.log(`💥 ОШИБКА ОБНОВЛЕНИЯ: ${error.message}`);
            throw error;
        }
    }
}

// 🔥 ОСНОВНАЯ ФУНКЦИЯ
async function main() {
    console.log(`🔥 ЗАПУСК ОБНОВЛЕНИЯ CUSTOM ALT ТАБЛИЦЫ`);
    console.log(`${'='.repeat(60)}`);
    
    const updater = new CustomALTUpdater();
    
    try {
        await updater.updateCustomALT();
        console.log(`\n🎉 ОБНОВЛЕНИЕ ЗАВЕРШЕНО УСПЕШНО!`);
    } catch (error) {
        console.log(`\n💥 ОШИБКА: ${error.message}`);
        process.exit(1);
    }
}

// Запуск скрипта
if (require.main === module) {
    main().catch(console.error);
}

module.exports = CustomALTUpdater;
