/**
 * 🔥 РАБОЧИЙ МЕТОД ОБМАНКИ СИМУЛЯЦИИ
 * Основан на анализе ошибки: SDK падает в isPositionNoFee из-за undefined feeX
 */

const { Connection, PublicKey } = require('@solana/web3.js');
const DLMM = require('@meteora-ag/dlmm').default;

// 🔥 НАСТРОЙКИ
const RPC_URL = 'https://api.mainnet-beta.solana.com';
const POOL_ADDRESS = '5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6';
const POSITION_ADDRESS = '2RNsPLUHxZp6BzsFiADvjH7BTEJiYFD46bdvBemadDMw';

class WorkingSimulationMock {
    constructor() {
        this.connection = new Connection(RPC_URL, 'confirmed');
        this.originalSimulateTransaction = null;
    }

    /**
     * 🔥 РАБОЧИЙ МЕТОД: ГЛОБАЛЬНАЯ ОБМАНКА + ПРАВИЛЬНЫЕ ПОЗИЦИИ
     */
    async createWorkingClaimFeeInstruction() {
        console.log('🔥 СОЗДАНИЕ РАБОЧЕЙ claimFee ИНСТРУКЦИИ С ОБМАНКОЙ');
        
        // 1. Устанавливаем глобальную обманку симуляции
        this.setupGlobalSimulationMock();
        
        try {
            // 2. Создаем DLMM инстанс
            const dlmm = await DLMM.create(this.connection, new PublicKey(POOL_ADDRESS));
            console.log('✅ DLMM создан с обманкой');
            
            // 3. Создаем правильную позицию с feeX и feeY
            const correctPosition = this.createCorrectPosition();
            console.log('✅ Правильная позиция создана');
            
            // 4. Пытаемся создать claimFee инструкцию
            const claimTx = await dlmm.claimSwapFee({
                owner: new PublicKey('11111111111111111111111111111111'),
                position: correctPosition.publicKey
            });
            
            if (claimTx && claimTx.instructions.length > 0) {
                console.log('🎉 УСПЕХ! ПОЛУЧИЛИ НАСТОЯЩУЮ claimFee ИНСТРУКЦИЮ!');
                console.log(`📋 Инструкций: ${claimTx.instructions.length}`);
                
                // Анализируем инструкцию
                const instruction = claimTx.instructions[0];
                console.log(`📊 Program: ${instruction.programId.toString()}`);
                console.log(`📊 Keys: ${instruction.keys.length}`);
                console.log(`📊 Data: ${instruction.data.length} bytes`);
                
                if (instruction.data.length >= 8) {
                    const discriminator = instruction.data.slice(0, 8).toString('hex');
                    console.log(`📊 Discriminator: ${discriminator}`);
                    
                    if (discriminator === '70bf65ab1c907fbb') {
                        console.log('🎯 ПОДТВЕРЖДЕНО: ЭТО НАСТОЯЩАЯ claimFee2 ИНСТРУКЦИЯ!');
                        return instruction;
                    }
                }
            }
            
            console.log('❌ Не удалось создать claimFee инструкцию');
            return null;
            
        } catch (error) {
            console.log(`❌ ОШИБКА: ${error.message}`);
            console.log(`🔍 Stack: ${error.stack.split('\n')[1]}`);
            return null;
        } finally {
            // Восстанавливаем оригинальную симуляцию
            this.restoreOriginalSimulation();
        }
    }

    /**
     * 🎭 УСТАНОВКА ГЛОБАЛЬНОЙ ОБМАНКИ СИМУЛЯЦИИ
     */
    setupGlobalSimulationMock() {
        console.log('🎭 Устанавливаем глобальную обманку simulateTransaction...');
        
        // Сохраняем оригинальный метод
        this.originalSimulateTransaction = this.connection.simulateTransaction.bind(this.connection);
        
        // Устанавливаем обманку
        this.connection.simulateTransaction = async (transaction, config) => {
            console.log('   🎭 ПЕРЕХВАТИЛИ simulateTransaction!');
            
            // Всегда возвращаем успешный результат
            return {
                value: {
                    err: null,
                    logs: [
                        "Program LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo invoke [1]",
                        "Program log: Instruction: claimFee2",
                        "Program log: Claimed fees: X=1000000, Y=2000000",
                        "Program LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo success"
                    ],
                    accounts: null,
                    unitsConsumed: 50000,
                    returnData: null
                }
            };
        };
    }

    /**
     * 🔧 СОЗДАНИЕ ПРАВИЛЬНОЙ ПОЗИЦИИ С feeX И feeY
     */
    createCorrectPosition() {
        console.log('🔧 Создаем правильную позицию с feeX и feeY...');
        
        // Создаем позицию с правильной структурой
        const position = {
            publicKey: new PublicKey(POSITION_ADDRESS),
            account: {
                lbPair: new PublicKey(POOL_ADDRESS),
                owner: new PublicKey('11111111111111111111111111111111'),
                liquidityShares: [],
                rewardInfos: [],
                feeInfos: [],
                lowerBinId: -4157,
                upperBinId: -4089,
                lastUpdatedAt: Date.now(),
                totalClaimedFeeXAmount: 0,
                totalClaimedFeeYAmount: 0,
                totalClaimedRewards: [],
                // 🔥 ГЛАВНОЕ: ДОБАВЛЯЕМ feeX И feeY!
                feeX: 1000000, // 1 USDC в комиссиях
                feeY: 2000000  // 2 WSOL в комиссиях
            }
        };
        
        console.log(`   ✅ Позиция создана с feeX: ${position.account.feeX}, feeY: ${position.account.feeY}`);
        return position;
    }

    /**
     * 🔄 ВОССТАНОВЛЕНИЕ ОРИГИНАЛЬНОЙ СИМУЛЯЦИИ
     */
    restoreOriginalSimulation() {
        if (this.originalSimulateTransaction) {
            console.log('🔄 Восстанавливаем оригинальную simulateTransaction...');
            this.connection.simulateTransaction = this.originalSimulateTransaction;
            this.originalSimulateTransaction = null;
        }
    }

    /**
     * 🧪 ТЕСТИРОВАНИЕ РАБОЧЕГО МЕТОДА
     */
    async testWorkingMethod() {
        console.log('🧪 ТЕСТИРУЕМ РАБОЧИЙ МЕТОД ОБМАНКИ...');
        
        const instruction = await this.createWorkingClaimFeeInstruction();
        
        if (instruction) {
            console.log('🎉 РАБОЧИЙ МЕТОД НАЙДЕН!');
            console.log('📋 ИНСТРУКЦИЯ ДЛЯ ИНТЕГРАЦИИ:');
            console.log(`   Program: ${instruction.programId.toString()}`);
            console.log(`   Keys: ${instruction.keys.length}`);
            console.log(`   Data: ${instruction.data.toString('hex')}`);
            return true;
        } else {
            console.log('❌ Рабочий метод не найден');
            return false;
        }
    }
}

// 🚀 ЗАПУСК ТЕСТА
async function main() {
    console.log('🔥🔥🔥 ПОИСК РАБОЧЕГО МЕТОДА ОБМАНКИ СИМУЛЯЦИИ 🔥🔥🔥');
    
    const mockTester = new WorkingSimulationMock();
    const success = await mockTester.testWorkingMethod();
    
    if (success) {
        console.log('\n✅ РАБОЧИЙ МЕТОД НАЙДЕН!');
        console.log('🎯 ИНТЕГРИРУЙ ЭТОТ МЕТОД В ОСНОВНОЙ КОД:');
        console.log('   1. setupGlobalSimulationMock() - перед вызовом SDK');
        console.log('   2. createCorrectPosition() - создание позиции с feeX/feeY');
        console.log('   3. restoreOriginalSimulation() - после получения инструкции');
    } else {
        console.log('\n❌ РАБОЧИЙ МЕТОД НЕ НАЙДЕН');
        console.log('🔍 ПРОБЛЕМА В SDK ИЛИ СТРУКТУРЕ ДАННЫХ');
    }
}

main().catch(console.error);
