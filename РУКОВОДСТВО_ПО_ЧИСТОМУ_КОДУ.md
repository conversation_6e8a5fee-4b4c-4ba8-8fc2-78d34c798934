# 📋 Руководство по чистому коду для Auggie

## 🎯 Основные принципы

### 1. **Осмысленные имена**
- ✅ Использую описательные имена переменных, функций и классов
- ✅ `getUserById()` вместо `get()`
- ✅ `isEmailValid` вместо `flag`
- ❌ Избегаю сокращений и однобуквенных переменных (кроме счетчиков циклов)

### 2. **Функции и методы**
- ✅ Одна функция = одна ответственность
- ✅ Функции должны быть короткими (до 20-30 строк)
- ✅ Минимум параметров (идеально 0-3)
- ✅ Возвращаю результат, избегаю побочных эффектов

### 3. **Структура кода**
- ✅ Логическая группировка связанного кода
- ✅ Консистентное форматирование и отступы
- ✅ Пустые строки для разделения логических блоков
- ✅ Импорты в начале файла, сгруппированные по типу

### 4. **Комментарии**
- ✅ Код должен быть самодокументируемым
- ✅ Комментарии только для объяснения "почему", а не "что"
- ✅ TODO комментарии с указанием автора и даты
- ❌ Избегаю очевидных комментариев

### 5. **Обработка ошибок**
- ✅ Явная обработка исключений
- ✅ Информативные сообщения об ошибках
- ✅ Fail-fast принцип
- ❌ Не игнорирую ошибки молча

## 🚫 Что НЕ делать (анти-паттерны)

### Мусор в коде:
- ❌ Закомментированный код (удаляю)
- ❌ Неиспользуемые импорты и переменные
- ❌ Дублирование кода (выношу в функции)
- ❌ Магические числа (использую константы)
- ❌ Глубокая вложенность (рефакторю)

### Плохие практики:
- ❌ Функции-монстры (>50 строк)
- ❌ Классы-боги (слишком много ответственности)
- ❌ Глобальные переменные
- ❌ Хардкод значений

## 🔧 Инструменты чистоты

### При написании кода:
1. **Линтеры** - настраиваю и следую правилам
2. **Форматтеры** - автоматическое форматирование
3. **Type hints** - для языков с поддержкой типов
4. **Тесты** - покрываю критичную логику

### При рефакторинге:
1. Сначала тесты, потом рефакторинг
2. Маленькие итеративные изменения
3. Проверяю работоспособность после каждого изменения

## 📝 Чек-лист перед коммитом

- [ ] Код читается как хорошая проза
- [ ] Нет дублирования логики
- [ ] Функции делают одну вещь хорошо
- [ ] Имена переменных и функций понятны
- [ ] Удален весь мертвый код
- [ ] Обработаны потенциальные ошибки
- [ ] Добавлены необходимые тесты

## 🎯 Цель
**Код должен быть написан для людей, а не только для компьютера. Через полгода я (или другой разработчик) должен легко понять, что происходит в коде.**

---

# 🔍 Анализатор пулов - Логика определения BUY/SELL

## 📊 Как работает анализатор

### 1. **Получение цен пулов**
```javascript
// В BMETEORA.js строки 709-712
poolPrices.sort((a, b) => a.price - b.price);
const cheapest = poolPrices[0];
const mostExpensive = poolPrices[poolPrices.length - 1];
```

### 2. **Определение направления арбитража**
```javascript
// В BMETEORA.js строки 718-722
this.completeFlashLoanStructure.lastOpportunity = {
    buyPool: cheapest,    // ДЕШЕВЫЙ ПУЛ для покупки (USDC → WSOL)
    sellPool: mostExpensive, // ДОРОГОЙ ПУЛ для продажи (WSOL → USDC)
    spread: spread,
    spreadPercent: spreadPercent
};
```

## 🏊 Конфигурация пулов

### Адреса пулов:
- **Pool 1 (meteora1)**: `5rCf1DM8LjKTw4YqhnoLcngyZYeNnQqztScTogYHAS6`
- **Pool 2 (meteora2)**: `BGm1tav58oGcsQJehL9WXBFXF7D27vZsKefj4xJKD5Y`

### Логика анализатора:
1. **Сортировка по цене**: Пулы сортируются по возрастанию цены
2. **Выбор самого дешевого**: `cheapest = poolPrices[0]` - для покупки SOL
3. **Выбор самого дорогого**: `mostExpensive = poolPrices[last]` - для продажи SOL
4. **Расчет спреда**: `spread = mostExpensive.price - cheapest.price`

## 🎯 Пример работы

### Если цены:
- Pool 1: $167.50
- Pool 2: $167.54

### Результат анализа:
- **BUY Pool**: Pool 1 ($167.50) - покупаем SOL дешевле
- **SELL Pool**: Pool 2 ($167.54) - продаем SOL дороже
- **Spread**: $0.04 (0.024%)

### Если цены поменялись:
- Pool 1: $167.54
- Pool 2: $167.50

### Новый результат:
- **BUY Pool**: Pool 2 ($167.50) - теперь Pool 2 дешевле
- **SELL Pool**: Pool 1 ($167.54) - теперь Pool 1 дороже

## ✅ Ключевые особенности

1. **Динамическое определение**: Анализатор автоматически определяет какой пул использовать для покупки/продажи
2. **Адаптивность**: Если цены меняются местами, направление арбитража автоматически меняется
3. **Точность**: Использует цены из активных бинов для максимальной точности
4. **Логирование**: Четко показывает какой пул выбран для какой операции

## 🔥 Важно помнить

- Анализатор **НЕ** привязан к конкретным адресам пулов
- Он **ВСЕГДА** выбирает самый дешевый для покупки и самый дорогой для продажи
- Это обеспечивает максимальную прибыльность арбитража
- Порядок может меняться в зависимости от рыночных условий

---

# 💻 Примеры чистого кода

## ✅ Хороший пример

```javascript
// 🎯 АНАЛИЗ АРБИТРАЖНЫХ ВОЗМОЖНОСТЕЙ
async function analyzeArbitrageOpportunity(pools) {
    const sortedPools = pools.sort((a, b) => a.price - b.price);
    const buyPool = sortedPools[0];
    const sellPool = sortedPools[sortedPools.length - 1];

    const spread = calculateSpread(buyPool, sellPool);

    if (!isProfitable(spread)) {
        return null;
    }

    return {
        buyPool,
        sellPool,
        spread,
        estimatedProfit: calculateProfit(spread)
    };
}

function calculateSpread(buyPool, sellPool) {
    const absolute = sellPool.price - buyPool.price;
    const percent = (absolute / buyPool.price) * 100;

    return { absolute, percent };
}

function isProfitable(spread) {
    const MIN_SPREAD_PERCENT = 0.004; // Из конфига
    return spread.percent >= MIN_SPREAD_PERCENT;
}
```

## ❌ Плохой пример

```javascript
// Плохо: неясные имена, магические числа, много ответственности
async function doStuff(p) {
    let x = p.sort((a, b) => a.price - b.price);
    let c = x[0];
    let e = x[x.length - 1];
    let s = e.price - c.price;
    let sp = (s / c.price) * 100;

    if (sp >= 0.004) { // Магическое число!
        // Много логики в одной функции
        let profit = s * 1000000 - (s * 0.003);
        console.log("profit:", profit);
        return { c, e, s, sp, profit };
    }
    return null;
}
```

## 🔧 Рефакторинг: от плохого к хорошему

### Шаг 1: Осмысленные имена
```javascript
// Было
let c = x[0];
let e = x[x.length - 1];

// Стало
const cheapestPool = sortedPools[0];
const mostExpensivePool = sortedPools[sortedPools.length - 1];
```

### Шаг 2: Константы вместо магических чисел
```javascript
// Было
if (sp >= 0.004) {

// Стало
const MIN_SPREAD_PERCENT = 0.004;
if (spreadPercent >= MIN_SPREAD_PERCENT) {
```

### Шаг 3: Разделение ответственности
```javascript
// Было: одна большая функция
async function doStuff(pools) {
    // 50 строк кода
}

// Стало: несколько маленьких функций
async function analyzeArbitrageOpportunity(pools) {
    const sortedPools = sortPools(pools);
    const { buyPool, sellPool } = selectPools(sortedPools);
    const spread = calculateSpread(buyPool, sellPool);

    return isProfitable(spread) ?
        createOpportunity(buyPool, sellPool, spread) :
        null;
}
```

## 📚 Полезные паттерны

### 1. Early Return
```javascript
// ✅ Хорошо
function validateUser(user) {
    if (!user) return { valid: false, error: 'User required' };
    if (!user.email) return { valid: false, error: 'Email required' };
    if (!user.password) return { valid: false, error: 'Password required' };

    return { valid: true };
}

// ❌ Плохо
function validateUser(user) {
    let result = { valid: false };
    if (user) {
        if (user.email) {
            if (user.password) {
                result.valid = true;
            } else {
                result.error = 'Password required';
            }
        } else {
            result.error = 'Email required';
        }
    } else {
        result.error = 'User required';
    }
    return result;
}
```

### 2. Объекты конфигурации
```javascript
// ✅ Хорошо
const TRADING_CONFIG = {
    MIN_SPREAD_PERCENT: 0.004,
    MAX_POSITION_SIZE: 1000000,
    SLIPPAGE_TOLERANCE: 0.01
};

function analyzeSpread(spread) {
    return spread >= TRADING_CONFIG.MIN_SPREAD_PERCENT;
}

// ❌ Плохо
function analyzeSpread(spread) {
    return spread >= 0.004; // Откуда это число?
}
```

### 3. Описательные булевы функции
```javascript
// ✅ Хорошо
function isSpreadProfitable(spread) {
    return spread.percent >= MIN_SPREAD_PERCENT;
}

function hasEnoughLiquidity(pool, amount) {
    return pool.liquidity >= amount * LIQUIDITY_MULTIPLIER;
}

if (isSpreadProfitable(spread) && hasEnoughLiquidity(buyPool, amount)) {
    executeArbitrage();
}

// ❌ Плохо
if (spread.percent >= 0.004 && pool.liquidity >= amount * 2) {
    executeArbitrage();
}
```
